{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@clerk/nextjs": "^6.20.1", "@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.28.4", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "csv-parser": "^3.0.0", "date-fns": "^3.6.0", "debug": "^4.4.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "firebase": "^11.8.0", "firebase-admin": "^13.4.0", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.446.0", "luqr": "latest", "mathjs": "^14.4.0", "next": "13.5.1", "next-themes": "^0.3.0", "postcss": "8.4.30", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-spinners": "^0.17.0", "recharts": "^2.12.7", "server-only": "^0.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "ts-fem": "github:jan<PERSON><PERSON><PERSON>/ts-fem#59c32e13ce76d2fec45861e9c15d50d7b6dd52c4", "typescript": "5.2.2", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@babel/preset-typescript": "^7.27.0", "@jest/globals": "^29.7.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/react-beautiful-dnd": "^13.1.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "prisma": "^6.8.2", "ts-jest": "^29.3.4"}}