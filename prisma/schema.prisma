generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BeamAnalysisResult {
  id                  String       @id @default(cuid())
  name                String?
  length              Float
  modulusOfElasticity Float
  momentOfInertia     Float
  bendingStressRatio  Float?
  shearStressRatio    Float?
  maxDeflection       Float?
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt
  beamProperties      String?
  loads               String?
  results             String?
  supports            String?
  beamData            String?      // Complete beam data including designMethod and selectedLoadCombos
  projectId           String?
  order               Int?
  organizationId      String?
  project             Project?     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  calculation         Calculation? @relation("CalculationToBeamAnalysisResult")

  @@index([projectId])
}

model Organization {
  id        String    @id @default(cuid())
  name      String
  address   String?
  phone     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  projects  Project[]
}

model Project {
  id                       String                    @id @default(cuid())
  title                    String
  clientName               String?
  address                  String?
  createdAt                DateTime                  @default(now())
  updatedAt                DateTime                  @updatedAt
  organizationId           String?
  description              String?
  status                   String?
  clientAddress            String?
  clientPhone              String?
  dueDate                  DateTime?
  projectNumber            String?
  beamAnalysisResults      BeamAnalysisResult[]
  calculations             Calculation[]
  calculationRelationships CalculationRelationship[]
  folders                  Folder[]
  organization             Organization?             @relation(fields: [organizationId], references: [id])
}

model Calculation {
  id                       String                    @id @default(cuid())
  name                     String
  type                     String
  projectId                String
  order                    Int
  createdAt                DateTime                  @default(now())
  updatedAt                DateTime                  @updatedAt
  result_id                String?                   @unique
  folderId                 String?
  folder                   Folder?                   @relation(fields: [folderId], references: [id])
  project                  Project                   @relation(fields: [projectId], references: [id])
  beamAnalysisResult       BeamAnalysisResult?       @relation("CalculationToBeamAnalysisResult", fields: [result_id], references: [id])
  dependentOnRelationships CalculationRelationship[] @relation("DependentCalculation")
  sourceOfRelationships    CalculationRelationship[] @relation("SourceCalculation")

  @@index([folderId])
}

model CalculationRelationship {
  id                        String                      @id @default(cuid())
  source_calculation_id     String
  calculation_id            String
  type                      CalculationRelationshipType @default(UNKNOWN)
  projectId                 String?
  load_application_position Float
  source_support_position   Float
  dependentCalculation      Calculation                 @relation("DependentCalculation", fields: [calculation_id], references: [id], onDelete: Cascade)
  project                   Project?                    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  sourceCalculation         Calculation                 @relation("SourceCalculation", fields: [source_calculation_id], references: [id], onDelete: Cascade)

  @@index([source_calculation_id])
  @@index([calculation_id])
  @@index([projectId])
}

model Folder {
  id           String        @id @default(cuid())
  name         String
  projectId    String
  order        Int?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  calculations Calculation[]
  project      Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, name])
  @@index([projectId])
}

enum CalculationRelationshipType {
  UNKNOWN
  BEAM_LINK
}
