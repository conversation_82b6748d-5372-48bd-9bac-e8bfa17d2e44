-- CreateEnum
CREATE TYPE "CalculationRelationshipType" AS ENUM ('UNKNOWN', 'BEAM_LINK');

-- CreateTable
CREATE TABLE "CalculationRelationship" (
    "id" TEXT NOT NULL,
    "source_calculation_id" TEXT NOT NULL,
    "calculation_id" TEXT NOT NULL,
    "type" "CalculationRelationshipType" NOT NULL DEFAULT 'UNKNOWN',
    "reaction_position" DOUBLE PRECISION NOT NULL,
    "projectId" TEXT,

    CONSTRAINT "CalculationRelationship_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CalculationRelationship_source_calculation_id_idx" ON "CalculationRelationship"("source_calculation_id");

-- CreateIndex
CREATE INDEX "CalculationRelationship_calculation_id_idx" ON "CalculationRelationship"("calculation_id");

-- CreateIndex
CREATE INDEX "CalculationRelationship_projectId_idx" ON "CalculationRelationship"("projectId");

-- AddForeignKey
ALTER TABLE "CalculationRelationship" ADD CONSTRAINT "CalculationRelationship_source_calculation_id_fkey" FOREIGN KEY ("source_calculation_id") REFERENCES "Calculation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CalculationRelationship" ADD CONSTRAINT "CalculationRelationship_calculation_id_fkey" FOREIGN KEY ("calculation_id") REFERENCES "Calculation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CalculationRelationship" ADD CONSTRAINT "CalculationRelationship_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;
