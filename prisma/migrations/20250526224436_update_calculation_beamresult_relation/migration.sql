/*
  Warnings:

  - A unique constraint covering the columns `[result_id]` on the table `Calculation` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Calculation" ADD COLUMN     "result_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Calculation_result_id_key" ON "Calculation"("result_id");

-- AddForeignKey
ALTER TABLE "Calculation" ADD CONSTRAINT "Calculation_result_id_fkey" FOREIGN KEY ("result_id") REFERENCES "BeamAnalysisResult"("id") ON DELETE SET NULL ON UPDATE CASCADE;
