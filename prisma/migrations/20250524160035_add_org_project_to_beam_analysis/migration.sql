-- DropForeignKey
ALTER TABLE "BeamAnalysisResult" DROP CONSTRAINT "BeamAnalysisResult_projectId_fkey";

-- AlterTable
ALTER TABLE "BeamAnalysisResult" ADD COLUMN     "organizationId" TEXT;

-- CreateIndex
CREATE INDEX "BeamAnalysisResult_projectId_idx" ON "BeamAnalysisResult"("projectId");

-- AddForeignKey
ALTER TABLE "BeamAnalysisResult" ADD CONSTRAINT "BeamAnalysisResult_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;
