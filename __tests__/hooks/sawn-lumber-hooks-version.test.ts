import { DEFAULT_NDS_VERSION, NDS_VERSIONS } from '@/lib/constants/nds-constants';

// Mock fetch globally
global.fetch = jest.fn();

describe('Sawn Lumber Hooks Version Parameter Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Hook URL Construction Logic', () => {
    it('should construct correct URL for useLumberProperties with default version', () => {
      const version = DEFAULT_NDS_VERSION;
      const expectedUrl = `/api/lumber-properties?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe(`/api/lumber-properties?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`);
    });

    it('should construct correct URL for useLumberProperties with custom version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      const expectedUrl = `/api/lumber-properties?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe(`/api/lumber-properties?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
    });

    it('should construct correct URL for useSawnLumberSpecies with default version', () => {
      const version = DEFAULT_NDS_VERSION;
      const expectedUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe(`/api/sawn-lumber-species?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`);
    });

    it('should construct correct URL for useSawnLumberSpecies with custom version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      const expectedUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe(`/api/sawn-lumber-species?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
    });

    it('should handle URL encoding for version with spaces', () => {
      const version = 'NDS 2018';
      const lumberUrl = `/api/lumber-properties?version=${encodeURIComponent(version)}`;
      const speciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      expect(lumberUrl).toBe('/api/lumber-properties?version=NDS%202018');
      expect(speciesUrl).toBe('/api/sawn-lumber-species?version=NDS%202018');
    });

    it('should construct correct URLs for all sawn lumber APIs', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const urls = {
        lumberProperties: `/api/lumber-properties?version=${encodeURIComponent(version)}`,
        sawnLumberSpecies: `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`,
      };
      
      expect(urls.lumberProperties).toBe(`/api/lumber-properties?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
      expect(urls.sawnLumberSpecies).toBe(`/api/sawn-lumber-species?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
    });
  });

  describe('Query Key Construction', () => {
    it('should create unique query keys for different versions', () => {
      const version1 = 'NDS 2018';
      const version2 = 'NDS 2015';
      
      const lumberKey1 = ['lumber-properties', version1];
      const lumberKey2 = ['lumber-properties', version2];
      const speciesKey1 = ['sawn-lumber-species', version1];
      const speciesKey2 = ['sawn-lumber-species', version2];
      
      expect(lumberKey1).not.toEqual(lumberKey2);
      expect(speciesKey1).not.toEqual(speciesKey2);
      expect(lumberKey1[1]).toBe(version1);
      expect(lumberKey2[1]).toBe(version2);
      expect(speciesKey1[1]).toBe(version1);
      expect(speciesKey2[1]).toBe(version2);
    });

    it('should create consistent query keys for same version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const lumberKey1 = ['lumber-properties', version];
      const lumberKey2 = ['lumber-properties', version];
      const speciesKey1 = ['sawn-lumber-species', version];
      const speciesKey2 = ['sawn-lumber-species', version];
      
      expect(lumberKey1).toEqual(lumberKey2);
      expect(speciesKey1).toEqual(speciesKey2);
    });

    it('should create different query keys for different APIs with same version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const lumberKey = ['lumber-properties', version];
      const speciesKey = ['sawn-lumber-species', version];
      
      expect(lumberKey).not.toEqual(speciesKey);
      
      // But both should have the same version
      expect(lumberKey[1]).toBe(version);
      expect(speciesKey[1]).toBe(version);
    });
  });

  describe('Version Parameter Validation', () => {
    it('should handle empty string version', () => {
      const version = '';
      const lumberUrl = `/api/lumber-properties?version=${encodeURIComponent(version)}`;
      const speciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      expect(lumberUrl).toBe('/api/lumber-properties?version=');
      expect(speciesUrl).toBe('/api/sawn-lumber-species?version=');
    });

    it('should handle special characters in version', () => {
      const version = 'NDS 2018 (Special)';
      const lumberUrl = `/api/lumber-properties?version=${encodeURIComponent(version)}`;
      const speciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      expect(lumberUrl).toBe('/api/lumber-properties?version=NDS%202018%20(Special)');
      expect(speciesUrl).toBe('/api/sawn-lumber-species?version=NDS%202018%20(Special)');
    });

    it('should use default version when undefined is passed', () => {
      const version = undefined;
      const actualVersion = version || DEFAULT_NDS_VERSION;
      const lumberUrl = `/api/lumber-properties?version=${encodeURIComponent(actualVersion)}`;
      const speciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(actualVersion)}`;
      
      expect(lumberUrl).toBe(`/api/lumber-properties?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`);
      expect(speciesUrl).toBe(`/api/sawn-lumber-species?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`);
    });
  });

  describe('Constants Integration', () => {
    it('should use constants for default version', () => {
      expect(DEFAULT_NDS_VERSION).toBeDefined();
      expect(typeof DEFAULT_NDS_VERSION).toBe('string');
      expect(DEFAULT_NDS_VERSION.length).toBeGreaterThan(0);
    });

    it('should use constants for NDS versions', () => {
      expect(NDS_VERSIONS).toBeDefined();
      expect(typeof NDS_VERSIONS).toBe('object');
      expect(NDS_VERSIONS.NDS_2018).toBeDefined();
    });

    it('should have consistent version values', () => {
      expect(NDS_VERSIONS.NDS_2018).toBe('NDS 2018');
      expect(DEFAULT_NDS_VERSION).toBe('NDS 2018');
    });
  });

  describe('Version Consistency for Sawn Lumber', () => {
    it('should use consistent NDS version format for sawn lumber species', () => {
      const version = 'NDS 2018';
      const speciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      expect(speciesUrl).toBe('/api/sawn-lumber-species?version=NDS%202018');
      expect(version).toMatch(/^NDS \d{4}$/);
    });

    it('should handle different NDS version formats consistently', () => {
      const versions = ['NDS 2018', 'NDS 2015', 'NDS 2012'];
      
      versions.forEach((version) => {
        expect(version).toMatch(/^NDS \d{4}$/);
        const url = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
        expect(url).toContain('NDS%20');
      });
    });
  });

  describe('API Integration Scenarios', () => {
    it('should handle version changes in sawn lumber component', () => {
      const initialVersion = 'NDS 2015';
      const newVersion = 'NDS 2018';
      
      // Initial API calls
      const initialLumberUrl = `/api/lumber-properties?version=${encodeURIComponent(initialVersion)}`;
      const initialSpeciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(initialVersion)}`;
      
      // After version change
      const newLumberUrl = `/api/lumber-properties?version=${encodeURIComponent(newVersion)}`;
      const newSpeciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(newVersion)}`;
      
      expect(initialLumberUrl).not.toBe(newLumberUrl);
      expect(initialSpeciesUrl).not.toBe(newSpeciesUrl);
      expect(newLumberUrl).toContain('NDS%202018');
      expect(newSpeciesUrl).toContain('NDS%202018');
    });

    it('should maintain consistency between lumber and species API calls', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const lumberUrl = `/api/lumber-properties?version=${encodeURIComponent(version)}`;
      const speciesUrl = `/api/sawn-lumber-species?version=${encodeURIComponent(version)}`;
      
      // Both should use the same encoded version
      const encodedVersion = encodeURIComponent(version);
      expect(lumberUrl).toContain(encodedVersion);
      expect(speciesUrl).toContain(encodedVersion);
    });
  });
}); 