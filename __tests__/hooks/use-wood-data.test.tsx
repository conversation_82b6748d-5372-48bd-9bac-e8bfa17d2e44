import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { useWoodData, useAvailableNdsVersions } from '@/hooks/use-wood-data';
import {
  DEFAULT_NDS_VERSION,
  API_ENDPOINTS,
  ERROR_MESSAGES,
} from '@/lib/constants/nds-constants';

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Create a wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false, // Disable retries for testing
      },
    },
  });
  
  return ({ children }: { children: ReactNode }) => {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};

describe('Wood Data Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useWoodData', () => {
    const mockWoodData = {
      speciesCombinations: ['DOUGLAS FIR-LARCH', 'HEM-FIR'],
      designValues: {
        'DOUGLAS FIR-LARCH': [
          {
            version: DEFAULT_NDS_VERSION,
            commercial_grade: 'No. 2',
            speciesCombination: 'DOUGLAS FIR-LARCH',
            Fb: 900,
            Ft: 575,
            Fv: 180,
            Fc_perp: 625,
            Fc: 1350,
            E: 1600000,
            Emin: 580000,
            G: 0.50,
            minThickness: 2,
            maxThickness: Infinity,
            minWidth: 2,
            maxWidth: Infinity,
            grading_rules_agency: 'WCLIB WWPA',
            design_values_table: '4A',
            location: '',
            size_classification: '2" & wider',
          },
        ],
      },
      commercialGrades: {
        'DOUGLAS FIR-LARCH': ['No. 2'],
      },
      availableVersions: [DEFAULT_NDS_VERSION],
    };

    it('should use default NDS version when no version is provided', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockWoodData,
      });

      const { result } = renderHook(() => useWoodData(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockFetch).toHaveBeenCalledWith(
        `${API_ENDPOINTS.WOOD_DATA}?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`
      );
      expect(result.current.data).toEqual(mockWoodData);
    });

    it('should use provided version parameter', async () => {
      const customVersion = 'NDS 2015';
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockWoodData,
      });

      const { result } = renderHook(() => useWoodData(customVersion), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockFetch).toHaveBeenCalledWith(
        `${API_ENDPOINTS.WOOD_DATA}?version=${encodeURIComponent(customVersion)}`
      );
    });

    it('should handle fetch errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
      });

      const { result } = renderHook(() => useWoodData(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe(ERROR_MESSAGES.WOOD_DATA_LOAD_FAILED);
    });

    it('should return loading state initially', () => {
      mockFetch.mockImplementationOnce(() => new Promise(() => {})); // Never resolves

      const { result } = renderHook(() => useWoodData(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(true);
      expect(result.current.data).toBeUndefined();
      expect(result.current.error).toBeNull();
    });
  });

  describe('useAvailableNdsVersions', () => {
    const mockWoodDataWithVersions = {
      speciesCombinations: ['DOUGLAS FIR-LARCH'],
      designValues: {},
      commercialGrades: {},
      availableVersions: [DEFAULT_NDS_VERSION, 'NDS 2015', 'NDS 2012'],
    };

    it('should fetch and return available NDS versions', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockWoodDataWithVersions,
      });

      const { result } = renderHook(() => useAvailableNdsVersions(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockFetch).toHaveBeenCalledWith(
        `${API_ENDPOINTS.WOOD_DATA}?version=${DEFAULT_NDS_VERSION}`
      );
      expect(result.current.data).toEqual([DEFAULT_NDS_VERSION, 'NDS 2015', 'NDS 2012']);
    });

    it('should handle fetch errors when loading versions', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      const { result } = renderHook(() => useAvailableNdsVersions(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe(ERROR_MESSAGES.VERSIONS_LOAD_FAILED);
    });
  });
}); 