import { DEFAULT_NDS_VERSION, NDS_VERSIONS } from '@/lib/constants/nds-constants';

// Mock fetch globally
global.fetch = jest.fn();

describe('Glulam Hooks Version Parameter Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Hook URL Construction Logic', () => {
    it('should construct correct URL for useGluLamSpecies with default version', () => {
      const version = DEFAULT_NDS_VERSION;
      const expectedUrl = `/api/glulam-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe(`/api/glulam-species?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`);
    });

    it('should construct correct URL for useGluLamSpecies with custom version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      const expectedUrl = `/api/glulam-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe(`/api/glulam-species?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
    });

    it('should handle URL encoding for version with spaces', () => {
      const version = 'NDS 2018';
      const expectedUrl = `/api/glulam-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe('/api/glulam-species?version=NDS%202018');
    });

    it('should construct correct URLs for all glulam APIs', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const urls = {
        species: `/api/glulam-species?version=${encodeURIComponent(version)}`,
        table5A: `/api/glulam-table-5a?version=${encodeURIComponent(version)}`,
        table5B: `/api/glulam-table-5b?version=${encodeURIComponent(version)}`,
        sectionProperties: `/api/glulam-section-properties?version=${encodeURIComponent(version)}`,
      };
      
      expect(urls.species).toBe(`/api/glulam-species?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
      expect(urls.table5A).toBe(`/api/glulam-table-5a?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
      expect(urls.table5B).toBe(`/api/glulam-table-5b?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
      expect(urls.sectionProperties).toBe(`/api/glulam-section-properties?version=${encodeURIComponent(NDS_VERSIONS.NDS_2018)}`);
    });
  });

  describe('Query Key Construction', () => {
    it('should create unique query keys for different versions', () => {
      const version1 = 'NDS 2018';
      const version2 = 'NDS 2015';
      
      const queryKey1 = ['glulam-species', version1];
      const queryKey2 = ['glulam-species', version2];
      
      expect(queryKey1).not.toEqual(queryKey2);
      expect(queryKey1[1]).toBe(version1);
      expect(queryKey2[1]).toBe(version2);
    });

    it('should create consistent query keys for same version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const queryKey1 = ['glulam-species', version];
      const queryKey2 = ['glulam-species', version];
      
      expect(queryKey1).toEqual(queryKey2);
    });

    it('should create different query keys for different APIs with same version', () => {
      const version = NDS_VERSIONS.NDS_2018;
      
      const speciesKey = ['glulam-species', version];
      const table5AKey = ['glulam-table-5a', version];
      const table5BKey = ['glulam-table-5b', version];
      const sectionPropsKey = ['glulam-section-properties', version];
      
      expect(speciesKey).not.toEqual(table5AKey);
      expect(table5AKey).not.toEqual(table5BKey);
      expect(table5BKey).not.toEqual(sectionPropsKey);
      
      // But all should have the same version
      expect(speciesKey[1]).toBe(version);
      expect(table5AKey[1]).toBe(version);
      expect(table5BKey[1]).toBe(version);
      expect(sectionPropsKey[1]).toBe(version);
    });
  });

  describe('Version Parameter Validation', () => {
    it('should handle empty string version', () => {
      const version = '';
      const expectedUrl = `/api/glulam-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe('/api/glulam-species?version=');
    });

    it('should handle special characters in version', () => {
      const version = 'NDS 2018 (Special)';
      const expectedUrl = `/api/glulam-species?version=${encodeURIComponent(version)}`;
      
      expect(expectedUrl).toBe('/api/glulam-species?version=NDS%202018%20(Special)');
    });

    it('should use default version when undefined is passed', () => {
      const version = undefined;
      const actualVersion = version || DEFAULT_NDS_VERSION;
      const expectedUrl = `/api/glulam-species?version=${encodeURIComponent(actualVersion)}`;
      
      expect(expectedUrl).toBe(`/api/glulam-species?version=${encodeURIComponent(DEFAULT_NDS_VERSION)}`);
    });
  });

  describe('Constants Integration', () => {
    it('should use constants for default version', () => {
      expect(DEFAULT_NDS_VERSION).toBeDefined();
      expect(typeof DEFAULT_NDS_VERSION).toBe('string');
      expect(DEFAULT_NDS_VERSION.length).toBeGreaterThan(0);
    });

    it('should use constants for NDS versions', () => {
      expect(NDS_VERSIONS).toBeDefined();
      expect(typeof NDS_VERSIONS).toBe('object');
      expect(NDS_VERSIONS.NDS_2018).toBeDefined();
    });

    it('should have consistent version values', () => {
      expect(NDS_VERSIONS.NDS_2018).toBe('NDS 2018');
      expect(DEFAULT_NDS_VERSION).toBe('NDS 2018');
    });
  });
}); 