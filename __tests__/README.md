# Test Suite for NDS Version Selection Functionality

This directory contains comprehensive unit tests for the NDS (National Design Specification) version selection functionality added to the Structural Engineering Application.

## Test Structure

### `/api/wood-data.test.ts`
Tests for the wood data API route functionality:
- Version parameter handling (default and custom versions)
- Error handling for missing files and parsing errors
- Response format validation
- Filtering logic verification

### `/hooks/use-wood-data.test.tsx`
Tests for the wood data React hooks:
- `useWoodData` hook with version parameters
- `useAvailableNdsVersions` hook
- Error handling and loading states
- Integration between hooks

### `/utils/nds-version-utils.test.ts`
Tests for utility functions and business logic:
- Data filtering by NDS version
- Version extraction from CSV data
- Commercial grades mapping
- Data consistency validation
- Edge case handling

### `/components/nds-version-selection.test.ts`
Tests for sawn lumber component state management logic:
- Initial state creation with constants
- NDS version change handling
- Selection reset on version change
- Backward compatibility for existing data
- State validation

### `/components/glulam-nds-version.test.ts`
Tests for glued laminated timber component state management logic:
- Initial glulam state creation with NDS version
- NDS version change handling for glulam
- Glulam selection reset on version change
- Loading orientation handling
- Combination symbol management
- Backward compatibility for existing glulam data

### `/api/glulam-version-filtering.test.ts`
Tests for glulam API version filtering functionality:
- Version parameter handling for all glulam APIs (Table 5A, 5B, 5C, 5D)
- Species and section properties API version filtering
- Default version behavior when no version specified
- URL-encoded version parameter handling
- Error handling for non-existent versions
- Data consistency across all glulam APIs
- Cache behavior with version filtering

### `/hooks/glulam-hooks-version.test.ts`
Tests for glulam React hooks version parameter functionality:
- URL construction with version parameters for all glulam hooks
- Query key construction with version parameters
- Version parameter validation and encoding
- Constants integration and consistency
- Default version handling

### `/hooks/sawn-lumber-hooks-version.test.ts`
Tests for sawn lumber React hooks version parameter functionality:
- URL construction with version parameters for lumber properties and species hooks
- Query key construction with version parameters
- Version parameter validation and URL encoding
- Version format consistency for sawn lumber species data
- Constants integration and API integration scenarios

## Constants Used

All tests utilize the constants defined in `/lib/constants/nds-constants.ts`:

- `DEFAULT_NDS_VERSION`: Default NDS version ("NDS 2018")
- `DEFAULT_WOOD_SELECTIONS`: Default wood property selections
- `NDS_VERSIONS`: All supported NDS versions
- `ERROR_MESSAGES`: Standardized error messages
- `API_ENDPOINTS`: API endpoint paths
- `QUERY_KEYS`: React Query cache keys

## Key Test Scenarios

### Version Filtering
- Verifies that wood data is correctly filtered by selected NDS version
- Ensures only data matching the selected version is returned
- Tests handling of multiple versions in the same dataset

### State Management
- Tests that changing NDS version resets all wood selections
- Validates backward compatibility for loading existing analyses
- Ensures proper default values are applied

### Error Handling
- Tests graceful handling of missing CSV files
- Validates proper error messages are returned
- Tests network error scenarios for API calls

### Constants Usage
- Verifies consistent use of constants throughout the codebase
- Tests that magic strings are eliminated in favor of constants
- Validates that all defined NDS versions are properly handled

## Running the Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test wood-data.test.ts

# Run tests for a specific pattern
npm test nds-version
```

## Test Coverage

The tests cover:
- ✅ API route functionality (wood data and glulam data filtering)
- ✅ React hooks (data fetching and state management)
- ✅ Utility functions (data processing and validation)
- ✅ Component logic (state management and user interactions)
- ✅ Constants usage (eliminating magic strings)
- ✅ Error handling (graceful failure scenarios)
- ✅ Backward compatibility (loading existing data)
- ✅ Glulam API version filtering (all table APIs and species data)
- ✅ Sawn lumber API version filtering (lumber properties and species data)
- ✅ Hook version parameter handling (both glulam and sawn lumber hooks)

## Mock Data

Tests use realistic mock data that matches the actual CSV structure:
- Design values with multiple versions
- Species combinations and commercial grades
- Proper numeric and string data types
- Edge cases and boundary conditions

## Future Enhancements

When adding new NDS versions:
1. Update `NDS_VERSIONS` constant
2. Add test cases for the new version
3. Verify filtering logic works correctly
4. Test backward compatibility with existing data

## Dependencies

The tests require:
- Jest (testing framework)
- React Testing Library (for component testing)
- @tanstack/react-query (for hook testing)
- TypeScript (for type safety)

Note: Some tests may be commented out if testing libraries are not installed. The business logic tests will run without additional dependencies. 