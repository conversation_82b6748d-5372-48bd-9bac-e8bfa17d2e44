import {
  DEFAULT_NDS_VERSION,
  DEFAULT_WOOD_SELECTIONS,
  NDS_VERSIONS,
  ERROR_MESSAGES,
} from '@/lib/constants/nds-constants';

// Mock data that matches the structure used in the application
const mockDesignValues = [
  {
    version: DEFAULT_NDS_VERSION,
    commercial_grade: 'No. 2',
    speciesCombination: 'DOUGLAS FIR-LARCH',
    Fb: 900,
    Ft: 575,
    Fv: 180,
    Fc_perp: 625,
    Fc: 1350,
    E: 1600000,
    Emin: 580000,
    G: 0.50,
    minThickness: 2,
    maxThickness: Infinity,
    minWidth: 2,
    maxWidth: Infinity,
    grading_rules_agency: 'WCLIB WWPA',
    design_values_table: '4A',
    location: '',
    size_classification: '2" & wider',
  },
  {
    version: 'NDS 2015',
    commercial_grade: 'No. 1',
    speciesCombination: 'DOUGLAS FIR-LARCH',
    Fb: 1000,
    Ft: 675,
    Fv: 180,
    Fc_perp: 625,
    Fc: 1500,
    E: 1700000,
    Emin: 620000,
    G: 0.50,
    minThickness: 2,
    maxThickness: Infinity,
    minWidth: 2,
    maxWidth: Infinity,
    grading_rules_agency: 'WCLIB WWPA',
    design_values_table: '4A',
    location: '',
    size_classification: '2" & wider',
  },
  {
    version: DEFAULT_NDS_VERSION,
    commercial_grade: 'No. 1',
    speciesCombination: 'HEM-FIR',
    Fb: 1000,
    Ft: 625,
    Fv: 150,
    Fc_perp: 405,
    Fc: 1450,
    E: 1300000,
    Emin: 470000,
    G: 0.43,
    minThickness: 2,
    maxThickness: Infinity,
    minWidth: 2,
    maxWidth: Infinity,
    grading_rules_agency: 'WCLIB WWPA',
    design_values_table: '4A',
    location: '',
    size_classification: '2" & wider',
  },
];

// Utility function to filter design values by version (replicating API logic)
function filterDesignValuesByVersion(designValues: typeof mockDesignValues, version: string) {
  return designValues.filter(dv => dv.version === version);
}

// Utility function to extract available versions
function extractAvailableVersions(designValues: typeof mockDesignValues): string[] {
  const versionSet = new Set<string>();
  designValues.forEach(dv => {
    if (dv.version) {
      versionSet.add(dv.version);
    }
  });
  return Array.from(versionSet);
}

// Utility function to build commercial grades map by version
function buildCommercialGradesByVersion(designValues: typeof mockDesignValues, version: string) {
  const filteredValues = filterDesignValuesByVersion(designValues, version);
  const gradesMap: Record<string, string[]> = {};
  
  filteredValues.forEach(dv => {
    if (!gradesMap[dv.speciesCombination]) {
      gradesMap[dv.speciesCombination] = [];
    }
    if (!gradesMap[dv.speciesCombination].includes(dv.commercial_grade)) {
      gradesMap[dv.speciesCombination].push(dv.commercial_grade);
    }
  });
  
  return gradesMap;
}

// Test that validates whether a version change requires resetting selections
function shouldResetSelectionsOnVersionChange(
  currentVersion: string,
  newVersion: string,
  currentSpecies: string,
  availableSpeciesForNewVersion: string[]
): boolean {
  if (currentVersion === newVersion) {
    return false;
  }
  
  // If current species is not available in new version, reset
  if (!availableSpeciesForNewVersion.includes(currentSpecies)) {
    return true;
  }
  
  return true; // Always reset on version change to be safe
}

describe('NDS Version Utilities', () => {
  describe('Constants', () => {
    it('should have correct default NDS version', () => {
      expect(DEFAULT_NDS_VERSION).toBe('NDS 2018');
    });

    it('should have all expected NDS versions defined', () => {
      expect(NDS_VERSIONS.NDS_2018).toBe('NDS 2018');
      expect(NDS_VERSIONS.NDS_2015).toBe('NDS 2015');
      expect(NDS_VERSIONS.NDS_2012).toBe('NDS 2012');
    });

    it('should have default wood selections defined', () => {
      expect(DEFAULT_WOOD_SELECTIONS.SPECIES).toBe('DOUGLAS FIR');
      expect(DEFAULT_WOOD_SELECTIONS.SPECIES_COMBINATION).toBe('DOUGLAS FIR-LARCH');
      expect(DEFAULT_WOOD_SELECTIONS.GRADE).toBe('No. 2');
      expect(DEFAULT_WOOD_SELECTIONS.SIZE_CLASSIFICATION).toBe('2" & wider');
    });

    it('should have proper error messages defined', () => {
      expect(ERROR_MESSAGES.WOOD_DATA_NOT_FOUND).toBe('Design values data file not found');
      expect(ERROR_MESSAGES.WOOD_DATA_LOAD_FAILED).toBe('Failed to load wood data');
      expect(ERROR_MESSAGES.VERSIONS_LOAD_FAILED).toBe('Failed to fetch wood data');
    });
  });

  describe('filterDesignValuesByVersion', () => {
    it('should filter design values by specific version', () => {
      const filtered = filterDesignValuesByVersion(mockDesignValues, DEFAULT_NDS_VERSION);
      
      expect(filtered).toHaveLength(2);
      expect(filtered.every(dv => dv.version === DEFAULT_NDS_VERSION)).toBe(true);
      expect(filtered.map(dv => dv.speciesCombination)).toEqual(['DOUGLAS FIR-LARCH', 'HEM-FIR']);
    });

    it('should return empty array for non-existent version', () => {
      const filtered = filterDesignValuesByVersion(mockDesignValues, 'NDS 2010');
      
      expect(filtered).toHaveLength(0);
    });

    it('should handle NDS 2015 version filtering', () => {
      const filtered = filterDesignValuesByVersion(mockDesignValues, 'NDS 2015');
      
      expect(filtered).toHaveLength(1);
      expect(filtered[0].version).toBe('NDS 2015');
      expect(filtered[0].speciesCombination).toBe('DOUGLAS FIR-LARCH');
      expect(filtered[0].commercial_grade).toBe('No. 1');
    });
  });

  describe('extractAvailableVersions', () => {
    it('should extract unique versions from design values', () => {
      const versions = extractAvailableVersions(mockDesignValues);
      
      expect(versions).toHaveLength(2);
      expect(versions).toContain(DEFAULT_NDS_VERSION);
      expect(versions).toContain('NDS 2015');
    });

    it('should handle empty design values array', () => {
      const versions = extractAvailableVersions([]);
      
      expect(versions).toHaveLength(0);
    });

    it('should remove duplicate versions', () => {
      const duplicateData = [
        ...mockDesignValues,
        { ...mockDesignValues[0] }, // Duplicate NDS 2018
      ];
      
      const versions = extractAvailableVersions(duplicateData);
      
      expect(versions).toHaveLength(2);
      expect(versions.filter(v => v === DEFAULT_NDS_VERSION)).toHaveLength(1);
    });
  });

  describe('buildCommercialGradesByVersion', () => {
    it('should build correct grades map for NDS 2018', () => {
      const gradesMap = buildCommercialGradesByVersion(mockDesignValues, DEFAULT_NDS_VERSION);
      
      expect(gradesMap['DOUGLAS FIR-LARCH']).toEqual(['No. 2']);
      expect(gradesMap['HEM-FIR']).toEqual(['No. 1']);
    });

    it('should build correct grades map for NDS 2015', () => {
      const gradesMap = buildCommercialGradesByVersion(mockDesignValues, 'NDS 2015');
      
      expect(gradesMap['DOUGLAS FIR-LARCH']).toEqual(['No. 1']);
      expect(gradesMap['HEM-FIR']).toBeUndefined();
    });

    it('should handle multiple grades for same species', () => {
      const extendedData = [
        ...mockDesignValues,
        {
          ...mockDesignValues[0],
          commercial_grade: 'No. 1',
        },
      ];
      
      const gradesMap = buildCommercialGradesByVersion(extendedData, DEFAULT_NDS_VERSION);
      
      expect(gradesMap['DOUGLAS FIR-LARCH']).toContain('No. 2');
      expect(gradesMap['DOUGLAS FIR-LARCH']).toContain('No. 1');
      expect(gradesMap['DOUGLAS FIR-LARCH']).toHaveLength(2);
    });
  });

  describe('shouldResetSelectionsOnVersionChange', () => {
    it('should not reset when version unchanged', () => {
      const shouldReset = shouldResetSelectionsOnVersionChange(
        DEFAULT_NDS_VERSION,
        DEFAULT_NDS_VERSION,
        'DOUGLAS FIR-LARCH',
        ['DOUGLAS FIR-LARCH', 'HEM-FIR']
      );
      
      expect(shouldReset).toBe(false);
    });

    it('should reset when version changes', () => {
      const shouldReset = shouldResetSelectionsOnVersionChange(
        DEFAULT_NDS_VERSION,
        'NDS 2015',
        'DOUGLAS FIR-LARCH',
        ['DOUGLAS FIR-LARCH', 'HEM-FIR']
      );
      
      expect(shouldReset).toBe(true);
    });

    it('should reset when current species not available in new version', () => {
      const shouldReset = shouldResetSelectionsOnVersionChange(
        DEFAULT_NDS_VERSION,
        'NDS 2015',
        'HEM-FIR',
        ['DOUGLAS FIR-LARCH'] // HEM-FIR not available
      );
      
      expect(shouldReset).toBe(true);
    });
  });

  describe('Data consistency', () => {
    it('should have consistent structure across versions', () => {
      const nds2018Data = filterDesignValuesByVersion(mockDesignValues, DEFAULT_NDS_VERSION);
      const nds2015Data = filterDesignValuesByVersion(mockDesignValues, 'NDS 2015');
      
      // Both should have the same property structure
      if (nds2018Data.length > 0 && nds2015Data.length > 0) {
        const nds2018Keys = Object.keys(nds2018Data[0]).sort();
        const nds2015Keys = Object.keys(nds2015Data[0]).sort();
        
        expect(nds2018Keys).toEqual(nds2015Keys);
      }
    });

    it('should have numeric properties as numbers', () => {
      const data = mockDesignValues[0];
      
      expect(typeof data.Fb).toBe('number');
      expect(typeof data.Ft).toBe('number');
      expect(typeof data.Fv).toBe('number');
      expect(typeof data.E).toBe('number');
      expect(typeof data.Emin).toBe('number');
      expect(typeof data.G).toBe('number');
    });

    it('should have required string properties', () => {
      const data = mockDesignValues[0];
      
      expect(typeof data.version).toBe('string');
      expect(typeof data.commercial_grade).toBe('string');
      expect(typeof data.speciesCombination).toBe('string');
      expect(typeof data.grading_rules_agency).toBe('string');
    });
  });

  describe('Edge cases', () => {
    it('should handle undefined version gracefully', () => {
      const dataWithUndefinedVersion = [
        { ...mockDesignValues[0], version: undefined as any },
      ];
      
      const versions = extractAvailableVersions(dataWithUndefinedVersion);
      expect(versions).toHaveLength(0);
    });

    it('should handle empty string version', () => {
      const dataWithEmptyVersion = [
        { ...mockDesignValues[0], version: '' },
      ];
      
      const filtered = filterDesignValuesByVersion(dataWithEmptyVersion, '');
      expect(filtered).toHaveLength(1);
    });

    it('should handle special characters in version names', () => {
      const specialVersion = 'NDS 2018 (Updated)';
      const dataWithSpecialVersion = [
        { ...mockDesignValues[0], version: specialVersion },
      ];
      
      const filtered = filterDesignValuesByVersion(dataWithSpecialVersion, specialVersion);
      expect(filtered).toHaveLength(1);
      expect(filtered[0].version).toBe(specialVersion);
    });
  });
}); 