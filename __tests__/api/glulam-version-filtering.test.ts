import { NextRequest } from 'next/server';
import { GET as getGlulamTable5A } from '@/app/api/glulam-table-5a/route';
import { GET as getGlulamTable5B } from '@/app/api/glulam-table-5b/route';
import { GET as getGlulamTable5C } from '@/app/api/glulam-table-5c/route';
import { GET as getGlulamTable5D } from '@/app/api/glulam-table-5d/route';
import { GET as getGlulamSpecies } from '@/app/api/glulam-species/route';
import { GET as getGlulamSectionProperties } from '@/app/api/glulam-section-properties/route';
import { DEFAULT_NDS_VERSION, NDS_VERSIONS } from '@/lib/constants/nds-constants';

// Mock NextRequest for testing
function createMockRequest(version?: string): NextRequest {
  const url = version 
    ? `http://localhost:3000/api/test?version=${encodeURIComponent(version)}`
    : 'http://localhost:3000/api/test';
  
  return new NextRequest(url);
}

describe('Glulam API Version Filtering', () => {
  describe('Glulam Table 5A API', () => {
    it('should return data filtered by default NDS version when no version specified', async () => {
      const request = createMockRequest();
      const response = await getGlulamTable5A(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      // All returned data should have the default version
      data.forEach((row: any) => {
        expect(row.version).toBe(DEFAULT_NDS_VERSION);
      });
    });

    it('should return data filtered by specified NDS version', async () => {
      const request = createMockRequest(NDS_VERSIONS.NDS_2018);
      const response = await getGlulamTable5A(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      // All returned data should have the specified version
      data.forEach((row: any) => {
        expect(row.version).toBe(NDS_VERSIONS.NDS_2018);
      });
    });
  });

  describe('Glulam Table 5B API', () => {
    it('should return data filtered by default NDS version', async () => {
      const request = createMockRequest();
      const response = await getGlulamTable5B(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      data.forEach((row: any) => {
        expect(row.version).toBe(DEFAULT_NDS_VERSION);
      });
    });

    it('should return data filtered by specified NDS version', async () => {
      const request = createMockRequest(NDS_VERSIONS.NDS_2018);
      const response = await getGlulamTable5B(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      data.forEach((row: any) => {
        expect(row.version).toBe(NDS_VERSIONS.NDS_2018);
      });
    });
  });

  describe('Glulam Table 5C API', () => {
    it('should return data filtered by default NDS version', async () => {
      const request = createMockRequest();
      const response = await getGlulamTable5C(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      data.forEach((row: any) => {
        expect(row.version).toBe(DEFAULT_NDS_VERSION);
      });
    });
  });

  describe('Glulam Table 5D API', () => {
    it('should return data filtered by default NDS version', async () => {
      const request = createMockRequest();
      const response = await getGlulamTable5D(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      data.forEach((row: any) => {
        expect(row.version).toBe(DEFAULT_NDS_VERSION);
      });
    });
  });

  describe('Glulam Species API', () => {
    it('should return species data (structure test)', async () => {
      const request = createMockRequest();
      const response = await getGlulamSpecies(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(typeof data).toBe('object');
      expect(data.speciesGroups).toBeDefined();
      expect(data.species).toBeDefined();
      expect(Array.isArray(data.speciesGroups)).toBe(true);
      expect(typeof data.species).toBe('object');
    });

    it('should filter species data by version', async () => {
      const request = createMockRequest(NDS_VERSIONS.NDS_2018);
      const response = await getGlulamSpecies(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.speciesGroups.length).toBeGreaterThan(0);
    });
  });

  describe('Glulam Section Properties API', () => {
    it('should return section properties data (structure test)', async () => {
      const request = createMockRequest();
      const response = await getGlulamSectionProperties(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(typeof data).toBe('object');
      expect(data.westernSpecies).toBeDefined();
      expect(data.southernPine).toBeDefined();
      expect(data.westernSpecies.widths).toBeDefined();
      expect(data.westernSpecies.depths).toBeDefined();
      expect(data.westernSpecies.properties).toBeDefined();
    });

    it('should filter section properties by version', async () => {
      const request = createMockRequest(NDS_VERSIONS.NDS_2018);
      const response = await getGlulamSectionProperties(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.westernSpecies.widths.length).toBeGreaterThan(0);
      expect(data.southernPine.widths.length).toBeGreaterThan(0);
    });
  });

  describe('Version Parameter Handling', () => {
    it('should handle URL-encoded version parameters', async () => {
      const request = createMockRequest('NDS 2018'); // This will be URL-encoded by createMockRequest
      const response = await getGlulamTable5A(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      data.forEach((row: any) => {
        expect(row.version).toBe('NDS 2018');
      });
    });

    it('should handle missing version parameter gracefully', async () => {
      const request = createMockRequest();
      const response = await getGlulamTable5A(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      
      data.forEach((row: any) => {
        expect(row.version).toBe(DEFAULT_NDS_VERSION);
      });
    });
  });

  describe('Error Handling', () => {
    it('should return empty array for non-existent version', async () => {
      const request = createMockRequest('NDS 2025'); // Future version that doesn't exist
      const response = await getGlulamTable5A(request);
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBe(0); // Should be empty since no data matches this version
    });
  });

  describe('Data Consistency', () => {
    it('should return consistent data structure across all table APIs', async () => {
      const request = createMockRequest(NDS_VERSIONS.NDS_2018);
      
      const [table5A, table5B, table5C, table5D] = await Promise.all([
        getGlulamTable5A(request),
        getGlulamTable5B(request),
        getGlulamTable5C(request),
        getGlulamTable5D(request),
      ]);
      
      const [data5A, data5B, data5C, data5D] = await Promise.all([
        table5A.json(),
        table5B.json(),
        table5C.json(),
        table5D.json(),
      ]);
      
      // All should return arrays
      expect(Array.isArray(data5A)).toBe(true);
      expect(Array.isArray(data5B)).toBe(true);
      expect(Array.isArray(data5C)).toBe(true);
      expect(Array.isArray(data5D)).toBe(true);
      
      // All should have data
      expect(data5A.length).toBeGreaterThan(0);
      expect(data5B.length).toBeGreaterThan(0);
      expect(data5C.length).toBeGreaterThan(0);
      expect(data5D.length).toBeGreaterThan(0);
      
      // All should have version field with correct value
      data5A.forEach((row: any) => expect(row.version).toBe(NDS_VERSIONS.NDS_2018));
      data5B.forEach((row: any) => expect(row.version).toBe(NDS_VERSIONS.NDS_2018));
      data5C.forEach((row: any) => expect(row.version).toBe(NDS_VERSIONS.NDS_2018));
      data5D.forEach((row: any) => expect(row.version).toBe(NDS_VERSIONS.NDS_2018));
    });
  });
}); 