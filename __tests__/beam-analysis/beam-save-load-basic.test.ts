/**
 * Basic tests for beam analysis save/load functionality
 * Tests saving and loading beam properties and results for sawn lumber, glulam, and steel
 */

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { 
  TEST_BEAM_DIMENSIONS,
  TEST_WOOD_PROPERTIES,
  TEST_STEEL_PROPERTIES,
  TEST_ANALYSIS_NAMES,
  TEST_IDS,
  EXPECTED_STRESS_RATIOS,
  TEST_DEFLECTION_LIMITS,
  createTestBeamData,
} from '../constants/beam-test-constants';

describe('Beam Analysis Save/Load Basic Tests', () => {
  describe('Constants and Test Data Validation', () => {
    it('should have valid test constants for all material types', () => {
      // Test beam dimensions
      expect(TEST_BEAM_DIMENSIONS.LENGTH).toBe(240);
      expect(TEST_BEAM_DIMENSIONS.SAWN_WIDTH).toBe(3.5);
      expect(TEST_BEAM_DIMENSIONS.SAWN_DEPTH).toBe(11.25);
      expect(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH).toBe(5.125);
      expect(TEST_BEAM_DIMENSIONS.GLULAM_DEPTH).toBe(18);
      expect(TEST_BEAM_DIMENSIONS.STEEL_SECTION_NAME).toBe("W12X26");
    });

    it('should have valid wood properties constants', () => {
      // Sawn lumber properties
      expect(TEST_WOOD_PROPERTIES.SAWN.SPECIES).toBe("Douglas Fir-Larch");
      expect(TEST_WOOD_PROPERTIES.SAWN.GRADE).toBe("Select Structural");
      expect(TEST_WOOD_PROPERTIES.SAWN.FB_PSI).toBe(1000);
      expect(TEST_WOOD_PROPERTIES.SAWN.E_PSI).toBe(1600000);

      // Glulam properties
      expect(TEST_WOOD_PROPERTIES.GLULAM.SPECIES_GROUP).toBe("Western Species");
      expect(TEST_WOOD_PROPERTIES.GLULAM.COMBINATION_SYMBOL).toBe("24F-V4");
      expect(TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI).toBe(2400);
      expect(TEST_WOOD_PROPERTIES.GLULAM.E_PSI).toBe(1800000);
    });

    it('should have valid steel properties constants', () => {
      expect(TEST_STEEL_PROPERTIES.GRADE_KEY).toBe("ASTM_A992_Gr 50");
      expect(TEST_STEEL_PROPERTIES.SHAPE_SIZE).toBe("W12X26");
      expect(TEST_STEEL_PROPERTIES.FY_PSI).toBe(50000);
      expect(TEST_STEEL_PROPERTIES.FU_PSI).toBe(65000);
    });

    it('should have valid analysis names', () => {
      expect(TEST_ANALYSIS_NAMES.SAWN_LUMBER).toBe("Test Sawn Lumber Beam Analysis");
      expect(TEST_ANALYSIS_NAMES.GLULAM).toBe("Test Glulam Beam Analysis");
      expect(TEST_ANALYSIS_NAMES.STEEL).toBe("Test Steel Beam Analysis");
    });

    it('should have valid expected stress ratios', () => {
      expect(EXPECTED_STRESS_RATIOS.SAFE_BENDING).toBe(0.8);
      expect(EXPECTED_STRESS_RATIOS.SAFE_SHEAR).toBe(0.6);
      expect(EXPECTED_STRESS_RATIOS.SAFE_DEFLECTION).toBe(0.7);
    });

    it('should have valid deflection limits', () => {
      expect(TEST_DEFLECTION_LIMITS.L_240).toBe(240);
      expect(TEST_DEFLECTION_LIMITS.L_360).toBe(360);
      expect(TEST_DEFLECTION_LIMITS.L_180).toBe(180);
    });
  });

  describe('Test Data Generation', () => {
    it('should create valid sawn lumber beam data', () => {
      const beamData = createTestBeamData('sawn');
      
      expect(beamData.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(beamData.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.SAWN.E_PSI); // Sawn E
      expect(beamData.properties.area).toBe(39.375); // 3.5 * 11.25
      expect(beamData.supports).toHaveLength(2);
      expect(beamData.loadGroups).toHaveLength(2);
      expect(beamData.designMethod).toBe('ASD');
    });

    it('should create valid glulam beam data', () => {
      const beamData = createTestBeamData('glulam');
      
      expect(beamData.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(beamData.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.GLULAM.E_PSI); // Glulam E
      expect(beamData.properties.area).toBe(92.25); // 5.125 * 18
      expect(beamData.supports).toHaveLength(2);
      expect(beamData.loadGroups).toHaveLength(2);
    });

    it('should create valid steel beam data', () => {
      const beamData = createTestBeamData('steel');
      
      expect(beamData.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(beamData.properties.elasticModulus).toBe(29000000); // Steel E
      expect(beamData.properties.momentOfInertia).toBe(204); // W12x26 Ix
      expect(beamData.properties.area).toBe(7.65); // W12x26 A
      expect(beamData.supports).toHaveLength(2);
      expect(beamData.loadGroups).toHaveLength(2);
    });
  });

  describe('Data Serialization/Deserialization', () => {
    it('should properly serialize and deserialize sawn lumber beam data', () => {
      const originalBeamData = createTestBeamData('sawn');
      
      // Simulate save/load cycle through JSON
      const serialized = JSON.stringify(originalBeamData);
      const deserialized = JSON.parse(serialized);
      
      // Verify data integrity
      expect(deserialized.properties.length).toBe(originalBeamData.properties.length);
      expect(deserialized.properties.elasticModulus).toBe(originalBeamData.properties.elasticModulus);
      expect(deserialized.properties.momentOfInertia).toBe(originalBeamData.properties.momentOfInertia);
      expect(deserialized.properties.area).toBe(originalBeamData.properties.area);
      expect(deserialized.supports).toHaveLength(originalBeamData.supports.length);
      expect(deserialized.loadGroups).toHaveLength(originalBeamData.loadGroups.length);
    });

    it('should properly serialize and deserialize glulam beam data', () => {
      const originalBeamData = createTestBeamData('glulam');
      
      const serialized = JSON.stringify(originalBeamData);
      const deserialized = JSON.parse(serialized);
      
      expect(deserialized.properties.length).toBe(originalBeamData.properties.length);
      expect(deserialized.properties.elasticModulus).toBe(originalBeamData.properties.elasticModulus);
      expect(deserialized.properties.momentOfInertia).toBe(originalBeamData.properties.momentOfInertia);
      expect(deserialized.properties.area).toBe(originalBeamData.properties.area);
    });

    it('should properly serialize and deserialize steel beam data', () => {
      const originalBeamData = createTestBeamData('steel');
      
      const serialized = JSON.stringify(originalBeamData);
      const deserialized = JSON.parse(serialized);
      
      expect(deserialized.properties.length).toBe(originalBeamData.properties.length);
      expect(deserialized.properties.elasticModulus).toBe(originalBeamData.properties.elasticModulus);
      expect(deserialized.properties.momentOfInertia).toBe(originalBeamData.properties.momentOfInertia);
      expect(deserialized.properties.area).toBe(originalBeamData.properties.area);
    });
  });

  describe('Material Type Differentiation', () => {
    it('should create different properties for each material type', () => {
      const sawnBeamData = createTestBeamData('sawn');
      const glulamBeamData = createTestBeamData('glulam');
      const steelBeamData = createTestBeamData('steel');
      
      // Elastic modulus should be different
      expect(sawnBeamData.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.SAWN.E_PSI);
      expect(glulamBeamData.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.GLULAM.E_PSI);
      expect(steelBeamData.properties.elasticModulus).toBe(29000000);
      
      // Moment of inertia should be different
      expect(sawnBeamData.properties.momentOfInertia).not.toBe(steelBeamData.properties.momentOfInertia);
      expect(glulamBeamData.properties.momentOfInertia).not.toBe(steelBeamData.properties.momentOfInertia);
      
      // Area should be different
      expect(sawnBeamData.properties.area).not.toBe(glulamBeamData.properties.area);
      expect(sawnBeamData.properties.area).not.toBe(steelBeamData.properties.area);
      expect(glulamBeamData.properties.area).not.toBe(steelBeamData.properties.area);
    });
  });

  describe('Validation Tests', () => {
    it('should validate beam properties exist for all material types', () => {
      const materialTypes = ['sawn', 'glulam', 'steel'] as const;
      
      materialTypes.forEach(materialType => {
        const beamData = createTestBeamData(materialType);
        
        expect(beamData.properties).toBeDefined();
        expect(beamData.properties.length).toBeGreaterThan(0);
        expect(beamData.properties.elasticModulus).toBeGreaterThan(0);
        expect(beamData.properties.momentOfInertia).toBeGreaterThan(0);
        expect(beamData.properties.area).toBeGreaterThan(0);
        expect(beamData.supports).toHaveLength(2);
        expect(beamData.loadGroups).toHaveLength(2);
      });
    });

    it('should validate constant consistency', () => {
      // Test that constants are internally consistent
      expect(TEST_BEAM_DIMENSIONS.SAWN_WIDTH * TEST_BEAM_DIMENSIONS.SAWN_DEPTH).toBe(39.375);
      expect(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * TEST_BEAM_DIMENSIONS.GLULAM_DEPTH).toBe(92.25);
      
      // Test that stress ratios are reasonable
      expect(EXPECTED_STRESS_RATIOS.SAFE_BENDING).toBeLessThan(1.0);
      expect(EXPECTED_STRESS_RATIOS.SAFE_SHEAR).toBeLessThan(1.0);
      expect(EXPECTED_STRESS_RATIOS.SAFE_DEFLECTION).toBeLessThan(1.0);
      
      // Test that critical ratios are higher than safe ratios
      expect(EXPECTED_STRESS_RATIOS.CRITICAL_BENDING).toBeGreaterThan(EXPECTED_STRESS_RATIOS.SAFE_BENDING);
      expect(EXPECTED_STRESS_RATIOS.CRITICAL_SHEAR).toBeGreaterThan(EXPECTED_STRESS_RATIOS.SAFE_SHEAR);
      expect(EXPECTED_STRESS_RATIOS.CRITICAL_DEFLECTION).toBeGreaterThan(EXPECTED_STRESS_RATIOS.SAFE_DEFLECTION);
    });
  });

  describe('Integration Readiness Tests', () => {
    it('should provide all necessary data for API tests', () => {
      // Verify all required test IDs are available
      expect(TEST_IDS.PROJECT_ID).toBeDefined();
      expect(TEST_IDS.ORGANIZATION_ID).toBeDefined();
      expect(TEST_IDS.CALCULATION_ID).toBeDefined();
      expect(TEST_IDS.BEAM_ANALYSIS_RESULT_ID).toBeDefined();
      
      // Verify test names are available
      expect(TEST_ANALYSIS_NAMES.SAWN_LUMBER).toBeDefined();
      expect(TEST_ANALYSIS_NAMES.GLULAM).toBeDefined();
      expect(TEST_ANALYSIS_NAMES.STEEL).toBeDefined();
    });

    it('should provide realistic material properties for testing', () => {
      // Wood properties should be realistic
      expect(TEST_WOOD_PROPERTIES.SAWN.FB_PSI).toBeGreaterThan(0);
      expect(TEST_WOOD_PROPERTIES.SAWN.FB_PSI).toBeLessThan(10000); // Reasonable upper bound
      expect(TEST_WOOD_PROPERTIES.SAWN.E_PSI).toBeGreaterThan(1000000); // At least 1000 ksi
      expect(TEST_WOOD_PROPERTIES.SAWN.E_PSI).toBeLessThan(3000000); // Less than 3000 ksi
      
      // Steel properties should be realistic
      expect(TEST_STEEL_PROPERTIES.FY_PSI).toBeGreaterThan(30000); // At least 30 ksi
      expect(TEST_STEEL_PROPERTIES.FY_PSI).toBeLessThan(100000); // Less than 100 ksi
      expect(TEST_STEEL_PROPERTIES.FU_PSI).toBeGreaterThan(TEST_STEEL_PROPERTIES.FY_PSI); // Fu > Fy
    });
  });

  describe('Design Method and Load Combinations Save/Load', () => {
    it('should preserve design method and selected load combinations through save/load cycle', () => {
      // Arrange
      const originalBeamData = createTestBeamData('sawn');
      originalBeamData.designMethod = 'LRFD';
      originalBeamData.selectedLoadCombos = ['1.4D', '1.2D+1.6L', '1.2D+1.6Lr+0.5W'];
      
      // Act - Simulate save operation
      const serializedBeamData = JSON.stringify(originalBeamData);
      
      // Simulate load operation
      const loadedBeamData = JSON.parse(serializedBeamData);
      
      // Assert - Verify data integrity
      expect(loadedBeamData.designMethod).toBe('LRFD');
      expect(loadedBeamData.selectedLoadCombos).toEqual(['1.4D', '1.2D+1.6L', '1.2D+1.6Lr+0.5W']);
      expect(loadedBeamData.selectedLoadCombos).toHaveLength(3);
    });

    it('should handle ASD design method with different load combinations', () => {
      // Arrange
      const originalBeamData = createTestBeamData('steel');
      originalBeamData.designMethod = 'ASD';
      originalBeamData.selectedLoadCombos = ['D', 'D+L', 'D+0.75L+0.75Lr'];
      
      // Act - Simulate save operation
      const serializedBeamData = JSON.stringify(originalBeamData);
      
      // Simulate load operation
      const loadedBeamData = JSON.parse(serializedBeamData);
      
      // Assert - Verify data integrity
      expect(loadedBeamData.designMethod).toBe('ASD');
      expect(loadedBeamData.selectedLoadCombos).toEqual(['D', 'D+L', 'D+0.75L+0.75Lr']);
    });

    it('should handle empty selected load combinations', () => {
      // Arrange
      const originalBeamData = createTestBeamData('glulam');
      originalBeamData.designMethod = 'ASD';
      originalBeamData.selectedLoadCombos = [];
      
      // Act - Simulate save operation
      const serializedBeamData = JSON.stringify(originalBeamData);
      
      // Simulate load operation
      const loadedBeamData = JSON.parse(serializedBeamData);
      
      // Assert - Verify data integrity
      expect(loadedBeamData.designMethod).toBe('ASD');
      expect(loadedBeamData.selectedLoadCombos).toEqual([]);
      expect(Array.isArray(loadedBeamData.selectedLoadCombos)).toBe(true);
    });
  });
}); 