/**
 * Integration tests for beam analysis save/load functionality
 * Tests the core save/load logic without complex API mocking
 */

import { describe, it, expect } from '@jest/globals';
import { 
  TEST_BEAM_DIMENSIONS,
  TEST_WOOD_PROPERTIES,
  TEST_STEEL_PROPERTIES,
  TEST_ANALYSIS_NAMES,
  createTestBeamData,
  EXPECTED_STRESS_RATIOS,
} from '../constants/beam-test-constants';
import {
  createSawnLumberBeamPropertiesState,
  createGlulamBeamPropertiesState,
  createSteelBeamPropertiesState,
} from '../helpers/beam-properties-helpers';

describe('Beam Analysis Save/Load Integration Tests', () => {
  describe('Data Serialization and Integrity', () => {
    it('should preserve sawn lumber data through save/load cycle', () => {
      // Arrange
      const originalBeamData = createTestBeamData('sawn');
      const originalBeamProperties = createSawnLumberBeamPropertiesState();
      
      // Act - Simulate save operation
      const serializedBeamData = JSON.stringify(originalBeamData);
      const serializedBeamProperties = JSON.stringify(originalBeamProperties);
      
      // Simulate load operation
      const loadedBeamData = JSON.parse(serializedBeamData);
      const loadedBeamProperties = JSON.parse(serializedBeamProperties);
      
      // Assert - Verify data integrity
      expect(loadedBeamData.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(loadedBeamData.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.SAWN.E_PSI);
      expect(loadedBeamData.properties.area).toBe(TEST_BEAM_DIMENSIONS.SAWN_WIDTH * TEST_BEAM_DIMENSIONS.SAWN_DEPTH);
      expect(loadedBeamData.supports).toHaveLength(2);
      expect(loadedBeamData.loadGroups).toHaveLength(2);
      
      expect(loadedBeamProperties.selectedMaterial).toBe("wood");
      expect(loadedBeamProperties.lumberType).toBe("sawn");
      expect(loadedBeamProperties.selectedSpecies).toBe(TEST_WOOD_PROPERTIES.SAWN.SPECIES);
      expect(loadedBeamProperties.selectedGrade).toBe(TEST_WOOD_PROPERTIES.SAWN.GRADE);
    });

    it('should preserve glulam data through save/load cycle', () => {
      // Arrange
      const originalBeamData = createTestBeamData('glulam');
      const originalBeamProperties = createGlulamBeamPropertiesState();
      
      // Act - Simulate save operation
      const serializedBeamData = JSON.stringify(originalBeamData);
      const serializedBeamProperties = JSON.stringify(originalBeamProperties);
      
      // Simulate load operation
      const loadedBeamData = JSON.parse(serializedBeamData);
      const loadedBeamProperties = JSON.parse(serializedBeamProperties);
      
      // Assert - Verify data integrity
      expect(loadedBeamData.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(loadedBeamData.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.GLULAM.E_PSI);
      expect(loadedBeamData.properties.area).toBe(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * TEST_BEAM_DIMENSIONS.GLULAM_DEPTH);
      
      expect(loadedBeamProperties.selectedMaterial).toBe("wood");
      expect(loadedBeamProperties.lumberType).toBe("glulam");
      expect(loadedBeamProperties.selectedSpecies).toBe(TEST_WOOD_PROPERTIES.GLULAM.SPECIES);
      expect(loadedBeamProperties.selectedGluLamProperties).toBeDefined();
      expect(loadedBeamProperties.selectedGluLamProperties.speciesGroup).toBe(TEST_WOOD_PROPERTIES.GLULAM.SPECIES_GROUP);
    });

    it('should preserve steel data through save/load cycle', () => {
      // Arrange
      const originalBeamData = createTestBeamData('steel');
      const originalBeamProperties = createSteelBeamPropertiesState();
      
      // Act - Simulate save operation
      const serializedBeamData = JSON.stringify(originalBeamData);
      const serializedBeamProperties = JSON.stringify(originalBeamProperties);
      
      // Simulate load operation
      const loadedBeamData = JSON.parse(serializedBeamData);
      const loadedBeamProperties = JSON.parse(serializedBeamProperties);
      
      // Assert - Verify data integrity
      expect(loadedBeamData.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(loadedBeamData.properties.elasticModulus).toBe(29000000); // Steel E = 29,000 ksi
      expect(loadedBeamData.properties.momentOfInertia).toBe(204); // W12x26 Ix
      expect(loadedBeamData.properties.area).toBe(7.65); // W12x26 A
      
      expect(loadedBeamProperties.selectedMaterial).toBe("steel");
      expect(loadedBeamProperties.selectedSteelGrade).toBe(TEST_STEEL_PROPERTIES.GRADE_KEY);
      expect(loadedBeamProperties.selectedSteelShapeSize).toBe(TEST_STEEL_PROPERTIES.SHAPE_SIZE);
      expect(loadedBeamProperties.steelDesignValues.Fy).toBe(TEST_STEEL_PROPERTIES.FY_PSI);
    });
  });

  describe('Cross-Material Compatibility', () => {
    it('should handle different material types correctly', () => {
      const materialTypes = ['sawn', 'glulam', 'steel'] as const;
      
      materialTypes.forEach(materialType => {
        const beamData = createTestBeamData(materialType);
        const serialized = JSON.stringify(beamData);
        const loaded = JSON.parse(serialized);
        
        expect(loaded.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
        expect(loaded.supports).toHaveLength(2);
        expect(loaded.loadGroups).toHaveLength(2);
        
        // Verify material-specific elastic modulus
        switch (materialType) {
          case 'sawn':
          case 'glulam':
            expect(loaded.properties.elasticModulus).toBeGreaterThan(1000000);
            expect(loaded.properties.elasticModulus).toBeLessThan(3000000);
            break;
          case 'steel':
            expect(loaded.properties.elasticModulus).toBe(29000000);
            break;
        }
      });
    });
  });

  describe('Analysis Results Handling', () => {
    it('should handle analysis results serialization', () => {
      const mockResults = {
        maxBendingStressRatio: {
          ratio: EXPECTED_STRESS_RATIOS.SAFE_BENDING,
          value: 800,
          position: TEST_BEAM_DIMENSIONS.LENGTH / 2,
          loadComboName: '1.2D+1.6L',
          actualStress: 800,
          allowableStress: 1000,
          spanNumber: 1,
        },
        maxShearStressRatio: {
          ratio: EXPECTED_STRESS_RATIOS.SAFE_SHEAR,
          value: 100,
          position: 0,
          loadComboName: '1.2D+1.6L',
          actualStress: 100,
          allowableStress: 167,
          spanNumber: 1,
        },
      };

      // Simulate save/load cycle
      const serialized = JSON.stringify(mockResults);
      const loaded = JSON.parse(serialized);

      expect(loaded.maxBendingStressRatio.ratio).toBe(EXPECTED_STRESS_RATIOS.SAFE_BENDING);
      expect(loaded.maxShearStressRatio.ratio).toBe(EXPECTED_STRESS_RATIOS.SAFE_SHEAR);
      expect(loaded.maxBendingStressRatio.actualStress).toBe(800);
      expect(loaded.maxBendingStressRatio.allowableStress).toBe(1000);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle missing properties gracefully', () => {
      const incompleteData = {
        properties: {
          length: TEST_BEAM_DIMENSIONS.LENGTH,
          elasticModulus: TEST_WOOD_PROPERTIES.SAWN.E_PSI,
          // Missing momentOfInertia and area
        },
        supports: [],
        loadGroups: [],
      };

      const serialized = JSON.stringify(incompleteData);
      const loaded = JSON.parse(serialized);

      expect(loaded.properties.length).toBe(TEST_BEAM_DIMENSIONS.LENGTH);
      expect(loaded.properties.elasticModulus).toBe(TEST_WOOD_PROPERTIES.SAWN.E_PSI);
      expect(loaded.supports).toEqual([]);
      expect(loaded.loadGroups).toEqual([]);
    });

    it('should maintain data types after serialization', () => {
      const beamData = createTestBeamData('sawn');
      const serialized = JSON.stringify(beamData);
      const loaded = JSON.parse(serialized);

      // Verify numeric properties remain numeric
      expect(typeof loaded.properties.length).toBe('number');
      expect(typeof loaded.properties.elasticModulus).toBe('number');
      expect(typeof loaded.properties.momentOfInertia).toBe('number');
      expect(typeof loaded.properties.area).toBe('number');

      // Verify arrays remain arrays
      expect(Array.isArray(loaded.supports)).toBe(true);
      expect(Array.isArray(loaded.loadGroups)).toBe(true);
      expect(Array.isArray(loaded.selectedLoadCombos)).toBe(true);
    });
  });
}); 