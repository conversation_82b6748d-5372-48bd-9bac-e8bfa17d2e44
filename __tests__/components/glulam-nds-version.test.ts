import {
  DEFAULT_NDS_VERSION,
  NDS_VERSIONS,
} from '@/lib/constants/nds-constants';

// Mock GluLam state interface for testing
interface MockGluLamState {
  selectedNdsVersion?: string;
  selectedGluLamGroup?: string;
  selectedGluLamSpecies?: string;
  selectedCombinedSymbolKey?: string | null;
  loadingOrientation?: 'perpendicular' | 'parallel';
}

// Utility functions that replicate glulam component logic
function createInitialGluLamState(): MockGluLamState {
  return {
    selectedNdsVersion: DEFAULT_NDS_VERSION,
    selectedGluLamGroup: '',
    selectedGluLamSpecies: '',
    selectedCombinedSymbolKey: null,
    loadingOrientation: 'perpendicular',
  };
}

function handleGluLamNdsVersionChangeLogic(
  currentState: MockGluLamState,
  newVersion: string
): MockGluLamState {
  // If version hasn't changed, return current state
  if (currentState.selectedNdsVersion === newVersion) {
    return currentState;
  }

  // Reset all glulam selections when version changes
  return {
    ...currentState,
    selectedNdsVersion: newVersion,
    selectedGluLamGroup: '',
    selectedGluLamSpecies: '',
    selectedCombinedSymbolKey: null,
    loadingOrientation: 'perpendicular',
  };
}

function validateGluLamNdsVersionState(state: MockGluLamState): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!state.selectedNdsVersion) {
    errors.push('NDS version is required for glulam');
  }

  if (state.selectedNdsVersion && !Object.values(NDS_VERSIONS).includes(state.selectedNdsVersion as any)) {
    errors.push(`Invalid NDS version for glulam: ${state.selectedNdsVersion}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Mock glulam data structure
const mockGluLamData = {
  availableVersions: [DEFAULT_NDS_VERSION, 'NDS 2015'],
  speciesGroups: ['Douglas Fir-Larch (DF)', 'Southern Pine'],
  combinationSymbols: {
    'Douglas Fir-Larch (DF)': ['24F-V4', '24F-V8'],
    'Southern Pine': ['24F-V4', '24F-V8'],
  },
};

describe('GluLam NDS Version Selection Logic', () => {
  describe('Initial State Creation', () => {
    it('should create initial state with default NDS version', () => {
      const state = createInitialGluLamState();

      expect(state.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
      expect(state.selectedGluLamGroup).toBe('');
      expect(state.selectedGluLamSpecies).toBe('');
      expect(state.selectedCombinedSymbolKey).toBeNull();
      expect(state.loadingOrientation).toBe('perpendicular');
    });
  });

  describe('NDS Version Change Handling', () => {
    it('should not change state when version is the same', () => {
      const initialState = createInitialGluLamState();
      const newState = handleGluLamNdsVersionChangeLogic(initialState, DEFAULT_NDS_VERSION);

      expect(newState).toEqual(initialState);
    });

    it('should reset all glulam selections when version changes', () => {
      const initialState: MockGluLamState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
        selectedGluLamSpecies: 'Douglas Fir',
        selectedCombinedSymbolKey: '5A_24F-V4',
        loadingOrientation: 'parallel',
      };

      const newState = handleGluLamNdsVersionChangeLogic(initialState, 'NDS 2015');

      expect(newState.selectedNdsVersion).toBe('NDS 2015');
      expect(newState.selectedGluLamGroup).toBe('');
      expect(newState.selectedGluLamSpecies).toBe('');
      expect(newState.selectedCombinedSymbolKey).toBeNull();
      expect(newState.loadingOrientation).toBe('perpendicular');
    });

    it('should handle version change from NDS 2015 to NDS 2018', () => {
      const initialState: MockGluLamState = {
        selectedNdsVersion: 'NDS 2015',
        selectedGluLamGroup: 'Southern Pine',
        selectedGluLamSpecies: 'Southern Pine',
        selectedCombinedSymbolKey: '5A_24F-V8',
        loadingOrientation: 'parallel',
      };

      const newState = handleGluLamNdsVersionChangeLogic(initialState, DEFAULT_NDS_VERSION);

      expect(newState.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
      expect(newState.selectedGluLamGroup).toBe('');
      expect(newState.selectedGluLamSpecies).toBe('');
      expect(newState.selectedCombinedSymbolKey).toBeNull();
      expect(newState.loadingOrientation).toBe('perpendicular');
    });
  });

  describe('State Validation', () => {
    it('should validate correct glulam NDS version state', () => {
      const validState: MockGluLamState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
      };

      const validation = validateGluLamNdsVersionState(validState);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing NDS version for glulam', () => {
      const invalidState: MockGluLamState = {
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
      };

      const validation = validateGluLamNdsVersionState(invalidState);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('NDS version is required for glulam');
    });

    it('should detect invalid NDS version for glulam', () => {
      const invalidState: MockGluLamState = {
        selectedNdsVersion: 'Invalid Version',
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
      };

      const validation = validateGluLamNdsVersionState(invalidState);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid NDS version for glulam: Invalid Version');
    });

    it('should accept all valid NDS versions for glulam', () => {
      Object.values(NDS_VERSIONS).forEach(version => {
        const state: MockGluLamState = {
          selectedNdsVersion: version,
        };

        const validation = validateGluLamNdsVersionState(state);
        expect(validation.isValid).toBe(true);
      });
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle full workflow from initial state to version change for glulam', () => {
      // Start with initial state
      let state = createInitialGluLamState();
      expect(state.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);

      // Simulate user making glulam selections
      state = {
        ...state,
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
        selectedGluLamSpecies: 'Douglas Fir',
        selectedCombinedSymbolKey: '5A_24F-V4',
        loadingOrientation: 'parallel',
      };

      // Validate current state
      let validation = validateGluLamNdsVersionState(state);
      expect(validation.isValid).toBe(true);

      // Change version - should reset selections
      state = handleGluLamNdsVersionChangeLogic(state, 'NDS 2015');
      expect(state.selectedNdsVersion).toBe('NDS 2015');
      expect(state.selectedGluLamGroup).toBe('');
      expect(state.selectedGluLamSpecies).toBe('');
      expect(state.selectedCombinedSymbolKey).toBeNull();
      expect(state.loadingOrientation).toBe('perpendicular');

      // Validate after version change
      validation = validateGluLamNdsVersionState(state);
      expect(validation.isValid).toBe(true);
    });

    it('should handle loading old glulam state without NDS version', () => {
      // Simulate loading old glulam state from database
      const oldSavedState: MockGluLamState = {
        selectedGluLamGroup: 'Southern Pine',
        selectedGluLamSpecies: 'Southern Pine',
        selectedCombinedSymbolKey: '5A_24F-V8',
        loadingOrientation: 'parallel',
      };

      // Apply backward compatibility (would be done in component)
      let state = {
        ...oldSavedState,
        selectedNdsVersion: DEFAULT_NDS_VERSION,
      };

      // Validate the restored state
      const validation = validateGluLamNdsVersionState(state);
      expect(validation.isValid).toBe(true);

      // Ensure original glulam selections are preserved
      expect(state.selectedGluLamGroup).toBe('Southern Pine');
      expect(state.selectedGluLamSpecies).toBe('Southern Pine');
      expect(state.selectedCombinedSymbolKey).toBe('5A_24F-V8');
      expect(state.loadingOrientation).toBe('parallel');
    });
  });

  describe('GluLam Specific Features', () => {
    it('should handle loading orientation changes independently of version', () => {
      const state = createInitialGluLamState();
      
      // Loading orientation should be independent of version changes
      const stateWithParallel = {
        ...state,
        loadingOrientation: 'parallel' as const,
      };

      expect(stateWithParallel.loadingOrientation).toBe('parallel');
      expect(stateWithParallel.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
    });

    it('should handle combination symbol selection with version context', () => {
      const state: MockGluLamState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
        selectedGluLamSpecies: 'Douglas Fir',
        selectedCombinedSymbolKey: '5A_24F-V4',
        loadingOrientation: 'perpendicular',
      };

      // Combination symbols should be version-specific
      expect(state.selectedCombinedSymbolKey).toBe('5A_24F-V4');
      expect(state.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
    });

    it('should reset combination symbol when version changes', () => {
      const initialState: MockGluLamState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
        selectedGluLamSpecies: 'Douglas Fir',
        selectedCombinedSymbolKey: '5A_24F-V4',
        loadingOrientation: 'perpendicular',
      };

      const newState = handleGluLamNdsVersionChangeLogic(initialState, 'NDS 2015');

      // Combination symbol should be reset because it's version-specific
      expect(newState.selectedCombinedSymbolKey).toBeNull();
      expect(newState.selectedNdsVersion).toBe('NDS 2015');
    });
  });

  describe('Constants Usage in GluLam', () => {
    it('should use constants consistently for glulam', () => {
      const state = createInitialGluLamState();

      // Verify constants are used throughout
      expect(state.selectedNdsVersion).toBe(NDS_VERSIONS.NDS_2018);
      expect(state.loadingOrientation).toBe('perpendicular');
    });

    it('should handle all defined NDS versions for glulam', () => {
      const allVersions = Object.values(NDS_VERSIONS);
      
      allVersions.forEach(version => {
        const state = createInitialGluLamState();
        const newState = handleGluLamNdsVersionChangeLogic(state, version);
        expect(newState.selectedNdsVersion).toBe(version);
      });
    });
  });

  describe('Edge Cases for GluLam', () => {
    it('should handle empty string version gracefully', () => {
      const state: MockGluLamState = {
        selectedNdsVersion: '',
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
      };

      const validation = validateGluLamNdsVersionState(state);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('NDS version is required for glulam');
    });

    it('should handle undefined version gracefully', () => {
      const state: MockGluLamState = {
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
      };

      const validation = validateGluLamNdsVersionState(state);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('NDS version is required for glulam');
    });

    it('should handle special characters in version names for glulam', () => {
      const specialVersion = 'NDS 2018 (Updated)';
      const state: MockGluLamState = {
        selectedNdsVersion: specialVersion,
        selectedGluLamGroup: 'Douglas Fir-Larch (DF)',
      };

      // This should fail validation since it's not in our defined versions
      const validation = validateGluLamNdsVersionState(state);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(`Invalid NDS version for glulam: ${specialVersion}`);
    });
  });
}); 