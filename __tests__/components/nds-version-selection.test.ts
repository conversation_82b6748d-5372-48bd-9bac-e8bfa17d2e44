import {
  DEFAULT_NDS_VERSION,
  DEFAULT_WOOD_SELECTIONS,
  NDS_VERSIONS,
} from '@/lib/constants/nds-constants';

// Mock BeamPropertiesState interface for testing
interface MockBeamPropertiesState {
  selectedNdsVersion?: string;
  selectedSpecies?: string;
  selectedSpeciesCombination?: string;
  selectedGrade?: string;
  selectedSizeClassification?: string;
  selectedNominalSize?: string;
}

// Utility functions that replicate component logic
function createInitialState(): MockBeamPropertiesState {
  return {
    selectedNdsVersion: DEFAULT_NDS_VERSION,
    selectedSpecies: DEFAULT_WOOD_SELECTIONS.SPECIES,
    selectedSpeciesCombination: DEFAULT_WOOD_SELECTIONS.SPECIES_COMBINATION,
    selectedGrade: DEFAULT_WOOD_SELECTIONS.GRADE,
    selectedSizeClassification: DEFAULT_WOOD_SELECTIONS.SIZE_CLASSIFICATION,
    selectedNominalSize: '',
  };
}

function handleNdsVersionChangeLogic(
  currentState: MockBeamPropertiesState,
  newVersion: string
): MockBeamPropertiesState {
  // If version hasn't changed, return current state
  if (currentState.selectedNdsVersion === newVersion) {
    return currentState;
  }

  // Reset all wood selections when version changes
  return {
    ...currentState,
    selectedNdsVersion: newVersion,
    selectedSpecies: '',
    selectedSpeciesCombination: '',
    selectedGrade: '',
    selectedSizeClassification: '',
    selectedNominalSize: '',
  };
}

function ensureBackwardCompatibility(state: MockBeamPropertiesState): MockBeamPropertiesState {
  if (!state.selectedNdsVersion) {
    return {
      ...state,
      selectedNdsVersion: DEFAULT_NDS_VERSION,
    };
  }
  return state;
}

function validateNdsVersionState(state: MockBeamPropertiesState): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!state.selectedNdsVersion) {
    errors.push('NDS version is required');
  }

  if (state.selectedNdsVersion && !Object.values(NDS_VERSIONS).includes(state.selectedNdsVersion as any)) {
    errors.push(`Invalid NDS version: ${state.selectedNdsVersion}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Mock wood data structure
const mockWoodData = {
  availableVersions: [DEFAULT_NDS_VERSION, 'NDS 2015'],
  speciesCombinations: ['DOUGLAS FIR-LARCH', 'HEM-FIR'],
  commercialGrades: {
    'DOUGLAS FIR-LARCH': ['No. 1', 'No. 2'],
    'HEM-FIR': ['No. 2'],
  },
};

describe('NDS Version Selection Component Logic', () => {
  describe('Initial State Creation', () => {
    it('should create initial state with default values', () => {
      const state = createInitialState();

      expect(state.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
      expect(state.selectedSpecies).toBe(DEFAULT_WOOD_SELECTIONS.SPECIES);
      expect(state.selectedSpeciesCombination).toBe(DEFAULT_WOOD_SELECTIONS.SPECIES_COMBINATION);
      expect(state.selectedGrade).toBe(DEFAULT_WOOD_SELECTIONS.GRADE);
      expect(state.selectedSizeClassification).toBe(DEFAULT_WOOD_SELECTIONS.SIZE_CLASSIFICATION);
    });

    it('should have empty nominal size by default', () => {
      const state = createInitialState();
      expect(state.selectedNominalSize).toBe('');
    });
  });

  describe('NDS Version Change Handling', () => {
    it('should not change state when version is the same', () => {
      const initialState = createInitialState();
      const newState = handleNdsVersionChangeLogic(initialState, DEFAULT_NDS_VERSION);

      expect(newState).toEqual(initialState);
    });

    it('should reset all wood selections when version changes', () => {
      const initialState: MockBeamPropertiesState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedSpecies: 'DOUGLAS FIR',
        selectedSpeciesCombination: 'DOUGLAS FIR-LARCH',
        selectedGrade: 'No. 2',
        selectedSizeClassification: '2" & wider',
        selectedNominalSize: '2x8',
      };

      const newState = handleNdsVersionChangeLogic(initialState, 'NDS 2015');

      expect(newState.selectedNdsVersion).toBe('NDS 2015');
      expect(newState.selectedSpecies).toBe('');
      expect(newState.selectedSpeciesCombination).toBe('');
      expect(newState.selectedGrade).toBe('');
      expect(newState.selectedSizeClassification).toBe('');
      expect(newState.selectedNominalSize).toBe('');
    });

    it('should preserve non-wood related properties', () => {
      const initialState: MockBeamPropertiesState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedSpecies: 'DOUGLAS FIR',
      };

      const newState = handleNdsVersionChangeLogic(initialState, 'NDS 2015');

      // Should update the version and reset wood-specific values
      expect(newState.selectedNdsVersion).toBe('NDS 2015');
      // All wood selections should be reset to empty strings
      expect(newState.selectedSpecies).toBe('');
      expect(newState.selectedSpeciesCombination).toBe('');
      expect(newState.selectedGrade).toBe('');
      expect(newState.selectedSizeClassification).toBe('');
      expect(newState.selectedNominalSize).toBe('');
    });
  });

  describe('Backward Compatibility', () => {
    it('should add default NDS version to state without version', () => {
      const stateWithoutVersion: MockBeamPropertiesState = {
        selectedSpecies: 'DOUGLAS FIR',
        selectedGrade: 'No. 2',
      };

      const updatedState = ensureBackwardCompatibility(stateWithoutVersion);

      expect(updatedState.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
      expect(updatedState.selectedSpecies).toBe('DOUGLAS FIR');
      expect(updatedState.selectedGrade).toBe('No. 2');
    });

    it('should not modify state that already has NDS version', () => {
      const stateWithVersion: MockBeamPropertiesState = {
        selectedNdsVersion: 'NDS 2015',
        selectedSpecies: 'HEM-FIR',
      };

      const updatedState = ensureBackwardCompatibility(stateWithVersion);

      expect(updatedState).toEqual(stateWithVersion);
      expect(updatedState.selectedNdsVersion).toBe('NDS 2015');
    });

    it('should handle empty string as missing version', () => {
      const stateWithEmptyVersion: MockBeamPropertiesState = {
        selectedNdsVersion: '',
        selectedSpecies: 'DOUGLAS FIR',
      };

      const updatedState = ensureBackwardCompatibility(stateWithEmptyVersion);

      expect(updatedState.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);
    });
  });

  describe('State Validation', () => {
    it('should validate correct NDS version state', () => {
      const validState: MockBeamPropertiesState = {
        selectedNdsVersion: DEFAULT_NDS_VERSION,
        selectedSpecies: 'DOUGLAS FIR',
      };

      const validation = validateNdsVersionState(validState);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing NDS version', () => {
      const invalidState: MockBeamPropertiesState = {
        selectedSpecies: 'DOUGLAS FIR',
      };

      const validation = validateNdsVersionState(invalidState);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('NDS version is required');
    });

    it('should detect invalid NDS version', () => {
      const invalidState: MockBeamPropertiesState = {
        selectedNdsVersion: 'Invalid Version',
        selectedSpecies: 'DOUGLAS FIR',
      };

      const validation = validateNdsVersionState(invalidState);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid NDS version: Invalid Version');
    });

    it('should accept all valid NDS versions', () => {
      Object.values(NDS_VERSIONS).forEach(version => {
        const state: MockBeamPropertiesState = {
          selectedNdsVersion: version,
        };

        const validation = validateNdsVersionState(state);
        expect(validation.isValid).toBe(true);
      });
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle full workflow from initial state to version change', () => {
      // Start with initial state
      let state = createInitialState();
      expect(state.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);

      // Simulate user making selections
      state = {
        ...state,
        selectedSpeciesCombination: 'DOUGLAS FIR-LARCH',
        selectedGrade: 'No. 2',
        selectedNominalSize: '2x8',
      };

      // Validate current state
      let validation = validateNdsVersionState(state);
      expect(validation.isValid).toBe(true);

      // Change version - should reset selections
      state = handleNdsVersionChangeLogic(state, 'NDS 2015');
      expect(state.selectedNdsVersion).toBe('NDS 2015');
      expect(state.selectedSpeciesCombination).toBe('');
      expect(state.selectedGrade).toBe('');
      expect(state.selectedNominalSize).toBe('');

      // Validate after version change
      validation = validateNdsVersionState(state);
      expect(validation.isValid).toBe(true);
    });

    it('should handle loading old saved state without NDS version', () => {
      // Simulate loading old state from database
      const oldSavedState: MockBeamPropertiesState = {
        selectedSpeciesCombination: 'HEM-FIR',
        selectedGrade: 'No. 1',
        selectedNominalSize: '2x10',
      };

      // Apply backward compatibility
      let state = ensureBackwardCompatibility(oldSavedState);
      expect(state.selectedNdsVersion).toBe(DEFAULT_NDS_VERSION);

      // Validate the restored state
      const validation = validateNdsVersionState(state);
      expect(validation.isValid).toBe(true);

      // Ensure original selections are preserved
      expect(state.selectedSpeciesCombination).toBe('HEM-FIR');
      expect(state.selectedGrade).toBe('No. 1');
      expect(state.selectedNominalSize).toBe('2x10');
    });
  });

  describe('Constants Usage', () => {
    it('should use constants consistently', () => {
      const state = createInitialState();

      // Verify constants are used throughout
      expect(state.selectedNdsVersion).toBe(NDS_VERSIONS.NDS_2018);
      expect(state.selectedSpecies).toBe(DEFAULT_WOOD_SELECTIONS.SPECIES);
      expect(state.selectedSpeciesCombination).toBe(DEFAULT_WOOD_SELECTIONS.SPECIES_COMBINATION);
    });

    it('should handle all defined NDS versions', () => {
      const allVersions = Object.values(NDS_VERSIONS);
      
      allVersions.forEach(version => {
        const state = createInitialState();
        const newState = handleNdsVersionChangeLogic(state, version);
        expect(newState.selectedNdsVersion).toBe(version);
      });
    });
  });
}); 