// Test constants for beam analysis testing
// Extracting magic strings and numbers for reusability

import { <PERSON><PERSON>D<PERSON>, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { Support } from "@/lib/types/support/support";
import { SupportType } from "@/lib/types/support/support-type";
import { LoadGroup } from "@/lib/types/load/load-group";
import { Load } from "@/lib/types/load/load";
import { LoadType, Type as LoadApplicationType } from "@/lib/types/load/load-type";

// Common test dimensions and properties
export const TEST_BEAM_DIMENSIONS = {
  LENGTH: 240, // inches (20 feet)
  SAWN_WIDTH: 3.5, // inches (2x12 nominal)
  SAWN_DEPTH: 11.25, // inches (2x12 actual)
  GLULAM_WIDTH: 5.125, // inches
  GLULAM_DEPTH: 18, // inches
  STEEL_SECTION_NAME: "W12X26",
} as const;

// Common material properties
export const TEST_MATERIAL_PROPERTIES = {
  WOOD: {
    ELASTIC_MODULUS: 1600000, // psi (1600 ksi)
    SAWN_MOMENT_OF_INERTIA: 177.979, // in^4 for 2x12
    GLULAM_MOMENT_OF_INERTIA: 1403.91, // in^4 for 5.125x18
    SAWN_AREA: 39.375, // in^2 for 2x12
    GLULAM_AREA: 92.25, // in^2 for 5.125x18
  },
  STEEL: {
    ELASTIC_MODULUS: 29000000, // psi (29000 ksi)
    W12X26_AREA: 7.65, // in^2
    W12X26_IX: 204, // in^4
    W12X26_SX: 33.4, // in^3
    W12X26_RX: 5.17, // in
    W12X26_IY: 17.3, // in^4
    W12X26_SY: 5.34, // in^3
    W12X26_RY: 1.51, // in
    W12X26_J: 0.457, // in^4
    W12X26_CW: 695, // in^6
    W12X26_DEPTH: 12.22, // in
    W12X26_FLANGE_WIDTH: 6.490, // in
    W12X26_WEB_THICKNESS: 0.230, // in
    W12X26_FLANGE_THICKNESS: 0.380, // in
    W12X26_WEIGHT: 26, // plf
  },
} as const;

// Common load values
export const TEST_LOADS = {
  DEAD_LOAD: 0.5, // kips/ft
  LIVE_LOAD: 2.0, // kips/ft
  POINT_LOAD: 10.0, // kips
  LOAD_POSITION: 120, // inches (mid-span)
} as const;

// Test analysis names
export const TEST_ANALYSIS_NAMES = {
  SAWN_LUMBER: "Test Sawn Lumber Beam Analysis",
  GLULAM: "Test Glulam Beam Analysis",
  STEEL: "Test Steel Beam Analysis",
  UNNAMED: "Unnamed Analysis",
} as const;

// Common support positions
export const TEST_SUPPORT_POSITIONS = {
  LEFT_SUPPORT: 0,
  RIGHT_SUPPORT: TEST_BEAM_DIMENSIONS.LENGTH,
} as const;

// Steel grade and shape test data
export const TEST_STEEL_PROPERTIES = {
  GRADE_KEY: "ASTM_A992_Gr 50",
  ASTM_DESIGNATION: "ASTM A992",
  GRADE: "Gr 50",
  SHAPE_TYPE: "W",
  SHAPE_SIZE: TEST_BEAM_DIMENSIONS.STEEL_SECTION_NAME,
  FY_PSI: 50000, // psi
  FU_PSI: 65000, // psi
  UNBRACED_LENGTH: "10.0", // feet
  // W12x26 section properties
  W12X26_AREA: 7.65, // in^2
  W12X26_DEPTH: 12.22, // in
  W12X26_FLANGE_WIDTH: 6.490, // in
  W12X26_WEB_THICKNESS: 0.230, // in
  W12X26_FLANGE_THICKNESS: 0.380, // in
  W12X26_IX: 204, // in^4
  W12X26_IY: 17.3, // in^4
  W12X26_SX: 33.4, // in^3
  W12X26_SY: 5.34, // in^3
  W12X26_RX: 5.16, // in
  W12X26_RY: 1.51, // in
  W12X26_J: 0.457, // in^4
  W12X26_CW: 695, // in^6
  W12X26_WEIGHT: 26, // plf
} as const;

// Wood material test data
export const TEST_WOOD_PROPERTIES = {
  SAWN: {
    SPECIES: "Douglas Fir-Larch",
    GRADE: "Select Structural",
    SIZE_CLASSIFICATION: "Dimension Lumber",
    NOMINAL_SIZE: "2x12",
    NDS_VERSION: "2018",
    FB_PSI: 1000,
    FT_PSI: 675,
    FV_PSI: 175,
    FC_PERP_PSI: 625,
    FC_PSI: 1350,
    E_PSI: 1600000,
    EMIN_PSI: 580000,
    G_PSI: 90000,
  },
  GLULAM: {
    SPECIES_GROUP: "Western Species",
    SPECIES: "Douglas Fir",
    COMBINATION_SYMBOL: "24F-V4",
    GRADE: "V4",
    FB_POS_PSI: 2400,
    FB_NEG_PSI: 1850,
    FT_PSI: 1450,
    FV_PSI: 265,
    FC_PERP_PSI: 650,
    FC_PSI: 1600,
    E_PSI: 1800000,
    EMIN_PSI: 850000,
    G_PSI: 90000,
  },
} as const;

// Common environmental conditions
export const TEST_ENVIRONMENTAL_CONDITIONS = {
  DRY_MOISTURE_CONTENT: 19, // percent
  WET_MOISTURE_CONTENT: 25, // percent
  NORMAL_TEMPERATURE: 70, // Fahrenheit
  HIGH_TEMPERATURE: 150, // Fahrenheit
} as const;

// Deflection limits
export const TEST_DEFLECTION_LIMITS = {
  L_240: 240,
  L_360: 360,
  L_180: 180,
} as const;

// Expected stress ratios for validation (approximate)
export const EXPECTED_STRESS_RATIOS = {
  SAFE_BENDING: 0.8,
  SAFE_SHEAR: 0.6,
  SAFE_DEFLECTION: 0.7,
  CRITICAL_BENDING: 0.95,
  CRITICAL_SHEAR: 0.9,
  CRITICAL_DEFLECTION: 0.95,
} as const;

// API endpoints
export const TEST_API_ENDPOINTS = {
  BEAM_ANALYSIS_RESULTS: "/api/beam-analysis/results",
  BEAM_ANALYSIS_RESULTS_BY_ID: (id: string) => `/api/beam-analysis/results/${id}`,
  CALCULATION_ANALYSIS_RESULTS: (id: string) => `/api/calculations/${id}/analysis-results`,
  TRIGGER_REANALYSIS: (id: string) => `/api/calculations/${id}/trigger-reanalysis`,
} as const;

// Mock project and organization IDs
export const TEST_IDS = {
  PROJECT_ID: "test-project-123",
  ORGANIZATION_ID: "test-org-456",
  CALCULATION_ID: "test-calc-789",
  BEAM_ANALYSIS_RESULT_ID: "test-bar-101112",
} as const;

// Helper function to create basic supports
export function createTestSupports(): Support[] {
  return [
    new Support(SupportType.PIN, TEST_SUPPORT_POSITIONS.LEFT_SUPPORT),
    new Support(SupportType.ROLLER, TEST_SUPPORT_POSITIONS.RIGHT_SUPPORT),
  ];
}

// Helper function to create basic load groups
export function createTestLoadGroups(): LoadGroup[] {
  const deadLoadGroup = new LoadGroup(
    "dead-load-1",
    "Dead Load",
    { start: 0, end: TEST_BEAM_DIMENSIONS.LENGTH },
    { [LoadType.DEAD]: TEST_LOADS.DEAD_LOAD },
    {},
    LoadApplicationType.DISTRIBUTED
  );

  const liveLoadGroup = new LoadGroup(
    "live-load-1", 
    "Live Load",
    { start: 0, end: TEST_BEAM_DIMENSIONS.LENGTH },
    { [LoadType.LIVE]: TEST_LOADS.LIVE_LOAD },
    {},
    LoadApplicationType.DISTRIBUTED
  );

  return [deadLoadGroup, liveLoadGroup];
}

// Helper function to create test beam data
export function createTestBeamData(materialType: 'sawn' | 'glulam' | 'steel'): BeamData {
  const baseBeamData: BeamData = {
    properties: {
      length: TEST_BEAM_DIMENSIONS.LENGTH,
      elasticModulus: 0, // Will be set based on material type
      momentOfInertia: 0, // Will be set based on material type
      area: 0, // Will be set based on material type
    },
    supports: createTestSupports(),
    loadGroups: createTestLoadGroups(),
    designMethod: 'ASD',
    selectedLoadCombos: ['1.4D', '1.2D+1.6L'],
  };

  switch (materialType) {
    case 'sawn':
      baseBeamData.properties.elasticModulus = TEST_WOOD_PROPERTIES.SAWN.E_PSI;
      baseBeamData.properties.momentOfInertia = TEST_MATERIAL_PROPERTIES.WOOD.SAWN_MOMENT_OF_INERTIA;
      baseBeamData.properties.area = TEST_MATERIAL_PROPERTIES.WOOD.SAWN_AREA;
      break;
    case 'glulam':
      baseBeamData.properties.elasticModulus = TEST_WOOD_PROPERTIES.GLULAM.E_PSI;
      baseBeamData.properties.momentOfInertia = TEST_MATERIAL_PROPERTIES.WOOD.GLULAM_MOMENT_OF_INERTIA;
      baseBeamData.properties.area = TEST_MATERIAL_PROPERTIES.WOOD.GLULAM_AREA;
      break;
    case 'steel':
      baseBeamData.properties.elasticModulus = TEST_MATERIAL_PROPERTIES.STEEL.ELASTIC_MODULUS;
      baseBeamData.properties.momentOfInertia = TEST_MATERIAL_PROPERTIES.STEEL.W12X26_IX;
      baseBeamData.properties.area = TEST_MATERIAL_PROPERTIES.STEEL.W12X26_AREA;
      break;
  }

  return baseBeamData;
}

// Expected analysis results structure
export const TEST_ANALYSIS_RESULTS = {
  MAX_BENDING_STRESS_RATIO: {
    ratio: EXPECTED_STRESS_RATIOS.SAFE_BENDING,
    value: 800, // psi
    position: TEST_BEAM_DIMENSIONS.LENGTH / 2,
    loadComboName: '1.2D+1.6L',
    actualStress: 800, // psi
    allowableStress: 1000, // psi
    spanNumber: 1,
  },
  MAX_SHEAR_STRESS_RATIO: {
    ratio: EXPECTED_STRESS_RATIOS.SAFE_SHEAR,
    value: 100, // psi
    position: 0,
    loadComboName: '1.2D+1.6L',
    actualStress: 100, // psi
    allowableStress: 167, // psi
    spanNumber: 1,
  },
  MAX_TOTAL_DEFLECTION_DOWNWARD: {
    value: 0.8, // inches
    position: TEST_BEAM_DIMENSIONS.LENGTH / 2,
    loadComboName: '1.0D+1.0L',
    ratio: 0.8, // (value/limit)
    limit: 1.0, // inches
  },
  MAX_TOTAL_DEFLECTION_UPWARD: {
    value: -0.1, // inches
    position: TEST_BEAM_DIMENSIONS.LENGTH / 4,
    loadComboName: '1.0D+1.0L',
    ratio: 0.1, // abs(value/limit)
    limit: 1.0, // inches
  },
} as const;

// Simple test to prevent Jest from complaining about no tests
describe('Beam Test Constants', () => {
  it('should export all required constants', () => {
    expect(TEST_BEAM_DIMENSIONS).toBeDefined();
    expect(TEST_WOOD_PROPERTIES).toBeDefined();
    expect(TEST_STEEL_PROPERTIES).toBeDefined();
    expect(TEST_ANALYSIS_NAMES).toBeDefined();
    expect(createTestBeamData).toBeDefined();
  });
}); 