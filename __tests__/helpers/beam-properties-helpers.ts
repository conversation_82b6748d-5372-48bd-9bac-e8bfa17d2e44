// Helper functions for creating beam properties state for testing

import { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import {
  TEST_BEAM_DIMENSIONS,
  TEST_WOOD_PROPERTIES,
  TEST_STEEL_PROPERTIES,
  TEST_ENVIRONMENTAL_CONDITIONS,
  TEST_DEFLECTION_LIMITS,
  TEST_MATERIAL_PROPERTIES,
} from "../constants/beam-test-constants";

// Helper function to create sawn lumber beam properties state
export function createSawnLumberBeamPropertiesState(): BeamPropertiesState {
  return {
    isWetService: false,
    isRepetitiveMember: false,
    isBraced: false,
    lu: "",
    selectedSpecies: TEST_WOOD_PROPERTIES.SAWN.SPECIES,
    selectedSpeciesCombination: TEST_WOOD_PROPERTIES.SAWN.SPECIES,
    selectedGrade: TEST_WOOD_PROPERTIES.SAWN.GRADE,
    selectedSizeClassification: TEST_WOOD_PROPERTIES.SAWN.SIZE_CLASSIFICATION,
    selectedNominalSize: TEST_WOOD_PROPERTIES.SAWN.NOMINAL_SIZE,
    selectedNdsVersion: TEST_WOOD_PROPERTIES.SAWN.NDS_VERSION,
    manualWidth: TEST_BEAM_DIMENSIONS.SAWN_WIDTH.toString(),
    manualDepth: TEST_BEAM_DIMENSIONS.SAWN_DEPTH.toString(),
    designValues: {
      Fb: TEST_WOOD_PROPERTIES.SAWN.FB_PSI,
      Ft: TEST_WOOD_PROPERTIES.SAWN.FT_PSI,
      Fv: TEST_WOOD_PROPERTIES.SAWN.FV_PSI,
      Fc_perp: TEST_WOOD_PROPERTIES.SAWN.FC_PERP_PSI,
      Fc: TEST_WOOD_PROPERTIES.SAWN.FC_PSI,
      E: TEST_WOOD_PROPERTIES.SAWN.E_PSI,
      Emin: TEST_WOOD_PROPERTIES.SAWN.EMIN_PSI,
      G: TEST_WOOD_PROPERTIES.SAWN.G_PSI,
      adjusted_Fb: TEST_WOOD_PROPERTIES.SAWN.FB_PSI,
      adjusted_Ft: TEST_WOOD_PROPERTIES.SAWN.FT_PSI,
      adjusted_Fv: TEST_WOOD_PROPERTIES.SAWN.FV_PSI,
      adjusted_Fc_perp: TEST_WOOD_PROPERTIES.SAWN.FC_PERP_PSI,
      adjusted_Fc: TEST_WOOD_PROPERTIES.SAWN.FC_PSI,
      adjusted_E: TEST_WOOD_PROPERTIES.SAWN.E_PSI,
      adjusted_Emin: TEST_WOOD_PROPERTIES.SAWN.EMIN_PSI,
      commercial_grade: TEST_WOOD_PROPERTIES.SAWN.GRADE,
      speciesCombination: TEST_WOOD_PROPERTIES.SAWN.SPECIES,
      grading_rules_agency: "WWPA",
      design_values_table: `NDS ${TEST_WOOD_PROPERTIES.SAWN.NDS_VERSION}`,
      location: "US",
      version: TEST_WOOD_PROPERTIES.SAWN.NDS_VERSION,
      minThickness: 0.75,
      maxThickness: 4.0,
      minWidth: 2.0,
      maxWidth: 12.0,
    },
    lumberProperties: {
      A: (TEST_BEAM_DIMENSIONS.SAWN_WIDTH * TEST_BEAM_DIMENSIONS.SAWN_DEPTH),
      modulus_of_elasticity_e_psi: TEST_WOOD_PROPERTIES.SAWN.E_PSI,
      nominal_width: TEST_BEAM_DIMENSIONS.SAWN_WIDTH,
      nominal_depth: TEST_BEAM_DIMENSIONS.SAWN_DEPTH,
    },
    flatUseFactor: 1.0,
    repetitiveMemberFactor: 1.0,
    wetServiceFactor: {
      Fb: { factor: 1.0 },
      Ft: { factor: 1.0 },
      Fv: { factor: 1.0 },
      Fc_perp: { factor: 1.0 },
      Fc: { factor: 1.0 },
      E: { factor: 1.0 },
      Emin: { factor: 1.0 },
    } as any,
    manual_E: TEST_WOOD_PROPERTIES.SAWN.E_PSI,
    manual_Fb_allow: TEST_WOOD_PROPERTIES.SAWN.FB_PSI,
    manual_Fv_allow: TEST_WOOD_PROPERTIES.SAWN.FV_PSI,
    manual_E_min: TEST_WOOD_PROPERTIES.SAWN.EMIN_PSI,
    manual_totalDeflectionLimit: TEST_DEFLECTION_LIMITS.L_240,
    manual_liveDeflectionLimit: TEST_DEFLECTION_LIMITS.L_360,
    manual_Area: (TEST_BEAM_DIMENSIONS.SAWN_WIDTH * TEST_BEAM_DIMENSIONS.SAWN_DEPTH),
    manual_Ixx: (TEST_BEAM_DIMENSIONS.SAWN_WIDTH * Math.pow(TEST_BEAM_DIMENSIONS.SAWN_DEPTH, 3)) / 12,
    manual_Iyy: (TEST_BEAM_DIMENSIONS.SAWN_DEPTH * Math.pow(TEST_BEAM_DIMENSIONS.SAWN_WIDTH, 3)) / 12,
    manual_Sxx: (TEST_BEAM_DIMENSIONS.SAWN_WIDTH * Math.pow(TEST_BEAM_DIMENSIONS.SAWN_DEPTH, 2)) / 6,
    manual_Syy: (TEST_BEAM_DIMENSIONS.SAWN_DEPTH * Math.pow(TEST_BEAM_DIMENSIONS.SAWN_WIDTH, 2)) / 6,
    beamStabilityFactorCL: null,
    manual_maxStressRatioLimit: 1.0,
    includeBeamWeight: false,
    moistureContent: TEST_ENVIRONMENTAL_CONDITIONS.DRY_MOISTURE_CONTENT,
    isIncised: false,
    incisingFactors: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    isTemperatureFactored: false,
    temperature: TEST_ENVIRONMENTAL_CONDITIONS.NORMAL_TEMPERATURE,
    temperatureFactors: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    selectedGluLamProperties: null,
    lumberType: "sawn",
    volumeFactorCv: null,
    manual_species: TEST_WOOD_PROPERTIES.SAWN.SPECIES,
    manual_grade: TEST_WOOD_PROPERTIES.SAWN.GRADE,
    manual_material_type: "Sawn Lumber",
    isManualMode: false,
    selectedMaterial: "wood",
    isSteelBraced: false,
    steelUnbracedLength: "",
  };
}

// Helper function to create glulam beam properties state
export function createGlulamBeamPropertiesState(): BeamPropertiesState {
  return {
    isWetService: false,
    isRepetitiveMember: false,
    isBraced: false,
    lu: "",
    selectedSpecies: TEST_WOOD_PROPERTIES.GLULAM.SPECIES,
    selectedSpeciesCombination: TEST_WOOD_PROPERTIES.GLULAM.SPECIES_GROUP,
    selectedGrade: TEST_WOOD_PROPERTIES.GLULAM.GRADE,
    selectedSizeClassification: "Glued Laminated Timber",
    selectedNominalSize: `${TEST_BEAM_DIMENSIONS.GLULAM_WIDTH}x${TEST_BEAM_DIMENSIONS.GLULAM_DEPTH}`,
    selectedNdsVersion: "2018",
    manualWidth: TEST_BEAM_DIMENSIONS.GLULAM_WIDTH.toString(),
    manualDepth: TEST_BEAM_DIMENSIONS.GLULAM_DEPTH.toString(),
    designValues: {
      Fb: TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI,
      Fb_pos: TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI,
      Fb_neg: TEST_WOOD_PROPERTIES.GLULAM.FB_NEG_PSI,
      Ft: TEST_WOOD_PROPERTIES.GLULAM.FT_PSI,
      Fv: TEST_WOOD_PROPERTIES.GLULAM.FV_PSI,
      Fc_perp: TEST_WOOD_PROPERTIES.GLULAM.FC_PERP_PSI,
      Fc: TEST_WOOD_PROPERTIES.GLULAM.FC_PSI,
      E: TEST_WOOD_PROPERTIES.GLULAM.E_PSI,
      Emin: TEST_WOOD_PROPERTIES.GLULAM.EMIN_PSI,
      G: TEST_WOOD_PROPERTIES.GLULAM.G_PSI,
      adjusted_Fb: TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI,
      adjusted_Ft: TEST_WOOD_PROPERTIES.GLULAM.FT_PSI,
      adjusted_Fv: TEST_WOOD_PROPERTIES.GLULAM.FV_PSI,
      adjusted_Fc_perp: TEST_WOOD_PROPERTIES.GLULAM.FC_PERP_PSI,
      adjusted_Fc: TEST_WOOD_PROPERTIES.GLULAM.FC_PSI,
      adjusted_E: TEST_WOOD_PROPERTIES.GLULAM.E_PSI,
      adjusted_Emin: TEST_WOOD_PROPERTIES.GLULAM.EMIN_PSI,
      commercial_grade: TEST_WOOD_PROPERTIES.GLULAM.COMBINATION_SYMBOL,
      speciesCombination: TEST_WOOD_PROPERTIES.GLULAM.SPECIES_GROUP,
      grading_rules_agency: "APA",
      design_values_table: "NDS 2018 Table 5A",
      location: "US",
      version: "2018",
      minThickness: 1.5,
      maxThickness: 10.75,
      minWidth: 2.5,
      maxWidth: 12.0,
    },
    lumberProperties: null,
    flatUseFactor: 1.0,
    repetitiveMemberFactor: 1.0,
    wetServiceFactor: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    manual_E: TEST_WOOD_PROPERTIES.GLULAM.E_PSI,
    manual_Fb_allow: TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI,
    manual_Fv_allow: TEST_WOOD_PROPERTIES.GLULAM.FV_PSI,
    manual_E_min: TEST_WOOD_PROPERTIES.GLULAM.EMIN_PSI,
    manual_totalDeflectionLimit: TEST_DEFLECTION_LIMITS.L_240,
    manual_liveDeflectionLimit: TEST_DEFLECTION_LIMITS.L_360,
    manual_Area: (TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * TEST_BEAM_DIMENSIONS.GLULAM_DEPTH),
    manual_Ixx: (TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_DEPTH, 3)) / 12,
    manual_Iyy: (TEST_BEAM_DIMENSIONS.GLULAM_DEPTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH, 3)) / 12,
    manual_Sxx: (TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_DEPTH, 2)) / 6,
    manual_Syy: (TEST_BEAM_DIMENSIONS.GLULAM_DEPTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH, 2)) / 6,
    beamStabilityFactorCL: null,
    manual_maxStressRatioLimit: 1.0,
    includeBeamWeight: false,
    moistureContent: TEST_ENVIRONMENTAL_CONDITIONS.DRY_MOISTURE_CONTENT,
    isIncised: false,
    incisingFactors: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    isTemperatureFactored: false,
    temperature: TEST_ENVIRONMENTAL_CONDITIONS.NORMAL_TEMPERATURE,
    temperatureFactors: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    selectedGluLamProperties: {
      speciesGroup: TEST_WOOD_PROPERTIES.GLULAM.SPECIES_GROUP,
      species: TEST_WOOD_PROPERTIES.GLULAM.SPECIES,
      width: TEST_BEAM_DIMENSIONS.GLULAM_WIDTH,
      depth: TEST_BEAM_DIMENSIONS.GLULAM_DEPTH,
      selectedCombinationSymbolKey: TEST_WOOD_PROPERTIES.GLULAM.COMBINATION_SYMBOL,
      grade: TEST_WOOD_PROPERTIES.GLULAM.GRADE,
      selectedTable5ADetail: {
        stress_class: TEST_WOOD_PROPERTIES.GLULAM.COMBINATION_SYMBOL,
        species_group: TEST_WOOD_PROPERTIES.GLULAM.SPECIES_GROUP,
        E_xapp_ksi: TEST_WOOD_PROPERTIES.GLULAM.E_PSI / 1000,
        E_yapp_ksi: (TEST_WOOD_PROPERTIES.GLULAM.E_PSI * 0.85) / 1000,
        Emin_ksi: TEST_WOOD_PROPERTIES.GLULAM.EMIN_PSI / 1000,
        Fbx_pos_psi: TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI,
        Fbx_neg_psi: TEST_WOOD_PROPERTIES.GLULAM.FB_NEG_PSI,
        Fby_psi: TEST_WOOD_PROPERTIES.GLULAM.FB_POS_PSI * 0.75,
        Ft_psi: TEST_WOOD_PROPERTIES.GLULAM.FT_PSI,
        Fv_psi: TEST_WOOD_PROPERTIES.GLULAM.FV_PSI,
        Fc_perp_psi: TEST_WOOD_PROPERTIES.GLULAM.FC_PERP_PSI,
        Fc_psi: TEST_WOOD_PROPERTIES.GLULAM.FC_PSI,
      } as any,
      sectionProperties: {
        area: (TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * TEST_BEAM_DIMENSIONS.GLULAM_DEPTH),
        Ix: (TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_DEPTH, 3)) / 12,
        Iy: (TEST_BEAM_DIMENSIONS.GLULAM_DEPTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH, 3)) / 12,
        Sx: (TEST_BEAM_DIMENSIONS.GLULAM_WIDTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_DEPTH, 2)) / 6,
        Sy: (TEST_BEAM_DIMENSIONS.GLULAM_DEPTH * Math.pow(TEST_BEAM_DIMENSIONS.GLULAM_WIDTH, 2)) / 6,
      } as any,
    },
    lumberType: "glulam",
    volumeFactorCv: 1.0,
    manual_species: TEST_WOOD_PROPERTIES.GLULAM.SPECIES,
    manual_grade: TEST_WOOD_PROPERTIES.GLULAM.GRADE,
    manual_material_type: "Glulam",
    isManualMode: false,
    selectedMaterial: "wood",
    isSteelBraced: false,
    steelUnbracedLength: "",
  };
}

// Helper function to create steel beam properties state
export function createSteelBeamPropertiesState(): BeamPropertiesState {
  return {
    isWetService: false,
    isRepetitiveMember: false,
    isBraced: false,
    lu: "",
    selectedSpecies: "",
    selectedSpeciesCombination: "",
    selectedGrade: "",
    selectedSizeClassification: "",
    selectedNominalSize: "",
    selectedNdsVersion: "2018",
    manualWidth: "",
    manualDepth: "",
    designValues: null,
    lumberProperties: null,
    flatUseFactor: 1.0,
    repetitiveMemberFactor: 1.0,
    wetServiceFactor: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    manual_E: 29000000, // Steel elastic modulus
    manual_Fb_allow: null,
    manual_Fv_allow: null,
    manual_E_min: null,
    manual_totalDeflectionLimit: TEST_DEFLECTION_LIMITS.L_240,
    manual_liveDeflectionLimit: TEST_DEFLECTION_LIMITS.L_360,
    manual_Area: null,
    manual_Ixx: null,
    manual_Iyy: null,
    manual_Sxx: null,
    manual_Syy: null,
    beamStabilityFactorCL: null,
    manual_maxStressRatioLimit: 1.0,
    includeBeamWeight: false,
    moistureContent: null,
    isIncised: false,
    incisingFactors: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    isTemperatureFactored: false,
    temperature: TEST_ENVIRONMENTAL_CONDITIONS.NORMAL_TEMPERATURE,
    temperatureFactors: {
      Fb: 1.0,
      Ft: 1.0,
      Fv: 1.0,
      Fc_perp: 1.0,
      Fc: 1.0,
      E: 1.0,
      Emin: 1.0,
    } as any,
    selectedGluLamProperties: null,
    lumberType: "sawn",
    volumeFactorCv: null,
    manual_species: null,
    manual_grade: null,
    manual_material_type: "Steel",
    isManualMode: false,
    selectedMaterial: "steel",
    isSteelBraced: false,
    steelUnbracedLength: TEST_STEEL_PROPERTIES.UNBRACED_LENGTH,
    // Steel-specific properties (as any to avoid TypeScript issues)
    selectedSteelGrade: TEST_STEEL_PROPERTIES.GRADE_KEY,
    selectedSteelShape: TEST_STEEL_PROPERTIES.SHAPE_TYPE,
    selectedSteelShapeSize: TEST_STEEL_PROPERTIES.SHAPE_SIZE,
    steelSectionProperties: {
      EDI_Std_Nomenclature: TEST_STEEL_PROPERTIES.SHAPE_SIZE,
      A: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_AREA.toString(),
      d: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_DEPTH.toString(),
      bf: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_FLANGE_WIDTH.toString(),
      tw: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_WEB_THICKNESS.toString(),
      tf: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_FLANGE_THICKNESS.toString(),
      Ix: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_IX.toString(),
      Iy: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_IY.toString(),
      Sx: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_SX.toString(),
      Sy: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_SY.toString(),
      rx: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_RX.toString(),
      ry: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_RY.toString(),
      J: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_J.toString(),
      Cw: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_CW.toString(),
      W: TEST_MATERIAL_PROPERTIES.STEEL.W12X26_WEIGHT.toString(),
    },
    steelDesignValues: {
      Fy: TEST_STEEL_PROPERTIES.FY_PSI,
      Fu: TEST_STEEL_PROPERTIES.FU_PSI,
      grade: TEST_STEEL_PROPERTIES.GRADE,
      astm_designation: TEST_STEEL_PROPERTIES.ASTM_DESIGNATION,
    },
  } as any; // Using 'as any' to avoid TypeScript issues with steel-specific properties
}

// Simple test to prevent Jest from complaining about no tests
describe('Beam Properties Helpers', () => {
  it('should export all helper functions', () => {
    expect(createSawnLumberBeamPropertiesState).toBeDefined();
    expect(createGlulamBeamPropertiesState).toBeDefined();
    expect(createSteelBeamPropertiesState).toBeDefined();
  });
}); 