"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { AnalysisType, chartColors } from "@/lib/types/analysis/analysis-results";
import { 
  formatValue, 
  calculateDomain, 
  formatTick 
} from "@/lib/utils/analysis-utils";

interface ResultChartProps { 
  type: AnalysisType;
  data: { x: number; value: number }[];
  units: ReturnType<typeof getUnitsBySystem>;
}

export function ResultChart({ type, data, units }: ResultChartProps) {
  const domain = calculateDomain(data);

  // Calculate desired tick positions for X-axis
  const xValues = data?.map(d => d.x) ?? [0];
  const xMin = Math.min(...xValues, 0);
  const xMax = Math.max(...xValues, 0);
  const desiredTicks: number[] = [];
  // Add ticks at whole number intervals within the range
  for (let i = Math.ceil(xMin); i <= Math.floor(xMax); i++) {
    desiredTicks.push(i);
  }
  // Ensure start and end points are included if not already whole numbers
  if (desiredTicks.indexOf(xMin) === -1) desiredTicks.unshift(xMin);
  if (desiredTicks.indexOf(xMax) === -1) desiredTicks.push(xMax);
  // Sort ticks to be safe
  desiredTicks.sort((a, b) => a - b);

  return (
    <div className="h-[180px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 2, right: 5, left: 5, bottom: 15 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="x"
            label={{ 
              value: `Position (${units.length})`, 
              position: "bottom",
              offset: -10,
              style: { fontSize: 11, fill: 'white' }
            }}
            tick={{ fontSize: 10, fill: 'white' }}
            tickFormatter={(value: number) => value.toFixed(0)}
            ticks={desiredTicks}
            type="number"
            domain={[xMin, xMax]}
            axisLine={{ stroke: 'white' }}
            tickLine={{ stroke: 'white' }}
          />
          <YAxis
            domain={domain}
            tickFormatter={formatTick}
            tick={{ fontSize: 10, fill: 'white' }}
            axisLine={{ stroke: 'white' }}
            tickLine={{ stroke: 'white' }}
          />
          <Tooltip 
            formatter={(value: number) => [
              formatValue(value, type),
              type === "shear"
                ? "Shear Force"
                : type === "moment"
                ? "Bending Moment"
                : "Deflection"
            ]}
          />
          <Line
            type="monotone"
            dataKey="value"
            stroke={chartColors[type]}
            dot={false}
            strokeWidth={3}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
} 