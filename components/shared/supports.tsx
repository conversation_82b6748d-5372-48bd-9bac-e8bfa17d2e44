"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { Support } from "@/lib/types/support/support";
import { SupportType } from "@/lib/types/support/support-type";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";

interface SupportsProps {
  supports: Support[];
  beamLength: number;
  unitSystem: UnitSystem;
  onChange: (supports: Support[]) => void;
}

interface ValidationError {
  index: number;
  message: string;
}

export function Supports({ supports, beamLength, unitSystem, onChange }: SupportsProps) {
  const [validationErrors, setValidationErrors] = useState<Record<number, ValidationError>>({});
  const units = getUnitsBySystem(unitSystem);

  const validateSupports = (newSupports: Support[], currentIndex: number): ValidationError | null => {
    const position = newSupports[currentIndex].position;
    
    // Check for overlapping supports
    const overlappingIndex = newSupports.findIndex(
      (support, index) => index !== currentIndex && Math.abs(support.position - position) < 0.001
    );
    
    if (overlappingIndex !== -1) {
      return {
        index: currentIndex,
        message: "Support overlaps with another support. Please adjust the position."
      };
    }

    // Check if position is within beam length
    if (position > beamLength) {
      return {
        index: currentIndex,
        message: `Support position cannot exceed beam length (${beamLength} ${units.length})`
      };
    }

    return null;
  };

  const findAvailablePosition = (existingSupports: Support[]): number => {
    // Sort supports by position
    const sortedSupports = [...existingSupports].sort((a, b) => a.position - b.position);
    
    // Find the largest gap between supports
    let maxGap = 0;
    let gapPosition = beamLength / 2;
    
    for (let i = 0; i < sortedSupports.length - 1; i++) {
      const gap = sortedSupports[i + 1].position - sortedSupports[i].position;
      if (gap > maxGap) {
        maxGap = gap;
        gapPosition = sortedSupports[i].position + gap / 2;
      }
    }
    
    // Also check gap between last support and beam end
    if (sortedSupports.length > 0) {
      const lastGap = beamLength - sortedSupports[sortedSupports.length - 1].position;
      if (lastGap > maxGap) {
        gapPosition = sortedSupports[sortedSupports.length - 1].position + lastGap / 2;
      }
    }
    
    // If all positions are too close together, try to find a position slightly offset from existing ones
    if (maxGap < 0.1) {
      for (let pos = 0; pos <= beamLength; pos += beamLength / 10) {
        const isTaken = sortedSupports.some(support => Math.abs(support.position - pos) < 0.1);
        if (!isTaken) {
          return pos;
        }
      }
    }
    
    return gapPosition;
  };

  const addSupport = () => {
    const newPosition = findAvailablePosition(supports);
    const newSupport = new Support(SupportType.PIN, newPosition);
    const newSupports = [...supports, newSupport];
    
    // Validate new support
    const error = validateSupports(newSupports, newSupports.length - 1);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [newSupports.length - 1]: error }));
    } else {
      setValidationErrors({});
    }
    
    onChange(newSupports);
  };

  const updateSupport = (index: number, updates: Partial<Support>) => {
    const newSupports = [...supports];
    const currentSupport = newSupports[index];
    
    // Create new support with existing position if not being updated
    const updatedSupport = new Support(
      updates.type ? updates.type : currentSupport.type,
      updates.position !== undefined ? updates.position : currentSupport.position
    );
    
    newSupports[index] = updatedSupport;
    
    // Validate before updating
    const error = validateSupports(newSupports, index);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [index]: error }));
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[index];
        return newErrors;
      });
    }
    
    onChange(newSupports);
  };

  const removeSupport = (index: number) => {
    // Don't allow removing if only two supports remain
    if (supports.length <= 2) {
      setValidationErrors(prev => ({
        ...prev,
        [index]: {
          index,
          message: "Cannot remove support. Minimum of two supports required."
        }
      }));
      return;
    }

    onChange(supports.filter((_, i) => i !== index));
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[index];
      return newErrors;
    });
  };

  return (
    <div className="space-y-4">
      {supports.map((support, index) => (
        <div key={index} className="space-y-4 p-4 border rounded-lg">
          <div className="flex items-center justify-between">
            <div className="grid grid-cols-2 gap-4 flex-1 mr-4">
              <div>
                <Label>Support Type</Label>
                <Select
                  value={support.type}
                  onValueChange={(value: SupportType) =>
                    updateSupport(index, { type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={SupportType.PIN}>Pin Support</SelectItem>
                    <SelectItem value={SupportType.ROLLER}>Roller Support</SelectItem>
                    <SelectItem value={SupportType.FIXED}>Fixed Support</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Position ({units.length})</Label>
                <Input
                  type="number"
                  value={support.position}
                  onChange={(e) => {
                    updateSupport(index, { position: parseFloat(e.target.value) });
                  }}
                  min={0}
                  max={beamLength}
                  step={0.1}
                  className={validationErrors[index] ? "border-red-500 focus:ring-red-500" : ""}
                />
                {validationErrors[index] && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{validationErrors[index].message}</AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => removeSupport(index)}
              className="h-8 w-8 self-end"
              disabled={supports.length <= 2}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
      <Button onClick={addSupport} className="w-full">
        Add Support
      </Button>
    </div>
  );
}