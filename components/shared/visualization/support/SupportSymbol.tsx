import React from 'react';
import { Support } from "@/lib/types/support/support";
import { SupportType } from "@/lib/types/support/support-type";
import { SVG_CONSTANTS } from "../types";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { UnitSystem } from "@/lib/types/units/unit-system";

interface SupportSymbolProps {
  support: Support;
  x: number;
  y: number;
  unitSystem: UnitSystem;
  keyPrefix: string;
}

const drawPinSupportSVG = (x: number, y: number, key: string): JSX.Element => (
  <g key={`${key}-pin`}>
    <polygon 
      points={`${x},${y + SVG_CONSTANTS.SUPPORT_SIZE} ${x - SVG_CONSTANTS.SUPPORT_SIZE},${y + SVG_CONSTANTS.SUPPORT_SIZE * 2.5} ${x + SVG_CONSTANTS.SUPPORT_SIZE},${y + SVG_CONSTANTS.SUPPORT_SIZE * 2.5}`}
      fill={SVG_CONSTANTS.SUPPORT_COLOR}
    />
    <circle 
      cx={x} 
      cy={y + SVG_CONSTANTS.SUPPORT_SIZE * 0.8} 
      r={SVG_CONSTANTS.SUPPORT_SIZE / 2.5} 
      fill="white" 
      stroke={SVG_CONSTANTS.SUPPORT_COLOR} 
      strokeWidth={SVG_CONSTANTS.SUPPORT_STROKE_WIDTH / 2} 
    />
  </g>
);

const drawRollerSupportSVG = (x: number, y: number, key: string): JSX.Element => (
  <g key={`${key}-roller`}>
    <polygon 
      points={`${x},${y + SVG_CONSTANTS.SUPPORT_SIZE} ${x - SVG_CONSTANTS.SUPPORT_SIZE},${y + SVG_CONSTANTS.SUPPORT_SIZE * 2.5} ${x + SVG_CONSTANTS.SUPPORT_SIZE},${y + SVG_CONSTANTS.SUPPORT_SIZE * 2.5}`}
      fill="none"
      stroke={SVG_CONSTANTS.SUPPORT_COLOR}
      strokeWidth={SVG_CONSTANTS.SUPPORT_STROKE_WIDTH}
    />
    <circle 
      cx={x} 
      cy={y + SVG_CONSTANTS.SUPPORT_SIZE * 2.5 + SVG_CONSTANTS.SUPPORT_SIZE / 2} 
      r={SVG_CONSTANTS.SUPPORT_SIZE / 2} 
      fill={SVG_CONSTANTS.SUPPORT_COLOR} 
    />
    <line 
      x1={x - SVG_CONSTANTS.SUPPORT_SIZE * 1.5} 
      y1={y + SVG_CONSTANTS.SUPPORT_SIZE * 3.5} 
      x2={x + SVG_CONSTANTS.SUPPORT_SIZE * 1.5} 
      y2={y + SVG_CONSTANTS.SUPPORT_SIZE * 3.5} 
      stroke={SVG_CONSTANTS.SUPPORT_COLOR} 
      strokeWidth={SVG_CONSTANTS.SUPPORT_STROKE_WIDTH}
    />
    <circle 
      cx={x} 
      cy={y + SVG_CONSTANTS.SUPPORT_SIZE * 0.8} 
      r={SVG_CONSTANTS.SUPPORT_SIZE / 2.5} 
      fill="white" 
      stroke={SVG_CONSTANTS.SUPPORT_COLOR} 
      strokeWidth={SVG_CONSTANTS.SUPPORT_STROKE_WIDTH / 2} 
    />
  </g>
);

const drawFixedSupportSVG = (x: number, y: number, key: string): JSX.Element => (
  <g key={`${key}-fixed`}>
    <rect 
      x={x - SVG_CONSTANTS.SUPPORT_SIZE / 2} 
      y={y - SVG_CONSTANTS.SUPPORT_SIZE * 1.5} 
      width={SVG_CONSTANTS.SUPPORT_SIZE} 
      height={SVG_CONSTANTS.SUPPORT_SIZE * 3} 
      fill={SVG_CONSTANTS.SUPPORT_COLOR} 
    />
    {/* Hatching lines */}
    {[...Array(5)].map((_, i) => (
      <line 
        key={`fixed-hatch-${i}`}
        x1={x - SVG_CONSTANTS.SUPPORT_SIZE / 2 - 5} 
        y1={y - SVG_CONSTANTS.SUPPORT_SIZE * 1.5 + (i * SVG_CONSTANTS.SUPPORT_SIZE * 3 / 4) - 2}
        x2={x - SVG_CONSTANTS.SUPPORT_SIZE / 2 + 5} 
        y2={y - SVG_CONSTANTS.SUPPORT_SIZE * 1.5 + (i * SVG_CONSTANTS.SUPPORT_SIZE * 3 / 4) + 2}
        stroke={SVG_CONSTANTS.SUPPORT_COLOR} 
        strokeWidth={1}
      />
    ))}
  </g>
);

const drawSupportLabelSVG = (x: number, y: number, position: number, unit: string, key: string): JSX.Element => (
  <text
    key={`${key}-label`}
    x={x}
    y={y + SVG_CONSTANTS.SUPPORT_SIZE * 4 + SVG_CONSTANTS.LABEL_FONT_SIZE}
    fontSize={SVG_CONSTANTS.LABEL_FONT_SIZE}
    textAnchor="middle"
    fill={SVG_CONSTANTS.TEXT_COLOR}
  >
    {`${position.toFixed(2)} ${unit}`}
  </text>
);

export function SupportSymbol({ support, x, y, unitSystem, keyPrefix }: SupportSymbolProps) {
  const units = getUnitsBySystem(unitSystem);
  let supportSymbol: JSX.Element | null = null;

  switch (support.type) {
    case SupportType.PIN:
      supportSymbol = drawPinSupportSVG(x, y, keyPrefix);
      break;
    case SupportType.ROLLER:
      supportSymbol = drawRollerSupportSVG(x, y, keyPrefix);
      break;
    case SupportType.FIXED:
      supportSymbol = drawFixedSupportSVG(x, y, keyPrefix);
      break;
  }

  const label = drawSupportLabelSVG(x, y, support.position, units.length, keyPrefix);

  return (
    <g key={keyPrefix}>
      {supportSymbol}
      {label}
    </g>
  );
}

export function SupportCollection({ supports, scale, baseY, unitSystem }: {
  supports: Support[];
  scale: number;
  baseY: number;
  unitSystem: UnitSystem;
}) {
  return (
    <>
      {supports.map((support, index) => (
        <SupportSymbol
          key={`support-${index}`}
          support={support}
          x={support.position * scale}
          y={baseY}
          unitSystem={unitSystem}
          keyPrefix={`support-${index}`}
        />
      ))}
    </>
  );
} 