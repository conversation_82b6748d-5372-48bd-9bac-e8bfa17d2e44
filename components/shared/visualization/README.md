# Shared Visualization Module

This module provides reusable visualization components for structural analysis that can be shared across different types of analysis (beam, column, etc.).

## Structure

```
components/shared/visualization/
├── types.ts                 # Common types and constants
├── index.ts                 # Main exports
├── base/
│   └── VisualizationBase.tsx # Base component for all visualizations
├── support/
│   └── SupportSymbol.tsx     # Support symbols (pin, roller, fixed)
├── load/
│   └── LoadSymbol.tsx        # Load symbols (point, distributed)
├── ruler/
│   └── Ruler.tsx            # Measurement rulers and span rulers
├── beam/
│   └── BeamLine.tsx         # Beam line and bracing components
└── README.md                # This file
```

## Key Components

### VisualizationBase
Base component that provides common SVG structure and styling.

### SupportSymbol & SupportCollection
Renders different types of supports (pin, roller, fixed) with position labels.

### LoadSymbol (PointLoad, DistributedLoad) & LoadCollection
Renders point loads and distributed loads with proper scaling and descriptions.

### Ruler & SpanRuler
Provides measurement rulers with major/minor ticks and span measurements.

### BeamLine & BraceCollection
Renders beam elements with optional bracing visualization.

## Usage Example

```tsx
import {
  VisualizationBase,
  STANDARD_BEAM_DIMENSIONS,
  SupportCollection,
  LoadCollection,
  Ruler,
  BeamLine,
  // ... other components
} from "@/components/shared/visualization";

function MyStructuralVisualization() {
  return (
    <VisualizationBase dimensions={STANDARD_BEAM_DIMENSIONS}>
      <BeamLine drawingWidth={800} baseY={150} />
      <SupportCollection supports={supports} scale={scale} baseY={150} unitSystem={unitSystem} />
      <LoadCollection loadGroups={loadGroups} scale={scale} baseY={150} maxTotalMagnitude={1000} />
      <Ruler drawingWidth={800} beamLength={20} baseY={150} unitSystem={unitSystem} />
    </VisualizationBase>
  );
}
```

## Reusability

These components are designed to be reused across different types of structural analysis:
- **Beam Analysis** (current implementation)
- **Column Analysis** (future)
- **Truss Analysis** (future)
- **Frame Analysis** (future)

The modular design allows for easy extension and customization for different analysis types while maintaining consistent visual styling and behavior. 