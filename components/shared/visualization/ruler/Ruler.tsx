import React from 'react';
import { Support } from "@/lib/types/support/support";
import { SVG_CONSTANTS } from "../types";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { UnitSystem } from "@/lib/types/units/unit-system";

interface RulerProps {
  drawingWidth: number;
  beamLength: number;
  baseY: number;
  unitSystem: UnitSystem;
  numMajorTicks?: number;
}

interface SpanRulerProps {
  supports: Support[];
  scale: number;
  baseY: number;
}

const drawMainRulerSVG = (drawingWidth: number, y: number, key: string): JSX.Element => (
  <line
    key={`${key}-main-ruler-line`}
    x1={0}
    y1={y}
    x2={drawingWidth}
    y2={y}
    stroke={SVG_CONSTANTS.RULER_COLOR}
    strokeWidth={1}
  />
);

const drawRulerTickSVG = (x: number, y: number, height: number, key: string): JSX.Element => (
  <line
    key={key}
    x1={x}
    y1={y - height / 2}
    x2={x}
    y2={y + height / 2}
    stroke={SVG_CONSTANTS.RULER_COLOR}
    strokeWidth={1}
  />
);

const drawRulerLabelSVG = (x: number, y: number, label: string, key: string): JSX.Element => (
  <text
    key={key}
    x={x}
    y={y + SVG_CONSTANTS.MAJOR_TICK_HEIGHT / 2 + SVG_CONSTANTS.LABEL_FONT_SIZE + 2}
    fontSize={SVG_CONSTANTS.LABEL_FONT_SIZE}
    textAnchor="middle"
    fill={SVG_CONSTANTS.RULER_COLOR}
  >
    {label}
  </text>
);

const drawSpanRulerLineSVG = (x1: number, x2: number, y: number, key: string): JSX.Element => (
  <g key={`${key}-span-line`}>
    {/* Horizontal line */}
    <line x1={x1} y1={y} x2={x2} y2={y} stroke={SVG_CONSTANTS.RULER_COLOR} strokeWidth={1} />
    {/* Vertical ticks */}
    <line x1={x1} y1={y - 3} x2={x1} y2={y + 3} stroke={SVG_CONSTANTS.RULER_COLOR} strokeWidth={1} />
    <line x1={x2} y1={y - 3} x2={x2} y2={y + 3} stroke={SVG_CONSTANTS.RULER_COLOR} strokeWidth={1} />
  </g>
);

const drawSpanLabelSVG = (x: number, y: number, label: string, key: string): JSX.Element => (
  <text
    key={`${key}-span-label`}
    x={x}
    y={y - 5}
    fontSize={SVG_CONSTANTS.LABEL_FONT_SIZE}
    textAnchor="middle"
    fill={SVG_CONSTANTS.RULER_COLOR}
  >
    {label}
  </text>
);

export function Ruler({ 
  drawingWidth, 
  beamLength, 
  baseY, 
  unitSystem, 
  numMajorTicks = 5 
}: RulerProps) {
  const units = getUnitsBySystem(unitSystem);
  const rulerElements: JSX.Element[] = [];
  
  const mainRulerY = baseY + SVG_CONSTANTS.RULER_Y_OFFSET;
  rulerElements.push(drawMainRulerSVG(drawingWidth, mainRulerY, 'main-ruler'));

  const majorTickSpacing = beamLength / numMajorTicks;
  const majorTickPixelSpacing = drawingWidth / numMajorTicks;

  for (let i = 0; i <= numMajorTicks; i++) {
    const x = i * majorTickPixelSpacing;
    const value = i * majorTickSpacing;
    const majorTickKey = `major-tick-${i}`;
    rulerElements.push(drawRulerTickSVG(x, mainRulerY, SVG_CONSTANTS.MAJOR_TICK_HEIGHT, majorTickKey));
    rulerElements.push(drawRulerLabelSVG(x, mainRulerY, `${value.toFixed(1)} ${units.length}`, `${majorTickKey}-label`));

    // Add minor ticks
    if (i < numMajorTicks) {
      const numMinorTicks = 4;
      for (let j = 1; j <= numMinorTicks; j++) {
        const minorX = x + (j * majorTickPixelSpacing) / (numMinorTicks + 1);
        rulerElements.push(drawRulerTickSVG(minorX, mainRulerY, SVG_CONSTANTS.MINOR_TICK_HEIGHT, `minor-tick-${i}-${j}`));
      }
    }
  }

  return <>{rulerElements}</>;
}

export function SpanRuler({ supports, scale, baseY }: SpanRulerProps) {
  const spanRulerElements: JSX.Element[] = [];
  const spanRulerY = baseY + SVG_CONSTANTS.SPAN_RULER_Y_OFFSET;
  const sortedSupports = [...supports].sort((a, b) => a.position - b.position);
  
  for (let i = 0; i < sortedSupports.length - 1; i++) {
    const start = sortedSupports[i].position;
    const end = sortedSupports[i + 1].position;
    const spanLength = end - start;
    const startX = start * scale;
    const endX = end * scale;
    const midX = (startX + endX) / 2;
    const spanKey = `span-${i}`;

    spanRulerElements.push(drawSpanRulerLineSVG(startX, endX, spanRulerY, spanKey));
    spanRulerElements.push(drawSpanLabelSVG(midX, spanRulerY, spanLength.toFixed(2), spanKey));
  }

  return <>{spanRulerElements}</>;
} 