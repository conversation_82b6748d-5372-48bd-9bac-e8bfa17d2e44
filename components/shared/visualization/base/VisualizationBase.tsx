import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { VisualizationDimensions } from "../types";

interface VisualizationBaseProps {
  dimensions: VisualizationDimensions;
  backgroundColor?: string;
  className?: string;
  children: React.ReactNode;
}

export function VisualizationBase({
  dimensions,
  backgroundColor = '#1e183a',
  className = "border-none shadow-none",
  children
}: VisualizationBaseProps) {
  const { width, height, margin } = dimensions;

  return (
    <Card className={className} style={{ backgroundColor }}>
      <CardContent className="p-0.5">
        <svg 
          width="100%" 
          height={height} 
          viewBox={`0 0 ${width} ${height}`} 
          preserveAspectRatio="xMidYMid meet" 
          className="w-full"
        >
          <g transform={`translate(${margin.left}, 0)`}>
            {children}
          </g>
        </svg>
      </CardContent>
    </Card>
  );
}

// Helper function to calculate common beam positioning
export function calculateBeamPositioning(
  svgDimensions: VisualizationDimensions,
  beamLength: number
) {
  const { width, height, margin } = svgDimensions;
  const drawingWidth = width - margin.left - margin.right;
  const scale = drawingWidth / beamLength;
  
  return {
    drawingWidth,
    scale,
    drawingHeight: height - margin.top - margin.bottom
  };
}

// Standard dimensions for beam analysis
export const STANDARD_BEAM_DIMENSIONS: VisualizationDimensions = {
  width: 900,
  height: 300,
  margin: { top: 30, right: 15, bottom: 55, left: 40 }
}; 