import { LoadType } from "@/lib/types/load/load-type";
import { UnitSystem } from "@/lib/types/units/unit-system";

// Common visualization constants
export const SVG_CONSTANTS = {
  SUPPORT_SIZE: 15,
  SUPPORT_COLOR: "#03ada2",
  SUPPORT_STROKE_WIDTH: 2,
  LABEL_FONT_SIZE: 16,
  RULER_Y_OFFSET: 90,
  SPAN_RULER_Y_OFFSET: 35,
  MAJOR_TICK_HEIGHT: 15,
  MINOR_TICK_HEIGHT: 4,
  RULER_COLOR: "#FFFFFF",
  LOAD_ARROW_SIZE: 12,
  LEVEL_PADDING: 4,
  MIN_LOAD_HEIGHT: 25,
  MAX_LOAD_HEIGHT: 60,
  BEAM_COLOR: "#FFFFFF",
  BEAM_STROKE_WIDTH: 10,
  BRACE_COLOR: "#FFFFFF",
  BRACE_STROKE_WIDTH: 3,
  TEXT_COLOR: "#FFFFFF",
} as const;

// Load type mappings
export const loadTypeSymbols: Record<LoadType, string> = {
  [LoadType.DEAD]: "D",
  [LoadType.LIVE]: "L",
  [LoadType.SNOW]: "S",
  [LoadType.WIND]: "W",
  [LoadType.EARTHQUAKE]: "E",
  [LoadType.SOIL]: "H",
  [LoadType.FLOOD]: "F",
  [LoadType.TEMPERATURE]: "T",
  [LoadType.ROOF_LIVE]: "Lr",
  [LoadType.RAIN]: "R",
};

export const loadTypeColors: Record<LoadType, string> = {
  [LoadType.DEAD]: "#faa82d",
  [LoadType.LIVE]: "#faa82d",
  [LoadType.SNOW]: "#faa82d",
  [LoadType.WIND]: "#faa82d",
  [LoadType.EARTHQUAKE]: "#faa82d",
  [LoadType.SOIL]: "#faa82d",
  [LoadType.FLOOD]: "#faa82d",
  [LoadType.TEMPERATURE]: "#faa82d",
  [LoadType.ROOF_LIVE]: "#faa82d",
  [LoadType.RAIN]: "#faa82d",
};

// Base interfaces
export interface VisualizationDimensions {
  width: number;
  height: number;
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface VisualizationPosition {
  x: number;
  y: number;
}

export interface BaseVisualizationProps {
  scale: number;
  baseY: number;
  unitSystem: UnitSystem;
} 