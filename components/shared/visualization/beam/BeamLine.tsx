import React from 'react';
import { SVG_CONSTANTS } from "../types";
import { BeamPropertiesState } from "@/lib/types/beam/beam-data";

interface BeamLineProps {
  drawingWidth: number;
  baseY: number;
  isFullyBraced?: boolean;
}

interface BraceProps {
  scale: number;
  baseY: number;
  beamLength: number;
  unbracedLength: number;
  keyPrefix: string;
}

interface BraceCollectionProps {
  scale: number;
  baseY: number;
  beamLength: number;
  beamPropertiesState?: BeamPropertiesState;
}

const drawBraceSVG = (
  x: number,
  y: number,
  keyPrefix: string
): JSX.Element => {
  const braceHeight = 40;
  const braceWidth = 20;

  return (
    <g key={keyPrefix} stroke={SVG_CONSTANTS.BRACE_COLOR} strokeWidth={SVG_CONSTANTS.BRACE_STROKE_WIDTH} strokeDasharray="4,4">
      {/* Vertical line */}
      <line x1={x} y1={y - braceHeight / 2} x2={x} y2={y + braceHeight / 2} />
      {/* Diagonal lines */}
      <line x1={x - braceWidth / 2} y1={y - braceHeight / 2} x2={x + braceWidth / 2} y2={y + braceHeight / 2} />
      <line x1={x - braceWidth / 2} y1={y + braceHeight / 2} x2={x + braceWidth / 2} y2={y - braceHeight / 2} />
    </g>
  );
};

export function BeamLine({ drawingWidth, baseY, isFullyBraced = false }: BeamLineProps) {
  const bracedBeamBorderProps = isFullyBraced ? {
    x1: 0,
    y1: baseY,
    x2: drawingWidth,
    y2: baseY,
    stroke: "green",
    strokeWidth: 20,
  } : null;

  const beamLineProps = {
    x1: 0,
    y1: baseY,
    x2: drawingWidth,
    y2: baseY,
    stroke: SVG_CONSTANTS.BEAM_COLOR,
    strokeWidth: SVG_CONSTANTS.BEAM_STROKE_WIDTH,
  };

  return (
    <>
      {/* Render braced border first (behind beam line) if fully braced */}
      {bracedBeamBorderProps && <line {...bracedBeamBorderProps} />}
      <line {...beamLineProps} />
    </>
  );
}

export function Brace({ scale, baseY, beamLength, unbracedLength, keyPrefix }: BraceProps) {
  if (unbracedLength <= 0 || unbracedLength >= beamLength) return null;

  const braceElements: JSX.Element[] = [];
  const tolerance = 1e-6;

  let currentPosition = unbracedLength;
  let i = 1;
  while (currentPosition < beamLength - tolerance) {
    const x = currentPosition * scale;
    const braceKey = `${keyPrefix}-brace-${i}`;
    braceElements.push(drawBraceSVG(x, baseY, braceKey));
    currentPosition += unbracedLength;
    i++;
  }

  return <>{braceElements}</>;
}

export function BraceCollection({ scale, baseY, beamLength, beamPropertiesState }: BraceCollectionProps) {
  const braceElements: JSX.Element[] = [];

  // Check for wood bracing
  if (beamPropertiesState?.isBraced && beamPropertiesState.lu) {
    const luValue = parseFloat(beamPropertiesState.lu);
    if (!isNaN(luValue) && luValue > 0) {
      braceElements.push(
        <Brace
          key="wood-braces"
          scale={scale}
          baseY={baseY}
          beamLength={beamLength}
          unbracedLength={luValue}
          keyPrefix="wood-braces"
        />
      );
    }
  }

  // Check for steel bracing
  if ((beamPropertiesState as any)?.isSteelBraced && (beamPropertiesState as any)?.steelUnbracedLength) {
    const steelLuValue = parseFloat((beamPropertiesState as any).steelUnbracedLength);
    if (!isNaN(steelLuValue) && steelLuValue > 0) {
      braceElements.push(
        <Brace
          key="steel-braces"
          scale={scale}
          baseY={baseY}
          beamLength={beamLength}
          unbracedLength={steelLuValue}
          keyPrefix="steel-braces"
        />
      );
    }
  }

  return <>{braceElements}</>;
} 