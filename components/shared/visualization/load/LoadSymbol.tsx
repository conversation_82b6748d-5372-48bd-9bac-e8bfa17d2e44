import React from 'react';
import { LoadType, Type } from "@/lib/types/load/load-type";
import { LoadGroup } from "@/lib/types/load/load-group";
import { SVG_CONSTANTS, loadTypeSymbols, loadTypeColors } from "../types";

interface PointLoadProps {
  x: number;
  y: number;
  magnitude: number;
  loadType: LoadType;
  label: string;
  description: string;
  level: number;
  maxTotalMagnitude: number;
  keyPrefix: string;
}

interface DistributedLoadProps {
  x1: number;
  x2: number;
  y: number;
  startMagnitudes: Partial<Record<LoadType, number | string>>;
  endMagnitudes: Partial<Record<LoadType, number | string>>;
  loadType: LoadType;
  label: string;
  description: string;
  level: number;
  maxTotalMagnitude: number;
  keyPrefix: string;
}

interface LoadCollectionProps {
  loadGroups: LoadGroup[];
  scale: number;
  baseY: number;
  maxTotalMagnitude: number;
}

export function PointLoad({
  x,
  y,
  magnitude,
  loadType,
  label,
  description,
  level,
  maxTotalMagnitude,
  keyPrefix
}: PointLoadProps) {
  const color = loadTypeColors[loadType];
  const relativeMagnitude = maxTotalMagnitude > 1e-6 ? Math.abs(magnitude) / maxTotalMagnitude : 0;
  const arrowHeight = SVG_CONSTANTS.MIN_LOAD_HEIGHT + relativeMagnitude * (SVG_CONSTANTS.MAX_LOAD_HEIGHT - SVG_CONSTANTS.MIN_LOAD_HEIGHT);
  const verticalOffset = level * (SVG_CONSTANTS.MAX_LOAD_HEIGHT + SVG_CONSTANTS.LEVEL_PADDING);
  const startY = y - verticalOffset - arrowHeight;
  const endY = y - verticalOffset;

  return (
    <g key={`${keyPrefix}-pointload`}>
      <line 
        x1={x} 
        y1={startY} 
        x2={x} 
        y2={endY - SVG_CONSTANTS.LOAD_ARROW_SIZE}
        stroke={color} 
        strokeWidth={2} 
      />
      <polygon 
        points={`${x},${endY} ${x - SVG_CONSTANTS.LOAD_ARROW_SIZE},${endY - SVG_CONSTANTS.LOAD_ARROW_SIZE * 1.5} ${x + SVG_CONSTANTS.LOAD_ARROW_SIZE},${endY - SVG_CONSTANTS.LOAD_ARROW_SIZE * 1.5}`}
        fill={color} 
      />
      <text 
        x={x + SVG_CONSTANTS.LOAD_ARROW_SIZE + 2} 
        y={startY} 
        fontSize={SVG_CONSTANTS.LABEL_FONT_SIZE} 
        fill={SVG_CONSTANTS.TEXT_COLOR}
        textAnchor="start"
      >
        {`${label !== 'default' ? label + ': ' : ''}${description}`}
      </text>
    </g>
  );
}

export function DistributedLoad({
  x1,
  x2,
  y,
  startMagnitudes,
  endMagnitudes,
  loadType,
  label,
  description,
  level,
  maxTotalMagnitude,
  keyPrefix
}: DistributedLoadProps) {
  const color = loadTypeColors[loadType];
  
  const startMag = Math.abs(parseFloat(startMagnitudes[loadType]?.toString() || '0'));
  const endMag = Math.abs(parseFloat(endMagnitudes[loadType]?.toString() || startMag.toString()));

  const relativeStartMag = maxTotalMagnitude > 1e-6 ? startMag / maxTotalMagnitude : 0;
  const relativeEndMag = maxTotalMagnitude > 1e-6 ? endMag / maxTotalMagnitude : 0;
  
  const startRectHeight = SVG_CONSTANTS.MIN_LOAD_HEIGHT + relativeStartMag * (SVG_CONSTANTS.MAX_LOAD_HEIGHT - SVG_CONSTANTS.MIN_LOAD_HEIGHT);
  const endRectHeight = SVG_CONSTANTS.MIN_LOAD_HEIGHT + relativeEndMag * (SVG_CONSTANTS.MAX_LOAD_HEIGHT - SVG_CONSTANTS.MIN_LOAD_HEIGHT);
  
  const verticalOffset = level * (SVG_CONSTANTS.MAX_LOAD_HEIGHT + SVG_CONSTANTS.LEVEL_PADDING);
  const topY1 = y - verticalOffset - startRectHeight;
  const topY2 = y - verticalOffset - endRectHeight;
  const bottomY = y - verticalOffset;

  const arrowSpacing = 30;
  const numArrows = Math.max(1, Math.floor((x2 - x1) / arrowSpacing));
  const arrows: JSX.Element[] = [];
  
  for (let i = 0; i <= numArrows; i++) {
    const ratio = numArrows === 0 ? 0 : i / numArrows;
    const arrowX = x1 + ratio * (x2 - x1);
    const currentMag = startMag + ratio * (endMag - startMag);
    const relativeCurrentMag = maxTotalMagnitude > 1e-6 ? currentMag / maxTotalMagnitude : 0;
    const currentArrowHeight = SVG_CONSTANTS.MIN_LOAD_HEIGHT + relativeCurrentMag * (SVG_CONSTANTS.MAX_LOAD_HEIGHT - SVG_CONSTANTS.MIN_LOAD_HEIGHT);
    const currentTopY = y - verticalOffset - currentArrowHeight;
    
    arrows.push(
      <g key={`${keyPrefix}-distarrow-${i}`}>
        <line 
          x1={arrowX} 
          y1={currentTopY} 
          x2={arrowX} 
          y2={bottomY - SVG_CONSTANTS.LOAD_ARROW_SIZE} 
          stroke={color} 
          strokeWidth={1} 
        />
        <polygon 
          points={`${arrowX},${bottomY} ${arrowX - SVG_CONSTANTS.LOAD_ARROW_SIZE / 1.5},${bottomY - SVG_CONSTANTS.LOAD_ARROW_SIZE} ${arrowX + SVG_CONSTANTS.LOAD_ARROW_SIZE / 1.5},${bottomY - SVG_CONSTANTS.LOAD_ARROW_SIZE}`} 
          fill={color} 
        />
      </g>
    );
  }

  return (
    <g key={`${keyPrefix}-distload`}>
      <line x1={x1} y1={topY1} x2={x2} y2={topY2} stroke={color} strokeWidth={2} />
      <line x1={x1} y1={topY1} x2={x1} y2={bottomY} stroke={color} strokeWidth={0.5} strokeDasharray="2,2" />
      <line x1={x2} y1={topY2} x2={x2} y2={bottomY} stroke={color} strokeWidth={0.5} strokeDasharray="2,2" />
      {arrows}
      <text 
        x={(x1 + x2) / 2} 
        y={Math.min(topY1, topY2) - 5}
        fontSize={SVG_CONSTANTS.LABEL_FONT_SIZE} 
        fill={SVG_CONSTANTS.TEXT_COLOR}
        textAnchor="middle"
      >
        {`${label !== 'default' ? label + ': ' : ''}${description}`}
      </text>
    </g>
  );
}

export function LoadCollection({ loadGroups, scale, baseY, maxTotalMagnitude }: LoadCollectionProps) {
  const loadElements: JSX.Element[] = [];

  loadGroups.forEach((group, index) => {
    const groupKey = `load-group-${index}`;
    const groupLevel = group.level ?? 0;
    const firstLoad = group.loads && group.loads[0];
    if (!firstLoad) return;

    // Reconstruct magnitude maps for this specific group from its loads array
    const currentStartMagnitudes: Partial<Record<LoadType, number>> = {};
    const currentEndMagnitudes: Partial<Record<LoadType, number>> = {};
    group.loads.forEach(load => {
      if (load.startMagnitude !== undefined) {
        currentStartMagnitudes[load.loadType] = load.startMagnitude;
      }
      if (load.type === Type.DISTRIBUTED && load.endMagnitude !== undefined) {
        currentEndMagnitudes[load.loadType] = load.endMagnitude;
      } else if (load.type === Type.DISTRIBUTED && load.startMagnitude !== undefined) {
        currentEndMagnitudes[load.loadType] = load.startMagnitude;
      }
    });

    // Generate description string using the reconstructed maps
    const loadDescription = Object.entries(currentStartMagnitudes)
      .map(([type, startMag]) => {
        const loadType = type as LoadType;
        if (Math.abs(startMag) < 1e-6) return null;

        const symbol = loadTypeSymbols[loadType];
        if (group.type === Type.DISTRIBUTED) {
          const endMag = currentEndMagnitudes[loadType] ?? startMag;
          if (Math.abs(startMag - endMag) > 1e-6) {
            return `${symbol}(${startMag.toFixed(0)}->${endMag.toFixed(0)})`;
          } else {
            return `${symbol}(${startMag.toFixed(0)})`;
          }
        } else {
          return `${symbol}(${startMag.toFixed(0)})`;
        }
      })
      .filter(Boolean)
      .join(", ");
      
    if (group.type === Type.POINT) {
      const xPos = group.startPosition * scale;
      const commonMagnitude = firstLoad.startMagnitude ?? 0;
      loadElements.push(
        <PointLoad
          key={groupKey}
          x={xPos}
          y={baseY}
          magnitude={commonMagnitude}
          loadType={firstLoad.loadType}
          label={group.label || 'default'}
          description={loadDescription}
          level={groupLevel}
          maxTotalMagnitude={maxTotalMagnitude}
          keyPrefix={groupKey}
        />
      );
    } else if (group.type === Type.DISTRIBUTED) {
      const startX = (group.startPosition ?? 0) * scale;
      const endX = (group.endPosition ?? group.startPosition ?? 0) * scale;
      loadElements.push(
        <DistributedLoad
          key={groupKey}
          x1={startX}
          x2={endX}
          y={baseY}
          startMagnitudes={currentStartMagnitudes}
          endMagnitudes={currentEndMagnitudes}
          loadType={firstLoad.loadType}
          label={group.label || 'default'}
          description={loadDescription}
          level={groupLevel}
          maxTotalMagnitude={maxTotalMagnitude}
          keyPrefix={groupKey}
        />
      );
    }
  });

  return <>{loadElements}</>;
} 