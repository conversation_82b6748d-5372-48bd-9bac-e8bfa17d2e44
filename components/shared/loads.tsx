"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { STRING_CONSTANTS, CSS_CLASSES } from "@/components/constants";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronDown, ChevronUp, Trash2, LinkIcon } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { LoadType, Type } from "@/lib/types/load/load-type";
import { LoadGroup } from "@/lib/types/load/load-group";
import {
  LoadGroupForm,
  createLoadGroupForm,
  loadGroupsToF<PERSON>,
  createLoadGroupFromForm,
  updateLoadGroupForm,
} from "@/lib/utils/load-group-utils";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";

// Minimal interface for calculation summaries fetched for linking
interface CalculationSummary {
  id: string;
  name: string | null;
}

type ReactionDetail = {
  id: string;
  name: string;
  value: number;
  position: number;
};

interface LoadsProps {
  loadGroups: LoadGroup[];
  memberLength: number;
  unitSystem: UnitSystem;
  onChange: (loadGroups: LoadGroup[]) => void;
  projectId?: string;
  currentCalculationId?: string;
  memberType?: 'beam' | 'column' | 'lateral';
}

const loadTypeInfo = {
  [LoadType.DEAD]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.DEAD,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.DEAD,
  },
  [LoadType.LIVE]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.LIVE,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.LIVE,
  },
  [LoadType.SNOW]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.SNOW,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.SNOW,
  },
  [LoadType.WIND]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.WIND,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.WIND,
  },
  [LoadType.EARTHQUAKE]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.EARTHQUAKE,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.EARTHQUAKE,
  },
  [LoadType.SOIL]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.SOIL,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.SOIL,
  },
  [LoadType.FLOOD]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.FLOOD,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.FLOOD,
  },
  [LoadType.TEMPERATURE]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.TEMPERATURE,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.TEMPERATURE,
  },
  [LoadType.ROOF_LIVE]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.ROOF_LIVE,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.ROOF_LIVE,
  },
  [LoadType.RAIN]: {
    symbol: STRING_CONSTANTS.LOAD_SYMBOLS.RAIN,
    color: CSS_CLASSES.TEXT.WHITE,
    description: STRING_CONSTANTS.LOAD_DESCRIPTIONS.RAIN,
  },
};

interface SupportForceReactions {
  maxUp: number;
  maxDown: number;
  maxUpCombo: string;
  maxDownCombo: string;
}

interface AnalysisResultsData {
  supportReactions?: {
    forces?: Record<string, SupportForceReactions>;
    moments?: Record<string, any>;
  };
  individualLoadReactions?: {
    forces?: Record<string, Record<string, number>>;
  };
  maxShearValue?: any;
  maxMomentValue?: any;
}

function parseReactionsFromAnalysisResult(resultsData: AnalysisResultsData | string | null | undefined, sourceSupportsJson: string | null | undefined): ReactionDetail[] {
  if (!resultsData) return [];

  let data: AnalysisResultsData;
  if (typeof resultsData === 'string') {
    try {
      data = JSON.parse(resultsData);
    } catch (e) {
      console.error("Failed to parse resultsData string:", e);
      return [];
    }
  } else {
    data = resultsData;
  }

  const reactions: ReactionDetail[] = [];
  let sourceSupports: Array<{ type: string; position: number }> = [];

  if (sourceSupportsJson) {
    try {
      sourceSupports = JSON.parse(sourceSupportsJson);
    } catch (e) {
      console.error("Failed to parse sourceSupportsJson:", e);
    }
  }

  if (data.supportReactions?.forces) {
    Object.entries(data.supportReactions.forces).forEach(([supportKey, forceReactions]) => {
      if (forceReactions.maxUp !== undefined && forceReactions.maxUp !== 0) {
        const supportIndex = parseInt(supportKey, 10) - 1;
        let position: number | null = null;

        if (sourceSupports[supportIndex] !== undefined) {
          position = sourceSupports[supportIndex].position;
        } else {
          console.warn(`Could not find matching support for key ${supportKey} in sourceSupports:`, sourceSupports);
          const positionMatch = supportKey.match(/@(-?\d*\.?\d+)/);
          if (positionMatch && positionMatch[1]) {
            position = parseFloat(positionMatch[1]);
            console.warn(`Fell back to parsing position from supportKey ${supportKey} for reaction value ${forceReactions.maxUp}`);
          } else {
            console.warn(`Still could not determine position for supportKey: ${supportKey}`);
            return;
          }
        }

        reactions.push({
          id: `support_${supportKey}_maxUp_${position !== null ? position.toFixed(2) : 'unknownPos'}`,
          name: `S_${supportKey}`,
          value: forceReactions.maxUp,
          position: position as number,
        });
      }
    });
  }

  if (reactions.length === 0) {
    console.warn("parseReactionsFromAnalysisResult: No non-zero 'Max Upward' reactions could be parsed from the provided data.", data);
  }

  return reactions.sort((a, b) => a.name.localeCompare(b.name));
}

export function Loads({
  loadGroups,
  memberLength,
  unitSystem,
  onChange,
  projectId,
  currentCalculationId,
  memberType = 'beam',
}: LoadsProps) {
  const [expandedGroupId, setExpandedGroupId] = useState<string | null>(null);
  const [forms, setForms] = useState<LoadGroupForm[]>(() =>
    loadGroupsToForms(loadGroups)
  );

  const [isLinkReactionModalOpen, setIsLinkReactionModalOpen] = useState(false);
  const [linkReactionStep, setLinkReactionStep] = useState<'selectBeam' | 'selectReaction' | 'confirmLoadPosition'>('selectBeam');
  const [availableProjectBeams, setAvailableProjectBeams] = useState<CalculationSummary[]>([]);
  const [selectedSourceBeamId, setSelectedSourceBeamId] = useState<string | null>(null);
  const [sourceBeamReactions, setSourceBeamReactions] = useState<ReactionDetail[]>([]);
  const [selectedReactionDetail, setSelectedReactionDetail] = useState<ReactionDetail | null>(null);
  const [loadApplicationPositionInput, setLoadApplicationPositionInput] = useState<string>("0");
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoadingBeams, setIsLoadingBeams] = useState(false);
  const [isLoadingReactions, setIsLoadingReactions] = useState(false);

  useEffect(() => {
    setForms(loadGroupsToForms(loadGroups));
  }, [loadGroups]);

  const units = getUnitsBySystem(unitSystem);

  const addLoadGroup = (options?: Parameters<typeof createLoadGroupForm>[1]) => {
    const newForm = createLoadGroupForm(memberLength, options);
    setForms((prev) => [...prev, newForm]);
    setExpandedGroupId(newForm.id);
    return newForm.id;
  };

  const removeLoadGroup = (id: string) => {
    const form = forms.find((f) => f.id === id);
    if (!form) return;

    setForms(forms.filter((f) => f.id !== id));
    if (expandedGroupId === id) {
      setExpandedGroupId(null);
    }
    
    const remainingForms = forms.filter((f) => f.id !== id);
    const newLoadGroups = remainingForms
      .map(form => createLoadGroupFromForm(form))
      .filter(lg => lg !== null) as LoadGroup[];
    
    onChange(newLoadGroups);
  };

  const updateForm = (id: string, updates: Partial<LoadGroupForm>) => {
    setForms((prevForms) =>
      prevForms.map((form) =>
        form.id === id ? updateLoadGroupForm(form, updates) : form
      )
    );
  };

  const handleSave = (form: LoadGroupForm) => {
    const newGroup = createLoadGroupFromForm(form);
    if (!newGroup) return;

    // Find existing load group by ID to update it
    const existingIndex = loadGroups.findIndex(g => g.id === form.id);
    
    let updatedGroups: LoadGroup[];
    if (existingIndex > -1) {
      // Update existing load group
      updatedGroups = [...loadGroups];
      updatedGroups[existingIndex] = newGroup;
    } else {
      // Add new load group
      updatedGroups = [...loadGroups, newGroup];
    }
    
    onChange(updatedGroups);
    setExpandedGroupId(null);
  };

  const handleKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>,
    form: LoadGroupForm 
  ) => {
    if (event.key === "Enter") {
      const hasMagnitude = Object.values(form.startMagnitudes).some(
        (val) => typeof val === "number" && isFinite(val) && val !== 0
      );
      const hasEndMagnitude = form.type === Type.DISTRIBUTED && form.endMagnitudes && Object.values(form.endMagnitudes).some(
        (val) => typeof val === "number" && isFinite(val) && val !== 0
      );

      if (hasMagnitude || hasEndMagnitude) {
        handleSave(form);
      }
    }
  };

  useEffect(() => {
    if (isLinkReactionModalOpen && linkReactionStep === 'selectBeam' && projectId) {
      const fetchBeams = async () => {
        setIsLoadingBeams(true);
        try {
          console.log('[Loads] Fetching beams. CurrentCalculationId:', currentCalculationId, 'ProjectId:', projectId);
          const response = await fetch(`/api/projects/${projectId}/calculations?type=BEAM`);
          if (!response.ok) {
            let errorMsg = response.statusText;
            try {
              const errorData = await response.json();
              errorMsg = errorData.error || errorData.message || errorMsg;
            } catch (e) { /* Ignore */ }
            throw new Error(`Failed to fetch project beams: ${response.status} ${errorMsg}`);
          }
          const data = await response.json();
          console.log('[Loads] Raw beams data from API:', data); 
          if (Array.isArray(data)) {
            const allBeams: CalculationSummary[] = data.map((beam: any) => ({ id: beam.id, name: beam.name })); 
            console.log('[Loads] All beams before filter (data is array):', allBeams.map(b => ({ id: b.id, name: b.name })));
            const filtered = allBeams.filter(beam => beam.id !== currentCalculationId);
            console.log('[Loads] Filtered beams (data is array):', filtered.map(b => ({ id: b.id, name: b.name })));
            setAvailableProjectBeams(filtered);
          } else if (data && Array.isArray(data.calculations)) { 
            const allBeams: CalculationSummary[] = data.calculations.map((beam: any) => ({ id: beam.id, name: beam.name }));
            console.log('[Loads] All beams before filter (data.calculations is array):', allBeams.map(b => ({ id: b.id, name: b.name })));
            const filtered = allBeams.filter(beam => beam.id !== currentCalculationId);
            console.log('[Loads] Filtered beams (data.calculations is array):', filtered.map(b => ({ id: b.id, name: b.name })));
            setAvailableProjectBeams(filtered);
          } else {
            console.warn("[Loads] Fetched project beams data is not an array:", data);
            setAvailableProjectBeams([]);
          }
        } catch (error) {
          console.error("Failed to fetch project beams:", error);
          setAvailableProjectBeams([]);
        } finally {
          setIsLoadingBeams(false);
        }
      };
      fetchBeams();
    }
  }, [isLinkReactionModalOpen, linkReactionStep, projectId, currentCalculationId]);

  useEffect(() => {
    if (selectedSourceBeamId && linkReactionStep === 'selectReaction') {
      const fetchReactionsForSelectedBeam = async () => {
        if (!projectId || !selectedSourceBeamId) return;
        setIsLoadingReactions(true);
        try {
          const response = await fetch(`/api/calculations/${selectedSourceBeamId}/analysis-results`);
          if (!response.ok) {
            throw new Error(`Failed to fetch source beam analysis results (status: ${response.status})`);
          }
          const sourceAnalysisResult = await response.json();

          console.log('[Loads] Fetched sourceAnalysisResult for linking:', JSON.stringify(sourceAnalysisResult, null, 2));
          console.log('[Loads] Raw results string from fetched sourceAnalysisResult:', sourceAnalysisResult.results);

          if (!sourceAnalysisResult || !sourceAnalysisResult.results) {
            console.warn("Source beam analysis results or the 'results' field is missing.");
            setSourceBeamReactions([]);
            return;
          }

          const parsedReactions = parseReactionsFromAnalysisResult(sourceAnalysisResult.results, sourceAnalysisResult.supports);
          setSourceBeamReactions(parsedReactions);

          if (parsedReactions.length === 0) {
            console.warn("No reactions could be parsed from the analysis results for beam:", selectedSourceBeamId, "Raw data:", sourceAnalysisResult.results);
          }

        } catch (error) {
          console.error("Failed to fetch or parse beam reactions:", error);
          setSourceBeamReactions([]);
        } finally {
          setIsLoadingReactions(false);
        }
      };
      fetchReactionsForSelectedBeam();
    }
  }, [selectedSourceBeamId, linkReactionStep, projectId]);

  const handleReactionSelected = (selectedReactionId: string) => {
    const reactionDetail = sourceBeamReactions.find(r => r.id === selectedReactionId);
    
    if (!reactionDetail) {
      console.error("Selected reaction not found:", selectedReactionId);
      return;
    }
    setSelectedReactionDetail(reactionDetail);
    setLoadApplicationPositionInput("0");
    setLinkReactionStep('confirmLoadPosition');
  };

  const handleConfirmLoadApplication = () => {
    if (!selectedReactionDetail || !selectedSourceBeamId) {
      console.error("Cannot confirm load application: missing selected reaction or source beam.");
      setIsLinkReactionModalOpen(false);
      return;
    }

    const sourceBeam = availableProjectBeams.find(b => b.id === selectedSourceBeamId);
    const sourceBeamName = sourceBeam?.name || 'Linked Beam';
    const finalLabel = `${sourceBeamName} - ${selectedReactionDetail.name} @ ${selectedReactionDetail.position.toFixed(2)}`;

    const positionOnCurrentMemberParsed = parseFloat(loadApplicationPositionInput);

    if (isNaN(positionOnCurrentMemberParsed) || positionOnCurrentMemberParsed < 0 || positionOnCurrentMemberParsed > memberLength) {
      alert(`Invalid load application position: ${loadApplicationPositionInput}. Must be between 0 and ${memberLength}.`);
      return;
    }

    const newForm = createLoadGroupForm(memberLength, {
      isLinkedLoad: true,
      linkedSourceCalculationId: selectedSourceBeamId,
      linkedSourceSupportPosition: selectedReactionDetail.position,
      label: finalLabel,
      type: Type.POINT,
      startPosition: positionOnCurrentMemberParsed.toString(),
      startMagnitudes: { [LoadType.DEAD]: selectedReactionDetail.value }, 
      isEditable: false, 
    });

    const newLoadGroup = createLoadGroupFromForm(newForm);
    if (newLoadGroup) {
      const updatedLoadGroups = [...loadGroups, newLoadGroup];
      onChange(updatedLoadGroups);
    }

    setIsLinkReactionModalOpen(false);
    setSelectedSourceBeamId(null);
    setSourceBeamReactions([]);
    setSelectedReactionDetail(null);
    setLoadApplicationPositionInput("0");
    setLinkReactionStep('selectBeam');
    setSearchTerm("");
  };

  const filteredBeams = availableProjectBeams.filter(beam => 
    beam.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getMemberLabel = () => {
    switch (memberType) {
      case 'column': return 'Column';
      case 'lateral': return 'Lateral Member';
      default: return 'Beam';
    }
  };

  return (
    <div className="space-y-4">
      {forms.map((form, index) => {
        return (
        <div key={form.id} className="border rounded-lg overflow-hidden">
          <div
            className="flex items-center justify-between p-4 bg-background cursor-pointer"
            onClick={() =>
              setExpandedGroupId(expandedGroupId === form.id ? null : form.id)
            }
          >
            <div className="flex items-center gap-2">
              {expandedGroupId === form.id ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
              <span className="font-medium">
                {form.isLinkedLoad && <LinkIcon className="h-4 w-4 mr-1 inline" />}
                {form.label || `Load Input ${index + 1}`}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                removeLoadGroup(form.id);
              }}
              disabled={!form.isEditable && !form.isLinkedLoad}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          {expandedGroupId === form.id && (
            <div className="p-4 border-t space-y-4">
              <div className="flex items-center gap-2">
                <Label className="w-16">Label</Label>
                <Input
                  value={form.label}
                  onChange={(e) =>
                    updateForm(form.id, { label: e.target.value })
                  }
                  placeholder="Enter load label"
                  onKeyDown={(e) => handleKeyDown(e, form)}
                  disabled={!form.isEditable && !form.isLinkedLoad}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Load Type</Label>
                  <Select
                    value={form.type}
                    onValueChange={(value: Type) =>
                      updateForm(form.id, { type: value })
                    }
                    disabled={!form.isEditable || form.isLinkedLoad}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={Type.POINT}>
                        Point Load ({units.force})
                      </SelectItem>
                      <SelectItem value={Type.DISTRIBUTED}>
                        Distributed Load ({units.distributedLoad})
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {form.type === Type.POINT ? (
                  <div className="space-y-2">
                    <Label>Position ({units.length})</Label>
                    <Input
                      type="number"
                      value={form.startPosition}
                      onChange={(e) =>
                        updateForm(form.id, {
                          startPosition: e.target.value,
                        })
                      }
                      max={memberLength}
                      min={0}
                      onKeyDown={(e) => handleKeyDown(e, form)}
                      disabled={!form.isEditable}
                    />
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-2">
                        <Label>Start ({units.length})</Label>
                        <Input
                          id={`lg-${form.id}-start-position`}
                          type="number"
                          value={form.startPosition}
                          onChange={(e) =>
                            updateForm(form.id, {
                              startPosition: e.target.value,
                            })
                          }
                          max={memberLength}
                          min={0}
                          onKeyDown={(e) => handleKeyDown(e, form)}
                          disabled={!form.isEditable}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>End ({units.length})</Label>
                        <Input
                          id={`lg-${form.id}-end-position`}
                          type="number"
                          value={form.endPosition}
                          onChange={(e) =>
                            updateForm(form.id, {
                              endPosition: e.target.value,
                            })
                          }
                          max={memberLength}
                          min={parseFloat(form.startPosition) || 0}
                          onKeyDown={(e) => handleKeyDown(e, form)}
                          disabled={!form.isEditable}
                        />
                      </div>
                    </div>
                    <div className="space-y-2 mt-4">
                      <Label>Tributary Width ({units.length})</Label>
                      <Input
                        type="number"
                        value={form.tributaryWidth ?? ''}
                        onChange={(e) =>
                          updateForm(form.id, { 
                            tributaryWidth: e.target.value 
                          })
                        }
                        min={0}
                        step={0.1}
                        placeholder="Enter tributary width"
                        onKeyDown={(e) => handleKeyDown(e, form)}
                        disabled={!form.isEditable}
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="space-y-2">
                <Label>Magnitudes ({form.type === Type.POINT ? units.force : units.distributedLoad})</Label>
                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                  {Object.entries(loadTypeInfo).map(([key, info]) => (
                    <div key={key} className="flex items-center gap-2">
                      <Label htmlFor={`${form.id}-${key}`} className={`w-8 text-sm ${info.color}`}>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>{info.symbol}</TooltipTrigger>
                            <TooltipContent>
                              <p>{info.description}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Input
                        id={`${form.id}-${key}`}
                        type="number"
                        value={form.startMagnitudes[key as LoadType] || ""}
                        onChange={(e) => {
                          const value = parseFloat(e.target.value);
                          updateForm(form.id, {
                            startMagnitudes: {
                              ...form.startMagnitudes,
                              [key as LoadType]: isNaN(value) ? "" : value,
                            },
                          });
                        }}
                        className="flex-1"
                        onKeyDown={(e) => handleKeyDown(e, form)}
                        disabled={!form.isEditable || (form.isLinkedLoad && key !== LoadType.DEAD)}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {form.type === Type.DISTRIBUTED && (
                <div className="space-y-2">
                  <Label>End Magnitudes (for varying load)</Label>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    {Object.entries(loadTypeInfo).map(([key, info]) => (
                      <div key={key} className="flex items-center gap-2">
                        <Label htmlFor={`${form.id}-${key}-end`} className={`w-8 text-sm ${info.color}`}>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>{info.symbol}</TooltipTrigger>
                              <TooltipContent>
                                <p>{info.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <Input
                          id={`${form.id}-${key}-end`}
                          type="number"
                          value={form.endMagnitudes?.[key as LoadType] || ""}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value);
                            updateForm(form.id, {
                              endMagnitudes: {
                                ...form.endMagnitudes,
                                [key as LoadType]: isNaN(value) ? "" : value,
                              },
                            });
                          }}
                          className="flex-1"
                          onKeyDown={(e) => handleKeyDown(e, form)}
                          disabled={!form.isEditable}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {form.isEditable && (
                <Button onClick={() => handleSave(form)} className="w-full">
                  Save Load
                </Button>
              )}
            </div>
          )}
        </div>
      )})}

      <Button onClick={() => addLoadGroup()} className="w-full">
        Add Manual Load
      </Button>
      {projectId && (
        <Button 
          onClick={() => {
            setIsLinkReactionModalOpen(true);
            setLinkReactionStep('selectBeam');
            setSelectedSourceBeamId(null);
            setSourceBeamReactions([]);
            setSearchTerm("");
          }}
          className="w-full mt-2"
          variant="outline"
        >
          <LinkIcon className="h-4 w-4 mr-2" />
          Link {getMemberLabel()} Reaction as Load
        </Button>
      )}

      <Dialog open={isLinkReactionModalOpen} onOpenChange={setIsLinkReactionModalOpen}>
        <DialogContent className="sm:max-w-[425px] md:max-w-[600px] lg:max-w-[800px] max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              {linkReactionStep === 'selectBeam' ? `Link Reaction: Select Source ${getMemberLabel()}` :
               linkReactionStep === 'selectReaction' ? `Link Reaction: Select Reaction from "${availableProjectBeams.find(b => b.id === selectedSourceBeamId)?.name || 'Selected ' + getMemberLabel()}"` :
               'Link Reaction: Confirm Load Position'}
            </DialogTitle>
            <DialogDescription>
              {linkReactionStep === 'selectBeam' 
                ? `Choose a ${memberType} from this project whose reaction you want to apply as a load.` 
                : linkReactionStep === 'selectReaction'
                  ? `Selected Source: ${availableProjectBeams.find(b => b.id === selectedSourceBeamId)?.name || 'Selected ' + getMemberLabel()}. Choose a reaction below.`
                  : selectedReactionDetail 
                    ? `Applying reaction "${selectedReactionDetail.name}" (Value: ${selectedReactionDetail.value.toFixed(2)} ${units.force}) from parent support at ${selectedReactionDetail.position.toFixed(2)} ${units.length}. Specify where this load applies on the current ${memberType} (0 to ${memberLength.toFixed(2)} ${units.length}).`
                    : 'Confirm load position details.'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-grow overflow-y-auto p-1 space-y-4">
            {linkReactionStep === 'selectBeam' && (
              <>
                <Input 
                  placeholder={`Search ${memberType} name...`} 
                  value={searchTerm} 
                  onChange={(e) => setSearchTerm(e.target.value)} 
                />
                {isLoadingBeams ? (
                  <p>Loading {memberType}s...</p>
                ) : filteredBeams.length > 0 ? (
                  <ul className="space-y-2">
                    {filteredBeams.map(beam => (
                      <li key={beam.id} 
                          className="p-2 border rounded hover:bg-accent cursor-pointer"
                          onClick={() => {
                            setSelectedSourceBeamId(beam.id);
                            setLinkReactionStep('selectReaction');
                          }}
                      >
                        {beam.name || `Unnamed ${getMemberLabel()}`} (ID: ...{beam.id.slice(-6)})
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>No other {memberType}s found in this project or matching your search.</p>
                )}
              </>
            )}

            {linkReactionStep === 'selectReaction' && selectedSourceBeamId && (
              <>
                {isLoadingReactions ? (
                  <p>Loading reactions...</p>
                ) : sourceBeamReactions.length > 0 ? (
                  <ul className="space-y-2">
                    {sourceBeamReactions.map(reaction => (
                      <li key={reaction.id} 
                          className="p-2 border rounded hover:bg-accent cursor-pointer"
                          onClick={() => handleReactionSelected(reaction.id)}
                      >
                        {reaction.name}: {reaction.value.toFixed(2)} {units.force}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>No reactions found for the selected {memberType}, or they could not be parsed. Ensure it has been analyzed and the results format is recognized.</p>
                )}
                 <Button variant="outline" onClick={() => { setLinkReactionStep('selectBeam'); setSelectedReactionDetail(null); }}>Back to {getMemberLabel()} Selection</Button>
              </>
            )}

            {linkReactionStep === 'confirmLoadPosition' && selectedReactionDetail && selectedSourceBeamId && (
              <div className="space-y-4 pt-4">
                <p className="text-sm text-muted-foreground">
                  Source {getMemberLabel()}: <strong>{availableProjectBeams.find(b => b.id === selectedSourceBeamId)?.name || 'N/A'}</strong><br />
                  Reaction: <strong>{selectedReactionDetail.name}</strong> (Value: {selectedReactionDetail.value.toFixed(2)} {units.force})<br />
                  From Parent Support At: <strong>{selectedReactionDetail.position.toFixed(2)} {units.length}</strong>
                </p>
                <Label htmlFor="loadApplicationPosition">Load Application Position on Current {getMemberLabel()} ({units.length})</Label>
                <Input 
                  id="loadApplicationPosition"
                  type="number"
                  placeholder={`Enter position (0 - ${memberLength.toFixed(2)})`}
                  value={loadApplicationPositionInput}
                  onChange={(e) => setLoadApplicationPositionInput(e.target.value)}
                  min={0}
                  max={memberLength}
                  step="any"
                />
                <div className="flex justify-between pt-2">
                  <Button variant="outline" onClick={() => setLinkReactionStep('selectReaction')}>Back to Reaction Selection</Button>
                  <Button onClick={handleConfirmLoadApplication}>Confirm & Add Linked Load</Button>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="mt-auto pt-2">
            <Button variant="ghost" onClick={() => {
              setIsLinkReactionModalOpen(false);
              setSelectedSourceBeamId(null);
              setSourceBeamReactions([]);
              setSelectedReactionDetail(null);
              setLoadApplicationPositionInput("0");
              setLinkReactionStep('selectBeam');
              setSearchTerm("");
            }}>Cancel</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Legacy export for backward compatibility
export const BeamLoads = Loads; 