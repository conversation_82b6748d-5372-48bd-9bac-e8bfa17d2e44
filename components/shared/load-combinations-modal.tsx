"use client";

import { useState, useRef, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ASCE_7_10_ASD_ComboMap } from "@/lib/types/load/asce-load-combo";
import { ASCE_7_10_LRFD_ComboMap } from "@/lib/types/load/asce-lrfd-combo";
import { LoadType } from "@/lib/types/load/load-type";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import { FileText, Edit2, Trash2, Save, X } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LoadCombinationsModalProps {
  designMethod: 'ASD' | 'LRFD';
  currentSelectedCombos?: string[];
  customLoadCombos?: Record<string, LoadComboFactor>;
  onSaveSelectedCombos?: (selectedComboNames: string[], selectedComboFactors: Record<string, LoadComboFactor>) => void;
  onDesignMethodChange?: (method: 'ASD' | 'LRFD') => void;
}

export function LoadCombinationsModal({ designMethod, currentSelectedCombos, customLoadCombos, onSaveSelectedCombos, onDesignMethodChange }: LoadCombinationsModalProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Calculate load combinations based on current design method
  const { loadComboMap, combinations, allLoadTypes } = useMemo(() => {
    const standardLoadComboMap = designMethod === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap;
    
    // Merge standard combinations with custom ones for the current design method
    const customCombosForCurrentMethod = customLoadCombos || {};
    const loadComboMap = { ...standardLoadComboMap, ...customCombosForCurrentMethod };
    const combinations = Object.entries(loadComboMap);
    
    // Get all unique load types used across all combinations
    const allLoadTypes = Array.from(
      new Set(
        combinations.flatMap(([_, comboFactor]) => 
          Object.keys(comboFactor.coefficients[0] || {})
        )
      )
    ).sort() as LoadType[];

    return { loadComboMap, combinations, allLoadTypes };
  }, [designMethod, customLoadCombos]);

  // Initialize selected combos based on currentSelectedCombos prop
  const [selectedCombos, setSelectedCombos] = useState(() => {
    if (currentSelectedCombos && currentSelectedCombos.length > 0) {
      // If there are saved selected combos, match them to the combinations
      return combinations.map(([comboName]) => currentSelectedCombos.includes(comboName));
    } else {
      // Default to all selected if no saved selection
      return combinations.map(() => true);
    }
  });

  // State for adding a new combo
  const [isAddingNewCombo, setIsAddingNewCombo] = useState(false);
  const [newComboName, setNewComboName] = useState("");
  const [newComboCoefficients, setNewComboCoefficients] = useState<Record<LoadType, number | "">>(
    {} as Record<LoadType, number | "">
  );

  // Store combos in state to allow adding new ones
  const [userCombos, setUserCombos] = useState(combinations);

  // State for editing a combo
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editComboName, setEditComboName] = useState("");
  const [editComboCoefficients, setEditComboCoefficients] = useState<Record<LoadType, number | "">>({} as any);

  // Blur handler for editing row
  const editingRowRef = useRef<HTMLTableRowElement>(null);
  const handleEditingRowBlur = (e: React.FocusEvent<HTMLTableRowElement>) => {
    // Only save if focus moves outside the row
    if (!editingRowRef.current?.contains(e.relatedTarget)) {
      saveEditCombo();
    }
  };

  // Ref for scroll area and new combo input
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const newComboFirstInputRef = useRef<HTMLInputElement>(null);

  // Ref and blur handler for add-new row
  const addNewRowRef = useRef<HTMLTableRowElement>(null);
  const handleAddNewRowBlur = (e: React.FocusEvent<HTMLTableRowElement>) => {
    if (!addNewRowRef.current?.contains(e.relatedTarget)) {
      if (newComboName.trim()) handleSaveNewCombo();
    }
  };

  useEffect(() => {
    if (isAddingNewCombo && scrollAreaRef.current) {
      // Scroll to bottom when adding new combo
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
      // Focus the first input in the new row
      setTimeout(() => {
        newComboFirstInputRef.current?.focus();
      }, 0);
    }
  }, [isAddingNewCombo]);

  const handleCheckboxChange = (index: number) => {
    setSelectedCombos((prev) => {
      const updated = [...prev];
      updated[index] = !updated[index];
      return updated;
    });
  };

  // When a new combo is added, also add to selectedCombos
  const handleSaveNewCombo = () => {
    if (!newComboName.trim()) return;
    // Filter out empty coefficients
    const filteredCoefficients = Object.fromEntries(
      Object.entries(newComboCoefficients).filter(([_, v]) => v !== "" && !isNaN(Number(v)))
    ) as Partial<Record<LoadType, number>>;
    const newFactor = new LoadComboFactor(filteredCoefficients, newComboName);
    setUserCombos((prev) => [
      ...prev,
      [newComboName, newFactor],
    ]);
    setSelectedCombos((prev) => [...prev, true]);
    setIsAddingNewCombo(false);
    setNewComboName("");
    setNewComboCoefficients(Object.fromEntries(allLoadTypes.map((lt) => [lt, ""])) as Record<LoadType, number | "">);
  };

  const handleCancelNewCombo = () => {
    setIsAddingNewCombo(false);
    setNewComboName("");
    setNewComboCoefficients(Object.fromEntries(allLoadTypes.map((lt) => [lt, ""])) as Record<LoadType, number | "">);
  };

  const handleNewComboCoefficientChange = (loadType: LoadType, value: string) => {
    setNewComboCoefficients((prev) => ({
      ...prev,
      [loadType]: value === "" ? "" : Number(value),
    }));
  };

  const startEditCombo = (index: number) => {
    setEditingIndex(index);
    setEditComboName(userCombos[index][0]);
    const coeffs = userCombos[index][1].coefficients[0] || {};
    setEditComboCoefficients(
      Object.fromEntries(allLoadTypes.map(lt => [lt, coeffs[lt] ?? ""])) as Record<LoadType, number | "">
    );
  };

  const handleEditComboCoefficientChange = (loadType: LoadType, value: string) => {
    setEditComboCoefficients(prev => ({
      ...prev,
      [loadType]: value === "" ? "" : Number(value),
    }));
  };

  const saveEditCombo = () => {
    if (editingIndex === null || !editComboName.trim()) return;
    const filteredCoefficients = Object.fromEntries(
      Object.entries(editComboCoefficients).filter(([_, v]) => v !== "" && !isNaN(Number(v)))
    ) as Partial<Record<LoadType, number>>;
    const newFactor = new LoadComboFactor(filteredCoefficients, editComboName);
    setUserCombos(prev => prev.map((c, i) => i === editingIndex ? [editComboName, newFactor] : c));
    setEditingIndex(null);
    setEditComboName("");
    setEditComboCoefficients({} as any);
  };

  const cancelEditCombo = () => {
    setEditingIndex(null);
    setEditComboName("");
    setEditComboCoefficients({} as any);
  };

  const deleteCombo = (index: number) => {
    setUserCombos(prev => prev.filter((_, i) => i !== index));
    setSelectedCombos(prev => prev.filter((_, i) => i !== index));
    if (editingIndex === index) cancelEditCombo();
  };

  // Select/unselect all functionality
  const allSelected = selectedCombos.every(Boolean);
  const someSelected = selectedCombos.some(Boolean);
  
  const handleSelectAll = () => {
    const newValue = !allSelected;
    setSelectedCombos(prev => prev.map(() => newValue));
  };

  // Global style for hiding number input steppers
  if (typeof window !== 'undefined') {
    const style = document.createElement('style');
    style.innerHTML = `
      input[type=number].no-spin::-webkit-inner-spin-button,
      input[type=number].no-spin::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      input[type=number].no-spin {
        -moz-appearance: textfield;
      }
    `;
    if (!document.head.querySelector('style[data-no-spin]')) {
      style.setAttribute('data-no-spin', 'true');
      document.head.appendChild(style);
    }
  }

  // Reset selections when design method changes
  useEffect(() => {
    const standardLoadComboMap = designMethod === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap;
    const customCombosForCurrentMethod = customLoadCombos || {};
    const newCombinations = Object.entries({ ...standardLoadComboMap, ...customCombosForCurrentMethod });
    setUserCombos(newCombinations);
    
    // If we have currentSelectedCombos, use those to determine selections
    // Otherwise, default to all selected for new users
    if (currentSelectedCombos && currentSelectedCombos.length > 0) {
      setSelectedCombos(newCombinations.map(([comboName]) => currentSelectedCombos.includes(comboName)));
    } else {
      // Only default to all selected if no previous selections exist
      setSelectedCombos(newCombinations.map(() => true));
    }
  }, [designMethod, currentSelectedCombos, customLoadCombos]);

  // Update newComboCoefficients when allLoadTypes changes
  useEffect(() => {
    setNewComboCoefficients(
      Object.fromEntries(allLoadTypes.map((lt) => [lt, ""])) as Record<LoadType, number | "">
    );
  }, [allLoadTypes]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          Load Combos
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl w-[1100px] h-[90vh] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {designMethod} Load Combinations
            <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded">
              ASCE 7-10
            </span>
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col flex-1 min-h-0 space-y-4 h-full">
          <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg border border-slate-700">
            <div className="flex items-center gap-3">
              <Label htmlFor="design-method" className="text-sm font-medium text-slate-200">
                Design Method:
              </Label>
              <Select
                value={designMethod}
                onValueChange={(value: 'ASD' | 'LRFD') => {
                  if (onDesignMethodChange) {
                    onDesignMethodChange(value);
                  }
                }}
              >
                <SelectTrigger className="w-20 bg-slate-700 border-slate-600 text-slate-200" id="design-method">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ASD">ASD</SelectItem>
                  <SelectItem value="LRFD">LRFD</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="text-sm text-slate-300">
              {designMethod === 'ASD' 
                ? 'Allowable Stress Design' 
                : 'Load and Resistance Factor Design'
              }
            </div>
          </div>

          <div className="flex-1 min-h-0 overflow-hidden border rounded-md relative">
            <div className="h-[400px] overflow-auto" ref={scrollAreaRef}>
              <Table className="w-full" style={{ tableLayout: 'fixed' }}>
                <TableHeader className="bg-[#2d2650] border-b border-gray-600 sticky top-0 z-10">
                  <TableRow>
                    <TableHead className="w-8 text-center">
                      <Checkbox
                        checked={allSelected}
                        ref={someSelected && !allSelected ? (el) => { 
                          const input = el?.querySelector('input');
                          if (input) input.indeterminate = true; 
                        } : undefined}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all load combinations"
                      />
                    </TableHead>
                    <TableHead className="w-[200px]">Load Combination</TableHead>
                    {allLoadTypes.map((loadType) => (
                      <TableHead key={loadType} className="text-center w-16">
                        <div className="font-bold">{loadType}</div>
                      </TableHead>
                    ))}
                    <TableHead className="w-24 text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {userCombos.map(([comboName, comboFactor], index) => (
                    editingIndex === index ? (
                      <TableRow
                        key={comboName}
                        className="hover:bg-inherit"
                        ref={editingRowRef}
                        tabIndex={-1}
                        onBlur={handleEditingRowBlur}
                      >
                        <TableCell className="w-8 text-center">
                          <Checkbox
                            checked={selectedCombos[index]}
                            onCheckedChange={() => handleCheckboxChange(index)}
                            aria-label={`Select ${comboName}`}
                          />
                        </TableCell>
                        <TableCell className="w-[200px]">
                          <Input
                            value={editComboName}
                            onChange={e => setEditComboName(e.target.value)}
                            className="font-mono text-sm"
                            tabIndex={0}
                          />
                        </TableCell>
                        {allLoadTypes.map((loadType) => (
                          <TableCell key={loadType} className="w-16 text-center">
                            <Input
                              type="number"
                              value={editComboCoefficients[loadType]}
                              onChange={e => handleEditComboCoefficientChange(loadType, e.target.value)}
                              className="w-16 text-sm no-spin"
                              tabIndex={0}
                            />
                          </TableCell>
                        ))}
                        <TableCell className="w-24 flex gap-2 justify-center">
                          <Button size="icon" variant="ghost" onClick={saveEditCombo} aria-label="Save"><Save className="w-4 h-4" /></Button>
                          <Button size="icon" variant="ghost" onClick={cancelEditCombo} aria-label="Cancel"><X className="w-4 h-4" /></Button>
                        </TableCell>
                      </TableRow>
                    ) : (
                      <TableRow key={comboName}>
                        <TableCell className="w-8 text-center">
                          <Checkbox
                            checked={selectedCombos[index]}
                            onCheckedChange={() => handleCheckboxChange(index)}
                            aria-label={`Select ${comboName}`}
                          />
                        </TableCell>
                        <TableCell className="w-[200px] font-mono text-sm">
                          {comboName}
                        </TableCell>
                        {allLoadTypes.map((loadType) => {
                          const coefficient = comboFactor.coefficients[0]?.[loadType];
                          return (
                            <TableCell key={loadType} className="w-16 text-center font-mono text-sm">
                              {coefficient !== undefined ? coefficient : '—'}
                            </TableCell>
                          );
                        })}
                        <TableCell className="w-24 flex gap-2 justify-center">
                          <Button size="icon" variant="ghost" onClick={() => startEditCombo(index)} aria-label="Edit"><Edit2 className="w-4 h-4" /></Button>
                          <Button size="icon" variant="ghost" onClick={() => deleteCombo(index)} aria-label="Delete"><Trash2 className="w-4 h-4 text-red-500" /></Button>
                        </TableCell>
                      </TableRow>
                    )
                  ))}
                  {isAddingNewCombo && (
                    <TableRow
                      tabIndex={-1}
                      ref={addNewRowRef}
                      onBlur={handleAddNewRowBlur}
                      className="hover:bg-inherit"
                    >
                      <TableCell className="w-8 text-center"></TableCell>
                      <TableCell className="w-[200px]">
                        <Input
                          ref={newComboFirstInputRef}
                          value={newComboName}
                          onChange={e => setNewComboName(e.target.value)}
                          placeholder="Combo Name"
                          className="font-mono text-sm"
                          tabIndex={0}
                        />
                      </TableCell>
                      {allLoadTypes.map((loadType) => (
                        <TableCell key={loadType} className="w-16 text-center">
                          <Input
                            type="number"
                            value={newComboCoefficients[loadType]}
                            onChange={e => handleNewComboCoefficientChange(loadType, e.target.value)}
                            placeholder="—"
                            className="w-16 text-sm no-spin"
                            tabIndex={0}
                          />
                        </TableCell>
                      ))}
                      <TableCell className="w-24 flex gap-2 justify-center">
                        <Button size="icon" variant="ghost" onClick={handleSaveNewCombo} disabled={!newComboName.trim()} aria-label="Save"><Save className="w-4 h-4" /></Button>
                        <Button size="icon" variant="ghost" onClick={handleCancelNewCombo} aria-label="Cancel"><X className="w-4 h-4" /></Button>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="flex flex-col gap-2 pt-2">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsAddingNewCombo(true)}
                disabled={isAddingNewCombo}
              >
                + Add New Combo
              </Button>
            </div>
            <div className="flex justify-end">
              <Button
                onClick={() => {
                  const selectedNames = userCombos
                    .filter((_, idx) => selectedCombos[idx])
                    .map(([name]) => name);
                  
                  // Get the standard load combinations for the current design method
                  const standardLoadComboMap = designMethod === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap;
                  const standardComboNames = Object.keys(standardLoadComboMap);
                  
                  // Only save custom combinations (ones not in the standard ASCE maps)
                  const customOnlyFactors = Object.fromEntries(
                    userCombos
                      .filter((_, idx) => selectedCombos[idx])
                      .filter(([name]) => !standardComboNames.includes(name))
                      .map(([name, factor]) => [name, factor])
                  );
                  
                  if (onSaveSelectedCombos) {
                    onSaveSelectedCombos(selectedNames, customOnlyFactors);
                  }
                  setIsOpen(false);
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow"
              >
                Save Selected Load Combos
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 