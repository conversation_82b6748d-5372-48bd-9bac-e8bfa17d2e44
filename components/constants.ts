/**
 * UI and Visual Constants
 */
export const UI_CONSTANTS = {
  // Spinner and loading constants
  SPINNER_SIZE: {
    SMALL: 20,
    MEDIUM: 40,
    LARGE: 60,
  },
  SPINNER_COLORS: {
    PRIMARY: '#eb804d',
    SECONDARY: '#e27d51',
    SUCCESS: '#70b445',
    ERROR: '#f07d52',
  },
  
  // Toast and notification durations (in milliseconds)
  TOAST_DURATION: {
    SHORT: 4000,
    MEDIUM: 7000,
    LONG: 10000,
  },
  
  // Animation and transition durations
  TRANSITION_DURATION: {
    FAST: 150,
    MEDIUM: 200,
    SLOW: 300,
  },
  
  // Common dimensions
  DIMENSIONS: {
    CARD_MIN_HEIGHT: 160,
    CHART_HEIGHT: 180,
    MODAL_MAX_WIDTH: '3xl',
    MODAL_HEIGHT: '70vh',
    MODAL_MAX_HEIGHT: '90vh',
    MODAL_MAX_WIDTH_PERCENT: '90vw',
  },
  
  // Common spacing and layout values
  SPACING: {
    GRID_COLS_2: 2,
    GRID_COLS_4: 4,
    BORDER_WIDTH: 1,
    BOR<PERSON>R_WIDTH_THICK: 2,
    PADDING_SMALL: 2,
    PADDING_MEDIUM: 4,
    PADDING_LARGE: 6,
  },
  
  // Color constants
  COLORS: {
    BACKGROUND_OVERLAY: 'rgba(0, 0, 0, 0.5)',
    BACKGROUND_MUTED: '#3b3364',
    SUCCESS: '#6fb945',
    ERROR: '#f07d52',
    WARNING: '#4a8cda',
    PENDING: '#2c2449',
    PURPLE: '#70b445',
    RED: '#f07d52',
  },
} as const;

/**
 * Form and Input Constants
 */
export const FORM_CONSTANTS = {
  // Input step values
  INPUT_STEPS: {
    DECIMAL: 0.1,
    INTEGER: 1,
    LARGE: 10,
  },
  
  // Input minimum values
  INPUT_MIN: {
    ZERO: 0,
    SMALL_POSITIVE: 0.1,
    ONE: 1,
  },
  
  // Decimal precision for formatting
  DECIMAL_PRECISION: {
    NONE: 0,
    TWO: 2,
    EXPONENTIAL: 2,
  },
  
  // Form validation thresholds
  VALIDATION: {
    MIN_VALUE_THRESHOLD: 0.001,
    MAX_SAFE_NUMBER: Number.MAX_VALUE,
  },
} as const;

/**
 * Analysis and Calculation Constants
 */
export const ANALYSIS_CONSTANTS = {
  // Default values for beam analysis
  DEFAULTS: {
    FLAT_USE_FACTOR: 1.0,
    REPETITIVE_MEMBER_FACTOR: 1.0,
    MANUAL_ADJUSTMENT_FACTOR: 1.0,
    MAX_STRESS_RATIO_LIMIT: 1.0,
    DEPENDENTS_COUNT: 0,
    WOOD_SPECIFIC_GRAVITY: 0.50,
  },
  
  // Steel constants
  STEEL: {
    MODULUS_OF_ELASTICITY_KSI: 29000,
    MODULUS_OF_ELASTICITY_PSI: 29000000, // 29,000 ksi * 1000
    PLF_TO_NM_CONVERSION: 14.5939, // 1 plf = 14.5939 N/m
  },
  
  // Wood density constants (used in self-weight calculations)
  WOOD_DENSITY: {
    IMPERIAL: 35, // pcf (pounds per cubic foot)
    METRIC: 560, // kg/m³
    CONVERSION_FACTOR: 9.80665, // m/s² for metric calculations
    AREA_CONVERSION_IMPERIAL: 144, // square inches to square feet
    AREA_CONVERSION_METRIC: 1000000, // square millimeters to square meters
  },
  
  // Load combination factors
  LOAD_COMBO: {
    DEFAULT_FACTOR: 1.0,
  },
  
  // Formatting thresholds
  FORMATTING: {
    SMALL_VALUE_THRESHOLD: 0.01,
    EXPONENTIAL_THRESHOLD: 0.001,
  },
} as const;

/**
 * String Constants and Labels
 */
export const STRING_CONSTANTS = {
  // Tab identifiers
  TABS: {
    PROPERTIES: 'properties',
    LOADS: 'loads',
    SUPPORTS: 'supports',
  },
  
  // Tab labels
  TAB_LABELS: {
    PROPERTIES: 'Properties',
    LOADS: 'Loads',
    SUPPORTS: 'Supports',
  },
  
  // Common labels and text
  LABELS: {
    UNNAMED_ANALYSIS: 'Unnamed Analysis',
    LOADING_ANALYSIS: 'Loading Analysis',
    RUNNING_ANALYSIS: 'Running Analysis',
    GENERATING_DIAGRAMS: 'Generating Diagrams',
    LOADING_DEPENDENCY_GRAPH: 'Loading dependency graph...',
    NO_DEPENDENCIES_FOUND: 'No dependencies found or graph data is empty.',
    ADD_LOADS_MESSAGE: 'Add Loads to see calculation.',
    SELF_WEIGHT_BEAM: 'Self Weight (Beam)',
    SAWN_LUMBER: 'Sawn Lumber',
    MANUAL_OVERRIDE: 'Manual override',
    ENTER_ELASTIC_MODULUS: 'Enter elastic modulus',
    MANUAL_OVERRIDE_EMIN: 'Manual override for Emin',
    SEARCH_SIZE_CLASSIFICATIONS: 'Search size classifications...',
  },
  
  // Error and status messages
  MESSAGES: {
    ERROR_PREFIX: 'Error: ',
    N_A: 'N/A',
    ERROR_STATUS: 'Error',
    PENDING_STATUS: 'PENDING',
    IN_PROGRESS_STATUS: 'IN_PROGRESS',
    SUCCESS_STATUS: 'SUCCESS',
    FAILED_STATUS: 'FAILED',
    PROCESSING_SUFFIX: ' (Processing...)',
    DONE_SUFFIX: ' (Done ✓)',
    FAILED_SUFFIX: ' (Failed X)',
  },
  
  // Load type descriptions
  LOAD_DESCRIPTIONS: {
    DEAD: 'Dead Load - Permanent loads such as self-weight and fixed equipment',
    LIVE: 'Live Load - Temporary loads from occupancy and movable equipment',
    SNOW: 'Snow Load - Load from snow accumulation',
    WIND: 'Wind Load - Lateral forces from wind pressure',
    EARTHQUAKE: 'Earthquake Load - Seismic forces',
    SOIL: 'Soil Load - Lateral earth pressure and soil weight',
    FLOOD: 'Flood Load - Hydrostatic and hydrodynamic forces',
    TEMPERATURE: 'Temperature Load - Forces from thermal expansion/contraction',
    ROOF_LIVE: 'Roof Live Load - Temporary loads on roofs from maintenance and construction',
    RAIN: 'Rain Load - Load from rain accumulation',
  },
  
  // Load type symbols
  LOAD_SYMBOLS: {
    DEAD: 'D',
    LIVE: 'L',
    SNOW: 'S',
    WIND: 'W',
    EARTHQUAKE: 'E',
    SOIL: 'H',
    FLOOD: 'F',
    TEMPERATURE: 'T',
    ROOF_LIVE: 'Lr',
    RAIN: 'R',
  },
  
  // Design method options
  DESIGN_METHODS: {
    ASD: 'ASD',
    LRFD: 'LRFD',
  },
  
  // Material types
  MATERIALS: {
    WOOD: 'wood',
    STEEL: 'steel',
    SAWN: 'sawn',
    GLULAM: 'glulam',
  },
  
  // Load group IDs
  LOAD_GROUP_IDS: {
    SELF_WEIGHT: 'self-weight-beam',
  },
} as const;

/**
 * Graph and Visualization Constants
 */
export const GRAPH_CONSTANTS = {
  // Node positioning
  NODE_POSITIONING: {
    LEVEL_SPACING: 300,
    VERTICAL_SPACING: 120,
    INITIAL_LEVEL: 0,
  },
  
  // Edge styling
  EDGE_STYLING: {
    MARKER_WIDTH: 20,
    MARKER_HEIGHT: 20,
  },
  
  // Node status colors
  NODE_COLORS: {
    IN_PROGRESS: '#4a8cda',
    SUCCESS: '#6fb945',
    FAILED: '#f07d52',
    DEFAULT: '#2c2449',
  },
  
  // Chart themes
  CHART_THEMES: {
    LIGHT: '',
    DARK: '.dark',
  },
} as const;

/**
 * API and Network Constants
 */
export const API_CONSTANTS = {
  // Cache control headers
  CACHE_HEADERS: {
    NO_CACHE: 'no-cache',
    PRAGMA_NO_CACHE: 'no-cache',
  },
  
  // HTTP methods
  HTTP_METHODS: {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE',
  },
  
  // Status codes
  STATUS_CODES: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
  },
} as const;

/**
 * CSS Class Name Constants
 */
export const CSS_CLASSES = {
  // Common utility classes
  FLEX: {
    CENTER: 'flex items-center justify-center',
    COLUMN: 'flex flex-col',
    SPACE_BETWEEN: 'flex justify-between items-center',
    SPACE_X_2: 'flex space-x-2',
    SPACE_Y_2: 'space-y-2',
    SPACE_Y_4: 'space-y-4',
  },
  
  // Text styles
  TEXT: {
    TRUNCATE: 'truncate',
    CENTER: 'text-center',
    MUTED: 'text-muted-foreground',
    ERROR: 'text-red-500',
    SUCCESS: 'text-green-500',
    WHITE: 'text-white',
    PRIMARY: 'text-primary',
    DESTRUCTIVE: 'text-destructive',
  },
  
  // Background styles
  BACKGROUND: {
    MUTED: 'bg-muted/30',
    ERROR: 'bg-red-50',
    SUCCESS: 'bg-green-50',
    WARNING: 'bg-yellow-50',
    INFO: 'bg-blue-50',
    DESTRUCTIVE: 'bg-destructive/10',
  },
  
  // Border styles
  BORDER: {
    DEFAULT: 'border',
    THICK: 'border-2',
    ROUNDED: 'rounded-md',
    ERROR: 'border-red-200',
    SUCCESS: 'border-green-200',
    WARNING: 'border-yellow-200',
    INFO: 'border-blue-200',
    DESTRUCTIVE: 'border-destructive',
  },
  
  // Sizing
  SIZE: {
    FULL: 'w-full',
    HALF: 'w-1/2',
    QUARTER: 'w-1/4',
    HEIGHT_FULL: 'h-full',
    MIN_HEIGHT: 'min-h-0',
  },
  
  // Positioning
  POSITION: {
    ABSOLUTE: 'absolute',
    RELATIVE: 'relative',
    FIXED: 'fixed',
    STICKY: 'sticky',
    TOP_0: 'top-0',
    BOTTOM_0: 'bottom-0',
    LEFT_0: 'left-0',
    RIGHT_0: 'right-0',
    INSET_0: 'inset-0',
    Z_10: 'z-10',
    Z_50: 'z-50',
  },
  
  // Responsive classes
  RESPONSIVE: {
    MD_COLS_2: 'md:grid-cols-2',
    MD_COL_SPAN_1: 'md:col-span-1',
    SM_TEXT: 'text-sm',
    XS_TEXT: 'text-xs',
    LG_TEXT: 'text-lg',
  },
  
  // Hover and transition effects
  EFFECTS: {
    HOVER_SHADOW: 'hover:shadow-lg',
    HOVER_UNDERLINE: 'hover:underline',
    TRANSITION_SHADOW: 'transition-shadow',
    TRANSITION_OPACITY: 'transition-opacity',
    OPACITY_0: 'opacity-0',
    OPACITY_100: 'opacity-100',
  },
} as const;

/**
 * Validation Constants
 */
export const VALIDATION_CONSTANTS = {
  // Field validation rules
  FIELD_VALIDATION: {
    REQUIRED_FIELDS: ['name', 'length', 'width', 'height'],
    MIN_LENGTH: 1,
    MAX_LENGTH: 255,
    MIN_NUMERIC_VALUE: 0,
    MAX_NUMERIC_VALUE: 999999,
  },
  
  // Error messages
  ERROR_MESSAGES: {
    REQUIRED: 'This field is required',
    MIN_VALUE: 'Value must be greater than 0',
    MAX_VALUE: 'Value exceeds maximum allowed',
    INVALID_FORMAT: 'Invalid format',
    PARSE_ERROR: 'Failed to parse data',
    NETWORK_ERROR: 'Network error occurred',
    UNEXPECTED_ERROR: 'An unexpected error occurred',
  },
} as const;

/**
 * Export all constants as a single object for convenience
 */
export const COMPONENT_CONSTANTS = {
  UI: UI_CONSTANTS,
  FORM: FORM_CONSTANTS,
  ANALYSIS: ANALYSIS_CONSTANTS,
  STRING: STRING_CONSTANTS,
  GRAPH: GRAPH_CONSTANTS,
  API: API_CONSTANTS,
  CSS: CSS_CLASSES,
  VALIDATION: VALIDATION_CONSTANTS,
} as const; 