'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { BeamVisualization } from '@/components/beam-analysis/beam-visualization';
import { CompactBeamSummary } from './CompactBeamSummary';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UnitSystem } from '@/lib/types/units/unit-system';
import type { BeamPropertiesState, BeamData, Support } from '@/lib/types/beam/beam-data';
import type { SummaryData } from '@/lib/types/analysis/analysis-output';
import { getUnitsBySystem } from '@/lib/types/units/unit-enum';
import { Trash2, ExternalLink, AlertTriangle, GripVertical, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import { DependentsModal } from './DependentsModal';
import { LoadGroup } from '@/lib/types/load/load-group';
import { Load } from '@/lib/types/load/load';
import { Support as LoadSupport } from '@/lib/types/support/support';
import { Type as LoadApplicationType } from '@/lib/types/load/load-type';
import { SupportType } from '@/lib/types/support/support-type';

// This interface should match the structure of beamAnalysisResults from your API
interface BeamAnalysisResultForCard {
  id: string;
  name?: string | null;
  length: number; // Physical length of the beam
  modulusOfElasticity: number;
  momentOfInertia: number;
  beamProperties: string; // JSON string of BeamPropertiesState
  loads: string;          // JSON string of LoadGroup[]
  supports: string;       // JSON string of Support[]
  results: string | null; // JSON string of SummaryData
  createdAt: string;
  updatedAt: string; // Added from ProjectDetailPage definition
  calculationId: string; // Changed to non-optional
  dependentsCount?: number; // Added for dependency tracking
  originalUnitSystem?: UnitSystem | string; // Added
  order?: number | null; // Added
  bendingStressRatio?: number | null; // Added
  shearStressRatio?: number | null;   // Added
  isReanalyzing?: boolean; // Added
  // Add other fields like bendingStressRatio, shearStressRatio, maxDeflection if needed directly
}

interface BeamAnalysisResultCardProps {
  analysisResult: BeamAnalysisResultForCard;
  unitSystem: UnitSystem;
  onAnalysisDeleted?: (analysisId: string) => void;
  dragHandleProps?: DraggableProvidedDragHandleProps | null;
  orgId?: string | null;
  projectId?: string | null;
  currentFolderId?: string | null;
  onInitiateDelete?: (analysisResult: BeamAnalysisResultForCard) => Promise<void>;
}

// Helper to get beam size (width x depth)
const getBeamSize = (propertiesState: BeamPropertiesState | null): string => {
  if (!propertiesState) return "N/A";
  
  // Check if this is manual mode first
  if (propertiesState.isManualMode) {
    if (propertiesState.manualWidth && propertiesState.manualDepth) {
      return `${propertiesState.manualWidth}" x ${propertiesState.manualDepth}"`;
    }
    return "N/A";
  }

  // Check for glulam properties
  if (propertiesState.lumberType === 'glulam' && propertiesState.selectedGluLamProperties) {
    const width = propertiesState.selectedGluLamProperties.width;
    const depth = propertiesState.selectedGluLamProperties.depth;
    if (width && depth) {
      return `${width}" x ${depth}"`;
    }
  }

  // Check for sawn lumber properties
  if (propertiesState.lumberType === 'sawn' && propertiesState.lumberProperties) {
    // Try standard dressed dimensions first
    if (propertiesState.lumberProperties.standard_width && propertiesState.lumberProperties.standard_depth) {
      return `${propertiesState.lumberProperties.standard_width}" x ${propertiesState.lumberProperties.standard_depth}"`;
    }
  }

  // Generic fallback for any width/depth properties
  if (propertiesState.manualWidth && propertiesState.manualDepth) {
    return `${propertiesState.manualWidth}" x ${propertiesState.manualDepth}"`;
  }

  return "N/A";
};

// Helper to get material info in two parts for better formatting
const getMaterialInfo = (propertiesState: BeamPropertiesState | null): { type: string; details: string } => {
  if (!propertiesState) return { type: "N/A", details: "" };
  
  // Check if this is manual mode first
  if (propertiesState.isManualMode) {
    const materialType = propertiesState.manual_material_type || "Manual Input";
    const species = propertiesState.manual_species || "Custom";
    const grade = propertiesState.manual_grade || "N/A";
    return { type: materialType, details: `${species} ${grade}` };
  }
  
  if (propertiesState.lumberType === 'sawn') {
    const species = propertiesState.selectedSpeciesCombination || propertiesState.selectedSpecies || "Unknown Species";
    const grade = propertiesState.selectedGrade || "Unknown Grade";
    return { type: "Sawn Lumber", details: `${grade} ${species}` };
  } else if (propertiesState.lumberType === 'glulam') {
    const combination = propertiesState.selectedSpeciesCombination || "Unknown Combination";
    return { type: "Glulam", details: combination };
  }
  return { type: "Material N/A", details: "" };
};

// Helper to get a concise material description
const getMaterialDescription = (propertiesState: BeamPropertiesState | null): string => {
  if (!propertiesState) return "N/A";
  
  // Check if this is manual mode first
  if (propertiesState.isManualMode) {
    const materialType = propertiesState.manual_material_type || "Manual Input";
    const species = propertiesState.manual_species || "Custom";
    const grade = propertiesState.manual_grade || "N/A";
    return `${materialType}: ${species} ${grade}`;
  }
  
  if (propertiesState.lumberType === 'sawn') {
    const species = propertiesState.selectedSpeciesCombination || propertiesState.selectedSpecies || "Unknown Species";
    const grade = propertiesState.selectedGrade || "Unknown Grade";
    return `Sawn: ${species} - ${grade}`;
  } else if (propertiesState.lumberType === 'glulam') {
    const combination = propertiesState.selectedSpeciesCombination || "Unknown Combination";
    // Add more glulam details if needed, e.g., species
    return `Glulam: ${combination}`;
  }
  return "Material N/A";
};

export function BeamAnalysisResultCard({ analysisResult, unitSystem, onAnalysisDeleted, dragHandleProps, orgId, projectId, currentFolderId, onInitiateDelete }: BeamAnalysisResultCardProps) {
  console.log("[BeamAnalysisResultCard] Props:", { analysisResult, orgId, projectId, currentFolderId });

  const [parsedBeamData, setParsedBeamData] = useState<BeamData | null>(null);
  const [parsedBeamPropertiesState, setParsedBeamPropertiesState] = useState<BeamPropertiesState | null>(null);
  const [parsedSummaryData, setParsedSummaryData] = useState<SummaryData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDependentsModalOpen, setIsDependentsModalOpen] = useState(false);
  const units = getUnitsBySystem(unitSystem);

  const detailPageUrl = orgId && projectId 
    ? `/organizations/${orgId}/projects/${projectId}/calculations/${analysisResult.calculationId}${currentFolderId ? `?folderId=${currentFolderId}` : ''}` 
    : `/some/fallback/path/${analysisResult.calculationId}`;

  useEffect(() => {
    try {
      setError(null);
      const propertiesState = JSON.parse(analysisResult.beamProperties) as BeamPropertiesState;
      const plainLoadGroups: any[] = JSON.parse(analysisResult.loads);
      const plainSupports: any[] = JSON.parse(analysisResult.supports);
      const summary = analysisResult.results ? JSON.parse(analysisResult.results) as SummaryData : null;

      setParsedBeamPropertiesState(propertiesState);
      setParsedSummaryData(summary);
      
      let area = 0;
      if (propertiesState) {
        if (propertiesState.manual_Area) {
          area = propertiesState.manual_Area;
        } else if (propertiesState.lumberType === 'sawn' && propertiesState.lumberProperties?.A) {
          area = propertiesState.lumberProperties.A;
        } else if (propertiesState.lumberType === 'glulam' && propertiesState.selectedGluLamProperties?.sectionProperties?.area) {
          area = propertiesState.selectedGluLamProperties.sectionProperties.area;
        }
      }

      const instantiatedLoadGroups = plainLoadGroups.map(lgPlain => {
        // Instantiate internal Load objects first
        const instantiatedInternalLoads = (lgPlain.loads || []).map((loadPlain: any) => new Load({
          ...loadPlain, // Spread plain object properties. Assumes Load constructor can take this shape.
        }));

        let groupPosition = { start: parseFloat(lgPlain.startPosition), end: parseFloat(lgPlain.endPosition) };
        if (lgPlain.isLinkedLoad && instantiatedInternalLoads.length > 0 && instantiatedInternalLoads[0].startPosition !== undefined) {
          const linkedLoadPos = parseFloat(String(instantiatedInternalLoads[0].startPosition));
          groupPosition = { start: linkedLoadPos, end: linkedLoadPos }; // For point linked load, start=end
        }
        
        // Improved enum parsing logic to handle both string and enum values
        let groupTypeEnum: LoadApplicationType;
        if (lgPlain.isSelfWeight) {
          // Self-weight loads should always be distributed
          groupTypeEnum = LoadApplicationType.DISTRIBUTED;
        } else {
          // Try to parse the type enum - handle both string values and enum values
          if (typeof lgPlain.type === 'string') {
            // Convert string to uppercase and match with enum keys
            const typeKey = lgPlain.type.toUpperCase() as keyof typeof LoadApplicationType;
            groupTypeEnum = LoadApplicationType[typeKey] || LoadApplicationType.POINT;
          } else {
            // If it's already an enum value or object, try direct lookup
            groupTypeEnum = LoadApplicationType[lgPlain.type as keyof typeof LoadApplicationType] || LoadApplicationType.POINT;
          }
        }
        
        // Instantiate LoadGroup using its defined constructor signature
        const newLoadGroup = new LoadGroup(
          lgPlain.id,
          lgPlain.label,
          groupPosition,
          lgPlain.startMagnitudes || {}, // Magnitudes used by constructor to create initial loads
          lgPlain.endMagnitudes || {},   // if lgPlain.loads is not used directly after.
          groupTypeEnum,
          lgPlain.tributaryWidth,      // Optional from here
          lgPlain.isEditable,
          lgPlain.isSelfWeight,
          lgPlain.isLinkedLoad,
          lgPlain.linkedSourceCalculationId,
          lgPlain.linkedSourceSupportPosition
        );

        // If lgPlain had a 'loads' array, it means we are reconstructing.
        // The LoadGroup constructor creates its own 'loads' based on magnitudes.
        // So, if we have pre-existing load objects (now instantiated), overwrite the constructor-generated ones.
        if (lgPlain.loads && lgPlain.loads.length > 0) {
          newLoadGroup.loads = instantiatedInternalLoads;
        }
        
        return newLoadGroup;
      });

      console.log("[BeamAnalysisResultCard] plainSupports before instantiation:", JSON.stringify(plainSupports, null, 2)); // Log plainSupports

      const instantiatedSupports = plainSupports.map(supportPlain => {
        const typeString = String(supportPlain.type);
        const supportTypeEnum: SupportType = SupportType[typeString as keyof typeof SupportType] || SupportType.PIN;
        return new LoadSupport(supportTypeEnum, parseFloat(supportPlain.position));
      });

      setParsedBeamData({
        properties: {
          length: analysisResult.length,
          elasticModulus: analysisResult.modulusOfElasticity,
          momentOfInertia: analysisResult.momentOfInertia,
          area: area,
        },
        loadGroups: instantiatedLoadGroups,
        supports: instantiatedSupports,
      });

    } catch (e) {
      console.error("Error parsing analysis result data for card:", e);
      setError("Failed to parse analysis data. It may be corrupted or in an old format.");
      setParsedBeamData(null);
      setParsedBeamPropertiesState(null);
      setParsedSummaryData(null);
    }
  }, [analysisResult]);

  if (error) {
    return (
      <Card className="w-full mb-6 break-inside-avoid overflow-hidden border-destructive">
        <CardHeader>
          <CardTitle className="text-lg text-destructive flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" /> 
            {analysisResult.name || 'Unnamed Analysis'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-destructive-foreground bg-destructive/10 p-3 rounded-md">Error: {error}</p>
           <div className="mt-4 flex justify-end space-x-2">
            <Button variant="outline" size="sm" onClick={() => setError(null)}>Dismiss</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!parsedBeamData || !parsedBeamPropertiesState) {
    return (
      <Card className="w-full mb-6 break-inside-avoid overflow-hidden">
        <CardHeader>
          <CardTitle className="text-lg">{analysisResult.name || 'Unnamed Analysis'}</CardTitle>
        </CardHeader>
        <CardContent><p>Loading analysis data...</p></CardContent>
      </Card>
    );
  }

  const materialDesc = getMaterialDescription(parsedBeamPropertiesState);
  const materialInfo = getMaterialInfo(parsedBeamPropertiesState);

  return (
    <Card className="group relative w-full mb-4 break-inside-avoid overflow-visible shadow-md hover:shadow-lg transition-shadow duration-200 flex flex-col">
      {dragHandleProps && (
        <div
          {...dragHandleProps}
          className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2  p-1 bg-gray-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-150 cursor-grab z-10"
          style={{ touchAction: 'none' }} // Important for touch devices
        >
          <GripVertical size={20} className="text-gray-600" />
        </div>
      )}
      {/* Main content area with two columns */}
      <div className="grid grid-cols-1 md:grid-cols-2 flex-grow">
        
        {/* Left Column: Visualization and Basic Info (takes 1/2 on md screens) */}
        <div className="md:col-span-1 p-2 border-r border-gray-200 flex flex-col bg-gray-50/30">
          {/* Analysis Name and Saved Date */}
          <div className="text-center mb-1">
            <h3 className="text-base font-semibold text-primary truncate" title={analysisResult.name || 'Unnamed Analysis'}>
              <Link href={detailPageUrl} className="hover:underline">
                {analysisResult.name || 'Unnamed Analysis'}
              </Link>
            </h3>
            <p className="text-xs text-muted-foreground">
              Saved: {new Date(analysisResult.createdAt).toLocaleDateString()}
            </p>
          </div>

          {/* Beam Visualization - takes available space */}
          <div className="flex-grow mb-1" style={{ minHeight: '160px' }}> {/* Reduced from 200px */}
            <BeamVisualization 
              beamData={parsedBeamData} 
              unitSystem={unitSystem} 
              beamPropertiesState={parsedBeamPropertiesState} 
            />
          </div>

          {/* Length and Material Info */}
          <div className="text-xs space-y-0.5">
            <p><span className="font-semibold">Length:</span> {analysisResult.length.toFixed(2)} {units.length}</p>
            <p><span className="font-semibold">Size:</span> <span className="truncate" title={getBeamSize(parsedBeamPropertiesState)}>{getBeamSize(parsedBeamPropertiesState)}</span></p>
            <p><span className="font-semibold">Material:</span> <span className="truncate" title={`${materialInfo.type}${materialInfo.details ? ' - ' + materialInfo.details : ''}`}>{materialInfo.type}{materialInfo.details ? ` - ${materialInfo.details}` : ''}</span></p>
          </div>
        </div>

        {/* Right Column: Compact Summary and Actions (takes 1/2 on md screens) */}
        <div className="md:col-span-1 p-2 flex flex-col">{/* Reduced padding from p-3 to p-2 */}
          {parsedSummaryData ? ( // Restore CompactBeamSummary
            <div className="flex-grow">
              <CompactBeamSummary 
                summaryData={parsedSummaryData} 
                unitSystem={unitSystem} 
                beamPropertiesState={parsedBeamPropertiesState}
                beamLength={parsedBeamData.properties.length}
              />
            </div>
          ) : ( // Keep the fallback for no summary data
            <div className="flex-grow flex items-center justify-center">
              <p className="text-sm text-muted-foreground">No summary results available.</p>
            </div>
          )}
          
          {/* Log props right before Link container div */}
          {(() => {
            console.log(`[BeamAnalysisResultCard] Props for Link - Analysis ID: ${analysisResult.id}, Calculation ID: '${analysisResult.calculationId}', Org ID: '${orgId}', Project ID: '${projectId}'`);
            return null; // JSX requires a returned value, even if null
          })()}
          {/* Action Buttons - aligned to bottom of this column */}
          <div className="mt-auto pt-2 flex space-x-2">{/* Reduced pt-3 to pt-2 */}
            {error && (
              <span className="text-xs text-red-500 flex items-center mr-auto">
                <AlertTriangle className="h-4 w-4 mr-1" /> {error}
              </span>
            )}
            {analysisResult.dependentsCount && analysisResult.dependentsCount > 0 && (
              <Button 
                variant="outline"
                size="sm" 
                className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
                onClick={() => setIsDependentsModalOpen(true)}
              >
                <Users size={16} className="mr-1.5" /> 
                View Dependencies ({analysisResult.dependentsCount})
              </Button>
            )}
            <Button 
              variant="outline" 
              size="sm" 
              asChild 
              className="flex-1" 
              style={{ backgroundColor: '#70b445', color: 'white', borderColor: '#70b445' }}
            >
              <Link href={detailPageUrl}>
                <ExternalLink className="h-4 w-4 mr-1.5" />
                Open Calculation
              </Link>
            </Button>
            {onInitiateDelete && (
              <Button 
                variant="destructive" 
                size="sm" 
                className="flex-1 bg-red-500 hover:bg-red-600 text-white"
                onClick={() => onInitiateDelete(analysisResult)}
              >
                <Trash2 className="h-4 w-4 mr-1.5" /> Delete
              </Button>
            )}
          </div>
        </div>
      </div>
      {/* Footer removed as buttons are now in the right column */}
      {analysisResult.calculationId && (
        <DependentsModal 
          isOpen={isDependentsModalOpen}
          onClose={() => setIsDependentsModalOpen(false)}
          calculationId={analysisResult.calculationId}
          calculationName={analysisResult.name}
          orgId={orgId}
          projectId={projectId}
        />
      )}
    </Card>
  );
} 