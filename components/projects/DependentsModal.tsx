import { useEffect, useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import ReactFlow, { Node, Edge, Position, MiniMap, Controls, Background, isNode, MarkerType } from 'react-flow-renderer';
import { GRAPH_CONSTANTS, STRING_CONSTANTS } from "@/components/constants";
// Styles for react-flow-renderer (ensure this path is correct for your setup or if you handle styles globally)
// import 'react-flow-renderer/dist/style.css'; 
// import 'react-flow-renderer/dist/theme-default.css';

// Simplified Elements type for react-flow-renderer v10
type FlowElements = (Node | Edge)[];

interface ApiCalculationNode {
  id: string;
  name: string;
  type: string | null;
}

interface ApiGraphEdge {
  source: string;
  target: string;
}

interface ApiDependencyGraphData {
  nodes: ApiCalculationNode[];
  edges: ApiGraphEdge[];
}

interface FormattedGraphData {
    nodes: Node[];
    edges: Edge[];
}

interface DependentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  calculationId: string;
  calculationName?: string | null;
  orgId?: string | null; 
  projectId?: string | null; 
  showUpdateButton?: boolean;
  onConfirmUpdate?: () => Promise<void>;
  isDeleteConfirmation?: boolean;
  onConfirmDelete?: (childrenToReanalyze: string[]) => Promise<void>;
  isProcessingDelete?: boolean;
  directChildrenToReanalyze?: string[];
  onReanalysisComplete?: () => void;
  deleteFunction?: () => Promise<void>;
}

// Enum for update statuses
enum NodeUpdateStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

const transformApiData = (apiData: ApiDependencyGraphData | null, currentCalcId: string): FormattedGraphData => {
  if (!apiData || !apiData.nodes || !apiData.edges) return { nodes: [], edges: [] };

  const nodes: Node[] = [];
  const edges: Edge[] = [];
  const levels: { [key: string]: number } = {};
  const bfsQueue: Array<{ id: string; level: number }> = [{ id: currentCalcId, level: 0 }];
  const visitedInLayout: Set<string> = new Set();
  const positionsInLevel: { [level: number]: number } = {};

  while(bfsQueue.length > 0) {
    const { id, level } = bfsQueue.shift()!;
    if (visitedInLayout.has(id)) continue;
    visitedInLayout.add(id);
    levels[id] = level;
    apiData.edges.filter(edge => edge.source === id).forEach(edge => {
        if (!visitedInLayout.has(edge.target)) {
            bfsQueue.push({ id: edge.target, level: level + 1});
        }
    });
  }

  apiData.nodes.forEach((node) => {
    const level = levels[node.id] !== undefined ? levels[node.id] : GRAPH_CONSTANTS.NODE_POSITIONING.INITIAL_LEVEL;
    const xPos = level * GRAPH_CONSTANTS.NODE_POSITIONING.LEVEL_SPACING;
    const yPos = (positionsInLevel[level] || 0) * GRAPH_CONSTANTS.NODE_POSITIONING.VERTICAL_SPACING;
    positionsInLevel[level] = (positionsInLevel[level] || 0) + 1;

    nodes.push({
      id: node.id,
      type: 'default', 
      data: { label: `${node.name} (${node.type || 'N/A'})` },
      position: { x: xPos, y: yPos },
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
      className: node.id === currentCalcId ? 'border-2 border-blue-500 shadow-lg' : ''
    });
  });

  apiData.edges.forEach((edge, index) => {
    edges.push({
      id: `edge-${edge.source}-${edge.target}-${index}`,
      source: edge.source,
      target: edge.target,
      animated: true,
      markerEnd: { 
        type: MarkerType.ArrowClosed,
        width: GRAPH_CONSTANTS.EDGE_STYLING.MARKER_WIDTH,
        height: GRAPH_CONSTANTS.EDGE_STYLING.MARKER_HEIGHT,
      },
    });
  });

  return { nodes, edges };
};

export function DependentsModal({ 
  isOpen, 
  onClose, 
  calculationId, 
  calculationName, 
  orgId, 
  projectId,
  showUpdateButton,
  onConfirmUpdate,
  isDeleteConfirmation,
  onConfirmDelete,
  isProcessingDelete,
  directChildrenToReanalyze,
  onReanalysisComplete,
  deleteFunction
}: DependentsModalProps) {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updateStatuses, setUpdateStatuses] = useState<Record<string, NodeUpdateStatus>>({});
  const [reanalysisStatuses, setReanalysisStatuses] = useState<Record<string, NodeUpdateStatus>>({});
  const [isUpdatingAll, setIsUpdatingAll] = useState(false);
  const [isReanalyzingDependents, setIsReanalyzingDependents] = useState(false);

  // Function to fetch dependencies
  const fetchDependencies = useCallback(() => {
    if (isOpen && calculationId) {
      setIsLoading(true);
      setError(null);
      setNodes([]);
      setEdges([]);
      setUpdateStatuses({});
      setReanalysisStatuses({});
      setIsReanalyzingDependents(false);
      fetch(`/api/calculations/${calculationId}/dependents`) 
        .then(res => {
          if (!res.ok) {
            throw new Error('Failed to fetch dependency graph');
          }
          return res.json();
        })
        .then((data: ApiDependencyGraphData) => {
          if (data && Array.isArray(data.nodes) && Array.isArray(data.edges)) {
            const { nodes: transformedNodes, edges: transformedEdges } = transformApiData(data, calculationId);
            setNodes(transformedNodes);
            setEdges(transformedEdges);
            // Initialize PENDING status for all nodes except the current one if we are in update mode
            if (showUpdateButton) {
              const initialStatuses: Record<string, NodeUpdateStatus> = {};
              transformedNodes.forEach(node => {
                if (node.id !== calculationId) { // Current calculation is already being saved by parent
                  initialStatuses[node.id] = NodeUpdateStatus.PENDING;
                }
              });
              setUpdateStatuses(initialStatuses);
            }
          } else {
            console.warn("Dependency graph data is not in the expected format:", data);
          }
          setIsLoading(false);
        })
        .catch(err => {
          console.error("Error fetching dependency graph:", err);
          setError(err.message);
          setIsLoading(false);
        });
    }
  }, [isOpen, calculationId, showUpdateButton]);

  useEffect(() => {
    fetchDependencies();
    if (isOpen && isDeleteConfirmation && directChildrenToReanalyze && directChildrenToReanalyze.length > 0) {
      const initialStatuses: Record<string, NodeUpdateStatus> = {};
      directChildrenToReanalyze.forEach(childId => {
        initialStatuses[childId] = NodeUpdateStatus.PENDING;
      });
      setReanalysisStatuses(initialStatuses);
    }
  }, [fetchDependencies, isOpen, isDeleteConfirmation, directChildrenToReanalyze]);

  const onLoad = useCallback((reactFlowInstance: any) => {
    if (nodes && nodes.length > 0) {
        reactFlowInstance.fitView();
    }
  }, [nodes]);

  const getNodeStatusStyle = (nodeId: string): React.CSSProperties => {
    const status = updateStatuses[nodeId] || reanalysisStatuses[nodeId];
    switch (status) {
      case NodeUpdateStatus.IN_PROGRESS:
        return { 
          backgroundColor: GRAPH_CONSTANTS.NODE_COLORS.IN_PROGRESS, 
          color: 'white',
          border: 'none'
        };
      case NodeUpdateStatus.SUCCESS:
        return { 
          backgroundColor: GRAPH_CONSTANTS.NODE_COLORS.SUCCESS, 
          color: 'white',
          border: 'none'
        };
      case NodeUpdateStatus.FAILED:
        return { 
          backgroundColor: GRAPH_CONSTANTS.NODE_COLORS.FAILED, 
          color: 'white',
          border: 'none'
        };
      default: // PENDING or no status
        return { 
          backgroundColor: GRAPH_CONSTANTS.NODE_COLORS.DEFAULT, 
          color: 'white',
          border: nodeId === calculationId ? '2px solid white' : '1px solid white'
        };
    }
  };
  
  const nodesWithStatus = nodes.map(node => ({
    ...node,
    style: { ...node.style, ...getNodeStatusStyle(node.id) },
    data: {
      ...node.data,
      label: (
        <>
          {node.data.label}
          {(updateStatuses[node.id] === NodeUpdateStatus.IN_PROGRESS || reanalysisStatuses[node.id] === NodeUpdateStatus.IN_PROGRESS) && " (Processing...)"}
          {(updateStatuses[node.id] === NodeUpdateStatus.SUCCESS || reanalysisStatuses[node.id] === NodeUpdateStatus.SUCCESS) && " (Done ✓)"}
          {(updateStatuses[node.id] === NodeUpdateStatus.FAILED || reanalysisStatuses[node.id] === NodeUpdateStatus.FAILED) && " (Failed X)"}
        </>
      )
    }
  }));

  // This is the function that will perform the cascading update
  const handleConfirmAndUpdateAll = async () => {
    if (!onConfirmUpdate) return;

    setIsUpdatingAll(true);
    try {
      // First, call the onConfirmUpdate passed from parent (beam-analysis.tsx)
      // This will handle saving the *current* beam.
      await onConfirmUpdate(); 
      
      // Now, proceed with updating dependents
      // The `nodes` state here should be the full list from the graph
      // Filter out the current calculationId as it's already handled by onConfirmUpdate
      const dependentNodes = nodes.filter(node => node.id !== calculationId);

      for (const node of dependentNodes) {
        setUpdateStatuses(prev => ({ ...prev, [node.id]: NodeUpdateStatus.IN_PROGRESS }));
        try {
          const res = await fetch(`/api/calculations/${node.id}/trigger-reanalysis`, {
            method: 'POST',
          });
          if (!res.ok) {
            const errorData = await res.json().catch(() => ({})); // Catch if res.json() fails
            throw new Error(errorData.error || `Failed to trigger re-analysis for ${node.data.label} (status: ${res.status})`);
          }
          setUpdateStatuses(prev => ({ ...prev, [node.id]: NodeUpdateStatus.SUCCESS }));
        } catch (error) {
          console.error(`Error updating node ${node.id}:`, error);
          setUpdateStatuses(prev => ({ ...prev, [node.id]: NodeUpdateStatus.FAILED }));
          // Optionally, decide if you want to stop on first error or continue
        }
      }
      // Maybe a success toast or message here
    } catch (error) {
      console.error("Error during the update process (parent save or setup):", error);
      // Handle error from the parent's onConfirmUpdate if necessary
      // Maybe a general error toast
    } finally {
      setIsUpdatingAll(false);
      // Do not close modal automatically, let user see statuses.
      // onClose(); // Or maybe keep it open to show statuses
    }
  };

  const handleConfirmAction = async () => {
    if (isDeleteConfirmation) {
      // For deletion, we first reanalyze dependents, then delete
      if (directChildrenToReanalyze && directChildrenToReanalyze.length > 0) {
        setIsReanalyzingDependents(true);
        
        const childReanalysisPromises = directChildrenToReanalyze.map(async (childId) => {
          setReanalysisStatuses(prev => ({ ...prev, [childId]: NodeUpdateStatus.IN_PROGRESS }));
          try {
            const res = await fetch(`/api/calculations/${childId}/trigger-reanalysis`, {
              method: 'POST',
            });
            if (!res.ok) {
              const errorData = await res.json().catch(() => ({}));
              throw new Error(errorData.error || `Re-analysis for ${childId.substring(0,8)} failed (status: ${res.status})`);
            }
            setReanalysisStatuses(prev => ({ ...prev, [childId]: NodeUpdateStatus.SUCCESS }));
          } catch (error: any) {
            console.error(`Error re-analyzing child ${childId}:`, error);
            setReanalysisStatuses(prev => ({ ...prev, [childId]: NodeUpdateStatus.FAILED }));
          }
        });

        await Promise.all(childReanalysisPromises);
        setIsReanalyzingDependents(false);
      }
      
      // Now perform the actual deletion - either through deleteFunction or onConfirmDelete
      if (deleteFunction) {
        await deleteFunction();
      } else if (onConfirmDelete) {
        await onConfirmDelete(directChildrenToReanalyze || []);
      }
      
      // Call the completion handler
      if (onReanalysisComplete) {
        onReanalysisComplete();
      }
    } else if (showUpdateButton && onConfirmUpdate) {
      await handleConfirmAndUpdateAll();
    }
  };

  const canCloseModal = !isProcessingDelete && !isReanalyzingDependents && !isUpdatingAll;
  const confirmButtonText = () => {
    if (isDeleteConfirmation) {
      if (isReanalyzingDependents) return "Re-analyzing Dependents...";
      if (isProcessingDelete) return "Deleting...";
      return "Re-analyze Dependents & Delete";
    }
    if (showUpdateButton) {
      if (isUpdatingAll) return "Updating All...";
      return "Update & Cascade";
    }
    return "Confirm";
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => { if (!open && canCloseModal) onClose(); }}>
      <DialogContent className="max-w-3xl h-[70vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {isDeleteConfirmation 
              ? `Confirm Deletion: ${calculationName || 'Selected Calculation'}` 
              : `Update Dependencies: ${calculationName || 'Selected Calculation'}`}
          </DialogTitle>
          <DialogDescription>
            {isDeleteConfirmation 
              ? "This calculation has downstream dependencies. Deleting it may affect other parts of your project. Review the graph below. Are you sure you want to proceed?"
              : "This calculation has been updated. The following downstream calculations may need to be re-analyzed. Review the graph and confirm to trigger updates."}
          </DialogDescription>
        </DialogHeader>
        <div className="flex-grow overflow-auto border rounded-md my-4 relative">
          {isLoading && <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10"><p>Loading dependency graph...</p></div>}
          {error && <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10"><p className="text-destructive">Error: {error}</p></div>}
          {!isLoading && !error && nodes.length === 0 && <div className="absolute inset-0 flex items-center justify-center"><p>No dependencies found or graph data is empty.</p></div>}
          
          {nodes.length > 0 && (
             <ReactFlow
                nodes={nodesWithStatus}
                edges={edges}
                onLoad={onLoad}
                fitView
                nodesDraggable={false}
                nodesConnectable={false}
                className="bg-muted/30"
              >
                <Controls />
                <Background />
              </ReactFlow>
          )}
        </div>
        <DialogFooter className="mt-auto pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={!canCloseModal}>
            {isDeleteConfirmation && (isProcessingDelete || isReanalyzingDependents) ? "Close (Pending Actions)" : "Close"}
          </Button>
          {(showUpdateButton || isDeleteConfirmation) && (
            <Button 
              onClick={handleConfirmAction} 
              disabled={isProcessingDelete || isReanalyzingDependents || isUpdatingAll}
              variant={isDeleteConfirmation ? "destructive" : "default"}
            >
              {confirmButtonText()}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 