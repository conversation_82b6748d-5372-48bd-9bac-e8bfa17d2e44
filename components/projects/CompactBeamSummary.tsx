'use client';

import React from 'react';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { getUnitsBySystem, Units } from '@/lib/types/units/unit-enum';
import { SummaryData } from '@/lib/types/analysis/analysis-output';
import type { BeamPropertiesState } from '@/lib/types/beam/beam-data';
import { cn } from '@/lib/utils';
import { CheckCircle, XCircle, ArrowDown, ArrowUp } from 'lucide-react';

interface CompactBeamSummaryProps {
  summaryData: SummaryData | null;
  unitSystem: UnitSystem;
  beamPropertiesState: BeamPropertiesState | null; 
  beamLength: number;
}

const getStatusStyle = (ratio: number, limit: number) => {
  if (isNaN(ratio) || !isFinite(ratio)) return "text-gray-500";
  return ratio <= limit ? "text-[#6db145]" : "text-[#eb8052]";
};

const getStatusIcon = (ratio: number, limit: number) => {
  if (isNaN(ratio) || !isFinite(ratio)) return null;
  return ratio <= limit ? <CheckCircle className="inline h-4 w-4 ml-1" /> : <XCircle className="inline h-4 w-4 ml-1" />;
};

const formatDeflectionRatio = (deflectionValue: number, beamLength: number, limitDenominator: number | undefined): string => {
  if (isNaN(deflectionValue) || !isFinite(deflectionValue) || beamLength === 0 || deflectionValue === 0) return "N/A";
  const actualRatio = beamLength / Math.abs(deflectionValue);
  let limitStr = "N/A";
  if (limitDenominator && isFinite(limitDenominator) && limitDenominator !== 0) {
    limitStr = `L/${limitDenominator}`;
  }
  return `L/${actualRatio.toFixed(0)} (Limit ${limitStr})`;
};

export function CompactBeamSummary({ summaryData, unitSystem, beamPropertiesState, beamLength }: CompactBeamSummaryProps) {
  const units: Units = getUnitsBySystem(unitSystem);
  const effectiveStressRatioLimit = beamPropertiesState?.manual_maxStressRatioLimit ?? 1.0;

  if (!summaryData) {
    return <p className="text-sm text-muted-foreground text-center py-4">No summary data available.</p>;
  }

  const totalDeflectionLimitDenom = beamPropertiesState?.manual_totalDeflectionLimit ?? undefined;

  const reactionsToDisplay = summaryData.supportReactions?.forces 
    ? Object.entries(summaryData.supportReactions.forces).map(([nodeLabel, reactionData]) => ({
        label: nodeLabel, // Use the nodeLabel directly (e.g., "R1", "Support A")
        value: reactionData.maxUp, 
        loadCombo: reactionData.maxUpCombo,
      }))
    : [];

  return (
    <div className="space-y-4 text-sm"> 
      <h4 className="text-md font-semibold text-center mb-3">Calculation Summary</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3"> 
        {/* Bending Stress */}
        <div>
          <p className="font-medium">Bending Stress Ratio:</p>
          {summaryData.maxBendingStressRatio ? (
            <>
              <p className={cn("font-bold", getStatusStyle(summaryData.maxBendingStressRatio.ratio, effectiveStressRatioLimit))}>
                {summaryData.maxBendingStressRatio.ratio.toFixed(3)}
                {getStatusIcon(summaryData.maxBendingStressRatio.ratio, effectiveStressRatioLimit)}
              </p>
              <p className="text-xs text-muted-foreground">
                Actual: {summaryData.maxBendingStressRatio.actualStress.toFixed(2)} {units.pressure}<br/>
                Allowable: {summaryData.maxBendingStressRatio.allowableStress.toFixed(2)} {units.pressure}<br/>
                Load Combo: {summaryData.maxBendingStressRatio.loadComboName}
              </p>
            </>
          ) : (
            <p className="text-sm text-muted-foreground">N/A</p>
          )}
        </div>

        {/* Shear Stress */}
        <div>
          <p className="font-medium">Shear Stress Ratio:</p>
          {summaryData.maxShearStressRatio ? (
            <>
              <p className={cn("font-bold", getStatusStyle(summaryData.maxShearStressRatio.ratio, effectiveStressRatioLimit))}>
                {summaryData.maxShearStressRatio.ratio.toFixed(3)}
                {getStatusIcon(summaryData.maxShearStressRatio.ratio, effectiveStressRatioLimit)}
              </p>
              <p className="text-xs text-muted-foreground">
                Actual: {summaryData.maxShearStressRatio.actualStress.toFixed(2)} {units.pressure}<br/>
                Allowable: {summaryData.maxShearStressRatio.allowableStress.toFixed(2)} {units.pressure}<br/>
                Load Combo: {summaryData.maxShearStressRatio.loadComboName}
              </p>
            </>
          ) : (
            <p className="text-sm text-muted-foreground">N/A</p>
          )}
        </div>

        {/* Max Downward Deflection */}
        <div>
          <p className="font-medium flex items-center">Max Downward <ArrowDown className="inline h-4 w-4 ml-1" />:</p>
          {summaryData.maxTotalDeflectionDownward && typeof summaryData.maxTotalDeflectionDownward.value === 'number' ? (
            <>
              <p className="font-bold text-white">{summaryData.maxTotalDeflectionDownward.value.toFixed(unitSystem === UnitSystem.IMPERIAL ? 3 : 1)} {units.deflection}</p>
              <p className="text-xs text-muted-foreground">
                Ratio: {formatDeflectionRatio(summaryData.maxTotalDeflectionDownward.value, beamLength, totalDeflectionLimitDenom)}<br/>
                Load Combo: {summaryData.maxTotalDeflectionDownward.loadComboName}
              </p>
            </>
          ) : (
            <p className="text-sm text-muted-foreground">N/A</p>
          )}
        </div>

        {/* Max Upward Deflection */}
        <div>
          <p className="font-medium flex items-center">Max Upward <ArrowUp className="inline h-4 w-4 ml-1" />:</p>
          {summaryData.maxTotalDeflectionUpward && typeof summaryData.maxTotalDeflectionUpward.value === 'number' ? (
            <>
              <p className="font-bold text-white">{summaryData.maxTotalDeflectionUpward.value.toFixed(unitSystem === UnitSystem.IMPERIAL ? 3 : 1)} {units.deflection}</p>
              <p className="text-xs text-muted-foreground">
                Ratio: {formatDeflectionRatio(summaryData.maxTotalDeflectionUpward.value, beamLength, totalDeflectionLimitDenom)}<br/>
                Load Combo: {summaryData.maxTotalDeflectionUpward.loadComboName}
              </p>
            </>
          ) : (
            <p className="text-sm text-muted-foreground">N/A</p>
          )}
        </div>
      </div>

      {/* Reaction Envelope Section */}
      {reactionsToDisplay && reactionsToDisplay.length > 0 && (
        <div className="pt-3 mt-3 border-t border-gray-200">
          <h5 className="text-sm font-semibold mb-2 text-white">Max. Upward Reactions:</h5>
          <ul className="space-y-1 text-xs">
            {reactionsToDisplay.map((reaction, index) => (
              <li key={index} className="flex justify-between items-center">
                <span className="font-medium text-white">{reaction.label}:</span>
                <span className='text-white font-semibold'>
                  {reaction.value.toFixed(2)} {units.force} 
                  <span className="text-gray-300 font-normal ml-1">({reaction.loadCombo})</span>
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
} 