"use client";

import Link from 'next/link';
import { <PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Folder as FolderIconLucide, MoreHorizontal, Eye, Edit3, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from 'next/navigation';
import { Droppable } from 'react-beautiful-dnd';

// Define the Folder interface (can be imported from a shared types file later)
interface Folder {
  id: string;
  name: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

interface FolderCardProps {
  folder: Folder;
  orgId: string;
  // Placeholder functions for actions - these would typically trigger modals or API calls
  onRename: (folderId: string, currentName: string) => void;
  onDelete: (folderId: string) => void;
  // dragHandleProps?: any; // If making the card draggable itself
}

export function FolderCard({ folder, orgId, onRename, onDelete }: FolderCardProps) {
  const router = useRouter();
  const projectPagePath = `/organizations/${orgId}/projects/${folder.projectId}`;
  const folderViewPathWithQuery = `${projectPagePath}?folderId=${folder.id}`;

  return (
    <Droppable droppableId={`folder-drop-${folder.id}`} type="analysis">
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.droppableProps}
          // Basic visual feedback for drag over
          className={`rounded-lg ${snapshot.isDraggingOver ? 'bg-blue-100 dark:bg-blue-900/50 ring-2 ring-blue-500' : ''}`}
        >
          <Card className="hover:shadow-md transition-shadow duration-150 w-full sm:w-auto min-w-[180px]">
            <CardHeader className="p-3">
              <div className="flex items-center justify-between space-x-2">
                <div className="flex items-center space-x-2 overflow-hidden">
                  <FolderIconLucide className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  <CardTitle className="text-sm font-medium truncate" title={folder.name || 'Unnamed Folder'}>
                    <Link href={folderViewPathWithQuery} className="hover:underline">
                      {folder.name || 'Unnamed Folder'}
                    </Link>
                  </CardTitle>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-7 w-7 flex-shrink-0">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Folder actions</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => router.push(folderViewPathWithQuery)}>
                      <Eye className="mr-2 h-4 w-4" /> Open
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onRename(folder.id, folder.name)}>
                      <Edit3 className="mr-2 h-4 w-4" /> Rename
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onDelete(folder.id)}
                      className="text-destructive focus:text-destructive focus:bg-destructive/10"
                    >
                      <Trash2 className="mr-2 h-4 w-4" /> Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            {/* Placeholder for droppable content if needed, usually not for this type of target */}
            {/* <div style={{ minHeight: '10px', display: snapshot.isDraggingOver ? 'block' : 'none' }}> */} 
              {/* {provided.placeholder}  // Usually placeholder is used for list-type droppables */} 
            {/* </div> */} 
          </Card>
          {/* It's important to render the placeholder if items can be reordered *within* this droppable. */} 
          {/* For a simple drop target, the placeholder might not be strictly necessary inside the card itself. */} 
          {/* However, react-beautiful-dnd expects provided.placeholder to be rendered somewhere within the droppable. */} 
          {/* Let's put a minimal placeholder outside the card but inside the droppable div to satisfy this. */} 
          <div style={{ display: 'none' }}>{provided.placeholder}</div>
        </div>
      )}
    </Droppable>
  );
} 