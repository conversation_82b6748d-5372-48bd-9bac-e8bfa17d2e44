"use client";

import React from "react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { BeamData } from "@/lib/types/beam/beam-data";
import type { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { SummaryData } from "@/lib/types/analysis/analysis-output";
import { DesignValuesDisplay } from "./beam-properties/design-values-display"; // TODO: Move to materials component
import { SteelDesignResults } from "./beam-results/steel-design-results";
import { SawnLumberDisplay } from "./beam-results/sawn-lumber-display";
import { GlulamDisplay } from "./beam-results/glulam-display";
import { TraditionalStressRatios } from "./beam-results/traditional-stress-ratios";
import { ReactionsDisplay } from "./beam-results/reactions-display";
import { styles } from "@/lib/styles/design-system";
import { cn } from "@/lib/utils";
import Spinner from "@/app/components/ui/Spinner";

interface BeamSummaryProps {
  isLoading: boolean;
  error: string | null;
  summaryData: SummaryData | null;
  beamPropertiesState: BeamPropertiesState | null;
  beamData: BeamData;
  unitSystem: UnitSystem;
}

export function BeamSummary({ 
  isLoading, 
  error, 
  summaryData, 
  beamPropertiesState, 
  beamData, 
  unitSystem 
}: BeamSummaryProps) {
  // Check for presence of loads
  const hasLoads = beamData && beamData.loadGroups && beamData.loadGroups.some(group => group.loads && group.loads.length > 0);

  // Detect manual mode: when manual dimensions are present but no lumber properties
  const isManualMode = Boolean(beamPropertiesState?.manualWidth && 
                               beamPropertiesState?.manualWidth.trim() &&
                               beamPropertiesState?.manualDepth && 
                               beamPropertiesState?.manualDepth.trim() &&
                               !beamPropertiesState?.designValues && 
                               !beamPropertiesState?.selectedGluLamProperties);

  return (
    <div className={styles.container}>
      {/* Design Values Section - Material-specific displays */}
      {beamPropertiesState && (
        <>
          {summaryData?.glulamDesign?.designCheckResults?.woodAnalysis ? (
            /* Glulam Material Properties & Analysis */
            <GlulamDisplay 
              beamPropertiesState={beamPropertiesState}
              summaryData={summaryData}
              unitSystem={unitSystem}
            />
          ) : summaryData?.sawnLumberDesign?.designCheckResults?.woodAnalysis ? (
            /* Sawn Lumber Material Properties & Analysis */
            <SawnLumberDisplay 
              beamPropertiesState={beamPropertiesState}
              summaryData={summaryData}
              unitSystem={unitSystem}
            />
          ) : (
            /* Fallback: Basic Display for cases without detailed analysis */
            <div className={styles.section}>
              <h3 className={styles.heading.h3}>Material & Design Properties</h3>
              <DesignValuesDisplay 
                beamPropertiesState={beamPropertiesState}
                unitSystem={unitSystem}
                isManualMode={isManualMode}
              />
            </div>
          )}
        </>
      )}

      {/* Calculation Results */}
      {hasLoads ? (
        <>
          {/* Loading State */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-12 px-4">
              <Spinner loading={true} size={40} color="#eb804d" />
              <div className="text-center mt-4">
                <h3 className="text-lg font-semibold text-white">Running Analysis</h3>
                <p className="text-sm text-white mt-1">
                  Calculating beam summary and stress analysis...
                </p>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className={cn(styles.status.error, "p-3 rounded-md")}>
              Error: {error}
            </div>
          )}

          {/* Summary Data */}
          {!isLoading && !error && summaryData && (
            <>
              {/* Analysis Results - Material-specific */}
              {summaryData.steelDesign ? (
                <div className={styles.section}>
                  <h3 className={styles.heading.h3}>Steel Design Analysis</h3>
                  <SteelDesignResults steelDesign={summaryData.steelDesign} />
                </div>
              ) : summaryData.sawnLumberDesign || summaryData.glulamDesign ? (
                /* NDS Design results are already displayed in the material-specific components above */
                null
              ) : (
                /* Traditional Stress Ratios Section - Only show for materials without NDS analysis */
                <TraditionalStressRatios
                  beamPropertiesState={beamPropertiesState}
                  summaryData={summaryData}
                  unitSystem={unitSystem}
                />
              )}

              {/* Reactions Section */}
              <ReactionsDisplay
                beamData={beamData}
                summaryData={summaryData}
                unitSystem={unitSystem}
                isLoading={isLoading}
                error={error}
              />
            </>
          )}
        </>
      ) : (
        <div className={styles.emptyState}>
          Add Loads to see calculation results.
        </div>
      )}
    </div>
  );
}
