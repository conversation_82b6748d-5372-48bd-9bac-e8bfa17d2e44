"use client";

import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { BeamData } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem, Units } from "@/lib/types/units/unit-enum";
import { DiagramData, SummaryData } from "@/lib/types/analysis/analysis-output";
import { DataPoint } from "@/lib/types/analysis/analysis-results";
import { Support } from "@/lib/types/support/support";
import { SpanAnalysisModal } from "./span-analysis-modal";
import { BarChart4 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Spinner from "@/app/components/ui/Spinner";

interface BeamSpanAnalysisProps {
  beamData: BeamData;
  unitSystem: UnitSystem;
  isLoading: boolean;
  error: string | null;
  diagramData: DiagramData | null;
  summaryData: SummaryData | null;
}

const findMaxAbsoluteValue = (data: DataPoint[] | undefined): { value: number; position: number } => {
  if (!data || data.length === 0) {
    return { value: NaN, position: NaN };
  }
  let maxAbsValue = -Infinity;
  let positionAtMax = NaN;
  let valueAtMax = NaN;

  data.forEach(point => {
    const absValue = Math.abs(point.value);
    if (isFinite(absValue) && absValue > maxAbsValue) {
      maxAbsValue = absValue;
      positionAtMax = point.x;
      valueAtMax = point.value;
    }
  });

  return { value: valueAtMax, position: positionAtMax };
};

function formatValueWithPosition(value: number, position: number, units: Units, unitType: keyof Units): string {
  if (!isFinite(value) || !isFinite(position)) {
    return "---";
  }
  const absValue = Math.abs(value);
  const formattedValue = absValue < 0.001 && absValue > 0
    ? value.toExponential(2)
    : value.toFixed(2);
  return `${formattedValue} ${units[unitType]} @ ${position.toFixed(2)} ${units.length}`;
}

export function BeamSpanAnalysis({ 
  beamData, 
  unitSystem, 
  isLoading, 
  error, 
  diagramData, 
  summaryData 
}: BeamSpanAnalysisProps) {
  const units = getUnitsBySystem(unitSystem);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <Spinner loading={true} size={40} />
        <div className="text-center mt-4">
          <h3 className="text-lg font-semibold text-gray-900">Analyzing Spans</h3>
          <p className="text-sm text-gray-600 mt-1">
            Calculating span-by-span analysis results...
          </p>
        </div>
      </div>
    );
  }

  // Check for presence of loads first
  const hasLoads = beamData && beamData.loadGroups && beamData.loadGroups.some(group => group.loads && group.loads.length > 0);

  if (!hasLoads) {
    return (
      <div className="p-6 text-center text-gray-600 bg-blue-50 border border-blue-200 rounded-md">
        Add Loads to see calculation.
      </div>
    );
  }

  // If loads exist, then check for other errors
  if (error) {
    return <div className="p-4 text-red-500">Error calculating span analysis: {error}</div>;
  }

  if (
    !diagramData || 
    !diagramData.shear?.length || 
    !diagramData.moment?.length || 
    !diagramData.deflection?.length || 
    !beamData?.supports || 
    beamData.supports.length < 2
  ) {
    console.log("BeamSpanAnalysis - Not enough data:", { 
      hasDiagramData: !!diagramData,
      shearLength: diagramData?.shear?.length,
      momentLength: diagramData?.moment?.length,
      deflectionLength: diagramData?.deflection?.length,
      supports: beamData?.supports 
    });
    return <div className="p-4 text-center">Not enough data for span analysis.</div>;
  }

  const sortedSupports = [...beamData.supports].sort((a, b) => a.position - b.position);
  const TOLERANCE = 1e-9;

  const spans = sortedSupports.slice(0, -1).map((startSupport, index) => {
    const endSupport = sortedSupports[index + 1];
    const spanStart = startSupport.position;
    const spanEnd = endSupport.position;
    const spanLength = spanEnd - spanStart;

    if (spanLength < TOLERANCE) return null;

    const filterSpanData = (data: DataPoint[] | undefined): DataPoint[] => 
      (data || []).filter(p => p.x >= spanStart - TOLERANCE && p.x <= spanEnd + TOLERANCE);

    const spanShearData = filterSpanData(diagramData.shear);
    const spanMomentData = filterSpanData(diagramData.moment);
    const spanDeflectionData = filterSpanData(diagramData.deflection);

    const maxShear = findMaxAbsoluteValue(spanShearData);
    const maxMoment = findMaxAbsoluteValue(spanMomentData);
    const maxDeflection = findMaxAbsoluteValue(spanDeflectionData);

    const spanType = `Span ${index + 1}`;

    return {
      number: index + 1,
      start: spanStart,
      end: spanEnd,
      length: spanLength,
      shearData: spanShearData,
      momentData: spanMomentData,
      deflectionData: spanDeflectionData,
      maxShear,
      maxMoment,
      maxDeflection,
      type: spanType,
    };
  }).filter((span): span is NonNullable<typeof span> => span !== null);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Span</TableHead>
          <TableHead>Start ({units.length})</TableHead>
          <TableHead>End ({units.length})</TableHead>
          <TableHead>Length ({units.length})</TableHead>
          <TableHead>Max Deflection</TableHead>
          <TableHead>Max Shear</TableHead>
          <TableHead>Max Moment</TableHead>
          <TableHead className="w-8"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {spans.length > 0 ? spans.map((span) => (
          <TableRow key={span.number}>
            <TableCell>{span.number}</TableCell>
            <TableCell>{span.start.toFixed(2)}</TableCell>
            <TableCell>{span.end.toFixed(2)}</TableCell>
            <TableCell>{span.length.toFixed(2)}</TableCell>
            <TableCell>
              {formatValueWithPosition(span.maxDeflection.value, span.maxDeflection.position, units, 'deflection')}
            </TableCell>
            <TableCell>
              {formatValueWithPosition(span.maxShear.value, span.maxShear.position, units, 'force')}
            </TableCell>
            <TableCell>
               {formatValueWithPosition(span.maxMoment.value, span.maxMoment.position, units, 'moment')}
            </TableCell>
            <TableCell>
              <SpanAnalysisModal
                 spanNumber={span.number}
                 spanStart={span.start}
                 spanEnd={span.end}
                 unitSystem={unitSystem}
                 shearData={span.shearData}
                 momentData={span.momentData}
                 deflectionData={span.deflectionData}
                 maxShear={span.maxShear}
                 maxMoment={span.maxMoment}
                 maxDeflection={span.maxDeflection}
              />
            </TableCell>
          </TableRow>
        )) : (
          <TableRow>
            <TableCell colSpan={8} className="text-center">No spans found or calculated.</TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}