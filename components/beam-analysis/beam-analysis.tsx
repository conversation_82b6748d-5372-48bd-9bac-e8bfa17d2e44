"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { BeamProperties } from "./beam-properties";
import { BeamLoads } from "@/components/shared/loads";
import { Supports } from "@/components/shared/supports";
import { BeamVisualization } from "./beam-visualization";
import { BeamResults } from "./beam-results";
import { BeamSpanAnalysis } from "./beam-span-analysis";
import { BeamSummary } from "./beam-summary";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import { ASCE_7_10_ASD_ComboMap } from "@/lib/types/load/asce-load-combo";
import { ASCE_7_10_LRFD_ComboMap } from "@/lib/types/load/asce-lrfd-combo";
import { useBeamData } from "@/lib/hooks/use-beam-data";
import { 
  ANALYSIS_TABS, 
  TAB_LABELS, 
  DEFAULT_FB_ALLOW_PLACEHOLDER, 
  DEFAULT_FV_ALLOW_PLACEHOLDER,
  DEFAULT_TOTAL_DEFLECTION_LIMIT,
  DEFAULT_LIVE_DEFLECTION_LIMIT,
  DEFAULT_MANUAL_WIDTH_PLACEHOLDER,
  DEFAULT_MANUAL_DEPTH_PLACEHOLDER,
  DEFAULT_ELASTIC_MODULUS,
  DEFAULT_ALLOWED_ELASTIC_MODULUS,
  WOOD_DENSITY_IMPERIAL,
  WOOD_DENSITY_METRIC,
  DEFAULT_ADJUSTMENT_FACTORS,
  DEFAULT_TEMPERATURE
} from "@/lib/constants/beam-constants";
import { LoadCombinationsModal } from "../shared/load-combinations-modal";
import { DEFAULT_NDS_VERSION } from "@/lib/constants/nds-constants";
import type { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { LoadGroup } from "@/lib/types/load/load-group";
import { Support } from "@/lib/types/support/support";
import { useBeamAnalysisCalculations } from "@/lib/hooks/useBeamAnalysisCalculations";
import { LoadType, Type as LoadEnumType } from "@/lib/types/load/load-type";
import { filterLoadGroupsById, reconstructLoadGroupsFromJson } from "@/lib/utils/load-group-utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { Save, Users } from "lucide-react";
import Link from "next/link";
import { DependentsModal } from "../projects/DependentsModal";
import Spinner from "@/app/components/ui/Spinner";
import { STRING_CONSTANTS, ANALYSIS_CONSTANTS } from "@/components/constants";
import { MATERIAL_TYPES } from "@/components/materials";

interface BeamAnalysisProps {
  unitSystem: UnitSystem;
  analysisIdToLoad?: string;
  currentOrgId?: string;
  currentProjectId?: string;
}

// Use constants from the constants file
const INPUT_TABS = STRING_CONSTANTS.TABS;

// Map the tab values to their labels
const INPUT_TAB_LABELS = {
  [STRING_CONSTANTS.TABS.PROPERTIES]: STRING_CONSTANTS.TAB_LABELS.PROPERTIES,
  [STRING_CONSTANTS.TABS.LOADS]: STRING_CONSTANTS.TAB_LABELS.LOADS,
  [STRING_CONSTANTS.TABS.SUPPORTS]: STRING_CONSTANTS.TAB_LABELS.SUPPORTS,
} as const;

const getDefaultLoadCombos = (designMethod: 'ASD' | 'LRFD') =>
  Object.keys(designMethod === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap);

export function BeamAnalysis({ unitSystem, analysisIdToLoad, currentOrgId, currentProjectId }: BeamAnalysisProps) {
  const {
    beamData,
    setBeamData,
    handlePropertiesChange,
    handleSupportsChange
  } = useBeamData(unitSystem);

  const [beamPropertiesState, setBeamPropertiesState] = useState<BeamPropertiesState>({
    isWetService: false,
    isRepetitiveMember: false,
    isBraced: false,
    lu: "",
    selectedSpecies: "",
    selectedSpeciesCombination: "",
    selectedGrade: "",
    selectedSizeClassification: "",
    selectedNominalSize: "",
    selectedNdsVersion: DEFAULT_NDS_VERSION, // Add default NDS version
    manualWidth: DEFAULT_MANUAL_WIDTH_PLACEHOLDER,
    manualDepth: DEFAULT_MANUAL_DEPTH_PLACEHOLDER,
    designValues: null,
    lumberProperties: null,
    flatUseFactor: 1.0,
    repetitiveMemberFactor: 1.0,
    wetServiceFactor: DEFAULT_ADJUSTMENT_FACTORS,
    manual_E: DEFAULT_ELASTIC_MODULUS,
    manual_Fb_allow: DEFAULT_FB_ALLOW_PLACEHOLDER,
    manual_Fv_allow: DEFAULT_FV_ALLOW_PLACEHOLDER,
    manual_E_min: DEFAULT_ALLOWED_ELASTIC_MODULUS,
    manual_totalDeflectionLimit: DEFAULT_TOTAL_DEFLECTION_LIMIT,
    manual_liveDeflectionLimit: DEFAULT_LIVE_DEFLECTION_LIMIT,
    manual_Area: null,
    manual_Ixx: null,
    manual_Iyy: null,
    manual_Sxx: null,
    manual_Syy: null,
    beamStabilityFactorCL: null,
    manual_maxStressRatioLimit: 1.0,
    includeBeamWeight: false,
    moistureContent: null,
    isIncised: false,
    incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
    isTemperatureFactored: false,
    temperature: DEFAULT_TEMPERATURE,
    temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
    selectedGluLamProperties: null,
    lumberType: "sawn",
    volumeFactorCv: null,
    manual_species: null,
    manual_grade: null,
    manual_material_type: "Sawn Lumber",
    isManualMode: false,
    selectedMaterial: "wood",
    
    // Steel-specific properties
    isSteelBraced: false,
    steelUnbracedLength: "",
  });

  const [analysisName, setAnalysisName] = useState<string>("");
  const [loadedAnalysisId, setLoadedAnalysisId] = useState<string | null>(null);
  const [isJustLoaded, setIsJustLoaded] = useState(false);
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [projectId, setProjectId] = useState<string | null>(currentProjectId || null);
  const [orgId, setOrgId] = useState<string | null>(currentOrgId || null);
  const [activeTab, setActiveTab] = useState(ANALYSIS_TABS.SUMMARY);
  const [dependentsCount, setDependentsCount] = useState<number>(0);
  const [isDependentsModalOpen, setIsDependentsModalOpen] = useState(false);
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState<boolean>(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [modalOpenIntent, setModalOpenIntent] = useState<'view' | 'update' | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [designMethod, setDesignMethod] = useState<'ASD' | 'LRFD'>('ASD');

  useEffect(() => {
    console.log("[BeamAnalysis] dependentsCount state updated to:", dependentsCount);
  }, [dependentsCount]);

  const fetchAndLoadAnalysis = useCallback(async (loadId: string) => {
    try {
      const response = await fetch(`/api/calculations/${loadId}/analysis-results?cache=${Date.now()}`, {
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        }
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to load analysis (status: ${response.status})`);
      }
      const savedData = await response.json();

      if (!savedData) {
        throw new Error("Loaded data is empty or invalid.");
      }

      console.log("[BeamAnalysis] Raw savedData from API:", JSON.stringify(savedData, null, 2));
      console.log("[BeamAnalysis] Raw savedData.loads string:", savedData.loads);

      setAnalysisName(savedData.name || "");
      setLoadedAnalysisId(savedData.id);
      if (savedData.projectId) {
        setProjectId(savedData.projectId); 
      }
      if (savedData.organizationId) {
        setOrgId(savedData.organizationId);
      }

      console.log("[BeamAnalysis] Loaded savedData:", savedData);

      // Set dependentsCount: Prefer from savedData, else fetch separately
      if (typeof savedData.dependentsCount === 'number') {
        console.log("[BeamAnalysis] Using dependentsCount from savedData:", savedData.dependentsCount);
        setDependentsCount(savedData.dependentsCount);
      } else if (savedData.id) {
        console.log("[BeamAnalysis] savedData.dependentsCount not found or not a number. Attempting to fetch dependents count separately for ID:", savedData.id);
        try {
          const dependentsResponse = await fetch(`/api/calculations/${savedData.id}/dependents?cache=${Date.now()}`, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache',
            }
          });
          if (dependentsResponse.ok) {
            const dependentsData = await dependentsResponse.json();
            console.log("[BeamAnalysis] Fetched dependentsData separately:", dependentsData);
            setDependentsCount(dependentsData.dependentsCount || 0);
          } else {
            console.warn(`[BeamAnalysis] Failed to fetch dependents count separately. Status: ${dependentsResponse.status}, Response: ${await dependentsResponse.text()}`);
            setDependentsCount(0);
          }
        } catch (depError) {
          console.error("[BeamAnalysis] Error fetching dependents count separately:", depError);
          setDependentsCount(0);
        }
      } else {
        console.warn("[BeamAnalysis] No savedData.id found after loading analysis, cannot determine dependents count.");
        setDependentsCount(0);
      }

      let loadedBeamPropertiesState: BeamPropertiesState | null = null;
      if (savedData.beamProperties && typeof savedData.beamProperties === 'string') {
        loadedBeamPropertiesState = JSON.parse(savedData.beamProperties) as BeamPropertiesState;
        
        // Ensure backward compatibility for manual_material_type
        if (loadedBeamPropertiesState && !loadedBeamPropertiesState.manual_material_type) {
          loadedBeamPropertiesState.manual_material_type = "Sawn Lumber";
        }
        
        // Ensure backward compatibility for selectedNdsVersion
        if (loadedBeamPropertiesState && !loadedBeamPropertiesState.selectedNdsVersion) {
          loadedBeamPropertiesState.selectedNdsVersion = DEFAULT_NDS_VERSION;
        }
        
        // Ensure backward compatibility for selectedMaterial
        if (loadedBeamPropertiesState && !loadedBeamPropertiesState.selectedMaterial) {
          // Check if this analysis has steel properties to determine default value
          const hasSteel = (loadedBeamPropertiesState as any).selectedSteelGrade || 
                          (loadedBeamPropertiesState as any).selectedSteelShape || 
                          (loadedBeamPropertiesState as any).steelDesignValues || 
                          (loadedBeamPropertiesState as any).steelSectionProperties;
          loadedBeamPropertiesState.selectedMaterial = hasSteel ? "steel" : "wood";
        }
        
        // Ensure backward compatibility for steel bracing properties
        if (loadedBeamPropertiesState && typeof (loadedBeamPropertiesState as any).isSteelBraced === 'undefined') {
          (loadedBeamPropertiesState as any).isSteelBraced = false;
        }
        if (loadedBeamPropertiesState && typeof (loadedBeamPropertiesState as any).steelUnbracedLength === 'undefined') {
          (loadedBeamPropertiesState as any).steelUnbracedLength = "";
        }
        
        setBeamPropertiesState(loadedBeamPropertiesState);
      }

      const loadedLoads: LoadGroup[] = savedData.loads ? reconstructLoadGroupsFromJson(JSON.parse(savedData.loads)) : [];
      const loadedSupports: Support[] = savedData.supports ? JSON.parse(savedData.supports) : [];

      // Load design method and selected load combinations from saved beamData
      let savedDesignMethod: 'ASD' | 'LRFD' = 'ASD';
      let savedSelectedLoadCombos: string[] = [];
      let savedCustomLoadCombos: Record<string, LoadComboFactor> = {};
      
      if (savedData.beamData && typeof savedData.beamData === 'string') {
        try {
          const parsedBeamData = JSON.parse(savedData.beamData);
          if (parsedBeamData.designMethod) {
            savedDesignMethod = parsedBeamData.designMethod;
            setDesignMethod(parsedBeamData.designMethod);
            console.log("[BeamAnalysis] Loaded design method:", parsedBeamData.designMethod);
          }
          if (parsedBeamData.selectedLoadCombos && Array.isArray(parsedBeamData.selectedLoadCombos)) {
            savedSelectedLoadCombos = parsedBeamData.selectedLoadCombos;
            console.log("[BeamAnalysis] Loaded selected load combos:", parsedBeamData.selectedLoadCombos);
          }
          if (parsedBeamData.customLoadCombos && typeof parsedBeamData.customLoadCombos === 'object') {
            savedCustomLoadCombos = parsedBeamData.customLoadCombos;
            console.log("[BeamAnalysis] Loaded custom load combos:", parsedBeamData.customLoadCombos);
          }
        } catch (error) {
          console.warn("[BeamAnalysis] Error parsing saved beamData for design method/load combos:", error);
        }
      }

      // Also update beamPropertiesState with loaded load combinations for backward compatibility
      if (loadedBeamPropertiesState && (savedSelectedLoadCombos.length > 0 || Object.keys(savedCustomLoadCombos).length > 0)) {
        loadedBeamPropertiesState.selectedLoadCombos = savedSelectedLoadCombos;
        loadedBeamPropertiesState.customLoadCombos = savedCustomLoadCombos;
        setBeamPropertiesState(loadedBeamPropertiesState);
      }

      console.log("[BeamAnalysis] Parsed loadedLoads:", JSON.stringify(loadedLoads, null, 2));
      console.log("[BeamAnalysis] Data fetch timestamp:", new Date().toISOString());
      console.log("[BeamAnalysis] Number of loads parsed:", loadedLoads.length);
      if (loadedLoads.length > 0) {
        console.log("[BeamAnalysis] First load group details:", {
          id: loadedLoads[0].id,
          label: loadedLoads[0].label,
          loads: loadedLoads[0].loads?.map(load => ({
            loadType: load.loadType,
            startPosition: load.startPosition,
            startMagnitude: load.startMagnitude
          }))
        });
      }

      let area = 0;
      if (loadedBeamPropertiesState) {
        if (loadedBeamPropertiesState.selectedMaterial === "steel") {
          // For steel beams, get area from steel section properties
          const steelSectionProperties = (loadedBeamPropertiesState as any).steelSectionProperties;
          if (steelSectionProperties) {
            area = parseFloat(steelSectionProperties.A || "0");
            console.log("[BeamAnalysis] Loaded steel beam area:", area, "from section:", steelSectionProperties.EDI_Std_Nomenclature || "Unknown");
          }
        } else if (loadedBeamPropertiesState.manual_Area) {
          area = loadedBeamPropertiesState.manual_Area;
        } else if (loadedBeamPropertiesState.lumberType === 'sawn' && loadedBeamPropertiesState.lumberProperties?.A) {
          area = loadedBeamPropertiesState.lumberProperties.A;
        } else if (loadedBeamPropertiesState.lumberType === 'glulam' && loadedBeamPropertiesState.selectedGluLamProperties?.sectionProperties?.area) {
          area = loadedBeamPropertiesState.selectedGluLamProperties.sectionProperties.area;
        }
      }

      const beamDataForAnalysis = {
        ...beamData,
        properties: {
          length: savedData.length,
          elasticModulus: savedData.modulusOfElasticity,
          momentOfInertia: savedData.momentOfInertia,
          area: area, 
        },
        loadGroups: loadedLoads,
        supports: loadedSupports,
        selectedLoadCombos: savedSelectedLoadCombos,
        designMethod: savedDesignMethod,
        customLoadCombos: savedCustomLoadCombos,
      };

      setBeamData(beamDataForAnalysis);

      setIsJustLoaded(true);
      toast({
        title: "Analysis Loaded",
        description: `Successfully loaded "${savedData.name}".`,
      });

    } catch (error: any) {
      console.error("Error loading analysis:", error);
      toast({
        title: "Load Failed",
        description: error.message || "An unexpected error occurred while loading.",
        variant: "destructive",
      });
      setLoadedAnalysisId(null);
      setIsLoadingAnalysis(false);
      setLoadingError(error.message || "An unexpected error occurred while loading.");
    }
  }, [
    toast, 
    setAnalysisName, 
    setLoadedAnalysisId, 
    setProjectId, 
    setOrgId, 
    setBeamPropertiesState, 
    setBeamData, 
    setIsJustLoaded,
    setDependentsCount,
    beamData,
    beamPropertiesState,
    designMethod
  ]);
  
  // Effect to manage orgId and projectId state based on props and query params,
  // primarily when not actively loading a specific analysis.
  useEffect(() => {
    if (!loadedAnalysisId) { // Only adjust context if no specific analysis is loaded/being loaded
      const orgIdFromQuery = searchParams.get('orgId');
      const projectIdFromQuery = searchParams.get('projectId');

      const targetOrgId = currentOrgId || orgIdFromQuery || null;
      const targetProjectId = currentProjectId || projectIdFromQuery || null;

      if (orgId !== targetOrgId) {
        setOrgId(targetOrgId);
      }
      if (projectId !== targetProjectId) {
        setProjectId(targetProjectId);
      }
    }
  }, [currentOrgId, currentProjectId, searchParams, loadedAnalysisId, orgId, projectId]);

  // Effect to handle loading an analysis or clearing state for a new one.
  useEffect(() => {
    const loadIdFromQuery = searchParams.get("load");
    // Prioritize analysisIdToLoad prop if provided (coming from a specific calculation page context)
    const effectiveLoadId = analysisIdToLoad || loadIdFromQuery;

    if (effectiveLoadId && effectiveLoadId !== "new") {
      console.log(`[BeamAnalysis] Attempting to load analysis with effectiveLoadId: ${effectiveLoadId}`);
      console.log(`[BeamAnalysis] useEffect load: effectiveLoadId (${effectiveLoadId}) !== loadedAnalysisId (${loadedAnalysisId}). Fetching.`);
      setIsLoadingAnalysis(true);
      setLoadingError(null);
      fetch(`/api/calculations/${effectiveLoadId}/analysis-results?cache=${Date.now()}`, {
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        }
      })
        .then(async (res) => {
          if (!res.ok) {
            const errorData = await res.json();
            throw new Error(errorData.error || `Failed to load analysis (status: ${res.status})`);
          }
          const savedData = await res.json();

          if (!savedData) {
            throw new Error("Loaded data is empty or invalid.");
          }

          console.log("[BeamAnalysis] Raw savedData from API:", JSON.stringify(savedData, null, 2));
          console.log("[BeamAnalysis] Raw savedData.loads string:", savedData.loads);

          setAnalysisName(savedData.name || "");
          setLoadedAnalysisId(savedData.id);
          if (savedData.projectId) {
            setProjectId(savedData.projectId); 
          }
          if (savedData.organizationId) {
            setOrgId(savedData.organizationId);
          }

          console.log("[BeamAnalysis] Loaded savedData:", savedData);

          // Set dependentsCount: Prefer from savedData, else fetch separately
          if (typeof savedData.dependentsCount === 'number') {
            console.log("[BeamAnalysis] Using dependentsCount from savedData:", savedData.dependentsCount);
            setDependentsCount(savedData.dependentsCount);
          } else if (savedData.id) {
            console.log("[BeamAnalysis] savedData.dependentsCount not found or not a number. Attempting to fetch dependents count separately for ID:", savedData.id);
            try {
              const dependentsResponse = await fetch(`/api/calculations/${savedData.id}/dependents?cache=${Date.now()}`, {
                headers: {
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache',
                }
              });
              if (dependentsResponse.ok) {
                const dependentsData = await dependentsResponse.json();
                console.log("[BeamAnalysis] Fetched dependentsData separately:", dependentsData);
                setDependentsCount(dependentsData.dependentsCount || 0);
              } else {
                console.warn(`[BeamAnalysis] Failed to fetch dependents count separately. Status: ${dependentsResponse.status}, Response: ${await dependentsResponse.text()}`);
                setDependentsCount(0);
              }
            } catch (depError) {
              console.error("[BeamAnalysis] Error fetching dependents count separately:", depError);
              setDependentsCount(0);
            }
          } else {
            console.warn("[BeamAnalysis] No savedData.id found after loading analysis, cannot determine dependents count.");
            setDependentsCount(0);
          }

          let loadedBeamPropertiesState: BeamPropertiesState | null = null;
          if (savedData.beamProperties && typeof savedData.beamProperties === 'string') {
            loadedBeamPropertiesState = JSON.parse(savedData.beamProperties) as BeamPropertiesState;
            
            // Ensure backward compatibility for manual_material_type
            if (loadedBeamPropertiesState && !loadedBeamPropertiesState.manual_material_type) {
              loadedBeamPropertiesState.manual_material_type = "Sawn Lumber";
            }
            
            // Ensure backward compatibility for selectedMaterial
            if (loadedBeamPropertiesState && !loadedBeamPropertiesState.selectedMaterial) {
              // Check if this analysis has steel properties to determine default value
              const hasSteel = (loadedBeamPropertiesState as any).selectedSteelGrade || 
                              (loadedBeamPropertiesState as any).selectedSteelShape || 
                              (loadedBeamPropertiesState as any).steelDesignValues || 
                              (loadedBeamPropertiesState as any).steelSectionProperties;
              loadedBeamPropertiesState.selectedMaterial = hasSteel ? "steel" : "wood";
            }
            
            // Ensure backward compatibility for steel bracing properties
            if (loadedBeamPropertiesState && typeof (loadedBeamPropertiesState as any).isSteelBraced === 'undefined') {
              (loadedBeamPropertiesState as any).isSteelBraced = false;
            }
            if (loadedBeamPropertiesState && typeof (loadedBeamPropertiesState as any).steelUnbracedLength === 'undefined') {
              (loadedBeamPropertiesState as any).steelUnbracedLength = "";
            }
            
            setBeamPropertiesState(loadedBeamPropertiesState);
          }

          const loadedLoads: LoadGroup[] = savedData.loads ? reconstructLoadGroupsFromJson(JSON.parse(savedData.loads)) : [];
          const loadedSupports: Support[] = savedData.supports ? JSON.parse(savedData.supports) : [];

          // Load design method and selected load combinations from saved beamData
          let savedDesignMethod: 'ASD' | 'LRFD' = 'ASD';
          let savedSelectedLoadCombos: string[] = [];
          let savedCustomLoadCombos: Record<string, LoadComboFactor> = {};
          
          if (savedData.beamData && typeof savedData.beamData === 'string') {
            try {
              const parsedBeamData = JSON.parse(savedData.beamData);
              if (parsedBeamData.designMethod) {
                savedDesignMethod = parsedBeamData.designMethod;
                setDesignMethod(parsedBeamData.designMethod);
                console.log("[BeamAnalysis] Loaded design method:", parsedBeamData.designMethod);
              }
              if (parsedBeamData.selectedLoadCombos && Array.isArray(parsedBeamData.selectedLoadCombos)) {
                savedSelectedLoadCombos = parsedBeamData.selectedLoadCombos;
                console.log("[BeamAnalysis] Loaded selected load combos:", parsedBeamData.selectedLoadCombos);
              }
              if (parsedBeamData.customLoadCombos && typeof parsedBeamData.customLoadCombos === 'object') {
                savedCustomLoadCombos = parsedBeamData.customLoadCombos;
                console.log("[BeamAnalysis] Loaded custom load combos:", parsedBeamData.customLoadCombos);
              }
            } catch (error) {
              console.warn("[BeamAnalysis] Error parsing saved beamData for design method/load combos:", error);
            }
          }

          // Also update beamPropertiesState with loaded load combinations for backward compatibility
          if (loadedBeamPropertiesState && (savedSelectedLoadCombos.length > 0 || Object.keys(savedCustomLoadCombos).length > 0)) {
            loadedBeamPropertiesState.selectedLoadCombos = savedSelectedLoadCombos;
            loadedBeamPropertiesState.customLoadCombos = savedCustomLoadCombos;
            setBeamPropertiesState(loadedBeamPropertiesState);
          }

          console.log("[BeamAnalysis] Parsed loadedLoads:", JSON.stringify(loadedLoads, null, 2));
          console.log("[BeamAnalysis] Data fetch timestamp:", new Date().toISOString());
          console.log("[BeamAnalysis] Number of loads parsed:", loadedLoads.length);
          if (loadedLoads.length > 0) {
            console.log("[BeamAnalysis] First load group details:", {
              id: loadedLoads[0].id,
              label: loadedLoads[0].label,
              loads: loadedLoads[0].loads?.map(load => ({
                loadType: load.loadType,
                startPosition: load.startPosition,
                startMagnitude: load.startMagnitude
              }))
            });
          }

          let area = 0;
          if (loadedBeamPropertiesState) {
            if (loadedBeamPropertiesState.selectedMaterial === "steel") {
              // For steel beams, get area from steel section properties
              const steelSectionProperties = (loadedBeamPropertiesState as any).steelSectionProperties;
              if (steelSectionProperties) {
                area = parseFloat(steelSectionProperties.A || "0");
                console.log("[BeamAnalysis] Loaded steel beam area:", area, "from section:", steelSectionProperties.EDI_Std_Nomenclature || "Unknown");
              }
            } else if (loadedBeamPropertiesState.manual_Area) {
              area = loadedBeamPropertiesState.manual_Area;
            } else if (loadedBeamPropertiesState.lumberType === 'sawn' && loadedBeamPropertiesState.lumberProperties?.A) {
              area = loadedBeamPropertiesState.lumberProperties.A;
            } else if (loadedBeamPropertiesState.lumberType === 'glulam' && loadedBeamPropertiesState.selectedGluLamProperties?.sectionProperties?.area) {
              area = loadedBeamPropertiesState.selectedGluLamProperties.sectionProperties.area;
            }
          }

          const beamDataForAnalysis = {
            ...beamData,
            properties: {
              length: savedData.length,
              elasticModulus: savedData.modulusOfElasticity,
              momentOfInertia: savedData.momentOfInertia,
              area: area, 
            },
            loadGroups: loadedLoads,
            supports: loadedSupports,
            selectedLoadCombos: savedSelectedLoadCombos,
            designMethod: savedDesignMethod,
            customLoadCombos: savedCustomLoadCombos,
          };

          setBeamData(beamDataForAnalysis);

          setIsJustLoaded(true);
          toast({
            title: "Analysis Loaded",
            description: `Successfully loaded "${savedData.name}".`,
          });

        })
        .catch((error: any) => {
          console.error("Error loading analysis:", error);
          toast({
            title: "Load Failed",
            description: error.message || "An unexpected error occurred while loading.",
            variant: "destructive",
          });
          setLoadedAnalysisId(null);
          setIsLoadingAnalysis(false);
          setLoadingError(error.message || "An unexpected error occurred while loading.");
        })
        .finally(() => {
          setIsLoadingAnalysis(false);
        });
    } else {
      // No effectiveLoadId means we are likely in a "new analysis" scenario or navigating away.
      // Only clear if there was a previously loaded analysis.
      // If effectiveLoadId IS "new", we also don't fetch, and the state should remain as for a new analysis.
      if (loadedAnalysisId !== null && !effectiveLoadId) { // only clear if truly no ID, not if it's "new"
        console.log("[BeamAnalysis] useEffect load: No effectiveLoadId (and not 'new') and loadedAnalysisId was present. Clearing state.");
        setLoadedAnalysisId(null);
        setAnalysisName("");
        setDependentsCount(0);
        setIsLoadingAnalysis(false);
        setLoadingError(null);
        // Reset beam data to default for a new analysis if necessary
        // This might require a more specific reset function from useBeamData or setting default beamData here.
      } else {
        console.log("[BeamAnalysis] useEffect load: No effectiveLoadId (and not 'new') and no loadedAnalysisId. State is already clear.");
      }
    }
  }, [analysisIdToLoad, searchParams, loadedAnalysisId]);

  const { analysisResults, isLoading, error: calculationError } = useBeamAnalysisCalculations(
    beamData, 
    unitSystem, 
    beamPropertiesState,
    designMethod
  );

  const SELF_WEIGHT_LOAD_GROUP_ID = STRING_CONSTANTS.LOAD_GROUP_IDS.SELF_WEIGHT;

  const handleLumberTypeChange = (type: "sawn" | "glulam") => {
    setBeamPropertiesState(prevState => ({
      ...prevState,
      lumberType: type,
      selectedSpecies: "",
      selectedSpeciesCombination: "",
      selectedGrade: "",
      selectedSizeClassification: "",
      selectedNominalSize: "",
      designValues: null,
      lumberProperties: null, 
      selectedGluLamProperties: null, 
    }));
  };

  // Extract self-weight calculation logic into a separate function
  const calculateSelfWeight = (currentBeamData: typeof beamData) => {
    let weightPerUnitLength = 0;
    
    // Check if this is a steel beam
    console.log("Self-weight calculation debug:", {
      selectedMaterial: beamPropertiesState.selectedMaterial,
      hasSteelProperties: !!(beamPropertiesState as any).steelSectionProperties,
      steelSectionProperties: (beamPropertiesState as any).steelSectionProperties,
      beamArea: currentBeamData.properties.area
    });
    
    if (beamPropertiesState.selectedMaterial === MATERIAL_TYPES.STEEL && 
        (beamPropertiesState as any).steelSectionProperties) {
      
      const steelSection = (beamPropertiesState as any).steelSectionProperties;
      console.log("Steel section for weight lookup:", steelSection);
      
      const sectionWeight = parseFloat(steelSection.W || "0"); // Weight in plf from steel database
      
      console.log("Steel weight parsing:", {
        rawWeight: steelSection.W,
        parsedWeight: sectionWeight,
        weightType: typeof steelSection.W
      });
      
      if (sectionWeight > 0) {
        if (unitSystem === UnitSystem.IMPERIAL) {
          // Steel section weight is already in plf (pounds per linear foot)
          weightPerUnitLength = sectionWeight;
        } else {
          // Convert from plf to N/m for metric system
          weightPerUnitLength = sectionWeight * ANALYSIS_CONSTANTS.STEEL.PLF_TO_NM_CONVERSION;
        }
        
        console.log("Steel self-weight calculation SUCCESS:", {
          sectionName: steelSection.EDI_Std_Nomenclature || "Unknown",
          weightFromDatabase: sectionWeight,
          unitSystem,
          finalWeight: weightPerUnitLength
        });
      } else {
        console.log("Steel weight is 0 or invalid, falling back to wood calculation");
      }
    }
    
    // Wood beam calculation (existing logic) - also used as fallback for steel
    if (weightPerUnitLength === 0) {
      console.log("Using wood/fallback calculation");
      const area = currentBeamData.properties.area;
      console.log("Wood calculation inputs:", {
        area,
        unitSystem,
        WOOD_DENSITY_IMPERIAL,
        WOOD_DENSITY_METRIC,
        beamPropertiesState: {
          manualWidth: beamPropertiesState.manualWidth,
          manualDepth: beamPropertiesState.manualDepth,
          selectedMaterial: beamPropertiesState.selectedMaterial
        }
      });
      
      if (area > 0) {
        if (unitSystem === UnitSystem.IMPERIAL) {
          weightPerUnitLength = (area / ANALYSIS_CONSTANTS.WOOD_DENSITY.AREA_CONVERSION_IMPERIAL) * WOOD_DENSITY_IMPERIAL;
        } else {
          weightPerUnitLength = (area / ANALYSIS_CONSTANTS.WOOD_DENSITY.AREA_CONVERSION_METRIC) * WOOD_DENSITY_METRIC * ANALYSIS_CONSTANTS.WOOD_DENSITY.CONVERSION_FACTOR;
        }
        
        console.log("Wood calculation result:", {
          weightPerUnitLength,
          calculation: unitSystem === UnitSystem.IMPERIAL ? 
            `(${area} / 144) * ${WOOD_DENSITY_IMPERIAL} = ${weightPerUnitLength}` :
            `(${area} / 1000000) * ${WOOD_DENSITY_METRIC} * 9.80665 = ${weightPerUnitLength}`
        });
      } else {
        console.log("Wood calculation failed: area is 0 or negative");
      }
    }
    
    console.log("Final self-weight calculation result:", {
      weightPerUnitLength,
      unitSystem,
      material: beamPropertiesState.selectedMaterial
    });
    
    return weightPerUnitLength;
  };

  const handleIncludeBeamWeightChange = (include: boolean) => {
    console.log("handleIncludeBeamWeightChange called with:", include);
    
    setBeamData(prevBeamData => {
      let newLoadGroups = [...prevBeamData.loadGroups];
      const existingSelfWeightCount = newLoadGroups.filter(group => group.id === SELF_WEIGHT_LOAD_GROUP_ID).length;
      
      console.log("Before filtering - existing self-weight loads:", existingSelfWeightCount);
      newLoadGroups = filterLoadGroupsById(newLoadGroups, SELF_WEIGHT_LOAD_GROUP_ID);
      console.log("After filtering - remaining load groups:", newLoadGroups.length);

      if (include) {
        const weightPerUnitLength = calculateSelfWeight(prevBeamData);
        console.log("Calculated weight per unit length:", weightPerUnitLength);
        
        if (weightPerUnitLength > 0) {
          const selfWeightLoadGroup = new LoadGroup(
            SELF_WEIGHT_LOAD_GROUP_ID,
            "Self Weight (Beam)",
            { start: 0, end: prevBeamData.properties.length },
            { [LoadType.DEAD]: weightPerUnitLength },
            { [LoadType.DEAD]: weightPerUnitLength },
            LoadEnumType.DISTRIBUTED,
            undefined,
            false,
            true
          );
          newLoadGroups.push(selfWeightLoadGroup);
          console.log("Added self-weight load group. Total load groups now:", newLoadGroups.length);
        } else {
          console.log("Weight per unit length is 0 or negative, not adding self-weight load");
        }
      } else {
        console.log("Removing self-weight load (include = false)");
      }
      
      return { ...prevBeamData, loadGroups: newLoadGroups };
    });
  };

  const [selectedLoadCombo, setSelectedLoadCombo] = useState<LoadComboFactor>();

  const handleLoadComboChange = (comboName: string) => {
    setSelectedLoadCombo(ASCE_7_10_ASD_ComboMap[comboName]);
  };

  // Auto-update self-weight when material changes if self-weight is enabled
  useEffect(() => {
    // Check if self-weight is currently enabled
    const hasSelfWeight = beamData.loadGroups.some(group => group.id === SELF_WEIGHT_LOAD_GROUP_ID);
    
    if (hasSelfWeight) {
      console.log("Material changed, updating self-weight automatically");
      // Recalculate and update self-weight
      setBeamData(prevBeamData => {
        let newLoadGroups = [...prevBeamData.loadGroups];
        newLoadGroups = filterLoadGroupsById(newLoadGroups, SELF_WEIGHT_LOAD_GROUP_ID);
        
        const weightPerUnitLength = calculateSelfWeight(prevBeamData);
        
        if (weightPerUnitLength > 0) {
          const selfWeightLoadGroup = new LoadGroup(
            SELF_WEIGHT_LOAD_GROUP_ID,
            "Self Weight (Beam)",
            { start: 0, end: prevBeamData.properties.length },
            { [LoadType.DEAD]: weightPerUnitLength },
            { [LoadType.DEAD]: weightPerUnitLength },
            LoadEnumType.DISTRIBUTED,
            undefined,
            false,
            true
          );
          newLoadGroups.push(selfWeightLoadGroup);
        }
        
        return { ...prevBeamData, loadGroups: newLoadGroups };
      });
    }
  }, [
    beamPropertiesState.selectedMaterial, 
    (beamPropertiesState as any).steelSectionProperties,
    beamData.properties.area // Also update if area changes (for wood)
  ]);

  // Ensure beam weight is properly synchronized with the toggle state
  useEffect(() => {
    const hasSelfWeight = beamData.loadGroups.some(group => group.id === SELF_WEIGHT_LOAD_GROUP_ID);
    
    // If the toggle state doesn't match the actual load groups, fix it
    if (beamPropertiesState.includeBeamWeight && !hasSelfWeight) {
      console.log("Toggle is ON but no self-weight load found. Adding self-weight load.");
      handleIncludeBeamWeightChange(true);
    } else if (!beamPropertiesState.includeBeamWeight && hasSelfWeight) {
      console.log("Toggle is OFF but self-weight load exists. Removing self-weight load.");
      handleIncludeBeamWeightChange(false);
    }
  }, [beamPropertiesState.includeBeamWeight, beamData.loadGroups]);

  const handleSaveAnalysis = async (isCalledFromModalConfirm = false) => {
    if (isJustLoaded && !projectId && !isCalledFromModalConfirm) {
      setIsJustLoaded(false);
      console.log("handleSaveAnalysis call suppressed immediately after load.");
      toast({
        title: "Analysis Ready",
        description: "Review the loaded data. Click 'Update Analysis' again if you wish to save changes.",
        duration: 4000,
      });
      return;
    }

    if (isSaving) {
      console.log("[BeamAnalysis] Save already in progress. Call ignored.");
      return;
    }

    if (!isCalledFromModalConfirm && loadedAnalysisId && dependentsCount > 0) {
      console.log("[BeamAnalysis] Update Analysis clicked with dependents. Opening modal for confirmation.");
      setModalOpenIntent('update');
      setIsDependentsModalOpen(true);
      return;
    }

    console.log("[BeamAnalysis] Proceeding with handleSaveAnalysis logic. isCalledFromModalConfirm:", isCalledFromModalConfirm);

    if (!analysisResults?.summaryData) {
      toast({
        title: "Error",
        description: "No analysis results to save. Please run an analysis first.",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);

    let modulusE: number | undefined | null = undefined;
    let inertiaI: number | undefined | null = undefined;

    // Handle steel beam properties
    if (beamPropertiesState.selectedMaterial === "steel") {
      const steelSectionProperties = (beamPropertiesState as any).steelSectionProperties;
      if (steelSectionProperties) {
        // For steel, modulus of elasticity is typically 29,000 ksi (29,000,000 psi)
        modulusE = ANALYSIS_CONSTANTS.STEEL.MODULUS_OF_ELASTICITY_PSI;
        // Use the moment of inertia from steel section properties
        const momentOfInertiaStr = steelSectionProperties.Ix || steelSectionProperties.I_x || "0";
        inertiaI = parseFloat(momentOfInertiaStr);
        
        console.log("[BeamAnalysis] Steel properties for save:", {
          modulusE,
          inertiaI,
          sectionName: steelSectionProperties.EDI_Std_Nomenclature || "Unknown",
          selectedGrade: (beamPropertiesState as any).selectedSteelGrade,
          selectedShape: (beamPropertiesState as any).selectedSteelShape,
          selectedShapeSize: (beamPropertiesState as any).selectedSteelShapeSize
        });
      } else {
        console.error("[BeamAnalysis] Steel material selected but no steel section properties found");
        toast({ 
          title: "Error", 
          description: "Steel section properties are missing. Please select a steel section.", 
          variant: "destructive" 
        });
        setIsSaving(false);
        return;
      }
    }
    // Handle wood beam properties
    else if (beamPropertiesState.selectedSpecies === "MANUAL" && beamPropertiesState.lumberType === "sawn") {
      modulusE = beamPropertiesState.manual_E;
      inertiaI = beamPropertiesState.manual_Ixx;
    } else if (beamPropertiesState.lumberType === "sawn" && beamPropertiesState.designValues) {
      modulusE = beamPropertiesState.designValues.E;
      inertiaI = beamData.properties.momentOfInertia;
    } else if (beamPropertiesState.lumberType === "glulam" && beamPropertiesState.selectedGluLamProperties) {
      const glulamProps = beamPropertiesState.selectedGluLamProperties;
      if (glulamProps.sectionProperties) {
        inertiaI = glulamProps.sectionProperties.Ix;
      }
      if (glulamProps.selectedTable5ADetail) {
        modulusE = glulamProps.selectedTable5ADetail.E_xapp_ksi * 1000;
      } else if (glulamProps.selectedTable5BDetail) {
        modulusE = glulamProps.selectedTable5BDetail.E_axial_ksi * 1000;
      }
    } else if (beamPropertiesState.manual_E !== null && beamPropertiesState.manual_Ixx !== null) {
      modulusE = beamPropertiesState.manual_E;
      inertiaI = beamPropertiesState.manual_Ixx;
    }

    if (modulusE === undefined || modulusE === null || isNaN(modulusE)) {
        const materialType = beamPropertiesState.selectedMaterial === "steel" ? "steel beam" : "wood beam";
        toast({ 
          title: "Error", 
          description: `Modulus of Elasticity (E) could not be determined for ${materialType}. Please ensure all material properties are selected.`, 
          variant: "destructive" 
        });
        setIsSaving(false);
        return;
    }
    if (inertiaI === undefined || inertiaI === null || isNaN(inertiaI)) {
        const materialType = beamPropertiesState.selectedMaterial === "steel" ? "steel beam" : "wood beam";
        toast({ 
          title: "Error", 
          description: `Moment of Inertia (I) could not be determined for ${materialType}. Please ensure all section properties are selected.`, 
          variant: "destructive" 
        });
        setIsSaving(false);
        return;
    }
    
    const summary = analysisResults.summaryData;
    const nameToSave = analysisName.trim() || (loadedAnalysisId ? "Unnamed Loaded Analysis" : "Unnamed Analysis");

    const payload: any = {
      name: nameToSave, 
      length: beamData.properties.length,
      modulusOfElasticity: modulusE,
      momentOfInertia: inertiaI,
      beamProperties: JSON.stringify(beamPropertiesState), 
      loads: JSON.stringify(beamData.loadGroups),
      supports: JSON.stringify(beamData.supports),
      beamData: JSON.stringify(beamData),
      results: JSON.stringify(summary),
      bendingStressRatio: summary.maxBendingStressRatio?.ratio || 0,
      shearStressRatio: summary.maxShearStressRatio?.ratio || 0,
      maxDeflection: Math.max(Math.abs(summary.maxTotalDeflectionDownward?.value || 0), Math.abs(summary.maxTotalDeflectionUpward?.value || 0))
    };

    if (projectId && !loadedAnalysisId) {
      console.log("[BeamAnalysis] Adding projectId to payload:", projectId);
      payload.projectId = projectId;
    }
    if (orgId && !loadedAnalysisId) {
      console.log("[BeamAnalysis] Adding organizationId to payload:", orgId);
      payload.organizationId = orgId;
    }
    
    console.log("[BeamAnalysis] Save payload:", {
      ...payload,
      beamProperties: "[JSON string]", // Don't log the full JSON
      loads: "[JSON string]",
      supports: "[JSON string]",
      beamData: "[JSON string]",
      results: "[JSON string]"
    });

    let response;
    let successMessage = "";
    let newAnalysisId: string | null = loadedAnalysisId;
    let wasNewCreation = false;

    try {
      if (loadedAnalysisId) {
        // This is an UPDATE of an existing analysis
        response = await fetch(`/api/beam-analysis/results/${loadedAnalysisId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        successMessage = `Analysis "${nameToSave}" updated successfully.`;
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: "Failed to parse error response from server." }));
          throw new Error(errorData.error || `Failed to update analysis (status: ${response.status})`);
        }
      } else {
        // This is a CREATE of a new analysis
        wasNewCreation = true;
        response = await fetch('/api/beam-analysis/results', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        if (response.ok) {
            const newRecord = await response.json();
            if (newRecord && newRecord.calculationId) { 
                newAnalysisId = newRecord.calculationId;
                setLoadedAnalysisId(newRecord.calculationId);
                setAnalysisName(newRecord.beamAnalysisResult?.name || nameToSave); 

                const folderIdFromUrl = searchParams.get('folderId');
                if (folderIdFromUrl && newAnalysisId) {
                    console.log(`[BeamAnalysis] New analysis ${newAnalysisId} created. Associating with folder ${folderIdFromUrl}.`);
                    try {
                        const associateResponse = await fetch(`/api/calculations/${newAnalysisId}`, {
                            method: 'PATCH',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ folderId: folderIdFromUrl }),
                        });
                        if (!associateResponse.ok) {
                            const assocErrorData = await associateResponse.json().catch(() => ({ error: "Failed to parse association error response." }));
                            console.error(`Failed to associate calculation ${newAnalysisId} with folder ${folderIdFromUrl}:`, assocErrorData.error || associateResponse.statusText);
                            toast({
                                title: "Folder Association Failed",
                                description: `Analysis saved, but could not associate with folder: ${assocErrorData.error || associateResponse.statusText}`,
                                variant: "destructive",
                                duration: 7000,
                            });
                        } else {
                            console.log(`[BeamAnalysis] Analysis ${newAnalysisId} successfully associated with folder ${folderIdFromUrl}.`);
                        }
                    } catch (assocErr: any) {
                        console.error(`Error during folder association for ${newAnalysisId} with folder ${folderIdFromUrl}:`, assocErr);
                        toast({
                            title: "Folder Association Error",
                            description: `Analysis saved, but an unexpected error occurred while associating with folder: ${assocErr.message}`,
                            variant: "destructive",
                            duration: 7000,
                        });
                    }
                }
                successMessage = `Analysis "${nameToSave}" saved successfully${payload.projectId ? ' to project' : ''}.`;
            } else {
                 console.error("[BeamAnalysis] Save POST was OK but newRecord or newRecord.calculationId is missing:", newRecord);
                 throw new Error("Save operation succeeded but calculation ID was not returned.");
            }
        } else {
            const errorData = await response.json().catch(() => ({ error: "Failed to parse error response from server." }));
            throw new Error(errorData.error || `Failed to save analysis (status: ${response.status})`);
        }
      }
      
      toast({
        title: (wasNewCreation && !newAnalysisId) ? "Save Attempted" : (loadedAnalysisId && !wasNewCreation ? "Analysis Updated" : "Analysis Saved"),
        description: (wasNewCreation && !newAnalysisId) ? "Save operation sent, but record ID was not confirmed." : successMessage,
      });

      if (newAnalysisId) {
        let targetUrl = "";
        const currentPathname = pathname; // Keep current pathname
        const currentQueryString = searchParams.toString();
        const currentUrl = currentQueryString ? `${currentPathname}?${currentQueryString}` : currentPathname;
        const folderIdFromUrl = searchParams.get('folderId'); // Get folderId from current URL

        if (wasNewCreation) {
          // Just created a new record, navigate to its canonical URL
          if (orgId && projectId) { // orgId and projectId are from state
            const folderParam = folderIdFromUrl ? `?folderId=${folderIdFromUrl}` : '';
            targetUrl = `/organizations/${orgId}/projects/${projectId}/calculations/${newAnalysisId}${folderParam}`;
          } else if (projectId) {
            // If a specific route like /projects/[projectId]/calculations/[calculationId] exists, use it.
            // Otherwise, fallback to a more generic page with query parameters.
            // Assuming /beam-analysis?load=ID&projectId=ID as a common fallback:
            const params = new URLSearchParams();
            params.set('load', newAnalysisId);
            params.set('projectId', projectId);
            if (folderIdFromUrl) {
              params.set('folderId', folderIdFromUrl);
            }
            targetUrl = `/beam-analysis?${params.toString()}`;
            // If you have a route like /projects/[projectId]/calculations/[calculationId],
            // you might prefer: targetUrl = `/projects/${projectId}/calculations/${newAnalysisId}`;
          } else {
            // Standalone new analysis
            const params = new URLSearchParams();
            params.set('load', newAnalysisId);
            if (folderIdFromUrl) {
              params.set('folderId', folderIdFromUrl);
            }
            targetUrl = `/beam-analysis?${params.toString()}`;
          }
        } else {
          // This was an update to an existing record. The URL should generally not change.
          targetUrl = currentUrl;
        }

        if (targetUrl && targetUrl !== currentUrl) {
          router.replace(targetUrl);
        }
      }

    } catch (err: any) {
      console.error("Error saving analysis:", err);
      
      let errorMessage = err.message || "An unexpected error occurred.";
      
      // Handle specific foreign key constraint errors
      if (err.message && err.message.includes("Foreign key constraint violated") && err.message.includes("projectId")) {
        console.error("[BeamAnalysis] Project foreign key constraint failed. ProjectId:", projectId, "OrgId:", orgId);
        
        // Try to save without project association as a fallback
        try {
          console.log("[BeamAnalysis] Attempting to save without project association...");
          const fallbackPayload = { ...payload };
          delete fallbackPayload.projectId;
          delete fallbackPayload.organizationId;
          
          const fallbackResponse = await fetch('/api/beam-analysis/results', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(fallbackPayload),
          });
          
          if (fallbackResponse.ok) {
            const newRecord = await fallbackResponse.json();
            if (newRecord && newRecord.calculationId) {
              newAnalysisId = newRecord.calculationId;
              wasNewCreation = true;
              setLoadedAnalysisId(newRecord.calculationId);
              setAnalysisName(newRecord.beamAnalysisResult?.name || nameToSave);
              
              toast({
                title: "Analysis Saved",
                description: `Analysis "${nameToSave}" saved successfully (without project association due to invalid project ID).`,
                variant: "default",
              });
              
              // Redirect to the new analysis URL
              let targetUrl = "";
              const currentPathname = pathname;
              const currentQueryString = searchParams.toString();
              const currentUrl = currentQueryString ? `${currentPathname}?${currentQueryString}` : currentPathname;
              const folderIdFromUrl = searchParams.get('folderId');
              
              // For fallback saves, we don't have orgId/projectId, so use the standalone format
              if (newAnalysisId) {
                const params = new URLSearchParams();
                params.set('load', newAnalysisId);
                if (folderIdFromUrl) {
                  params.set('folderId', folderIdFromUrl);
                }
                targetUrl = `/beam-analysis?${params.toString()}`;
                
                if (targetUrl && targetUrl !== currentUrl) {
                  router.replace(targetUrl);
                }
              }
              return; // Exit early after redirect
            }
          }
        } catch (fallbackErr) {
          console.error("[BeamAnalysis] Fallback save also failed:", fallbackErr);
        }
        
        errorMessage = `Invalid project ID (${projectId}). The project may have been deleted or you may not have access to it. Fallback save also failed.`;
      }
      
      toast({
        title: loadedAnalysisId ? "Update Failed" : "Save Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4 space-y-6">
      {/* Loading overlay for initial analysis loading */}
      {isLoadingAnalysis && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="rounded-lg p-6 shadow-xl max-w-sm w-full mx-4" style={{ backgroundColor: '#3b3364' }}>
            <div className="flex flex-col items-center space-y-4">
              <Spinner loading={true} size={40} color="#e27d51" />
              <div className="text-center">
                <h3 className="text-lg font-semibold text-white">Loading Analysis</h3>
                <p className="text-sm text-gray-200 mt-1">
                  Please wait while we load your beam analysis...
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error overlay for loading errors */}
      {loadingError && !isLoadingAnalysis && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Loading Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{loadingError}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6 pb-4 border-b">
        {loadedAnalysisId && analysisName && !projectId && !analysisIdToLoad ? (
          <h2 className="text-2xl font-semibold text-primary truncate">
            {analysisName}
          </h2>
        ) : (
          <div className="flex flex-col sm:flex-row items-center gap-4">
            <Input
              type="text"
              placeholder="Enter Analysis Name (e.g., 'Living Room Beam')"
              value={analysisName}
              onChange={(e) => setAnalysisName(e.target.value)}
              className="flex-grow"
              disabled={isLoadingAnalysis}
            />
            <Button 
              onClick={() => handleSaveAnalysis()} 
              disabled={!analysisResults?.summaryData || isSaving || isLoadingAnalysis}
              className="ml-4 min-w-[150px]"
            >
              {isSaving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {loadedAnalysisId ? "Update Analysis" : "Save Analysis"}
                </>
              )}
            </Button>
            {(() => {
              // console.log("[BeamAnalysis] Render check for dep button:", { loadedAnalysisId, dependentsCount, orgId, projectId });
              return null;
            })()}
            {loadedAnalysisId && dependentsCount > 0 && (
              <Button
                variant="outline"
                onClick={() => {
                  console.log("[BeamAnalysis] View Dependencies clicked. Opening modal for:", loadedAnalysisId);
                  setIsDependentsModalOpen(true);
                }}
                className="ml-2 bg-purple-500 hover:bg-purple-600 text-white"
                disabled={isLoadingAnalysis}
              >
                <Users size={16} className="mr-1.5" />
                View Dependencies ({dependentsCount})
              </Button>
            )}
            
            {/* LRFD/ASD Toggle Switch and Load Combinations Button */}
            <div className="flex items-center space-x-3 ml-4">
              <div className="flex items-center space-x-3 p-2 border rounded-md bg-transparent">
                <Label 
                  htmlFor="design-method-toggle" 
                  className={`text-sm font-medium cursor-pointer transition-colors ${
                    designMethod === 'ASD' ? 'text-primary font-bold' : 'text-gray-500'
                  }`}
                >
                  ASD
                </Label>
                <Switch
                  id="design-method-toggle"
                  checked={designMethod === 'LRFD'}
                  onCheckedChange={(checked) => {
                    const newDesignMethod = checked ? 'LRFD' : 'ASD';
                    setDesignMethod(newDesignMethod);
                    setBeamData(prev => {
                      // Get the standard combinations for the new design method
                      const standardLoadComboMap = newDesignMethod === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap;
                      const standardComboNames = Object.keys(standardLoadComboMap);
                      
                      // Keep custom combinations but remove standard ones from the old design method
                      const customCombos = prev.customLoadCombos || {};
                      const customComboNames = Object.keys(customCombos);
                      
                      // Filter selectedLoadCombos to only include custom combinations that still exist
                      const preservedSelectedCombos = (prev.selectedLoadCombos || []).filter(comboName => 
                        customComboNames.includes(comboName)
                      );
                      
                      return {
                        ...prev,
                        designMethod: newDesignMethod,
                        // Keep custom combinations but clear standard ones since they're different between ASD/LRFD
                        selectedLoadCombos: preservedSelectedCombos,
                      };
                    });
                  }}
                  disabled={isLoadingAnalysis}
                />
                <Label 
                  htmlFor="design-method-toggle" 
                  className={`text-sm font-medium cursor-pointer transition-colors ${
                    designMethod === 'LRFD' ? 'text-primary font-bold' : 'text-gray-500'
                  }`}
                >
                  LRFD
                </Label>
              </div>
              {/* Load Combinations Button */}
              <LoadCombinationsModal
                designMethod={designMethod}
                currentSelectedCombos={beamData.selectedLoadCombos || []}
                customLoadCombos={beamData.customLoadCombos || {}}
                onSaveSelectedCombos={(selectedComboNames, selectedComboFactors) => {
                  setBeamData(prev => ({
                    ...prev,
                    selectedLoadCombos: selectedComboNames,
                    customLoadCombos: selectedComboFactors
                  }));
                  setBeamPropertiesState(prev => ({
                    ...prev,
                    selectedLoadCombos: selectedComboNames,
                    customLoadCombos: selectedComboFactors
                  }));
                }}
                onDesignMethodChange={(method) => {
                  setDesignMethod(method);
                  setBeamData(prev => {
                    // Get the standard combinations for the new design method
                    const standardLoadComboMap = method === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap;
                    const standardComboNames = Object.keys(standardLoadComboMap);
                    
                    // Keep custom combinations but remove standard ones from the old design method
                    const customCombos = prev.customLoadCombos || {};
                    const customComboNames = Object.keys(customCombos);
                    
                    // Filter selectedLoadCombos to only include custom combinations that still exist
                    const preservedSelectedCombos = (prev.selectedLoadCombos || []).filter(comboName => 
                      customComboNames.includes(comboName)
                    );
                    
                    return {
                      ...prev,
                      designMethod: method,
                      // Keep custom combinations but clear standard ones since they're different between ASD/LRFD
                      selectedLoadCombos: preservedSelectedCombos,
                    };
                  });
                }}
              />
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div className="lg:col-span-5 space-y-4">
          <Card>
            <CardHeader><CardTitle>Beam Visualization</CardTitle></CardHeader>
            <CardContent>
              <BeamVisualization 
                beamData={beamData} 
                unitSystem={unitSystem}
                beamPropertiesState={beamPropertiesState}
              />
            </CardContent>
          </Card>
          
          <Card className="overflow-hidden">
            <CardContent className="p-4">
              <Tabs defaultValue={INPUT_TABS.PROPERTIES} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value={INPUT_TABS.PROPERTIES} disabled={isLoadingAnalysis}>{INPUT_TAB_LABELS[INPUT_TABS.PROPERTIES]}</TabsTrigger>
                  <TabsTrigger value={INPUT_TABS.LOADS} disabled={isLoadingAnalysis}>{INPUT_TAB_LABELS[INPUT_TABS.LOADS]}</TabsTrigger>
                  <TabsTrigger value={INPUT_TABS.SUPPORTS} disabled={isLoadingAnalysis}>{INPUT_TAB_LABELS[INPUT_TABS.SUPPORTS]}</TabsTrigger>
                </TabsList>
                <TabsContent value={INPUT_TABS.PROPERTIES} className="mt-4">
                  <BeamProperties
                    properties={beamData.properties}
                    onChange={handlePropertiesChange}
                    unitSystem={unitSystem}
                    beamPropertiesState={beamPropertiesState}
                    onBeamPropertiesStateChange={setBeamPropertiesState}
                    onLumberTypeChange={handleLumberTypeChange}
                    onIncludeBeamWeightChange={handleIncludeBeamWeightChange}
                  />
                </TabsContent>
                <TabsContent value={INPUT_TABS.LOADS} className="mt-4">
                  <BeamLoads
                    loadGroups={beamData.loadGroups}
                    onChange={(newLoadGroups: LoadGroup[]) => setBeamData(prev => ({ ...prev, loadGroups: newLoadGroups }))}
                    memberLength={beamData.properties.length}
                    unitSystem={unitSystem}
                    projectId={projectId === null ? undefined : projectId}
                    currentCalculationId={analysisIdToLoad}
                    memberType="beam"
                  />
                </TabsContent>
                <TabsContent value={INPUT_TABS.SUPPORTS} className="mt-4">
                  <Supports
                    supports={beamData.supports}
                    onChange={handleSupportsChange}
                    beamLength={beamData.properties.length}
                    unitSystem={unitSystem}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-7">
          <Card className="h-full">
            <CardContent className="p-4">
              <Tabs defaultValue={ANALYSIS_TABS.SUMMARY} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value={ANALYSIS_TABS.SUMMARY}>{TAB_LABELS[ANALYSIS_TABS.SUMMARY]}</TabsTrigger>
                  <TabsTrigger value={ANALYSIS_TABS.DIAGRAMS}>{TAB_LABELS[ANALYSIS_TABS.DIAGRAMS]}</TabsTrigger>
                  <TabsTrigger value={ANALYSIS_TABS.PER_SPAN}>{TAB_LABELS[ANALYSIS_TABS.PER_SPAN]}</TabsTrigger>
                </TabsList>
                <TabsContent value={ANALYSIS_TABS.SUMMARY} className="mt-4">
                  <BeamSummary 
                    summaryData={analysisResults?.summaryData ?? null}
                    unitSystem={unitSystem} 
                    isLoading={isLoading} 
                    error={calculationError}
                    beamPropertiesState={beamPropertiesState}
                    beamData={beamData}
                  />
                </TabsContent>
                <TabsContent value={ANALYSIS_TABS.DIAGRAMS} className="mt-4">
                  <BeamResults 
                    diagramData={analysisResults?.diagramData ?? null} 
                    unitSystem={unitSystem}
                    isLoading={isLoading}
                    error={calculationError}
                    summaryData={analysisResults?.summaryData ?? null}
                    beamData={beamData}
                  />
                </TabsContent>
                <TabsContent value={ANALYSIS_TABS.PER_SPAN} className="mt-4">
                  <BeamSpanAnalysis 
                    beamData={beamData}
                    unitSystem={unitSystem}
                    isLoading={isLoading}
                    error={calculationError}
                    diagramData={analysisResults?.diagramData ?? null}
                    summaryData={analysisResults?.summaryData ?? null}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>

      {loadedAnalysisId && orgId && projectId && (
        <DependentsModal
          isOpen={isDependentsModalOpen}
          onClose={() => setIsDependentsModalOpen(false)}
          calculationId={loadedAnalysisId}
          calculationName={analysisName}
          orgId={orgId}
          projectId={projectId}
          showUpdateButton={modalOpenIntent === 'update'}
          onConfirmUpdate={async () => {
            console.log("[BeamAnalysis] Modal confirm update clicked for", loadedAnalysisId, "- calling handleSaveAnalysis(true).");
            try {
              await handleSaveAnalysis(true);
              console.log("[BeamAnalysis] handleSaveAnalysis(true) completed successfully from modal confirm.");
            } catch (error) {
              console.error("[BeamAnalysis] Error from handleSaveAnalysis(true) during modal confirm:", error);
              throw error;
            }
          }}
        />
      )}
    </div>
  );
}