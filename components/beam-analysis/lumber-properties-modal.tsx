"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useState } from "react";
import { useLumberProperties } from "@/hooks/use-wood-data";

export function LumberPropertiesModal() {
  const [searchQuery, setSearchQuery] = useState("");
  const { data: lumberData, error, isLoading } = useLumberProperties();

  const filteredData = lumberData ? {
    ...lumberData,
    sizeClassifications: lumberData.sizeClassifications.filter(classification =>
      classification.toLowerCase().includes(searchQuery.toLowerCase())
    )
  } : null;

  return (
    <Dialog>
      <DialogTrigger className="text-sm text-muted-foreground hover:underline">
        NDS 2018 Table 1B
      </DialogTrigger>
      <DialogContent className="max-w-[90vw] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>NDS 2018 Table 1B - Properties of Standard Dressed Sawn Lumber</DialogTitle>
        </DialogHeader>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search size classifications..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <ScrollArea className="h-[70vh]">
          {error ? (
            <p className="text-destructive">{error.message}</p>
          ) : isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredData ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Size Classification</TableHead>
                  <TableHead>Nominal Size</TableHead>
                  <TableHead>Dressed Size</TableHead>
                  <TableHead>A (in²)</TableHead>
                  <TableHead>Sₓₓ (in³)</TableHead>
                  <TableHead>Iₓₓ (in⁴)</TableHead>
                  <TableHead>Sᵧᵧ (in³)</TableHead>
                  <TableHead>Iᵧᵧ (in⁴)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.sizeClassifications.map((classification) => (
                  filteredData.properties[classification].map((properties, index) => (
                    <TableRow key={`${classification}-${index}`}>
                      <TableCell>{classification}</TableCell>
                      <TableCell>{properties.nominal_size_bxd}</TableCell>
                      <TableCell>{properties.standard_dressed_size_bxd}</TableCell>
                      <TableCell>{properties.area_of_section_a_in2.toFixed(2)}</TableCell>
                      <TableCell>{properties.Sxx.toFixed(2)}</TableCell>
                      <TableCell>{properties.Ixx.toFixed(2)}</TableCell>
                      <TableCell>{properties.Syy.toFixed(2)}</TableCell>
                      <TableCell>{properties.Iyy.toFixed(2)}</TableCell>
                    </TableRow>
                  ))
                ))}
              </TableBody>
            </Table>
          ) : null}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}