"use client";

import React from "react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { SummaryData } from "@/lib/types/analysis/analysis-output";
import { styles, getMetricStyle, getCardStyle, getMetricColor } from "@/lib/styles/design-system";

interface TraditionalStressRatiosProps {
  beamPropertiesState: BeamPropertiesState | null;
  summaryData: SummaryData;
  unitSystem: UnitSystem;
}

export function TraditionalStressRatios({ 
  beamPropertiesState, 
  summaryData, 
  unitSystem 
}: TraditionalStressRatiosProps) {
  const units = getUnitsBySystem(unitSystem);
  
  // Determine the effective stress ratio limit
  const effectiveStressRatioLimit = beamPropertiesState?.manual_maxStressRatioLimit ?? 1.0;

  return (
    <div className={styles.section}>
      <h3 className={styles.heading.h3}>Maximum Stress Ratios</h3>
      <div className={styles.grid.dense}>
        {/* Bending Stress Card */}
        <div className={getCardStyle()}>
          <h4 className={styles.cardHeader}>Bending Stress Ratio</h4>
          <div className={styles.cardContent}>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Ratio (Act/Allow):</span>
              <span 
                className={getMetricStyle(
                  summaryData.maxBendingStressRatio.loadComboName === "Error" || !isFinite(summaryData.maxBendingStressRatio.ratio)
                    ? "error"
                    : summaryData.maxBendingStressRatio.ratio > effectiveStressRatioLimit
                    ? "error"
                    : "success"
                )}
                style={getMetricColor(
                  summaryData.maxBendingStressRatio.loadComboName === "Error" || !isFinite(summaryData.maxBendingStressRatio.ratio)
                    ? "error"
                    : summaryData.maxBendingStressRatio.ratio > effectiveStressRatioLimit
                    ? "error"
                    : "success"
                )}
              >
                {summaryData.maxBendingStressRatio.loadComboName === "Error" || !isFinite(summaryData.maxBendingStressRatio.ratio)
                  ? "Error"
                  : `${summaryData.maxBendingStressRatio.ratio.toFixed(3)} : ${effectiveStressRatioLimit.toFixed(1)}`}
              </span>
            </div>
            {summaryData.maxBendingStressRatio.loadComboName &&
              summaryData.maxBendingStressRatio.loadComboName !== "Error" &&
              summaryData.maxBendingStressRatio.loadComboName !== "N/A" && isFinite(summaryData.maxBendingStressRatio.ratio) && (
                <>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual f<sub>b</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.maxBendingStressRatio.actualStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable F<sub>b</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.maxBendingStressRatio.allowableStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Span:</span>
                    <span className={styles.metricValue}>
                      {summaryData.maxBendingStressRatio.spanNumber + 1}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Location:</span>
                    <span className={styles.metricValue}>
                      {isFinite(summaryData.maxBendingStressRatio.position)
                        ? summaryData.maxBendingStressRatio.position.toFixed(2)
                        : "N/A"} { " "}
                      <i className={styles.metricUnit}>{units.length}</i>
                    </span>
                  </div>
                   <div className={styles.metric}>
                    <span className={styles.metricLabel}>Load Combo:</span>
                    <span className={styles.metricValue}>
                      {summaryData.maxBendingStressRatio.loadComboName}
                    </span>
                  </div>
                </>
              )}
            {(summaryData.maxBendingStressRatio.loadComboName === "Error" || !isFinite(summaryData.maxBendingStressRatio.ratio)) && (
              <p className={styles.errorText}>Calculation Error</p>
            )}
            {summaryData.maxBendingStressRatio.loadComboName === "N/A" && isFinite(summaryData.maxBendingStressRatio.ratio) && (
              <p className={styles.infoText}>N/A (No Loads?)</p>
            )}
          </div>
        </div>

        {/* Shear Stress Card */}
        <div className={getCardStyle()}>
          <h4 className={styles.cardHeader}>Shear Stress Ratio</h4>
          <div className={styles.cardContent}>
            <div className={styles.metric}>
              <span className={styles.metricLabel}>Ratio (Act/Allow):</span>
              <span 
                className={getMetricStyle(
                  (summaryData.maxShearStressRatio.loadComboName === "Error" || 
                   !summaryData.maxShearStressRatio.ratio || 
                   !isFinite(summaryData.maxShearStressRatio.ratio))
                    ? "error"
                    : summaryData.maxShearStressRatio.ratio > effectiveStressRatioLimit
                    ? "error"
                    : "success"
                )}
                style={getMetricColor(
                  (summaryData.maxShearStressRatio.loadComboName === "Error" || 
                   !summaryData.maxShearStressRatio.ratio || 
                   !isFinite(summaryData.maxShearStressRatio.ratio))
                    ? "error"
                    : summaryData.maxShearStressRatio.ratio > effectiveStressRatioLimit
                    ? "error"
                    : "success"
                )}
              >
                {(summaryData.maxShearStressRatio.loadComboName === "Error" || 
                  !summaryData.maxShearStressRatio.ratio || 
                  !isFinite(summaryData.maxShearStressRatio.ratio))
                  ? "Error"
                  : `${summaryData.maxShearStressRatio.ratio.toFixed(3)} : ${effectiveStressRatioLimit.toFixed(1)}`}
              </span>
            </div>
             {summaryData.maxShearStressRatio.loadComboName &&
              summaryData.maxShearStressRatio.loadComboName !== "Error" &&
              summaryData.maxShearStressRatio.loadComboName !== "N/A" && isFinite(summaryData.maxShearStressRatio.ratio) && (
                <>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual fᵥ:</span>
                     <span className={styles.metricValue}>
                      {summaryData.maxShearStressRatio.actualStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable F&apos;<sub>v</sub>:</span>
                     <span className={styles.metricValue}>
                      {summaryData.maxShearStressRatio.allowableStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                   <div className={styles.metric}>
                    <span className={styles.metricLabel}>Span:</span>
                    <span className={styles.metricValue}>
                      {summaryData.maxShearStressRatio.spanNumber + 1}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Location:</span>
                    <span className={styles.metricValue}>
                      {isFinite(summaryData.maxShearStressRatio.position)
                        ? summaryData.maxShearStressRatio.position.toFixed(2)
                        : "N/A"} { " "}
                      <i className={styles.metricUnit}>{units.length}</i>
                    </span>
                  </div>
                   <div className={styles.metric}>
                    <span className={styles.metricLabel}>Load Combo:</span>
                    <span className={styles.metricValue}>
                      {summaryData.maxShearStressRatio.loadComboName}
                    </span>
                  </div>
                </>
              )}
             {(summaryData.maxShearStressRatio.loadComboName === "Error" || !isFinite(summaryData.maxShearStressRatio.ratio)) && (
              <p className={styles.errorText}>Calculation Error</p>
            )}
            {summaryData.maxShearStressRatio.loadComboName === "N/A" && isFinite(summaryData.maxShearStressRatio.ratio) && (
              <p className={styles.infoText}>N/A (No Loads?)</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 