"use client";

import React, { useState } from "react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { SummaryData } from "@/lib/types/analysis/analysis-output";
import { DesignValueBreakdown } from "./design-value-breakdown";
import { styles, getMetricStyle, getCardStyle, getMetricColor } from "@/lib/styles/design-system";
import { cn } from "@/lib/utils";

interface GlulamDisplayProps {
  beamPropertiesState: BeamPropertiesState;
  summaryData: SummaryData;
  unitSystem: UnitSystem;
}

export function GlulamDisplay({ 
  beamPropertiesState, 
  summaryData, 
  unitSystem 
}: GlulamDisplayProps) {
  const units = getUnitsBySystem(unitSystem);
  const [isDesignValuesExpanded, setIsDesignValuesExpanded] = useState(false);

  if (!summaryData.glulamDesign) {
    return null;
  }

  return (
    <div className={styles.section}>
      <h3 className={styles.heading.h3}>Material & Design Properties</h3>
      
      {/* Basic Material & Section Properties */}
      <div className={cn(getCardStyle())}>
        <h4 className={styles.cardHeader}>Section Properties</h4>
        <div className={styles.cardContent}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Type:</span>
                <span className="text-sm">Glued Laminated Timber</span>
              </div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Grade:</span>
                <span className="text-sm">
                  {beamPropertiesState.selectedGluLamProperties?.selectedTable5ADetail?.stress_class 
                    ? `${beamPropertiesState.selectedGluLamProperties.selectedTable5ADetail.stress_class} (${beamPropertiesState.selectedGluLamProperties.selectedTable5ADetail.species_outer}/${beamPropertiesState.selectedGluLamProperties.selectedTable5ADetail.species_core})`
                    : "N/A"}
                </span>
              </div>
            </div>
            <div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Species:</span>
                <span className="text-sm">
                  {beamPropertiesState.selectedGluLamProperties 
                    ? `${beamPropertiesState.selectedGluLamProperties.speciesGroup} - ${beamPropertiesState.selectedGluLamProperties.species}`
                    : "N/A"}
                </span>
              </div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Size:</span>
                <span className="text-sm">
                  {beamPropertiesState.selectedGluLamProperties 
                    ? `${beamPropertiesState.selectedGluLamProperties.width}" x ${beamPropertiesState.selectedGluLamProperties.depth}"`
                    : "N/A"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Collapsible Detailed Current Design Values */}
      {summaryData.glulamDesign.designCheckResults?.woodAnalysis && (
        <div className={cn(getCardStyle(), "col-span-full", "mt-4")}>
          <div 
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setIsDesignValuesExpanded(!isDesignValuesExpanded)}
          >
            <h4 className={styles.heading.h4}>Current Design Values</h4>
            <button 
              type="button"
              className="text-white hover:text-blue-300 transition-colors p-1"
              aria-label={isDesignValuesExpanded ? "Collapse design values" : "Expand design values"}
            >
              <svg 
                className={`w-5 h-5 transform transition-transform ${isDesignValuesExpanded ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
          
          {isDesignValuesExpanded && (
            <div className={styles.cardContent}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Strength Properties */}
                <div>
                  <h5 className="text-base font-semibold text-white mb-4">Strength Properties</h5>
                  <div className="space-y-4">
                    {/* Bending Positive (Fbx_pos) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fbx_pos && (
                      <DesignValueBreakdown
                        label="Bending - Positive (Tension Zone Stressed)"
                        symbol="F<sub>bx+</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Fbx_pos_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fbx_pos}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Fbx_pos}
                      />
                    )}

                    {/* Bending Negative (Fbx_neg) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fbx_neg && (
                      <DesignValueBreakdown
                        label="Bending - Negative (Compression Zone Stressed)"
                        symbol="F<sub>bx-</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Fbx_neg_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fbx_neg}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Fbx_neg}
                      />
                    )}

                    {/* Tension Parallel to Grain (Ft) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Ft && (
                      <DesignValueBreakdown
                        label="Tension Parallel to Grain"
                        symbol="F<sub>t</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Ft_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Ft}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Ft}
                      />
                    )}

                    {/* Shear X-direction (Fvx) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fvx && (
                      <DesignValueBreakdown
                        label="Shear Parallel to Grain (x-direction)"
                        symbol="F<sub>vx</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Fvx_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fvx}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Fvx}
                      />
                    )}

                    {/* Compression Perpendicular X-face (Fc_perp_x) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fc_perp_x && (
                      <DesignValueBreakdown
                        label="Compression Perpendicular to Grain (x-face)"
                        symbol="F<sub>c⊥x</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Fc_perp_x_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fc_perp_x}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Fc_perp_x}
                      />
                    )}

                    {/* Compression Parallel (Fc) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fc && (
                      <DesignValueBreakdown
                        label="Compression Parallel to Grain"
                        symbol="F<sub>c</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Fc_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fc}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Fc}
                      />
                    )}
                  </div>
                </div>

                {/* Elastic Properties */}
                <div>
                  <h5 className="text-base font-semibold text-white mb-4">Elastic Properties</h5>
                  <div className="space-y-4">
                    {/* Modulus of Elasticity x-axis (Ex) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Ex && (
                      <DesignValueBreakdown
                        label="Modulus of Elasticity (x-axis)"
                        symbol="E<sub>x</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Ex_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Ex}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Ex}
                      />
                    )}

                    {/* Modulus of Elasticity y-axis (Ey) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Ey && (
                      <DesignValueBreakdown
                        label="Modulus of Elasticity (y-axis)"
                        symbol="E<sub>y</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Ey_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Ey}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Ey}
                      />
                    )}

                    {/* Minimum Modulus of Elasticity x-axis (Ex_min) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Ex_min && (
                      <DesignValueBreakdown
                        label="Minimum Modulus of Elasticity (x-axis)"
                        symbol="E<sub>x,min</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Ex_min_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Ex_min}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Ex_min}
                      />
                    )}

                    {/* Minimum Modulus of Elasticity y-axis (Ey_min) */}
                    {summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Ey_min && (
                      <DesignValueBreakdown
                        label="Minimum Modulus of Elasticity (y-axis)"
                        symbol="E<sub>y,min</sub>"
                        currentValue={summaryData.glulamDesign.designCheckResults.adjustedDesignValues?.Ey_min_prime}
                        baseValue={summaryData.glulamDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Ey_min}
                        units={units.pressure}
                        adjustmentFactors={summaryData.glulamDesign.designCheckResults.adjustmentFactors?.Ey_min}
                      />
                    )}

                    {/* Specific Gravity */}
                    <DesignValueBreakdown
                      label="Specific Gravity"
                      symbol="G"
                      currentValue={beamPropertiesState?.designValues?.G}
                      baseValue={beamPropertiesState?.designValues?.G}
                      units=""
                    />

                    {/* Density */}
                    <DesignValueBreakdown
                      label="Density"
                      symbol="ρ"
                      currentValue={unitSystem === UnitSystem.METRIC 
                        ? (beamPropertiesState?.designValues?.G || 0.410) * 1000
                        : (beamPropertiesState?.designValues?.G || 0.410) * 62.4}
                      baseValue={unitSystem === UnitSystem.METRIC 
                        ? (beamPropertiesState?.designValues?.G || 0.410) * 1000
                        : (beamPropertiesState?.designValues?.G || 0.410) * 62.4}
                      units={unitSystem === UnitSystem.METRIC ? 'kg/m³' : 'lb/ft³'}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Glulam NDS Design Results */}
      <div className={styles.section}>
        <h3 className={styles.heading.h3}>Glulam Design Analysis (NDS)</h3>
        {summaryData.glulamDesign.success ? (
          <div className={styles.grid.dense}>
            {/* Design Summary Card */}
            <div className={getCardStyle()}>
              <h4 className={styles.cardHeader}>Design Summary</h4>
              <div className={styles.cardContent}>
                {summaryData.glulamDesign.designCheckResults?.summary && (
                  <>
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Overall Design:</span>
                      <span 
                        className={getMetricStyle(
                          summaryData.glulamDesign.designCheckResults.summary.isDesignAcceptable ? "success" : "error"
                        )}
                        style={getMetricColor(
                          summaryData.glulamDesign.designCheckResults.summary.isDesignAcceptable ? "success" : "error"
                        )}
                      >
                        {summaryData.glulamDesign.designCheckResults.summary.isDesignAcceptable ? "PASSES" : "FAILS"}
                      </span>
                    </div>
                    {summaryData.glulamDesign.designCheckResults.summary.controllingCriteria && (
                      <div className={styles.metric}>
                        <span className={styles.metricLabel}>Controlling Criteria:</span>
                        <span className={styles.metricValue}>
                          {summaryData.glulamDesign.designCheckResults.summary.controllingCriteria}
                        </span>
                      </div>
                    )}
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Design Method:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.analysisParameters.designMethod}
                      </span>
                    </div>
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Primary Axis:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.analysisParameters.primaryAxis === 'x_axis' ? 'Strong Axis (x-x)' : 'Weak Axis (y-y)'}
                      </span>
                    </div>
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Layup Type:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.analysisParameters.layupType.charAt(0).toUpperCase() + summaryData.glulamDesign.analysisParameters.layupType.slice(1)}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Bending Design Check */}
            {summaryData.glulamDesign.designCheckResults?.designChecks?.bending && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>Bending Design Check</h4>
                <div className={styles.cardContent}>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Ratio (Act/Allow):</span>
                    <span 
                      className={getMetricStyle(
                        summaryData.glulamDesign.designCheckResults.designChecks.bending.passes ? "success" : "error"
                      )}
                      style={getMetricColor(
                        summaryData.glulamDesign.designCheckResults.designChecks.bending.passes ? "success" : "error"
                      )}
                    >
                      {summaryData.glulamDesign.designCheckResults.designChecks.bending.ratio.toFixed(3)} : 1.0
                      {summaryData.glulamDesign.designCheckResults.designChecks.bending.passes ? " ✓" : " ✗"}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual f<sub>b</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.glulamDesign.designCheckResults.designChecks.bending.actualStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable F&apos;<sub>b</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.glulamDesign.designCheckResults.designChecks.bending.allowableStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  {summaryData.glulamDesign.designCheckResults.designChecks.bending.stressType && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Stress Type:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.designChecks.bending.stressType.charAt(0).toUpperCase() + summaryData.glulamDesign.designCheckResults.designChecks.bending.stressType.slice(1)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.analysisParameters?.controllingLoadCombinations?.flexural && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Controlling Load Combo:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.analysisParameters.controllingLoadCombinations.flexural}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Shear Design Check */}
            {summaryData.glulamDesign.designCheckResults?.designChecks?.shear && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>Shear Design Check</h4>
                <div className={styles.cardContent}>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Ratio (Act/Allow):</span>
                    <span 
                      className={getMetricStyle(
                        summaryData.glulamDesign.designCheckResults.designChecks.shear.passes ? "success" : "error"
                      )}
                      style={getMetricColor(
                        summaryData.glulamDesign.designCheckResults.designChecks.shear.passes ? "success" : "error"
                      )}
                    >
                      {summaryData.glulamDesign.designCheckResults.designChecks.shear.ratio.toFixed(3)} : 1.0
                      {summaryData.glulamDesign.designCheckResults.designChecks.shear.passes ? " ✓" : " ✗"}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual fᵥ:</span>
                    <span className={styles.metricValue}>
                      {summaryData.glulamDesign.designCheckResults.designChecks.shear.actualStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable F&apos;<sub>v</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.glulamDesign.designCheckResults.designChecks.shear.allowableStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  {summaryData.glulamDesign.analysisParameters?.controllingLoadCombinations?.shear && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Controlling Load Combo:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.analysisParameters.controllingLoadCombinations.shear}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Deflection Design Check */}
            {summaryData.glulamDesign.designCheckResults?.designChecks?.deflection && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>Deflection Design Check</h4>
                <div className={styles.cardContent}>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Ratio (Limit/Actual):</span>
                    <span 
                      className={getMetricStyle(
                        summaryData.glulamDesign.designCheckResults.designChecks.deflection.passes ? "success" : "error"
                      )}
                      style={getMetricColor(
                        summaryData.glulamDesign.designCheckResults.designChecks.deflection.passes ? "success" : "error"
                      )}
                    >
                      {summaryData.glulamDesign.designCheckResults.designChecks.deflection.ratio.toFixed(0)} : 1.0
                      {summaryData.glulamDesign.designCheckResults.designChecks.deflection.passes ? " ✓" : " ✗"}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual δ:</span>
                    <span className={styles.metricValue}>
                      {summaryData.glulamDesign.designCheckResults.designChecks.deflection.actualDeflection.toFixed(3)} <i className={styles.metricUnit}>{units.deflection}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable δ:</span>
                    <span className={styles.metricValue}>
                      {summaryData.glulamDesign.designCheckResults.designChecks.deflection.allowableDeflection.toFixed(3)} <i className={styles.metricUnit}>{units.deflection}</i>
                    </span>
                  </div>
                  {summaryData.glulamDesign.analysisParameters?.controllingLoadCombinations?.flexural && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Controlling Load Combo:</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.analysisParameters.controllingLoadCombinations.flexural}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Glulam Adjustment Factors Summary */}
            {summaryData.glulamDesign.designCheckResults?.adjustmentFactors && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>NDS Adjustment Factors (Glulam)</h4>
                <div className={styles.cardContent}>
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CD && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Load Duration (C<sub>D</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CD.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CM && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Wet Service (C<sub>M</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CM.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.Ct && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Temperature (C<sub>t</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.Ct.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CL && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Beam Stability (C<sub>L</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CL.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CV && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Volume Factor (C<sub>V</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.CV.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.Cc && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Curvature Factor (C<sub>c</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.Cc.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.glulamDesign.designCheckResults.adjustmentFactors.Cfu && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Flat Use Factor (C<sub>fu</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.glulamDesign.designCheckResults.adjustmentFactors.Cfu.toFixed(3)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className={cn(styles.status.error, "p-3 rounded-md")}>
            Glulam Analysis Error: {summaryData.glulamDesign.error || "Unknown error occurred"}
          </div>
        )}
      </div>
    </div>
  );
} 