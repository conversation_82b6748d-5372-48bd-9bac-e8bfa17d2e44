"use client";

import React from "react";
import { Info } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { styles } from "@/lib/styles/design-system";

interface DesignValueBreakdownProps {
  label: string;
  symbol: string;
  currentValue: number | undefined;
  baseValue: number | undefined;
  units: string;
  adjustmentFactors?: Record<string, number | undefined>;
}

// Mapping of symbols to their explanations
const symbolExplanations: Record<string, string> = {
  "F<sub>b</sub>": "Allowable bending stress - Maximum stress the material can safely sustain in bending",
  "F<sub>t</sub>": "Allowable tension stress parallel to grain - Maximum tensile stress the material can sustain",
  "F<sub>v</sub>": "Allowable shear stress parallel to grain - Maximum shear stress the material can sustain",
  "F<sub>c⊥</sub>": "Allowable compression stress perpendicular to grain - Bearing stress capacity across the grain",
  "F<sub>c</sub>": "Allowable compression stress parallel to grain - Maximum compressive stress along the grain",
  "E": "Modulus of elasticity - Measure of material stiffness (stress/strain ratio)",
  "E<sub>min</sub>": "Minimum modulus of elasticity - Conservative value for stability calculations",
  "G": "Shear modulus - Material stiffness in shear deformation",
  "M<sub>n</sub>": "Nominal flexural strength - Theoretical moment capacity of the cross-section",
  "φM<sub>n</sub> / Ω": "Available flexural strength - Design moment capacity (nominal strength × resistance factor)",
  "V<sub>n</sub>": "Nominal shear strength - Theoretical shear capacity of the cross-section", 
  "φV<sub>n</sub> / Ω": "Available shear strength - Design shear capacity (nominal strength × resistance factor)",
  "C<sub>b</sub>": "Lateral-torsional buckling modification factor - Accounts for moment gradient effects",
  "L<sub>b</sub>": "Unbraced length - Distance between points of lateral support for compression flange",
  "F<sub>y</sub>": "Yield strength - Stress at which material begins to deform permanently",
  "F<sub>u</sub>": "Ultimate tensile strength - Maximum stress material can withstand before failure"
};

// Function to get symbol explanation
const getSymbolExplanation = (symbol: string): string => {
  // Remove any HTML formatting for lookup
  const cleanSymbol = symbol.replace(/<[^>]*>/g, (match) => {
    if (match.includes('sub')) {
      return match.replace('<sub>', '').replace('</sub>', '');
    }
    return match;
  });
  
  const explanation = symbolExplanations[symbol] || symbolExplanations[cleanSymbol];
  return explanation || `${symbol} - Engineering parameter used in structural design calculations`;
};

// Mapping of adjustment factors to their explanations
const adjustmentFactorExplanations: Record<string, string> = {
  // Load duration and service condition factors
  lambda: "Load Duration Factor - Adjusts design values based on the duration of applied loads",
  m: "Moisture Content Factor - Adjusts design values for moisture content in service conditions", 
  t: "Temperature Factor - Adjusts design values for elevated temperature conditions",
  i: "Incising Factor - Adjusts design values for incised lumber to improve preservative penetration",
  
  // Format and resistance factors
  kf: "Format Conversion Factor - Converts between ASD (Allowable Stress Design) and LRFD formats",
  phi: "Resistance Factor - LRFD resistance factor that accounts for material and fabrication uncertainties",
  φ: "Resistance Factor - LRFD resistance factor (φ) that accounts for material variability, fabrication tolerances, and analysis uncertainties. For flexure φ = 0.90, for shear φ = 0.90",
  
  // Stability and geometric factors
  L: "Beam Stability Factor - Adjusts bending stress for lateral-torsional buckling potential",
  l: "Beam Stability Factor - Adjusts bending stress for lateral-torsional buckling potential",
  V: "Volume Factor - Adjusts bending stress based on member volume effects for glulam",
  v: "Volume Factor - Adjusts bending stress based on member volume effects for glulam",
  fu: "Flat Use Factor - Adjusts bending stress when lumber is loaded perpendicular to wide face",
  r: "Repetitive Member Factor - Adjusts bending stress for repetitive framing members spaced 24\" or less",
  F: "Size Factor - Adjusts design values based on cross-sectional dimensions",
  f: "Size Factor - Adjusts design values based on cross-sectional dimensions",
  c: "Curvature Factor - Adjusts bending stress for curved glulam members",
  cc: "Column Stability Factor - Adjusts compression stress for column buckling",
  b: "Lateral-Torsional Buckling Modifier - Factor Cb that accounts for moment gradient effects on lateral-torsional buckling resistance",
  
  // Bearing and support factors  
  p: "Bearing Area Factor - Adjusts compression perpendicular stress for bearing area effects",
  
  // Alternative naming patterns that might be used
  cf: "Size Factor - Adjusts design values based on cross-sectional dimensions",
  cm: "Moisture Content Factor - Adjusts design values for moisture content in service conditions",
  ct: "Temperature Factor - Adjusts design values for elevated temperature conditions",
  ci: "Incising Factor - Adjusts design values for incised lumber to improve preservative penetration",
  cl: "Beam Stability Factor - Adjusts bending stress for lateral-torsional buckling potential",
  cv: "Volume Factor - Adjusts bending stress based on member volume effects for glulam",
  cr: "Repetitive Member Factor - Adjusts bending stress for repetitive framing members spaced 24\" or less",
  cb: "Lateral-Torsional Buckling Modifier - Factor Cb that accounts for moment gradient effects on lateral-torsional buckling resistance",
  cp: "Bearing Area Factor - Adjusts compression perpendicular stress for bearing area effects"
};

export function DesignValueBreakdown({
  label,
  symbol,
  currentValue,
  baseValue,
  units,
  adjustmentFactors
}: DesignValueBreakdownProps) {
  // Filter out factors that are undefined or very close to 1.0 (within 0.001 tolerance)
  const significantFactors = adjustmentFactors 
    ? Object.entries(adjustmentFactors).filter(([_, value]) => 
        value !== undefined && Math.abs(value - 1.0) > 0.001
      )
    : [];

  const getFactorExplanation = (factorName: string): string => {
    // Handle different naming conventions
    let cleanFactorName = factorName.toLowerCase();
    
    // Remove 'C' prefix if present
    if (cleanFactorName.startsWith('c')) {
      cleanFactorName = cleanFactorName.substring(1);
    }
    
    // Look up the explanation
    const explanation = adjustmentFactorExplanations[cleanFactorName];
    
    if (explanation) {
      return explanation;
    }
    
    // Additional fallback mappings for common patterns
    switch (cleanFactorName) {
      case 'lambda':
        return "Load Duration Factor - Adjusts design values based on the duration of applied loads";
      case 'phi':
        return "Resistance Factor - LRFD resistance factor for structural reliability";
      default:
        return `Adjustment factor for ${factorName} - Modifies the base design value based on specific design conditions`;
    }
  };

  return (
    <div className="border-b border-gray-600 pb-3">
      <div className="flex justify-between items-start">
        <div className="flex items-center gap-2">
          <span className="text-white font-medium" dangerouslySetInnerHTML={{ __html: symbol }} />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-3 w-3 text-gray-400 cursor-help flex-shrink-0" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p className="text-sm">{getSymbolExplanation(symbol)}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="text-right">
          <div className="text-base font-medium text-white">
            {currentValue != null && typeof currentValue === 'number' ? 
              (currentValue < 1 ? currentValue.toFixed(3) : currentValue.toFixed(0)) 
              : 'N/A'} <span className="text-sm">{units}</span>
          </div>
          {baseValue != null && typeof baseValue === 'number' && (
            <div className="text-sm text-gray-300 mt-1">
              Base: {baseValue < 1 ? baseValue.toFixed(3) : baseValue.toFixed(0)} {units}
            </div>
          )}
          {significantFactors.length > 0 && (
            <div className="text-xs text-gray-400 mt-1 space-y-0.5">
              {significantFactors.map(([factor, value]) => (
                <div key={factor} className="flex items-center gap-1">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help flex-shrink-0" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p className="text-sm">{getFactorExplanation(factor)}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <span>
                    × C<sub>{factor.replace('C', '').toLowerCase()}</sub> = {value!.toFixed(3)}  
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 