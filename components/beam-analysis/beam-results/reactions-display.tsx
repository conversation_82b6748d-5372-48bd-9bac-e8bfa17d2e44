"use client";

import React, { useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { LoadType } from "@/lib/types/load/load-type";
import { BeamData } from "@/lib/types/beam/beam-data";
import { SummaryData } from "@/lib/types/analysis/analysis-output";
import { Support } from "@/lib/types/support/support";
import { styles } from "@/lib/styles/design-system";

interface ReactionsDisplayProps {
  beamData: BeamData;
  summaryData: SummaryData;
  unitSystem: UnitSystem;
  isLoading: boolean;
  error: string | null;
}

// Define Load Type Symbols
const loadTypeSymbols: Record<LoadType, string> = {
  [LoadType.DEAD]: "D",
  [LoadType.LIVE]: "L",
  [LoadType.ROOF_LIVE]: "Lr",
  [LoadType.SNOW]: "S",
  [LoadType.WIND]: "W",
  [LoadType.EARTHQUAKE]: "E",
  [LoadType.RAIN]: "R",
  [LoadType.SOIL]: "H", 
  [LoadType.FLOOD]: "F",
  [LoadType.TEMPERATURE]: "T",
};

// Get active load types based on beam data (Helper function)
const getActiveLoadTypes = (beamData: BeamData): LoadType[] => {
  const activeTypes = new Set<LoadType>();
  beamData.loadGroups?.forEach(group => {
    group.loads?.forEach(load => {
      activeTypes.add(load.loadType);
    });
  });
  // Define a standard order
  const orderedTypes = [
    LoadType.DEAD, LoadType.LIVE, LoadType.ROOF_LIVE, LoadType.SNOW,
    LoadType.WIND, LoadType.EARTHQUAKE, LoadType.RAIN, LoadType.SOIL,
    LoadType.FLOOD, LoadType.TEMPERATURE
  ];
  return orderedTypes.filter(type => activeTypes.has(type));
};

// Helper function to map support positions to actual node labels from server response
const createSupportToNodeMapping = (
  supports: Support[], 
  summaryData: SummaryData
): Map<number, string> => {
  const mapping = new Map<number, string>();
  
  if (!summaryData?.supportReactions?.forces) {
    return mapping;
  }
  
  // Get all available node labels from the server response
  const availableNodeLabels = Object.keys(summaryData.supportReactions.forces);
  
  // For each support, find the closest matching node label
  // This handles cases where FEM solver creates non-sequential node labels
  supports.forEach((support, supportIndex) => {
    // Try different strategies to find the matching node label
    
    // Strategy 1: Try sequential mapping first (supportIndex + 1)
    const sequentialLabel = (supportIndex + 1).toString();
    if (availableNodeLabels.includes(sequentialLabel)) {
      mapping.set(supportIndex, sequentialLabel);
      return;
    }
    
    // Strategy 2: For 2-support beam, map to available labels in order
    if (supports.length === 2 && availableNodeLabels.length >= 2) {
      // Sort node labels numerically and map in order
      const sortedLabels = availableNodeLabels.sort((a, b) => parseInt(a) - parseInt(b));
      if (supportIndex < sortedLabels.length) {
        mapping.set(supportIndex, sortedLabels[supportIndex]);
        return;
      }
    }
    
    // Strategy 3: Try to find by position-based logic (fallback)
    // For simple beams, first support maps to lowest node number, last to highest
    if (supportIndex === 0 && availableNodeLabels.length > 0) {
      const minLabel = availableNodeLabels.reduce((min, label) => 
        parseInt(label) < parseInt(min) ? label : min
      );
      mapping.set(supportIndex, minLabel);
    } else if (supportIndex === supports.length - 1 && availableNodeLabels.length > 0) {
      const maxLabel = availableNodeLabels.reduce((max, label) => 
        parseInt(label) > parseInt(max) ? label : max
      );
      mapping.set(supportIndex, maxLabel);
    }
  });
  
  return mapping;
};

export function ReactionsDisplay({ 
  beamData, 
  summaryData, 
  unitSystem, 
  isLoading, 
  error 
}: ReactionsDisplayProps) {
  const units = getUnitsBySystem(unitSystem);
  const activeLoadTypes = useMemo(() => getActiveLoadTypes(beamData), [beamData]);
  
  // Create mapping from support index to actual node labels
  const supportToNodeMapping = useMemo(() => 
    createSupportToNodeMapping(beamData.supports || [], summaryData), 
    [beamData.supports, summaryData]
  );

  return (
    <div className={styles.section}>
      <h3 className={styles.heading.h3}>Reactions</h3>
      <Tabs defaultValue="support-reactions" className="w-full">
        <TabsList className={styles.tabs}>
          <TabsTrigger value="support-reactions" className={styles.tabTrigger}>
            Envelop Results
          </TabsTrigger>
          <TabsTrigger value="individual-loads" className={styles.tabTrigger}>
            Individual Loads
          </TabsTrigger>
        </TabsList>
        <TabsContent value="support-reactions" className="mt-4">
          <div className="overflow-x-auto">
            <Table className={styles.table}>
              <TableHeader>
                <TableRow>
                  <TableHead className={styles.tableHeader}>
                    Support (# @ Pos)
                  </TableHead>
                  <TableHead className={styles.tableHeader}>
                    Max Up Force ({units.force})
                  </TableHead>
                  <TableHead className={styles.tableHeader}>
                    Max Down Force ({units.force})
                  </TableHead>
                  <TableHead className={styles.tableHeader}>
                    Max + Moment ({units.moment})
                  </TableHead>
                  <TableHead className={styles.tableHeader}>
                    Max - Moment ({units.moment})
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {beamData.supports &&
                beamData.supports.length > 0 &&
                summaryData?.supportReactions &&
                Object.keys(summaryData.supportReactions.forces).length > 0 ? (
                  beamData.supports.map(
                    (support: Support, supportIndex: number) => {
                      // Use the proper mapping instead of assuming sequential labels
                      const nodeLabel = supportToNodeMapping.get(supportIndex);
                      const reactionsForNode = nodeLabel ? summaryData.supportReactions.forces[nodeLabel] : undefined;
                      const momentsForNode = nodeLabel ? summaryData.supportReactions.moments[nodeLabel] : undefined;

                      const formatReaction = (value: number | undefined, combo: string | undefined) => {
                        if (combo === "N/A" || value === undefined || !isFinite(value)) return "---";
                        return `${Math.abs(value).toFixed(2)} (${combo || 'N/A'})`;
                      };

                      return (
                        <TableRow key={supportIndex}>
                          <TableCell className={styles.tableCell}>
                            {supportIndex + 1}{" "}
                            <span className={styles.tableCellInfo}>
                              (@ {support.position.toFixed(2)} {units.length})
                            </span>
                          </TableCell>
                          <TableCell className={styles.tableCell}>
                            {formatReaction(reactionsForNode?.maxUp, reactionsForNode?.maxUpCombo)}
                          </TableCell>
                          <TableCell className={styles.tableCell}>
                            {formatReaction(reactionsForNode?.maxDown, reactionsForNode?.maxDownCombo)}
                          </TableCell>
                          <TableCell className={styles.tableCell}>
                            {formatReaction(momentsForNode?.maxPositive, momentsForNode?.maxPosCombo)}
                          </TableCell>
                          <TableCell className={styles.tableCell}>
                            {formatReaction(momentsForNode?.maxNegative, momentsForNode?.maxNegCombo)}
                          </TableCell>
                        </TableRow>
                      );
                    }
                  )
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className={styles.tableCell}
                    >
                      {beamData.supports && beamData.supports.length === 0
                        ? "No Supports Defined"
                        : isLoading ? "Calculating..." : (error ? "Calculation Error" : "No Reactions Calculated")}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="individual-loads" className="mt-4">
          <div className="overflow-x-auto">
            <Table className={styles.table}>
              <TableHeader>
                <TableRow>
                  <TableHead className={styles.tableHeader}>
                    Support (# @ Pos)
                  </TableHead>
                  {activeLoadTypes.map(loadType => (
                    <TableHead key={loadType} className={styles.tableHeader}>
                       {loadTypeSymbols[loadType]} ({units.force})
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                 {beamData.supports && beamData.supports.length > 0 ? (
                   beamData.supports.map((support, supportIndex) => {
                      // Use the proper mapping instead of assuming sequential labels
                      const nodeLabel = supportToNodeMapping.get(supportIndex);
                      
                      return (
                        <TableRow key={supportIndex}>
                          <TableCell className={styles.tableCell}>
                            {supportIndex + 1}{" "}
                            <span className={styles.tableCellInfo}>
                              (@ {support.position.toFixed(2)} {units.length})
                            </span>
                          </TableCell>
                          {activeLoadTypes.map(loadType => {
                             // Access the actual calculated reaction data using proper node label
                             const individualReactionsForNode = nodeLabel ? summaryData?.individualLoadReactions?.forces?.[nodeLabel] : undefined;
                             const reactionValue = individualReactionsForNode?.[loadType];

                             // Keep the existing display logic
                             const displayValue = (reactionValue !== undefined && isFinite(reactionValue))
                                ? reactionValue.toFixed(2)
                                : "---";

                            return (
                              <TableCell key={loadType} className={styles.tableCell}>
                                {isLoading ? "..." : displayValue}
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      );
                   })
                 ) : (
                    <TableRow>
                      <TableCell
                        colSpan={1 + activeLoadTypes.length} // Adjust colspan
                        className={styles.tableCell}
                      >
                         {beamData.supports && beamData.supports.length === 0
                          ? "No Supports Defined"
                          : isLoading ? "Calculating..." : (error ? "Calculation Error" : "Individual Load Reactions Unavailable")}
                      </TableCell>
                    </TableRow>
                 )}
              </TableBody>
            </Table>
           </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 