"use client";

import React, { useState } from "react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { SummaryData } from "@/lib/types/analysis/analysis-output";
import { DesignValueBreakdown } from "./design-value-breakdown";
import { styles, getMetricStyle, getCardStyle, getMetricColor } from "@/lib/styles/design-system";
import { cn } from "@/lib/utils";

interface SawnLumberDisplayProps {
  beamPropertiesState: BeamPropertiesState;
  summaryData: SummaryData;
  unitSystem: UnitSystem;
}

export function SawnLumberDisplay({ 
  beamPropertiesState, 
  summaryData, 
  unitSystem 
}: SawnLumberDisplayProps) {
  const units = getUnitsBySystem(unitSystem);
  const [isDesignValuesExpanded, setIsDesignValuesExpanded] = useState(false);

  if (!summaryData.sawnLumberDesign) {
    return null;
  }

  return (
    <div className={styles.section}>
      <h3 className={styles.heading.h3}>Material & Design Properties</h3>
      
      {/* Basic Material & Section Properties */}
      <div className={cn(getCardStyle())}>
        <h4 className={styles.cardHeader}>Section Properties</h4>
        <div className={styles.cardContent}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Type:</span>
                <span className="text-sm">Sawn Lumber</span>
              </div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Grade:</span>
                <span className="text-sm">{beamPropertiesState.selectedGrade || "N/A"}</span>
              </div>
            </div>
            <div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Species:</span>
                <span className="text-sm">{beamPropertiesState.selectedSpecies || "N/A"}</span>
              </div>
              <div className="flex justify-between py-1">
                <span className="text-sm font-medium">Size:</span>
                <span className="text-sm">{beamPropertiesState.selectedNominalSize || "N/A"}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Collapsible Detailed Current Design Values */}
      {summaryData.sawnLumberDesign.designCheckResults?.woodAnalysis && (
        <div className={cn(getCardStyle(), "col-span-full", "mt-4")}>
          <div 
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setIsDesignValuesExpanded(!isDesignValuesExpanded)}
          >
            <h4 className={styles.heading.h4}>Current Design Values</h4>
            <button 
              type="button"
              className="text-white hover:text-blue-300 transition-colors p-1"
              aria-label={isDesignValuesExpanded ? "Collapse design values" : "Expand design values"}
            >
              <svg 
                className={`w-5 h-5 transform transition-transform ${isDesignValuesExpanded ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
          
          {isDesignValuesExpanded && (
            <div className={styles.cardContent}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Strength Properties */}
                <div>
                  <h5 className="text-base font-semibold text-white mb-4">Strength Properties</h5>
                  <div className="space-y-4">
                    {/* Tension Parallel to Grain (Ft) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Ft && (
                      <DesignValueBreakdown
                        label="Tension Parallel to Grain"
                        symbol="F<sub>t</sub>"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.Ft_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Ft}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.Ft}
                      />
                    )}

                    {/* Bending (Fb) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fb && (
                      <DesignValueBreakdown
                        label="Bending"
                        symbol="F<sub>b</sub>"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.Fb_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fb}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.Fb}
                      />
                    )}

                    {/* Shear (Fv) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fv && (
                      <DesignValueBreakdown
                        label="Shear Parallel to Grain"
                        symbol="F<sub>v</sub>"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.Fv_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fv}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.Fv}
                      />
                    )}

                    {/* Compression Perpendicular (Fc⊥) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fc_perp && (
                      <DesignValueBreakdown
                        label="Compression Perpendicular to Grain"
                        symbol="F<sub>c⊥</sub>"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.Fc_perp_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fc_perp}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.Fc_perp}
                      />
                    )}

                    {/* Compression Parallel (Fc) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Fc && (
                      <DesignValueBreakdown
                        label="Compression Parallel to Grain"
                        symbol="F<sub>c</sub>"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.Fc_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Fc}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.Fc}
                      />
                    )}
                  </div>
                </div>

                {/* Elastic Properties */}
                <div>
                  <h5 className="text-base font-semibold text-white mb-4">Elastic Properties</h5>
                  <div className="space-y-4">
                    {/* Modulus of Elasticity (E) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.E && (
                      <DesignValueBreakdown
                        label="Modulus of Elasticity"
                        symbol="E"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.E_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.E}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.E}
                      />
                    )}

                    {/* Minimum Modulus of Elasticity (Emin) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.Emin && (
                      <DesignValueBreakdown
                        label="Minimum Modulus of Elasticity"
                        symbol="E<sub>min</sub>"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.adjustedDesignValues?.Emin_prime}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.Emin}
                        units={units.pressure}
                        adjustmentFactors={summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors?.Emin}
                      />
                    )}

                    {/* Shear Modulus (G) */}
                    {summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs?.material?.referenceDesignValues?.G && (
                      <DesignValueBreakdown
                        label="Shear Modulus"
                        symbol="G"
                        currentValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.G}
                        baseValue={summaryData.sawnLumberDesign.designCheckResults.woodAnalysis.inputs.material.referenceDesignValues.G}
                        units={units.pressure}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Sawn Lumber NDS Design Results */}
      <div className={styles.section}>
        <h3 className={styles.heading.h3}>Sawn Lumber Design Analysis (NDS)</h3>
        {summaryData.sawnLumberDesign.success ? (
          <div className={styles.grid.dense}>
            {/* Design Summary Card */}
            <div className={getCardStyle()}>
              <h4 className={styles.cardHeader}>Design Summary</h4>
              <div className={styles.cardContent}>
                {summaryData.sawnLumberDesign.designCheckResults?.summary && (
                  <>
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Overall Design:</span>
                      <span 
                        className={getMetricStyle(
                          summaryData.sawnLumberDesign.designCheckResults.summary.isDesignAcceptable ? "success" : "error"
                        )}
                        style={getMetricColor(
                          summaryData.sawnLumberDesign.designCheckResults.summary.isDesignAcceptable ? "success" : "error"
                        )}
                      >
                        {summaryData.sawnLumberDesign.designCheckResults.summary.isDesignAcceptable ? "PASSES" : "FAILS"}
                      </span>
                    </div>
                    {summaryData.sawnLumberDesign.designCheckResults.summary.controllingFactor && (
                      <div className={styles.metric}>
                        <span className={styles.metricLabel}>Controlling Factor:</span>
                        <span className={styles.metricValue}>
                          {summaryData.sawnLumberDesign.designCheckResults.summary.controllingFactor}
                        </span>
                      </div>
                    )}
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Design Method:</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.analysisParameters.designMethod}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Bending Design Check */}
            {summaryData.sawnLumberDesign.designCheckResults?.designChecks?.bending && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>Bending Design Check</h4>
                <div className={styles.cardContent}>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Ratio (Act/Allow):</span>
                    <span 
                      className={getMetricStyle(
                        summaryData.sawnLumberDesign.designCheckResults.designChecks.bending.passes ? "success" : "error"
                      )}
                      style={getMetricColor(
                        summaryData.sawnLumberDesign.designCheckResults.designChecks.bending.passes ? "success" : "error"
                      )}
                    >
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.bending.ratio.toFixed(3)} : 1.0
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.bending.passes ? " ✓" : " ✗"}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual f<sub>b</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.bending.actualStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable F&apos;<sub>b</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.bending.allowableStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  {summaryData.sawnLumberDesign.analysisParameters?.controllingLoadCombinations?.flexural && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Controlling Load Combo:</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.analysisParameters.controllingLoadCombinations.flexural}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Shear Design Check */}
            {summaryData.sawnLumberDesign.designCheckResults?.designChecks?.shear && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>Shear Design Check</h4>
                <div className={styles.cardContent}>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Ratio (Act/Allow):</span>
                    <span 
                      className={getMetricStyle(
                        summaryData.sawnLumberDesign.designCheckResults.designChecks.shear.passes ? "success" : "error"
                      )}
                      style={getMetricColor(
                        summaryData.sawnLumberDesign.designCheckResults.designChecks.shear.passes ? "success" : "error"
                      )}
                    >
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.shear.ratio.toFixed(3)} : 1.0
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.shear.passes ? " ✓" : " ✗"}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual fᵥ:</span>
                    <span className={styles.metricValue}>
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.shear.actualStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable F&apos;<sub>v</sub>:</span>
                    <span className={styles.metricValue}>
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.shear.allowableStress.toFixed(2)} <i className={styles.metricUnit}>{units.pressure}</i>
                    </span>
                  </div>
                  {summaryData.sawnLumberDesign.analysisParameters?.controllingLoadCombinations?.shear && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Controlling Load Combo:</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.analysisParameters.controllingLoadCombinations.shear}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Deflection Design Check */}
            {summaryData.sawnLumberDesign.designCheckResults?.designChecks?.deflection && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>Deflection Design Check</h4>
                <div className={styles.cardContent}>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Ratio (Limit/Actual):</span>
                    <span 
                      className={getMetricStyle(
                        summaryData.sawnLumberDesign.designCheckResults.designChecks.deflection.passes ? "success" : "error"
                      )}
                      style={getMetricColor(
                        summaryData.sawnLumberDesign.designCheckResults.designChecks.deflection.passes ? "success" : "error"
                      )}
                    >
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.deflection.ratio.toFixed(0)} : 1.0
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.deflection.passes ? " ✓" : " ✗"}
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Actual δ:</span>
                    <span className={styles.metricValue}>
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.deflection.actualDeflection.toFixed(3)} <i className={styles.metricUnit}>{units.deflection}</i>
                    </span>
                  </div>
                  <div className={styles.metric}>
                    <span className={styles.metricLabel}>Allowable δ:</span>
                    <span className={styles.metricValue}>
                      {summaryData.sawnLumberDesign.designCheckResults.designChecks.deflection.allowableDeflection.toFixed(3)} <i className={styles.metricUnit}>{units.deflection}</i>
                    </span>
                  </div>
                  {summaryData.sawnLumberDesign.analysisParameters?.controllingLoadCombinations?.flexural && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Controlling Load Combo:</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.analysisParameters.controllingLoadCombinations.flexural}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Adjustment Factors Summary */}
            {summaryData.sawnLumberDesign.designCheckResults?.adjustmentFactors && (
              <div className={getCardStyle()}>
                <h4 className={styles.cardHeader}>NDS Adjustment Factors</h4>
                <div className={styles.cardContent}>
                  {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CD && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Load Duration (C<sub>D</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CD.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CM && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Wet Service (C<sub>M</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CM.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.Ct && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Temperature (C<sub>t</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.Ct.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CL && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Beam Stability (C<sub>L</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CL.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CF && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Size Factor (C<sub>F</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.CF.toFixed(3)}
                      </span>
                    </div>
                  )}
                  {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.Cr && (
                    <div className={styles.metric}>
                      <span className={styles.metricLabel}>Repetitive Member (C<sub>r</sub>):</span>
                      <span className={styles.metricValue}>
                        {summaryData.sawnLumberDesign.designCheckResults.adjustmentFactors.Cr.toFixed(3)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className={cn(styles.status.error, "p-3 rounded-md")}>
            Sawn Lumber Analysis Error: {summaryData.sawnLumberDesign.error || "Unknown error occurred"}
          </div>
        )}
      </div>
    </div>
  );
} 