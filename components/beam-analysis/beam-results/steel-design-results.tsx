import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, ChevronUp, ChevronDown } from 'lucide-react';
import { SteelDesignAnalysis } from '@/lib/types/analysis/analysis-output';
import { DesignValueBreakdown } from './design-value-breakdown';
import { styles, getCardStyle } from "@/lib/styles/design-system";
import { cn } from "@/lib/utils";

interface SteelDesignResultsProps {
  steelDesign: SteelDesignAnalysis;
}

export function SteelDesignResults({ steelDesign }: SteelDesignResultsProps) {
  const [showDesignValues, setShowDesignValues] = React.useState(false);

  if (!steelDesign.success) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <XCircle className="h-5 w-5" />
            Steel Design Analysis Failed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{steelDesign.error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const { designCheckResults, analysisParameters } = steelDesign;
  const { flexure, shear, deflection, stiffnessAdjustments, overallAdequate } = designCheckResults;

  const formatNumber = (num: number, decimals: number = 2) => {
    return Number(num).toFixed(decimals);
  };

  const formatDCR = (dcr: number) => {
    // Use scientific notation for very small numbers to avoid showing 0.000
    if (dcr < 0.001 && dcr > 0) {
      return dcr.toExponential(2);
    }
    return formatNumber(dcr, 3);
  };

  const getStatusIcon = (adequate: boolean) => {
    return adequate ? (
      <CheckCircle className="h-4 w-4 text-[#6eb245]" />
    ) : (
      <XCircle className="h-4 w-4 text-[#f28a5e]" />
    );
  };

  const getStatusBadge = (adequate: boolean) => {
    return (
      <Badge 
        variant={adequate ? "default" : "destructive"}
        className={adequate ? "bg-[#6eb245] hover:bg-[#6eb245]/80" : "bg-[#f28a5e] hover:bg-[#f28a5e]/80"}
      >
        {adequate ? "PASS" : "FAIL"}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Current Design Values Section */}
      <div className={cn(getCardStyle(), "col-span-full", "mt-4")}>
        <div 
          className="flex items-center justify-between cursor-pointer"
          onClick={() => setShowDesignValues(!showDesignValues)}
        >
          <h4 className={styles.heading.h4}>Current Design Values</h4>
          <button 
            type="button"
            className="text-white hover:text-blue-300 transition-colors p-1"
            aria-label={showDesignValues ? "Collapse design values" : "Expand design values"}
          >
            <svg 
              className={`w-5 h-5 transform transition-transform ${showDesignValues ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
        
        {showDesignValues && (
          <div className={styles.cardContent}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Strength Properties */}
              <div>
                <h5 className="text-base font-semibold text-white mb-4">Strength Properties</h5>
                <div className="space-y-4">
                  <DesignValueBreakdown
                    label="Flexural Nominal Strength"
                    symbol="M<sub>n</sub>"
                    currentValue={flexure?.nominalStrength}
                    baseValue={flexure?.nominalStrength}
                    units="kip-in"
                  />
                  <DesignValueBreakdown
                    label="Flexural Available Strength"
                    symbol="φM<sub>n</sub> / Ω"
                    currentValue={flexure?.availableStrength}
                    baseValue={flexure?.nominalStrength}
                    units="kip-in"
                    adjustmentFactors={{
                      'φ': flexure?.nominalStrength ? flexure.availableStrength / flexure.nominalStrength : undefined
                    }}
                  />
                  <DesignValueBreakdown
                    label="Shear Nominal Strength"
                    symbol="V<sub>n</sub>"
                    currentValue={shear?.nominalStrength}
                    baseValue={shear?.nominalStrength}
                    units="kips"
                  />
                  <DesignValueBreakdown
                    label="Shear Available Strength"
                    symbol="φV<sub>n</sub> / Ω"
                    currentValue={shear?.availableStrength}
                    baseValue={shear?.nominalStrength}
                    units="kips"
                    adjustmentFactors={{
                      'φ': shear?.nominalStrength ? shear.availableStrength / shear.nominalStrength : undefined
                    }}
                  />
                </div>
              </div>

              {/* Analysis Parameters and Factors */}
              <div>
                <h5 className="text-base font-semibold text-white mb-4">Analysis Parameters</h5>
                <div className="space-y-4">
                  <DesignValueBreakdown
                    label="Modulus of Elasticity"
                    symbol="E"
                    currentValue={29000}
                    baseValue={29000}
                    units="ksi"
                  />
                  <DesignValueBreakdown
                    label="Lateral-Torsional Buckling Modifier"
                    symbol="C<sub>b</sub>"
                    currentValue={analysisParameters.lateralTorsionalBucklingModifier}
                    baseValue={1.0}
                    units=""
                    adjustmentFactors={{
                      'b': analysisParameters.lateralTorsionalBucklingModifier
                    }}
                  />
                  <DesignValueBreakdown
                    label="Unbraced Length"
                    symbol="L<sub>b</sub>"
                    currentValue={analysisParameters.unbrancedLength}
                    baseValue={analysisParameters.unbrancedLength}
                    units="in"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Flexural Design Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              {getStatusIcon(flexure.adequate)}
              Flexural Design Check
            </span>
            {getStatusBadge(flexure.adequate)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Governing Limit State</div>
              <div className="text-lg">{flexure.governingLimitState}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Demand-to-Capacity Ratio</div>
              <div className={`text-lg font-bold ${flexure.adequate ? 'text-[#6eb245]' : 'text-[#f28a5e]'}`}>
                {formatDCR(flexure.dcr)}
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-2 border-t">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Nominal Strength</div>
              <div className="text-base">{formatNumber(flexure.nominalStrength)} kip-in</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Available Strength</div>
              <div className="text-base">{formatNumber(flexure.availableStrength)} kip-in</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Applied Moment</div>
              <div className="text-base">{formatNumber(flexure.demand)} kip-in</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Load Combo</div>
              <div className="text-base">{analysisParameters.controllingLoadCombinations.flexural}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shear Design Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              {getStatusIcon(shear.adequate)}
              Shear Design Check
            </span>
            {getStatusBadge(shear.adequate)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Governing Limit State</div>
              <div className="text-lg">{shear.governingLimitState}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Demand-to-Capacity Ratio</div>
              <div className={`text-lg font-bold ${shear.adequate ? 'text-[#6eb245]' : 'text-[#f28a5e]'}`}>
                {formatDCR(shear.dcr)}
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-2 border-t">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Nominal Strength</div>
              <div className="text-base">{formatNumber(shear.nominalStrength)} kips</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Available Strength</div>
              <div className="text-base">{formatNumber(shear.availableStrength)} kips</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Applied Shear</div>
              <div className="text-base">{formatNumber(shear.demand)} kips</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Load Combo</div>
              <div className="text-base">{analysisParameters.controllingLoadCombinations.shear}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deflection Check Results */}
      {deflection && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                {getStatusIcon(deflection.overallAdequate)}
                Deflection Check
              </span>
              {getStatusBadge(deflection.overallAdequate)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Beam Length</div>
                <div className="text-lg">{formatNumber(deflection.beamLength)} in</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Deflection Limit</div>
                <div className="text-lg">L/{deflection.deflectionLimit}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Controlling Direction</div>
                <div className={`text-lg font-medium ${deflection.overallAdequate ? 'text-[#6eb245]' : 'text-[#f28a5e]'}`}>
                  {deflection.controllingDirection.charAt(0).toUpperCase() + deflection.controllingDirection.slice(1)}
                </div>
              </div>
            </div>

            {/* Deflection Details */}
            <div className="space-y-2 pt-2 border-t">
              <h4 className="font-medium text-muted-foreground">Deflection Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Maximum Deflection</div>
                  <div className="text-base">
                    {formatNumber(Math.max(
                      Math.abs(deflection.upward.actualDeflection), 
                      Math.abs(deflection.downward.actualDeflection)
                    ))} in
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Allowable Deflection</div>
                  <div className="text-base">{formatNumber(deflection.upward.allowableDeflection)} in</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Deflection Ratio</div>
                  <div className="text-base">
                    {(() => {
                      const upwardLOverDelta = deflection.upward.lOverDelta;
                      const downwardLOverDelta = deflection.downward.lOverDelta;
                      const minLOverDelta = Math.min(upwardLOverDelta, downwardLOverDelta);
                      
                      console.log("Deflection Ratio Debug:", {
                        upwardLOverDelta,
                        downwardLOverDelta,
                        minLOverDelta,
                        controllingDirection: deflection.controllingDirection,
                        upwardDeflection: deflection.upward.actualDeflection,
                        downwardDeflection: deflection.downward.actualDeflection
                      });
                      
                      // Use the controlling direction's lOverDelta instead of min
                      const controllingLOverDelta = deflection.controllingDirection === 'upward' 
                        ? upwardLOverDelta 
                        : downwardLOverDelta;
                      
                      const displayValue = isFinite(controllingLOverDelta) && controllingLOverDelta > 0 
                        ? controllingLOverDelta 
                        : minLOverDelta;
                      
                      return `L/${formatNumber(displayValue)}`;
                    })()}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Status</div>
                  <div className={`text-base font-medium ${deflection.overallAdequate ? 'text-[#6eb245]' : 'text-[#f28a5e]'}`}>
                    {deflection.overallAdequate ? 'OK' : 'FAIL'}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stiffness Adjustments */}
      {stiffnessAdjustments && stiffnessAdjustments.applied && (
        <Card>
          <CardHeader>
            <CardTitle>Stiffness Adjustments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 bg-muted rounded">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Base Reduction Factor</div>
                <div className="text-base">{formatNumber(stiffnessAdjustments.baseReductionFactor, 3)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Tau B Factor</div>
                <div className="text-base">{formatNumber(stiffnessAdjustments.tauB, 3)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Total Flexural Reduction</div>
                <div className="text-base">{formatNumber(stiffnessAdjustments.totalFlexuralReduction, 3)}</div>
              </div>
            </div>
            {stiffnessAdjustments.adjustedSection && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3 p-3 bg-muted rounded">
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Adjusted Ix</div>
                  <div className="text-base">{formatNumber(stiffnessAdjustments.adjustedSection.IxAdjusted)} in⁴</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Adjusted Iy</div>
                  <div className="text-base">{formatNumber(stiffnessAdjustments.adjustedSection.IyAdjusted)} in⁴</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}