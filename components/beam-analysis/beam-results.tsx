"use client";

import { useState, useMemo } from "react";
import { BeamData } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { ResultChart } from "@/components/shared/result-chart";
import { DataPoint } from "@/lib/types/analysis/analysis-results";
import { DiagramData, SummaryData } from "@/lib/types/analysis/analysis-output";
import Spinner from "@/app/components/ui/Spinner";
import { STRING_CONSTANTS, UI_CONSTANTS, FORM_CONSTANTS } from "@/components/constants";

// Define the specific error message for "no loads"
// const NO_LOADS_ERROR_MESSAGE = "Calculation failed: No load combinations generated any results (possibly no relevant loads applied)."; // No longer needed

interface MaxResult {
  value: number;
  position: number;
  loadCombo: string;
}

interface BeamResultsProps {
  isLoading: boolean;
  error: string | null;
  diagramData: DiagramData | null;
  summaryData: SummaryData | null;
  unitSystem: UnitSystem;
  beamData: BeamData; // Add beamData to props
}

export function BeamResults({ 
  isLoading, 
  error, 
  diagramData, 
  summaryData,
  unitSystem, 
  beamData // Destructure beamData
  }: BeamResultsProps) {
    const envelopeResults = useMemo(() => {
    const defaultEnvelope = {
      shear: { value: NaN, position: NaN, loadCombo: STRING_CONSTANTS.MESSAGES.N_A },
      moment: { value: NaN, position: NaN, loadCombo: STRING_CONSTANTS.MESSAGES.N_A },
      deflection: { value: NaN, position: NaN, loadCombo: STRING_CONSTANTS.MESSAGES.N_A },
    };

    if (error) {
      return {
          shear: { value: NaN, position: NaN, loadCombo: STRING_CONSTANTS.MESSAGES.ERROR_STATUS },
          moment: { value: NaN, position: NaN, loadCombo: STRING_CONSTANTS.MESSAGES.ERROR_STATUS },
          deflection: { value: NaN, position: NaN, loadCombo: STRING_CONSTANTS.MESSAGES.ERROR_STATUS },
      };
    }

    if (summaryData) {
        const downAbs = Math.abs(summaryData.maxTotalDeflectionDownward.value);
        const upAbs = Math.abs(summaryData.maxTotalDeflectionUpward.value);
        const controllingDeflection = (downAbs >= upAbs) 
            ? summaryData.maxTotalDeflectionDownward 
            : summaryData.maxTotalDeflectionUpward;

      return {
        shear: { 
          value: summaryData.maxShearValue?.value ?? NaN,
          position: summaryData.maxShearValue?.position ?? NaN,
          loadCombo: summaryData.maxShearValue?.loadComboName ?? STRING_CONSTANTS.MESSAGES.N_A,
        },
        moment: {
          value: summaryData.maxMomentValue?.value ?? NaN,
          position: summaryData.maxMomentValue?.position ?? NaN,
          loadCombo: summaryData.maxMomentValue?.loadComboName ?? STRING_CONSTANTS.MESSAGES.N_A,
        },
        deflection: {
           value: controllingDeflection?.value ?? NaN, 
          position: controllingDeflection?.position ?? NaN,
          loadCombo: controllingDeflection?.loadComboName ?? STRING_CONSTANTS.MESSAGES.N_A,
        },
      };
    } 
    return defaultEnvelope; 

  }, [summaryData, error]);

  const units = useMemo(() => getUnitsBySystem(unitSystem), [unitSystem]);

  const formatMaxValue = (value: number): string => {
    if (!isFinite(value)) return STRING_CONSTANTS.MESSAGES.ERROR_STATUS;
    return Math.abs(value) < FORM_CONSTANTS.VALIDATION.MIN_VALUE_THRESHOLD && value !== 0
      ? value.toExponential(FORM_CONSTANTS.DECIMAL_PRECISION.EXPONENTIAL)
      : value.toFixed(FORM_CONSTANTS.DECIMAL_PRECISION.TWO);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-4">
        <Spinner loading={true} size={40} />
        <div className="text-center mt-4">
          <h3 className="text-lg font-semibold text-gray-900">Generating Diagrams</h3>
          <p className="text-sm text-gray-600 mt-1">
            Creating shear, moment, and deflection diagrams...
          </p>
        </div>
      </div>
    );
  }

  // Check for presence of loads first
  const hasLoads = beamData && beamData.loadGroups && beamData.loadGroups.some(group => group.loads && group.loads.length > 0);

  if (!hasLoads) {
    return (
      <div className="p-6 text-center text-gray-600 bg-blue-50 border border-blue-200 rounded-md">
        Add Loads to see calculation.
      </div>
    );
  }

  // Case 2: Loads exist but calculation fails (any other error)
  if (error) {
    return (
      <div className="p-4 text-red-600 bg-red-50 border border-red-200 rounded-md">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="h-[180px]">
        <div className="flex justify-between items-center mb-1">
          <h3 className="text-sm font-medium">Shear Force ({units.force})</h3>
          <span
            className={`text-sm ${ 
              envelopeResults.shear.loadCombo === "Error"
                ? "text-red-500"
                : "text-muted-foreground"
            }`}
          >
            Max: {formatMaxValue(envelopeResults.shear.value)} @{` `}
            {isFinite(envelopeResults.shear.position)
              ? envelopeResults.shear.position.toFixed(2)
              : "N/A"}
            {` `} {units.length}
            <span className="text-xs">
              ({envelopeResults.shear.loadCombo || "N/A"})
            </span>
          </span>
        </div>
        <ResultChart type="shear" data={diagramData?.shear || []} units={units} />
      </div>

      <div className="h-[180px]">
        <div className="flex justify-between items-center mb-1">
          <h3 className="text-sm font-medium">
            Bending Moment ({units.moment})
          </h3>
          <span
             className={`text-sm ${ 
              envelopeResults.moment.loadCombo === "Error"
                ? "text-red-500"
                : "text-muted-foreground"
            }`}
          >
            Max: {formatMaxValue(envelopeResults.moment.value)} @{` `}
            {isFinite(envelopeResults.moment.position)
              ? envelopeResults.moment.position.toFixed(2)
              : "N/A"}
            {` `} {units.length}
            <span className="text-xs">
              ({envelopeResults.moment.loadCombo || "N/A"})
            </span>
          </span>
        </div>
        <ResultChart type="moment" data={diagramData?.moment || []} units={units} />
      </div>

      <div className="h-[180px]">
        <div className="flex justify-between items-center mb-1">
          <h3 className="text-sm font-medium">
            Deflection ({units.deflection})
          </h3>
           <span
             className={`text-sm ${ 
              envelopeResults.deflection.loadCombo === "Error"
                ? "text-red-500"
                : "text-muted-foreground"
            }`}
          >
            Max: {formatMaxValue(envelopeResults.deflection.value)} @{` `}
            {isFinite(envelopeResults.deflection.position)
              ? envelopeResults.deflection.position.toFixed(2)
              : "N/A"}
            {` `} {units.length}
            <span className="text-xs">
              ({envelopeResults.deflection.loadCombo || "N/A"})
            </span>
          </span>
        </div>
        <ResultChart
          type="deflection"
          data={diagramData?.deflection || []}
          units={units}
        />
      </div>
    </div>
  );
}
