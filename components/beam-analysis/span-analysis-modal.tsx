"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { BarChart4 } from "lucide-react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { DataPoint } from "@/lib/types/analysis/analysis-results";

interface MaxValue {
  value: number;
  position: number;
}

interface SpanAnalysisModalProps {
  spanNumber: number;
  spanStart: number;
  spanEnd: number;
  unitSystem: UnitSystem;
  shearData: DataPoint[];
  momentData: DataPoint[];
  deflectionData: DataPoint[];
  maxShear: MaxValue;
  maxMoment: MaxValue;
  maxDeflection: MaxValue;
}

const chartColors = {
  deflection: "hsl(221, 83%, 53%)", // Blue
  shear: "hsl(142, 71%, 45%)",      // Green
  moment: "hsl(0, 84%, 60%)"        // Red
};

export function SpanAnalysisModal({
  spanNumber,
  spanStart,
  spanEnd,
  unitSystem,
  shearData,
  momentData,
  deflectionData,
  maxShear,
  maxMoment,
  maxDeflection
}: SpanAnalysisModalProps) {
  const units = getUnitsBySystem(unitSystem);

  const formatValue = (value: number): string => {
    if (!isFinite(value)) return "N/A";
    return Math.abs(value) < 0.01 && value !== 0
      ? value.toExponential(2)
      : value.toFixed(2);
  };

  const formatPosition = (value: number): string => {
     if (!isFinite(value)) return "N/A";
     return value.toFixed(2);
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
         <button aria-label={`Analyze Span ${spanNumber}`}>
            <BarChart4 className="h-4 w-4 text-muted-foreground hover:text-primary cursor-pointer" />
         </button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Span {spanNumber} Analysis ({spanStart.toFixed(2)} to {spanEnd.toFixed(2)} {units.length})</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="h-[180px]">
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-sm font-medium">Deflection ({units.deflection})</h3>
              <span className="text-sm text-muted-foreground">
                Max: {formatValue(maxDeflection.value)} @ {formatPosition(maxDeflection.position)} {units.length}
              </span>
            </div>
            <ResponsiveContainer>
              <LineChart data={deflectionData} margin={{ top: 5, right: 5, left: 5, bottom: 15 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="x"
                  type="number"
                  domain={[spanStart, spanEnd]}
                  label={{ value: `Position (${units.length})`, position: "insideBottom", offset: -10, style: { fontSize: 11 } }}
                  tick={{ fontSize: 10 }}
                  tickFormatter={(tick) => tick.toFixed(1)}
                />
                <YAxis tick={{ fontSize: 10 }} width={50} tickFormatter={formatValue} />
                <Tooltip formatter={(value: number) => [formatValue(value), "Deflection"]} labelFormatter={(label) => `Position: ${label.toFixed(2)}`} />
                <Line type="monotone" dataKey="value" stroke={chartColors.deflection} dot={false} strokeWidth={2}/>
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="h-[180px]">
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-sm font-medium">Shear ({units.force})</h3>
              <span className="text-sm text-muted-foreground">
                 Max: {formatValue(maxShear.value)} @ {formatPosition(maxShear.position)} {units.length}
              </span>
            </div>
            <ResponsiveContainer>
              <LineChart data={shearData} margin={{ top: 5, right: 5, left: 5, bottom: 15 }}>
                 <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="x"
                  type="number"
                  domain={[spanStart, spanEnd]}
                  label={{ value: `Position (${units.length})`, position: "insideBottom", offset: -10, style: { fontSize: 11 } }}
                  tick={{ fontSize: 10 }}
                  tickFormatter={(tick) => tick.toFixed(1)}
                />
                <YAxis tick={{ fontSize: 10 }} width={50} tickFormatter={formatValue}/>
                <Tooltip formatter={(value: number) => [formatValue(value), "Shear"]} labelFormatter={(label) => `Position: ${label.toFixed(2)}`} />
                <Line type="monotone" dataKey="value" stroke={chartColors.shear} dot={false} strokeWidth={2}/>
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="h-[180px]">
            <div className="flex justify-between items-center mb-1">
              <h3 className="text-sm font-medium">Moment ({units.moment})</h3>
              <span className="text-sm text-muted-foreground">
                Max: {formatValue(maxMoment.value)} @ {formatPosition(maxMoment.position)} {units.length}
              </span>
            </div>
            <ResponsiveContainer>
              <LineChart data={momentData} margin={{ top: 5, right: 5, left: 5, bottom: 15 }}>
                <CartesianGrid strokeDasharray="3 3" />
                 <XAxis
                  dataKey="x"
                  type="number"
                  domain={[spanStart, spanEnd]}
                  label={{ value: `Position (${units.length})`, position: "insideBottom", offset: -10, style: { fontSize: 11 } }}
                  tick={{ fontSize: 10 }}
                  tickFormatter={(tick) => tick.toFixed(1)}
                />
                <YAxis tick={{ fontSize: 10 }} width={50} tickFormatter={formatValue}/>
                <Tooltip formatter={(value: number) => [formatValue(value), "Moment"]} labelFormatter={(label) => `Position: ${label.toFixed(2)}`}/>
                <Line type="monotone" dataKey="value" stroke={chartColors.moment} dot={false} strokeWidth={2}/>
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}