"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { materials } from "@/lib/materials";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { 
  SawnLumber, 
  MATERIAL_TYPES, 
  LUMBER_TYPES,
  MATERIAL_LABELS 
} from "@/components/materials";
import { GluedLaminated } from "./beam-properties/glued-laminated";
import { Steel } from "./beam-properties/steel";
import type { BeamPropertiesState, BeamProperties as BeamPropertiesType, GluLamSelectedProperties, AdjustmentFactorSet } from "@/lib/types/beam/beam-data";
import {
  DEFAULT_TOTAL_DEFLECTION_LIMIT,
  DEFAULT_LIVE_DEFLECTION_LIMIT,
  DEFAULT_FB_ALLOW_PLACEHOLDER,
  DEFAULT_FV_ALLOW_PLACEHOLDER,
  DEFAULT_ADJUSTMENT_FACTORS,
  DEFAULT_TEMPERATURE,
} from "@/lib/constants/beam-constants";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Switch } from "@/components/ui/switch";

// Table5ARow might not be needed here anymore if GluedLaminated handles its data fully
// import type { Table5ARow } from '@/hooks/use-wood-data'; 
import { calculateRectangularSectionProps } from "@/lib/utils/section-calculations";
// calculateVolumeFactorCv and getDesignValueFromTables will be used within GluedLaminated.tsx
// import { calculateVolumeFactorCv } from "@/lib/utils/timber-design-factors";
// import { ksiToPsi } from "@/lib/utils/metric-converter";


// This type defines the more comprehensive payload from GluedLaminated
export interface ProcessedGluLamData {
  selectedProperties: GluLamSelectedProperties | null; // The selections made in GluedLaminated
  designValues: BeamPropertiesState['designValues'];   // Fully adjusted design values
  volumeFactorCv: number | null;
  wetServiceFactors: AdjustmentFactorSet; // The specific wet service factors used
  // other factors like CL could be added if GluedLaminated calculates them
}


interface BeamPropertiesProps {
  properties: BeamPropertiesType;
  unitSystem: UnitSystem;
  onChange: (properties: BeamPropertiesType) => void;
  beamPropertiesState: BeamPropertiesState;
  onBeamPropertiesStateChange: (state: BeamPropertiesState) => void;
  onIncludeBeamWeightChange: (include: boolean) => void;
  onLumberTypeChange: (type: "sawn" | "glulam") => void;
}

// getDesignValueFromTables is no longer needed here, will be in GluedLaminated.tsx

export function BeamProperties({ properties, unitSystem, onChange, beamPropertiesState, onBeamPropertiesStateChange, onIncludeBeamWeightChange, onLumberTypeChange }: BeamPropertiesProps) {
  const [isLimitsOpen, setIsLimitsOpen] = useState(false);
  const [isMaterialsOpen, setIsMaterialsOpen] = useState(true);
  // Initialize isManualMode from beamPropertiesState, defaulting to false for new analyses
  const [isManualMode, setIsManualMode] = useState(
    () => beamPropertiesState.isManualMode || false
  );
  
  // Add state for manual species and grade
  const [manualSpecies, setManualSpecies] = useState(
    () => beamPropertiesState.manual_species || ""
  );
  const [manualGrade, setManualGrade] = useState(
    () => beamPropertiesState.manual_grade || ""
  );
  
  // Add state for manual material type
  const [manualMaterialType, setManualMaterialType] = useState(
    () => beamPropertiesState.manual_material_type || "Sawn Lumber"
  );

  // Add state for material selection (steel vs wood)
  const [selectedMaterial, setSelectedMaterial] = useState<"steel" | "wood">(
    beamPropertiesState.selectedMaterial || MATERIAL_TYPES.WOOD
  );

  // Sync local isManualMode state when beamPropertiesState.isManualMode changes (e.g., when loading)
  useEffect(() => {
    const newValue = beamPropertiesState.isManualMode || false;
    if (isManualMode !== newValue) {
      setIsManualMode(newValue);
    }
  }, [beamPropertiesState.isManualMode, isManualMode]);

  // Sync manual species and grade when beamPropertiesState changes (e.g., when loading)
  useEffect(() => {
    const newValue = beamPropertiesState.manual_species || "";
    if (manualSpecies !== newValue) {
      setManualSpecies(newValue);
    }
  }, [beamPropertiesState.manual_species, manualSpecies]);

  useEffect(() => {
    const newValue = beamPropertiesState.manual_grade || "";
    if (manualGrade !== newValue) {
      setManualGrade(newValue);
    }
  }, [beamPropertiesState.manual_grade, manualGrade]);

  useEffect(() => {
    const newValue = beamPropertiesState.manual_material_type || "Sawn Lumber";
    if (manualMaterialType !== newValue) {
      setManualMaterialType(newValue);
    }
  }, [beamPropertiesState.manual_material_type, manualMaterialType]);

  // Sync selectedMaterial when beamPropertiesState.selectedMaterial changes (e.g., when loading)
  useEffect(() => {
    const newValue = beamPropertiesState.selectedMaterial || "wood";
    if (selectedMaterial !== newValue) {
      setSelectedMaterial(newValue);
    }
  }, [beamPropertiesState.selectedMaterial, selectedMaterial]);

  // Handle manual mode toggle
  const handleManualModeChange = (enabled: boolean) => {
    setIsManualMode(enabled);
    
    // Save the manual mode state to beamPropertiesState
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      isManualMode: enabled,
    });
    
    if (enabled) {
      // When switching to manual mode, populate species and grade from current state
      let currentSpecies = "";
      let currentGrade = "";
      let currentMaterialType: "Sawn Lumber" | "Glulam" | "Steel" = "Sawn Lumber";
      
      if (beamPropertiesState.lumberType === "sawn") {
        currentSpecies = beamPropertiesState.selectedSpecies || "";
        currentGrade = beamPropertiesState.selectedGrade || "";
        currentMaterialType = "Sawn Lumber";
      } else if (beamPropertiesState.lumberType === "glulam" && beamPropertiesState.selectedGluLamProperties) {
        currentSpecies = `${beamPropertiesState.selectedGluLamProperties.speciesGroup} - ${beamPropertiesState.selectedGluLamProperties.species}`;
        currentGrade = beamPropertiesState.selectedGluLamProperties.selectedTable5ADetail?.stress_class || "";
        currentMaterialType = "Glulam";
      }
      
      setManualSpecies(currentSpecies);
      setManualGrade(currentGrade);
      setManualMaterialType(currentMaterialType);
      
      // Clear lumber properties so they don't interfere
      onBeamPropertiesStateChange({
        ...beamPropertiesState,
        isManualMode: enabled,
        selectedGluLamProperties: null,
        designValues: null,
        lumberProperties: null,
        volumeFactorCv: null,
        manual_species: currentSpecies,
        manual_grade: currentGrade,
        manual_material_type: currentMaterialType,
      });
    }
  };

  const units = getUnitsBySystem(unitSystem);
  const lumberType = beamPropertiesState.lumberType;

  // Debounced function to update beam properties
  const debouncedUpdateProperties = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout | null = null;
      
      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        timeoutId = setTimeout(() => {
          const { manualWidth, manualDepth, lumberProperties, selectedGluLamProperties, designValues, manual_E } = beamPropertiesState;
          const currentLumberType = beamPropertiesState.lumberType;
          const currentMaterial = beamPropertiesState.selectedMaterial;
          
          let effectiveArea: number = 0;
          let effectiveIxx: number = 0;
          let effectiveE: number = 0; // Initialize E to 0

          let calculated_manual_Area: number | null = null;
          let calculated_manual_Ixx: number | null = null;
          let calculated_manual_Iyy: number | null = null;
          let calculated_manual_Sxx: number | null = null;
          let calculated_manual_Syy: number | null = null;

          const manualW = parseFloat(manualWidth);
          const manualD = parseFloat(manualDepth);
          const hasValidManualNum = !isNaN(manualW) && manualW > 0 && !isNaN(manualD) && manualD > 0;

          // Section Properties (Area, Ix) calculation
          if (isManualMode) {
            // In manual mode, ALWAYS use manual width/depth if valid
            if (hasValidManualNum) {
              const calculatedProps = calculateRectangularSectionProps(manualW, manualD);
              if (calculatedProps) {
                effectiveArea = calculatedProps.area;
                effectiveIxx = calculatedProps.Ix;
                // Store these for beamPropertiesState internal update
                calculated_manual_Area = calculatedProps.area;
                calculated_manual_Ixx = calculatedProps.Ix;
                calculated_manual_Iyy = calculatedProps.Iy;
                calculated_manual_Sxx = calculatedProps.Sx;
                calculated_manual_Syy = calculatedProps.Sy;
              }
            }
          } else {
            // In automatic mode, handle steel vs wood differently
            if (currentMaterial === MATERIAL_TYPES.STEEL) {
              // For steel, use the existing properties (set by steel component)
              // Don't override with manual dimensions
              effectiveArea = properties.area;
              effectiveIxx = properties.momentOfInertia;
                      } else {
            // For wood, prioritize lumber/glulam properties over manual dimensions
            // Only use manual dimensions if we're explicitly in manual mode or no lumber properties exist
            if (hasValidManualNum && (isManualMode || (!lumberProperties && !selectedGluLamProperties))) {
              const calculatedProps = calculateRectangularSectionProps(manualW, manualD);
              if (calculatedProps) {
                effectiveArea = calculatedProps.area;
                effectiveIxx = calculatedProps.Ix;
                // Store these for beamPropertiesState internal update
                calculated_manual_Area = calculatedProps.area;
                calculated_manual_Ixx = calculatedProps.Ix;
                calculated_manual_Iyy = calculatedProps.Iy;
                calculated_manual_Sxx = calculatedProps.Sx;
                calculated_manual_Syy = calculatedProps.Sy;
              }
            } else {
              // Use lumber/glulam properties from the child components
              effectiveArea = properties.area;
              effectiveIxx = properties.momentOfInertia;
            }
          }
          }

          // Elastic Modulus (E) determination
          if (isManualMode) {
            // In manual mode, prioritize manual_E
            if (manual_E && manual_E > 0) {
              effectiveE = manual_E;
            }
          } else {
            // In automatic mode, handle steel vs wood differently
            if (currentMaterial === MATERIAL_TYPES.STEEL) {
              // For steel, use the existing elastic modulus (set by steel component)
              // Only override if manually specified in steel mode
              if (manual_E && manual_E > 0 && manual_E !== materials.steel.elasticModulus[unitSystem]) {
                effectiveE = manual_E;
              } else {
                effectiveE = properties.elasticModulus;
              }
            } else {
              // For wood, use the existing logic
              if (manual_E && manual_E > 0) {
                effectiveE = manual_E;
              } else if (designValues?.adjusted_E && designValues.adjusted_E > 0) {
                effectiveE = designValues.adjusted_E;
              } else if (currentLumberType === LUMBER_TYPES.SAWN && lumberProperties?.modulus_of_elasticity_e_psi && lumberProperties.modulus_of_elasticity_e_psi > 0) {
                effectiveE = lumberProperties.modulus_of_elasticity_e_psi;
                              } else if (currentLumberType === LUMBER_TYPES.GLULAM && selectedGluLamProperties?.selectedTable5ADetail) {
                const orientation = selectedGluLamProperties.loadingOrientation || 'perpendicular';
                const tableE_ksi = (orientation === 'perpendicular'
                                  ? selectedGluLamProperties.selectedTable5ADetail.E_xapp_ksi
                                  : selectedGluLamProperties.selectedTable5ADetail.E_yapp_ksi);
                if (tableE_ksi && tableE_ksi > 0) { // Check if tableE_ksi is valid
                     effectiveE = tableE_ksi * 1000000;
                }
              }
            }
          }
          // If none of the above, effectiveE remains 0
          
          const manualStateNeedsUpdate = 
              (calculated_manual_Area !== null && Math.abs((calculated_manual_Area || 0) - (beamPropertiesState.manual_Area || 0)) > 1e-9) ||
              (calculated_manual_Ixx !== null && Math.abs((calculated_manual_Ixx || 0) - (beamPropertiesState.manual_Ixx || 0)) > 1e-9) ||
              (calculated_manual_Iyy !== null && Math.abs((calculated_manual_Iyy || 0) - (beamPropertiesState.manual_Iyy || 0)) > 1e-9) ||
              (calculated_manual_Sxx !== null && Math.abs((calculated_manual_Sxx || 0) - (beamPropertiesState.manual_Sxx || 0)) > 1e-9) ||
              (calculated_manual_Syy !== null && Math.abs((calculated_manual_Syy || 0) - (beamPropertiesState.manual_Syy || 0)) > 1e-9);

          if (manualStateNeedsUpdate) {
              onBeamPropertiesStateChange({
                  ...beamPropertiesState,
                  manual_Area: calculated_manual_Area ?? beamPropertiesState.manual_Area,
                  manual_Ixx: calculated_manual_Ixx ?? beamPropertiesState.manual_Ixx,
                  manual_Iyy: calculated_manual_Iyy ?? beamPropertiesState.manual_Iyy,
                  manual_Sxx: calculated_manual_Sxx ?? beamPropertiesState.manual_Sxx,
                  manual_Syy: calculated_manual_Syy ?? beamPropertiesState.manual_Syy,
                  isManualMode: isManualMode, // Preserve manual mode state
              });
          }

          const parentPropsNeedUpdate = 
              (Math.abs(effectiveArea - properties.area) > 1e-6 ||
              Math.abs(effectiveIxx - properties.momentOfInertia) > 1e-6 ||
              Math.abs(effectiveE - properties.elasticModulus) > 1e-6);

          // In manual mode, always allow updates if we have any effective values
          // In automatic mode, only update if we have valid lumber properties or manual dimensions
          const shouldUpdate = isManualMode ? 
            (effectiveArea > 0 || effectiveIxx > 0 || effectiveE > 0) :
            (effectiveArea > 1e-9 || effectiveIxx > 1e-9 || effectiveE > 1e-9 || currentLumberType === LUMBER_TYPES.GLULAM || hasValidManualNum);

          if (parentPropsNeedUpdate && shouldUpdate) {
              console.log("BeamProperties - Updating parent with:", {
                currentMaterial,
                isManualMode,
                area: effectiveArea,
                momentOfInertia: effectiveIxx,
                elasticModulus: effectiveE,
                existingProperties: {
                  area: properties.area,
                  momentOfInertia: properties.momentOfInertia,
                  elasticModulus: properties.elasticModulus
                }
              });
              
              onChange({
                  length: properties.length, // Ensure length is preserved
                  area: effectiveArea,
                  momentOfInertia: effectiveIxx, 
                  elasticModulus: effectiveE,
              });
          }
        }, 200); // 200ms debounce
      };
    })(),
    [beamPropertiesState, isManualMode, properties, onChange, onBeamPropertiesStateChange]
  );

  useEffect(() => {
    debouncedUpdateProperties();
  }, [
      isManualMode,
      beamPropertiesState.manualWidth,
      beamPropertiesState.manualDepth,
      beamPropertiesState.lumberProperties,
      beamPropertiesState.selectedGluLamProperties,
      beamPropertiesState.designValues,
      beamPropertiesState.manual_E,
      beamPropertiesState.manual_Area, 
      beamPropertiesState.manual_Ixx,
      beamPropertiesState.manual_Iyy,
      beamPropertiesState.manual_Sxx,
      beamPropertiesState.manual_Syy,
      beamPropertiesState.lumberType,
      properties.area, 
      properties.momentOfInertia,
      properties.elasticModulus,
      properties.length,
      debouncedUpdateProperties,
    ]);

  const handleMaterialChange = (value: "steel" | "wood") => {
    setSelectedMaterial(value);
    if (value === MATERIAL_TYPES.STEEL) {
      // Set steel elastic modulus
      onChange({ 
        ...properties, 
        elasticModulus: materials.steel.elasticModulus[unitSystem],
      });
      onBeamPropertiesStateChange({
        ...beamPropertiesState,
        manual_E: materials.steel.elasticModulus[unitSystem],
        selectedMaterial: value,
        isManualMode: isManualMode,
      });
    } else {
      // Wood: E will come from SawnLumber/GluedLaminated or manual input
      // Clear all steel-related properties to ensure clean state
      const newState = { ...beamPropertiesState };
      
      // Remove steel-specific properties
      delete (newState as any).selectedSteelGrade;
      delete (newState as any).selectedSteelShape;
      delete (newState as any).selectedSteelShapeSize;
      delete (newState as any).steelSectionProperties;
      delete (newState as any).steelDesignValues;
      
      if (beamPropertiesState.manual_E === materials.steel.elasticModulus[unitSystem]){
        onBeamPropertiesStateChange({ 
          ...newState, 
          manual_E: null,
          selectedMaterial: value,
          isManualMode: isManualMode,
        });
      } else {
        onBeamPropertiesStateChange({ 
          ...newState, 
          selectedMaterial: value,
          isManualMode: isManualMode,
        });
      }
    }
  };

  const handleGluLamPropertiesChange = (data: ProcessedGluLamData | null) => {
    // Skip updating lumber properties if in manual mode
    if (isManualMode) {
      return;
    }
    
    if (data) {
      onBeamPropertiesStateChange({
        ...beamPropertiesState,
        selectedGluLamProperties: data.selectedProperties,
        designValues: data.designValues,
        volumeFactorCv: data.volumeFactorCv,
        wetServiceFactor: data.wetServiceFactors,
        // DON'T override manual values when not in manual mode - keep existing manual values
        lumberProperties: null, // Clear sawn lumber properties
      });
    } else { // Glulam selection cleared
      onBeamPropertiesStateChange({
        ...beamPropertiesState,
        selectedGluLamProperties: null,
        designValues: null,
        volumeFactorCv: null,
        wetServiceFactor: DEFAULT_ADJUSTMENT_FACTORS,
        // Keep manual values as they are
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="length">Beam Length ({units.length})</Label>
          <Input
            id="length"
            type="number"
            value={properties.length || ''}
            onChange={(e) => {
              const value = e.target.value;
              onChange({ ...properties, length: value === '' ? 0 : parseFloat(value) });
            }}
            min={0.1}
            step={0.1}
          />
        </div>

        {/* Include beam weight switch - always visible */}
        <div className="flex items-center space-x-2 mt-2">
          <label htmlFor="includeBeamWeight" className="text-sm">Include beam&apos;s weight</label>
          <Switch
            id="includeBeamWeight"
            checked={!!beamPropertiesState.includeBeamWeight}
            onCheckedChange={checked => {
              onBeamPropertiesStateChange({
                ...beamPropertiesState,
                includeBeamWeight: checked,
                isManualMode: isManualMode, // Preserve manual mode state
              });
              onIncludeBeamWeightChange(checked);
            }}
          />
        </div>
      </div>

      {/* Manual Input Mode */}
      <div className="grid grid-cols-2 gap-4">
        <div>{/* Empty div for spacing/alignment if needed */}
          <div className="flex items-center justify-between">
            <Label htmlFor="manualMode" className="text-sm">Manual Input Mode</Label>
            <Switch
              id="manualMode"
              checked={isManualMode}
              onCheckedChange={handleManualModeChange}
            />
          </div>
        </div>
      </div>

      {/* Manual Input Mode Section */}
      {isManualMode && (
        <div className="space-y-4 border rounded-md p-4 bg-gray-50 dark:bg-gray-800">
          <h4 className="text-sm font-medium mb-2">Manual Input</h4>
          
          {/* Material Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="manualMaterialType">Material Type</Label>
            <Select 
              value={manualMaterialType} 
              onValueChange={(value: "Sawn Lumber" | "Glulam" | "Steel") => {
                setManualMaterialType(value);
                onBeamPropertiesStateChange({
                  ...beamPropertiesState,
                  manual_material_type: value,
                  isManualMode: isManualMode,
                });
              }}
            >
              <SelectTrigger id="manualMaterialType">
                <SelectValue placeholder="Select material type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Sawn Lumber">Sawn Lumber</SelectItem>
                <SelectItem value="Glulam">Glued Laminated Timber</SelectItem>
                <SelectItem value="Steel">Steel</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Manual Width and Depth */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="manualWidth">Width ({units.size})</Label>
              <Input
                id="manualWidth"
                type="number"
                placeholder="Enter width"
                value={beamPropertiesState.manualWidth || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  const numValue = parseFloat(value);
                  onBeamPropertiesStateChange({
                    ...beamPropertiesState,
                    manualWidth: value,
                    isManualMode: isManualMode, // Preserve manual mode state
                  });
                  
                  // The useEffect will handle property updates automatically
                }}
                min={0}
                step={0.1}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="manualDepth">Depth ({units.size})</Label>
              <Input
                id="manualDepth"
                type="number"
                placeholder="Enter depth"
                value={beamPropertiesState.manualDepth || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  const numValue = parseFloat(value);
                  onBeamPropertiesStateChange({
                    ...beamPropertiesState,
                    manualDepth: value,
                    isManualMode: isManualMode, // Preserve manual mode state
                  });
                  
                  // The useEffect will handle property updates automatically
                }}
                min={0}
                step={0.1}
              />
            </div>
          </div>

          {/* Manual Species and Grade */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="manualSpecies">Species</Label>
              <Input
                id="manualSpecies"
                type="text"
                placeholder="e.g., Douglas Fir-Larch"
                value={manualSpecies}
                onChange={(e) => {
                  setManualSpecies(e.target.value);
                  onBeamPropertiesStateChange({
                    ...beamPropertiesState,
                    manual_species: e.target.value,
                    isManualMode: isManualMode, // Preserve manual mode state
                  });
                }}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="manualGrade">Grade</Label>
              <Input
                id="manualGrade"
                type="text"
                placeholder="e.g., No.1, 24F-V4"
                value={manualGrade}
                onChange={(e) => {
                  setManualGrade(e.target.value);
                  onBeamPropertiesStateChange({
                    ...beamPropertiesState,
                    manual_grade: e.target.value,
                    isManualMode: isManualMode, // Preserve manual mode state
                  });
                }}
              />
            </div>
          </div>

          {/* Manual Elastic Modulus */}
          <div className="space-y-2">
            <Label htmlFor="elasticModulus">Elastic Modulus ({units.pressure})</Label>
            <Input
              id="elasticModulus"
              type="number"
              value={beamPropertiesState.manual_E !== null && beamPropertiesState.manual_E !== undefined 
                     ? beamPropertiesState.manual_E 
                     : ''}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '') {
                  onBeamPropertiesStateChange({
                    ...beamPropertiesState,
                    manual_E: null,
                    isManualMode: isManualMode, // Preserve manual mode state
                  });
                  return;
                }
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                  onBeamPropertiesStateChange({
                    ...beamPropertiesState,
                    manual_E: numValue,
                    isManualMode: isManualMode, // Preserve manual mode state
                  });
                  // The useEffect will handle property updates automatically
                }
              }}
              min={0.1}
              step={0.1}
              placeholder="Enter elastic modulus"
            />
          </div>

          {/* Manual Limits */}
          <Collapsible 
            open={isLimitsOpen} 
            onOpenChange={setIsLimitsOpen} 
            className="space-y-2 border rounded-md p-4"
          >
            <CollapsibleTrigger asChild>
              <button className="flex w-full items-center justify-between py-2 text-sm font-medium">
                Limits
                {isLimitsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manual_Fb_allow">Allowable Fb ({units.pressure})</Label>
                  <Input
                    id="manual_Fb_allow"
                    type="number"
                    value={beamPropertiesState.manual_Fb_allow !== null ? beamPropertiesState.manual_Fb_allow : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      onBeamPropertiesStateChange({
                        ...beamPropertiesState,
                        manual_Fb_allow: value === '' ? null : parseFloat(value),
                        isManualMode: isManualMode, // Preserve manual mode state
                      });
                    }}
                    min={0}
                    step={10}
                    placeholder="Manual override"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manual_Fv_allow">Allowable Fv ({units.pressure})</Label>
                  <Input
                    id="manual_Fv_allow"
                    type="number"
                    value={beamPropertiesState.manual_Fv_allow !== null ? beamPropertiesState.manual_Fv_allow : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      onBeamPropertiesStateChange({
                        ...beamPropertiesState,
                        manual_Fv_allow: value === '' ? null : parseFloat(value),
                        isManualMode: isManualMode, // Preserve manual mode state
                      });
                    }}
                    min={0}
                    step={10}
                    placeholder="Manual override"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manual_E_min">Allowable Emin ({units.pressure})</Label>
                  <Input
                    id="manual_E_min"
                    type="number"
                    value={beamPropertiesState.manual_E_min !== null ? beamPropertiesState.manual_E_min : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      onBeamPropertiesStateChange({
                        ...beamPropertiesState,
                        manual_E_min: value === '' ? null : parseFloat(value),
                        isManualMode: isManualMode, // Preserve manual mode state
                      });
                    }}
                    min={0}
                    step={10}
                    placeholder="Manual override for Emin"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="manual_maxStressRatioLimit">Max Stress Ratio Limit</Label>
                  <Input
                    id="manual_maxStressRatioLimit"
                    type="number"
                    value={beamPropertiesState.manual_maxStressRatioLimit !== null ? beamPropertiesState.manual_maxStressRatioLimit : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === '') {
                        onBeamPropertiesStateChange({
                          ...beamPropertiesState,
                          manual_maxStressRatioLimit: null,
                          isManualMode: isManualMode, // Preserve manual mode state
                        });
                      } else {
                        const parsedValue = parseFloat(value);
                        if (!isNaN(parsedValue) && parsedValue >= 0) {
                          onBeamPropertiesStateChange({
                            ...beamPropertiesState,
                            manual_maxStressRatioLimit: parsedValue,
                            isManualMode: isManualMode, // Preserve manual mode state
                          });
                        }
                      }
                    }}
                    min={0}
                    step={0.05}
                    placeholder="e.g., 1.0"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manual_totalDeflectionLimit">Min Total Defl. Ratio (L/X)</Label>
                  <Input
                    id="manual_totalDeflectionLimit"
                    type="number"
                    value={beamPropertiesState.manual_totalDeflectionLimit !== null ? beamPropertiesState.manual_totalDeflectionLimit : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      onBeamPropertiesStateChange({
                        ...beamPropertiesState,
                        manual_totalDeflectionLimit: value === '' ? null : parseFloat(value),
                        isManualMode: isManualMode, // Preserve manual mode state
                      });
                    }}
                    min={1}
                    step={10}
                    placeholder="e.g., 240"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manual_liveDeflectionLimit">Min Live Defl. Ratio (L/X)</Label>
                  <Input
                    id="manual_liveDeflectionLimit"
                    type="number"
                    value={beamPropertiesState.manual_liveDeflectionLimit !== null ? beamPropertiesState.manual_liveDeflectionLimit : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      onBeamPropertiesStateChange({
                        ...beamPropertiesState,
                        manual_liveDeflectionLimit: value === '' ? null : parseFloat(value),
                        isManualMode: isManualMode, // Preserve manual mode state
                      });
                    }}
                    min={1}
                    step={10}
                    placeholder="e.g., 360"
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      )}

      {/* Materials Section - only show when NOT in manual mode */}
      {!isManualMode && (
        <Collapsible 
          open={isMaterialsOpen} 
          onOpenChange={setIsMaterialsOpen} 
          className="space-y-2 border rounded-md p-4"
        >
           <CollapsibleTrigger asChild>
            <button className="flex w-full items-center justify-between py-2 text-sm font-medium">
              Materials
              {isMaterialsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Material</Label>
                <Select 
                  value={selectedMaterial} 
                  onValueChange={(value: "steel" | "wood") => {
                    setSelectedMaterial(value);
                    handleMaterialChange(value);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select material" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={MATERIAL_TYPES.WOOD}>Wood</SelectItem>
                    <SelectItem value={MATERIAL_TYPES.STEEL}>Steel</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedMaterial === MATERIAL_TYPES.WOOD && (
                <div className="space-y-2">
                  <Label>Lumber Type</Label>
                  <Select value={lumberType} onValueChange={onLumberTypeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select lumber type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={LUMBER_TYPES.SAWN}>Sawn Lumber</SelectItem>
                      <SelectItem value={LUMBER_TYPES.GLULAM}>Glued Laminated Timber</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* Steel Component */}
            {selectedMaterial === MATERIAL_TYPES.STEEL && (
              <Steel
                properties={properties}
                unitSystem={unitSystem}
                onChange={onChange}
                beamPropertiesState={beamPropertiesState}
                onBeamPropertiesStateChange={onBeamPropertiesStateChange}
              />
            )}

            {/* Wood-specific sections */}
            {selectedMaterial === MATERIAL_TYPES.WOOD && (
              <>
                {/* Shared Adjustment Factor Controls */}
                <div className="space-y-4 pt-4 border-t mt-4">
                  <h4 className="text-sm font-medium mb-2">General Adjustments</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3">
                    {/* Repetitive Member */}
                    <div className="flex items-center justify-between">
                      <Label htmlFor="isRepetitiveMember" className="text-sm flex-shrink-0" dangerouslySetInnerHTML={{ __html: "Repetitive Member (C<sub>r</sub>)" }} />
                      <Switch
                        id="isRepetitiveMember"
                        checked={beamPropertiesState.isRepetitiveMember}
                        onCheckedChange={(checked) =>
                          onBeamPropertiesStateChange({
                            ...beamPropertiesState,
                            isRepetitiveMember: checked,
                          })
                        }
                      />
                    </div>

                    {/* Incising Factor */}
                    <div className="flex items-center justify-between">
                      <Label htmlFor="isIncised" className="text-sm flex-shrink-0" dangerouslySetInnerHTML={{ __html: "Incised Lumber (C<sub>i</sub>)" }} />
                      <Switch
                        id="isIncised"
                        checked={beamPropertiesState.isIncised}
                        onCheckedChange={(checked) =>
                          onBeamPropertiesStateChange({
                            ...beamPropertiesState,
                            isIncised: checked,
                          })
                        }
                      />
                    </div>

                    {/* Wet Service & Moisture Content */}
                    <div className="flex items-center justify-between col-span-1 md:col-span-2">
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="isWetService" className="text-sm flex-shrink-0" dangerouslySetInnerHTML={{ __html: "Wet Service (C<sub>M</sub>)" }} />
                        <Switch
                          id="isWetService"
                          checked={beamPropertiesState.isWetService}
                          onCheckedChange={(checked) =>
                            onBeamPropertiesStateChange({
                              ...beamPropertiesState,
                              isWetService: checked,
                            })
                          }
                        />
                      </div>
                      {beamPropertiesState.isWetService && (
                        <div className="flex items-center space-x-2 ml-4">
                          <Label htmlFor="moistureContent" className="text-sm flex-shrink-0 whitespace-nowrap">Moisture Content</Label>
                          <Input
                            id="moistureContent"
                            type="number"
                            min={0}
                            max={100}
                            step={0.1}
                            value={beamPropertiesState.moistureContent ?? 19} // Default to 19% if null
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              onBeamPropertiesStateChange({
                                ...beamPropertiesState,
                                moistureContent: isNaN(value) ? null : value,
                              });
                            }}
                            className="w-20 h-8 text-xs"
                          />
                          <span className="text-sm text-muted-foreground">%</span>
                        </div>
                      )}
                    </div>

                    {/* Temperature Factor & Input */}
                    <div className="flex items-center justify-between col-span-1 md:col-span-2">
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="isTemperatureFactored" className="text-sm flex-shrink-0" dangerouslySetInnerHTML={{ __html: "Temperature Effects (C<sub>t</sub>)" }} />
                        <Switch
                          id="isTemperatureFactored"
                          checked={beamPropertiesState.isTemperatureFactored}
                          onCheckedChange={(checked) =>
                            onBeamPropertiesStateChange({
                              ...beamPropertiesState,
                              isTemperatureFactored: checked,
                            })
                          }
                        />
                      </div>
                      {beamPropertiesState.isTemperatureFactored && (
                        <div className="flex items-center space-x-2 ml-4">
                          <Label htmlFor="temperature" className="text-sm flex-shrink-0 whitespace-nowrap">Temperature</Label>
                          <Input
                            id="temperature"
                            type="number"
                            value={beamPropertiesState.temperature ?? ''} 
                            onChange={(e) => {
                              const value = e.target.value;
                              onBeamPropertiesStateChange({
                                ...beamPropertiesState,
                                temperature: value === '' ? null : parseFloat(value),
                              });
                            }}
                            placeholder={`${DEFAULT_TEMPERATURE}`}
                            className="w-20 h-8 text-xs"
                          />
                          <span className="text-sm text-muted-foreground">{units.temperature}</span>
                        </div>
                      )}
                    </div>

                    {/* Braced Member & Unbraced Length */}
                    <div className="flex items-center justify-between">
                      <Label htmlFor="isBraced" className="text-sm flex-shrink-0" dangerouslySetInnerHTML={{ __html: "Laterally Braced (C<sub>L</sub>)" }} />
                      <Switch
                        id="isBraced"
                        checked={beamPropertiesState.isBraced}
                        onCheckedChange={(checked) =>
                          onBeamPropertiesStateChange({
                            ...beamPropertiesState,
                            isBraced: checked,
                          })
                        }
                      />
                    </div>
                    {beamPropertiesState.isBraced && (
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="lu" className="text-sm flex-shrink-0">Unbraced Length</Label>
                        <Input
                          id="lu"
                          type="number"
                          value={beamPropertiesState.lu ?? ''} // Keep as empty string for placeholder
                          onChange={(e) => {
                              const value = e.target.value;
                              onBeamPropertiesStateChange({
                                  ...beamPropertiesState,
                                  lu: value === '' ? '' : value, // Store as string, parse in children
                              });
                          }}
                          min={0}
                          step={0.1}
                          placeholder="0"
                          className="w-20 h-8 text-xs"
                        />
                        <span className="text-sm text-muted-foreground">{units.length}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {lumberType === "sawn" ? (
                  <SawnLumber
                    properties={properties}
                    unitSystem={unitSystem}
                    onChange={onChange} 
                    beamPropertiesState={beamPropertiesState} // Pass the whole state
                    onBeamPropertiesStateChange={onBeamPropertiesStateChange}
                    // Pass individual adjustment factor states for SawnLumber to consume
                    isWetService={beamPropertiesState.isWetService}
                    moistureContent={beamPropertiesState.moistureContent}
                    isRepetitiveMember={beamPropertiesState.isRepetitiveMember}
                    isIncised={beamPropertiesState.isIncised}
                    isTemperatureFactored={beamPropertiesState.isTemperatureFactored}
                    temperature={beamPropertiesState.temperature} // Pass null or number
                    isBraced={beamPropertiesState.isBraced}
                    lu={beamPropertiesState.lu} // Pass string
                  />
                ) : (
                  <GluedLaminated
                    properties={properties}
                    unitSystem={unitSystem}
                    onGluLamPropertiesChange={handleGluLamPropertiesChange}
                    initialSelectedProperties={beamPropertiesState.selectedGluLamProperties}
                    isWetService={beamPropertiesState.isWetService}
                    isRepetitiveMember={beamPropertiesState.isRepetitiveMember}
                    isIncised={beamPropertiesState.isIncised}
                    isBraced={beamPropertiesState.isBraced}
                    unbracedLengthLu={parseFloat(beamPropertiesState.lu) || 0}
                    isTemperatureFactored={beamPropertiesState.isTemperatureFactored}
                    temperatureInFahrenheit={beamPropertiesState.temperature}
                    moistureContent={beamPropertiesState.moistureContent}
                    manualWidth={beamPropertiesState.manualWidth} // Pass manualWidth
                    manualDepth={beamPropertiesState.manualDepth} // Pass manualDepth
                    beamPropertiesState={beamPropertiesState} // Pass the whole state
                    onBeamPropertiesStateChange={onBeamPropertiesStateChange} // Pass state change handler
                  />
                )}
              </>
            )}
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  );
}