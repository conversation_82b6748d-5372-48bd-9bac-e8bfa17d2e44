"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { FileText, Search } from "lucide-react";
import { useEffect, useState } from "react";
import type { DesignValues } from "@/components/materials";
import type { WoodData } from "@/hooks/use-wood-data";
import { DEFAULT_NDS_VERSION, API_ENDPOINTS } from "@/lib/constants/nds-constants";

interface NdsTableModalProps {
  onSelectDesignValues?: (values: DesignValues) => void;
  selectedVersion?: string;
}

export function NdsTableModal({ onSelectDesignValues, selectedVersion = DEFAULT_NDS_VERSION }: NdsTableModalProps) {
  const [woodData, setWoodData] = useState<WoodData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<DesignValues | null>(null);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    fetch(`${API_ENDPOINTS.WOOD_DATA}?version=${encodeURIComponent(selectedVersion)}`)
      .then(response => response.json())
      .then((data: WoodData) => {
        setWoodData(data);
        setError(null);
      })
      .catch(error => {
        console.error('Error fetching wood data:', error);
        setError('Failed to load wood species data');
      });
  }, [selectedVersion]);

  useEffect(() => {
    if (!open) {
      setSelectedRow(null);
      setSearchQuery("");
    }
  }, [open]);

  const handleRowClick = (values: DesignValues) => {
    setSelectedRow(values);
  };

  const handleConfirm = () => {
    if (selectedRow && onSelectDesignValues) {
      onSelectDesignValues(selectedRow);
      setOpen(false);
    }
  };

  const filteredData = woodData ? {
    ...woodData,
    speciesCombinations: woodData.speciesCombinations.filter(speciesCombination =>
      speciesCombination.toLowerCase().includes(searchQuery.toLowerCase())
    )
  } : null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button className="inline-flex items-center text-sm font-medium text-primary hover:underline">
          <FileText className="mr-1 h-4 w-4" />
          {selectedVersion} Design Values
        </button>
      </DialogTrigger>
      <DialogContent className="max-w-[90vw] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{selectedVersion} Design Values</DialogTitle>
        </DialogHeader>
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search wood species combinations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <ScrollArea className="h-[70vh]">
          {error ? (
            <p className="text-destructive">{error}</p>
          ) : filteredData ? (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Species Combination</TableHead>
                    <TableHead>Commercial Grade</TableHead>
                    <TableHead>Size Range</TableHead>
                    <TableHead>Grading Rules Agency</TableHead>
                    <TableHead>Design Values Table</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Fb (psi)</TableHead>
                    <TableHead>Ft (psi)</TableHead>
                    <TableHead>Fv (psi)</TableHead>
                    <TableHead>Fc⊥ (psi)</TableHead>
                    <TableHead>Fc (psi)</TableHead>
                    <TableHead>E (×10⁶ psi)</TableHead>
                    <TableHead>Emin (×10⁶ psi)</TableHead>
                    <TableHead>G</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.speciesCombinations.map((speciesCombination) => (
                    filteredData.designValues[speciesCombination].map((values, index) => (
                      <TableRow 
                        key={`${speciesCombination}-${index}`}
                        className={`cursor-pointer hover:bg-muted/50 ${selectedRow === values ? 'bg-muted' : ''}`}
                        onClick={() => handleRowClick(values)}
                      >
                        <TableCell>{speciesCombination}</TableCell>
                        <TableCell>{values.commercial_grade}</TableCell>
                        <TableCell>
                          {values.minThickness}″-{values.maxThickness === Infinity ? "∞" : values.maxThickness}″ × {(values.minWidth ? values.minWidth : -Infinity)}″-{values.maxWidth === Infinity ? "∞" : values.maxWidth}″
                        </TableCell>
                        <TableCell>{values.grading_rules_agency}</TableCell>
                        <TableCell>{values.design_values_table}</TableCell>
                        <TableCell>{values.location}</TableCell>
                        <TableCell>{values.Fb.toFixed(0)}</TableCell>
                        <TableCell>{values.Ft.toFixed(0)}</TableCell>
                        <TableCell>{values.Fv.toFixed(0)}</TableCell>
                        <TableCell>{values.Fc_perp.toFixed(0)}</TableCell>
                        <TableCell>{values.Fc.toFixed(0)}</TableCell>
                        <TableCell>{(values.E / 1e6).toFixed(2)}</TableCell>
                        <TableCell>{(values.Emin / 1e6).toFixed(2)}</TableCell>
                        <TableCell>{values.G.toFixed(3)}</TableCell>
                      </TableRow>
                    ))
                  ))}
                </TableBody>
              </Table>
              <div className="flex justify-end space-x-2 sticky bottom-0 bg-background p-4 border-t">
                <Button 
                  variant="outline" 
                  onClick={() => setOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleConfirm}
                  disabled={!selectedRow}
                >
                  Confirm Selection
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}