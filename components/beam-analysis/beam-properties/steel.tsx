import React, { useState, useEffect, useMemo, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import type { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import { SectionPropertyDisplay } from "./value-displays";

interface SteelGrade {
  astm_designation: string;
  grade: string;
  yield_stress_fy_ksi: string;
  tensile_stress_fu_ksi: string;
  table: string;
}

interface SteelShape {
  Type: string;
  EDI_Std_Nomenclature: string;
}

interface SteelSectionProperties {
  [key: string]: string;
}

interface SteelDesignValues {
  Fy: number;  // Yield stress
  Fu: number;  // Ultimate tensile stress
  grade?: string;
  astm_designation?: string;
}

interface SteelProps {
  properties: {
    length: number;
    elasticModulus: number;
    momentOfInertia: number;
  };
  unitSystem: UnitSystem;
  onChange: (
    properties: import("@/lib/types/beam/beam-data").BeamProperties & {
      area: number;
    }
  ) => void;
  beamPropertiesState: BeamPropertiesState;
  onBeamPropertiesStateChange: (state: BeamPropertiesState) => void;
}

export function Steel({
  properties,
  unitSystem,
  onChange,
  beamPropertiesState,
  onBeamPropertiesStateChange,
}: SteelProps) {
  const [grades, setGrades] = useState<SteelGrade[]>([]);
  const [shapes, setShapes] = useState<SteelShape[]>([]);
  const [selectedGrade, setSelectedGrade] = useState<string>(
    (beamPropertiesState as any).selectedSteelGrade || ""
  );
  const [selectedShape, setSelectedShape] = useState<string>(
    (beamPropertiesState as any).selectedSteelShape || ""
  );
  const [selectedShapeSize, setSelectedShapeSize] = useState<string>(
    (beamPropertiesState as any).selectedSteelShapeSize || ""
  );
  const [sectionProperties, setSectionProperties] = useState<SteelSectionProperties | null>(
    (beamPropertiesState as any).steelSectionProperties || null
  );
  const [designValues, setDesignValues] = useState<SteelDesignValues | null>(
    (beamPropertiesState as any).steelDesignValues || null
  );
  const [isSteelBraced, setIsSteelBraced] = useState<boolean>(
    (beamPropertiesState as any).isSteelBraced || false
  );
  const [steelUnbracedLength, setSteelUnbracedLength] = useState<string>(
    (beamPropertiesState as any).steelUnbracedLength || ""
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const units = getUnitsBySystem(unitSystem);

  // Fetch steel grades on component mount
  useEffect(() => {
    const fetchGrades = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/steel/grades');
        const data = await response.json();
        
        if (data.success) {
          setGrades(data.data);
        } else {
          setError('Failed to load steel grades');
        }
      } catch (err) {
        setError('Error loading steel grades');
        console.error('Error fetching steel grades:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchGrades();
  }, []);

  // Fetch steel shapes when component mounts
  useEffect(() => {
    const fetchShapes = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/steel/shapes');
        const data = await response.json();
        
        if (data.shapes) {
          setShapes(data.shapes);
        } else {
          setError('Failed to load steel shapes');
        }
      } catch (err) {
        setError('Error loading steel shapes');
        console.error('Error fetching steel shapes:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchShapes();
  }, []);

  // Get unique grades for display
  const uniqueGrades = useMemo(() => {
    const gradeMap = new Map();
    grades.forEach(g => {
      if (g.astm_designation && g.astm_designation.trim() !== "") {
        // Handle cases where grade might be empty (like A992)
        const gradeDisplay = g.grade && g.grade.trim() !== "" ? g.grade : "Default";
        const displayText = `${g.astm_designation} ${gradeDisplay}`;
        // Use unique key that combines ASTM designation and grade to avoid conflicts
        const uniqueKey = `${g.astm_designation}_${g.grade}`;
        const value = uniqueKey; // Use unique key as value
        gradeMap.set(uniqueKey, { displayText, value, astm_designation: g.astm_designation, grade: g.grade });
      }
    });
    return Array.from(gradeMap.values()).sort((a, b) => a.displayText.localeCompare(b.displayText));
  }, [grades]);

  // Get unique shape types for the dropdown
  const uniqueShapeTypes = useMemo(() => {
    const types = shapes.map(shape => shape.Type).filter(type => type && type.trim() !== "");
    return Array.from(new Set(types)).sort();
  }, [shapes]);

  // Get shape sizes for selected shape type
  const shapeSizes = useMemo(() => {
    if (!selectedShape) return [];
    return shapes
      .filter(shape => shape.Type === selectedShape)
      .map(shape => shape.EDI_Std_Nomenclature)
      .filter(size => size && size.trim() !== "")
      .sort();
  }, [shapes, selectedShape]);

  // Auto-select first grade when grades are loaded and no grade is currently selected
  useEffect(() => {
    if (uniqueGrades.length > 0 && !selectedGrade) {
      const firstGrade = uniqueGrades[0].value;
      setSelectedGrade(firstGrade);
    }
  }, [uniqueGrades, selectedGrade]);

  // Auto-select first shape when grade is selected and shapes are available
  useEffect(() => {
    if (selectedGrade && uniqueShapeTypes.length > 0 && !selectedShape) {
      const firstShape = uniqueShapeTypes[0];
      setSelectedShape(firstShape);
    }
  }, [selectedGrade, uniqueShapeTypes, selectedShape]);

  // Auto-select first shape size when shape is selected and sizes are available
  useEffect(() => {
    if (selectedShape && shapeSizes.length > 0 && !selectedShapeSize) {
      const firstSize = shapeSizes[0];
      setSelectedShapeSize(firstSize);
    }
  }, [selectedShape, shapeSizes, selectedShapeSize]);

  // Fetch section properties when grade, shape, and size are selected
  useEffect(() => {
    const fetchSectionProperties = async () => {
      if (!selectedShape || !selectedShapeSize) {
        setSectionProperties(null);
        setDesignValues(null);
        return;
      }

      try {
        setLoading(true);
        setError("");
        
        // Fetch section properties
        const sectionResponse = await fetch(
          `/api/steel/section-properties?type=${encodeURIComponent(selectedShape)}&nomenclature=${encodeURIComponent(selectedShapeSize)}`
        );
        const sectionData = await sectionResponse.json();
        
        if (sectionData.sectionProperties) {
          setSectionProperties(sectionData.sectionProperties);
        } else {
          setError('Section properties not found for selected shape');
          setSectionProperties(null);
        }

        // Get design values from selected grade
        if (selectedGrade) {
          // Parse the unique key format (ASTM_designation_grade)
          const parts = selectedGrade.split('_');
          const astm_designation = parts[0];
          const grade = parts.slice(1).join('_'); // Handle cases where grade might contain underscores
          
          const selectedGradeData = grades.find(g => 
            g.astm_designation === astm_designation && g.grade === grade
          );
          if (selectedGradeData) {
            const designValues: SteelDesignValues = {
              Fy: parseFloat(selectedGradeData.yield_stress_fy_ksi) * 1000,
              Fu: parseFloat(selectedGradeData.tensile_stress_fu_ksi) * 1000,
              grade: selectedGradeData.grade,
              astm_designation: selectedGradeData.astm_designation,
            };
            setDesignValues(designValues);
          }
        }
      } catch (err) {
        setError('Error loading section properties');
        console.error('Error fetching section properties:', err);
        setSectionProperties(null);
        setDesignValues(null);
      } finally {
        setLoading(false);
      }
    };

    fetchSectionProperties();
  }, [selectedShape, selectedShapeSize, selectedGrade, grades]);

  // Update parent component when properties change
  const previousValues = useRef<{
    area?: number;
    momentOfInertia?: number;
    elasticModulus?: number;
    selectedSteelGrade?: string;
    selectedSteelShape?: string;
    selectedSteelShapeSize?: string;
  }>({});

  useEffect(() => {
    if (sectionProperties && designValues) {
      const area = parseFloat(sectionProperties.A || "0");
      const momentOfInertiaStr = sectionProperties.Ix || sectionProperties.I_x || "0";
      const momentOfInertia = parseFloat(momentOfInertiaStr);
      const elasticModulus = 29000 * 1000; // Convert 29,000 ksi to psi

      // Check if values have actually changed
      const prev = previousValues.current;
      const hasBeamPropsChanged = 
        prev.area !== area || 
        prev.momentOfInertia !== momentOfInertia || 
        prev.elasticModulus !== elasticModulus;

      const hasStateChanged = 
        prev.selectedSteelGrade !== selectedGrade ||
        prev.selectedSteelShape !== selectedShape ||
        prev.selectedSteelShapeSize !== selectedShapeSize;

      if (hasBeamPropsChanged) {
        console.log("Steel Component - Updating beam properties:", {
          length: properties.length,
          elasticModulus,
          momentOfInertia,
          area,
          sectionProperties_Ix: sectionProperties.Ix
        });
        
        // Update beam properties
        onChange({
          length: properties.length,
          elasticModulus,
          momentOfInertia,
          area,
        });
      }

      if (hasStateChanged) {
        // Update beam properties state
        const newState = {
          ...beamPropertiesState,
          selectedSteelGrade: selectedGrade,
          selectedSteelShape: selectedShape,
          selectedSteelShapeSize: selectedShapeSize,
          steelSectionProperties: sectionProperties,
          steelDesignValues: designValues,
          isSteelBraced: isSteelBraced,
          steelUnbracedLength: steelUnbracedLength,
        } as any;
        onBeamPropertiesStateChange(newState);
      }

      // Update previous values
      previousValues.current = {
        area,
        momentOfInertia,
        elasticModulus,
        selectedSteelGrade: selectedGrade,
        selectedSteelShape: selectedShape,
        selectedSteelShapeSize: selectedShapeSize,
      };
    }
  }, [sectionProperties, designValues, selectedGrade, selectedShape, selectedShapeSize, properties.length, isSteelBraced, steelUnbracedLength]);

  const handleGradeChange = (grade: string) => {
    setSelectedGrade(grade);
    setError("");
  };

  const handleShapeChange = (shape: string) => {
    setSelectedShape(shape);
    setSelectedShapeSize(""); // Reset shape size when shape changes
    setSectionProperties(null);
    setDesignValues(null);
    setError("");
  };

  const handleShapeSizeChange = (shapeSize: string) => {
    setSelectedShapeSize(shapeSize);
    setError("");
  };

  const handleSteelBracedChange = (checked: boolean) => {
    setIsSteelBraced(checked);
    // Update parent state immediately
    const newState = {
      ...beamPropertiesState,
      isSteelBraced: checked,
    } as any;
    onBeamPropertiesStateChange(newState);
  };

  const handleSteelUnbracedLengthChange = (value: string) => {
    setSteelUnbracedLength(value);
    // Update parent state immediately
    const newState = {
      ...beamPropertiesState,
      steelUnbracedLength: value,
    } as any;
    onBeamPropertiesStateChange(newState);
  };

  return (
    <div className="space-y-4">
      {/* Steel Grade Selection */}
      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor="steel-grade" className="text-sm font-medium">
            Steel Grade
          </Label>
          <Select value={selectedGrade} onValueChange={handleGradeChange} disabled={loading}>
            <SelectTrigger>
              <SelectValue placeholder={loading ? "Loading grades..." : "Select steel grade"} />
            </SelectTrigger>
            <SelectContent>
              {uniqueGrades.map((grade) => (
                <SelectItem key={grade.value} value={grade.value}>
                  {grade.displayText}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Steel Shape Selection */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="steel-shape" className="text-sm font-medium">
            Shape Type
          </Label>
          <Select value={selectedShape} onValueChange={handleShapeChange} disabled={loading}>
            <SelectTrigger>
              <SelectValue placeholder={loading ? "Loading shapes..." : "Select shape"} />
            </SelectTrigger>
            <SelectContent>
              {uniqueShapeTypes.map((type) => (
                type && type.trim() !== "" && (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                )
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="steel-shape-size" className="text-sm font-medium">
            Shape Size
          </Label>
          <Select 
            value={selectedShapeSize} 
            onValueChange={handleShapeSizeChange} 
            disabled={loading || !selectedShape}
          >
            <SelectTrigger>
              <SelectValue placeholder={
                !selectedShape ? "Select shape first" : 
                loading ? "Loading sizes..." : 
                "Select size"
              } />
            </SelectTrigger>
            <SelectContent>
              {shapeSizes.map((size) => (
                size && size.trim() !== "" && (
                  <SelectItem key={size} value={size}>
                    {size}
                  </SelectItem>
                )
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Steel Bracing Controls */}
      <div className="space-y-4 pt-4 border-t">
        <h4 className="text-sm font-medium mb-2">Lateral Bracing</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3">
          {/* Laterally Braced Switch */}
          <div className="flex items-center justify-between">
            <Label htmlFor="isSteelBraced" className="text-sm flex-shrink-0" dangerouslySetInnerHTML={{ __html: "Laterally Braced (C<sub>L</sub>)" }} />
            <Switch
              id="isSteelBraced"
              checked={isSteelBraced}
              onCheckedChange={handleSteelBracedChange}
            />
          </div>
          
          {/* Unbraced Length Input */}
          {isSteelBraced && (
            <div className="flex items-center space-x-2">
              <Label htmlFor="steelUnbracedLength" className="text-sm flex-shrink-0">Unbraced Length</Label>
              <Input
                id="steelUnbracedLength"
                type="number"
                value={steelUnbracedLength}
                onChange={(e) => handleSteelUnbracedLengthChange(e.target.value)}
                min={0}
                step={0.1}
                placeholder="0"
                className="w-20 h-8 text-xs"
              />
              <span className="text-sm text-muted-foreground">{units.length}</span>
            </div>
          )}
        </div>
      </div>

      {/* Loading indicator */}
      {loading && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin" />
          Loading...
        </div>
      )}

      {/* Error display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
} 