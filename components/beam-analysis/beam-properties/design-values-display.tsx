import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { BeamPropertiesState, GluLamSelectedProperties } from "@/lib/types/beam/beam-data";
import { DesignValueDisplay, SectionPropertyDisplay } from "./value-displays";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { calculateRectangularSectionProps } from "@/lib/utils/section-calculations";

interface DesignValuesDisplayProps {
  beamPropertiesState: BeamPropertiesState;
  unitSystem: UnitSystem;
  isManualMode?: boolean;
}

export function DesignValuesDisplay({ beamPropertiesState, unitSystem, isManualMode = false }: DesignValuesDisplayProps) {
  const units = getUnitsBySystem(unitSystem);
  const { designValues, lumberProperties, selectedGluLamProperties } = beamPropertiesState;

  // State for controlling which sections are open
  const [isMaterialsOpen, setIsMaterialsOpen] = useState(true);
  const [isDesignValuesOpen, setIsDesignValuesOpen] = useState(true);

  // Helper function to get material type and species info
  const getMaterialInfo = () => {
    if (!beamPropertiesState) return null;

    // Check for steel properties first
    const steelDesignValues = (beamPropertiesState as any).steelDesignValues;
    const steelSectionProperties = (beamPropertiesState as any).steelSectionProperties;
    const selectedSteelGrade = (beamPropertiesState as any).selectedSteelGrade;
    const selectedSteelShape = (beamPropertiesState as any).selectedSteelShape;
    const selectedSteelShapeSize = (beamPropertiesState as any).selectedSteelShapeSize;

    if (steelDesignValues || steelSectionProperties || selectedSteelGrade) {
      return {
        materialType: "Steel",
        species: steelDesignValues?.astm_designation || "N/A",
        grade: steelDesignValues?.grade || selectedSteelGrade || "N/A",
        size: selectedSteelShapeSize || "N/A"
      };
    }

    // In manual mode, show manual properties
    if (isManualMode) {
      return {
        materialType: beamPropertiesState.manual_material_type || "Manual Input",
        species: beamPropertiesState.manual_species || "Custom",
        grade: beamPropertiesState.manual_grade || "N/A",
        size: (beamPropertiesState.manualWidth && beamPropertiesState.manualDepth ? 
               `${beamPropertiesState.manualWidth}" x ${beamPropertiesState.manualDepth}"` : 
               "N/A")
      };
    }

    if (selectedGluLamProperties) {
      return {
        materialType: "Glued Laminated Timber",
        species: `${selectedGluLamProperties.speciesGroup} - ${selectedGluLamProperties.species}`,
        grade: (selectedGluLamProperties.selectedTable5ADetail && selectedGluLamProperties.selectedTable5ADetail.stress_class)
               ? `${selectedGluLamProperties.selectedTable5ADetail.stress_class} (${selectedGluLamProperties.selectedTable5ADetail.species_outer}/${selectedGluLamProperties.selectedTable5ADetail.species_core})`
               : "N/A (Select Combination Symbol)",
        size: `${selectedGluLamProperties.width}" x ${selectedGluLamProperties.depth}"`
      };
    }

    const materialType = beamPropertiesState.selectedSpecies ? "Sawn Lumber" : "Manual";
    const species = beamPropertiesState.selectedSpecies || "Custom";
    const grade = beamPropertiesState.selectedGrade || "N/A";
    const size = beamPropertiesState.selectedNominalSize || 
                 (beamPropertiesState.manualWidth && beamPropertiesState.manualDepth ? 
                  `${beamPropertiesState.manualWidth} x ${beamPropertiesState.manualDepth}` : 
                  "N/A");

    return {
      materialType,
      species,
      grade,
      size
    };
  };

  // Helper function to get section properties for Sawn Lumber tooltip
  const getSawnLumberSectionPropertiesForTooltip = () => {
    const manualW = parseFloat(beamPropertiesState.manualWidth || "0");
    const manualD = parseFloat(beamPropertiesState.manualDepth || "0");
    const hasValidManualDims = !isNaN(manualW) && manualW > 0 && !isNaN(manualD) && manualD > 0;

    let area = 0, Sx = 0, Ix = 0, Sy = 0, Iy = 0;

    if (hasValidManualDims && !lumberProperties) {
      const calculatedProps = calculateRectangularSectionProps(manualW, manualD);
      if (calculatedProps) {
        area = calculatedProps.area;
        Ix = calculatedProps.Ix;
        Iy = calculatedProps.Iy;
        Sx = calculatedProps.Sx;
        Sy = calculatedProps.Sy;
      }
    } else if (lumberProperties) {
      area = lumberProperties.area_of_section_a_in2 ?? 0;
      Sx = lumberProperties.Sxx ?? 0;
      Ix = lumberProperties.Ixx ?? 0;
      Sy = lumberProperties.Syy ?? 0;
      Iy = lumberProperties.Iyy ?? 0;
    }
    
    return [
      { symbol: "A", value: area, unit: units.area },
      { symbol: "S<sub>xx</sub>", value: Sx, unit: units.sectionModulus },
      { symbol: "I<sub>xx</sub>", value: Ix, unit: units.momentOfInertia },
      { symbol: "S<sub>yy</sub>", value: Sy, unit: units.sectionModulus },
      { symbol: "I<sub>yy</sub>", value: Iy, unit: units.momentOfInertia }
    ];
  };

  // --- Density Calculation Helper ---
  const getDensity = () => {
    if (
      beamPropertiesState.includeBeamWeight &&
      designValues &&
      typeof designValues.G === 'number' &&
      !isNaN(designValues.G) &&
      designValues.G > 0
    ) {
      const G = designValues.G;
      let mc = 19;
      if (
        typeof beamPropertiesState.moistureContent === 'number' &&
        !isNaN(beamPropertiesState.moistureContent)
      ) {
        mc = beamPropertiesState.moistureContent;
      }
      // density = 62.4 * [ G / (1 + G * 0.009 * m.c.) ] * [ 1 + m.c. / 100 ]
      const density = 62.4 * (G / (1 + G * 0.009 * mc)) * (1 + mc / 100);
      return density;
    }
    return null;
  };
  const density = getDensity();

  const materialInfo = getMaterialInfo();
  const sawnLumberSectionProperties = getSawnLumberSectionPropertiesForTooltip();
  
  // Check if this is steel material
  const steelDesignValues = (beamPropertiesState as any).steelDesignValues;
  const steelSectionProperties = (beamPropertiesState as any).steelSectionProperties;
  const isSteel = Boolean(steelDesignValues || steelSectionProperties);

  // In manual mode, show material info even without designValues or selectedGluLamProperties
  // Only show "not available" if we're not in manual mode AND have no lumber properties
  if (!isManualMode && !designValues && !selectedGluLamProperties && !isSteel) {
    return <div className="text-sm text-muted-foreground">Design values or Glulam properties not available.</div>;
  }

  // Helper function to create manual design values for display
  const getManualDesignValues = () => {
    if (!isManualMode) return null;
    
    const manualFb = beamPropertiesState.manual_Fb_allow ?? 0;
    
    return {
      original_E: beamPropertiesState.manual_E ?? 0,
      adjusted_E: beamPropertiesState.manual_E ?? 0,
      E: beamPropertiesState.manual_E ?? 0,
      original_Emin: beamPropertiesState.manual_E_min ?? 0,
      adjusted_Emin: beamPropertiesState.manual_E_min ?? 0,
      Emin: beamPropertiesState.manual_E_min ?? 0,
      original_Fb: manualFb,
      adjusted_Fb: manualFb,
      Fb: manualFb,
      // For manual mode, set Fb_pos and Fb_neg to the same value as Fb
      original_Fb_pos: manualFb,
      adjusted_Fb_pos: manualFb,
      Fb_pos: manualFb,
      original_Fb_neg: manualFb,
      adjusted_Fb_neg: manualFb,
      Fb_neg: manualFb,
      original_Fv: beamPropertiesState.manual_Fv_allow ?? 0,
      adjusted_Fv: beamPropertiesState.manual_Fv_allow ?? 0,
      Fv: beamPropertiesState.manual_Fv_allow ?? 0,
      original_Ft: 0, // Not typically manually entered
      adjusted_Ft: 0,
      Ft: 0,
      original_Fc: 0, // Not typically manually entered
      adjusted_Fc: 0,
      Fc: 0,
      original_Fc_perp: 0, // Not typically manually entered
      adjusted_Fc_perp: 0,
      Fc_perp: 0,
      G: 0.50, // Default specific gravity for wood
      C_M_Fb: 1.0,
      C_M_Ft: 1.0,
      C_M_Fv: 1.0,
      C_M_Fc_perp: 1.0,
      C_M_Fc: 1.0,
      C_M_E: 1.0,
      C_F: 1.0,
      C_fu: 1.0,
      C_r: 1.0,
      C_L: 1.0,
      C_V: 1.0,
      calculatedIncisingFactors: null,
      calculatedTemperatureFactors: null,
    };
  };

  const displayDesignValues = isManualMode ? getManualDesignValues() : designValues;

  return (
    <div className="space-y-4">
      {/* Materials Section */}
      <Collapsible 
        open={isMaterialsOpen} 
        onOpenChange={setIsMaterialsOpen} 
        className="border rounded-lg"
      >
        <CollapsibleTrigger asChild>
          <button className="flex w-full items-center justify-between p-4 text-sm font-medium">
            Section Properties
            {isMaterialsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
        </CollapsibleTrigger>
        <CollapsibleContent className="px-4 pb-4 space-y-3">
          {/* General Material Info */}
          <div className="grid grid-cols-2 gap-x-4 gap-y-1">
            <div className="text-sm"><span className="text-foreground">Type:</span> <span className="font-medium">{materialInfo?.materialType}</span></div>
            <div className="text-sm"><span className="text-foreground">Species:</span> <span className="font-medium">{materialInfo?.species}</span></div>
            <div className="text-sm"><span className="text-foreground">Grade:</span> <span className="font-medium">{materialInfo?.grade}</span></div>
            <div className="text-sm flex items-center">
              <span className="text-foreground">Size:</span>
              <span className="font-medium ml-1">{materialInfo?.size}</span>
              {( (lumberProperties && materialInfo?.size !== "N/A") || 
                 (selectedGluLamProperties && selectedGluLamProperties.sectionProperties) ||
                 (steelSectionProperties && materialInfo?.size !== "N/A") ) && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3 w-3 text-muted-foreground cursor-help ml-1" />
                    </TooltipTrigger>
                    <TooltipContent className="w-64">
                      <div className="space-y-1">
                        <p className="font-medium text-xs">
                          {steelSectionProperties ? "Steel Section Properties:" :
                           selectedGluLamProperties ? "Glulam Section Properties:" : 
                           "Sawn Lumber/Manual Section Props:"}
                        </p>
                        {steelSectionProperties ? (
                          <>
                            <div className="flex justify-between text-xs"><span className="">Area (A):</span><span className="font-mono">{parseFloat(steelSectionProperties.A || "0").toFixed(3)} {units.area}</span></div>
                            <div className="flex justify-between text-xs"><span className="">I<sub>x</sub>:</span><span className="font-mono">{parseFloat(steelSectionProperties.Ix || steelSectionProperties.I_x || "0").toFixed(3)} {units.momentOfInertia}</span></div>
                            <div className="flex justify-between text-xs"><span className="">S<sub>x</sub>:</span><span className="font-mono">{parseFloat(steelSectionProperties.Sx || steelSectionProperties.S_x || "0").toFixed(3)} {units.sectionModulus}</span></div>
                            <div className="flex justify-between text-xs"><span className="">r<sub>x</sub>:</span><span className="font-mono">{parseFloat(steelSectionProperties.rx || steelSectionProperties.r_x || "0").toFixed(3)} {units.length}</span></div>
                            <div className="flex justify-between text-xs"><span className="">I<sub>y</sub>:</span><span className="font-mono">{parseFloat(steelSectionProperties.Iy || steelSectionProperties.I_y || "0").toFixed(3)} {units.momentOfInertia}</span></div>
                            <div className="flex justify-between text-xs"><span className="">S<sub>y</sub>:</span><span className="font-mono">{parseFloat(steelSectionProperties.Sy || steelSectionProperties.S_y || "0").toFixed(3)} {units.sectionModulus}</span></div>
                            <div className="flex justify-between text-xs"><span className="">r<sub>y</sub>:</span><span className="font-mono">{parseFloat(steelSectionProperties.ry || steelSectionProperties.r_y || "0").toFixed(3)} {units.length}</span></div>
                            <div className="flex justify-between text-xs"><span className="">d:</span><span className="font-mono">{parseFloat(steelSectionProperties.d || "0").toFixed(3)} {units.length}</span></div>
                          </>
                        ) : selectedGluLamProperties && selectedGluLamProperties.sectionProperties ? (
                          <>
                            <div className="flex justify-between text-xs"><span className="">Area (A):</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.area === 'number' ? selectedGluLamProperties.sectionProperties.area.toFixed(2) : 'N/A'} {units.area}</span></div>
                            <div className="flex justify-between text-xs"><span className="">I<sub>xx</sub>:</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.Ix === 'number' ? selectedGluLamProperties.sectionProperties.Ix.toFixed(2) : 'N/A'} {units.momentOfInertia}</span></div>
                            <div className="flex justify-between text-xs"><span className="">S<sub>xx</sub>:</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.Sx === 'number' ? selectedGluLamProperties.sectionProperties.Sx.toFixed(2) : 'N/A'} {units.sectionModulus}</span></div>
                            <div className="flex justify-between text-xs"><span className="">r<sub>x</sub>:</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.rx === 'number' ? selectedGluLamProperties.sectionProperties.rx.toFixed(2) : 'N/A'} in</span></div>
                            <div className="flex justify-between text-xs"><span className="">I<sub>yy</sub>:</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.Iy === 'number' ? selectedGluLamProperties.sectionProperties.Iy.toFixed(2) : 'N/A'} {units.momentOfInertia}</span></div>
                            <div className="flex justify-between text-xs"><span className="">S<sub>yy</sub>:</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.Sy === 'number' ? selectedGluLamProperties.sectionProperties.Sy.toFixed(2) : 'N/A'} {units.sectionModulus}</span></div>
                            <div className="flex justify-between text-xs"><span className="">r<sub>y</sub>:</span><span className="font-mono">{typeof selectedGluLamProperties.sectionProperties.ry === 'number' ? selectedGluLamProperties.sectionProperties.ry.toFixed(2) : 'N/A'} in</span></div>
                          </>
                        ) : (
                          sawnLumberSectionProperties.map((prop: { symbol: string; value: number; unit: string }) => (
                            <div key={prop.symbol} className="flex justify-between text-xs">
                              <span dangerouslySetInnerHTML={{ __html: prop.symbol + ":" }}></span>
                              <span className="font-mono">{prop.value.toFixed(3)} {prop.unit}</span>
                            </div>
                          ))
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Current Design Values Section - Show if we have lumber design values OR manual mode OR steel */}
      {((((!selectedGluLamProperties || (selectedGluLamProperties && designValues)) && designValues) || (isManualMode && displayDesignValues) || isSteel)) && (
        <Collapsible 
          open={isDesignValuesOpen} 
          onOpenChange={setIsDesignValuesOpen} 
          className="border rounded-lg"
        >
          <CollapsibleTrigger asChild>
            <button className="flex w-full items-center justify-between p-4 text-sm font-medium">
              Current Design Values
              {isDesignValuesOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </button>
          </CollapsibleTrigger>
          <CollapsibleContent className="px-4 pb-4">
            {isSteel ? (
              /* Steel Design Values */
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium text-sm text-muted-foreground mb-2">
                    Strength Properties
                  </h5>
                  {steelDesignValues && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between py-1">
                        <div className="flex items-center gap-1">
                          <span className="font-medium">
                            <span className="font-mono">F<sub>y</sub></span>
                          </span>
                        </div>
                        <span className="font-mono">
                          {steelDesignValues.Fy.toFixed(0)} {units.pressure}
                        </span>
                      </div>
                      <div className="flex items-center justify-between py-1">
                        <div className="flex items-center gap-1">
                          <span className="font-medium">
                            <span className="font-mono">F<sub>u</sub></span>
                          </span>
                        </div>
                        <span className="font-mono">
                          {steelDesignValues.Fu.toFixed(0)} {units.pressure}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <h5 className="font-medium text-sm text-muted-foreground mb-2">
                    Elastic Properties
                  </h5>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between py-1">
                      <div className="flex items-center gap-1">
                        <span className="font-medium">
                          <span className="font-mono">E</span>
                        </span>
                      </div>
                      <span className="font-mono">
                        29000000 {units.pressure}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              /* Wood Design Values */
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium text-sm text-muted-foreground mb-2">
                    Strength Properties
                  </h5>
                  {displayDesignValues ? (
                    <>
                      <DesignValueDisplay
                        property="Ft"
                        baseValue={displayDesignValues.original_Ft ?? displayDesignValues.Ft}
                        value={displayDesignValues.adjusted_Ft ?? displayDesignValues.Ft}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_Ft}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Ft?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Ft?.factor}
                        C_F={displayDesignValues.C_F}
                      />
                      <DesignValueDisplay
                        property="Fb_pos"
                        baseValue={displayDesignValues.original_Fb_pos ?? displayDesignValues.Fb_pos ?? 0}
                        value={displayDesignValues.adjusted_Fb_pos ?? displayDesignValues.Fb_pos ?? 0}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_Fb}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Fb?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Fb?.factor}
                        C_F={displayDesignValues.C_F}
                        C_fu={displayDesignValues.C_fu}
                        C_r={displayDesignValues.C_r}
                        isRepetitiveMember={beamPropertiesState.isRepetitiveMember}
                        C_L={displayDesignValues.C_L ?? undefined}
                        C_V={displayDesignValues.C_V}
                      />
                      <DesignValueDisplay
                        property="Fb_neg"
                        baseValue={displayDesignValues.original_Fb_neg ?? displayDesignValues.Fb_neg ?? 0}
                        value={displayDesignValues.adjusted_Fb_neg ?? displayDesignValues.Fb_neg ?? 0}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_Fb}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Fb?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Fb?.factor}
                        C_F={displayDesignValues.C_F}
                        C_fu={displayDesignValues.C_fu}
                        C_r={displayDesignValues.C_r}
                        isRepetitiveMember={beamPropertiesState.isRepetitiveMember}
                        C_L={displayDesignValues.C_L ?? undefined}
                        C_V={displayDesignValues.C_V}
                      />
                      <DesignValueDisplay
                        property="Fv"
                        baseValue={displayDesignValues.original_Fv ?? displayDesignValues.Fv}
                        value={displayDesignValues.adjusted_Fv ?? displayDesignValues.Fv}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_Fv}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Fv?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Fv?.factor}
                      />
                      <DesignValueDisplay
                        property="Fc_perp"
                        baseValue={displayDesignValues.original_Fc_perp ?? displayDesignValues.Fc_perp}
                        value={displayDesignValues.adjusted_Fc_perp ?? displayDesignValues.Fc_perp}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_Fc_perp}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Fc_perp?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Fc_perp?.factor}
                      />
                      <DesignValueDisplay
                        property="Fc"
                        baseValue={displayDesignValues.original_Fc ?? displayDesignValues.Fc}
                        value={displayDesignValues.adjusted_Fc ?? displayDesignValues.Fc}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_Fc}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Fc?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Fc?.factor}
                        C_F={displayDesignValues.C_F}
                      />
                    </>
                  ) : (
                    <>
                      <DesignValueDisplay
                        property="Fb"
                        baseValue={beamPropertiesState.manual_Fb_allow || 0}
                        value={beamPropertiesState.manual_Fb_allow || 0}
                        unitSystem={unitSystem}
                      />
                      <DesignValueDisplay
                        property="Fv"
                        baseValue={beamPropertiesState.manual_Fv_allow || 0}
                        value={beamPropertiesState.manual_Fv_allow || 0}
                        unitSystem={unitSystem}
                      />
                    </>
                  )}
                </div>
                <div>
                  <h5 className="font-medium text-sm text-muted-foreground mb-2">
                    Elastic Properties
                  </h5>
                  {displayDesignValues ? (
                    <>
                      <DesignValueDisplay
                        property="E"
                        baseValue={displayDesignValues.original_E ?? displayDesignValues.E}
                        value={displayDesignValues.adjusted_E ?? displayDesignValues.E}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_E}
                        C_i={displayDesignValues.calculatedIncisingFactors?.E?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.E?.factor}
                      />
                      <DesignValueDisplay
                        property="Emin"
                        baseValue={displayDesignValues.original_Emin ?? displayDesignValues.Emin}
                        value={displayDesignValues.adjusted_Emin ?? displayDesignValues.Emin}
                        unitSystem={unitSystem}
                        C_M={displayDesignValues.C_M_E}
                        C_i={displayDesignValues.calculatedIncisingFactors?.Emin?.factor}
                        C_t={displayDesignValues.calculatedTemperatureFactors?.Emin?.factor}
                      />
                      <DesignValueDisplay
                        property="G"
                        baseValue={displayDesignValues?.G}
                        value={displayDesignValues?.G}
                        unitSystem={unitSystem}
                      />
                      {density !== null && (
                        <DesignValueDisplay
                          property="density"
                          baseValue={density}
                          value={density}
                          unitSystem={unitSystem}
                        />
                      )}
                    </>
                  ) : (
                    <>
                      <DesignValueDisplay
                        property="E"
                        baseValue={beamPropertiesState.manual_E || 0}
                        value={beamPropertiesState.manual_E || 0}
                        unitSystem={unitSystem}
                      />
                      <DesignValueDisplay
                        property="Emin"
                        baseValue={beamPropertiesState.manual_E_min || 0}
                        value={beamPropertiesState.manual_E_min || 0}
                        unitSystem={unitSystem}
                      />
                    </>
                  )}
                </div>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  );
} 