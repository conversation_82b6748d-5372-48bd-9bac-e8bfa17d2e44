"use client";

import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import {
  useGluLamSpecies,
  useGluLamTable5AData,
  useAvailableNdsVersions,
  Table5ARow,
} from "@/hooks/use-wood-data";
import type { GluLamSelectedProperties, GluLamSectionProperties as GluLamSectionPropertiesType, AdjustmentFactorSet, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import type { DesignValues } from '@/components/materials';
import { ProcessedGluLamData } from "../beam-properties";
import { calculateRectangularSectionProps } from '@/lib/utils/section-calculations';
import { DEFAULT_ADJUSTMENT_FACTORS } from "@/lib/constants/beam-constants";
import {
  DEFAULT_NDS_VERSION,
  DEFAULT_WOOD_SELECTIONS,
} from "@/lib/constants/nds-constants";

const DEFAULT_SPECIES_GROUP = "Douglas Fir-Larch (DF)";
const GLULAM_SPECIES_GROUP_SOUTHERN_PINE = "Southern Pine";
const NDS_TABLE_5A = "5A";
const TABLE_5A_SPECIES_FILTER_ANY = "Any";
const COMBINED_SYMBOL_KEY_PREFIX_5A = "5A_";
const COMBINED_SYMBOL_DISPLAY_SUFFIX_5A = " (5A)";

// Constants for Loading Orientation
const LOADING_ORIENTATION_PERPENDICULAR_KEY = "perpendicular";
const LOADING_ORIENTATION_PARALLEL_KEY = "parallel";
const LOADING_ORIENTATION_PERPENDICULAR_DISPLAY = "Loaded Perpendicular to Wide Faces of Laminations";
const LOADING_ORIENTATION_PARALLEL_DISPLAY = "Loaded Parallel to Wide Faces of Laminations";

interface CombinedSymbolItem {
  key: string;
  display: string;
  sourceTable: "5A";
  originalSymbol: string;
}

interface GluedLaminatedProps {
  properties: {
    length: number;
    elasticModulus: number;
    momentOfInertia: number;
  };
  unitSystem: UnitSystem;
  onGluLamPropertiesChange: (glulamProps: ProcessedGluLamData | null) => void;
  initialSelectedProperties?: GluLamSelectedProperties | null;
  isWetService: boolean;
  isRepetitiveMember: boolean;
  isIncised: boolean;
  isBraced: boolean;
  unbracedLengthLu: number;
  isTemperatureFactored: boolean;
  temperatureInFahrenheit: number | null;
  moistureContent: number | null;
  manualWidth: string;
  manualDepth: string;
  beamPropertiesState: BeamPropertiesState;
  onBeamPropertiesStateChange: (state: BeamPropertiesState) => void;
}

// Helper function to get base design values from Table 5A based on orientation
const getDesignValueFromTables = (
  table5ADetail: Table5ARow | null,
  orientation: 'perpendicular' | 'parallel'
): {
  Fb_psi: number | null; E_psi: number | null; Emin_psi: number | null; Fv_psi: number | null;
  Fc_perp_psi: number | null; Fc_psi: number | null; Ft_psi: number | null; G: number | null;
  Fb_pos_psi: number | null; Fb_neg_psi: number | null;
} => {
  if (!table5ADetail) {
    return { Fb_psi: null, E_psi: null, Emin_psi: null, Fv_psi: null, Fc_perp_psi: null, Fc_psi: null, Ft_psi: null, G: null, Fb_pos_psi: null, Fb_neg_psi: null };
  }

  const E_multiplier = 1000000; // Convert KSI to PSI

  if (orientation === 'perpendicular') {
    return {
      Fb_psi: table5ADetail.F_bx_pos_psi, // Default Fb to positive bending
      Fb_pos_psi: table5ADetail.F_bx_pos_psi,
      Fb_neg_psi: table5ADetail.F_bx_neg_psi,
      E_psi: table5ADetail.E_xapp_ksi * E_multiplier,
      Emin_psi: table5ADetail.E_xmin_ksi * E_multiplier,
      Fv_psi: table5ADetail.F_vx_psi,
      Fc_perp_psi: table5ADetail.F_cperp_y_psi,
      Fc_psi: table5ADetail.F_c_psi,
      Ft_psi: table5ADetail.F_t_psi,
      G: table5ADetail.G_top_or_bottom,
    };
  } else { // parallel
    return {
      Fb_psi: table5ADetail.F_by_psi,
      Fb_pos_psi: table5ADetail.F_by_psi,
      Fb_neg_psi: table5ADetail.F_by_psi,
      E_psi: table5ADetail.E_yapp_ksi * E_multiplier,
      Emin_psi: table5ADetail.E_ymin_ksi * E_multiplier,
      Fv_psi: table5ADetail.F_vy_psi,
      Fc_perp_psi: table5ADetail.F_cperp_y_psi,
      Fc_psi: table5ADetail.F_c_psi,
      Ft_psi: table5ADetail.F_t_psi,
      G: table5ADetail.G_side,
    };
  }
};

export function GluedLaminated({
  properties,
  unitSystem,
  onGluLamPropertiesChange,
  initialSelectedProperties,
  isWetService,
  isRepetitiveMember,
  isIncised,
  isBraced,
  unbracedLengthLu,
  isTemperatureFactored,
  temperatureInFahrenheit,
  moistureContent,
  manualWidth,
  manualDepth,
  beamPropertiesState,
  onBeamPropertiesStateChange,
}: GluedLaminatedProps) {
  const [selectedNdsVersion, setSelectedNdsVersion] = React.useState<string>(
    beamPropertiesState.selectedNdsVersion || DEFAULT_NDS_VERSION
  );
  const [selectedGluLamGroup, setSelectedGluLamGroup] = React.useState<string>(
    () => initialSelectedProperties?.speciesGroup || ""
  );
  const [selectedGluLamSpecies, setSelectedGluLamSpecies] = React.useState<string>(
    () => initialSelectedProperties?.species || ""
  );
  const [loadingOrientation, setLoadingOrientation] = React.useState<'perpendicular' | 'parallel'>(
    () => initialSelectedProperties?.loadingOrientation || LOADING_ORIENTATION_PERPENDICULAR_KEY
  );
  const [selectedCombinedSymbolKey, setSelectedCombinedSymbolKey] = React.useState<string | null>(
    () => initialSelectedProperties?.selectedCombinationSymbolKey || null
  );
  const [selectedTable5ADetail, setSelectedTable5ADetail] = React.useState<Table5ARow | null>(
    () => initialSelectedProperties?.selectedTable5ADetail || null
  );
  
  // Add state for glulam-specific size inputs
  const [glulamWidth, setGlulamWidth] = React.useState<string>(
    () => initialSelectedProperties?.width?.toString() || "6.75"
  );
  const [glulamDepth, setGlulamDepth] = React.useState<string>(
    () => initialSelectedProperties?.depth?.toString() || "12"
  );

  const { data: gluLamData, error: gluLamError, isLoading: isGluLamLoading } = useGluLamSpecies(selectedNdsVersion);
  const { data: table5AData, isLoading: isTable5ALoading } = useGluLamTable5AData(selectedNdsVersion);
  const { data: availableVersions, isLoading: isVersionsLoading } = useAvailableNdsVersions();

  const units = getUnitsBySystem(unitSystem);
  const beamLengthInFeet = properties.length;

  // Parse width and depth from glulam-specific inputs
  const parsedWidth = React.useMemo(() => parseFloat(glulamWidth), [glulamWidth]);
  const parsedDepth = React.useMemo(() => parseFloat(glulamDepth), [glulamDepth]);
  const numW = isNaN(parsedWidth) || parsedWidth <= 0 ? null : parsedWidth;
  const numD = isNaN(parsedDepth) || parsedDepth <= 0 ? null : parsedDepth;

  const availableSpeciesGroups = React.useMemo(() => {
    if (!gluLamData || !gluLamData.speciesGroups || !gluLamData.species) {
      return [];
    }
    return gluLamData.speciesGroups.filter(groupName => {
      const groupDetail = gluLamData.species[groupName];
      return groupDetail && groupDetail.designValues && groupDetail.designValues.includes(NDS_TABLE_5A);
    });
  }, [gluLamData]);

  const getGluLamPropertiesPayload = React.useCallback((args: {
    group: string | null;
    spec: string | null;
    symKey: string | null;
    sym5ADetail: Table5ARow | null;
    currentNumWidth: number | null;
    currentNumDepth: number | null;
    orientation: 'perpendicular' | 'parallel';
    speciesDataForLookup: typeof gluLamData | undefined | null;
  }): GluLamSelectedProperties | null => {
    const designValueTablesForSpecies = args.group && args.speciesDataForLookup?.species[args.group]?.designValues || "";
    const tablesNeedSymbol = designValueTablesForSpecies.includes(NDS_TABLE_5A);

    if (args.group && args.spec && tablesNeedSymbol && !args.symKey) return null;

    let secProps: GluLamSectionPropertiesType | null = null;
    if (args.currentNumWidth !== null && args.currentNumWidth > 0 && args.currentNumDepth !== null && args.currentNumDepth > 0) {
        const calculated = calculateRectangularSectionProps(args.currentNumWidth, args.currentNumDepth);
        if (calculated) {
            secProps = {
                width: String(args.currentNumWidth),
                depth: String(args.currentNumDepth),
                area: calculated.area,
                Ix: calculated.Ix,
                Sx: calculated.Sx,
                rx: calculated.rx,
                Iy: calculated.Iy,
                Sy: calculated.Sy,
                ry: calculated.ry,
            };
        }
    }

    return {
      speciesGroup: args.group || "",
      species: args.spec || "",
      width: args.currentNumWidth || 0,
      depth: args.currentNumDepth || 0,
      sectionProperties: secProps,
      selectedCombinationSymbolKey: args.symKey || undefined,
      selectedTable5ADetail: args.sym5ADetail || undefined,
      grade: undefined,
      loadingOrientation: args.orientation,
    };
  }, []);

  const resetSymbolAndDetails = React.useCallback(() => {
    setSelectedCombinedSymbolKey(null);
    setSelectedTable5ADetail(null);
  }, []);

  const handleNdsVersionChange = React.useCallback((version: string) => {
    setSelectedNdsVersion(version);
    
    // Reset all glulam selections when version changes
    setSelectedGluLamGroup("");
    setSelectedGluLamSpecies("");
    resetSymbolAndDetails();
    setLoadingOrientation(LOADING_ORIENTATION_PERPENDICULAR_KEY);
    
    // Update the beam properties state
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedNdsVersion: version,
      // Reset glulam properties when version changes
      selectedGluLamProperties: null,
    });
  }, [beamPropertiesState, onBeamPropertiesStateChange, resetSymbolAndDetails]);

  const handleGluLamGroupChange = React.useCallback((group: string) => {
    setSelectedGluLamGroup(group);
    setSelectedGluLamSpecies("");
    resetSymbolAndDetails();
    setLoadingOrientation(LOADING_ORIENTATION_PERPENDICULAR_KEY);
  }, [resetSymbolAndDetails]);

  const handleGluLamSpeciesChange = React.useCallback((species: string) => {
    setSelectedGluLamSpecies(species);
    resetSymbolAndDetails();
    setLoadingOrientation(LOADING_ORIENTATION_PERPENDICULAR_KEY);
  }, [resetSymbolAndDetails]);

  // Sync selectedNdsVersion with beamPropertiesState when it changes (for loading existing analyses)
  React.useEffect(() => {
    if (beamPropertiesState.selectedNdsVersion && beamPropertiesState.selectedNdsVersion !== selectedNdsVersion) {
      setSelectedNdsVersion(beamPropertiesState.selectedNdsVersion);
    }
  }, [beamPropertiesState.selectedNdsVersion, selectedNdsVersion]);

  const handleCombinedSymbolChange = React.useCallback((selectedKey: string | null) => {
    setSelectedCombinedSymbolKey(selectedKey);
    let detail5A: Table5ARow | null = null;
    if (selectedKey) {
      const parts = selectedKey.split('_');
      const sourceTable = parts[0] as "5A";
      const originalSymbol = parts.slice(1).join('_');
      if (sourceTable === NDS_TABLE_5A && table5AData) {
        detail5A = table5AData.find(row => row.stress_class === originalSymbol) || null;
      }
    }
    setSelectedTable5ADetail(detail5A);
    setLoadingOrientation(LOADING_ORIENTATION_PERPENDICULAR_KEY);
  }, [table5AData]);

  // Sync selectedNdsVersion when beamPropertiesState.selectedNdsVersion changes (e.g., when loading)
  React.useEffect(() => {
    const newVersion = beamPropertiesState.selectedNdsVersion || DEFAULT_NDS_VERSION;
    if (selectedNdsVersion !== newVersion) {
      setSelectedNdsVersion(newVersion);
    }
  }, [beamPropertiesState.selectedNdsVersion, selectedNdsVersion]);

  // Effect to set default species group
  React.useEffect(() => {
    if (initialSelectedProperties?.speciesGroup) return;
    if (!isGluLamLoading && gluLamData) {
      if (selectedGluLamGroup && availableSpeciesGroups.length > 0 && !availableSpeciesGroups.includes(selectedGluLamGroup)) {
        const newDefaultGroup = availableSpeciesGroups.includes(DEFAULT_SPECIES_GROUP)
          ? DEFAULT_SPECIES_GROUP
          : availableSpeciesGroups[0];
        handleGluLamGroupChange(newDefaultGroup);
      } else if (selectedGluLamGroup && availableSpeciesGroups.length === 0) {
        handleGluLamGroupChange("");
      } else if (!selectedGluLamGroup && availableSpeciesGroups.length > 0) {
        if (availableSpeciesGroups.includes(DEFAULT_SPECIES_GROUP)) {
          handleGluLamGroupChange(DEFAULT_SPECIES_GROUP);
        } else {
          handleGluLamGroupChange(availableSpeciesGroups[0]);
        }
      }
    }
  }, [initialSelectedProperties, selectedGluLamGroup, availableSpeciesGroups, gluLamData, isGluLamLoading, handleGluLamGroupChange]);

  // Effect to set default species when group changes
  React.useEffect(() => {
    if (initialSelectedProperties?.species && initialSelectedProperties?.speciesGroup === selectedGluLamGroup) return;
    if (selectedGluLamGroup && gluLamData && gluLamData.species[selectedGluLamGroup] && !selectedGluLamSpecies) {
      const speciesForGroup = gluLamData.species[selectedGluLamGroup].species;
      if (speciesForGroup && speciesForGroup.length > 0) {
        handleGluLamSpeciesChange(speciesForGroup[0]);
      }
    }
  }, [initialSelectedProperties, selectedGluLamGroup, selectedGluLamSpecies, gluLamData, handleGluLamSpeciesChange]);

  const availableCombinedSymbols = React.useMemo(() => {
    const symbols: CombinedSymbolItem[] = [];
    if (!gluLamData || !selectedGluLamGroup || !selectedGluLamSpecies || !gluLamData.species[selectedGluLamGroup]) return symbols;
    
    const groupSymbol = gluLamData.species[selectedGluLamGroup]?.symbol; // Get the symbol of the selected group
    if (!groupSymbol) return symbols; // Should ideally not happen if previous checks passed

    const designTables = gluLamData.species[selectedGluLamGroup].designValues || "";
    
    if (designTables.includes(NDS_TABLE_5A) && table5AData) {
      table5AData
        .filter(row => row.species_outer === groupSymbol || row.species_outer === TABLE_5A_SPECIES_FILTER_ANY) // Use groupSymbol here
        .forEach(row => {
          symbols.push({
            key: `${COMBINED_SYMBOL_KEY_PREFIX_5A}${row.stress_class}`,
            display: `${row.stress_class} (${row.species_outer}/${row.species_core})${COMBINED_SYMBOL_DISPLAY_SUFFIX_5A}`,
            sourceTable: NDS_TABLE_5A,
            originalSymbol: row.stress_class
          });
        });
    }
    return symbols;
  }, [table5AData, selectedGluLamSpecies, selectedGluLamGroup, gluLamData]);

  // Effect to auto-select the first combination symbol
  React.useEffect(() => {
    if (selectedGluLamSpecies && !selectedCombinedSymbolKey && availableCombinedSymbols.length > 0 && table5AData && !isTable5ALoading) {
      handleCombinedSymbolChange(availableCombinedSymbols[0].key);
    }
  }, [selectedGluLamSpecies, selectedCombinedSymbolKey, availableCombinedSymbols, handleCombinedSymbolChange, table5AData, isTable5ALoading]);

  // Main effect to set up basic Glulam properties (calculations handled server-side)
  React.useEffect(() => {
    const currentSectionProperties = getGluLamPropertiesPayload({
      group: selectedGluLamGroup,
      spec: selectedGluLamSpecies,
      symKey: selectedCombinedSymbolKey,
      sym5ADetail: selectedTable5ADetail,
      currentNumWidth: numW,
      currentNumDepth: numD,
      orientation: loadingOrientation,
      speciesDataForLookup: gluLamData,
    });

    if (!selectedTable5ADetail || !numW || numW <= 0 || !numD || numD <= 0 || !currentSectionProperties?.sectionProperties || !selectedGluLamGroup) {
      onGluLamPropertiesChange(null);
      return;
    }

    // Get base design values without applying adjustments (server will handle calculations)
    const baseDesignValues = getDesignValueFromTables(selectedTable5ADetail, loadingOrientation);

    // Create basic design values object without complex calculations
    const finalDesignValues: DesignValues = {
      Fb: baseDesignValues.Fb_pos_psi ?? 0,
      Fb_pos: baseDesignValues.Fb_pos_psi ?? 0,
      Fb_neg: baseDesignValues.Fb_neg_psi ?? 0,
      Fv: baseDesignValues.Fv_psi ?? 0,
      Fc_perp: baseDesignValues.Fc_perp_psi ?? 0,
      Fc: baseDesignValues.Fc_psi ?? 0,
      Ft: baseDesignValues.Ft_psi ?? 0,
      E: baseDesignValues.E_psi ?? 0,
      Emin: baseDesignValues.Emin_psi ?? 0,
      G: baseDesignValues.G ?? 0,
      
      // Store original values (same as base for glulam)
      original_Fb_pos: baseDesignValues.Fb_pos_psi,
      original_Fb_neg: baseDesignValues.Fb_neg_psi,
      original_Fv: baseDesignValues.Fv_psi,
      original_Fc_perp: baseDesignValues.Fc_perp_psi,
      original_Fc: baseDesignValues.Fc_psi,
      original_Ft: baseDesignValues.Ft_psi,
      original_E: baseDesignValues.E_psi,
      original_Emin: baseDesignValues.Emin_psi,
      original_G: baseDesignValues.G,
      
      // Set adjusted values same as original (server will calculate actual adjustments)
      adjusted_Fb: baseDesignValues.Fb_pos_psi ?? 0,
      adjusted_Fb_pos: baseDesignValues.Fb_pos_psi ?? 0,
      adjusted_Fb_neg: baseDesignValues.Fb_neg_psi ?? 0,
      adjusted_Fv: baseDesignValues.Fv_psi ?? 0,
      adjusted_Fc_perp: baseDesignValues.Fc_perp_psi ?? 0,
      adjusted_Fc: baseDesignValues.Fc_psi ?? 0,
      adjusted_Ft: baseDesignValues.Ft_psi ?? 0,
      adjusted_E: baseDesignValues.E_psi ?? 0,
      adjusted_Emin: baseDesignValues.Emin_psi ?? 0,
      
      // Set all adjustment factors to defaults (server will calculate)
      Fc_par: null,
      Frt: null,
      C_F: 1.0,
      C_M_Fb: 1.0,
      C_M_Ft: 1.0,
      C_M_Fv: 1.0,
      C_M_Fc_perp: 1.0,
      C_M_Fc: 1.0,
      C_M_E: 1.0,
      C_L: 1.0,
      C_V: 1.0,
      C_fu: 1.0,
      flatUseFactor: 1.0,
      C_r: 1.0,
      C_i_Fb: 1.0,
      C_i_Ft: 1.0,
      C_i_Fv: 1.0,
      C_i_Fc_perp: 1.0,
      C_i_Fc: 1.0,
      C_i_E: 1.0,
      calculatedTemperatureFactors: undefined,
      calculatedIncisingFactors: undefined,
      lambda: 0.8,
      phi_b: 0.85,
      phi_v: 0.75,
      K_F: 2.54,
      notes: `Species: ${selectedGluLamSpecies}, Symbol: ${selectedCombinedSymbolKey}, Orientation: ${loadingOrientation}, Wet: ${isWetService}, Repetitive: ${isRepetitiveMember}, Incised: ${isIncised}, Braced: ${isBraced}, TempFactored: ${isTemperatureFactored}`,
      
      // Basic properties
      minThickness: 0,
      maxThickness: 0,
      minWidth: 0,
      maxWidth: 0,
      commercial_grade: selectedTable5ADetail?.stress_class || "",
      speciesCombination: selectedTable5ADetail?.species_outer || selectedGluLamGroup || "",
      grading_rules_agency: "Various",
      design_values_table: selectedTable5ADetail?.table || NDS_TABLE_5A,
      location: "N/A",
      version: selectedNdsVersion,
      service_condition: isWetService ? "Wet" : "Dry",
      
      // Wet service factors from table (not calculated)
      wet_service_factor_Cm_for_Fb: selectedTable5ADetail?.wet_service_factor_Cm_for_Fb,
      wet_service_factor_Cm_for_Ft: selectedTable5ADetail?.wet_service_factor_Cm_for_Ft,
      wet_service_factor_Cm_for_Fv: selectedTable5ADetail?.wet_service_factor_Cm_for_Fv,
      wet_service_factor_Cm_for_Fc_perp: selectedTable5ADetail?.wet_service_factor_Cm_for_Fc_perp,
      wet_service_factor_Cm_for_Fc: selectedTable5ADetail?.wet_service_factor_Cm_for_Fc,
      wet_service_factor_Cm_for_E_and_Emin: selectedTable5ADetail?.wet_service_factor_Cm_for_E_and_Emin,
      size_classification: "Glulam",
    };

    const payload: ProcessedGluLamData = {
      selectedProperties: currentSectionProperties,
      designValues: finalDesignValues,
      volumeFactorCv: 1.0, // Default, server will calculate
      wetServiceFactors: DEFAULT_ADJUSTMENT_FACTORS, // Server will calculate
    };

    onGluLamPropertiesChange(payload);

  }, [
    selectedNdsVersion,
    selectedGluLamGroup,
    selectedGluLamSpecies,
    glulamWidth,
    glulamDepth,
    loadingOrientation,
    selectedCombinedSymbolKey,
    selectedTable5ADetail,
    isWetService,
    isIncised,
    isRepetitiveMember,
    isBraced,
    unbracedLengthLu,
    isTemperatureFactored,
    temperatureInFahrenheit,
    moistureContent
  ]);

  const isLoadingSymbols = isTable5ALoading;
  const currentSpeciesDesignValueTables = gluLamData?.species[selectedGluLamGroup]?.designValues || "";

  return (
    <>
      {/* NDS Version Selection */}
      <div className="space-y-2">
        <Label>NDS Version</Label>
        <Select
          value={selectedNdsVersion}
          onValueChange={handleNdsVersionChange}
          disabled={isVersionsLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder={isVersionsLoading ? "Loading versions..." : "Select NDS version"} />
          </SelectTrigger>
          <SelectContent>
            {availableVersions?.map((version) => (
              <SelectItem key={version} value={version}>
                {version}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Species Group</Label>
        <Select 
          value={selectedGluLamGroup} 
          onValueChange={handleGluLamGroupChange}
          disabled={isGluLamLoading || availableSpeciesGroups.length === 0}
        >
          <SelectTrigger>
            <SelectValue placeholder={isGluLamLoading ? "Loading..." : gluLamError ? "Error loading groups" : "Select species group"} />
          </SelectTrigger>
          <SelectContent>
            {availableSpeciesGroups.map((group) => (
              <SelectItem key={group} value={group}>
                {group} ({gluLamData?.species[group]?.symbol})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {selectedGluLamGroup && (
        <>
          <div className="space-y-2">
            <Label>Species</Label>
            <Select 
              value={selectedGluLamSpecies} 
              onValueChange={handleGluLamSpeciesChange}
              disabled={!gluLamData?.species[selectedGluLamGroup]}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select species" />
              </SelectTrigger>
              <SelectContent>
                {gluLamData?.species[selectedGluLamGroup]?.species.map((species) => (
                  <SelectItem key={species} value={species}>
                    {species}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedGluLamSpecies && currentSpeciesDesignValueTables && (
              <p className="text-sm text-muted-foreground">
                Design values provided in Tables: {currentSpeciesDesignValueTables}
              </p>
            )}
          </div>

          {selectedGluLamSpecies && (currentSpeciesDesignValueTables.includes("5A")) && (
            <div className="space-y-2">
              <Label>Combination Symbol</Label>
              <Select
                value={selectedCombinedSymbolKey || ""}
                onValueChange={handleCombinedSymbolChange}
                disabled={isLoadingSymbols || availableCombinedSymbols.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder={isLoadingSymbols ? "Loading symbols..." : "Select Combination Symbol"} />
                </SelectTrigger>
                <SelectContent>
                  {availableCombinedSymbols.map((symbolItem) => (
                    <SelectItem key={symbolItem.key} value={symbolItem.key}>
                      {symbolItem.display}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </>
      )}

      <div className="space-y-2 mt-2 mb-2">
        <Label>Loading Orientation</Label>
        <Select
          value={loadingOrientation}
          onValueChange={(value: 'perpendicular' | 'parallel') => setLoadingOrientation(value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select loading orientation" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={LOADING_ORIENTATION_PERPENDICULAR_KEY}>{LOADING_ORIENTATION_PERPENDICULAR_DISPLAY}</SelectItem>
            <SelectItem value={LOADING_ORIENTATION_PARALLEL_KEY}>{LOADING_ORIENTATION_PARALLEL_DISPLAY}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Glulam Size Inputs */}
      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="space-y-2">
          <Label htmlFor="glulamWidth">Width ({units.size})</Label>
          <Input
            id="glulamWidth"
            type="number"
            placeholder="Enter width"
            value={glulamWidth}
            onChange={(e) => setGlulamWidth(e.target.value)}
            min={0}
            step={0.25}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="glulamDepth">Depth ({units.size})</Label>
          <Input
            id="glulamDepth"
            type="number"
            placeholder="Enter depth"
            value={glulamDepth}
            onChange={(e) => setGlulamDepth(e.target.value)}
            min={0}
            step={0.25}
          />
        </div>
      </div>
    </>
  );
}
