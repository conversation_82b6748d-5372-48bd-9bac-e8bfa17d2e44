"use client";

import React from "react";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { UnitSystem } from "@/lib/types/units/unit-system";
// AdjustmentFactorSet is no longer needed here if we pass individual factors
// import type { AdjustmentFactorSet } from "@/lib/types/beam/beam-data";

interface DesignValueDisplayProps {
  property: "Fb" | "Ft" | "Fv" | "Fc_perp" | "Fc" | "E" | "Emin" | "G" | "density" | "Fb_pos" | "Fb_neg";
  value: number;
  baseValue: number;
  unitSystem: UnitSystem;
  
  // Individual factors from DesignValues type
  C_F?: number;         // Was sizeFactor
  C_M?: number;         // Wet Service Factor for the specific property
  C_t?: number;         // Temperature Factor for the specific property
  C_L?: number;         // Beam Stability Factor (was beamStabilityFactorCL)
  C_V?: number | null;  // Volume Factor (was volumeFactorCv)
  C_fu?: number;        // Flat Use Factor (was flatUseFactor)
  C_r?: number;         // Repetitive Member Factor (was repetitiveMemberFactor)
  C_i?: number;         // Incising Factor for the specific property
  
  isRepetitiveMember?: boolean; // Still needed to decide if Cr applies in tooltip text
}

interface SectionPropertyDisplayProps {
  symbol: string;
  value: number;
  unit: string;
}

// Add a mapping for property symbols to their mathematical notation
const propertySymbols = {
  Ft: "F<sub>t</sub>",
  Fb: "F<sub>b</sub>",
  Fb_pos: "F<sub>b+</sub>",
  Fb_neg: "F<sub>b-</sub>",
  Fv: "F<sub>v</sub>",
  Fc_perp: "F<sub>c⊥</sub>",
  Fc: "F<sub>c</sub>",
  E: "E",
  Emin: "E<sub>min</sub>",
  G: "G",
  density: "Density",
} as const; // Use 'as const' for stricter typing with keyof

// Add a mapping for adjustment factor symbols
const factorSymbols: Record<string, string> = {
  CF: "C<sub>F</sub>",      // Size Factor
  Cfu: "C<sub>fu</sub>",     // Flat Use Factor
  Cr: "C<sub>r</sub>",      // Repetitive Member Factor
  Cm: "C<sub>M</sub>",      // Wet Service Factor
  Ci: "C<sub>i</sub>",      // Incising Factor
  CL: "C<sub>L</sub>",      // Beam Stability Factor
  Ct: "C<sub>t</sub>",      // Temperature Factor
  Cv: "C<sub>V</sub>",      // Volume Factor
};

export function DesignValueDisplay({
  property,
  baseValue,
  value,
  unitSystem,
  C_F,
  C_M,
  C_t,
  C_L,
  C_V,
  C_fu,
  C_r,
  C_i,
  isRepetitiveMember,
}: DesignValueDisplayProps) {
  const units = getUnitsBySystem(unitSystem);

  const getPropertyLabel = (prop: keyof typeof propertySymbols) => {
    let adjustments: string[] = [];
    const addAdjustmentText = (text: string | null) => {
      if (text) adjustments.push(text);
    };

    // Helper to format individual factor text
    const formatFactor = (factorValue: number | undefined | null, symbolName: string): string | null => {
      if (factorValue != null && Math.abs(factorValue - 1.0) > 1e-9) {
        const precision = (symbolName === factorSymbols.CL || symbolName === factorSymbols.Cv || symbolName === factorSymbols.Cfu) ? 3 : 2;
        return `${symbolName} = ${factorValue.toFixed(precision)}`;
      }
      return null;
    };
    
    addAdjustmentText(formatFactor(C_M, factorSymbols.Cm));
    addAdjustmentText(formatFactor(C_i, factorSymbols.Ci));
    addAdjustmentText(formatFactor(C_t, factorSymbols.Ct));
    addAdjustmentText(formatFactor(C_F, factorSymbols.CF)); // For all applicable properties

    if (prop === "Fb" || prop === "Fb_pos" || prop === "Fb_neg") {
      addAdjustmentText(formatFactor(C_fu, factorSymbols.Cfu));
      if (isRepetitiveMember) { // C_r applies only if isRepetitiveMember is true
        addAdjustmentText(formatFactor(C_r, factorSymbols.Cr));
      }
      addAdjustmentText(formatFactor(C_L, factorSymbols.CL));
      addAdjustmentText(formatFactor(C_V, factorSymbols.Cv));
    }

    // Base descriptions (can be expanded)
    const descriptions: Record<keyof typeof propertySymbols, string> = {
      Ft: "Tension parallel to grain",
      Fb: "Bending",
      Fb_pos: "Positive Bending",
      Fb_neg: "Negative Bending",
      Fv: "Shear parallel to grain",
      Fc_perp: "Compression perpendicular to grain",
      Fc: "Compression parallel to grain",
      E: "Modulus of elasticity",
      Emin: "Minimum modulus of elasticity",
      G: "Specific gravity",
      density: "Density (lbs/ft³)",
    };
    
    const baseDescription = descriptions[prop] || prop;
    let label = baseDescription;
    if (adjustments.length > 0) {
      // Constructing the string carefully to avoid complex nested templates for the tool
      label += " (" + adjustments.join(", ") + ")";
    }
    return label;
  };

  const getBaseValue = (val: number, prop: keyof typeof propertySymbols) => {
    if (typeof val !== 'number' || isNaN(val)) return "N/A";
    if (prop === "density") return val.toFixed(1);
    if (prop === "G") return val.toFixed(3);
    return val.toFixed(0);
  };

  const getDisplayValue = (val: number, prop: keyof typeof propertySymbols) => {
    if (typeof val !== 'number' || isNaN(val)) return "N/A";
    if (prop === "density") return val.toFixed(1);
    if (prop === "G") return val.toFixed(3);
    return val.toFixed(0);
  };

  const getUnit = (prop: keyof typeof propertySymbols) => {
    if (prop === "density") return "lbs/ft³";
    if (prop === "G") return "";
    return units.pressure;
  };

  const hasAdjustments = 
    (C_F != null && Math.abs(C_F - 1.0) > 1e-9) ||
    (C_M != null && Math.abs(C_M - 1.0) > 1e-9) ||
    (C_i != null && Math.abs(C_i - 1.0) > 1e-9) ||
    (C_t != null && Math.abs(C_t - 1.0) > 1e-9) ||
    (C_L != null && Math.abs(C_L - 1.0) > 1e-9) ||
    (C_V != null && typeof C_V === 'number' && Math.abs(C_V - 1.0) > 1e-9) || // C_V can be null, so check type and value
    (C_fu != null && Math.abs(C_fu - 1.0) > 1e-9) ||
    (isRepetitiveMember && C_r != null && Math.abs(C_r - 1.0) > 1e-9);


  const valuesAreEqual = Math.abs(baseValue - value) < 1e-6;

  const renderFactorInTooltip = (factorValue: number | undefined | null, symbolName: string) => {
    if (factorValue != null && Math.abs(factorValue - 1.0) > 1e-9) {
      const precision = (symbolName === factorSymbols.CL || symbolName === factorSymbols.Cv || symbolName === factorSymbols.Cfu) ? 3 : 2;
      return (
        <div>
          ×{" "}
          <span dangerouslySetInnerHTML={{ __html: symbolName }} />{" "}
          = {factorValue.toFixed(precision)}
        </div>
      );
    }
    return null;
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center justify-between py-1">
            <div className="flex items-center gap-1">
              <span className="font-medium">
                <span
                  className="font-mono"
                  dangerouslySetInnerHTML={{
                    __html: propertySymbols[property],
                  }}
                ></span>
              </span>
              <Info className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="text-right">
              <span>
                {getDisplayValue(value, property)}
                {getUnit(property) ? ` ${getUnit(property)}` : ""} 
              </span>
              {hasAdjustments &&
                !valuesAreEqual &&
                property !== "G" &&
                property !== "density" && (
                  <div className="text-sm text-muted-foreground">
                    Base: {getBaseValue(baseValue, property)}{" "}
                    {units.pressure}
                    {/* Render factors relevant to the current property. Fb is most complex. */}
                    {renderFactorInTooltip(C_F, factorSymbols.CF)}
                    {/* For Fb, Cfu, Cr, CL, CV are specific. Others (CM, Ci, Ct) are general per property */} 
                    {(property === "Fb" || property === "Fb_pos" || property === "Fb_neg") && renderFactorInTooltip(C_fu, factorSymbols.Cfu)}
                    {(property === "Fb" || property === "Fb_pos" || property === "Fb_neg") && isRepetitiveMember && renderFactorInTooltip(C_r, factorSymbols.Cr)}
                    {renderFactorInTooltip(C_M, factorSymbols.Cm)}
                    {renderFactorInTooltip(C_i, factorSymbols.Ci)}
                    {renderFactorInTooltip(C_t, factorSymbols.Ct)}
                    {(property === "Fb" || property === "Fb_pos" || property === "Fb_neg") && renderFactorInTooltip(C_L, factorSymbols.CL)}
                    {(property === "Fb" || property === "Fb_pos" || property === "Fb_neg") && renderFactorInTooltip(C_V, factorSymbols.Cv)}
                  </div>
                )}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p
            dangerouslySetInnerHTML={{ __html: getPropertyLabel(property) }}
          ></p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function SectionPropertyDisplay({
  symbol,
  value,
  unit,
}: SectionPropertyDisplayProps) {
  return (
    <div className="flex items-center justify-between py-1">
      <span
        className="font-medium"
        dangerouslySetInnerHTML={{ __html: symbol }}
      ></span>
      <span>
        {value.toFixed(3)} {unit}
      </span>
    </div>
  );
}
