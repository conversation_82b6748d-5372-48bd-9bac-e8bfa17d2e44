import type { AdjustmentFactorSet } from "@/lib/types/beam/beam-data";

export interface DesignValues {
  minThickness: number;
  maxThickness: number;
  minWidth: number;
  maxWidth: number;
  Ft: number;  // Tension parallel to grain. This might be an original/base value.
  Fb: number;  // Bending. This might be an original/base value.
  Fv: number;  // Shear parallel to grain. This might be an original/base value.
  Fc_perp: number;  // Compression perpendicular to grain. This might be an original/base value.
  Fc: number;  // Compression parallel to grain. This might be an original/base value.
  E: number;   // Modulus of elasticity. This might be an original/base value.
  Emin: number; // Minimum modulus of elasticity. This might be an original/base value.
  G: number;   // Specific gravity. This might be an original/base value.
  commercial_grade: string;
  speciesCombination: string;
  grading_rules_agency: string;
  design_values_table: string;
  location: string;
  version: string;
  service_condition?: string;
  bending_Fb_Cr_Repetitive_Member?: number;
  repetitive_member_factor_Cr?: number;
  wet_service_factor_Cm_for_Fb?: number;
  wet_service_factor_Cm_for_Ft?: number;
  wet_service_factor_Cm_for_Fv?: number;
  wet_service_factor_Cm_for_Fc_perp?: number;
  wet_service_factor_Cm_for_Fc?: number;
  wet_service_factor_Cm_for_E_and_Emin?: number;
  size_factor_Cf_for_Fb?: number;
  size_factor_Cf_for_Fc?: number;
  size_factor_Cf_for_Ft?: number;
  adjusted_Fb?: number;
  adjusted_Ft?: number;
  adjusted_Fc?: number;
  adjusted_Fv?: number;
  adjusted_Fc_perp?: number;
  adjusted_E?: number;
  adjusted_Emin?: number;
  size_classification?: string;
  flatUseFactor?: number;

  // Add fields for original (unadjusted) values from Table 5A/Sawn
  original_Fb?: number | null;
  original_Fv?: number | null;
  original_Fc_perp?: number | null;
  original_Fc?: number | null;
  original_Ft?: number | null;
  original_E?: number | null;
  original_Emin?: number | null;
  original_G?: number | null;

  // Add fields for adjustment factors
  C_M_Fb?: number;
  C_M_Ft?: number;
  C_M_Fv?: number;
  C_M_Fc?: number;
  C_M_Fc_perp?: number;
  C_M_E?: number;
  C_M_Emin?: number;
  C_F?: number;
  C_fu?: number;
  C_r?: number;
  C_L?: number | null;
  C_V?: number | null;
  calculatedIncisingFactors?: {
    Fb: { factor: number };
    Ft: { factor: number };
    Fv: { factor: number };
    Fc: { factor: number };
    Fc_perp: { factor: number };
    E: { factor: number };
    Emin: { factor: number };
  };
  calculatedTemperatureFactors?: {
    Fb: { factor: number };
    Ft: { factor: number };
    Fv: { factor: number };
    Fc: { factor: number };
    Fc_perp: { factor: number };
    E: { factor: number };
    Emin: { factor: number };
  };

  // Add other factors used in glued-laminated.tsx and potentially sawn-lumber.tsx
  Fc_par?: number | null;      // Compression parallel to grain (often same as Fc or original_Fc)
  Frt?: number | null;         // Radial tension
  
  // Incising Factors
  C_i_Fb?: number;             // Incising Factor for Fb
  C_i_Ft?: number;             // Incising Factor for Ft
  C_i_Fv?: number;             // Incising Factor for Fv
  C_i_Fc_perp?: number;        // Incising Factor for Fc_perp
  C_i_Fc?: number;             // Incising Factor for Fc
  C_i_E?: number;              // Incising Factor for E and Emin
  
  lambda?: number;             // Time Effect Factor (LRFD)
  phi_b?: number;              // Resistance Factor Bending (LRFD)
  phi_v?: number;              // Resistance Factor Shear (LRFD)
  K_F?: number;                // Format Conversion Factor (ASD to LRFD)

  notes?: string;              // To store notes about adjustments

  Fb_pos?: number | null; // Bending stress positive
  original_Fb_pos?: number | null;
  adjusted_Fb_pos?: number | null;

  Fb_neg?: number | null; // Bending stress negative
  original_Fb_neg?: number | null;
  adjusted_Fb_neg?: number | null;
}

export interface Units {
  length: string;
  elasticModulus: string;
  momentOfInertia: string;
  dimension: string;
  area: string;
  sectionModulus: string;
  stress: string;
}

export interface DesignValueTooltip {
  symbol: string;
  description: string;
}

export const designValueTooltips: { [key: string]: DesignValueTooltip } = {
  Ft: { symbol: "Ft", description: "Tension parallel to grain" },
  Fb: { symbol: "Fb", description: "Bending" },
  Fv: { symbol: "Fv", description: "Shear parallel to grain" },
  Fc_perp: { symbol: "Fc⊥", description: "Compression perpendicular to grain" },
  Fc: { symbol: "Fc", description: "Compression parallel to grain" },
  E: { symbol: "E", description: "Modulus of elasticity" },
  Emin: { symbol: "Emin", description: "Minimum modulus of elasticity" },
  G: { symbol: "G", description: "Specific gravity" },
};

export const sectionPropertyTooltips: { [key: string]: string } = {
  A: "Cross-sectional area",
  S_x: "Section modulus about X-X axis (edgewise bending)",
  I_x: "Moment of inertia about X-X axis (edgewise bending)",
  S_y: "Section modulus about Y-Y axis (flatwise bending)",
  I_y: "Moment of inertia about Y-Y axis (flatwise bending)",
  r_x: "Radius of gyration about X-X axis",
  r_y: "Radius of gyration about Y-Y axis",
};