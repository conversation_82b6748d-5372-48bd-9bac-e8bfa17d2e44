import React, { useState, useEffect, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { NdsTableModal } from "../nds-table-modal";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import type {
  BeamPropertiesState,
  AdjustmentFactorSet,
} from "@/lib/types/beam/beam-data";
import type { DesignValues } from "./types";
import {
  useWoodData,
  useLumberProperties,
  useSawnLumberSpecies,
  useAvailableNdsVersions,
} from "@/hooks/use-wood-data";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import {
  DEFAULT_NOMINAL_B,
  DEFAULT_NOMINAL_D,
  DEFAULT_ADJUSTMENT_FACTORS,
  DEFAULT_TEMPERATURE,
} from "@/lib/constants/beam-constants";
import {
  DEFAULT_NDS_VERSION,
  DEFAULT_WOOD_SELECTIONS,
} from "@/lib/constants/nds-constants";

interface SawnLumberProps {
  properties: {
    length: number;
    elasticModulus: number;
    momentOfInertia: number;
  };
  unitSystem: UnitSystem;
  onChange: (
    properties: import("@/lib/types/beam/beam-data").BeamProperties & {
      area: number;
    }
  ) => void;
  beamPropertiesState: BeamPropertiesState;
  onBeamPropertiesStateChange: (state: BeamPropertiesState) => void;
  isWetService: boolean;
  moistureContent: number | null;
  isRepetitiveMember: boolean;
  isIncised: boolean;
  isTemperatureFactored: boolean;
  temperature: number | null;
  isBraced: boolean;
  lu: string;
}

export function SawnLumber({
  properties,
  unitSystem,
  onChange,
  beamPropertiesState,
  onBeamPropertiesStateChange,
  isWetService,
  moistureContent,
  isRepetitiveMember,
  isIncised,
  isTemperatureFactored,
  temperature,
  isBraced,
  lu,
}: SawnLumberProps) {
  const [selectedNdsVersion, setSelectedNdsVersion] = useState<string>(
    beamPropertiesState.selectedNdsVersion || DEFAULT_NDS_VERSION
  );
  const [selectedSpecies, setSelectedSpecies] = useState<string>(
    beamPropertiesState.selectedSpecies || DEFAULT_WOOD_SELECTIONS.SPECIES
  );
  const [selectedSpeciesCombination, setSelectedSpeciesCombination] =
    useState<string>(
      beamPropertiesState.selectedSpeciesCombination || DEFAULT_WOOD_SELECTIONS.SPECIES_COMBINATION
    );
  const [selectedGrade, setSelectedGrade] = useState<string>(
    beamPropertiesState.selectedGrade || DEFAULT_WOOD_SELECTIONS.GRADE
  );
  const [selectedSizeClassification, setSelectedSizeClassification] =
    useState<string>(
      beamPropertiesState.selectedSizeClassification || DEFAULT_WOOD_SELECTIONS.SIZE_CLASSIFICATION
    );
  const [selectedNominalSize, setSelectedNominalSize] = useState<string>(
    beamPropertiesState.selectedNominalSize || ""
  );
  const [manualWidth, setManualWidth] = useState<string>(
    beamPropertiesState.manualWidth || ""
  );
  const [manualDepth, setManualDepth] = useState<string>(
    beamPropertiesState.manualDepth || ""
  );
  const [sizeError, setSizeError] = useState<string>("");
  const [designValues, setDesignValues] = useState<DesignValues | null>(
    beamPropertiesState.designValues
  );
  const [incisingFactors, setIncisingFactors] = useState<AdjustmentFactorSet>(
    beamPropertiesState.incisingFactors || DEFAULT_ADJUSTMENT_FACTORS
  );
  const [temperatureFactors, setTemperatureFactors] = useState<AdjustmentFactorSet>(
    beamPropertiesState.temperatureFactors || DEFAULT_ADJUSTMENT_FACTORS
  );
  const [beamStabilityFactorCL, setBeamStabilityFactorCL] = useState<
    number | null
  >(beamPropertiesState.beamStabilityFactorCL);
  const [lumberProperties, setLumberProperties] = useState<any>(
    beamPropertiesState.lumberProperties
  );
  const [flatUseFactor, setFlatUseFactor] = useState<number>(
    beamPropertiesState.flatUseFactor
  );
  const [repetitiveMemberFactor, setRepetitiveMemberFactor] = useState<number>(
    beamPropertiesState.repetitiveMemberFactor
  );
  const [wetServiceFactor, setWetServiceFactor] = useState<AdjustmentFactorSet>(
    beamPropertiesState.wetServiceFactor || DEFAULT_ADJUSTMENT_FACTORS
  );
  const [currentBaseDesignValues, setCurrentBaseDesignValues] =
    useState<DesignValues | null>(null);
  const [currentLumberProperties, setCurrentLumberProperties] = useState<
    any | null
  >(beamPropertiesState.lumberProperties);
  const [
    effectiveBaseDesignValuesForCalc,
    setEffectiveBaseDesignValuesForCalc,
  ] = useState<DesignValues | null>(null);
  const [effectiveNominalBForCalc, setEffectiveNominalBForCalc] = useState<
    number | null
  >(null);
  const [effectiveNominalDForCalc, setEffectiveNominalDForCalc] = useState<
    number | null
  >(null);
  const [finalLumberPropertiesForParent, setFinalLumberPropertiesForParent] =
    useState<any | null>(null);

  const {
    data: woodData,
    error: woodError,
    isLoading: isWoodLoading,
  } = useWoodData(selectedNdsVersion);
  const {
    data: lumberData,
    error: lumberError,
    isLoading: isLumberLoading,
  } = useLumberProperties(selectedNdsVersion);
  const {
    data: sawnLumberData,
    error: sawnLumberError,
    isLoading: isSawnLumberLoading,
  } = useSawnLumberSpecies(selectedNdsVersion);
  const {
    data: availableVersions,
    error: versionsError,
    isLoading: isVersionsLoading,
  } = useAvailableNdsVersions();

  const units = getUnitsBySystem(unitSystem);

  const availableSpecies = useMemo(() => {
    if (!sawnLumberData?.species) return [];
    return Object.values(sawnLumberData.species)
      .flatMap((data) => data.species)
      .filter((value, index, self) => self.indexOf(value) === index)
      .sort();
  }, [sawnLumberData]);

  const availableSpeciesCombinations = useMemo(() => {
    if (!selectedSpecies || !sawnLumberData?.species) return [];
    return sawnLumberData.speciesCombinations.filter(
      (combination: string | number) =>
        sawnLumberData.species[combination].species.includes(selectedSpecies)
    );
  }, [selectedSpecies, sawnLumberData]);

  const availableSizeClassifications = useMemo(() => {
    if (
      !selectedSpeciesCombination ||
      !selectedGrade ||
      !woodData?.designValues
    )
      return [];

    const designValuesList = woodData.designValues[selectedSpeciesCombination];
    if (!designValuesList) return [];

    return designValuesList
      .filter(
        (dv: DesignValues) =>
          dv.commercial_grade === selectedGrade
      )
      .map((dv: DesignValues) => dv.size_classification)
      .filter(
        (value: any, index: any, self: string | any[]) =>
          value && self.indexOf(value) === index
      )
      .sort();
  }, [selectedSpeciesCombination, selectedGrade, woodData]);

  const availableNominalSizes = useMemo(() => {
    if (
      !selectedGrade ||
      !lumberData?.properties ||
      !selectedSizeClassification
    ) {
      return [];
    }

    const sizeClassificationProperties =
      lumberData.properties[selectedSizeClassification];
    if (!sizeClassificationProperties) {
      return [];
    }

    return sizeClassificationProperties.map(
      (prop: { nominal_size_bxd: any }) => prop.nominal_size_bxd
    );
  }, [selectedGrade, lumberData, selectedSizeClassification]);

  const validateManualSize = (width: number, depth: number) => {
    if (width <= 0 || depth <= 0) {
      setSizeError("Width and depth must be positive values.");
      return false;
    }
    setSizeError("");
    return true;
  };

  useEffect(() => {
    if (
      selectedSpeciesCombination &&
      selectedGrade &&
      selectedSizeClassification &&
      woodData?.designValues?.[selectedSpeciesCombination]
    ) {
      const matchingDV = woodData.designValues[selectedSpeciesCombination].find(
        (dv: DesignValues) =>
          dv.commercial_grade === selectedGrade &&
          dv.size_classification === selectedSizeClassification
      );
      setCurrentBaseDesignValues(matchingDV || null);
    } else {
      setCurrentBaseDesignValues(null);
    }
  }, [
    selectedSpeciesCombination,
    selectedGrade,
    selectedSizeClassification,
    woodData,
  ]);

  useEffect(() => {
    if (
      selectedNominalSize &&
      lumberData?.properties[selectedSizeClassification]
    ) {
      const matchingProps = lumberData.properties[
        selectedSizeClassification
      ].find(
        (prop: { nominal_size_bxd: string }) =>
          prop.nominal_size_bxd === selectedNominalSize
      );
      if (matchingProps) {
        setCurrentLumberProperties(matchingProps);
        setSizeError("");
        return;
      }
    }

    const localManualWidthFloat = parseFloat(beamPropertiesState.manualWidth);
    const localManualDepthFloat = parseFloat(beamPropertiesState.manualDepth);

    if (
      !selectedNominalSize &&
      !isNaN(localManualWidthFloat) &&
      localManualWidthFloat > 0 &&
      !isNaN(localManualDepthFloat) &&
      localManualDepthFloat > 0
    ) {
      setSizeError("");
      const area = localManualWidthFloat * localManualDepthFloat;
      const Ixx =
        (localManualWidthFloat * Math.pow(localManualDepthFloat, 3)) / 12;
      const Iyy =
        (localManualDepthFloat * Math.pow(localManualWidthFloat, 3)) / 12;
      const Sxx = Ixx / (localManualDepthFloat / 2);
      const Syy = Iyy / (localManualWidthFloat / 2);
      setCurrentLumberProperties({
        nominal_b: localManualWidthFloat,
        nominal_d: localManualDepthFloat,
        nominal_size_bxd: `${localManualWidthFloat} x ${localManualDepthFloat}`,
        standard_dressed_size_bxd: `${localManualWidthFloat} x ${localManualDepthFloat}`,
        standard_width: localManualWidthFloat,
        standard_depth: localManualDepthFloat,
        area_of_section_a_in2: area,
        Ixx,
        Iyy,
        Sxx,
        Syy,
      });
    }
  }, [selectedNominalSize, selectedSizeClassification]);

  // Effect A: Derive Basic Properties for Selection
  useEffect(() => {
    let ebdVal: DesignValues | null =
      currentBaseDesignValues || null;
    
    // If no base design values but we have species selected, try to find a default
    if (
      !currentBaseDesignValues &&
      selectedSpecies &&
      woodData?.designValues &&
      sawnLumberData?.species
    ) {
      const speciesKeys = Object.keys(woodData.designValues);
      let firstMatchForSpecies: DesignValues | undefined = undefined;
      for (const key of speciesKeys) {
        if (sawnLumberData.species[key]?.species.includes(selectedSpecies)) {
          const dvList = woodData.designValues[key];
          if (dvList && dvList.length > 0) {
            firstMatchForSpecies = dvList[0];
            break;
          }
        }
      }
      if (firstMatchForSpecies) {
        ebdVal = firstMatchForSpecies;
      }
    }
    setEffectiveBaseDesignValuesForCalc(ebdVal);

    // Determine effective dimensions
    let enbVal: number;
    let endVal: number;
    let flpVal: any | null = currentLumberProperties;

    if (
      currentLumberProperties &&
      currentLumberProperties.nominal_b > 0 &&
      currentLumberProperties.nominal_d > 0
    ) {
      enbVal = currentLumberProperties.nominal_b;
      endVal = currentLumberProperties.nominal_d;
    } else {
      const manualW = parseFloat(manualWidth);
      const manualD = parseFloat(manualDepth);
      if (!isNaN(manualW) && manualW > 0 && !isNaN(manualD) && manualD > 0) {
        enbVal = manualW;
        endVal = manualD;
        flpVal = {
          nominal_b: manualW,
          nominal_d: manualD,
          nominal_size_bxd: `${manualW} x ${manualD}`,
          standard_dressed_size_bxd: `${manualW} x ${manualD}`,
          standard_width: manualW,
          standard_depth: manualD,
          area_of_section_a_in2: manualW * manualD,
          Ixx: (manualW * Math.pow(manualD, 3)) / 12,
        };
      } else {
        enbVal = DEFAULT_NOMINAL_B;
        endVal = DEFAULT_NOMINAL_D;
        flpVal = null;
      }
    }
    setEffectiveNominalBForCalc(enbVal);
    setEffectiveNominalDForCalc(endVal);
    setFinalLumberPropertiesForParent(flpVal);

    // Set basic design values without calculations
    // For sawn lumber, Fb_pos and Fb_neg are typically the same as Fb
    if (ebdVal && ebdVal !== designValues) {
      const enhancedDesignValues: DesignValues = {
        ...ebdVal,
        // Set original values for tracking
        original_Fb: ebdVal.Fb,
        original_Fb_pos: ebdVal.Fb,
        original_Fb_neg: ebdVal.Fb,
        original_Ft: ebdVal.Ft,
        original_Fv: ebdVal.Fv,
        original_Fc: ebdVal.Fc,
        original_Fc_perp: ebdVal.Fc_perp,
        original_E: ebdVal.E,
        original_Emin: ebdVal.Emin,
        original_G: ebdVal.G,
        // Set positive and negative bending values equal to Fb for sawn lumber
        Fb_pos: ebdVal.Fb,
        Fb_neg: ebdVal.Fb,
        adjusted_Fb_pos: ebdVal.Fb,
        adjusted_Fb_neg: ebdVal.Fb,
      };
      setDesignValues(enhancedDesignValues);
    }
  }, [
    currentBaseDesignValues,
    currentLumberProperties,
    selectedSpecies,
    manualWidth,
    manualDepth,
    woodData,
    sawnLumberData,
    designValues,
  ]);

  // Effect B: Reset adjustment factors to defaults (calculations will be done on server)
  useEffect(() => {
    // Reset all adjustment factors to defaults since calculations will be server-side
    if (JSON.stringify(wetServiceFactor) !== JSON.stringify(DEFAULT_ADJUSTMENT_FACTORS)) {
      setWetServiceFactor(DEFAULT_ADJUSTMENT_FACTORS);
    }
    
    if (JSON.stringify(incisingFactors) !== JSON.stringify(DEFAULT_ADJUSTMENT_FACTORS)) {
      setIncisingFactors(DEFAULT_ADJUSTMENT_FACTORS);
    }

    if (JSON.stringify(temperatureFactors) !== JSON.stringify(DEFAULT_ADJUSTMENT_FACTORS)) {
      setTemperatureFactors(DEFAULT_ADJUSTMENT_FACTORS);
    }

    if (flatUseFactor !== 1.0) {
      setFlatUseFactor(1.0);
    }
    
    if (repetitiveMemberFactor !== 1.0) {
      setRepetitiveMemberFactor(1.0);
    }
    
    if (beamStabilityFactorCL !== null) {
      setBeamStabilityFactorCL(null);
    }
  }, [
    wetServiceFactor,
    incisingFactors,
    temperatureFactors,
    flatUseFactor,
    repetitiveMemberFactor,
    beamStabilityFactorCL,
  ]);

  // Effect C: Update Parent State (basic selections only - calculations handled server-side)
  useEffect(() => {
    const nextState: BeamPropertiesState = {
      ...beamPropertiesState, // Spread existing parent state first, to preserve unrelated fields

      // Overlay with all current SawnLumber selections/inputs
      selectedSpecies,
      selectedSpeciesCombination,
      selectedGrade,
      selectedSizeClassification,
      selectedNominalSize,
      manualWidth:
        finalLumberPropertiesForParent?.standard_width?.toString() ??
        beamPropertiesState.manualWidth,
      manualDepth:
        finalLumberPropertiesForParent?.standard_depth?.toString() ??
        beamPropertiesState.manualDepth,
      isWetService,
      isRepetitiveMember,
      isBraced,
      lu,
      isIncised,
      isTemperatureFactored,
      temperature,

      // Basic design values and lumber properties (no calculated adjustments)
      designValues: designValues,
      lumberProperties: finalLumberPropertiesForParent,
      
      // Reset adjustment factors to defaults (calculations will be server-side)
      flatUseFactor: 1.0,
      repetitiveMemberFactor: 1.0,
      wetServiceFactor: DEFAULT_ADJUSTMENT_FACTORS,
      incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
      temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
      beamStabilityFactorCL: null,
    };

    // Check for changes in basic selections and properties
    const hasChanged =
      nextState.selectedSpecies !== beamPropertiesState.selectedSpecies ||
      nextState.selectedSpeciesCombination !==
        beamPropertiesState.selectedSpeciesCombination ||
      nextState.selectedGrade !== beamPropertiesState.selectedGrade ||
      nextState.selectedSizeClassification !==
        beamPropertiesState.selectedSizeClassification ||
      nextState.selectedNominalSize !==
        beamPropertiesState.selectedNominalSize ||
      nextState.manualWidth !== beamPropertiesState.manualWidth ||
      nextState.manualDepth !== beamPropertiesState.manualDepth ||
      nextState.isWetService !== beamPropertiesState.isWetService ||
      nextState.isRepetitiveMember !== beamPropertiesState.isRepetitiveMember ||
      nextState.isBraced !== beamPropertiesState.isBraced ||
      nextState.lu !== beamPropertiesState.lu ||
      nextState.isIncised !== beamPropertiesState.isIncised ||
      nextState.isTemperatureFactored !== beamPropertiesState.isTemperatureFactored ||
      nextState.temperature !== beamPropertiesState.temperature ||
      JSON.stringify(nextState.designValues) !==
        JSON.stringify(beamPropertiesState.designValues) ||
      JSON.stringify(nextState.lumberProperties) !==
        JSON.stringify(beamPropertiesState.lumberProperties);

    if (hasChanged) {
      onBeamPropertiesStateChange(nextState);
    }
  }, [
    designValues,
    finalLumberPropertiesForParent,
    selectedSpecies,
    selectedSpeciesCombination,
    selectedGrade,
    selectedSizeClassification,
    selectedNominalSize,
    isWetService,
    isRepetitiveMember,
    isBraced,
    lu,
    isIncised,
    isTemperatureFactored,
    temperature,
    beamPropertiesState,
    onBeamPropertiesStateChange,
  ]);

  const handleNdsVersionChange = (version: string) => {
    setSelectedNdsVersion(version);
    // Reset all selections when version changes
    setSelectedSpecies("DOUGLAS FIR");
    setSelectedSpeciesCombination("");
    setSelectedGrade("");
    setSelectedSizeClassification("");
    setSelectedNominalSize("");
    setManualWidth("");
    setManualDepth("");
    setDesignValues(null);
    setLumberProperties(null);
    setSizeError("");

    // Update parent state
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedNdsVersion: version,
      selectedSpecies: "DOUGLAS FIR",
      selectedSpeciesCombination: "",
      selectedGrade: "",
      selectedSizeClassification: "",
      selectedNominalSize: "",
      manualWidth: "",
      manualDepth: "",
      designValues: null,
      lumberProperties: null,
    });
  };

  const handleSpeciesChange = (species: string) => {
    setSelectedSpecies(species);
    setSelectedSpeciesCombination("");
    setSelectedGrade("");
    setSelectedSizeClassification("");
    setSelectedNominalSize("");
    setDesignValues(null);
    setCurrentBaseDesignValues(null);
    setLumberProperties(null);
    setFinalLumberPropertiesForParent(null);

    // Update parent state immediately
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedNdsVersion: selectedNdsVersion,
      selectedSpecies: species,
      selectedSpeciesCombination: "",
      selectedGrade: "",
      selectedSizeClassification: "",
      selectedNominalSize: "",
      isIncised: false,
      incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
      isTemperatureFactored: false,
      temperature: DEFAULT_TEMPERATURE,
      temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
    });
  };

  const handleSpeciesCombinationChange = (combination: string) => {
    setSelectedSpeciesCombination(combination);
    setSelectedGrade("");
    setSelectedSizeClassification("");
    setSelectedNominalSize("");

    // Update parent state immediately
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedSpeciesCombination: combination,
      selectedGrade: "",
      selectedSizeClassification: "",
      selectedNominalSize: "",
      isIncised: false,
      incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
      isTemperatureFactored: false,
      temperature: DEFAULT_TEMPERATURE,
      temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
    });
  };

  const handleGradeChange = (grade: string) => {
    setSelectedGrade(grade);
    setSelectedSizeClassification("");
    setSelectedNominalSize("");

    // Update parent state immediately
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedGrade: grade,
      selectedSizeClassification: "",
      selectedNominalSize: "",
      isIncised: false,
      incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
      isTemperatureFactored: false,
      temperature: DEFAULT_TEMPERATURE,
      temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
    });
  };

  const handleSizeClassificationChange = (sizeClassification: string) => {
    setSelectedSizeClassification(sizeClassification);
    setSelectedNominalSize("");

    // Update parent state immediately
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedSizeClassification: sizeClassification,
      selectedNominalSize: "",
      isIncised: false,
      incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
      isTemperatureFactored: false,
      temperature: DEFAULT_TEMPERATURE,
      temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
    });
  };

  const handleDesignValuesSelect = (values: DesignValues) => {
    const species = values.speciesCombination.split("(")[0].trim();
    setSelectedSpecies(species);
    setSelectedSpeciesCombination(values.speciesCombination);
    setSelectedGrade(values.commercial_grade);
    if (values.size_classification) {
      setSelectedSizeClassification(values.size_classification);
    }
    setSelectedNominalSize("");

    // Update parent state immediately
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      selectedSpecies: species,
      selectedSpeciesCombination: values.speciesCombination,
      selectedGrade: values.commercial_grade,
      selectedSizeClassification: values.size_classification || "",
      selectedNominalSize: "",
      isIncised: false,
      incisingFactors: DEFAULT_ADJUSTMENT_FACTORS,
      isTemperatureFactored: false,
      temperature: DEFAULT_TEMPERATURE,
      temperatureFactors: DEFAULT_ADJUSTMENT_FACTORS,
    });
  };

  // Add handlers for manual dimension changes
  const handleManualWidthChange = (value: string) => {
    setManualWidth(value);
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      manualWidth: value,
    });
  };

  const handleManualDepthChange = (value: string) => {
    setManualDepth(value);
    setSelectedNominalSize(""); // Clear nominal size selection
    setLumberProperties(null); // Clear lumber properties from dropdown
    setCurrentLumberProperties(null); // Clear current lumber properties
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      manualDepth: value,
    });
  };

  // Add handlers for service condition changes
  const handleWetServiceChange = (checked: boolean) => {
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      isWetService: checked,
    });
  };

  const handleRepetitiveMemberChange = (checked: boolean) => {
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      isRepetitiveMember: checked,
    });
  };

  const handleBracedChange = (checked: boolean) => {
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      isBraced: checked,
    });
  };

  const handleLuChange = (value: string) => {
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      lu: value,
    });
  };

  const handleIncisingChange = (checked: boolean) => {
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      isIncised: checked,
    });
  };

  const handleTemperatureFactorChange = (checked: boolean) => {
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      isTemperatureFactored: checked,
    });
  };

  const handleTemperatureChange = (value: string) => {
    const newTemperature = parseFloat(value);
    onBeamPropertiesStateChange({
      ...beamPropertiesState,
      temperature: isNaN(newTemperature) ? null : newTemperature,
    });
  };

  return (
    <div className="space-y-4">
      {/* NDS Version Selection */}
      <div className="space-y-2">
        <Label>NDS Version</Label>
        <Select
          value={selectedNdsVersion}
          onValueChange={handleNdsVersionChange}
          disabled={isVersionsLoading}
        >
          <SelectTrigger>
            <SelectValue
              placeholder={
                isVersionsLoading
                  ? "Loading versions..."
                  : versionsError
                  ? "Error loading versions"
                  : "Select NDS version"
              }
            />
          </SelectTrigger>
          <SelectContent>
            {availableVersions?.map((version) => (
              <SelectItem key={version} value={version}>
                {version}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between mb-2">
          <Label>Wood Species</Label>
          <NdsTableModal 
            onSelectDesignValues={handleDesignValuesSelect} 
            selectedVersion={selectedNdsVersion}
          />
        </div>
        <Select
          value={selectedSpecies}
          onValueChange={handleSpeciesChange}
          disabled={isSawnLumberLoading}
        >
          <SelectTrigger>
            <SelectValue
              placeholder={
                isSawnLumberLoading
                  ? "Loading..."
                  : sawnLumberError
                  ? "Error loading species"
                  : "Select species"
              }
            />
          </SelectTrigger>
          <SelectContent>
            {availableSpecies.map((species) => (
              <SelectItem key={species} value={species}>
                {species}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {selectedSpecies && (
        <div className="space-y-2">
          <Label>Species Combination</Label>
          <Select
            value={selectedSpeciesCombination}
            onValueChange={handleSpeciesCombinationChange}
            disabled={isSawnLumberLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select species combination" />
            </SelectTrigger>
            <SelectContent>
              {availableSpeciesCombinations.map((combination: any) => (
                <SelectItem key={combination} value={combination}>
                  {combination}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedSpeciesCombination &&
            sawnLumberData?.species[selectedSpeciesCombination] && (
              <p className="text-sm text-muted-foreground">
                Design values provided in Tables{" "}
                {
                  sawnLumberData.species[selectedSpeciesCombination]
                    .designValues
                }
              </p>
            )}
        </div>
      )}

      {selectedSpeciesCombination && (
        <div className="space-y-2">
          <Label>Commercial Grade</Label>
          <Select value={selectedGrade} onValueChange={handleGradeChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select commercial grade" />
            </SelectTrigger>
            <SelectContent>
              {woodData?.commercialGrades[selectedSpeciesCombination]?.map(
                (grade: any) => (
                  <SelectItem key={grade} value={grade}>
                    {grade}
                  </SelectItem>
                )
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {selectedGrade && (
        <div className="space-y-2">
          <Label>Size Classification</Label>
          <Select
            value={selectedSizeClassification}
            onValueChange={handleSizeClassificationChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select size classification" />
            </SelectTrigger>
            <SelectContent>
              {availableSizeClassifications.map((sizeClass: any) => (
                <SelectItem key={sizeClass} value={sizeClass}>
                  {sizeClass}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {selectedSizeClassification && (
        <>
          <div className="space-y-2">
            <Label>Nominal Size</Label>
            {(!designValues?.design_values_table ||
              (designValues.design_values_table !== "4E" &&
                designValues.design_values_table !== "4F")) &&
            availableNominalSizes.length > 0 ? (
              <Select
                value={selectedNominalSize}
                onValueChange={setSelectedNominalSize}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select nominal size" />
                </SelectTrigger>
                <SelectContent>
                  {availableNominalSizes.map((size: any) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="space-y-4">
                {sizeError && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{sizeError}</AlertDescription>
                  </Alert>
                )}
                {designValues?.design_values_table &&
                  (designValues.design_values_table === "4E" ||
                    designValues.design_values_table === "4F") && (
                    <p className="text-sm text-muted-foreground">
                      Manual size entry is required for Table{" "}
                      {designValues.design_values_table}
                    </p>
                  )}
                {!availableNominalSizes.length &&
                  designValues?.size_classification && (
                    <p className="text-sm text-muted-foreground">
                      No standard sizes available for{" "}
                      {designValues.size_classification}. Please enter
                      dimensions manually.
                    </p>
                  )}
                
                {/* Manual Width and Depth Inputs for cases with no nominal sizes */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sawnManualWidth">Width ({units.size})</Label>
                    <Input
                      id="sawnManualWidth"
                      type="number"
                      placeholder="Enter width"
                      value={manualWidth || ''}
                      onChange={(e) => handleManualWidthChange(e.target.value)}
                      min={0}
                      step={0.1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sawnManualDepth">Depth ({units.size})</Label>
                    <Input
                      id="sawnManualDepth"
                      type="number"
                      placeholder="Enter depth"
                      value={manualDepth || ''}
                      onChange={(e) => handleManualDepthChange(e.target.value)}
                      min={0}
                      step={0.1}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      )}
      {(!designValues || !currentBaseDesignValues) && (
        <div className="p-4 border rounded-lg mt-4">
          <p className="text-sm text-muted-foreground">
            Select species, grade, and size to view calculated design values.
          </p>
        </div>
      )}
    </div>
  );
}
