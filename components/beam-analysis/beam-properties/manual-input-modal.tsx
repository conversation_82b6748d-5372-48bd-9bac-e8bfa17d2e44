"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";

interface ManualInputModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  initialElasticModulus?: number;
  initialManualWidth: string;
  initialManualDepth: string;
  initialAllowableFb?: number;
  initialAllowableFv?: number;
  onSave: (updatedProperties: {
    manual_E?: number;
    manualWidth: string;
    manualDepth: string;
    manual_Fb_allow?: number;
    manual_Fv_allow?: number;
  }) => void;
  unitSystem: UnitSystem;
}

export function ManualInputModal({
  isOpen,
  onOpenChange,
  initialElasticModulus,
  initialManualWidth,
  initialManualDepth,
  initialAllowableFb,
  initialAllowableFv,
  onSave,
  unitSystem,
}: ManualInputModalProps) {
  const [elasticModulus, setElasticModulus] = useState<string>(
    initialElasticModulus?.toString() ?? ""
  );
  const [manualWidth, setManualWidth] = useState<string>(initialManualWidth);
  const [manualDepth, setManualDepth] = useState<string>(initialManualDepth);
  const [allowableFb, setAllowableFb] = useState<string>(
    initialAllowableFb?.toString() ?? ""
  );
  const [allowableFv, setAllowableFv] = useState<string>(
    initialAllowableFv?.toString() ?? ""
  );

  const units = getUnitsBySystem(unitSystem);

  useEffect(() => {
    setElasticModulus(initialElasticModulus?.toString() ?? "");
    setManualWidth(initialManualWidth);
    setManualDepth(initialManualDepth);
    setAllowableFb(initialAllowableFb?.toString() ?? "");
    setAllowableFv(initialAllowableFv?.toString() ?? "");
  }, [
    initialElasticModulus,
    initialManualWidth,
    initialManualDepth,
    initialAllowableFb,
    initialAllowableFv,
    isOpen, // Re-initialize when modal opens
  ]);

  const handleSave = () => {
    onSave({
      manual_E: elasticModulus ? parseFloat(elasticModulus) : undefined,
      manualWidth: manualWidth,
      manualDepth: manualDepth,
      manual_Fb_allow: allowableFb ? parseFloat(allowableFb) : undefined,
      manual_Fv_allow: allowableFv ? parseFloat(allowableFv) : undefined,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Manual Inputs & Limits</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="elasticModulusModal" className="text-right col-span-1">
              E ({units.pressure})
            </Label>
            <Input
              id="elasticModulusModal"
              value={elasticModulus}
              onChange={(e) => setElasticModulus(e.target.value)}
              type="number"
              min={0.1}
              step={0.1}
              className="col-span-3"
              placeholder="e.g. 1600000"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="manualWidthModal" className="text-right col-span-1">
              Width ({units.length})
            </Label>
            <Input
              id="manualWidthModal"
              value={manualWidth}
              onChange={(e) => setManualWidth(e.target.value)}
              type="number"
              min={0}
              step={0.1}
              className="col-span-3"
              placeholder="Enter width"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="manualDepthModal" className="text-right col-span-1">
              Depth ({units.length})
            </Label>
            <Input
              id="manualDepthModal"
              value={manualDepth}
              onChange={(e) => setManualDepth(e.target.value)}
              type="number"
              min={0}
              step={0.1}
              className="col-span-3"
              placeholder="Enter depth"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="allowableFbModal" className="text-right col-span-1">
              Fb allow ({units.pressure})
            </Label>
            <Input
              id="allowableFbModal"
              value={allowableFb}
              onChange={(e) => setAllowableFb(e.target.value)}
              type="number"
              min={0}
              step={10}
              className="col-span-3"
              placeholder="Manual override"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="allowableFvModal" className="text-right col-span-1">
              Fv allow ({units.pressure})
            </Label>
            <Input
              id="allowableFvModal"
              value={allowableFv}
              onChange={(e) => setAllowableFv(e.target.value)}
              type="number"
              min={0}
              step={10}
              className="col-span-3"
              placeholder="Manual override"
            />
          </div>
          {/* Placeholder for other limits if needed in the future */}
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </DialogClose>
          <Button type="button" onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 