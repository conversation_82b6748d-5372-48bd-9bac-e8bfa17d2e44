"use client";

import { useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { BeamData } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { Type } from "@/lib/types/load/load-type";
import {
  SVG_CONSTANTS,
  SupportCollection,
  LoadCollection,
  Ruler,
  SpanRuler,
  BeamLine,
  BraceCollection,
  assignLevels,
  VisualizationBase,
  STANDARD_BEAM_DIMENSIONS,
  calculateBeamPositioning
} from "@/components/shared/visualization";

interface BeamVisualizationProps {
  beamData: BeamData;
  unitSystem: UnitSystem;
  beamPropertiesState?: BeamPropertiesState;
}

export function BeamVisualization({
  beamData,
  unitSystem,
  beamPropertiesState,
}: BeamVisualizationProps) {
  const units = useMemo(() => getUnitsBySystem(unitSystem), [unitSystem]);
  
  const svgWidth = 900;
  const svgHeight = 300;
  const margin = { top: 30, right: 15, bottom: 55, left: 40 };
  const drawingWidth = svgWidth - margin.left - margin.right;
  const drawingHeight = svgHeight - margin.top - margin.bottom;
  const beamLength = beamData?.properties?.length ?? 1;
  const scale = drawingWidth / beamLength;

  // Calculate beamBaseY position
  const rulerLabelOffsetBelowRulerLine = SVG_CONSTANTS.MAJOR_TICK_HEIGHT / 2 + SVG_CONSTANTS.LABEL_FONT_SIZE + 2;
  const desiredMainRulerY = svgHeight - margin.bottom - rulerLabelOffsetBelowRulerLine;
  const beamBaseY = desiredMainRulerY - SVG_CONSTANTS.RULER_Y_OFFSET;
  
  if (!beamData?.properties?.length) {
    return (
      <Card className="h-full flex items-center justify-center border-none shadow-none">
        <p className="text-muted-foreground text-xs p-2">Beam properties required.</p>
      </Card>
    );
  }

  // Check if beam is fully braced (wood or steel)
  const isWoodFullyBraced = beamPropertiesState?.isBraced && 
    (!beamPropertiesState.lu || 
     parseFloat(beamPropertiesState.lu) <= 0 || 
     parseFloat(beamPropertiesState.lu) >= beamLength);
  
  const isSteelFullyBraced = (beamPropertiesState as any)?.isSteelBraced && 
    (!(beamPropertiesState as any)?.steelUnbracedLength || 
     parseFloat((beamPropertiesState as any).steelUnbracedLength) <= 0 || 
     parseFloat((beamPropertiesState as any).steelUnbracedLength) >= beamLength);
  
  const isFullyBraced = isWoodFullyBraced || isSteelFullyBraced;

  // Process load groups with levels
  const allLoadGroups = beamData.loadGroups || [];
  const groupsWithLevels = assignLevels(allLoadGroups);

  // Calculate max magnitude for load scaling
  const maxTotalMagnitude = Math.max(
    ...groupsWithLevels.flatMap((group) =>
      (group.loads || []).flatMap(load => {
        const startMag = Math.abs(load.startMagnitude ?? 0);
        if (load.type === Type.DISTRIBUTED) {
          const endMag = Math.abs(load.endMagnitude ?? startMag);
          return [startMag, endMag];
        } 
        return [startMag];
      })
    ),
    1
  );

  return (
    <Card className="border-none shadow-none" style={{ backgroundColor: '#1e183a' }}>
      <CardContent className="p-0.5">
        <svg 
          width="100%" 
          height={svgHeight} 
          viewBox={`0 0 ${svgWidth} ${svgHeight}`} 
          preserveAspectRatio="xMidYMid meet" 
          className="w-full"
        >
          <g transform={`translate(${margin.left}, 0)`}> 
            {/* Beam line with optional bracing border */}
            <BeamLine 
              drawingWidth={drawingWidth}
              baseY={beamBaseY}
              isFullyBraced={isFullyBraced}
            />
            
            {/* Rulers */}
            <Ruler
              drawingWidth={drawingWidth}
              beamLength={beamLength}
              baseY={beamBaseY}
              unitSystem={unitSystem}
            />
            <SpanRuler
              supports={beamData.supports}
              scale={scale}
              baseY={beamBaseY}
            />
            
            {/* Supports */}
            <SupportCollection
              supports={beamData.supports}
              scale={scale}
              baseY={beamBaseY}
              unitSystem={unitSystem}
            />
            
            {/* Braces */}
            <BraceCollection
              scale={scale}
              baseY={beamBaseY}
              beamLength={beamLength}
              beamPropertiesState={beamPropertiesState}
            />
            
            {/* Loads */}
            <LoadCollection
              loadGroups={groupsWithLevels}
              scale={scale}
              baseY={beamBaseY}
              maxTotalMagnitude={maxTotalMagnitude}
            />
          </g>
        </svg>
      </CardContent>
    </Card>
  );
}
