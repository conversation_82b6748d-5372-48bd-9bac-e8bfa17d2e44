/**
 * Material Selection Constants
 * 
 * This file contains all constants used in material selection components
 * including lumber properties, steel properties, and design values.
 */

/**
 * Material type constants
 */
export const MATERIAL_TYPES = {
  WOOD: 'wood',
  STEEL: 'steel',
} as const;

export const LUMBER_TYPES = {
  SAWN: 'sawn',
  GLULAM: 'glulam',
} as const;

/**
 * Default values for lumber properties
 */
export const LUMBER_DEFAULTS = {
  // Nominal dimensions (inches)
  NOMINAL_WIDTH: 2,
  NOMINAL_DEPTH: 8,
  
  // Manual dimensions (inches) - Use default nominal dimensions
  MANUAL_WIDTH: '3.5',  // Actual dimension for 2x4 
  MANUAL_DEPTH: '7.25', // Actual dimension for 2x8
  
  // Size classifications
  SIZE_CLASSIFICATION: 'Posts and Timbers',
  
  // Grade defaults
  GRADE: 'No.1',
  
  // Species defaults
  SPECIES: 'Douglas Fir-Larch',
  SPECIES_COMBINATION: 'Douglas Fir-Larch',
  
  // Adjustment factors
  FLAT_USE_FACTOR: 1.0,
  REPETITIVE_MEMBER_FACTOR: 1.0,
  WET_SERVICE_FACTOR: 1.0,
  TEMPERATURE_FACTOR: 1.0,
  INCISING_FACTOR: 1.0,
  
  // Temperature
  TEMPERATURE_FAHRENHEIT: 70,
  
  // Service conditions
  DRY_SERVICE: false,
  WET_SERVICE: false,
  
  // Member properties
  IS_REPETITIVE: false,
  IS_INCISED: false,
  IS_BRACED: true,
  
  // Unbraced length
  UNBRACED_LENGTH: '0',
} as const;

/**
 * Steel material constants
 */
export const STEEL_DEFAULTS = {
  // Standard steel properties
  MODULUS_OF_ELASTICITY_KSI: 29000,
  MODULUS_OF_ELASTICITY_PSI: 29000000, // 29,000 ksi * 1000
  YIELD_STRENGTH_KSI: 36, // A36 steel default
  YIELD_STRENGTH_PSI: 36000,
  
  // Steel grades
  GRADE_A36: 'A36',
  GRADE_A992: 'A992',
  GRADE_A572: 'A572',
  
  // Default dimensions
  WIDTH: 6,
  DEPTH: 12,
  THICKNESS: 0.5,
} as const;

/**
 * Design value limits and tolerances
 */
export const DESIGN_VALUE_LIMITS = {
  // Minimum thickness constraints (inches)
  MIN_THICKNESS: 0.75,
  MAX_THICKNESS: 24,
  
  // Width constraints (inches)
  MIN_WIDTH: 2,
  MAX_WIDTH: 24,
  
  // Stress limits (psi)
  MIN_STRESS: 100,
  MAX_STRESS: 10000,
  
  // Modulus limits (psi)
  MIN_MODULUS: 500000,
  MAX_MODULUS: 3000000,
  
  // Specific gravity limits
  MIN_SPECIFIC_GRAVITY: 0.3,
  MAX_SPECIFIC_GRAVITY: 1.0,
} as const;

/**
 * Adjustment factor ranges
 */
export const ADJUSTMENT_FACTOR_RANGES = {
  // Wet service factors (Table 2A NDS)
  WET_SERVICE: {
    MIN: 0.67,
    MAX: 1.0,
    FB: 0.85,
    FT: 1.0,
    FV: 1.0,
    FC_PERP: 0.67,
    FC: 0.8,
    E: 0.9,
  },
  
  // Temperature factors (Table 2B NDS)
  TEMPERATURE: {
    MIN: 0.7,
    MAX: 1.0,
    DEFAULT: 1.0,
  },
  
  // Size factors
  SIZE_FACTOR: {
    MIN: 0.8,
    MAX: 1.5,
    DEFAULT: 1.0,
  },
  
  // Flat use factor
  FLAT_USE: {
    MIN: 1.0,
    MAX: 1.2,
    DEFAULT: 1.0,
  },
  
  // Repetitive member factor
  REPETITIVE_MEMBER: {
    MIN: 1.0,
    MAX: 1.15,
    DEFAULT: 1.0,
  },
  
  // Incising factors (Table 4A NDS)
  INCISING: {
    MIN: 0.8,
    MAX: 1.0,
    FB: 0.8,
    FT: 0.8,
    FV: 1.0,
    FC_PERP: 1.0,
    FC: 0.8,
    E: 0.95,
  },
  
  // Volume factor for glulam
  VOLUME_FACTOR: {
    MIN: 0.8,
    MAX: 1.0,
    DEFAULT: 1.0,
  },
} as const;

/**
 * NDS (National Design Specification) constants
 */
export const NDS_CONSTANTS = {
  // Default NDS version
  DEFAULT_VERSION: '2018',
  
  // Available versions
  VERSIONS: ['2018', '2015', '2012'] as const,
  
  // Table references
  TABLES: {
    DESIGN_VALUES: 'Table 4A',
    WET_SERVICE: 'Table 2A',
    TEMPERATURE: 'Table 2B',
    SIZE_FACTORS: 'Table 4B',
    INCISING: 'Table 4A',
    GLULAM_DESIGN_VALUES: 'Table 5A',
  },
  
  // Standard lumber sizes (nominal)
  NOMINAL_SIZES: [
    '2x4', '2x6', '2x8', '2x10', '2x12', '2x14',
    '4x4', '4x6', '4x8', '4x10', '4x12', '4x14', '4x16',
    '6x6', '6x8', '6x10', '6x12', '6x14', '6x16', '6x18', '6x20',
    '8x8', '8x10', '8x12', '8x14', '8x16', '8x18', '8x20', '8x22', '8x24',
    '10x10', '10x12', '10x14', '10x16', '10x18', '10x20', '10x22', '10x24',
    '12x12', '12x14', '12x16', '12x18', '12x20', '12x22', '12x24',
  ] as const,
} as const;

/**
 * Material property labels and descriptions
 */
export const MATERIAL_LABELS = {
  // Material types
  MATERIAL_TYPE: 'Material Type',
  LUMBER_TYPE: 'Lumber Type',
  
  // Lumber selection labels
  NDS_VERSION: 'NDS Version',
  SPECIES: 'Species',
  SPECIES_COMBINATION: 'Species Combination',
  GRADE: 'Grade',
  SIZE_CLASSIFICATION: 'Size Classification',
  NOMINAL_SIZE: 'Nominal Size',
  
  // Manual input labels
  MANUAL_WIDTH: 'Width',
  MANUAL_DEPTH: 'Depth',
  MANUAL_THICKNESS: 'Thickness',
  
  // Service condition labels
  WET_SERVICE: 'Wet Service Conditions',
  MOISTURE_CONTENT: 'Moisture Content (%)',
  REPETITIVE_MEMBER: 'Repetitive Member',
  INCISED_LUMBER: 'Incised Lumber',
  TEMPERATURE_FACTOR: 'Temperature Factor',
  TEMPERATURE: 'Temperature (°F)',
  BRACED: 'Braced Against Lateral-Torsional Buckling',
  UNBRACED_LENGTH: 'Unbraced Length',
  
  // Design value labels
  FB: 'Bending Stress (Fb)',
  FT: 'Tension Parallel (Ft)',
  FV: 'Shear Parallel (Fv)',
  FC: 'Compression Parallel (Fc)',
  FC_PERP: 'Compression Perpendicular (Fc⊥)',
  E: 'Modulus of Elasticity (E)',
  EMIN: 'Minimum Modulus (Emin)',
  G: 'Specific Gravity (G)',
  
  // Steel labels
  STEEL_GRADE: 'Steel Grade',
  YIELD_STRENGTH: 'Yield Strength (Fy)',
  TENSILE_STRENGTH: 'Tensile Strength (Fu)',
  
  // Section property labels
  AREA: 'Cross-sectional Area (A)',
  MOMENT_OF_INERTIA_X: 'Moment of Inertia X-X (Ix)',
  MOMENT_OF_INERTIA_Y: 'Moment of Inertia Y-Y (Iy)',
  SECTION_MODULUS_X: 'Section Modulus X-X (Sx)',
  SECTION_MODULUS_Y: 'Section Modulus Y-Y (Sy)',
  RADIUS_OF_GYRATION_X: 'Radius of Gyration X-X (rx)',
  RADIUS_OF_GYRATION_Y: 'Radius of Gyration Y-Y (ry)',
} as const;

/**
 * Validation messages
 */
export const VALIDATION_MESSAGES = {
  // Size validation
  SIZE_TOO_SMALL: 'Size must be greater than minimum allowed dimensions',
  SIZE_TOO_LARGE: 'Size exceeds maximum allowed dimensions',
  INVALID_SIZE: 'Invalid size specification',
  
  // Material validation
  SPECIES_REQUIRED: 'Species selection is required',
  GRADE_REQUIRED: 'Grade selection is required',
  SIZE_CLASSIFICATION_REQUIRED: 'Size classification is required',
  
  // Value validation
  VALUE_OUT_OF_RANGE: 'Value is outside acceptable range',
  POSITIVE_VALUE_REQUIRED: 'Value must be positive',
  NUMERIC_VALUE_REQUIRED: 'Numeric value required',
  MANUAL_WIDTH_INVALID: 'Manual width must be a positive number',
  MANUAL_DEPTH_INVALID: 'Manual depth must be a positive number',
  
  // Design value validation
  DESIGN_VALUES_NOT_FOUND: 'Design values not found for selected material properties',
  ADJUSTMENT_FACTOR_ERROR: 'Error calculating adjustment factors',
} as const;

/**
 * UI Constants specific to materials
 */
export const MATERIALS_UI = {
  // Modal dimensions
  MODAL_WIDTH: 'max-w-4xl',
  MODAL_HEIGHT: 'max-h-[80vh]',
  
  // Grid layouts
  GRID_COLS_2: 'grid-cols-2',
  GRID_COLS_3: 'grid-cols-3',
  GRID_COLS_4: 'grid-cols-4',
  
  // Spacing
  SPACING_SM: 'space-y-2',
  SPACING_MD: 'space-y-4',
  SPACING_LG: 'space-y-6',
  
  // Input widths
  INPUT_WIDTH_SM: 'w-24',
  INPUT_WIDTH_MD: 'w-32',
  INPUT_WIDTH_LG: 'w-48',
  INPUT_WIDTH_FULL: 'w-full',
} as const;

/**
 * Common tooltips and help text
 */
export const MATERIAL_TOOLTIPS = {
  WET_SERVICE: 'Select when lumber will be used in conditions where moisture content exceeds 19% for an extended time period',
  REPETITIVE_MEMBER: 'Select when lumber is one of three or more essentially parallel members spaced not more than 24" on center',
  INCISED_LUMBER: 'Select when lumber has been incised for pressure treatment',
  TEMPERATURE_FACTOR: 'Apply when lumber will be exposed to sustained elevated temperatures',
  FLAT_USE: 'Factor applied when dimension lumber is loaded perpendicular to its wide face',
  VOLUME_FACTOR: 'Factor that accounts for the reduced probability of a strength-reducing defect occurring in a larger volume',
  SIZE_FACTOR: 'Factor that accounts for the effect of member size on bending strength',
} as const;

/**
 * Export type definitions for constants
 */
export type MaterialType = typeof MATERIAL_TYPES[keyof typeof MATERIAL_TYPES];
export type LumberType = typeof LUMBER_TYPES[keyof typeof LUMBER_TYPES];
export type NdsVersion = typeof NDS_CONSTANTS.VERSIONS[number];
export type NominalSize = typeof NDS_CONSTANTS.NOMINAL_SIZES[number];

/**
 * Default adjustment factors for calculations
 */
export const DEFAULT_ADJUSTMENT_FACTORS = {
  Fb: 1.0,
  Ft: 1.0,
  Fv: 1.0,
  Fc: 1.0,
  Fc_perp: 1.0,
  E: 1.0,
  Emin: 1.0,
  G: 1.0,
};

/**
 * Type definition for adjustment factor sets
 */
export type AdjustmentFactorSet = typeof DEFAULT_ADJUSTMENT_FACTORS; 