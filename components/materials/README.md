# Materials Component

The Materials component provides comprehensive material selection and property calculation functionality for structural analysis applications. This component was refactored from the original `beam-properties` component to make it reusable across different types of structural calculations (beams, columns, lateral analysis, etc.).

## Overview

This component handles:
- Material type selection (wood, steel, etc.)
- Lumber species and grade selection according to NDS specifications
- Design value calculation with adjustment factors
- Section property computation
- Service condition adjustments
- Material property validation

## Features

### 🌲 Wood Materials
- **Sawn Lumber Support**: Complete NDS-compliant sawn lumber selection
- **Species Selection**: Comprehensive database of wood species and combinations
- **Grade Selection**: Support for various lumber grades
- **Size Classifications**: Posts and Timbers, Dimension Lumber, etc.
- **Adjustment Factors**: Wet service, temperature, incising, repetitive member factors
- **Design Values**: Real-time calculation of adjusted design values

### 🏗️ Steel Materials
- **Grade Selection**: A36, A992, A572, and other standard grades
- **Property Database**: Yield strength, ultimate strength, modulus values
- **Section Properties**: Area, moment of inertia, section modulus calculations

### 📊 General Features
- **Unit System Support**: Imperial and metric units
- **Real-time Validation**: Input validation with helpful error messages
- **Search and Filter**: Searchable design value tables
- **Responsive UI**: Mobile-friendly material selection interface
- **Type Safety**: Full TypeScript support with comprehensive types

## File Structure

```
components/materials/
├── index.ts                 # Main export file
├── README.md               # This documentation
├── constants.ts            # Material constants and defaults
├── types.ts               # TypeScript type definitions
├── sawn-lumber.tsx        # Sawn lumber selection component
├── nds-table-modal.tsx    # NDS design values table modal
└── [future components]    # Steel, glulam, engineered lumber components
```

## Usage

### Basic Import

```typescript
import { SawnLumber, LUMBER_DEFAULTS, MaterialProperties } from '@/components/materials';
```

### Sawn Lumber Component

```tsx
import { SawnLumber } from '@/components/materials';

function MyBeamDesign() {
  const [beamProperties, setBeamProperties] = useState(/* ... */);
  const [beamPropertiesState, setBeamPropertiesState] = useState(/* ... */);

  return (
    <SawnLumber
      properties={beamProperties}
      unitSystem={UnitSystem.IMPERIAL}
      onChange={setBeamProperties}
      beamPropertiesState={beamPropertiesState}
      onBeamPropertiesStateChange={setBeamPropertiesState}
      isWetService={false}
      moistureContent={null}
      isRepetitiveMember={false}
      isIncised={false}
      isTemperatureFactored={false}
      temperature={null}
      isBraced={true}
      lu="0"
    />
  );
}
```

### Material Validation

```typescript
import { validateMaterialProperties, MaterialProperties } from '@/components/materials';

const material: MaterialProperties = {
  materialType: 'wood',
  lumberType: 'sawn',
  // ... other properties
};

const validation = validateMaterialProperties(material);
if (!validation.isValid) {
  console.error('Material validation errors:', validation.errors);
}
```

### Creating Default Properties

```typescript
import { createDefaultMaterialProperties, MATERIAL_TYPES } from '@/components/materials';

const defaultWoodMaterial = createDefaultMaterialProperties(
  MATERIAL_TYPES.WOOD,
  LUMBER_TYPES.SAWN
);
```

## Constants

The component provides extensive constants to eliminate magic strings and numbers:

### Material Types
- `MATERIAL_TYPES`: Wood, steel, etc.
- `LUMBER_TYPES`: Sawn, glulam, engineered
- `LUMBER_DEFAULTS`: Default values for lumber properties
- `STEEL_DEFAULTS`: Default values for steel properties

### Design Limits
- `DESIGN_VALUE_LIMITS`: Min/max constraints for design values
- `ADJUSTMENT_FACTOR_RANGES`: Valid ranges for adjustment factors

### NDS Constants
- `NDS_CONSTANTS`: NDS versions, table references, nominal sizes
- `MATERIAL_LABELS`: UI labels for consistency
- `VALIDATION_MESSAGES`: Standardized error messages

### UI Constants
- `MATERIALS_UI`: Grid layouts, spacing, modal dimensions
- `MATERIAL_TOOLTIPS`: Help text for material properties

## Types

Comprehensive TypeScript types ensure type safety:

```typescript
// Core design values interface
interface DesignValues {
  Fb: number;          // Bending stress
  Ft: number;          // Tension parallel
  Fv: number;          // Shear parallel
  Fc_perp: number;     // Compression perpendicular
  Fc: number;          // Compression parallel
  E: number;           // Modulus of elasticity
  Emin: number;        // Minimum modulus
  G: number;           // Specific gravity
  // ... many more properties
}

// Complete material properties
interface MaterialProperties {
  materialType: 'wood' | 'steel' | 'concrete' | 'composite';
  lumberType?: 'sawn' | 'glulam' | 'engineered';
  designValues: DesignValues | null;
  sectionProperties: SectionProperties | null;
  serviceConditions: ServiceConditions;
  selectionState: LumberSelectionState | null;
}
```

## Components

### SawnLumber Component

The main component for sawn lumber selection with features:
- NDS version selection
- Species and grade selection
- Size classification
- Manual dimension override
- Service condition adjustments
- Real-time design value calculation

**Props:**
- `properties`: Beam structural properties
- `unitSystem`: Imperial or metric units
- `onChange`: Property change callback
- `beamPropertiesState`: Current state
- `onBeamPropertiesStateChange`: State change callback
- Service condition flags (wet service, repetitive member, etc.)

### NdsTableModal Component

A searchable modal for selecting design values from NDS tables:
- Filterable table of design values
- Search by species, grade, or size classification
- Formatted display of engineering values
- Row selection and confirmation

## Adjustment Factors

The component automatically calculates adjustment factors according to NDS:

### Wet Service Factors (Table 2A)
- Applied when moisture content > 19%
- Different factors for different stress types
- Ranges from 0.67 to 1.0

### Temperature Factors (Table 2B)
- Applied for sustained elevated temperatures
- Ranges from 0.7 to 1.0

### Size Factors (Table 4B)
- Applied based on member dimensions
- Affects bending strength

### Other Factors
- Flat use factor
- Repetitive member factor
- Incising factors
- Volume factor (for glulam)

## Validation

Built-in validation ensures data integrity:

```typescript
const validation = validateMaterialProperties(material);
// Returns: { isValid: boolean, errors: string[], warnings: string[] }
```

Validation checks:
- Required material type
- Wood-specific requirements (lumber type, design values, selection state)
- Steel-specific requirements (steel properties)
- Section property validity
- Value ranges and constraints

## Migration from beam-properties

If migrating from the old `beam-properties` component:

1. **Update imports:**
   ```typescript
   // Old
   import { DesignValues } from '@/components/beam-analysis/beam-properties/types';
   
   // New
   import { DesignValues } from '@/components/materials';
   ```

2. **Use new constants:**
   ```typescript
   // Old
   const species = "Douglas Fir-Larch";
   
   // New
   import { LUMBER_DEFAULTS } from '@/components/materials';
   const species = LUMBER_DEFAULTS.SPECIES;
   ```

3. **Component usage remains the same** - all props and callbacks are preserved for backward compatibility.

## Best Practices

1. **Use Constants**: Always use provided constants instead of magic strings
2. **Validate Properties**: Use validation functions before using material properties
3. **Handle Loading States**: Components handle async data loading gracefully
4. **Error Handling**: Display validation errors to users
5. **Type Safety**: Leverage TypeScript types for compile-time safety

## Extension Points

The component is designed for extensibility:

1. **New Material Types**: Add support for concrete, composite materials
2. **Additional Lumber Types**: Add engineered lumber, CLT, etc.
3. **International Standards**: Extend beyond NDS for international codes
4. **Custom Calculations**: Add custom adjustment factor calculations

## Dependencies

- React 18+
- TypeScript 4.5+
- Radix UI components
- Lucide React icons
- Custom hooks: `use-wood-data`

## Contributing

When adding new features:

1. Add constants to `constants.ts`
2. Define types in `types.ts`
3. Create components following existing patterns
4. Update exports in `index.ts`
5. Add comprehensive documentation
6. Include validation logic
7. Write tests for new functionality

## License

This component is part of the StructuralEngApp project and follows the same license terms. 