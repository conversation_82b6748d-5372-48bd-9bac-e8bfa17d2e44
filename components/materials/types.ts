/**
 * Material Selection Types
 * 
 * This file contains all TypeScript types and interfaces used throughout
 * the material selection components for structural analysis.
 */

import type { AdjustmentFactorSet } from "@/lib/types/beam/beam-data";

/**
 * Core design values interface representing material properties
 * from design specification tables (NDS, AISC, etc.)
 */
export interface DesignValues {
  // Dimensional constraints
  /** Minimum allowable thickness (inches) */
  minThickness: number;
  /** Maximum allowable thickness (inches) */
  maxThickness: number;
  /** Minimum allowable width (inches) */
  minWidth: number;
  /** Maximum allowable width (inches) */
  maxWidth: number;

  // Base design values (psi) - these are the original tabulated values
  /** Tension parallel to grain (psi) */
  Ft: number;
  /** Bending stress (psi) */
  Fb: number;
  /** Shear parallel to grain (psi) */
  Fv: number;
  /** Compression perpendicular to grain (psi) */
  Fc_perp: number;
  /** Compression parallel to grain (psi) */
  Fc: number;
  /** Modulus of elasticity (psi) */
  E: number;
  /** Minimum modulus of elasticity (psi) */
  Emin: number;
  /** Specific gravity */
  G: number;

  // Material identification
  /** Commercial grade designation */
  commercial_grade: string;
  /** Species or species combination */
  speciesCombination: string;
  /** Grading rules agency */
  grading_rules_agency: string;
  /** Reference design values table */
  design_values_table: string;
  /** Geographic location or region */
  location: string;
  /** Specification version */
  version: string;
  /** Service condition classification */
  service_condition?: string;

  // Repetitive member factors
  /** Bending stress with repetitive member factor applied */
  bending_Fb_Cr_Repetitive_Member?: number;
  /** Repetitive member factor value */
  repetitive_member_factor_Cr?: number;

  // Wet service factors (Table 2A NDS)
  /** Wet service factor for bending */
  wet_service_factor_Cm_for_Fb?: number;
  /** Wet service factor for tension */
  wet_service_factor_Cm_for_Ft?: number;
  /** Wet service factor for shear */
  wet_service_factor_Cm_for_Fv?: number;
  /** Wet service factor for compression perpendicular */
  wet_service_factor_Cm_for_Fc_perp?: number;
  /** Wet service factor for compression parallel */
  wet_service_factor_Cm_for_Fc?: number;
  /** Wet service factor for modulus values */
  wet_service_factor_Cm_for_E_and_Emin?: number;

  // Size factors (Table 4B NDS)
  /** Size factor for bending */
  size_factor_Cf_for_Fb?: number;
  /** Size factor for compression */
  size_factor_Cf_for_Fc?: number;
  /** Size factor for tension */
  size_factor_Cf_for_Ft?: number;

  // Adjusted design values (after applying all factors)
  /** Adjusted bending stress */
  adjusted_Fb?: number;
  /** Adjusted tension stress */
  adjusted_Ft?: number;
  /** Adjusted compression stress */
  adjusted_Fc?: number;
  /** Adjusted shear stress */
  adjusted_Fv?: number;
  /** Adjusted compression perpendicular stress */
  adjusted_Fc_perp?: number;
  /** Adjusted modulus of elasticity */
  adjusted_E?: number;
  /** Adjusted minimum modulus */
  adjusted_Emin?: number;

  // Size classification and factors
  /** Size classification category */
  size_classification?: string;
  /** Flat use factor */
  flatUseFactor?: number;

  // Original unadjusted values for reference
  /** Original bending stress from table */
  original_Fb?: number | null;
  /** Original shear stress from table */
  original_Fv?: number | null;
  /** Original compression perpendicular from table */
  original_Fc_perp?: number | null;
  /** Original compression parallel from table */
  original_Fc?: number | null;
  /** Original tension stress from table */
  original_Ft?: number | null;
  /** Original modulus from table */
  original_E?: number | null;
  /** Original minimum modulus from table */
  original_Emin?: number | null;
  /** Original specific gravity from table */
  original_G?: number | null;

  // Individual adjustment factors applied
  /** Wet service factor for bending */
  C_M_Fb?: number;
  /** Wet service factor for tension */
  C_M_Ft?: number;
  /** Wet service factor for shear */
  C_M_Fv?: number;
  /** Wet service factor for compression */
  C_M_Fc?: number;
  /** Wet service factor for compression perpendicular */
  C_M_Fc_perp?: number;
  /** Wet service factor for modulus */
  C_M_E?: number;
  /** Wet service factor for minimum modulus */
  C_M_Emin?: number;
  /** Flat use factor */
  C_F?: number;
  /** Flat use factor (alternative notation) */
  C_fu?: number;
  /** Repetitive member factor */
  C_r?: number;
  /** Beam stability factor */
  C_L?: number | null;
  /** Volume factor (glulam) */
  C_V?: number | null;

  // Calculated adjustment factors
  /** Incising factors for all properties */
  calculatedIncisingFactors?: {
    Fb: { factor: number };
    Ft: { factor: number };
    Fv: { factor: number };
    Fc: { factor: number };
    Fc_perp: { factor: number };
    E: { factor: number };
    Emin: { factor: number };
  };

  /** Temperature factors for all properties */
  calculatedTemperatureFactors?: {
    Fb: { factor: number };
    Ft: { factor: number };
    Fv: { factor: number };
    Fc: { factor: number };
    Fc_perp: { factor: number };
    E: { factor: number };
    Emin: { factor: number };
  };

  // Additional structural properties
  /** Compression parallel to grain (alternative) */
  Fc_par?: number | null;
  /** Radial tension stress */
  Frt?: number | null;

  // Individual incising factors (Table 4A NDS)
  /** Incising factor for bending */
  C_i_Fb?: number;
  /** Incising factor for tension */
  C_i_Ft?: number;
  /** Incising factor for shear */
  C_i_Fv?: number;
  /** Incising factor for compression perpendicular */
  C_i_Fc_perp?: number;
  /** Incising factor for compression parallel */
  C_i_Fc?: number;
  /** Incising factor for modulus values */
  C_i_E?: number;

  // Load and Resistance Factor Design (LRFD) factors
  /** Time effect factor */
  lambda?: number;
  /** Resistance factor for bending */
  phi_b?: number;
  /** Resistance factor for shear */
  phi_v?: number;
  /** Format conversion factor (ASD to LRFD) */
  K_F?: number;

  /** Additional notes about adjustments or calculations */
  notes?: string;

  // Glulam specific bending values
  /** Positive bending stress */
  Fb_pos?: number | null;
  /** Original positive bending stress */
  original_Fb_pos?: number | null;
  /** Adjusted positive bending stress */
  adjusted_Fb_pos?: number | null;

  /** Negative bending stress */
  Fb_neg?: number | null;
  /** Original negative bending stress */
  original_Fb_neg?: number | null;
  /** Adjusted negative bending stress */
  adjusted_Fb_neg?: number | null;
}

/**
 * Unit system configuration for display
 */
export interface Units {
  /** Length unit (in, ft, mm, m) */
  length: string;
  /** Elastic modulus unit (psi, ksi, MPa, GPa) */
  elasticModulus: string;
  /** Moment of inertia unit (in⁴, mm⁴) */
  momentOfInertia: string;
  /** Dimensional unit (in, mm) */
  dimension: string;
  /** Area unit (in², mm²) */
  area: string;
  /** Section modulus unit (in³, mm³) */
  sectionModulus: string;
  /** Stress unit (psi, ksi, MPa) */
  stress: string;
}

/**
 * Design value tooltip information for UI display
 */
export interface DesignValueTooltip {
  /** Symbol or notation */
  symbol: string;
  /** Description of the property */
  description: string;
}

/**
 * Steel material properties
 */
export interface SteelProperties {
  /** Steel grade (A36, A992, etc.) */
  grade: string;
  /** Yield strength (psi or MPa) */
  fy: number;
  /** Ultimate tensile strength (psi or MPa) */
  fu: number;
  /** Modulus of elasticity (psi or MPa) */
  E: number;
  /** Shear modulus (psi or MPa) */
  G?: number;
  /** Poisson's ratio */
  nu?: number;
  /** Density (pcf or kg/m³) */
  density?: number;
}

/**
 * Cross-sectional properties for any material
 */
export interface SectionProperties {
  /** Cross-sectional area */
  A: number;
  /** Moment of inertia about strong axis */
  Ix: number;
  /** Moment of inertia about weak axis */
  Iy: number;
  /** Section modulus about strong axis */
  Sx: number;
  /** Section modulus about weak axis */
  Sy: number;
  /** Radius of gyration about strong axis */
  rx: number;
  /** Radius of gyration about weak axis */
  ry: number;
  /** Plastic section modulus about strong axis */
  Zx?: number;
  /** Plastic section modulus about weak axis */
  Zy?: number;
}

/**
 * Material selection state for lumber
 */
export interface LumberSelectionState {
  /** NDS version */
  ndsVersion: string;
  /** Selected species */
  species: string;
  /** Species combination */
  speciesCombination: string;
  /** Lumber grade */
  grade: string;
  /** Size classification category */
  sizeClassification: string;
  /** Nominal size designation */
  nominalSize: string;
  /** Manual width override */
  manualWidth: string;
  /** Manual depth override */
  manualDepth: string;
}

/**
 * Service condition factors and flags
 */
export interface ServiceConditions {
  /** Wet service conditions flag */
  isWetService: boolean;
  /** Moisture content percentage */
  moistureContent: number | null;
  /** Repetitive member flag */
  isRepetitiveMember: boolean;
  /** Incised lumber flag */
  isIncised: boolean;
  /** Temperature factor applied flag */
  isTemperatureFactored: boolean;
  /** Operating temperature (°F) */
  temperature: number | null;
  /** Braced against lateral-torsional buckling */
  isBraced: boolean;
  /** Unbraced length for stability calculations */
  unbraceLength: string;
}

/**
 * Complete material properties for analysis
 */
export interface MaterialProperties {
  /** Material type identifier */
  materialType: 'wood' | 'steel' | 'concrete' | 'composite';
  /** Lumber type for wood materials */
  lumberType?: 'sawn' | 'glulam' | 'engineered';
  /** Design values */
  designValues: DesignValues | null;
  /** Section properties */
  sectionProperties: SectionProperties | null;
  /** Steel properties (if applicable) */
  steelProperties?: SteelProperties | null;
  /** Service conditions */
  serviceConditions: ServiceConditions;
  /** Selection state */
  selectionState: LumberSelectionState | null;
}

/**
 * Validation result for material properties
 */
export interface MaterialValidationResult {
  /** Validation passed flag */
  isValid: boolean;
  /** List of error messages */
  errors: string[];
  /** List of warning messages */
  warnings: string[];
}

/**
 * Predefined design value tooltips for common properties
 */
export const designValueTooltips: { [key: string]: DesignValueTooltip } = {
  Ft: { symbol: "Ft", description: "Tension parallel to grain" },
  Fb: { symbol: "Fb", description: "Bending" },
  Fv: { symbol: "Fv", description: "Shear parallel to grain" },
  Fc_perp: { symbol: "Fc⊥", description: "Compression perpendicular to grain" },
  Fc: { symbol: "Fc", description: "Compression parallel to grain" },
  E: { symbol: "E", description: "Modulus of elasticity" },
  Emin: { symbol: "Emin", description: "Minimum modulus of elasticity" },
  G: { symbol: "G", description: "Specific gravity" },
};

/**
 * Section property tooltips for display
 */
export const sectionPropertyTooltips: { [key: string]: string } = {
  A: "Cross-sectional area",
  S_x: "Section modulus about X-X axis (edgewise bending)",
  I_x: "Moment of inertia about X-X axis (edgewise bending)",
  S_y: "Section modulus about Y-Y axis (flatwise bending)",
  I_y: "Moment of inertia about Y-Y axis (flatwise bending)",
  r_x: "Radius of gyration about X-X axis",
  r_y: "Radius of gyration about Y-Y axis",
};

/**
 * Type guards for material type checking
 */
export const isWoodMaterial = (material: MaterialProperties): boolean => {
  return material.materialType === 'wood';
};

export const isSteelMaterial = (material: MaterialProperties): boolean => {
  return material.materialType === 'steel';
};

export const isSawnLumber = (material: MaterialProperties): boolean => {
  return isWoodMaterial(material) && material.lumberType === 'sawn';
};

export const isGlulamLumber = (material: MaterialProperties): boolean => {
  return isWoodMaterial(material) && material.lumberType === 'glulam';
}; 