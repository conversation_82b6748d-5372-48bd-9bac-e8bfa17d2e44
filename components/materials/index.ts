/**
 * Materials Component Index
 * 
 * This file exports all material-related components, types, and utilities
 * for use throughout the application. The materials component provides
 * comprehensive material selection and property calculation for structural
 * analysis including wood, steel, and other materials.
 */

// Import types and constants for internal use
import type {
  MaterialProperties,
  MaterialValidationResult,
} from './types';
import {
  isWoodMaterial,
  isSteelMaterial,
} from './types';
import {
  LUMBER_DEFAULTS,
  NDS_CONSTANTS,
  VALIDATION_MESSAGES,
  type MaterialType,
  type LumberType,
} from './constants';

// Core Components
export { SawnLumber } from './sawn-lumber';
export { NdsTableModal } from './nds-table-modal';

// Types and Interfaces
export type {
  DesignValues,
  Units,
  DesignValueTooltip,
  SteelProperties,
  SectionProperties,
  LumberSelectionState,
  ServiceConditions,
  MaterialProperties,
  MaterialValidationResult,
} from './types';

export {
  designValueTooltips,
  sectionPropertyTooltips,
  isWoodMaterial,
  isSteelMaterial,
  isSawnLumber,
  isGlulamLumber,
} from './types';

// Constants
export {
  MATERIAL_TYPES,
  LUMBER_TYPES,
  LUMBER_DEFAULTS,
  STEEL_DEFAULTS,
  DESIGN_VALUE_LIMITS,
  ADJUSTMENT_FACTOR_RANGES,
  NDS_CONSTANTS,
  MATERIAL_LABELS,
  VALIDATION_MESSAGES,
  MATERIALS_UI,
  MATERIAL_TOOLTIPS,
} from './constants';

export type {
  MaterialType,
  LumberType,
  NdsVersion,
  NominalSize,
} from './constants';

/**
 * Re-export commonly used constants for convenience
 */
export const DEFAULT_LUMBER_SPECIES = LUMBER_DEFAULTS.SPECIES;
export const DEFAULT_LUMBER_GRADE = LUMBER_DEFAULTS.GRADE;
export const DEFAULT_NDS_VERSION = NDS_CONSTANTS.DEFAULT_VERSION;

/**
 * Material validation utilities
 */
export const validateMaterialProperties = (material: MaterialProperties): MaterialValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if material type is specified
  if (!material.materialType) {
    errors.push('Material type is required');
  }

  // Wood-specific validation
  if (isWoodMaterial(material)) {
    if (!material.lumberType) {
      errors.push('Lumber type is required for wood materials');
    }

    if (!material.designValues) {
      errors.push('Design values are required for wood materials');
    }

    if (!material.selectionState) {
      errors.push('Selection state is required for wood materials');
    } else {
      const { species, grade, sizeClassification } = material.selectionState;
      
      if (!species) {
        errors.push(VALIDATION_MESSAGES.SPECIES_REQUIRED);
      }
      
      if (!grade) {
        errors.push(VALIDATION_MESSAGES.GRADE_REQUIRED);
      }
      
      if (!sizeClassification) {
        errors.push(VALIDATION_MESSAGES.SIZE_CLASSIFICATION_REQUIRED);
      }
    }
  }

  // Steel-specific validation
  if (isSteelMaterial(material)) {
    if (!material.steelProperties) {
      errors.push('Steel properties are required for steel materials');
    }
  }

  // Section properties validation
  if (!material.sectionProperties) {
    warnings.push('Section properties are not defined');
  } else {
    const { A, Ix, Iy, Sx, Sy } = material.sectionProperties;
    
    if (A <= 0) {
      errors.push('Cross-sectional area must be positive');
    }
    
    if (Ix <= 0 || Iy <= 0) {
      errors.push('Moments of inertia must be positive');
    }
    
    if (Sx <= 0 || Sy <= 0) {
      errors.push('Section moduli must be positive');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Utility function to create default material properties
 */
export const createDefaultMaterialProperties = (
  materialType: MaterialType,
  lumberType?: LumberType
): MaterialProperties => {
  return {
    materialType,
    lumberType,
    designValues: null,
    sectionProperties: null,
    serviceConditions: {
      isWetService: LUMBER_DEFAULTS.WET_SERVICE,
      moistureContent: null,
      isRepetitiveMember: LUMBER_DEFAULTS.IS_REPETITIVE,
      isIncised: LUMBER_DEFAULTS.IS_INCISED,
      isTemperatureFactored: false,
      temperature: null,
      isBraced: LUMBER_DEFAULTS.IS_BRACED,
      unbraceLength: LUMBER_DEFAULTS.UNBRACED_LENGTH,
    },
    selectionState: materialType === 'wood' ? {
      ndsVersion: NDS_CONSTANTS.DEFAULT_VERSION,
      species: LUMBER_DEFAULTS.SPECIES,
      speciesCombination: LUMBER_DEFAULTS.SPECIES_COMBINATION,
      grade: LUMBER_DEFAULTS.GRADE,
      sizeClassification: LUMBER_DEFAULTS.SIZE_CLASSIFICATION,
      nominalSize: '',
      manualWidth: LUMBER_DEFAULTS.MANUAL_WIDTH,
      manualDepth: LUMBER_DEFAULTS.MANUAL_DEPTH,
    } : null,
  };
}; 