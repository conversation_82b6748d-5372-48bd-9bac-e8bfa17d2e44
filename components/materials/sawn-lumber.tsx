/**
 * Sawn Lumber Component - API-Driven Version
 * 
 * This component provides material selection for sawn lumber members according to NDS.
 * It uses APIs to fetch lumber properties and size factors dynamically.
 * 
 * Features:
 * - Species, grade, and size classification selection with auto-defaults
 * - Nominal size selection from API data
 * - Toggle between nominal sizes and manual dimensions
 * - Design value lookup from NDS tables
 * - Clean, simple interface
 */

import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";
import type { BeamPropertiesState } from "@/lib/types/beam/beam-data";
import type { DesignValues } from "./types";
import {
  useWoodData,
  useSawnLumberSpecies,
  useAvailableNdsVersions,
  useLumberProperties,
} from "@/hooks/use-wood-data";
import {
  LUMBER_DEFAULTS,
  LUMBER_TYPES,
  NDS_CONSTANTS,
  MATERIAL_LABELS,
  VALIDATION_MESSAGES,
} from "./constants";
import { NdsTableModal } from "./nds-table-modal";

/**
 * Lumber properties interface from API
 */
interface LumberProperties {
  size_classification: string;
  nominal_size_bxd: string;
  nominal_b: number;
  nominal_d: number;
  standard_dressed_size_bxd: string;
  standard_width: number;
  standard_depth: number;
  area_of_section_a_in2: number;
  Sxx: number;
  Ixx: number;
  Syy: number;
  Iyy: number;
  weight_25_lbspft3: number;
  weight_30_lbspft3: number;
  weight_35_lbspft3: number;
  weight_40_lbspft3: number;
  weight_45_lbspft3: number;
  weight_50_lbspft3: number;
  notes?: string;
  version: string;
}

/**
 * Lumber data interface from API
 */
interface LumberData {
  sizeClassifications: string[];
  properties: {
    [sizeClassification: string]: LumberProperties[];
  };
}

/**
 * Props for the SawnLumber component
 */
interface SawnLumberProps {
  /** Beam structural properties */
  properties: {
    length: number;
    elasticModulus: number;
    momentOfInertia: number;
  };
  /** Unit system for display */
  unitSystem: UnitSystem;
  /** Callback when properties change */
  onChange: (
    properties: import("@/lib/types/beam/beam-data").BeamProperties & {
      area: number;
    }
  ) => void;
  /** Current beam properties state */
  beamPropertiesState: BeamPropertiesState;
  /** Callback when beam properties state changes */
  onBeamPropertiesStateChange: (state: BeamPropertiesState) => void;
  /** Wet service conditions flag */
  isWetService: boolean;
  /** Moisture content percentage */
  moistureContent: number | null;
  /** Repetitive member flag */
  isRepetitiveMember: boolean;
  /** Incised lumber flag */
  isIncised: boolean;
  /** Temperature factor applied flag */
  isTemperatureFactored: boolean;
  /** Operating temperature */
  temperature: number | null;
  /** Braced against lateral-torsional buckling */
  isBraced: boolean;
  /** Unbraced length for stability calculations */
  lu: string;
}

/**
 * Sawn Lumber Selection Component with API Integration
 * 
 * Uses lumber properties API for nominal sizes and design value lookup.
 * All calculations are handled server-side.
 */
export function SawnLumber({
  properties,
  unitSystem,
  onChange,
  beamPropertiesState,
  onBeamPropertiesStateChange,
}: SawnLumberProps) {
  // ==========================================
  // State Management
  // ==========================================
  
  const [selectedNdsVersion, setSelectedNdsVersion] = useState<string>(
    beamPropertiesState.selectedNdsVersion || NDS_CONSTANTS.DEFAULT_VERSION
  );
  
  const [selectedSpecies, setSelectedSpecies] = useState<string>(
    beamPropertiesState.selectedSpecies || ""
  );
  
  const [selectedSpeciesCombination, setSelectedSpeciesCombination] = useState<string>(
    beamPropertiesState.selectedSpeciesCombination || ""
  );
  
  const [selectedGrade, setSelectedGrade] = useState<string>(
    beamPropertiesState.selectedGrade || ""
  );
  
  const [selectedSizeClassification, setSelectedSizeClassification] = useState<string>(
    beamPropertiesState.selectedSizeClassification || ""
  );
  
  const [selectedNominalSize, setSelectedNominalSize] = useState<string>(
    beamPropertiesState.selectedNominalSize || ""
  );
  
  const [useManualDimensions, setUseManualDimensions] = useState<boolean>(
    (beamPropertiesState as any).useManualDimensions || false
  );
  
  const [manualWidth, setManualWidth] = useState<string>(
    beamPropertiesState.manualWidth || ""
  );
  
  const [manualDepth, setManualDepth] = useState<string>(
    beamPropertiesState.manualDepth || ""
  );
  
  const [sizeError, setSizeError] = useState<string>("");

  // ==========================================
  // Data Hooks
  // ==========================================
  
  const {
    data: woodData,
    error: woodError,
    isLoading: isWoodLoading,
  } = useWoodData(selectedNdsVersion);
  
  const {
    data: sawnLumberData,
    error: sawnLumberError,
    isLoading: isSawnLumberLoading,
  } = useSawnLumberSpecies(selectedNdsVersion);
  
  const {
    data: availableVersions,
    error: versionsError,
    isLoading: isVersionsLoading,
  } = useAvailableNdsVersions();

  const {
    data: lumberPropertiesData,
    error: lumberPropertiesError,
    isLoading: isLumberPropertiesLoading,
  } = useLumberProperties(selectedNdsVersion);

  // ==========================================
  // Computed Values
  // ==========================================
  
  const units = getUnitsBySystem(unitSystem);

  /**
   * Available species list from sawn lumber data
   */
  const availableSpecies = useMemo(() => {
    if (!sawnLumberData?.species) return [];
    return Object.values(sawnLumberData.species)
      .flatMap((data) => data.species)
      .filter((value, index, self) => self.indexOf(value) === index)
      .sort();
  }, [sawnLumberData]);

  /**
   * Available species combinations based on selected species
   */
  const availableSpeciesCombinations = useMemo(() => {
    if (!selectedSpecies || !sawnLumberData?.species) return [];
    return sawnLumberData.speciesCombinations.filter(
      (combination: string | number) =>
        sawnLumberData.species[combination].species.includes(selectedSpecies)
    );
  }, [selectedSpecies, sawnLumberData]);

  /**
   * Available grades based on current species combination
   */
  const availableGrades = useMemo(() => {
    if (!selectedSpeciesCombination || !woodData?.designValues) return [];

    const designValuesList = woodData.designValues[selectedSpeciesCombination];
    if (!designValuesList) return [];

    return designValuesList
      .map((dv: DesignValues) => dv.commercial_grade)
      .filter((grade, index, self) => grade && self.indexOf(grade) === index)
      .sort();
  }, [selectedSpeciesCombination, woodData]);

  /**
   * Available size classifications based on current selections
   */
  const availableSizeClassifications = useMemo(() => {
    if (!selectedSpeciesCombination || !selectedGrade || !woodData?.designValues) return [];

    const designValuesList = woodData.designValues[selectedSpeciesCombination];
    if (!designValuesList) return [];

    return designValuesList
      .filter((dv: DesignValues) => dv.commercial_grade === selectedGrade)
      .map((dv: DesignValues) => dv.size_classification)
      .filter((classification, index, self) => 
        classification && self.indexOf(classification) === index
      )
      .sort();
  }, [selectedSpeciesCombination, selectedGrade, woodData]);

  /**
   * Available nominal sizes based on selected size classification
   */
  const availableNominalSizes = useMemo(() => {
    if (!selectedSizeClassification || !lumberPropertiesData?.properties) return [];
    
    const propertiesForClassification = lumberPropertiesData.properties[selectedSizeClassification];
    if (!propertiesForClassification) return [];
    
    return propertiesForClassification
      .map((prop: LumberProperties) => prop.nominal_size_bxd)
      .filter((size: string, index: number, self: string[]) => size && self.indexOf(size) === index)
      .sort((a: string, b: string) => {
        // Sort by width first, then depth
        const [aWidth, aDepth] = a.split(' x ').map(Number);
        const [bWidth, bDepth] = b.split(' x ').map(Number);
        return aWidth - bWidth || aDepth - bDepth;
      });
  }, [selectedSizeClassification, lumberPropertiesData]);

  /**
   * Current lumber properties based on nominal size selection
   */
  const currentLumberProperties = useMemo(() => {
    if (!selectedSizeClassification || !selectedNominalSize || !lumberPropertiesData?.properties) {
      return null;
    }
    
    const propertiesForClassification = lumberPropertiesData.properties[selectedSizeClassification];
    if (!propertiesForClassification) {
      return null;
    }
    
    return propertiesForClassification.find(
      (prop: LumberProperties) => prop.nominal_size_bxd === selectedNominalSize
    ) || null;
  }, [selectedSizeClassification, selectedNominalSize, lumberPropertiesData]);

  /**
   * Current design values based on selections
   * Enhanced for sawn lumber with proper Fb_pos and Fb_neg values
   */
  const currentDesignValues = useMemo(() => {
    if (!selectedSpeciesCombination || !selectedGrade || !selectedSizeClassification || !woodData?.designValues) {
      return null;
    }

    const designValuesList = woodData.designValues[selectedSpeciesCombination];
    if (!designValuesList) return null;

    const baseDesignValues = designValuesList.find((dv: DesignValues) => 
      dv.commercial_grade === selectedGrade && 
      dv.size_classification === selectedSizeClassification
    );

    if (!baseDesignValues) return null;

    // For sawn lumber, set Fb_pos and Fb_neg equal to Fb
    // Also set original values for tracking
    const enhancedDesignValues: DesignValues = {
      ...baseDesignValues,
      // Set original values for tracking
      original_Fb: baseDesignValues.Fb,
      original_Ft: baseDesignValues.Ft,
      original_Fv: baseDesignValues.Fv,
      original_Fc: baseDesignValues.Fc,
      original_Fc_perp: baseDesignValues.Fc_perp,
      original_E: baseDesignValues.E,
      original_Emin: baseDesignValues.Emin,
      original_G: baseDesignValues.G,
      // Set positive and negative bending values equal to Fb for sawn lumber
      Fb_pos: baseDesignValues.Fb,
      Fb_neg: baseDesignValues.Fb,
      adjusted_Fb_pos: baseDesignValues.Fb,
      adjusted_Fb_neg: baseDesignValues.Fb,
    };

    return enhancedDesignValues;
  }, [selectedSpeciesCombination, selectedGrade, selectedSizeClassification, woodData]);

  /**
   * Filtered design values for NDS table modal
   */
  const filteredDesignValues = useMemo(() => {
    if (!selectedSpeciesCombination || !selectedGrade || !selectedSizeClassification || !woodData?.designValues) {
      return [];
    }

    const designValuesList = woodData.designValues[selectedSpeciesCombination];
    if (!designValuesList) return [];

    return designValuesList.filter((dv: DesignValues) => 
      dv.commercial_grade === selectedGrade && 
      dv.size_classification === selectedSizeClassification
    );
  }, [selectedSpeciesCombination, selectedGrade, selectedSizeClassification, woodData]);

  // ==========================================
  // Effects for Auto-Selection
  // ==========================================

  // Auto-select first available species when data loads
  useEffect(() => {
    if (availableSpecies.length > 0 && !selectedSpecies) {
      const defaultSpecies = availableSpecies.find(s => s.includes('Douglas Fir')) || availableSpecies[0];
      setSelectedSpecies(defaultSpecies);
    }
  }, [availableSpecies, selectedSpecies]);

  // Auto-select first available species combination when species changes
  useEffect(() => {
    if (availableSpeciesCombinations.length > 0 && !selectedSpeciesCombination) {
      setSelectedSpeciesCombination(availableSpeciesCombinations[0]);
    }
  }, [availableSpeciesCombinations, selectedSpeciesCombination]);

  // Auto-select first available grade when species combination changes
  useEffect(() => {
    if (availableGrades.length > 0 && !selectedGrade) {
      const defaultGrade = availableGrades.find(g => g.includes('No. 2')) || availableGrades[0];
      setSelectedGrade(defaultGrade);
    }
  }, [availableGrades, selectedGrade]);

  // Auto-select first available size classification when grade changes
  useEffect(() => {
    if (availableSizeClassifications.length > 0 && !selectedSizeClassification) {
      const defaultSizeClass = availableSizeClassifications.find((sc: string | undefined) => 
        sc && (sc.includes('Beams and Stringers') || sc.includes('Dimension Lumber'))
      ) || availableSizeClassifications[0];
      if (defaultSizeClass) {
        setSelectedSizeClassification(defaultSizeClass);
      }
    }
  }, [availableSizeClassifications, selectedSizeClassification]);

  // Auto-select first available nominal size when size classification changes
  // Or enable manual dimensions if no nominal sizes are available
  useEffect(() => {
    if (availableNominalSizes.length > 0 && !selectedNominalSize && !useManualDimensions) {
      const defaultSize = availableNominalSizes.find((size: string) => size.includes('2 x 8')) || availableNominalSizes[0];
      setSelectedNominalSize(defaultSize);
    } else if (availableNominalSizes.length === 0 && selectedSizeClassification && !useManualDimensions) {
      // No nominal sizes available for this size classification, enable manual dimensions
      setUseManualDimensions(true);
      setManualWidth("3.5");
      setManualDepth("7.25");
    }
  }, [availableNominalSizes, selectedNominalSize, useManualDimensions, selectedSizeClassification]);

  // ==========================================
  // Effects for State Management
  // ==========================================

  // Ref to track last update to prevent infinite loops
  const lastUpdateRef = useRef<string>("");
  const lastBeamPropertiesRef = useRef<string>("");

  /**
   * Memoized update function to prevent unnecessary re-renders
   */
  const updateParentState = useCallback(() => {
    let lumberProperties = null;
    
    if (useManualDimensions) {
      // Use manual dimensions
      const manualWidthNum = parseFloat(manualWidth);
      const manualDepthNum = parseFloat(manualDepth);
      
      if (!isNaN(manualWidthNum) && manualWidthNum > 0 && !isNaN(manualDepthNum) && manualDepthNum > 0) {
        lumberProperties = {
          size_classification: selectedSizeClassification,
          nominal_size_bxd: `${manualWidthNum}" x ${manualDepthNum}"`,
          nominal_b: manualWidthNum,
          nominal_d: manualDepthNum,
          standard_dressed_size_bxd: `${manualWidthNum}${'"'} x ${manualDepthNum}${'"'}`,
          standard_width: manualWidthNum,
          standard_depth: manualDepthNum,
          area_of_section_a_in2: manualWidthNum * manualDepthNum,
          Sxx: (manualWidthNum * Math.pow(manualDepthNum, 3)) / 12 / (manualDepthNum / 2),
          Ixx: (manualWidthNum * Math.pow(manualDepthNum, 3)) / 12,
          Syy: (manualDepthNum * Math.pow(manualWidthNum, 3)) / 12 / (manualWidthNum / 2),
          Iyy: (manualDepthNum * Math.pow(manualWidthNum, 3)) / 12,
          weight_25_lbspft3: 0,
          weight_30_lbspft3: 0,
          weight_35_lbspft3: 0,
          weight_40_lbspft3: 0,
          weight_45_lbspft3: 0,
          weight_50_lbspft3: 0,
          version: selectedNdsVersion,
        };
      }
    } else if (currentLumberProperties) {
      // Use nominal size properties from API
      lumberProperties = currentLumberProperties;
    }

    // Create a signature of the current state to detect changes
    const stateSignature = JSON.stringify({
      selectedNdsVersion,
      selectedSpecies,
      selectedSpeciesCombination,
      selectedGrade,
      selectedSizeClassification,
      selectedNominalSize,
      useManualDimensions,
      manualWidth,
      manualDepth,
      designValues: currentDesignValues ? {
        speciesCombination: currentDesignValues.speciesCombination,
        commercial_grade: currentDesignValues.commercial_grade,
        size_classification: currentDesignValues.size_classification
      } : null,
      lumberProperties
    });

    // Only update if state has actually changed
    if (stateSignature !== lastUpdateRef.current) {
      lastUpdateRef.current = stateSignature;
      
      const updatedState: BeamPropertiesState = {
        ...beamPropertiesState,
        selectedNdsVersion,
        selectedSpecies,
        selectedSpeciesCombination,
        selectedGrade,
        selectedSizeClassification,
        selectedNominalSize,
        manualWidth,
        manualDepth,
        designValues: currentDesignValues,
        lumberProperties,
        ...(useManualDimensions && { useManualDimensions }),
      } as BeamPropertiesState;

      onBeamPropertiesStateChange(updatedState);
    }
  }, [
    selectedNdsVersion,
    selectedSpecies,
    selectedSpeciesCombination,
    selectedGrade,
    selectedSizeClassification,
    selectedNominalSize,
    useManualDimensions,
    manualWidth,
    manualDepth,
    currentDesignValues,
    currentLumberProperties,
    beamPropertiesState,
    onBeamPropertiesStateChange,
  ]);

  /**
   * Update parent state when selections change
   */
  useEffect(() => {
    updateParentState();
  }, [updateParentState]);

  /**
   * Update beam properties when design values and lumber properties change
   */
  useEffect(() => {
    // Early return if design values are not available
    if (!currentDesignValues) {
      return;
    }

    // Check if we have valid lumber properties or manual dimensions
    const hasValidLumberProperties = currentLumberProperties && 
      currentLumberProperties.area_of_section_a_in2 > 0 && 
      currentLumberProperties.Ixx > 0;
    
    const hasValidManualDimensions = useManualDimensions && 
      manualWidth && 
      manualDepth && 
      !sizeError &&
      parseFloat(manualWidth) > 0 && 
      parseFloat(manualDepth) > 0;

    if (!hasValidLumberProperties && !hasValidManualDimensions) {
      return;
    }

    const elasticModulus = currentDesignValues.E;
    let momentOfInertia = 0;
    let area = 0;

    if (useManualDimensions && hasValidManualDimensions) {
      // Use manual dimensions
      const manualWidthNum = parseFloat(manualWidth);
      const manualDepthNum = parseFloat(manualDepth);
      
      area = manualWidthNum * manualDepthNum;
      momentOfInertia = (manualWidthNum * Math.pow(manualDepthNum, 3)) / 12;
    } else if (hasValidLumberProperties) {
      // Use nominal size properties from API
      area = currentLumberProperties!.area_of_section_a_in2;
      momentOfInertia = currentLumberProperties!.Ixx;
    }

    // Validate all properties are positive
    if (elasticModulus > 0 && momentOfInertia > 0 && area > 0) {
      // Create a signature of the beam properties to detect changes
      const beamPropertiesSignature = JSON.stringify({
        length: properties.length,
        elasticModulus,
        momentOfInertia,
        area,
      });

      // Only call onChange if the beam properties have actually changed
      if (beamPropertiesSignature !== lastBeamPropertiesRef.current) {
        lastBeamPropertiesRef.current = beamPropertiesSignature;
        
        onChange({
          length: properties.length,
          elasticModulus,
          momentOfInertia,
          area,
        });
      }
    }
  }, [currentDesignValues, currentLumberProperties, useManualDimensions, manualWidth, manualDepth, sizeError, properties.length, onChange]);

  // ==========================================
  // Validation
  // ==========================================

  /**
   * Validates manual dimensions
   */
  const validateManualDimensions = (width: string, depth: string): string => {
    const widthNum = parseFloat(width);
    const depthNum = parseFloat(depth);

    if (isNaN(widthNum) || widthNum <= 0) {
      return VALIDATION_MESSAGES.MANUAL_WIDTH_INVALID;
    }

    if (isNaN(depthNum) || depthNum <= 0) {
      return VALIDATION_MESSAGES.MANUAL_DEPTH_INVALID;
    }

    return "";
  };

  // ==========================================
  // Event Handlers
  // ==========================================

  /**
   * Handles NDS version selection change
   */
  const handleNdsVersionChange = (version: string) => {
    setSelectedNdsVersion(version);
    // Reset dependent selections when version changes
    setSelectedSpecies("");
    setSelectedSpeciesCombination("");
    setSelectedGrade("");
    setSelectedSizeClassification("");
    setSelectedNominalSize("");
  };

  /**
   * Handles species selection change
   */
  const handleSpeciesChange = (species: string) => {
    setSelectedSpecies(species);
    // Reset dependent selections
    setSelectedSpeciesCombination("");
    setSelectedGrade("");
    setSelectedSizeClassification("");
    setSelectedNominalSize("");
  };

  /**
   * Handles species combination selection change
   */
  const handleSpeciesCombinationChange = (combination: string) => {
    setSelectedSpeciesCombination(combination);
    // Reset dependent selections
    setSelectedGrade("");
    setSelectedSizeClassification("");
    setSelectedNominalSize("");
  };

  /**
   * Handles grade selection change
   */
  const handleGradeChange = (grade: string) => {
    setSelectedGrade(grade);
    // Reset dependent selections
    setSelectedSizeClassification("");
    setSelectedNominalSize("");
  };

  /**
   * Handles size classification selection change
   */
  const handleSizeClassificationChange = (sizeClassification: string) => {
    setSelectedSizeClassification(sizeClassification);
    setSelectedNominalSize("");
  };

  /**
   * Handles nominal size selection change
   */
  const handleNominalSizeChange = (nominalSize: string) => {
    setSelectedNominalSize(nominalSize);
  };

  /**
   * Handles toggle between nominal and manual dimensions
   */
  const handleDimensionModeToggle = (useManual: boolean) => {
    setUseManualDimensions(useManual);
    if (useManual) {
      // Set default manual dimensions from current lumber properties
      if (currentLumberProperties) {
        setManualWidth(currentLumberProperties.standard_width.toString());
        setManualDepth(currentLumberProperties.standard_depth.toString());
      } else {
        setManualWidth("3.5");
        setManualDepth("7.25");
      }
    } else {
      // Clear manual dimensions
      setManualWidth("");
      setManualDepth("");
      setSizeError("");
    }
  };

  /**
   * Handles design values selection from NDS table modal
   */
  const handleDesignValuesSelect = (values: DesignValues) => {
    setSelectedSpeciesCombination(values.speciesCombination);
    setSelectedGrade(values.commercial_grade);
    setSelectedSizeClassification(values.size_classification || "");
  };

  /**
   * Handles manual width input change
   */
  const handleManualWidthChange = (value: string) => {
    setManualWidth(value);
    const error = validateManualDimensions(value, manualDepth);
    setSizeError(error);
  };

  /**
   * Handles manual depth input change
   */
  const handleManualDepthChange = (value: string) => {
    setManualDepth(value);
    const error = validateManualDimensions(manualWidth, value);
    setSizeError(error);
  };

  // ==========================================
  // Render
  // ==========================================

  if (isWoodLoading || isSawnLumberLoading || isVersionsLoading || isLumberPropertiesLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (woodError || sawnLumberError || versionsError || lumberPropertiesError) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Error loading lumber data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          {MATERIAL_LABELS.LUMBER_TYPE}: {LUMBER_TYPES.SAWN}
        </h3>
        <p className="text-sm text-muted-foreground">
          Select material properties according to NDS specifications
        </p>
      </div>

      {/* NDS Version Selection */}
      <div className="space-y-2">
        <Label htmlFor="nds-version">NDS Version</Label>
        <Select value={selectedNdsVersion} onValueChange={handleNdsVersionChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select NDS version" />
          </SelectTrigger>
          <SelectContent>
            {availableVersions?.map((version: string) => (
              <SelectItem key={version} value={version}>
                {version}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Species Selection */}
      <div className="space-y-2">
        <Label htmlFor="species">Species</Label>
        <Select value={selectedSpecies} onValueChange={handleSpeciesChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select species" />
          </SelectTrigger>
          <SelectContent>
            {availableSpecies.map((species: string) => (
              <SelectItem key={species} value={species}>
                {species}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Species Combination Selection */}
      {availableSpeciesCombinations.length > 0 && (
        <div className="space-y-2">
          <Label htmlFor="species-combination">Species Combination</Label>
          <Select value={selectedSpeciesCombination} onValueChange={handleSpeciesCombinationChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select species combination" />
            </SelectTrigger>
            <SelectContent>
              {availableSpeciesCombinations.map((combination: string) => (
                <SelectItem key={combination} value={combination}>
                  {combination}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Grade Selection */}
      {availableGrades.length > 0 && (
        <div className="space-y-2">
          <Label htmlFor="grade">Grade</Label>
          <Select value={selectedGrade} onValueChange={handleGradeChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select grade" />
            </SelectTrigger>
            <SelectContent>
              {availableGrades.map((grade: string) => (
                <SelectItem key={grade} value={grade}>
                  {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Size Classification Selection */}
      {availableSizeClassifications.length > 0 && (
        <div className="space-y-2">
          <Label htmlFor="size-classification">Size Classification</Label>
          <Select value={selectedSizeClassification} onValueChange={handleSizeClassificationChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select size classification" />
            </SelectTrigger>
            <SelectContent>
              {availableSizeClassifications
                .filter((classification): classification is string => 
                  classification !== undefined && classification !== null
                )
                .map((classification) => (
                  <SelectItem key={classification} value={classification}>
                    {classification}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Dimension Selection Mode Toggle */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="dimension-mode"
            checked={useManualDimensions}
            onCheckedChange={handleDimensionModeToggle}
            disabled={availableNominalSizes.length === 0 && !!selectedSizeClassification}
          />
          <Label htmlFor="dimension-mode">
            Use Manual Dimensions (Override Nominal Sizes)
          </Label>
        </div>
        
        {availableNominalSizes.length === 0 && selectedSizeClassification && (
          <p className="text-sm text-muted-foreground">
            No standard nominal sizes available for &quot;{selectedSizeClassification}&quot;. Manual dimensions are required.
          </p>
        )}

        {!useManualDimensions ? (
          /* Nominal Size Selection */
          availableNominalSizes.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="nominal-size">Nominal Size</Label>
              <Select value={selectedNominalSize} onValueChange={handleNominalSizeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select nominal size" />
                </SelectTrigger>
                <SelectContent>
                  {availableNominalSizes.map((size: string) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {currentLumberProperties && (
                <p className="text-sm text-muted-foreground">
                  Actual dimensions: {currentLumberProperties.standard_dressed_size_bxd}
                </p>
              )}
            </div>
          )
        ) : (
          /* Manual Dimensions Override */
          <div className="space-y-4">
            <h4 className="text-md font-medium">Manual Dimensions</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="manual-width">Width ({units.length})</Label>
                <Input
                  id="manual-width"
                  type="number"
                  step="0.125"
                  min="0"
                  value={manualWidth}
                  onChange={(e) => handleManualWidthChange(e.target.value)}
                  placeholder="Enter width"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="manual-depth">Depth ({units.length})</Label>
                <Input
                  id="manual-depth"
                  type="number"
                  step="0.125"
                  min="0"
                  value={manualDepth}
                  onChange={(e) => handleManualDepthChange(e.target.value)}
                  placeholder="Enter depth"
                />
              </div>
            </div>

            {sizeError && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{sizeError}</AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>

      {/* Design Values Display */}
      {filteredDesignValues.length > 0 && (
        <div className="space-y-2">
          <Label>Design Values</Label>
          <NdsTableModal
            designValues={filteredDesignValues}
            onSelect={handleDesignValuesSelect}
            unitSystem={unitSystem}
            selectedVersion={selectedNdsVersion}
          />
        </div>
      )}

      {/* Current Design Values Summary */}
      {currentDesignValues && (
        <div className="space-y-2">
          <h4 className="text-md font-medium">Current Design Values</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Species:</strong> {currentDesignValues.speciesCombination}
            </div>
            <div>
              <strong>Grade:</strong> {currentDesignValues.commercial_grade}
            </div>
            <div>
              <strong>Size Classification:</strong> {currentDesignValues.size_classification || "N/A"}
            </div>
            <div>
              <strong>Fb:</strong> {currentDesignValues.Fb} psi
            </div>
            <div>
              <strong>Ft:</strong> {currentDesignValues.Ft} psi
            </div>
            <div>
              <strong>Fv:</strong> {currentDesignValues.Fv} psi
            </div>
            <div>
              <strong>Fc:</strong> {currentDesignValues.Fc} psi
            </div>
            <div>
              <strong>E:</strong> {(currentDesignValues.E / 1000000).toFixed(2)} × 10⁶ psi
            </div>
          </div>
        </div>
      )}

      {/* Current Lumber Properties Summary */}
      {(currentLumberProperties || (useManualDimensions && manualWidth && manualDepth)) && (
        <div className="space-y-2">
          <h4 className="text-md font-medium">Current Lumber Properties</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {currentLumberProperties && !useManualDimensions ? (
              <>
                <div>
                  <strong>Nominal Size:</strong> {currentLumberProperties.nominal_size_bxd}
                </div>
                <div>
                  <strong>Actual Size:</strong> {currentLumberProperties.standard_dressed_size_bxd}
                </div>
                <div>
                  <strong>Area:</strong> {currentLumberProperties.area_of_section_a_in2.toFixed(2)} in²
                </div>
                <div>
                  <strong>Sxx:</strong> {currentLumberProperties.Sxx.toFixed(2)} in³
                </div>
                <div>
                  <strong>Ixx:</strong> {currentLumberProperties.Ixx.toFixed(2)} in⁴
                </div>
                <div>
                  <strong>Syy:</strong> {currentLumberProperties.Syy.toFixed(2)} in³
                </div>
              </>
            ) : useManualDimensions && manualWidth && manualDepth ? (
              <>
                <div>
                  <strong>Width:</strong> {manualWidth} in
                </div>
                <div>
                  <strong>Depth:</strong> {manualDepth} in
                </div>
                <div>
                  <strong>Area:</strong> {(parseFloat(manualWidth) * parseFloat(manualDepth)).toFixed(2)} in²
                </div>
                <div>
                  <strong>Ixx:</strong> {((parseFloat(manualWidth) * Math.pow(parseFloat(manualDepth), 3)) / 12).toFixed(2)} in⁴
                </div>
              </>
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
} 