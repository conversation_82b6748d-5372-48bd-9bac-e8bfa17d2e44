/**
 * NDS Table Modal Component
 * 
 * This component provides a modal interface for selecting design values from NDS
 * (National Design Specification) tables. It displays a searchable table of wood
 * species combinations with their corresponding design values.
 * 
 * Features:
 * - Searchable table of design values
 * - Version-specific data loading
 * - Row selection and confirmation
 * - Formatted display of engineering values
 */

"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { FileText, Search } from "lucide-react";
import { useEffect, useState } from "react";
import { UnitSystem } from "@/lib/types/units/unit-system";
import type { DesignValues } from "./types";
import type { WoodData } from "@/hooks/use-wood-data";
import { DEFAULT_NDS_VERSION, API_ENDPOINTS } from "@/lib/constants/nds-constants";
import {
  NDS_CONSTANTS,
  MATERIAL_LABELS,
  MATERIALS_UI,
} from "./constants";

/**
 * Props interface for the NdsTableModal component
 */
interface NdsTableModalProps {
  /** Array of design values to display */
  designValues: DesignValues[];
  /** Callback when a design value is selected */
  onSelect: (values: DesignValues) => void;
  /** Unit system for display formatting */
  unitSystem: UnitSystem;
  /** NDS version for table title */
  selectedVersion?: string;
}

/**
 * NDS Table Modal Component
 * 
 * Displays a modal with a searchable table of NDS design values.
 * Users can search for specific species combinations and select
 * design values for use in structural calculations.
 */
export function NdsTableModal({ 
  designValues, 
  onSelect, 
  unitSystem,
  selectedVersion = NDS_CONSTANTS.DEFAULT_VERSION 
}: NdsTableModalProps) {
  // ==========================================
  // State Management
  // ==========================================
  
  const [selectedRow, setSelectedRow] = useState<DesignValues | null>(null);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  /**
   * Reset selection state when modal closes
   */
  useEffect(() => {
    if (!open) {
      setSelectedRow(null);
      setSearchQuery("");
    }
  }, [open]);

  // ==========================================
  // Event Handlers
  // ==========================================
  
  /**
   * Handles row selection in the table
   * @param values - Selected design values
   */
  const handleRowClick = (values: DesignValues) => {
    setSelectedRow(values);
  };

  /**
   * Handles confirmation of selected design values
   */
  const handleConfirm = () => {
    if (selectedRow) {
      onSelect(selectedRow);
      setOpen(false);
    }
  };

  /**
   * Handles search input changes
   * @param query - Search query string
   */
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  // ==========================================
  // Data Processing
  // ==========================================
  
  /**
   * Filters design values based on search query
   */
  const filteredDesignValues = designValues.filter((values) =>
    values.speciesCombination.toLowerCase().includes(searchQuery.toLowerCase()) ||
    values.commercial_grade.toLowerCase().includes(searchQuery.toLowerCase()) ||
    values.size_classification?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  /**
   * Formats dimension range for display
   * @param minValue - Minimum dimension value
   * @param maxValue - Maximum dimension value
   * @returns Formatted dimension range string
   */
  const formatDimensionRange = (minValue: number | null, maxValue: number | null): string => {
    const min = minValue === null || minValue === -Infinity ? "0" : minValue.toString();
    const max = maxValue === null || maxValue === Infinity ? "∞" : maxValue.toString();
    return `${min}″ - ${max}″`;
  };

  /**
   * Formats engineering values for display
   * @param value - Value to format
   * @param precision - Number of decimal places
   * @returns Formatted value string
   */
  const formatValue = (value: number, precision: number = 0): string => {
    return value.toFixed(precision);
  };

  /**
   * Formats modulus values (converts from psi to millions of psi)
   * @param value - Modulus value in psi
   * @returns Formatted value in millions of psi
   */
  const formatModulus = (value: number): string => {
    return (value / 1e6).toFixed(2);
  };

  // ==========================================
  // Render Component
  // ==========================================
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <FileText className="mr-2 h-4 w-4" />
          Select from {selectedVersion} Design Values ({designValues.length} available)
        </Button>
      </DialogTrigger>
      
      <DialogContent className={`${MATERIALS_UI.MODAL_WIDTH} ${MATERIALS_UI.MODAL_HEIGHT}`}>
        <DialogHeader>
          <DialogTitle>
            {selectedVersion} Design Values - {MATERIAL_LABELS.LUMBER_TYPE}
          </DialogTitle>
        </DialogHeader>
        
        {/* Search Input */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search species, grade, or size classification..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Design Values Table */}
        <ScrollArea className="flex-1">
          {filteredDesignValues.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{MATERIAL_LABELS.SPECIES_COMBINATION}</TableHead>
                  <TableHead>{MATERIAL_LABELS.GRADE}</TableHead>
                  <TableHead>Size Range</TableHead>
                  <TableHead>Size Classification</TableHead>
                  <TableHead>Agency</TableHead>
                  <TableHead>Fb (psi)</TableHead>
                  <TableHead>Ft (psi)</TableHead>
                  <TableHead>Fv (psi)</TableHead>
                  <TableHead>Fc⊥ (psi)</TableHead>
                  <TableHead>Fc (psi)</TableHead>
                  <TableHead>E (×10⁶ psi)</TableHead>
                  <TableHead>Emin (×10⁶ psi)</TableHead>
                  <TableHead>G</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDesignValues.map((values, index) => (
                  <TableRow 
                    key={`${values.speciesCombination}-${values.commercial_grade}-${index}`}
                    className={`cursor-pointer hover:bg-muted/50 ${
                      selectedRow === values ? 'bg-muted' : ''
                    }`}
                    onClick={() => handleRowClick(values)}
                  >
                    <TableCell className="font-medium">
                      {values.speciesCombination}
                    </TableCell>
                    <TableCell>{values.commercial_grade}</TableCell>
                    <TableCell>
                      {formatDimensionRange(values.minThickness, values.maxThickness)} × {" "}
                      {formatDimensionRange(values.minWidth, values.maxWidth)}
                    </TableCell>
                    <TableCell>{values.size_classification || "N/A"}</TableCell>
                    <TableCell className="text-xs">
                      {values.grading_rules_agency}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatValue(values.Fb)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatValue(values.Ft)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatValue(values.Fv)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatValue(values.Fc_perp)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatValue(values.Fc)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatModulus(values.E)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatModulus(values.Emin)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatValue(values.G, 3)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Search className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No design values found</h3>
              <p className="text-muted-foreground">
                {searchQuery 
                  ? `No results match "${searchQuery}". Try adjusting your search terms.`
                  : "No design values available for the current selection."
                }
              </p>
            </div>
          )}
        </ScrollArea>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {filteredDesignValues.length} of {designValues.length} entries
            {selectedRow && (
              <span className="ml-2 font-medium">
                • Selected: {selectedRow.speciesCombination} - {selectedRow.commercial_grade}
              </span>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleConfirm}
              disabled={!selectedRow}
            >
              Confirm Selection
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 