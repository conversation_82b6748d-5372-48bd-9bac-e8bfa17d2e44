---
description: 
globs: 
alwaysApply: false
---
Always keep the code modular, small and testable functions.
Always document each function clearly with javadoc and the source where you get the information from.
Always add comments to explain clearly what you are doing
Follow RESTful principles
Include error handling middleware
Set up proper logging
For client code, do not throw errors. Instead log the errors and display errors to the users if the user's input needs to be changed
Props interface at the top
Component as named export
Styles at the bottom
Always define types, enums and constants for reusable code instead of using literal strings or literal numbers
Use camelcase for variable names and uppercase for constants