/**
 * Wet Service Factor (CM) Usage Examples
 * 
 * This example demonstrates how to use the wet service factor calculations
 * for both sawn lumber and glued laminated timber (glulam).
 */

import {
  getWetServiceFactor,
  applyWetServiceFactor,
  getWetServiceFactorAnalysis,
  WET_SERVICE_LUMBER_CATEGORIES,
  WET_SERVICE_SPECIES_GROUPS,
  type WetServiceFactorInput,
} from '../lib/utils/wood/adjustment-factors/wet-service';
import { DESIGN_VALUE_TYPES } from '../lib/utils/wood/constants';

console.log('=== Wet Service Factor (CM) Examples ===\n');

// Example 1: Dimension Lumber in Wet Conditions
console.log('1. Dimension Lumber (2x10 Douglas Fir) in Wet Service Conditions');
const dimensionLumberInput: WetServiceFactorInput = {
  moistureContent: 25, // 25% > 19% threshold
  designValueType: DESIGN_VALUE_TYPES.BENDING,
  lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
};

const dimensionFactor = getWetServiceFactor(dimensionLumberInput);
const adjustedBending = applyWetServiceFactor(1200, dimensionLumberInput);

console.log(`   Moisture Content: ${dimensionLumberInput.moistureContent}%`);
console.log(`   Wet Service Factor (CM): ${dimensionFactor}`);
console.log(`   Reference Fb: 1200 psi`);
console.log(`   Adjusted Fb: ${adjustedBending} psi (${1200} × ${dimensionFactor})\n`);

// Example 2: Glued Laminated Timber (Glulam)
console.log('2. Glued Laminated Timber (Glulam) in Wet Service Conditions');
const glulamInput: WetServiceFactorInput = {
  moistureContent: 18, // 18% ≥ 16% threshold for glulam
  designValueType: DESIGN_VALUE_TYPES.BENDING,
  lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER,
};

const glulamFactor = getWetServiceFactor(glulamInput);
console.log(`   Moisture Content: ${glulamInput.moistureContent}%`);
console.log(`   Wet Service Factor (CM): ${glulamFactor}`);
console.log(`   Reference Fb: 2400 psi`);
console.log(`   Adjusted Fb: ${applyWetServiceFactor(2400, glulamInput)} psi\n`);

// Example 3: Comprehensive Analysis
console.log('3. Comprehensive Wet Service Factor Analysis');
const analysisInput: WetServiceFactorInput = {
  moistureContent: 22,
  designValueType: DESIGN_VALUE_TYPES.BENDING,
  lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
  referenceDesignValue: 1000,
  sizeFactor: 1.15, // This will trigger special condition
};

const analysis = getWetServiceFactorAnalysis(analysisInput);
console.log(`   Input: ${analysis.input.moistureContent}% moisture, ${analysis.input.designValueType}`);
console.log(`   Wet Service Required: ${analysis.isWetServiceRequired}`);
console.log(`   Wet Service Factor: ${analysis.wetServiceFactor}`);
console.log(`   Factor Source: ${analysis.lumberFactorSource}`);
if (analysis.specialConditionApplied) {
  console.log(`   Special Condition: ${analysis.specialConditionApplied}`);
}

console.log('\n=== End of Wet Service Factor Examples ==='); 