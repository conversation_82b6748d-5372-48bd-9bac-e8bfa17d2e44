/**
 * NDS Chapter 15 Structural Design Examples
 * 
 * This example demonstrates the usage of the newly implemented NDS Chapter 15 provisions:
 * - 15.1 Lateral Distribution of a Concentrated Load
 * - 15.2 Spaced Columns
 * - 15.3 Built-Up Columns
 * 
 * These examples show real-world applications and provide guidance for structural engineers.
 */

// Import the new modules as namespaces
import * as LateralDistribution from '../lib/utils/wood/lateral-distribution';
import * as SpacedColumns from '../lib/utils/wood/spaced-columns';
import * as BuiltUpColumns from '../lib/utils/wood/built-up-columns';

/**
 * Example 1: Lateral Distribution Analysis for Bridge Design
 * 
 * A timber bridge with nail-laminated decking needs to distribute a concentrated
 * load from a vehicle to adjacent parallel beams.
 */
export function example1_BridgeLateralDistribution() {
  console.log('\n=== Example 1: Bridge Lateral Load Distribution ===');

  const bridgeLoadInput: LateralDistribution.LateralDistributionInput = {
    floorType: LateralDistribution.FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH,
    beamSpacing: 8, // 8 feet on center
    totalLoad: 16000, // 16 kips vehicle load
    loadPosition: LateralDistribution.LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
    centerBeamProportion: 0.80, // 80% of load to center beam
  };

  // Perform complete analysis for both moment and shear
  const analysis = LateralDistribution.performLateralDistributionAnalysis(bridgeLoadInput);

  console.log('Load Distribution Results:');
  console.log(`- Floor Construction: ${analysis.recommendations.floorConstruction}`);
  console.log(`- Critical Design Case: ${analysis.recommendations.criticalDesignCase}`);
  
  console.log('\nMoment Distribution:');
  console.log(`- Center Beam Load: ${analysis.momentDistribution.centerBeamLoad.toLocaleString()} lbs`);
  console.log(`- Each Side Beam Load: ${analysis.momentDistribution.sideBeamLoad.toLocaleString()} lbs`);
  console.log(`- Distribution Factor: ${analysis.momentDistribution.distributionFactor}`);

  console.log('\nShear Distribution:');
  console.log(`- Center Beam Load: ${analysis.shearDistribution.centerBeamLoad.toLocaleString()} lbs`);
  console.log(`- Each Side Beam Load: ${analysis.shearDistribution.sideBeamLoad.toLocaleString()} lbs`);

  return analysis;
}

/**
 * Example 2: Spaced Column Design for Agricultural Building
 * 
 * Design a spaced column for a post-frame agricultural building using
 * Southern Pine lumber with mechanical fasteners.
 */
export function example2_AgriculturalSpacedColumn() {
  console.log('\n=== Example 2: Agricultural Spaced Column Design ===');

  const spacedColumnInput: SpacedColumns.SpacedColumnInput = {
    speciesGroup: SpacedColumns.SPACED_COLUMN_SPECIES_GROUPS.B, // Southern Pine
    lateralSupportDistance: 168, // 14 feet between girts
    crossSectionalDimension: 5.5, // 2x6 actual dimension
    spacerBlockDistance: 2.0, // 2" spacer blocks
    columnLength: 168, // 14 feet tall
    endFixityCondition: SpacedColumns.END_FIXITY_CONDITIONS.CONDITION_A, // Good end fixity
    fcStar: 1500, // psi compression parallel to grain
    eMin: 1300000, // psi minimum modulus of elasticity
    materialType: 'sawn_lumber',
  };

  // Calculate spaced column capacity
  const result = SpacedColumns.calculateSpacedColumn(spacedColumnInput);

  // Check compliance with NDS provisions
  const compliance = SpacedColumns.checkSpacedColumnCompliance(spacedColumnInput);

  console.log('Spaced Column Analysis Results:');
  console.log(`- Column Stability Factor (Cp): ${result.columnStabilityFactor.toFixed(3)}`);
  console.log(`- Adjusted Compression Value: ${result.adjustedCompressionValue.toFixed(0)} psi`);
  console.log(`- End Spacer Block Constant (Ks): ${result.endSpacerBlockConstant.toFixed(0)}`);
  console.log(`- Slenderness Ratio: ${result.analysis.slendernessRatio.toFixed(1)}`);

  console.log('\nCompliance Check:');
  console.log(`- Code Compliant: ${compliance.compliant ? 'Yes' : 'No'}`);
  if (compliance.violations.length > 0) {
    console.log('- Violations:');
    compliance.violations.forEach((v: string) => console.log(`  • ${v}`));
  }
  if (compliance.recommendations.length > 0) {
    console.log('- Recommendations:');
    compliance.recommendations.forEach((r: string) => console.log(`  • ${r}`));
  }

  return { result, compliance };
}

/**
 * Example 3: Built-Up Column Design for Heavy Timber Construction
 * 
 * Design a built-up column for a heavy timber industrial building using
 * nailed laminations with optimized configuration.
 */
export function example3_IndustrialBuiltUpColumn() {
  console.log('\n=== Example 3: Industrial Built-Up Column Design ===');

  const builtUpColumnInput: BuiltUpColumns.BuiltUpColumnInput = {
    numberOfLaminations: 4,
    laminationThickness: 1.5, // 2x lumber actual
    laminationDepth: 7.25, // 2x8 actual depth
    columnLength: 192, // 16 feet tall
    connectionType: BuiltUpColumns.BUILT_UP_CONNECTION_TYPES.NAILED,
    fcStar: 1350, // psi Douglas Fir-Larch
    eMin: 1200000, // psi minimum modulus
    materialType: 'sawn_lumber',
    nailSpecs: {
      diameter: 0.148, // 12d common nail (0.148")
      spacing: 8, // 8" on center
      penetration: 2.5, // 2.5" penetration
      numberOfRows: 2, // Two longitudinal rows
    },
  };

  // Analyze the built-up column
  const result = BuiltUpColumns.calculateBuiltUpColumn(builtUpColumnInput);

  // Get design recommendations and alternatives
  const recommendations = BuiltUpColumns.getBuiltUpColumnDesignRecommendations(builtUpColumnInput);

  console.log('Built-Up Column Analysis Results:');
  console.log(`- Column Stability Factor (Cp): ${result.columnStabilityFactor.toFixed(3)}`);
  console.log(`- Adjusted Compression Value: ${result.adjustedCompressionValue.toFixed(0)} psi`);
  console.log(`- Allowable Axial Load: ${result.allowableAxialLoad.toLocaleString()} lbs`);
  console.log(`- Total Cross-Sectional Area: ${result.calculation.totalArea.toFixed(1)} sq in`);
  console.log(`- Governing Slenderness Ratio: ${result.analysis.governingSlendernessRatio.toFixed(1)}`);

  console.log('\nConnection Compliance:');
  console.log(`- Nailed Connection Compliant: ${result.connectionCompliance.compliant ? 'Yes' : 'No'}`);
  if (result.connectionCompliance.violations.length > 0) {
    console.log('- Connection Violations:');
    result.connectionCompliance.violations.forEach(v => console.log(`  • ${v}`));
  }

  console.log('\nDesign Recommendations:');
  recommendations.recommendations.forEach((r: string) => console.log(`- ${r}`));

  if (recommendations.alternatives.length > 0) {
    console.log('\nDesign Alternatives:');
    recommendations.alternatives.forEach((alt: any) => {
      console.log(`- ${alt.description}: ${alt.improvement}`);
    });
  }

  return { result, recommendations };
}

/**
 * Example 4: Comparative Analysis - Spaced vs Built-Up Columns
 * 
 * Compare the efficiency of spaced columns versus built-up columns
 * for the same loading and geometric constraints.
 */
export function example4_ComparativeColumnAnalysis() {
  console.log('\n=== Example 4: Spaced vs Built-Up Column Comparison ===');

  // Common parameters
  const columnHeight = 144; // 12 feet
  const targetCapacity = 15000; // 15 kips
  const materialFc = 1400; // psi
  const materialE = 1400000; // psi

  // Spaced column configuration
  const spacedConfig: SpacedColumns.SpacedColumnInput = {
    speciesGroup: SpacedColumns.SPACED_COLUMN_SPECIES_GROUPS.B,
    lateralSupportDistance: columnHeight,
    crossSectionalDimension: 5.5, // 2x6
    spacerBlockDistance: 2.0,
    columnLength: columnHeight,
    endFixityCondition: SpacedColumns.END_FIXITY_CONDITIONS.CONDITION_A,
    fcStar: materialFc,
    eMin: materialE,
    materialType: 'sawn_lumber',
  };

  // Built-up column configuration (similar total area)
  const builtUpConfig: BuiltUpColumns.BuiltUpColumnInput = {
    numberOfLaminations: 3,
    laminationThickness: 1.5, // 2x
    laminationDepth: 5.5, // 2x6
    columnLength: columnHeight,
    connectionType: BuiltUpColumns.BUILT_UP_CONNECTION_TYPES.BOLTED,
    fcStar: materialFc,
    eMin: materialE,
    materialType: 'sawn_lumber',
  };

  const spacedResult = SpacedColumns.calculateSpacedColumn(spacedConfig);
  const builtUpResult = BuiltUpColumns.calculateBuiltUpColumn(builtUpConfig);

  // Calculate equivalent axial capacity for spaced column (assuming 2 members)
  const spacedEquivalentArea = 2 * spacedConfig.crossSectionalDimension * 1.5; // Assume 2x6 members
  const spacedEquivalentCapacity = spacedResult.adjustedCompressionValue * spacedEquivalentArea;

  console.log('Column Comparison Results:');
  console.log(`
Spaced Column:
- Stability Factor: ${spacedResult.columnStabilityFactor.toFixed(3)}
- Adjusted Fc': ${spacedResult.adjustedCompressionValue.toFixed(0)} psi
- Equivalent Capacity: ${spacedEquivalentCapacity.toLocaleString()} lbs

Built-Up Column:
- Stability Factor: ${builtUpResult.columnStabilityFactor.toFixed(3)}
- Adjusted Fc': ${builtUpResult.adjustedCompressionValue.toFixed(0)} psi
- Allowable Load: ${builtUpResult.allowableAxialLoad.toLocaleString()} lbs

Efficiency Comparison:
- Built-up is ${builtUpResult.allowableAxialLoad > spacedEquivalentCapacity ? 'more' : 'less'} efficient
- Capacity difference: ${Math.abs(builtUpResult.allowableAxialLoad - spacedEquivalentCapacity).toLocaleString()} lbs
  `);

  return {
    spaced: { config: spacedConfig, result: spacedResult, equivalentCapacity: spacedEquivalentCapacity },
    builtUp: { config: builtUpConfig, result: builtUpResult },
  };
}

/**
 * Run all examples
 */
export function runAllNDS15Examples() {
  console.log('🌲 NDS Chapter 15 Structural Design Examples 🌲');
  console.log('================================================');

  try {
    const example1 = example1_BridgeLateralDistribution();
    const example2 = example2_AgriculturalSpacedColumn();
    const example3 = example3_IndustrialBuiltUpColumn();
    const example4 = example4_ComparativeColumnAnalysis();

    console.log('\n✅ All examples completed successfully!');
    console.log('\nKey Takeaways:');
    console.log('- Lateral distribution depends heavily on floor construction type');
    console.log('- Spaced columns are effective for agricultural and light commercial applications');
    console.log('- Built-up columns provide flexibility in achieving desired capacity');
    console.log('- Connection details significantly affect column performance');

    return {
      lateralDistribution: example1,
      spacedColumn: example2,
      builtUpColumn: example3,
      comparison: example4,
    };

  } catch (error) {
    console.error('❌ Error running examples:', error);
    throw error;
  }
}

// Uncomment to run examples
// runAllNDS15Examples(); 