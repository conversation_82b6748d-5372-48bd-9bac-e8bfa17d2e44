import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getWoodSpecies(): string[] {
  // Return a default list of wood species
  // This is a simplified version - you may want to load this from your CSV file
  return [
    "Douglas Fir-Larch",
    "Hem-Fir",
    "Spruce-Pine-Fir",
    "Southern Pine",
    "Mixed Southern Pine",
    "Mixed Softwoods",
    "Northern Species",
    "Western Woods",
    "Eastern Softwoods",
    "Eastern White Pine",
    "Red Pine"
  ];
}