import fs from 'fs';
import path from 'path';

export function getWoodSpecies(): string[] {
  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'DesignValues.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n');
    
    // Skip header row and get unique species
    const species = new Set(
      lines
        .slice(1) // Skip header row
        .map(line => line.split(',')[0]) // Get species column
        .filter(Boolean) // Remove empty lines
    );
    
    return Array.from(species).sort();
  } catch (error) {
    console.error('Error reading species from CSV:', error);
    return [];
  }
}