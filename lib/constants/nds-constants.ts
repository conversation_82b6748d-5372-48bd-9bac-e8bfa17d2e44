/**
 * Constants for NDS (National Design Specification) related functionality
 */

// NDS Versions
export const NDS_VERSIONS = {
  NDS_2018: 'NDS 2018',
  NDS_2015: 'NDS 2015', // For future use
  NDS_2012: 'NDS 2012', // For future use
} as const;

// Default values
export const DEFAULT_NDS_VERSION = NDS_VERSIONS.NDS_2018;

// API endpoints
export const API_ENDPOINTS = {
  WOOD_DATA: '/api/wood-data',
  LUMBER_PROPERTIES: '/api/lumber-properties',
  SAWN_LUMBER_SPECIES: '/api/sawn-lumber-species',
  SIZE_FACTORS: '/api/size-factors',
} as const;

// Query keys for React Query
export const QUERY_KEYS = {
  WOOD_DATA: 'wood-data',
  WOOD_DATA_VERSIONS: 'wood-data-versions',
  LUMBER_PROPERTIES: 'lumber-properties',
  SAWN_LUMBER_SPECIES: 'sawn-lumber-species',
  SIZE_FACTORS: 'size-factors',
} as const;

// Default species selections
export const DEFAULT_WOOD_SELECTIONS = {
  SPECIES: 'DOUGLAS FIR',
  SPECIES_COMBINATION: 'DOUGLAS FIR-LARCH',
  GRADE: 'No. 2',
  SIZE_CLASSIFICATION: '2" & wider',
} as const;

// Cache duration
export const CACHE_DURATION_MS = {
  WOOD_DATA: 60 * 60 * 1000, // 1 hour
} as const;

// Error messages
export const ERROR_MESSAGES = {
  WOOD_DATA_NOT_FOUND: 'Design values data file not found',
  WOOD_DATA_PARSE_FAILED: 'Failed to parse wood data',
  WOOD_DATA_LOAD_FAILED: 'Failed to load wood data',
  WOOD_DATA_PROCESS_FAILED: 'Failed to process wood data',
  VERSIONS_LOAD_FAILED: 'Failed to fetch wood data',
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// CSV column positions (0-based)
export const CSV_COLUMNS = {
  SPECIES_COMBINATION: 0,
  COMMERCIAL_GRADE: 1,
  SIZE_CLASSIFICATION: 2,
  MIN_WIDTH: 3,
  MAX_WIDTH: 4,
  MIN_THICK: 5,
  MAX_THICK: 6,
  VERSION: 19, // Based on the CSV structure we examined
} as const; 