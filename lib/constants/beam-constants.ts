export const ANALYSIS_TABS = {
  SUMMARY: "summary",
  DIAGRAMS: "diagrams",
  PER_SPAN: "per-span-analysis"
} as const;

export const TAB_LABELS = {
  [ANALYSIS_TABS.SUMMARY]: "Summary",
  [ANALYSIS_TABS.DIAGRAMS]: "Diagrams",
  [ANALYSIS_TABS.PER_SPAN]: "Per-Span Analysis"
} as const;

export const DEFAULT_BEAM_LENGTH = 10; // Base length in ft
export const DEFAULT_ELASTIC_MODULUS = 1000000; // Base elastic modulus in psi
export const DEFAULT_ALLOWED_ELASTIC_MODULUS = 580000; // Example placeholder
export const DEFAULT_MOMENT_OF_INERTIA = 100; // Base moment of inertia in in⁴
export const DEFAULT_AREA = 20; // Base area in in²

// Default Placeholders for Manual Allowable Stress Inputs (Imperial)
// TODO: Consider making these unit-system dependent if needed
export const DEFAULT_FB_ALLOW_PLACEHOLDER = 24000; // psi
export const DEFAULT_FV_ALLOW_PLACEHOLDER = 150;   // psi

// Default Deflection Limit Denominators
export const DEFAULT_TOTAL_DEFLECTION_LIMIT = 240; // L/240
export const DEFAULT_LIVE_DEFLECTION_LIMIT = 360;  // L/360

// Default Placeholders for Manual Width/Depth (Imperial)
export const DEFAULT_MANUAL_WIDTH_PLACEHOLDER = "2"; // inches (string)
export const DEFAULT_MANUAL_DEPTH_PLACEHOLDER = "4"; // inches (string)

// Wood Density Constants
export const WOOD_DENSITY_IMPERIAL = 35; // Approximate density in lb/ft^3
export const WOOD_DENSITY_METRIC = 560; // Approximate density in kg/m^3

// Import DesignValues for DEFAULT_BASE_DESIGN_VALUES
import type { DesignValues } from "@/components/materials"; // Updated to use new materials component

// Fallback/Default values for Sawn Lumber calculations when specifics are not available
export const DEFAULT_NOMINAL_B = 1.5; // e.g., for a 2x lumber (inches)
export const DEFAULT_NOMINAL_D = 3.5; // e.g., for a 2x4 (inches)
export const DEFAULT_BASE_DESIGN_VALUES: DesignValues = {
  Fb: DEFAULT_FB_ALLOW_PLACEHOLDER, // Placeholder psi
  original_Fb: DEFAULT_FB_ALLOW_PLACEHOLDER,
  adjusted_Fb: DEFAULT_FB_ALLOW_PLACEHOLDER,
  Fb_pos: DEFAULT_FB_ALLOW_PLACEHOLDER,
  original_Fb_pos: DEFAULT_FB_ALLOW_PLACEHOLDER,
  adjusted_Fb_pos: DEFAULT_FB_ALLOW_PLACEHOLDER,
  Fb_neg: DEFAULT_FB_ALLOW_PLACEHOLDER,
  original_Fb_neg: DEFAULT_FB_ALLOW_PLACEHOLDER,
  adjusted_Fb_neg: DEFAULT_FB_ALLOW_PLACEHOLDER,
  E: DEFAULT_ELASTIC_MODULUS, // Placeholder psi
  Emin: DEFAULT_ALLOWED_ELASTIC_MODULUS, // Placeholder psi
  Ft: 0, 
  Fv: DEFAULT_FV_ALLOW_PLACEHOLDER,
  Fc_perp: 0,
  Fc: 0,
  G: 0.5, 
  commercial_grade: "Fallback Grade", 
  speciesCombination: "Fallback Species",
  grading_rules_agency: "N/A",
  design_values_table: "FallbackTable", 
  location: "N/A",
  version: "N/A",
  minThickness: 0,
  maxThickness: Infinity,
  minWidth: 0,
  maxWidth: Infinity,
  // Ensure all required fields from DesignValues are present
};

// Default adjustment factors (all 1.0)
import type { AdjustmentFactorSet } from "@/lib/types/beam/beam-data"; // Import the type
export const DEFAULT_ADJUSTMENT_FACTORS: AdjustmentFactorSet = {
  Fb: { factor: 1.0, note: "Default" },
  Ft: { factor: 1.0, note: "Default" },
  Fv: { factor: 1.0, note: "Default" },
  Fc_perp: { factor: 1.0, note: "Default" },
  Fc: { factor: 1.0, note: "Default" },
  E: { factor: 1.0, note: "Default" },
  Emin: { factor: 1.0, note: "Default" },
};

export const DEFAULT_TEMPERATURE = 70; // Default temperature in Fahrenheit