import { <PERSON>amD<PERSON>, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import {
  AnalysisOutput,
  DiagramData,
  SummaryData,
  StressRatioResult,
  DeflectionResult,
  SteelDesignAnalysis,
} from "@/lib/types/analysis/analysis-output";
import { DataPoint } from "@/lib/types/analysis/analysis-results";
import { LoadType } from "@/lib/types/load/load-type";
import { Type as LoadApplicationType } from "@/lib/types/load/load-type";
import { SupportType } from "@/lib/types/support/support-type";
import {
  LinearStaticSolver,
  DofID,
  Domain,
  Beam2D,
  LoadCase,
} from "@/lib/beam-2d-fem";
import { ASCE_7_10_ASD_ComboMap } from "@/lib/types/load/asce-load-combo";
import { ASCE_7_10_LRFD_ComboMap } from "@/lib/types/load/asce-lrfd-combo";
import { Support } from "@/lib/types/support/support";
import { Load } from "@/lib/types/load/load";
import { LoadGroup } from "@/lib/types/load/load-group";
import {
  DEFAULT_TOTAL_DEFLECTION_LIMIT,
  DEFAULT_LIVE_DEFLECTION_LIMIT,
} from "@/lib/constants/beam-constants";
import {
  ftToIn,
  inToFt,
  kipToLb,
  lbToKip,
  ksiToPsi,
  psiToKsi,
  kipFtToLbIn,
  lbInToKipFt,
  kipPerFtToLbPerIn,
  lbPerInToKipPerFt,
  mToIn,
  inToM,
  nToLb,
  lbToN,
  paToPsi,
  psiToPa,
  m4ToIn4,
  in4ToM4,
  nPerMToLbPerIn,
  lbPerInToNPerM,
  nmToLbIn,
  lbInToNm,
  lbfPerFtToLbfPerIn,
} from "@/lib/utils/metric-converter";
import { Node } from "@/lib/beam-2d-fem";
import { 
  getDesignCheckResults, 
  BeamAnalysisInputs,
  DeflectionParameters,
} from "@/lib/utils/steel/steel-beam-orchestrator";
import { Section } from "@/lib/utils/steel/slenderness/types";
import { Material, DesignMethod } from "@/lib/utils/steel/constants";
import { DEFAULT_DEFLECTION_LIMITS } from "@/lib/utils/steel/deflection/index";

// --- Constants ---
const NUM_POINTS_PER_ELEMENT = 100;
const TOLERANCE = 1e-9; // Tolerance for floating point comparisons

// --- Local Type Definitions ---
interface SingleCombinationResult {
  diagramData: DiagramData;
  reactions: {
    forces: { [nodeLabel: string]: number };
    moments: { [nodeLabel: string]: number };
  } | null;
}

interface SingleLoadTypeResult {
  forces: { [nodeLabel: string]: number };
  moments: { [nodeLabel: string]: number };
}

interface FemModelDefinition {
  nodes: { label: string; coords: number[] }[];
  elements: {
    label: string;
    nodes: string[];
    materialLabel: string;
    csLabel: string;
  }[];
  boundaryConditions: { nodeLabel: string; bcs: DofID[] }[];
  material: { label: string; properties: { e: number } };
  crossSection: {
    label: string;
    properties: {
      a: number;
      iy: number;
      iz: number;
      dyz: number;
      h: number;
      k: number;
      j: number;
    };
  };
  positionToNodeLabel: Map<number, string>;
  beamLength: number;
}

type AssembleAnalysisOutputFn = (
  combinationResultsMap: Map<string, SingleCombinationResult | null>,
  individualLoadTypeResultsMap: Map<LoadType, SingleLoadTypeResult | null>,
  activeLoadTypes: LoadType[],
  beamPropertiesState: BeamPropertiesState | null,
  supports: Support[],
  boundaryConditions: FemModelDefinition["boundaryConditions"],
  positionToNodeLabel: Map<number, string>,
  targetUnitSystem: UnitSystem,
  beamLength: number,
  designMethod: 'ASD' | 'LRFD'
) => Promise<AnalysisOutput | null>;

// --- Helper Functions ---

const findSpanLength = (
  supports: Support[],
  position: number
): number | null => {
  const sortedSupports = [...supports].sort((a, b) => a.position - b.position);
  for (let i = 0; i < sortedSupports.length - 1; i++) {
    const start = sortedSupports[i].position;
    const end = sortedSupports[i + 1].position;
    if (position >= start - TOLERANCE && position <= end + TOLERANCE) {
      if (
        position >= start - TOLERANCE &&
        (position < end - TOLERANCE ||
          (i === sortedSupports.length - 2 &&
            Math.abs(position - end) < TOLERANCE))
      ) {
        const span = end - start;
        return span > TOLERANCE ? span : null;
      }
    }
  }
  if (
    sortedSupports.length === 2 &&
    Math.abs(position - sortedSupports[0].position) < TOLERANCE &&
    Math.abs(sortedSupports[0].position) < TOLERANCE
  ) {
    const span = sortedSupports[1].position - sortedSupports[0].position;
    return span > TOLERANCE ? span : null;
  }
  return null;
};

const getAllowableStresses = (propertiesState: BeamPropertiesState | null) => {
  if (!propertiesState) {
    return { Fb_allow: 24000, Fv_allow: 150 };
  }
  let Fb_allow = 24000;
  let Fv_allow = 150;
  if (
    propertiesState.manual_Fb_allow !== null &&
    isFinite(propertiesState.manual_Fb_allow)
  ) {
    Fb_allow = propertiesState.manual_Fb_allow;
  } else if (propertiesState.designValues) {
    const dv = propertiesState.designValues as any;
    Fb_allow = dv.adjusted_Fb ?? dv.Fb ?? Fb_allow;
  }
  if (
    propertiesState.manual_Fv_allow !== null &&
    isFinite(propertiesState.manual_Fv_allow)
  ) {
    Fv_allow = propertiesState.manual_Fv_allow;
  } else if (propertiesState.designValues) {
    const dv = propertiesState.designValues as any;
    Fv_allow = dv.adjusted_Fv ?? dv.Fv ?? Fv_allow;
  }
  return { Fb_allow, Fv_allow };
};

const createFemModelDefinition = (
  beamData: BeamData
): FemModelDefinition | null => {
  const { properties, supports } = beamData;
  if (
    !properties ||
    typeof properties.length !== "number" ||
    properties.length <= 0 ||
    typeof properties.elasticModulus !== "number" ||
    properties.elasticModulus <= 0 ||
    typeof properties.momentOfInertia !== "number" ||
    properties.momentOfInertia <= 0 ||
    typeof properties.area !== "number" ||
    properties.area <= 0 ||
    !supports ||
    supports.length < 2
  ) {
    console.error(
      "Invalid beam properties or supports for FEM definition.",
      properties,
      supports
    );
    return null;
  }
  console.log("Beam data After converting:", beamData);
  beamData.loadGroups.forEach((lg) => {
    console.log("Load group:", lg);
  });
  console.log("Supports:", supports);

  const E = properties.elasticModulus;
  const I = properties.momentOfInertia;
  const A = properties.area;
  const beamLength = properties.length;
  
  console.log("FEM Model Creation - Beam Properties in Solver Units:", {
    length: beamLength,
    elasticModulus: E,
    momentOfInertia: I,
    area: A
  });

  // For steel, G ≈ E / (2 * (1 + ν)) where ν ≈ 0.3, so G ≈ 0.385 * E
  const G = E * 0.385; // Shear modulus for steel
  const materialDef = { label: "mat1", properties: { e: E, g: G } };
  const csDef = {
    label: "cs1",
    properties: { a: A, iy: I, iz: 1.0, dyz: 0, h: 1, k: 1e10, j: 1e10 },
  };

  const nodePositions = new Set<number>([0, beamLength]);
  supports.forEach((s: Support) => nodePositions.add(s.position));
  
  // Add nodes at the start and end of each load segment to ensure accurate element discretization
  beamData.loadGroups.forEach((group) => {
    group.loads.forEach((load) => {
      if (load.startPosition !== undefined) {
        nodePositions.add(load.startPosition);
      }
      if (load.endPosition !== undefined) {
        nodePositions.add(load.endPosition);
      }
    });
  });

  const sortedPositions = Array.from(nodePositions).sort((a, b) => a - b);
  const positionToNodeLabel = new Map<number, string>();
  const nodes: FemModelDefinition["nodes"] = [];
  sortedPositions.forEach((pos, index) => {
    const label = (index + 1).toString();
    nodes.push({ label, coords: [pos, 0, 0] });
    positionToNodeLabel.set(pos, label);
  });

  const boundaryConditions: FemModelDefinition["boundaryConditions"] = [];
  supports.forEach((support: Support) => {
    const nodeLabel = positionToNodeLabel.get(support.position);
    if (!nodeLabel) {
      console.warn(
        `Support at position ${support.position} does not align with a node. Skipping BC.`
      );
      return;
    }
    let bcs: DofID[] = [];
    switch (support.type) {
      case SupportType.PIN:
        bcs = [DofID.Dx, DofID.Dz];
        break;
      case SupportType.ROLLER:
        bcs = [DofID.Dz];
        break;
      case SupportType.FIXED:
        bcs = [DofID.Dx, DofID.Dz, DofID.Ry];
        break;
      default:
        console.warn(`Unsupported support type: ${support.type}`);
    }
    if (bcs.length > 0) boundaryConditions.push({ nodeLabel, bcs });
  });

  const elements: FemModelDefinition["elements"] = [];
  for (let i = 0; i < sortedPositions.length - 1; i++) {
    const startPos = sortedPositions[i];
    const endPos = sortedPositions[i + 1];
    const startNodeLabel = positionToNodeLabel.get(startPos);
    const endNodeLabel = positionToNodeLabel.get(endPos);
    const elementLabel = `E${i + 1}`;
    if (startNodeLabel && endNodeLabel) {
      elements.push({
        label: elementLabel,
        nodes: [startNodeLabel, endNodeLabel],
        materialLabel: materialDef.label,
        csLabel: csDef.label,
      });
    } else {
      console.error(
        `Could not find node labels for element between ${startPos} and ${endPos}`
      );
      return null;
    }
  }

  return {
    nodes,
    elements,
    boundaryConditions,
    material: materialDef,
    crossSection: csDef,
    positionToNodeLabel,
    beamLength,
  };
};

const applyCombinationLoads = (
  solver: LinearStaticSolver,
  femDefinition: FemModelDefinition,
  loadGroups: LoadGroup[],
  comboFactors: any,
  comboName: string
): boolean => {
  const domain = solver.domain;
  const defaultLoadCase = solver.loadCases[0] as LoadCase;
  if (!defaultLoadCase) {
    throw new Error(
      "Default load case not found in solver instance provided to applyCombinationLoads."
    );
  }
  let loadsApplied = false;
  
  const elementsMap = new Map<string, Beam2D>();
  femDefinition.elements.forEach((elDef) => {
    const element = domain.getElement(elDef.label);
    if (element instanceof Beam2D) {
      elementsMap.set(elDef.label, element);
      if (!(element as any)._calculatedLength) {
        const node1 = domain.getNode(elDef.nodes[0]);
        const node2 = domain.getNode(elDef.nodes[1]);
        if (node1 && node2) {
          (element as any)._calculatedLength = Math.abs(
            node2.coords[0] - node1.coords[0]
          );
        }
      }
    }
  });

  // Accumulator for distributed loads on each element
  const elementDistributedLoads = new Map<string, number>();
  elementsMap.forEach((_, label) => {
    elementDistributedLoads.set(label, 0);
  });

  loadGroups.forEach((group) => {
    group.loads.forEach((load: Load) => {
      const factor =
        comboFactors?.coefficients?.[0]?.[load.loadType as LoadType] ?? 0;
      if (Math.abs(factor) < TOLERANCE) return;
      const factoredStartMag = (load.startMagnitude ?? 0) * factor;
      const factoredEndMag =
        (load.endMagnitude ?? load.startMagnitude ?? 0) * factor;
      const loadStart = load.startPosition ?? 0;
      const loadEnd = load.endPosition ?? loadStart;

      elementsMap.forEach((element) => {
        const node1 = domain.getNode(element.nodes[0]);
        const node2 = domain.getNode(element.nodes[1]);
        if (!node1 || !node2) return;

        const elemStartPos = node1.coords[0];
        const elemEndPos = node2.coords[0];
        const elementLength =
          (element as any)._calculatedLength ?? elemEndPos - elemStartPos;
        const overlaps =
          Math.max(loadStart, elemStartPos) <
          Math.min(loadEnd, elemEndPos) + TOLERANCE;

        if (overlaps) {
          if (load.type === LoadApplicationType.POINT) {
            const relativePos = loadStart - elemStartPos;
            const isOnOrAfterStart = relativePos >= -TOLERANCE;
            const isBeforeOrAtEnd = relativePos <= elementLength + TOLERANCE;
            const isAtVeryEnd = Math.abs(loadStart - femDefinition.beamLength) < TOLERANCE;

            // Apply load if it's within the element, but not at the end,
            // unless it's the very last point of the entire beam.
            if (
              isOnOrAfterStart &&
              isBeforeOrAtEnd &&
              (relativePos < elementLength - TOLERANCE || isAtVeryEnd)
            ) {
              if (Math.abs(factoredStartMag) > TOLERANCE) {
                const clampedRelativePos = Math.max(
                  0,
                  Math.min(relativePos, elementLength)
                );
                const values = [0, -factoredStartMag, 0, clampedRelativePos];
                defaultLoadCase.createBeamConcentratedLoad(
                  element.label,
                  values,
                  true
                );
                loadsApplied = true;
              }
            }
          } else if (load.type === LoadApplicationType.DISTRIBUTED) {
            // Check if this is effectively a point load (same start and end position)
            const isEffectivelyPointLoad = Math.abs(loadEnd - loadStart) < TOLERANCE;

            if (isEffectivelyPointLoad) {
              // Handle as point load to avoid double application across multiple elements
              const relativePos = loadStart - elemStartPos;
              const isOnOrAfterStart = relativePos >= -TOLERANCE;
              const isBeforeOrAtEnd = relativePos <= elementLength + TOLERANCE;
              const isAtVeryEnd = Math.abs(loadStart - femDefinition.beamLength) < TOLERANCE;

              // Apply load if it's within the element, but not at the end,
              // unless it's the very last point of the entire beam.
              if (
                isOnOrAfterStart &&
                isBeforeOrAtEnd &&
                (relativePos < elementLength - TOLERANCE || isAtVeryEnd)
              ) {
                if (Math.abs(factoredStartMag) > TOLERANCE) {
                  const clampedRelativePos = Math.max(
                    0,
                    Math.min(relativePos, elementLength)
                  );
                  const values = [0, -factoredStartMag, 0, clampedRelativePos];
                  defaultLoadCase.createBeamConcentratedLoad(
                    element.label,
                    values,
                    true
                  );
                  loadsApplied = true;
                }
              }
            } else {
              // Handle as true distributed load
              const avgMagnitude = (factoredStartMag + factoredEndMag) / 2;
              if (Math.abs(avgMagnitude) > TOLERANCE) {
                const currentLoad = elementDistributedLoads.get(element.label) || 0;
                elementDistributedLoads.set(element.label, currentLoad + avgMagnitude);
                loadsApplied = true;
              }
            }
          }
        }
      });
    });
  });

  // Apply the accumulated distributed loads
  elementDistributedLoads.forEach((totalLoad, elementLabel) => {
    if (Math.abs(totalLoad) > TOLERANCE) {
      const values = [0, -totalLoad, 0];
      defaultLoadCase.createBeamElementUniformEdgeLoad(
        elementLabel,
        values,
        true
      );
    }
  });

  return loadsApplied;
};

const applySingleTypeLoads = (
  solver: LinearStaticSolver,
  femDefinition: FemModelDefinition,
  loadGroups: LoadGroup[],
  targetLoadType: LoadType
): boolean => {
  const domain = solver.domain;
  const defaultLoadCase = solver.loadCases[0] as LoadCase;
  if (!defaultLoadCase) {
    throw new Error(
      "Default load case not found in solver instance provided to applySingleTypeLoads."
    );
  }
  let loadsApplied = false;
  const elementsMap = new Map<string, Beam2D>();
  femDefinition.elements.forEach((elDef) => {
    const element = domain.getElement(elDef.label);
    if (element instanceof Beam2D) {
      elementsMap.set(elDef.label, element);
      if (!(element as any)._calculatedLength) {
        const node1 = domain.getNode(elDef.nodes[0]);
        const node2 = domain.getNode(elDef.nodes[1]);
        if (node1 && node2) {
          (element as any)._calculatedLength = Math.abs(
            node2.coords[0] - node1.coords[0]
          );
        }
      }
    }
  });

  loadGroups.forEach((group) => {
    group.loads.forEach((load: Load) => {
      if (load.loadType !== targetLoadType) return;
      const factor = 1.0;
      const factoredStartMag = (load.startMagnitude ?? 0) * factor;
      const factoredEndMag =
        (load.endMagnitude ?? load.startMagnitude ?? 0) * factor;
      const loadStart = load.startPosition ?? 0;
      const loadEnd = load.endPosition ?? loadStart;

      elementsMap.forEach((element) => {
        const node1 = domain.getNode(element.nodes[0]);
        const node2 = domain.getNode(element.nodes[1]);
        if (!node1 || !node2) return;

        const elemStartPos = node1.coords[0];
        const elemEndPos = node2.coords[0];
        const elementLength =
          (element as any)._calculatedLength ?? elemEndPos - elemStartPos;
        const overlaps =
          Math.max(loadStart, elemStartPos) <
          Math.min(loadEnd, elemEndPos) + TOLERANCE;

        if (overlaps) {
          if (load.type === LoadApplicationType.POINT) {
            const relativePos = loadStart - elemStartPos;
            const isOnOrAfterStart = relativePos >= -TOLERANCE;
            const isBeforeOrAtEnd = relativePos <= elementLength + TOLERANCE;
            const isAtVeryEnd = Math.abs(loadStart - femDefinition.beamLength) < TOLERANCE;

            // Apply load if it's within the element, but not at the end,
            // unless it's the very last point of the entire beam.
            if (
              isOnOrAfterStart &&
              isBeforeOrAtEnd &&
              (relativePos < elementLength - TOLERANCE || isAtVeryEnd)
            ) {
              if (Math.abs(factoredStartMag) > TOLERANCE) {
                const clampedRelativePos = Math.max(
                  0,
                  Math.min(relativePos, elementLength)
                );
                const values = [0, -factoredStartMag, 0, clampedRelativePos];
                defaultLoadCase.createBeamConcentratedLoad(
                  element.label,
                  values,
                  true
                );
                loadsApplied = true;
              }
            }
          } else if (load.type === LoadApplicationType.DISTRIBUTED) {
            // Check if this is effectively a point load (same start and end position)
            const isEffectivelyPointLoad = Math.abs(loadEnd - loadStart) < TOLERANCE;

            if (isEffectivelyPointLoad) {
              // Handle as point load to avoid double application across multiple elements
              const relativePos = loadStart - elemStartPos;
              const isOnOrAfterStart = relativePos >= -TOLERANCE;
              const isBeforeOrAtEnd = relativePos <= elementLength + TOLERANCE;
              const isAtVeryEnd = Math.abs(loadStart - femDefinition.beamLength) < TOLERANCE;

              // Apply load if it's within the element, but not at the end,
              // unless it's the very last point of the entire beam.
              if (
                isOnOrAfterStart &&
                isBeforeOrAtEnd &&
                (relativePos < elementLength - TOLERANCE || isAtVeryEnd)
              ) {
                if (Math.abs(factoredStartMag) > TOLERANCE) {
                  const clampedRelativePos = Math.max(
                    0,
                    Math.min(relativePos, elementLength)
                  );
                  const values = [0, -factoredStartMag, 0, clampedRelativePos];
                  defaultLoadCase.createBeamConcentratedLoad(
                    element.label,
                    values,
                    true
                  );
                  loadsApplied = true;
                }
              }
            } else {
              // Handle as true distributed load
              const avgMagnitude = (factoredStartMag + factoredEndMag) / 2;
              if (Math.abs(avgMagnitude) > TOLERANCE) {
                const values = [0, -avgMagnitude, 0];
                defaultLoadCase.createBeamElementUniformEdgeLoad(
                  element.label,
                  values,
                  true
                );
                loadsApplied = true;
              }
            }
          }
        }
      });
    });
  });
  return loadsApplied;
};

const extractResultsFromSolver = (
  solver: LinearStaticSolver,
  femDefinition: FemModelDefinition
): SingleCombinationResult => {
  const domain = solver.domain;
  const defaultLoadCase = solver.loadCases[0] as LoadCase;
  if (!defaultLoadCase) {
    console.error(
      "Default load case not found in solver instance provided to extractResultsFromSolver."
    );
    return {
      diagramData: { shear: [], moment: [], deflection: [] },
      reactions: null,
    };
  }
  const diagramData: DiagramData = { shear: [], moment: [], deflection: [] };
  const reactions: SingleCombinationResult["reactions"] = {
    forces: {},
    moments: {},
  };

  femDefinition.elements.forEach((elDef) => {
    const element = domain.getElement(elDef.label);
    if (!(element instanceof Beam2D)) {
      console.warn(`Element ${elDef.label} not found or not a Beam2D element.`);
      return;
    }
    const node1 = domain.getNode(element.nodes[0]);
    if (!node1) return;
    const elemStartPos = node1.coords[0];
    try {
      const shearResult = element.computeShearForce(
        defaultLoadCase,
        NUM_POINTS_PER_ELEMENT
      );
      const momentResult = element.computeBendingMoment(
        defaultLoadCase,
        NUM_POINTS_PER_ELEMENT
      );
      const deflResult = element.computeGlobalDefl(
        defaultLoadCase,
        NUM_POINTS_PER_ELEMENT
      );
      if (!shearResult || !momentResult || !deflResult) {
        console.warn(
          `Results computation returned null/undefined for element ${element.label}.`
        );
        return;
      }
      for (let i = 0; i < NUM_POINTS_PER_ELEMENT; i++) {
        const localX = shearResult.x?.[i];
        if (localX === undefined) continue;
        const globalX = elemStartPos + localX;
        const shearVal = -(shearResult.V?.[i] ?? 0);
        const momentVal = -(momentResult.M?.[i] ?? 0);
        const deflectionVal = deflResult.w?.[i] ?? 0;
        diagramData.shear.push({ x: globalX, value: shearVal });
        diagramData.moment.push({ x: globalX, value: momentVal });
        diagramData.deflection.push({ x: globalX, value: deflectionVal });
      }
    } catch (computeError) {
      console.warn(
        `Could not compute results for element ${element.label}:`,
        computeError
      );
    }
  });

  diagramData.shear.sort((a, b) => a.x - b.x);
  diagramData.moment.sort((a, b) => a.x - b.x);
  diagramData.deflection.sort((a, b) => a.x - b.x);

  femDefinition.boundaryConditions.forEach(({ nodeLabel }) => {
    const node = domain.getNode(nodeLabel);
    if (!(node instanceof Node)) {
      console.warn(
        `Node ${nodeLabel} for reaction extraction not found or invalid.`
      );
      return;
    }
    try {
      const reactionResult = node.getReactions(defaultLoadCase);
      let fzReaction = 0;
      let ryReaction = 0;
      if (reactionResult && reactionResult.dofs && reactionResult.values) {
        const dzIndex = reactionResult.dofs.indexOf(DofID.Dz);
        if (dzIndex !== -1) {
          try {
            if (Array.isArray(reactionResult.values)) {
              fzReaction = Number(reactionResult.values[dzIndex]) || 0;
            } else if ((reactionResult.values as any)._data && Array.isArray((reactionResult.values as any)._data)) {
              // Handle DenseMatrix format
              fzReaction = Number((reactionResult.values as any)._data[dzIndex]) || 0;
            } else {
              // Handle other matrix formats
              fzReaction = Number((reactionResult.values as any).get(dzIndex, 0)) || 0;
            }
          } catch {
            fzReaction = 0;
          }
        }
        const ryIndex = reactionResult.dofs.indexOf(DofID.Ry);
        if (ryIndex !== -1) {
          try {
            if (Array.isArray(reactionResult.values)) {
              ryReaction = Number(reactionResult.values[ryIndex]) || 0;
            } else if ((reactionResult.values as any)._data && Array.isArray((reactionResult.values as any)._data)) {
              // Handle DenseMatrix format
              ryReaction = Number((reactionResult.values as any)._data[ryIndex]) || 0;
            } else {
              // Handle other matrix formats
              ryReaction = Number((reactionResult.values as any).get(ryIndex, 0)) || 0;
            }
          } catch {
            ryReaction = 0;
          }
        }
      } else {
        console.warn(
          `Reaction result structure invalid or missing for node ${nodeLabel}.`
        );
      }
      reactions.forces[nodeLabel] = fzReaction;
      reactions.moments[nodeLabel] = ryReaction;
    } catch (reactionError) {
      console.warn(
        `Could not get reactions for node ${nodeLabel}:`,
        reactionError
      );
      reactions.forces[nodeLabel] = 0;
      reactions.moments[nodeLabel] = 0;
    }
  });
  console.log("reactions:", reactions);
  return { diagramData, reactions };
};

const extractReactionsFromSolver = (
  solver: LinearStaticSolver,
  femDefinition: FemModelDefinition
): SingleLoadTypeResult | null => {
  const domain = solver.domain;
  const defaultLoadCase = solver.loadCases[0] as LoadCase;
  if (!defaultLoadCase) {
    console.error(
      "Default load case not found in solver instance provided to extractReactionsFromSolver."
    );
    return null;
  }
  const reactions: SingleLoadTypeResult = { forces: {}, moments: {} };
  femDefinition.boundaryConditions.forEach(({ nodeLabel }) => {
    const node = domain.getNode(nodeLabel);
    if (!(node instanceof Node)) {
      console.warn(
        `Node ${nodeLabel} for reaction extraction not found or invalid.`
      );
      return;
    }
    try {
      const reactionResult = node.getReactions(defaultLoadCase);
      let fzReaction = 0;
      let ryReaction = 0;
      if (reactionResult && reactionResult.dofs && reactionResult.values) {
        const dzIndex = reactionResult.dofs.indexOf(DofID.Dz);
        if (dzIndex !== -1) {
          try {
            if (Array.isArray(reactionResult.values)) {
              fzReaction = Number(reactionResult.values[dzIndex]) || 0;
            } else if ((reactionResult.values as any)._data && Array.isArray((reactionResult.values as any)._data)) {
              // Handle DenseMatrix format
              fzReaction = Number((reactionResult.values as any)._data[dzIndex]) || 0;
            } else {
              // Handle other matrix formats
              fzReaction = Number((reactionResult.values as any).get(dzIndex, 0)) || 0;
            }
          } catch {
            fzReaction = 0;
          }
        }
        const ryIndex = reactionResult.dofs.indexOf(DofID.Ry);
        if (ryIndex !== -1) {
          try {
            if (Array.isArray(reactionResult.values)) {
              ryReaction = Number(reactionResult.values[ryIndex]) || 0;
            } else if ((reactionResult.values as any)._data && Array.isArray((reactionResult.values as any)._data)) {
              // Handle DenseMatrix format
              ryReaction = Number((reactionResult.values as any)._data[ryIndex]) || 0;
            } else {
              // Handle other matrix formats
              ryReaction = Number((reactionResult.values as any).get(ryIndex, 0)) || 0;
            }
          } catch {
            ryReaction = 0;
          }
        }
      } else {
        console.warn(
          `Reaction result structure invalid or missing for node ${nodeLabel}.`
        );
      }
      reactions.forces[nodeLabel] = fzReaction;
      reactions.moments[nodeLabel] = ryReaction;
    } catch (reactionError) {
      console.warn(
        `Could not get reactions for node ${nodeLabel}:`,
        reactionError
      );
      reactions.forces[nodeLabel] = 0;
      reactions.moments[nodeLabel] = 0;
    }
  });
  return reactions;
};

const analyzeSingleCombination = (
  comboName: string,
  comboFactors: any,
  beamData: BeamData,
  femDefinition: FemModelDefinition
): SingleCombinationResult | null => {
  console.log(`Analyzing combination: ${comboName}`);
  try {
    const solver = new LinearStaticSolver();
    const domain = solver.domain;
    domain.createMaterial(
      femDefinition.material.label,
      femDefinition.material.properties
    );
    domain.createCrossSection(
      femDefinition.crossSection.label,
      femDefinition.crossSection.properties
    );
    femDefinition.nodes.forEach((n) => domain.createNode(n.label, n.coords));
    femDefinition.elements.forEach((e) => {
      const element = domain.createBeam2D(
        e.label,
        e.nodes,
        e.materialLabel,
        e.csLabel
      );
      const node1 = domain.getNode(e.nodes[0]);
      const node2 = domain.getNode(e.nodes[1]);
      if (element && node1 && node2)
        (element as any)._calculatedLength = Math.abs(
          node2.coords[0] - node1.coords[0]
        );
    });
    femDefinition.boundaryConditions.forEach((bc) => {
      const node = domain.getNode(bc.nodeLabel);
      if (node) node.change2({ bcs: bc.bcs });
      else
        console.warn(
          `Node ${bc.nodeLabel} not found while applying BCs for combo ${comboName}.`
        );
    });
    console.log("solver.loadCases before applying load combo:", solver.loadCases);
    const loadsWereApplied = applyCombinationLoads(
      solver,
      femDefinition,
      beamData.loadGroups,
      comboFactors,
      comboName
    );
    console.log("solver.loadCases after applying load combo:", solver.loadCases);
    if (!loadsWereApplied) {
      console.log(
        `No relevant loads applied for combination ${comboName}, skipping solve.`
      );
      return {
        diagramData: { shear: [], moment: [], deflection: [] },
        reactions: { forces: {}, moments: {} },
      };
    }
    solver.solve();
    console.log(`Combination ${comboName} solved.`);
    const results = extractResultsFromSolver(solver, femDefinition);
    return results;
  } catch (error) {
    console.error(`Error analyzing combination ${comboName}:`, error);
    return null;
  }
};

const analyzeSingleLoadType = (
  loadType: LoadType,
  beamData: BeamData,
  femDefinition: FemModelDefinition
): SingleLoadTypeResult | null => {
  console.log(`Analyzing individual load type: ${loadType}`);
  try {
    const solver = new LinearStaticSolver();
    const domain = solver.domain;
    domain.createMaterial(
      femDefinition.material.label,
      femDefinition.material.properties
    );
    domain.createCrossSection(
      femDefinition.crossSection.label,
      femDefinition.crossSection.properties
    );
    femDefinition.nodes.forEach((n) => domain.createNode(n.label, n.coords));
    femDefinition.elements.forEach((e) => {
      const element = domain.createBeam2D(
        e.label,
        e.nodes,
        e.materialLabel,
        e.csLabel
      );
      const node1 = domain.getNode(e.nodes[0]);
      const node2 = domain.getNode(e.nodes[1]);
      if (element && node1 && node2)
        (element as any)._calculatedLength = Math.abs(
          node2.coords[0] - node1.coords[0]
        );
    });
    femDefinition.boundaryConditions.forEach((bc) => {
      const node = domain.getNode(bc.nodeLabel);
      if (node) node.change2({ bcs: bc.bcs });
      else
        console.warn(
          `Node ${bc.nodeLabel} not found while applying BCs for type ${loadType}.`
        );
    });
    const loadsWereApplied = applySingleTypeLoads(
      solver,
      femDefinition,
      beamData.loadGroups,
      loadType
    );
    if (!loadsWereApplied) {
      console.log(
        `No relevant loads applied for type ${loadType}, skipping solve.`
      );
      return { forces: {}, moments: {} };
    }
    solver.solve();
    console.log(`Load type ${loadType} solved.`);
    const reactions = extractReactionsFromSolver(solver, femDefinition);
    return reactions;
  } catch (error) {
    console.error(`Error analyzing load type ${loadType}:`, error);
    return null;
  }
};

function convertBeamDataToSolverUnits(
  inputData: BeamData,
  sourceUnitSystem: UnitSystem
): BeamData {
  const solverData = JSON.parse(JSON.stringify(inputData)) as BeamData;
  
  console.log("convertBeamDataToSolverUnits - Input:", {
    sourceUnitSystem,
    inputProperties: {
      length: inputData.properties.length,
      elasticModulus: inputData.properties.elasticModulus,
      momentOfInertia: inputData.properties.momentOfInertia,
      area: inputData.properties.area
    }
  });
  if (sourceUnitSystem === UnitSystem.METRIC) {
    solverData.properties.length = mToIn(solverData.properties.length);
    solverData.properties.elasticModulus = paToPsi(
      solverData.properties.elasticModulus
    );
    solverData.properties.momentOfInertia = m4ToIn4(
      solverData.properties.momentOfInertia
    );
    solverData.supports.forEach((support) => {
      support.position = mToIn(support.position);
    });
    solverData.loadGroups.forEach((group) => {
      group.startPosition = mToIn(group.startPosition);
      if (group.endPosition !== undefined)
        group.endPosition = mToIn(group.endPosition);
      if (group.tributaryWidth !== undefined)
        group.tributaryWidth = mToIn(group.tributaryWidth);
      group.loads = group.loads.map((load) => {
        if (load.startPosition !== undefined)
          load.startPosition = mToIn(load.startPosition);
        if (load.endPosition !== undefined)
          load.endPosition = mToIn(load.endPosition);
        if (load.startMagnitude !== undefined) {
          load.startMagnitude =
            group.type === LoadApplicationType.POINT
              ? nToLb(load.startMagnitude)
              : nPerMToLbPerIn(load.startMagnitude);
        }
        if (load.endMagnitude !== undefined) {
          load.endMagnitude = nPerMToLbPerIn(load.endMagnitude);
        }
        return load;
      });
    });
  } else if (sourceUnitSystem === UnitSystem.IMPERIAL) {
    solverData.properties.length = ftToIn(solverData.properties.length);
    solverData.properties.elasticModulus = solverData.properties.elasticModulus;
    solverData.supports.forEach((support) => {
      support.position = ftToIn(support.position);
    });
    solverData.loadGroups.forEach((group) => {
      group.startPosition = ftToIn(group.startPosition);
      if (group.endPosition !== undefined)
        group.endPosition = ftToIn(group.endPosition);
      // NOTE: Keep tributary width in feet for area load calculations
      // Area loads (lbf/ft²) × tributary width (ft) = distributed load (lbf/ft)
      // Converting to inches would create 12x amplification error
      group.loads = group.loads.map((load) => {
        if (load.startPosition !== undefined)
          load.startPosition = ftToIn(load.startPosition);
        if (load.endPosition !== undefined)
          load.endPosition = ftToIn(load.endPosition);
        if (load.startMagnitude !== undefined) {
          load.startMagnitude =
            group.type === LoadApplicationType.POINT
              ? load.startMagnitude
              : load.startMagnitude; // Keep distributed loads in plf - solver expects force per unit length
        }
        if (load.endMagnitude !== undefined) {
          load.endMagnitude =
            group.type === LoadApplicationType.DISTRIBUTED
              ? load.endMagnitude // Keep distributed loads in plf
              : load.endMagnitude;
        }
        return load;
      });
    });
  }
  
  console.log("convertBeamDataToSolverUnits - Output:", {
    outputProperties: {
      length: solverData.properties.length,
      elasticModulus: solverData.properties.elasticModulus,
      momentOfInertia: solverData.properties.momentOfInertia,
      area: solverData.properties.area
    }
  });
  
  return solverData;
}

function convertResultsToDisplayUnits(
  solverResults: AnalysisOutput,
  targetUnitSystem: UnitSystem
): AnalysisOutput {
  const displayResults = JSON.parse(
    JSON.stringify(solverResults)
  ) as AnalysisOutput;
  const convertX = (x_in: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return inToM(x_in);
    if (targetUnitSystem === UnitSystem.IMPERIAL) return inToFt(x_in);
    return x_in;
  };
  const convertShear = (v_lb: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return lbToN(v_lb);
    if (targetUnitSystem === UnitSystem.IMPERIAL) return lbToKip(v_lb);
    return v_lb;
  };
  const convertMoment = (m_lbIn: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return lbInToNm(m_lbIn);
    if (targetUnitSystem === UnitSystem.IMPERIAL) return lbInToKipFt(m_lbIn);
    return m_lbIn;
  };
  const convertDeflection = (d_in: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return inToM(d_in);
    if (targetUnitSystem === UnitSystem.IMPERIAL) return d_in;
    return d_in;
  };
  const convertStress = (s_psi: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return psiToPa(s_psi);
    if (targetUnitSystem === UnitSystem.IMPERIAL) return s_psi;
    return s_psi;
  };

  const convertReactionForce = (v_lb: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return lbToN(v_lb);
    // For Imperial, reactions are expected in lbf, which is the solver's unit.
    if (targetUnitSystem === UnitSystem.IMPERIAL) return v_lb;
    return v_lb;
  };
  const convertReactionMoment = (m_lbIn: number): number => {
    if (targetUnitSystem === UnitSystem.METRIC) return lbInToNm(m_lbIn);
    // For Imperial, reaction moments are expected in lbf-ft.
    if (targetUnitSystem === UnitSystem.IMPERIAL) return m_lbIn / 12;
    return m_lbIn;
  };

  displayResults.diagramData.shear.forEach((p: DataPoint) => {
    p.x = convertX(p.x);
    p.value = convertShear(p.value);
  });
  displayResults.diagramData.moment.forEach((p: DataPoint) => {
    p.x = convertX(p.x);
    p.value = convertMoment(p.value);
  });
  displayResults.diagramData.deflection.forEach((p: DataPoint) => {
    p.x = convertX(p.x);
    p.value = convertDeflection(p.value);
  });

  if (displayResults.summaryData.maxShearValue) {
    displayResults.summaryData.maxShearValue.position = convertX(
      displayResults.summaryData.maxShearValue.position
    );
    displayResults.summaryData.maxShearValue.value = convertShear(
      displayResults.summaryData.maxShearValue.value
    );
  }
  if (displayResults.summaryData.maxMomentValue) {
    displayResults.summaryData.maxMomentValue.position = convertX(
      displayResults.summaryData.maxMomentValue.position
    );
    displayResults.summaryData.maxMomentValue.value = convertMoment(
      displayResults.summaryData.maxMomentValue.value
    );
  }
  if (displayResults.summaryData.maxTotalDeflectionDownward) {
    displayResults.summaryData.maxTotalDeflectionDownward.position = convertX(
      displayResults.summaryData.maxTotalDeflectionDownward.position
    );
    displayResults.summaryData.maxTotalDeflectionDownward.value =
      convertDeflection(
        displayResults.summaryData.maxTotalDeflectionDownward.value
      );
  }
  if (displayResults.summaryData.maxTotalDeflectionUpward) {
    displayResults.summaryData.maxTotalDeflectionUpward.position = convertX(
      displayResults.summaryData.maxTotalDeflectionUpward.position
    );
    displayResults.summaryData.maxTotalDeflectionUpward.value =
      convertDeflection(
        displayResults.summaryData.maxTotalDeflectionUpward.value
      );
  }
  if (displayResults.summaryData.maxBendingStressRatio) {
    displayResults.summaryData.maxBendingStressRatio.position = convertX(
      displayResults.summaryData.maxBendingStressRatio.position
    );
    displayResults.summaryData.maxBendingStressRatio.actualStress =
      convertStress(
        displayResults.summaryData.maxBendingStressRatio.actualStress
      );
    displayResults.summaryData.maxBendingStressRatio.allowableStress =
      convertStress(
        displayResults.summaryData.maxBendingStressRatio.allowableStress
      );
  }
  if (displayResults.summaryData.maxShearStressRatio) {
    displayResults.summaryData.maxShearStressRatio.position = convertX(
      displayResults.summaryData.maxShearStressRatio.position
    );
    displayResults.summaryData.maxShearStressRatio.actualStress = convertStress(
      displayResults.summaryData.maxShearStressRatio.actualStress
    );
    displayResults.summaryData.maxShearStressRatio.allowableStress =
      convertStress(
        displayResults.summaryData.maxShearStressRatio.allowableStress
      );
  }

  if (displayResults.summaryData.supportReactions) {
    Object.values(displayResults.summaryData.supportReactions.forces).forEach(
      (reaction: any) => {
        reaction.maxUp = convertReactionForce(reaction.maxUp);
        reaction.maxDown = convertReactionForce(reaction.maxDown);
      }
    );
    Object.values(displayResults.summaryData.supportReactions.moments).forEach(
      (reaction: any) => {
        reaction.maxPositive = convertReactionMoment(reaction.maxPositive);
        reaction.maxNegative = convertReactionMoment(reaction.maxNegative);
      }
    );
  }
  if (displayResults.summaryData.individualLoadReactions?.forces) {
    Object.values(
      displayResults.summaryData.individualLoadReactions.forces
    ).forEach((nodeReactions: any) => {
      Object.keys(nodeReactions).forEach((loadType) => {
        const typedLoadType = loadType as LoadType;
        if (nodeReactions[typedLoadType] !== undefined) {
          nodeReactions[typedLoadType] = convertReactionForce(
            nodeReactions[typedLoadType]!
          );
        }
      });
    });
  }
  return displayResults;
}

const assembleAnalysisOutput: AssembleAnalysisOutputFn = async (
  combinationResultsMap,
  individualLoadTypeResultsMap,
  activeLoadTypes,
  beamPropertiesState,
  supports,
  boundaryConditions,
  positionToNodeLabel,
  targetUnitSystem,
  beamLength,
  designMethod: 'ASD' | 'LRFD'
) => {
  console.log("Processing all combination results...");
  const envelopeMaxValues = {
    shear: { absValue: 0, value: 0, position: NaN, loadComboName: "N/A" },
    moment: { absValue: 0, value: 0, position: NaN, loadComboName: "N/A" },
    deflectionDown: { value: 0, position: NaN, loadComboName: "N/A" },
    deflectionUp: { value: 0, position: NaN, loadComboName: "N/A" },
  };
  let overallMaxPositiveMoment = { value: 0, position: NaN, loadComboName: "N/A" };
  let overallMaxNegativeMoment = { value: 0, position: NaN, loadComboName: "N/A" };

  const allReactionsByCombo = new Map<
    string,
    SingleCombinationResult["reactions"]
  >();

  if (combinationResultsMap.size === 0) {
    console.warn("No combination results to process.");
    return {
      diagramData: { shear: [], moment: [], deflection: [] },
      summaryData: {
        maxShearValue: { value: NaN, position: NaN, loadComboName: "N/A" },
        maxMomentValue: { value: NaN, position: NaN, loadComboName: "N/A" },
        maxBendingStressRatio: {
          ratio: NaN,
          actualStress: NaN,
          allowableStress: NaN,
          position: NaN,
          spanNumber: NaN,
          loadComboName: "N/A",
        },
        maxShearStressRatio: {
          ratio: NaN,
          actualStress: NaN,
          allowableStress: NaN,
          position: NaN,
          spanNumber: NaN,
          loadComboName: "N/A",
        },
        maxTotalDeflectionDownward: {
          value: NaN,
          position: NaN,
          ratio: NaN,
          limit: null,
          loadComboName: "N/A",
        },
        maxTotalDeflectionUpward: {
          value: NaN,
          position: NaN,
          ratio: NaN,
          limit: null,
          loadComboName: "N/A",
        },
        supportReactions: { forces: {}, moments: {} },
        individualLoadReactions: { forces: {} },
      },
    };
  }

  combinationResultsMap.forEach((result, comboName) => {
    if (!result) return;
    if (result.reactions) allReactionsByCombo.set(comboName, result.reactions);
    result.diagramData.shear.forEach((p) => {
      if (Math.abs(p.value) > envelopeMaxValues.shear.absValue) {
        envelopeMaxValues.shear = {
          absValue: Math.abs(p.value),
          value: p.value,
          position: p.x,
          loadComboName: comboName,
        };
      }
    });
    result.diagramData.moment.forEach((p) => {
      if (Math.abs(p.value) > envelopeMaxValues.moment.absValue) {
        envelopeMaxValues.moment = {
          absValue: Math.abs(p.value),
          value: p.value,
          position: p.x,
          loadComboName: comboName,
        };
      }
      if (p.value > overallMaxPositiveMoment.value) {
        overallMaxPositiveMoment = { value: p.value, position: p.x, loadComboName: comboName };
      }
      if (p.value < overallMaxNegativeMoment.value) {
        overallMaxNegativeMoment = { value: p.value, position: p.x, loadComboName: comboName };
      }
    });
    result.diagramData.deflection.forEach((p) => {
      if (p.value < envelopeMaxValues.deflectionDown.value) {
        envelopeMaxValues.deflectionDown = {
          value: p.value,
          position: p.x,
          loadComboName: comboName,
        };
      }
      if (p.value > envelopeMaxValues.deflectionUp.value) {
        envelopeMaxValues.deflectionUp = {
          value: p.value,
          position: p.x,
          loadComboName: comboName,
        };
      }
    });
  });

  const shearComboResult = combinationResultsMap.get(
    envelopeMaxValues.shear.loadComboName
  );
  const controllingShearDiagram: DataPoint[] =
    shearComboResult?.diagramData.shear ?? [];
  const momentComboResult = combinationResultsMap.get(
    envelopeMaxValues.moment.loadComboName
  );
  const controllingMomentDiagram: DataPoint[] =
    momentComboResult?.diagramData.moment ?? [];
  // For deflections, find the controlling combination from service load combinations
  // Service combinations are typically used for deflection checks (D+L, not factored)
  const serviceLoadCombinations = ['1.0D + 1.0L', 'D + L'];
  let controllingDeflectionComboName = '';
  let serviceDeflectionMax = { down: 0, up: 0, downCombo: '', upCombo: '' };
  
  // First, check service load combinations for deflection
  serviceLoadCombinations.forEach(comboName => {
    const serviceResult = combinationResultsMap.get(comboName);
    if (serviceResult) {
      serviceResult.diagramData.deflection.forEach((p) => {
        if (p.value < serviceDeflectionMax.down) {
          serviceDeflectionMax.down = p.value;
          serviceDeflectionMax.downCombo = comboName;
        }
        if (p.value > serviceDeflectionMax.up) {
          serviceDeflectionMax.up = p.value;
          serviceDeflectionMax.upCombo = comboName;
        }
      });
    }
  });
  
  // Use service combination if found, otherwise fall back to envelope method
  if (serviceDeflectionMax.downCombo || serviceDeflectionMax.upCombo) {
    controllingDeflectionComboName = Math.abs(serviceDeflectionMax.down) >= Math.abs(serviceDeflectionMax.up)
      ? serviceDeflectionMax.downCombo
      : serviceDeflectionMax.upCombo;
  } else {
    // Fallback to original method if no service combinations found
    controllingDeflectionComboName = Math.abs(envelopeMaxValues.deflectionDown.value) >=
      Math.abs(envelopeMaxValues.deflectionUp.value)
        ? envelopeMaxValues.deflectionDown.loadComboName
        : envelopeMaxValues.deflectionUp.loadComboName;
  }
  
  const deflectionComboResult = combinationResultsMap.get(
    controllingDeflectionComboName
  );
  const controllingDeflectionDiagram: DataPoint[] =
    deflectionComboResult?.diagramData.deflection ?? [];

  let maxBendingStressRatioResult: StressRatioResult = {
    ratio: NaN,
    actualStress: NaN,
    allowableStress: NaN,
    position: NaN,
    spanNumber: NaN,
    loadComboName: "N/A",
  };
  let maxShearStressRatioResult: StressRatioResult = {
    ratio: NaN,
    actualStress: NaN,
    allowableStress: NaN,
    position: NaN,
    spanNumber: NaN,
    loadComboName: "N/A",
  };
  let maxTotalDeflectionDownwardResult: DeflectionResult = {
    value: NaN,
    position: NaN,
    ratio: NaN,
    limit: null,
    loadComboName: "N/A",
  };
  let maxTotalDeflectionUpwardResult: DeflectionResult = {
    value: NaN,
    position: NaN,
    ratio: NaN,
    limit: null,
    loadComboName: "N/A",
  };

  const allowable = getAllowableStresses(beamPropertiesState);
  const effective_Sxx =
    beamPropertiesState?.manual_Sxx ??
    beamPropertiesState?.lumberProperties?.Sxx ??
    0;
  const effective_Area =
    beamPropertiesState?.manual_Area ??
    beamPropertiesState?.lumberProperties?.area_of_section_a_in2 ??
    0;

  if (effective_Sxx > TOLERANCE) {
    if (beamPropertiesState?.lumberType === "glulam" && beamPropertiesState.designValues) {
      const Sxx = effective_Sxx;
      const Fb_pos_allow = beamPropertiesState.designValues.adjusted_Fb_pos ?? 0;
      const Fb_neg_allow = beamPropertiesState.designValues.adjusted_Fb_neg ?? 0;

      let ratio_pos = NaN;
      let actual_fb_pos = NaN;
      if (Fb_pos_allow > TOLERANCE && overallMaxPositiveMoment.loadComboName !== "N/A" && isFinite(overallMaxPositiveMoment.value)) {
        actual_fb_pos = Math.abs(overallMaxPositiveMoment.value / Sxx);
        ratio_pos = actual_fb_pos / Fb_pos_allow;
      }

      let ratio_neg = NaN;
      let actual_fb_neg = NaN;
      if (Fb_neg_allow > TOLERANCE && overallMaxNegativeMoment.loadComboName !== "N/A" && isFinite(overallMaxNegativeMoment.value)) {
        actual_fb_neg = Math.abs(overallMaxNegativeMoment.value / Sxx);
        ratio_neg = actual_fb_neg / Fb_neg_allow;
      }

      const posIsValid = isFinite(ratio_pos);
      const negIsValid = isFinite(ratio_neg);
      const posIsCritical = posIsValid && (!negIsValid || ratio_pos >= ratio_neg);
      const negIsCritical = negIsValid && (!posIsValid || ratio_neg > ratio_pos);

      if (posIsCritical) {
        maxBendingStressRatioResult = {
          ratio: ratio_pos,
          actualStress: actual_fb_pos,
          allowableStress: Fb_pos_allow,
          position: overallMaxPositiveMoment.position,
          spanNumber: 0,
          loadComboName: overallMaxPositiveMoment.loadComboName,
        };
      } else if (negIsCritical) {
        maxBendingStressRatioResult = {
          ratio: ratio_neg,
          actualStress: actual_fb_neg,
          allowableStress: Fb_neg_allow,
          position: overallMaxNegativeMoment.position,
          spanNumber: 0,
          loadComboName: overallMaxNegativeMoment.loadComboName,
        };
      } else {
         maxBendingStressRatioResult = { ratio: NaN, actualStress: NaN, allowableStress: NaN, position: NaN, spanNumber: NaN, loadComboName: overallMaxPositiveMoment.loadComboName !== "N/A" ? overallMaxPositiveMoment.loadComboName : (overallMaxNegativeMoment.loadComboName !== "N/A" ? overallMaxNegativeMoment.loadComboName : "Error") };
         if (maxBendingStressRatioResult.loadComboName === "N/A" && envelopeMaxValues.moment.loadComboName !== "N/A") {
            maxBendingStressRatioResult.loadComboName = envelopeMaxValues.moment.loadComboName;
         } else if (maxBendingStressRatioResult.loadComboName === "N/A") {
            maxBendingStressRatioResult.loadComboName = "Error";
         }
         console.warn("Glulam bending ratio could not be determined conclusively for pos/neg. Max abs moment combo: ", envelopeMaxValues.moment.loadComboName);
      }

    } else {
      if (allowable.Fb_allow > TOLERANCE && isFinite(envelopeMaxValues.moment.value)) {
        const Sxx = effective_Sxx;
        const maxMomentValue = envelopeMaxValues.moment.value;
        const actual_fb = Math.abs(maxMomentValue / Sxx);
        const bendingRatio = actual_fb / allowable.Fb_allow;
        maxBendingStressRatioResult = {
          ratio: isFinite(bendingRatio) ? bendingRatio : NaN,
          actualStress: isFinite(actual_fb) ? actual_fb : NaN,
          allowableStress: allowable.Fb_allow,
          position: envelopeMaxValues.moment.position,
          spanNumber: 0,
          loadComboName: envelopeMaxValues.moment.loadComboName,
        };
      } else {
        console.warn("Cannot calculate bending ratio (Sawn/Other/Fallback): Allowable Fb, Sxx, or max moment missing/invalid.", {Fb_allow: allowable.Fb_allow, Sxx: effective_Sxx, moment: envelopeMaxValues.moment.value });
        maxBendingStressRatioResult = { ratio: NaN, actualStress: NaN, allowableStress: allowable.Fb_allow ?? NaN, position: NaN, spanNumber: NaN, loadComboName: envelopeMaxValues.moment.loadComboName !== "N/A" ? envelopeMaxValues.moment.loadComboName : "Error" };
      }
    }
  } else {
    console.warn("Cannot calculate bending ratio: Effective Sxx is zero or invalid.");
    maxBendingStressRatioResult = { ratio: NaN, actualStress: NaN, allowableStress: NaN, position: NaN, spanNumber: NaN, loadComboName: "Error" };
  }

  if (
    effective_Area > TOLERANCE &&
    allowable.Fv_allow > TOLERANCE &&
    isFinite(envelopeMaxValues.shear.value)
  ) {
    const Area = effective_Area;
    const maxShearValue = envelopeMaxValues.shear.value;
    const actual_fv = Math.abs((1.5 * maxShearValue) / Area);
    const shearRatio = actual_fv / allowable.Fv_allow;
    maxShearStressRatioResult = {
      ratio: isFinite(shearRatio) ? shearRatio : NaN,
      actualStress: isFinite(actual_fv) ? actual_fv : NaN,
      allowableStress: allowable.Fv_allow,
      position: envelopeMaxValues.shear.position,
      spanNumber: 0,
      loadComboName: envelopeMaxValues.shear.loadComboName,
    };
  } else {
    console.warn(
      "Cannot calculate shear ratio: Section properties, allowable stress, or max shear missing/invalid."
    );
  }

  const effectiveTotalLimit =
    beamPropertiesState?.manual_totalDeflectionLimit ??
    DEFAULT_TOTAL_DEFLECTION_LIMIT;
  const maxDeflectionDownVal = envelopeMaxValues.deflectionDown.value;
  const maxDeflectionDownPos = envelopeMaxValues.deflectionDown.position;
  const spanLengthDown = findSpanLength(supports, maxDeflectionDownPos);
  let ratioDown = NaN;
  if (
    spanLengthDown &&
    spanLengthDown > TOLERANCE &&
    maxDeflectionDownVal !== 0
  ) {
    ratioDown = Math.abs(spanLengthDown / maxDeflectionDownVal);
  } else {
    console.warn(
      `Could not determine span length or deflection is zero for downward deflection at ${maxDeflectionDownPos}.`
    );
  }
  // Update deflection results to use service load combination names when available
  const serviceDownCombo = serviceDeflectionMax.downCombo || envelopeMaxValues.deflectionDown.loadComboName;
  const serviceUpCombo = serviceDeflectionMax.upCombo || envelopeMaxValues.deflectionUp.loadComboName;
  
  maxTotalDeflectionDownwardResult = {
    value: maxDeflectionDownVal,
    position: maxDeflectionDownPos,
    ratio: isFinite(ratioDown) ? ratioDown : NaN,
    limit: effectiveTotalLimit,
    loadComboName: serviceDownCombo,
  };

  const maxDeflectionUpVal = envelopeMaxValues.deflectionUp.value;
  const maxDeflectionUpPos = envelopeMaxValues.deflectionUp.position;
  const spanLengthUp = findSpanLength(supports, maxDeflectionUpPos);
  let ratioUp = NaN;
  if (spanLengthUp && spanLengthUp > TOLERANCE && maxDeflectionUpVal !== 0) {
    ratioUp = Math.abs(spanLengthUp / maxDeflectionUpVal);
  } else {
    console.warn(
      `Could not determine span length or deflection is zero for upward deflection at ${maxDeflectionUpPos}.`
    );
  }
  maxTotalDeflectionUpwardResult = {
    value: maxDeflectionUpVal,
    position: maxDeflectionUpPos,
    ratio: isFinite(ratioUp) ? ratioUp : NaN,
    limit: effectiveTotalLimit,
    loadComboName: serviceUpCombo,
  };

  const finalSupportReactions: {
    forces: {
      [nodeLabel: string]: {
        maxUp: number;
        maxDown: number;
        maxUpCombo: string;
        maxDownCombo: string;
      };
    };
    moments: {
      [nodeLabel: string]: {
        maxPositive: number;
        maxNegative: number;
        maxPosCombo: string;
        maxNegCombo: string;
      };
    };
  } = { forces: {}, moments: {} };
  boundaryConditions.forEach(({ nodeLabel }) => {
    finalSupportReactions.forces[nodeLabel] = {
      maxUp: 0,
      maxDown: 0,
      maxUpCombo: "N/A",
      maxDownCombo: "N/A",
    };
    finalSupportReactions.moments[nodeLabel] = {
      maxPositive: 0,
      maxNegative: 0,
      maxPosCombo: "N/A",
      maxNegCombo: "N/A",
    };
  });
  allReactionsByCombo.forEach((reactions, comboName) => {
    if (!reactions) return;
    Object.entries(reactions.forces).forEach(([nodeLabel, force]) => {
      if (finalSupportReactions.forces[nodeLabel]) {
        if (force > finalSupportReactions.forces[nodeLabel].maxUp) {
          finalSupportReactions.forces[nodeLabel].maxUp = force;
          finalSupportReactions.forces[nodeLabel].maxUpCombo = comboName;
        }
        if (force < finalSupportReactions.forces[nodeLabel].maxDown) {
          finalSupportReactions.forces[nodeLabel].maxDown = force;
          finalSupportReactions.forces[nodeLabel].maxDownCombo = comboName;
        }
      }
    });
    Object.entries(reactions.moments).forEach(([nodeLabel, moment]) => {
      if (finalSupportReactions.moments[nodeLabel]) {
        if (moment > finalSupportReactions.moments[nodeLabel].maxPositive) {
          finalSupportReactions.moments[nodeLabel].maxPositive = moment;
          finalSupportReactions.moments[nodeLabel].maxPosCombo = comboName;
        }
        if (moment < finalSupportReactions.moments[nodeLabel].maxNegative) {
          finalSupportReactions.moments[nodeLabel].maxNegative = moment;
          finalSupportReactions.moments[nodeLabel].maxNegCombo = comboName;
        }
      }
    });
  });

  const finalIndividualLoadReactions: Required<
    SummaryData["individualLoadReactions"]
  > = { forces: {} };
  boundaryConditions.forEach(({ nodeLabel }) => {
    finalIndividualLoadReactions.forces[nodeLabel] = {};
  });
  activeLoadTypes.forEach((loadType) => {
    const result = individualLoadTypeResultsMap.get(loadType);
    if (result?.forces) {
      Object.entries(result.forces).forEach(([nodeLabel, force]) => {
        if (finalIndividualLoadReactions.forces[nodeLabel])
          finalIndividualLoadReactions.forces[nodeLabel][loadType] = force;
      });
    }
  });

  const resultsInSolverUnits: AnalysisOutput = {
    diagramData: {
      shear: controllingShearDiagram,
      moment: controllingMomentDiagram,
      deflection: controllingDeflectionDiagram,
    },
    summaryData: {
      maxShearValue: {
        value: envelopeMaxValues.shear.value,
        position: envelopeMaxValues.shear.position,
        loadComboName: envelopeMaxValues.shear.loadComboName,
      },
      maxMomentValue: {
        value: envelopeMaxValues.moment.value,
        position: envelopeMaxValues.moment.position,
        loadComboName: envelopeMaxValues.moment.loadComboName,
      },
      maxTotalDeflectionDownward: maxTotalDeflectionDownwardResult,
      maxTotalDeflectionUpward: maxTotalDeflectionUpwardResult,
      maxBendingStressRatio: maxBendingStressRatioResult,
      maxShearStressRatio: maxShearStressRatioResult,
      supportReactions: finalSupportReactions,
      individualLoadReactions: finalIndividualLoadReactions,
    },
  };

  // Perform steel design analysis if steel properties are available
  try {
    const steelAnalysis = await performSteelDesignAnalysis(
      beamPropertiesState,
      envelopeMaxValues.moment.value, // Max moment in lb-in (solver units)
      envelopeMaxValues.shear.value,  // Max shear in lbs (solver units)
      beamLength, // Beam length in inches (solver units)
      envelopeMaxValues.moment.loadComboName, // Controlling load combo for moment
      envelopeMaxValues.shear.loadComboName,   // Controlling load combo for shear
      designMethod, // Pass the design method
      envelopeMaxValues.deflectionUp.value, // Max upward deflection (negative value)
      envelopeMaxValues.deflectionDown.value, // Max downward deflection (positive value)
      targetUnitSystem // Pass unit system for proper conversion
    );
    
    if (steelAnalysis) {
      resultsInSolverUnits.summaryData.steelDesign = steelAnalysis;
      console.log("Steel design analysis completed successfully", steelAnalysis);
    } else {
      console.log("No steel properties available - skipping steel design analysis");
    }
  } catch (steelError) {
    console.warn("Steel design analysis failed:", steelError);
    // Don't fail the entire analysis if steel analysis fails
  }

  // Note: Glulam analysis is handled directly via the API endpoint to avoid circular dependencies
  // The glulam analysis results will be integrated at the API level, not here

  try {
    const resultsInDisplayUnits = convertResultsToDisplayUnits(
      resultsInSolverUnits,
      targetUnitSystem
    );
    return resultsInDisplayUnits;
  } catch (conversionError) {
    console.error(
      "Error converting results to display units:",
      conversionError
    );
    return null;
  }
};

// Add steel analysis function
const performSteelDesignAnalysis = async (
  beamPropertiesState: BeamPropertiesState | null,
  maxMomentValue: number, // in lb-in (solver units)
  maxShearValue: number,  // in lbs (solver units)
  beamLength: number,      // in inches (solver units)
  momentLoadComboName: string,
  shearLoadComboName: string,
  designMethod: 'ASD' | 'LRFD',
  maxUpwardDeflection: number, // Max upward deflection (negative value)
  maxDownwardDeflection: number, // Max downward deflection (positive value)
  targetUnitSystem: UnitSystem // Add unit system parameter for conversion
): Promise<SteelDesignAnalysis | null> => {
  if (!beamPropertiesState || !(beamPropertiesState as any).steelSectionProperties || !(beamPropertiesState as any).steelDesignValues) {
    return null; // No steel properties available
  }

  try {
    const sectionProps = (beamPropertiesState as any).steelSectionProperties;
    const designValues = (beamPropertiesState as any).steelDesignValues;

    // Determine section type from section name
    const getSectionType = (sectionName: string): string => {
      const name = sectionName.toUpperCase();
      if (name.startsWith('W')) return 'W';
      if (name.startsWith('S')) return 'S';
      if (name.startsWith('M')) return 'M';
      if (name.startsWith('I')) return 'I';
      if (name.startsWith('C')) return 'C';
      if (name.startsWith('MC')) return 'MC';
      if (name.includes('HSS') && name.includes('X')) return 'HSS-RECT';
      if (name.includes('HSS')) return 'HSS-ROUND';
      if (name.startsWith('2L')) return '2L'; // Double angle
      if (name.startsWith('L')) return 'L';   // Single angle
      return 'W'; // Default fallback
    };

    // Use steel unbraced length if available, otherwise default to beam length
    let unbrancedLength = beamLength; // Default to beam length (in inches, solver units)
    if ((beamPropertiesState as any).isSteelBraced && (beamPropertiesState as any).steelUnbracedLength) {
      const steelLuValue = parseFloat((beamPropertiesState as any).steelUnbracedLength);
      if (!isNaN(steelLuValue) && steelLuValue > 0) {
        // Convert steel unbraced length from display units to solver units (inches)
        if (targetUnitSystem === UnitSystem.IMPERIAL) {
          // Steel unbraced length is in feet in UI, convert to inches for steel analysis
          unbrancedLength = ftToIn(steelLuValue);
        } else {
          // For metric system, steel unbraced length is in meters in UI, convert to inches
          unbrancedLength = mToIn(steelLuValue);
        }
      }
    }
    const lateralTorsionalBucklingModifier = 1.0; // Conservative default

    // Build section for steel analysis
    const section: Section = {
      name: (beamPropertiesState as any).selectedSteelShapeSize || sectionProps.EDI_Std_Nomenclature || 'Unknown',
      Type: getSectionType((beamPropertiesState as any).selectedSteelShapeSize || sectionProps.EDI_Std_Nomenclature || '') as any,
      A: parseFloat(sectionProps.A || '0'),
      d: parseFloat(sectionProps.d || '0'),
      bf: parseFloat(sectionProps.bf || sectionProps.d || '0'),
      tw: parseFloat(sectionProps.tw || '0'),
      tf: parseFloat(sectionProps.tf || sectionProps.tw || '0'),
      Ix: parseFloat(sectionProps.Ix || '0'),
      Iy: parseFloat(sectionProps.Iy || '0'),
      Sx: parseFloat(sectionProps.Sx || '0'),
      Sy: parseFloat(sectionProps.Sy || '0'),
      Zx: parseFloat(sectionProps.Zx || '0'),
      Zy: parseFloat(sectionProps.Zy || '0'),
      rx: parseFloat(sectionProps.rx || '0'),
      ry: parseFloat(sectionProps.ry || '0'),
      J: parseFloat(sectionProps.J || '0'),
      Cw: parseFloat(sectionProps.Cw || '0'),
    };

    const material: Material = {
      Fy: designValues.Fy / 1000, // Convert psi to ksi
      Fu: designValues.Fu / 1000, // Convert psi to ksi
    };

    // Create deflection parameters only if both deflection values are valid
    let deflectionParameters: DeflectionParameters | undefined;
    if (
      typeof maxUpwardDeflection === 'number' && 
      typeof maxDownwardDeflection === 'number' &&
      isFinite(maxUpwardDeflection) && 
      isFinite(maxDownwardDeflection) &&
      !isNaN(maxUpwardDeflection) && 
      !isNaN(maxDownwardDeflection)
    ) {
      deflectionParameters = {
        beamLength: beamLength,
        maxUpwardDeflection: maxUpwardDeflection,
        maxDownwardDeflection: maxDownwardDeflection,
        deflectionLimit: DEFAULT_DEFLECTION_LIMITS.TOTAL, // Use default L/240
      };
      console.log("Steel design analysis: Including deflection parameters", {
        beamLength,
        maxUpwardDeflection,
        maxDownwardDeflection,
        deflectionLimit: DEFAULT_DEFLECTION_LIMITS.TOTAL
      });
    } else {
      console.log("Steel design analysis: Skipping deflection check - invalid deflection values", {
        maxUpwardDeflection,
        maxDownwardDeflection,
        upwardType: typeof maxUpwardDeflection,
        downwardType: typeof maxDownwardDeflection,
        upwardFinite: isFinite(maxUpwardDeflection),
        downwardFinite: isFinite(maxDownwardDeflection)
      });
    }

    // Convert forces from solver units to steel API expected units
    // Solver units: moments in lb-in, shear in lbs
    // Steel API expects: moments in kip-in, shear in kips
    const momentInKipIn = Math.abs(maxMomentValue) / 1000; // Convert lb-in to kip-in
    const shearInKips = Math.abs(maxShearValue) / 1000;   // Convert lbs to kips
    
    console.log("Steel analysis unit conversion:", {
      maxMomentValue_lbIn: maxMomentValue,
      maxShearValue_lbs: maxShearValue,
      momentInKipIn,
      shearInKips
    });

    const analysisInputs: BeamAnalysisInputs = {
      section,
      material,
      ltbParameters: {
        Lb: unbrancedLength,
        Cb: lateralTorsionalBucklingModifier,
      },
      demandForces: {
        Mu: momentInKipIn, // Convert lb-in to kip-in
        Vu: shearInKips,   // Convert lbs to kips
      },
      designMethod: designMethod as DesignMethod, // Convert string to enum
      applyStiffnessAdjustments: false, // Default to no stiffness adjustments
      deflectionParameters: deflectionParameters,
    };

    // Call the steel analysis directly
    const results = getDesignCheckResults(analysisInputs);

    return {
      designCheckResults: results,
      analysisParameters: {
        unbrancedLength,
        lateralTorsionalBucklingModifier,
        designMethod: designMethod,
        stiffnessAdjustmentsApplied: results.stiffnessAdjustments?.applied || false,
        controllingLoadCombinations: {
          flexural: momentLoadComboName,
          shear: shearLoadComboName,
        },
      },
      success: true,
    };

  } catch (error) {
    console.error('Error performing steel design analysis:', error);
    return {
      designCheckResults: {} as any,
      analysisParameters: {
        unbrancedLength: beamLength,
        lateralTorsionalBucklingModifier: 1.0,
        designMethod: designMethod,
        stiffnessAdjustmentsApplied: false,
        controllingLoadCombinations: {
          flexural: momentLoadComboName || 'Error',
          shear: shearLoadComboName || 'Error',
        },
      },
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error in steel analysis',
    };
  }
};

// --- Main Orchestration Function for Server-Side Calculations ---
export async function performServerSideCalculations(
  beamData: BeamData | null,
  unitSystem: UnitSystem,
  beamPropertiesState: BeamPropertiesState | null
): Promise<AnalysisOutput | null> {
  if (
    !beamData ||
    !beamData.properties ||
    !beamData.supports ||
    !beamData.loadGroups
  ) {
    console.error("performServerSideCalculations: Invalid initial beamData.");
    return null;
  }

  console.log("Beam data before converting:", beamData);
  beamData.loadGroups.forEach((lg) => {
    console.log("Load group:", lg);
  });

  const designMethod = beamData.designMethod || 'ASD';
  const selectedComboNames = beamData.selectedLoadCombos || [];

  // Select the appropriate load combination map based on design method
  const loadComboMapRaw = designMethod === 'LRFD' ? ASCE_7_10_LRFD_ComboMap : ASCE_7_10_ASD_ComboMap;
  
  // Merge standard combinations with custom ones
  const customCombos = (beamData as any).customLoadCombos || {};
  const allAvailableCombos = { ...loadComboMapRaw, ...customCombos };
  
  const loadComboMap = selectedComboNames.length > 0
    ? Object.fromEntries(Object.entries(allAvailableCombos).filter(([name]) => selectedComboNames.includes(name)))
    : allAvailableCombos;
    
  console.log(`Using ${designMethod} load combinations for analysis:`, Object.keys(loadComboMap));

  console.log("performServerSideCalculations: Starting analysis...");

  try {
    if (!beamData) {
      throw new Error("Beam data is null.");
    }
    const solverInputBeamData = convertBeamDataToSolverUnits(
      beamData,
      unitSystem
    );

    const femDefinition = createFemModelDefinition(solverInputBeamData);
    if (!femDefinition) {
      throw new Error("Failed to create FEM model definition from input data.");
    }

    const combinationResultsMap = new Map<
      string,
      SingleCombinationResult | null
    >();
    let hasSuccessfulCombo = false;
    let combinationErrors: string[] = [];

    for (const [comboName, comboFactors] of Object.entries(loadComboMap)) {
      const result = analyzeSingleCombination(
        comboName,
        comboFactors,
        solverInputBeamData,
        femDefinition
      );
      if (result) {
        combinationResultsMap.set(comboName, result);
        if (
          result.diagramData.shear.length > 0 ||
          result.diagramData.moment.length > 0 ||
          result.diagramData.deflection.length > 0
        ) {
          hasSuccessfulCombo = true;
        }
      } else {
        combinationErrors.push(comboName);
      }
    }

    if (!hasSuccessfulCombo) {
      const errorMsg =
        combinationErrors.length > 0
          ? `Analysis failed for combinations: ${combinationErrors.join(
              ", "
            )}. No successful results generated.`
          : "No load combinations generated any results (possibly no relevant loads applied).";
      throw new Error(errorMsg);
    }
    if (combinationErrors.length > 0) {
      console.warn(
        `Analysis completed, but errors occurred for combinations: ${combinationErrors.join(
          ", "
        )}`
      );
    }

    const activeLoadTypes = Array.from(
      new Set(
        solverInputBeamData.loadGroups.flatMap(
          (g) => g.loads?.map((l) => l.loadType) ?? []
        )
      )
    );
    const individualLoadTypeResultsMap = new Map<
      LoadType,
      SingleLoadTypeResult | null
    >();
    let individualLoadErrors: string[] = [];
    for (const loadType of activeLoadTypes) {
      const result = analyzeSingleLoadType(
        loadType,
        solverInputBeamData,
        femDefinition
      );
      individualLoadTypeResultsMap.set(loadType, result);
      if (!result) {
        individualLoadErrors.push(loadType);
      }
    }
    if (individualLoadErrors.length > 0) {
      console.warn(
        `Analysis completed, but errors occurred for individual load types: ${individualLoadErrors.join(
          ", "
        )}`
      );
    }

    const finalResults = await assembleAnalysisOutput(
      combinationResultsMap,
      individualLoadTypeResultsMap,
      activeLoadTypes,
      beamPropertiesState,
      beamData.supports,
      femDefinition.boundaryConditions,
      femDefinition.positionToNodeLabel,
      unitSystem,
      femDefinition.beamLength,
      designMethod
    );

    if (!finalResults) {
      throw new Error("Failed to process combination results.");
    }
    console.log(
      "performServerSideCalculations: Analysis complete. Results processed."
    );
    return finalResults;
  } catch (calcError) {
    const message =
      calcError instanceof Error ? calcError.message : String(calcError);
    console.error(
      "performServerSideCalculations: Overall calculation failed:",
      message,
      calcError
    );
    throw new Error(`Calculation failed: ${message}`);
  }
}
