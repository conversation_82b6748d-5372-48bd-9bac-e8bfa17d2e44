import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

// Utility function to merge Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Design tokens
export const tokens = {
  colors: {
    primary: {
      DEFAULT: "#475569", // Slate Blue
      light: "#64748B",
      dark: "#334155",
    },
    accent: {
      DEFAULT: "#10B981", // Emerald Green
      light: "#34D399",
      dark: "#059669",
    },
    neutral: {
      bg: "#F9FAFB", // Ghost White
      surface: "#FFFFFF",
      border: "#E5E7EB",
    },
    text: {
      primary: "#1F2937", // Soft Charcoal
      secondary: "#6B7280", // Cool <PERSON>
      muted: "#9CA3AF",
    },
    status: {
      success: "#6eb245", // Green for passing checks
      error: "#eb8053",   // Orange for failing checks
      warning: "#F59E0B",
      info: "#3B82F6",
    },
  },
  spacing: {
    xs: "0.25rem", // 4px
    sm: "0.5rem",  // 8px
    md: "0.75rem", // 12px
    lg: "1rem",    // 16px
    xl: "1.5rem",  // 24px
  },
  typography: {
    fontFamily: {
      heading: "Urbanist, sans-serif",
      body: "Inter, sans-serif",
      mono: "Roboto Mono, monospace",
    },
    fontSize: {
      xs: "0.75rem",    // 12px
      sm: "0.875rem",   // 14px
      base: "1rem",     // 16px
      lg: "1.125rem",   // 18px
      xl: "1.25rem",    // 20px
    },
    fontWeight: {
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
    },
  },
  borderRadius: {
    sm: "0.25rem",  // 4px
    md: "0.375rem", // 6px
    lg: "0.5rem",   // 8px
    xl: "0.75rem",  // 12px
  },
  shadows: {
    sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    md: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
    lg: "0 10px 15px -3px rgb(0 0 0 / 0.1)",
  },
  transitions: {
    default: "all 0.2s ease-in-out",
  },
} as const;

// Common component styles
export const styles = {
  // Layout
  container: cn(
    "w-full",
    "bg-neutral-bg"
  ),
  section: cn(
    "space-y-2"
  ),
  grid: {
    base: cn(
      "grid gap-4",
      "grid-cols-1 md:grid-cols-2"
    ),
    dense: cn(
      "grid gap-2",
      "grid-cols-1 md:grid-cols-2"
    ),
  },

  // Typography
  heading: {
    h1: cn(
      "text-2xl font-semibold text-text-primary",
      "font-heading"
    ),
    h2: cn(
      "text-xl font-semibold text-text-primary",
      "font-heading"
    ),
    h3: cn(
      "text-lg font-medium text-text-primary",
      "font-heading"
    ),
    h4: cn(
      "text-base font-medium text-text-primary",
      "font-heading"
    ),
  },

  // Cards
  card: cn(
    "bg-neutral-surface",
    "rounded-lg",
    "shadow-sm",
    "transition-shadow",
    "hover:shadow-md"
  ),
  cardHeader: cn(
    "text-sm font-medium text-text-primary",
    "mb-2",
    "font-heading"
  ),
  cardContent: cn(
    "p-3"
  ),

  // Tables
  table: cn(
    "w-full",
    "divide-y divide-neutral-border"
  ),
  tableHeader: cn(
    "bg-neutral-bg",
    "text-text-secondary text-sm font-medium",
    "px-3 py-2",
    "font-heading"
  ),
  tableCell: cn(
    "text-text-primary text-sm",
    "px-3 py-2",
    "whitespace-nowrap"
  ),
  tableRow: cn(
    "hover:bg-neutral-bg/50",
    "transition-colors"
  ),

  // Forms
  input: cn(
    "w-full",
    "px-3 py-1.5",
    "text-sm",
    "bg-neutral-surface",
    "border border-neutral-border",
    "rounded-md",
    "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary",
    "transition-colors"
  ),
  label: cn(
    "text-sm font-medium text-text-secondary",
    "mb-1",
    "block"
  ),
  select: cn(
    "w-full",
    "px-3 py-1.5",
    "text-sm",
    "bg-neutral-surface",
    "border border-neutral-border",
    "rounded-md",
    "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary",
    "transition-colors"
  ),

  // Status
  status: {
    success: cn(
      "font-medium",
      `text-[#6eb245]` // Green for passing checks
    ),
    error: cn(
      "font-medium", 
      `text-[#eb8053]` // Orange for failing checks
    ),
    warning: cn(
      "text-status-warning",
      "bg-status-warning/10"
    ),
    info: cn(
      "text-status-info",
      "bg-status-info/10"
    ),
  },

  // Interactive elements
  button: {
    base: cn(
      "inline-flex items-center justify-center",
      "px-3 py-1.5",
      "text-sm font-medium",
      "rounded-md",
      "transition-colors",
      "focus:outline-none focus:ring-2 focus:ring-offset-2"
    ),
    primary: cn(
      "bg-primary text-white",
      "hover:bg-primary-dark",
      "focus:ring-primary/50"
    ),
    secondary: cn(
      "bg-neutral-surface text-text-primary",
      "border border-neutral-border",
      "hover:bg-neutral-bg",
      "focus:ring-primary/50"
    ),
  },

  // Tabs
  tabs: cn(
    "grid w-full grid-cols-2",
    "bg-neutral-bg/50",
    "rounded-t-lg",
    "p-1"
  ),
  tabTrigger: cn(
    "w-full",
    "px-3 py-2",
    "text-sm font-medium",
    "rounded-md",
    "transition-colors",
    "text-center",
    // Default state
    "text-text-secondary",
    "hover:text-text-primary hover:bg-neutral-surface",
    // Active state
    "data-[state=active]:text-primary",
    "data-[state=active]:bg-neutral-surface",
    "data-[state=active]:font-semibold",
    "data-[state=active]:border-b-2",
    "data-[state=active]:border-accent"
  ),

  // Tooltips
  tooltip: cn(
    "bg-text-primary text-white",
    "text-sm",
    "px-2 py-1",
    "rounded-md",
    "shadow-lg"
  ),

  // Metrics display
  metric: cn(
    "flex justify-between items-center",
    "py-1"
  ),
  metricLabel: cn(
    "text-text-secondary text-sm"
  ),
  metricValue: cn(
    "text-text-primary text-sm font-mono"
  ),
  metricUnit: cn(
    "text-text-muted text-sm"
  ),
  metricInfo: cn(
    "text-text-muted text-xs"
  ),

  // Text styles
  errorText: cn(
    "text-status-error text-xs mt-1"
  ),
  infoText: cn(
    "text-text-muted text-xs mt-1"
  ),

  // Table styles
  tableCellInfo: cn(
    "text-text-muted"
  ),

  // Empty states
  emptyState: cn(
    "p-4",
    "text-center text-text-secondary",
    "bg-neutral-bg/50",
    "rounded-md"
  ),

  // Loading states
  loadingState: cn(
    "p-4",
    "text-center text-text-secondary",
    "animate-pulse"
  ),
} as const;

// Helper functions for common style combinations
export const getMetricStyle = (status?: "success" | "error" | "warning" | "info") => {
  const baseStyle = styles.metricValue;
  
  if (!status) return baseStyle;
  
  // Use inline styles to ensure colors are applied
  if (status === "success") {
    return cn(baseStyle, "font-medium");
  }
  if (status === "error") {
    return cn(baseStyle, "font-medium"); 
  }
  
  return cn(baseStyle, styles.status[status]);
};

export const getMetricColor = (status?: "success" | "error" | "warning" | "info"): React.CSSProperties => {
  if (status === "success") {
    return { color: "#6eb245" }; // Green for passing checks
  }
  if (status === "error") {
    return { color: "#eb8053" }; // Orange for failing checks
  }
  return {};
};

export const getCardStyle = (variant: "default" | "bordered" = "default") => {
  return variant === "bordered" 
    ? cn(styles.card, "border border-neutral-border")
    : styles.card;
}; 