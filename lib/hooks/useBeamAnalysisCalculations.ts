"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { <PERSON>amD<PERSON>, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import {
  AnalysisOutput
} from "@/lib/types/analysis/analysis-output";
// Remove imports for calculation-specific types/functions now handled by the API
// import { LoadType } from "@/lib/types/load/load-type";
// import { Type as LoadApplicationType } from "@/lib/types/load/load-type";
// import { SupportType } from "@/lib/types/support/support-type";
// import { LinearStaticSolver, DofID, Domain, Beam2D, LoadCase, Node } from "@/lib/beam-2d-fem";
// import { ASCE_7_10_ASD_ComboMap } from "@/lib/types/load/asce-load-combo";
// import { Support } from "@/lib/types/support/support";
// import { Load } from "@/lib/types/load/load";
// import { LoadGroup } from "@/lib/types/load/load-group";
// import { DEFAULT_TOTAL_DEFLECTION_LIMIT, DEFAULT_LIVE_DEFLECTION_LIMIT } from "@/lib/constants/beam-constants";
// import { ...conversionFunctions } from "@/lib/utils/metric-converter";

// Deep comparison function for objects
function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return a === b;
  
  if (typeof a !== typeof b) return false;
  
  if (typeof a !== 'object') return a === b;
  
  if (Array.isArray(a) !== Array.isArray(b)) return false;
  
  if (Array.isArray(a)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false;
    }
    return true;
  }
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
}

// --- Main Hook (Updated to call API) ---
export function useBeamAnalysisCalculations(
  beamData: BeamData,
  unitSystem: UnitSystem,
  beamPropertiesState: BeamPropertiesState | null,
  designMethod: 'ASD' | 'LRFD' = 'ASD'
) {
  const [analysisResults, setAnalysisResults] = useState<AnalysisOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if we have valid beam data for analysis
    if (!beamData?.properties?.length || 
        !beamData?.supports?.length || 
        beamData.supports.length < 2 ||
        !beamData?.loadGroups?.length ||
        !beamData.loadGroups.some(group => group.loads && group.loads.length > 0)) {
      setAnalysisResults(null);
      setError(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    const performAnalysis = async () => {
      try {
        let response;
        
        // Determine which analysis to call based on material type
        const isWoodMaterial = beamPropertiesState?.selectedMaterial === 'wood' && 
                              beamPropertiesState?.lumberType === 'sawn';

        const isGlulamMaterial = beamPropertiesState?.selectedMaterial === 'wood' && 
                                beamPropertiesState?.lumberType === 'glulam';

        if (isWoodMaterial) {
          // Call sawn lumber analysis API for comprehensive NDS analysis
          console.log('Calling sawn lumber analysis API for wood beam');
          console.log(beamPropertiesState);
          response = await fetch('/api/wood/sawn-lumber-analysis', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              beamData,
              unitSystem,
              beamPropertiesState,
              designMethod,
            }),
          });
        } else if (isGlulamMaterial) {
          // Call glulam analysis API for comprehensive NDS analysis
          console.log('Calling glulam analysis API for glulam beam');
          response = await fetch('/api/wood/glulam-analysis', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              beamData,
              unitSystem,
              beamPropertiesState,
              designMethod,
            }),
          });
        } else {
          // Call standard FEM analysis for non-wood materials
          console.log('Calling standard beam analysis API');
          response = await fetch('/api/beam-analysis', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              beamData,
              unitSystem,
              beamPropertiesState,
              designMethod,
            }),
          });
        }

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
          throw new Error(errorData.error || `Analysis failed (status: ${response.status})`);
        }

        const results = await response.json();
        
        if (isWoodMaterial) {
          // For sawn lumber analysis, we need to extract the structural results and add the sawn lumber design
          if (results.success && results.structuralAnalysis) {
            const woodResults: AnalysisOutput = {
              ...results.structuralAnalysis,
              summaryData: {
                ...results.structuralAnalysis.summaryData,
                sawnLumberDesign: {
                  designCheckResults: results,
                  analysisParameters: {
                    designMethod: designMethod,
                    controllingLoadCombinations: {
                      flexural: results.structuralAnalysis.summaryData.maxMomentValue?.loadComboName || 'Unknown',
                      shear: results.structuralAnalysis.summaryData.maxShearValue?.loadComboName || 'Unknown',
                    },
                  },
                  success: true,
                },
              },
            };
            setAnalysisResults(woodResults);
          } else {
            throw new Error(results.error || 'Sawn lumber analysis failed');
          }
        } else if (isGlulamMaterial) {
          // For glulam analysis, we need to extract the structural results and add the glulam design
          if (results.success && results.structuralAnalysis) {
            const glulamResults: AnalysisOutput = {
              ...results.structuralAnalysis,
              summaryData: {
                ...results.structuralAnalysis.summaryData,
                glulamDesign: {
                  designCheckResults: results,
                  analysisParameters: {
                    designMethod: designMethod,
                    primaryAxis: results.summary?.primaryAxis || 'x_axis',
                    layupType: results.summary?.layupType || 'balanced',
                    controllingLoadCombinations: {
                      flexural: results.structuralAnalysis.summaryData.maxMomentValue?.loadComboName || 'Unknown',
                      shear: results.structuralAnalysis.summaryData.maxShearValue?.loadComboName || 'Unknown',
                    },
                  },
                  success: true,
                },
              },
            };
            setAnalysisResults(glulamResults);
          } else {
            throw new Error(results.error || 'Glulam analysis failed');
          }
        } else {
          // For standard analysis, use results directly
          setAnalysisResults(results);
        }

      } catch (err) {
        console.error('Analysis error:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setAnalysisResults(null);
      } finally {
        setIsLoading(false);
      }
    };

    performAnalysis();
  }, [beamData, unitSystem, beamPropertiesState, designMethod]);

  return { analysisResults, isLoading, error };
}
