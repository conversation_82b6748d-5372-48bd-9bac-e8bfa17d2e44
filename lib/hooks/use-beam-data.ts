"use client";

import { useState, useEffect } from "react";
import { BeamData, BeamProperties } from "../types/beam/beam-data";
import { Support } from "../types/support/support";
import { SupportType } from "../types/support/support-type";
import { 
  DEFAULT_BEAM_LENGTH, 
  DEFAULT_ELASTIC_MODULUS, 
  DEFAULT_MOMENT_OF_INERTIA, 
  DEFAULT_AREA
} from "../constants/beam-constants";
import { UnitSystem } from "../types/units/unit-system";
import { convertBeamPropertiesForDisplay } from "../utils/metric-converter";

/**
 * Hook to manage beam data state and unit conversion
 */
export function useBeamData(unitSystem: UnitSystem) {
  const [beamData, setBeamData] = useState<BeamData>(() => ({
    properties: {
      length: DEFAULT_BEAM_LENGTH,
      elasticModulus: DEFAULT_ELASTIC_MODULUS,
      momentOfInertia: DEFAULT_MOMENT_OF_INERTIA,
      area: DEFAULT_AREA,
      tributaryWidth: 0,
    },
    loadGroups: [],
    supports: [
      new Support(SupportType.PIN, 0),
      new Support(SupportType.ROLLER, DEFAULT_BEAM_LENGTH)
    ],
    manual_Fb_allow: 24000,
    manual_Fv_allow: 150,
  }));

  const [previousUnitSystem, setPreviousUnitSystem] = useState<UnitSystem>(unitSystem);

  useEffect(() => {
    if (previousUnitSystem !== unitSystem) {
      const fromUnit = previousUnitSystem;
      const convertedPropertiesSubset = convertBeamPropertiesForDisplay(beamData.properties, fromUnit);
      const newFullProperties = { 
        ...beamData.properties,
        ...convertedPropertiesSubset 
      };
      const lengthRatio = newFullProperties.length / beamData.properties.length;

      // Create a deep copy to avoid modifying the original state directly before setBeamData
      const newData = JSON.parse(JSON.stringify(beamData)); 

      // Update properties
      newData.properties = newFullProperties;

      // Update load group and internal load positions directly on the copied data
      newData.loadGroups.forEach((loadGroup: any) => { // Use 'any' temporarily if LoadGroup type is complex after JSON parse
        loadGroup.startPosition *= lengthRatio;
        if (loadGroup.endPosition !== undefined) {
          loadGroup.endPosition *= lengthRatio;
        }
        loadGroup.loads.forEach((load: any) => {
          if (load.startPosition !== undefined) {
            load.startPosition *= lengthRatio;
          }
          if (load.endPosition !== undefined) {
            load.endPosition *= lengthRatio;
          }
        });
      });

      // Update support positions
      newData.supports = beamData.supports.map(support => new Support(
        support.type,
        support.position * lengthRatio
      ));

      // Now set the state with the modified deep copy
      // We need to cast newData back to BeamData, assuming JSON parse loses class info
      // This assumes LoadGroup/Load methods aren't needed immediately after conversion state set
      setBeamData(newData as BeamData);
      setPreviousUnitSystem(unitSystem);
    }
  }, [unitSystem, previousUnitSystem, beamData]);

  const handlePropertiesChange = (newProperties: BeamProperties) => {
    setBeamData(prev => {
      const lengthRatio = newProperties.length / prev.properties.length;
      
      // Update load group and internal load positions directly
      const updatedLoadGroups = prev.loadGroups.map(loadGroup => {
        // Keep the existing loadGroup instance (class)
        loadGroup.startPosition *= lengthRatio;
        if (loadGroup.endPosition !== undefined) {
          loadGroup.endPosition *= lengthRatio;
        }
        loadGroup.loads.forEach(load => {
          if (load.startPosition !== undefined) {
            load.startPosition *= lengthRatio;
          }
          if (load.endPosition !== undefined) {
            load.endPosition *= lengthRatio;
          }
        });
        return loadGroup; // Return the modified instance
      });

      // Update support positions
      const updatedSupports = prev.supports.map(support => new Support(
        support.type,
        support.position * lengthRatio
      ));

      return {
        ...prev,
        properties: newProperties,
        loadGroups: updatedLoadGroups,
        supports: updatedSupports
      };
    });
  };

  const handleSupportsChange = (newSupports: Support[]) => {
    const sortedSupports = [...newSupports].sort((a, b) => a.position - b.position);
    setBeamData(prev => ({ ...prev, supports: sortedSupports }));
  };

  return {
    beamData,
    setBeamData,
    handlePropertiesChange,
    handleSupportsChange
  };
}