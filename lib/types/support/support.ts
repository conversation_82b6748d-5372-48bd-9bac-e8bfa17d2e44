import { SupportType } from "./support-type";

export class Support {
  type: SupportType;
  position: number;

  constructor(type: SupportType, position: number) {
    if (position < 0) {
      console.warn("Support position cannot be negative. Setting to 0.");
      position = 0;
    }

    this.type = type;
    this.position = position;
  }

  /**
   * Returns true if this support provides horizontal restraint.
   */
  hasHorizontalRestraint(): boolean {
    return this.type === SupportType.PIN || this.type === SupportType.FIXED;
  }

  /**
   * Returns true if this support provides rotational restraint.
   */
  hasRotationalRestraint(): boolean {
    return this.type === SupportType.FIXED;
  }

  /**
   * Returns true if this support provides vertical restraint.
   */
  hasVerticalRestraint(): boolean {
    return true; // All support types provide vertical restraint
  }

  /**
   * Returns true if this support is at the start of the beam.
   */
  isAtStart(beamLength: number): boolean {
    return Math.abs(this.position) < 0.001;
  }

  /**
   * Returns true if this support is at the end of the beam.
   */
  isAtEnd(beamLength: number): boolean {
    return Math.abs(this.position - beamLength) < 0.001;
  }
}