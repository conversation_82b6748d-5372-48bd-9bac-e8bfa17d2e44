import { describe, it, expect, beforeEach } from "@jest/globals";
import { analyze_3d_fem, FEMInput, FEMResult } from "./fem";
import { calculateTransformMatrix, calculateElementStiffness } from "./fem";
import { matrix, multiply, transpose, sqrt } from "mathjs";

xdescribe("analyze_3d_fem Simple Beam Test", () => {
  // --- Shared Setup Data for a 2x10 Wood Beam (DF-L No.1/No.2) ---
  const E_wood = 1600; // ksi (Example for DF-L No.1/No.2)
  const L = 120; // inches (10 ft span)
  // Properties for 2x10 (1.5" x 9.25")
  const b = 1.5; // in
  const d = 9.25; // in
  const A_wood = b * d; // in^2 (13.875)
  const Izz_wood = (b * d ** 3) / 12; // Strong axis inertia, in^4 (~98.93)
  const Iyy_wood = (d * b ** 3) / 12; // Weak axis inertia, in^4 (~2.60)
  const J_wood = 5.6; // Approx Torsional Constant, in^4 (less critical for 2D)
  const Ayy_wood = (5 / 6) * A_wood; // Approx Shear Area y (~11.56)
  const Azz_wood = (5 / 6) * A_wood; // Approx Shear Area z (~11.56)
  const v_wood = 0.3; // Poisson's ratio (typical generic value)

  const tol = 1e-6; // Tolerance for general checks
  const reaction_tol = 1e-3; // Tolerance for force/reaction checks

  let input: FEMInput; // Define input variable in the describe scope

  // --- beforeEach Hook for Common Setup ---
  beforeEach(() => {
    // Initialize the input object before each test
    input = {
      nnodes: 3,
      coord: [
        [0, 0, 0], // Node 1 (Pin)
        [L / 2, 0, 0], // Node 2 (Mid-span)
        [L, 0, 0], // Node 3 (Roller)
      ],
      nele: 2,
      ends: [
        [1, 2],
        [2, 3],
      ],
      fixity: [
        [0, 0, 0, null, null, null], // Node 1: Pin (Fix Txyz)
        [null, null, null, null, null, null], // Node 2: Free
        [null, 0, 0, null, null, null], // Node 3: Roller (Fix Ty, Tz)
      ],
      concen: [ // Default: No concentrated loads
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
      ],
      // Use wood properties
      A: [A_wood, A_wood],
      Izz: [Izz_wood, Izz_wood],
      Iyy: [Iyy_wood, Iyy_wood],
      J: [J_wood, J_wood],
      Ayy: [Ayy_wood, Ayy_wood],
      Azz: [Azz_wood, Azz_wood],
      E: [E_wood, E_wood],
      v: [v_wood, v_wood],
      webdir: [
        [0, 1, 0], // Align local y' with global Y for 2D XY plane analysis
        [0, 1, 0],
      ],
      w: [ // Default: No distributed loads
        [0, 0, 0],
        [0, 0, 0],
      ],
    };
  });

  // --- Test Case: Center Point Load ---
  it("should analyze a simply supported beam with center point load", () => {
    // 1. Define Specific Load for this test
    const P = 10; // kips (High load for testing mechanics)
    input.concen[1] = [0, -P, 0, 0, 0, 0]; // Apply load at Node 2

    // 2. Run Analysis
    const result = analyze_3d_fem(input);

    // 3. Assertions
    expect(result.AFLAG).toBe(1);

    // Deflections
    expect(Math.abs(result.DEFL[0][1])).toBeLessThan(tol); // dy1 ~ 0
    expect(Math.abs(result.DEFL[2][1])).toBeLessThan(tol); // dy3 ~ 0
    const expectedMidDeflection = (P * L ** 3) / (48 * E_wood * Izz_wood); // Use wood E & Izz
    expect(result.DEFL[1][1]).toBeCloseTo(-expectedMidDeflection, 3); // dy2 (negative)

    // Reactions (should be independent of E and I)
    const R1_y = result.REACT[0][1];
    const R3_y = result.REACT[2][1];
    expect(R1_y).toBeCloseTo(P / 2, reaction_tol);
    expect(R3_y).toBeCloseTo(P / 2, reaction_tol);
    expect(R1_y + R3_y).toBeCloseTo(P, reaction_tol);

    // Element Forces (Shear and Moment independent of E and I for statically determinate beam)
    const F1 = result.ELE_FOR[0];
    const F2 = result.ELE_FOR[1];

    // Shear (Fy)
    const V1_start_y = F1[1];
    const V1_end_y = F1[7];
    const V2_start_y = F2[1];
    const V2_end_y = F2[7];
    expect(V1_start_y).toBeCloseTo(P / 2, reaction_tol);
    expect(V1_end_y).toBeCloseTo(P / 2, reaction_tol);
    expect(V2_start_y).toBeCloseTo(-P / 2, reaction_tol);
    expect(V2_end_y).toBeCloseTo(-P / 2, reaction_tol);

    // Moment (Mz)
    const M1_start_z = F1[5];
    const M1_end_z = F1[11];
    const M2_start_z = F2[5];
    const M2_end_z = F2[11];
    expect(Math.abs(M1_start_z)).toBeLessThan(tol * L * L);
    expect(Math.abs(M2_end_z)).toBeLessThan(tol * L * L);
    expect(M1_end_z).toBeCloseTo((P * L) / 4, reaction_tol);
    expect(M2_start_z).toBeCloseTo((P * L) / 4, reaction_tol);
  });

  // --- Test Case: Uniform Load ---
  it("should analyze a simply supported beam with uniform load", () => {
    // 1. Define Specific Load for this test
    const w_load = 0.1; // kips/in (High load for testing mechanics)
    input.w[0] = [0, -w_load, 0];
    input.w[1] = [0, -w_load, 0];

    // 2. Run Analysis
    const result = analyze_3d_fem(input);

    // 3. Assertions
    expect(result.AFLAG).toBe(1);

    // Deflections
    expect(Math.abs(result.DEFL[0][1])).toBeLessThan(tol);
    expect(Math.abs(result.DEFL[2][1])).toBeLessThan(tol);
    const expectedMidDeflection = (5 * w_load * L ** 4) / (384 * E_wood * Izz_wood); // Use wood E & Izz
    expect(result.DEFL[1][1]).toBeCloseTo(-expectedMidDeflection, 3);

    // Reactions (independent of E & I)
    const R1_y = result.REACT[0][1];
    const R3_y = result.REACT[2][1];
    const expectedReaction = (w_load * L) / 2;
    expect(R1_y).toBeCloseTo(expectedReaction, reaction_tol);
    expect(R3_y).toBeCloseTo(expectedReaction, reaction_tol);
    expect(R1_y + R3_y).toBeCloseTo(w_load * L, reaction_tol);

    // Element Forces (independent of E & I)
    const F1 = result.ELE_FOR[0];
    const F2 = result.ELE_FOR[1];

    // Shear (Fy)
    const V1_start_y = F1[1];
    const V1_end_y = F1[7];
    const V2_start_y = F2[1];
    const V2_end_y = F2[7];
    const expectedShearStart = (w_load * L) / 2;
    const expectedShearEnd = -(w_load * L) / 2;
    expect(V1_start_y).toBeCloseTo(expectedShearStart, reaction_tol);
    expect(V1_end_y).toBeCloseTo(0, reaction_tol);
    expect(V2_start_y).toBeCloseTo(0, reaction_tol);
    expect(V2_end_y).toBeCloseTo(expectedShearEnd, reaction_tol);

    // Moment (Mz)
    const M1_start_z = F1[5];
    const M1_end_z = F1[11];
    const M2_start_z = F2[5];
    const M2_end_z = F2[11];
    const expectedMomentMid = (w_load * L ** 2) / 8;
    expect(Math.abs(M1_start_z)).toBeLessThan(tol * L * L);
    expect(Math.abs(M2_end_z)).toBeLessThan(tol * L * L);
    expect(M1_end_z).toBeCloseTo(expectedMomentMid, reaction_tol);
    expect(M2_start_z).toBeCloseTo(expectedMomentMid, reaction_tol);
  });

  // --- Test Case: Two Symmetric Point Loads ---
  it("should analyze a simply supported beam with two symmetric point loads", () => {
    // 1. Define Specific Setup and Load for this test
    const P = 10; // kips per load
    const load_pos_a = L / 3; // Position of first load from left support

    // Redefine nodes and elements for this specific case
    const nnodes_sym = 4;
    const nele_sym = 3;
    const coord_sym = [
      [0, 0, 0], // Node 1 (Pin)
      [load_pos_a, 0, 0], // Node 2 (Load P1)
      [L - load_pos_a, 0, 0], // Node 3 (Load P2)
      [L, 0, 0], // Node 4 (Roller)
    ];
    const ends_sym = [
      [1, 2],
      [2, 3],
      [3, 4],
    ];
    const fixity_sym = [
      [0, 0, 0, null, null, null], // Node 1: Pin
      [null, null, null, null, null, null], // Node 2: Free
      [null, null, null, null, null, null], // Node 3: Free
      [null, 0, 0, null, null, null], // Node 4: Roller
    ];
    const concen_sym = [
      [0, 0, 0, 0, 0, 0],
      [0, -P, 0, 0, 0, 0], // Node 2: Load -P
      [0, -P, 0, 0, 0, 0], // Node 3: Load -P
      [0, 0, 0, 0, 0, 0],
    ];

    // Use a copy of the shared input and modify geometry/loads
    const sym_input: FEMInput = {
      ...input, // Start with shared properties (E, I, A, v etc.)
      nnodes: nnodes_sym,
      nele: nele_sym,
      coord: coord_sym,
      ends: ends_sym,
      fixity: fixity_sym,
      concen: concen_sym,
      // Ensure material properties arrays match new number of elements
      A: Array(nele_sym).fill(A_wood),
      Izz: Array(nele_sym).fill(Izz_wood),
      Iyy: Array(nele_sym).fill(Iyy_wood),
      J: Array(nele_sym).fill(J_wood),
      Ayy: Array(nele_sym).fill(Ayy_wood),
      Azz: Array(nele_sym).fill(Azz_wood),
      E: Array(nele_sym).fill(E_wood),
      v: Array(nele_sym).fill(v_wood),
      webdir: Array(nele_sym).fill([0, 1, 0]),
      w: Array(nele_sym).fill([0, 0, 0]), // No distributed load
    };

    // 2. Run Analysis
    const result = analyze_3d_fem(sym_input);

    // 3. Assertions
    expect(result.AFLAG).toBe(1);

    // Reactions
    const R1_y = result.REACT[0][1]; // Reaction at Node 1
    const R4_y = result.REACT[3][1]; // Reaction at Node 4
    expect(R1_y).toBeCloseTo(P, reaction_tol);
    expect(R4_y).toBeCloseTo(P, reaction_tol);
    expect(R1_y + R4_y).toBeCloseTo(2 * P, reaction_tol); // Sum reactions = total load

    // Element Forces
    const F1 = result.ELE_FOR[0]; // Element 1 (0 to L/3)
    const F2 = result.ELE_FOR[1]; // Element 2 (L/3 to 2L/3)
    const F3 = result.ELE_FOR[2]; // Element 3 (2L/3 to L)

    // Shear (Fy)
    // Segment 1 (0 to L/3): +P
    expect(F1[1]).toBeCloseTo(P, reaction_tol); // V_start_1
    expect(F1[7]).toBeCloseTo(P, reaction_tol); // V_end_1
    // Segment 2 (L/3 to 2L/3): 0
    expect(F2[1]).toBeCloseTo(0, reaction_tol); // V_start_2
    expect(F2[7]).toBeCloseTo(0, reaction_tol); // V_end_2
    // Segment 3 (2L/3 to L): -P
    expect(F3[1]).toBeCloseTo(-P, reaction_tol); // V_start_3
    expect(F3[7]).toBeCloseTo(-P, reaction_tol); // V_end_3

    // Moment (Mz)
    const expectedMaxMoment = P * load_pos_a; // P * (L/3)
    expect(Math.abs(F1[5])).toBeLessThan(tol * L * L); // Mz at Node 1 (start Elem 1) ~ 0
    expect(F1[11]).toBeCloseTo(expectedMaxMoment, reaction_tol); // Mz at Node 2 (end Elem 1)
    expect(F2[5]).toBeCloseTo(expectedMaxMoment, reaction_tol); // Mz at Node 2 (start Elem 2)
    expect(F2[11]).toBeCloseTo(expectedMaxMoment, reaction_tol); // Mz at Node 3 (end Elem 2)
    expect(F3[5]).toBeCloseTo(expectedMaxMoment, reaction_tol); // Mz at Node 3 (start Elem 3)
    expect(Math.abs(F3[11])).toBeLessThan(tol * L * L); // Mz at Node 4 (end Elem 3) ~ 0
  });
});

// --- Test Suite for Helper Functions (Mirroring MATLAB Example) ---
xdescribe("FEM Helper Functions vs MATLAB Example", () => {
  const tol = 1e-4; // Tolerance for floating point comparisons

  it("should match MATLAB results for Gamma, Kele, and Global Stiffness", () => {
    // Input Variables from MATLAB example
    const coordi = [0, 0, 0];
    const coordj = [120, 120, 120];
    const webdir = [-0.7071, 0.7071, 0];
    const A = 10;
    const Izz = 100;
    const Iyy = 20;
    const J = 1;
    const Ayy = 8;
    const Azz = 2;
    const E = 29000;
    const v = 0.3;

    // Calculate Length
    const dx = coordj[0] - coordi[0];
    const dy = coordj[1] - coordi[1];
    const dz = coordj[2] - coordi[2];
    const L = sqrt(dx * dx + dy * dy + dz * dz) as number;

    // Call TypeScript functions
    const Gamma = calculateTransformMatrix(coordi, coordj, webdir);
    const Kele = calculateElementStiffness(A, Izz, Iyy, J, Ayy, Azz, E, v, L);

    // Calculate Global Element Stiffness
    const Gamma_T = transpose(Gamma);
    const Global = multiply(multiply(Gamma_T, Kele), Gamma);

    // --- Assertions --- 
    // Verify dimensions
    expect(Gamma.size()).toEqual([12, 12]);
    expect(Kele.size()).toEqual([12, 12]);
    expect(Global.size()).toEqual([12, 12]);

    // Define expected Gamma matrix from MATLAB output
    const expectedGammaData = [
      [ 5.7735e-01,  5.7735e-01,  5.7735e-01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 ],
      [-7.0710e-01,  7.0710e-01,         0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 ],
      [-4.0824e-01, -4.0824e-01,  8.1649e-01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0, 5.7735e-01,  5.7735e-01,  5.7735e-01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0,-7.0710e-01,  7.0710e-01,         0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0,-4.0824e-01, -4.0824e-01,  8.1649e-01, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0, 0.0, 0.0, 0.0, 5.7735e-01,  5.7735e-01,  5.7735e-01, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0, 0.0, 0.0, 0.0,-7.0710e-01,  7.0710e-01,         0.0, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0, 0.0, 0.0, 0.0,-4.0824e-01, -4.0824e-01,  8.1649e-01, 0.0, 0.0, 0.0 ],
      [        0.0,         0.0,         0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.7735e-01,  5.7735e-01,  5.7735e-01 ],
      [        0.0,         0.0,         0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,-7.0710e-01,  7.0710e-01,         0.0 ],
      [        0.0,         0.0,         0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,-4.0824e-01, -4.0824e-01,  8.1649e-01 ],
    ];
    const expectedGammaMatrix = matrix(expectedGammaData);

    // Compare Gamma
    console.log("Calculated Gamma", Gamma);
    console.log("Expected Gamma", expectedGammaMatrix);
    Gamma.forEach((value, index) => {
      expect(value).toBeCloseTo(expectedGammaMatrix.get(index), 4); // Use precision 4 for Gamma
    });

    // Define expected Kele matrix from MATLAB output
    const expectedKeleData = [
      [ 1.3953e+03,        0.0,        0.0,        0.0,        0.0,        0.0, -1.3953e+03,        0.0,        0.0,        0.0,        0.0,        0.0 ],
      [        0.0, 3.8411e+00,        0.0,        0.0,        0.0, 3.9917e+02,        0.0, -3.8411e+00,        0.0,        0.0,        0.0, 3.9917e+02 ],
      [        0.0,        0.0, 7.6959e-01,        0.0, -7.9978e+01,        0.0,        0.0,        0.0, -7.6959e-01,        0.0, -7.9978e+01,        0.0 ],
      [        0.0,        0.0,        0.0, 5.3664e+01,        0.0,        0.0,        0.0,        0.0,        0.0, -5.3664e+01,        0.0,        0.0 ],
      [        0.0,        0.0, -7.9978e+01,        0.0, 1.1102e+04,        0.0,        0.0,        0.0, 7.9978e+01,        0.0, 5.5210e+03,        0.0 ],
      [        0.0, 3.9917e+02,        0.0,        0.0,        0.0, 5.5436e+04,        0.0, -3.9917e+02,        0.0,        0.0,        0.0, 2.7531e+04 ],
      [-1.3953e+03,        0.0,        0.0,        0.0,        0.0,        0.0, 1.3953e+03,        0.0,        0.0,        0.0,        0.0,        0.0 ],
      [        0.0, -3.8411e+00,        0.0,        0.0,        0.0, -3.9917e+02,        0.0, 3.8411e+00,        0.0,        0.0,        0.0, -3.9917e+02 ],
      [        0.0,        0.0, -7.6959e-01,        0.0, 7.9978e+01,        0.0,        0.0,        0.0, 7.6959e-01,        0.0, 7.9978e+01,        0.0 ],
      [        0.0,        0.0,        0.0, -5.3664e+01,        0.0,        0.0,        0.0,        0.0,        0.0, 5.3664e+01,        0.0,        0.0 ],
      [        0.0,        0.0, -7.9978e+01,        0.0, 5.5210e+03,        0.0,        0.0,        0.0, 7.9978e+01,        0.0, 1.1062e+04,        0.0 ],
      [        0.0, 3.9917e+02,        0.0,        0.0,        0.0, 2.7531e+04,        0.0, -3.9917e+02,        0.0,        0.0,        0.0, 5.5436e+04 ]
    ];
    const expectedKeleMatrix = matrix(expectedKeleData);

    // Compare Kele
    console.log("Calculated Kele", Kele);
    console.log("Expected Kele", expectedKeleMatrix);
    Kele.forEach((value, index) => {
      // console.log(`index: ${index}, value: ${value}, expected: ${expectedKeleMatrix.get(index)}`);
      expect(value).toBeCloseTo(expectedKeleMatrix.get(index), 0); // Use precision 1 for Kele
    });

    console.log("Global", Global);

    // Define the expected Global matrix from MATLAB output
    const expectedGlobalData = [
      [ 4.6714e+02,  4.6330e+02,  4.6483e+02,  9.2142e+01,  1.3832e+02, -2.3046e+02, -4.6714e+02, -4.6330e+02, -4.6483e+02,  9.2142e+01,  1.3832e+02, -2.3046e+02 ],
      [ 4.6330e+02,  4.6714e+02,  4.6483e+02, -1.3832e+02, -9.2142e+01,  2.3046e+02, -4.6330e+02, -4.6714e+02, -4.6483e+02, -1.3832e+02, -9.2142e+01,  2.3046e+02 ],
      [ 4.6483e+02,  4.6483e+02,  4.6560e+02,  4.6174e+01, -4.6174e+01,         0.0, -4.6483e+02, -4.6483e+02, -4.6560e+02,  4.6174e+01, -4.6174e+01,         0.0 ],
      [ 9.2142e+01, -1.3832e+02,  4.6174e+01,  1.4808e+04,  3.7061e+03, -1.8460e+04, -9.2142e+01,  1.3832e+02, -4.6174e+01,  7.3309e+03,  1.8100e+03, -9.1946e+03 ],
      [ 1.3832e+02, -9.2142e+01, -4.6174e+01,  3.7061e+03,  1.4808e+04, -1.8460e+04, -1.3832e+02,  9.2142e+01,  4.6174e+01,  1.8100e+03,  7.3309e+03, -9.1946e+03 ],
      [-2.3046e+02,  2.3046e+02,         0.0, -1.8460e+04, -1.8460e+04,  3.6975e+04,  2.3046e+02, -2.3046e+02,         0.0, -9.1946e+03, -9.1946e+03,  1.8336e+04 ],
      [-4.6714e+02, -4.6330e+02, -4.6483e+02, -9.2142e+01, -1.3832e+02,  2.3046e+02,  4.6714e+02,  4.6330e+02,  4.6483e+02, -9.2142e+01, -1.3832e+02,  2.3046e+02 ],
      [-4.6330e+02, -4.6714e+02, -4.6483e+02,  1.3832e+02,  9.2142e+01, -2.3046e+02,  4.6330e+02,  4.6714e+02,  4.6483e+02,  1.3832e+02,  9.2142e+01, -2.3046e+02 ],
      [-4.6483e+02, -4.6483e+02, -4.6560e+02, -4.6174e+01,  4.6174e+01,         0.0,  4.6483e+02,  4.6483e+02,  4.6560e+02, -4.6174e+01,  4.6174e+01,         0.0 ],
      [ 9.2142e+01, -1.3832e+02,  4.6174e+01,  7.3309e+03,  1.8100e+03, -9.1946e+03, -9.2142e+01,  1.3832e+02, -4.6174e+01,  1.4788e+04,  3.7261e+03, -1.8460e+04 ],
      [ 1.3832e+02, -9.2142e+01, -4.6174e+01,  1.8100e+03,  7.3309e+03, -9.1946e+03, -1.3832e+02,  9.2142e+01,  4.6174e+01,  3.7261e+03,  1.4788e+04, -1.8460e+04 ],
      [-2.3046e+02,  2.3046e+02,         0.0, -9.1946e+03, -9.1946e+03,  1.8336e+04,  2.3046e+02, -2.3046e+02,         0.0, -1.8460e+04, -1.8460e+04,  3.6975e+04 ]
    ];
    const expectedGlobalMatrix = matrix(expectedGlobalData);

    // Compare each element
    Global.forEach((value, index) => {
      console.log(`index: ${index}, value: ${value}, expected: ${expectedGlobalMatrix.get(index)}`);
      expect(value).toBeCloseTo(expectedGlobalMatrix.get(index), -1); // Increase tolerance (precision -1 allows diff < 5)
    });
  });
});
