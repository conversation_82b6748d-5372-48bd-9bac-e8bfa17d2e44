/**
 * BeamModel represents a 2D beam with spans, supports, and loads.
 * It computes deflection, moment, and shear force along the beam using Hermite shape functions,
 * and calculates summary results like max stress ratios, deflections, and reactions.
 */

import { Span } from "@/lib/types/span/span";
import { Support } from "@/lib/types/support/support";
import { SupportType } from "@/lib/types/support/support-type";
import { Node } from "./node";
import { LoadGroup } from "@/lib/types/load/load-group";
import { LoadType } from "@/lib/types/load/load-type";
import { Type } from "@/lib/types/load/load";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import {
  getEquivalentNodalLoadForElement,
  getConstrainedDOFs,
} from "./beam-util";
import { FEMInput, FEMResult, analyze_3d_fem } from "./fem";

// --- Result Types (moved from beam-summary) ---
export interface MaxResult {
  value: number;
  position: number;
  spanNumber: number;
  loadComboName: string;
}

export interface MaxStressRatioResult {
  maxBendingRatio: MaxResult;
  maxShearRatio: MaxResult;
}

export interface MaxDeflectionResult {
  maxAbsDeflection: MaxResult;
  maxDeflectionRatio: MaxResult;
}

export interface SupportReactionMap {
  [supportIndex: number]: {
    [loadType in LoadType]?: number;
  };
}

export interface SupportMomentReactionMap {
  [supportIndex: number]: {
    [loadType in LoadType]?: number;
  };
}

export class BeamModel {
  spans: Span[];
  supports: Support[];
  loadGroups: LoadGroup[];
  nodes: Node[];
  displacements: number[] = [];
  private K_original: number[][] = [];
  private constrainedDOFs: number[] = [];
  private dofCount: number;
  
  // FEM analysis related properties
  private femInput: FEMInput | null = null;
  private femResult: FEMResult | null = null;
  private useFEM: boolean = true; // Flag to control whether to use FEM analysis

  /**
   * Creates a BeamModel instance.
   * @param spans - Array of beam spans.
   * @param supports - Array of beam supports.
   * @param loadGroups - Array of load groups.
   */
  constructor(
    spans: Span[] = [],
    supports: Support[] = [],
    loadGroups: LoadGroup[] = []
  ) {
    this.spans = spans;
    this.supports = supports;
    this.loadGroups = loadGroups;
    this.nodes = [];
    this.dofCount = 0;
  }

  /**
   * Computes vertical deflection at global position `x`. Requires analyzeCombination/analyzeLoadType first.
   */
  deflectionAt(x: number): number {
    if (this.displacements.length === 0) {
      console.warn(
        "deflectionAt called before analysis. Returning 0. Run analyze first for results."
      );
      return 0;
    }
    if (this.spans.length === 0) return 0; // No spans, no deflection

    const epsilon = 1e-9;
    
    // Find the span containing x
    const span = this.spans.find(
      (s) => x >= s.start - epsilon && x <= s.end + epsilon
    );

    if (!span) {
      // If x is outside the bounds of all spans
      const minX = Math.min(...this.spans.map((s) => s.start));
      const maxX = Math.max(...this.spans.map((s) => s.end));
      if (x < minX - epsilon || x > maxX + epsilon) {
        console.warn(
          `Position x = ${x} is outside the beam range [${minX}, ${maxX}]. Returning 0 deflection.`
        );
        return 0;
      } else {
        throw new Error(
          `No span found containing position x = ${x}, although it appears within beam bounds.`
        );
      }
    }
    
    if (this.useFEM && this.femResult) {
      // Use FEM results for deflection calculation
      
      // Find the nodes that bound the position x
      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      const endNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.end) < epsilon
      );
      
      if (startNodeIndex === -1 || endNodeIndex === -1) {
        throw new Error(
          `Could not find nodes for span [${span.start}, ${span.end}] during deflection calculation.`
        );
      }
      
      // If x is exactly at a node, return that node's displacement
      const exactNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - x) < epsilon
      );
      if (exactNodeIndex !== -1) {
        return this.femResult.DEFL[exactNodeIndex][1]; // Y displacement
      }
      
      // Otherwise, interpolate between the two nodes
      const L = span.length;
      if (L < epsilon) {
        // For zero-length span, return the node's displacement
        return this.femResult.DEFL[startNodeIndex][1];
      }
      
      // Linear interpolation between nodes
      const Xi = (x - span.start) / L;
      const startDisp = this.femResult.DEFL[startNodeIndex][1];
      const endDisp = this.femResult.DEFL[endNodeIndex][1];
      
      // For more accuracy, use cubic Hermite interpolation with rotations
      const startRot = this.femResult.DEFL[startNodeIndex][5]; // Z rotation
      const endRot = this.femResult.DEFL[endNodeIndex][5]; // Z rotation
      
      // Hermite shape functions
      const Xi2 = Xi * Xi;
      const Xi3 = Xi2 * Xi;
      const H1 = 1 - 3 * Xi2 + 2 * Xi3;
      const H2 = L * (Xi - 2 * Xi2 + Xi3);
      const H3 = 3 * Xi2 - 2 * Xi3;
      const H4 = L * (-Xi2 + Xi3);
      
      const deflection = H1 * startDisp + H2 * startRot + H3 * endDisp + H4 * endRot;
      
      if (!isFinite(deflection)) {
        console.error("Non-finite deflection calculated from FEM results", {
          x, Xi, startDisp, startRot, endDisp, endRot
        });
        throw new Error("Numerical instability in FEM deflection calculation");
      }
      
      return deflection;
    } else {
      // Use original Hermite shape function method
      
      // Check for valid span length
      const L = span.length;
      if (L < epsilon) {
        // For zero-length span, deflection is likely the nodal displacement
        const startNodeIndex = this.nodes.findIndex(
          (n) => Math.abs(n.x - span.start) < epsilon
        );
        if (startNodeIndex !== -1) return this.displacements[startNodeIndex * 2];
        console.error(
          "Could not find node for zero-length span deflection calculation."
        );
        return 0;
      }

      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      const endNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.end) < epsilon
      );

      if (startNodeIndex === -1 || endNodeIndex === -1) {
        throw new Error(
          `Could not find nodes for span [${span.start}, ${span.end}] during deflection calculation.`
        );
      }

      const u1_idx = startNodeIndex * 2;
      const t1_idx = startNodeIndex * 2 + 1;
      const u2_idx = endNodeIndex * 2;
      const t2_idx = endNodeIndex * 2 + 1;

      // Check displacement array bounds
      if (t2_idx >= this.displacements.length) {
        throw new Error(
          `DOF index out of bounds accessing displacements array (length ${this.displacements.length}, needed ${t2_idx})`
        );
      }

      const u1 = this.displacements[u1_idx];
      const theta1 = this.displacements[t1_idx];
      const u2 = this.displacements[u2_idx];
      const theta2 = this.displacements[t2_idx];

      // Normalize coordinate Xi = (x - start) / L
      const Xi = (x - span.start) / L;
      // Clamp Xi to [0, 1] to avoid extrapolation issues due to floating point errors near ends
      const Xi_clamped = Math.max(0, Math.min(1, Xi));
      const Xi2 = Xi_clamped * Xi_clamped;
      const Xi3 = Xi2 * Xi_clamped;

      // Hermite Shape Functions N(Xi)
      const N1 = 1 - 3 * Xi2 + 2 * Xi3;
      const N2 = L * (Xi_clamped - 2 * Xi2 + Xi3);
      const N3 = 3 * Xi2 - 2 * Xi3;
      const N4 = L * (-Xi2 + Xi3);

      const deflection = N1 * u1 + N2 * theta1 + N3 * u2 + N4 * theta2;
      if (!isFinite(deflection)) {
        console.error("Non-finite deflection calculated", {
          x, Xi, u1, theta1, u2, theta2, N1, N2, N3, N4,
        });
        throw new Error(
          "Numerical instability resulted in non-finite deflection."
        );
      }
      return deflection;
    }
  }

  /**
   * Computes internal bending moment M(x). Requires analyzeCombination/analyzeLoadType first.
   */
  momentAt(x: number): number {
    if (this.displacements.length === 0) {
      console.warn("momentAt called before analysis. Returning 0.");
      return 0;
    }
    if (this.spans.length === 0) return 0; // No spans, no moment

    const epsilon = 1e-9;
    const span = this.spans.find(
      (s) => x >= s.start - epsilon && x <= s.end + epsilon
    );
    if (!span) {
      const minX = Math.min(...this.spans.map((s) => s.start));
      const maxX = Math.max(...this.spans.map((s) => s.end));
      if (x < minX - epsilon || x > maxX + epsilon) {
        console.warn(
          `Position x = ${x} is outside the beam range [${minX}, ${maxX}]. Returning 0 moment.`
        );
        return 0;
      } else {
        throw new Error(
          `No span found containing position x = ${x} for moment calculation.`
        );
      }
    }

    if (
      typeof span.E !== "number" ||
      typeof span.I !== "number" ||
      typeof span.length !== "number"
    ) {
      throw new Error(
        `Invalid span properties (E, I, length) for moment calculation at span [${span.start}, ${span.end}]`
      );
    }

    const L = span.length;
    const E = span.E;
    const I = span.I;

    if (L < epsilon || E * I === 0) {
      return 0; // No moment in zero-length or zero-stiffness element
    }
    
    if (this.useFEM && this.femResult) {
      // Use FEM results for moment calculation
      
      // Find the element index for this span
      const spanIndex = this.spans.indexOf(span);
      if (spanIndex === -1) {
        throw new Error(`Could not find span index for span [${span.start}, ${span.end}]`);
      }
      
      // Find the nodes that bound the position x
      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      const endNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.end) < epsilon
      );
      
      if (startNodeIndex === -1 || endNodeIndex === -1) {
        throw new Error(
          `Could not find nodes for span [${span.start}, ${span.end}] during moment calculation.`
        );
      }
      
      // If x is exactly at a node, return that node's moment
      const exactNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - x) < epsilon
      );
      
      if (exactNodeIndex !== -1) {
        // For start node of element, use the end moment (index 10)
        // For end node of element, use the start moment (index 4)
        if (exactNodeIndex === startNodeIndex) {
          return this.femResult.ELE_FOR[spanIndex][4]; // My1 (start node moment)
        } else if (exactNodeIndex === endNodeIndex) {
          return this.femResult.ELE_FOR[spanIndex][10]; // My2 (end node moment)
        }
      }
      
      // For a point within the element, interpolate the moment
      // In a beam element with distributed load, moment varies linearly between nodes
      const Xi = (x - span.start) / L;
      
      // Get moments at the element ends
      const startMoment = this.femResult.ELE_FOR[spanIndex][4]; // My1
      const endMoment = this.femResult.ELE_FOR[spanIndex][10]; // My2
      
      // Linear interpolation for moment
      const moment = startMoment * (1 - Xi) + endMoment * Xi;
      
      if (!isFinite(moment)) {
        console.error("Non-finite moment calculated from FEM results", {
          x, Xi, startMoment, endMoment
        });
        throw new Error("Numerical instability in FEM moment calculation");
      }
      
      return moment;
    } else {
      // Use original method based on shape functions
      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      const endNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.end) < epsilon
      );

      if (startNodeIndex === -1 || endNodeIndex === -1) {
        throw new Error(
          `Could not find nodes for span [${span.start}, ${span.end}] during moment calculation.`
        );
      }

      const u1_idx = startNodeIndex * 2;
      const t1_idx = startNodeIndex * 2 + 1;
      const u2_idx = endNodeIndex * 2;
      const t2_idx = endNodeIndex * 2 + 1;

      if (t2_idx >= this.displacements.length) {
        throw new Error(
          `DOF index out of bounds accessing displacements array (length ${this.displacements.length}, needed ${t2_idx})`
        );
      }

      const u1 = this.displacements[u1_idx];
      const theta1 = this.displacements[t1_idx];
      const u2 = this.displacements[u2_idx];
      const theta2 = this.displacements[t2_idx];

      const Xi = (x - span.start) / L;
      const Xi_clamped = Math.max(0, Math.min(1, Xi)); // Use clamped coordinate
      const L2 = L * L;

      // Second derivatives of shape functions w.r.t x: d²N/dx² = (1/L²) * d²N/dXi²
      const d2N1_dx2 = (12 * Xi_clamped - 6) / L2;
      const d2N2_dx2 = (6 * Xi_clamped - 4) / L;
      const d2N3_dx2 = (6 - 12 * Xi_clamped) / L2;
      const d2N4_dx2 = (6 * Xi_clamped - 2) / L;

      // Second derivative of deflection w.r.t x: d²w/dx²
      const d2w_dx2 =
        d2N1_dx2 * u1 + d2N2_dx2 * theta1 + d2N3_dx2 * u2 + d2N4_dx2 * theta2;

      // Moment M = -EI * d²w/dx²
      const moment = -E * I * d2w_dx2;

      if (!isFinite(moment)) {
        console.error("Non-finite moment calculated", {
          x, Xi, u1, theta1, u2, theta2, d2N1_dx2, d2N2_dx2, d2N3_dx2, d2N4_dx2, d2w_dx2,
        });
        throw new Error("Numerical instability resulted in non-finite moment.");
      }
      return moment;
    }
  }

  /**
   * Computes internal shear force V(x). Requires analyzeCombination/analyzeLoadType first.
   */
  shearAt(x: number): number {
    if (this.displacements.length === 0) {
      console.warn("shearAt called before analysis. Returning 0.");
      return 0;
    }
    if (this.spans.length === 0) return 0; // No spans, no shear

    const epsilon = 1e-9;
    const span = this.spans.find(
      (s) => x >= s.start - epsilon && x <= s.end + epsilon
    );
    if (!span) {
      const minX = Math.min(...this.spans.map((s) => s.start));
      const maxX = Math.max(...this.spans.map((s) => s.end));
      if (x < minX - epsilon || x > maxX + epsilon) {
        console.warn(
          `Position x = ${x} is outside the beam range [${minX}, ${maxX}]. Returning 0 shear.`
        );
        return 0;
      } else {
        throw new Error(
          `No span found containing position x = ${x} for shear calculation.`
        );
      }
    }

    if (
      typeof span.E !== "number" ||
      typeof span.I !== "number" ||
      typeof span.length !== "number"
    ) {
      throw new Error(
        `Invalid span properties (E, I, length) for shear calculation at span [${span.start}, ${span.end}]`
      );
    }

    const L = span.length;
    const E = span.E;
    const I = span.I;

    if (L < epsilon || E * I === 0) {
      return 0; // No shear in zero-length or zero-stiffness element
    }
    
    if (this.useFEM && this.femResult) {
      // Use FEM results for shear calculation
      
      // Find the element index for this span
      const spanIndex = this.spans.indexOf(span);
      if (spanIndex === -1) {
        throw new Error(`Could not find span index for span [${span.start}, ${span.end}]`);
      }
      
      // Find the nodes that bound the position x
      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      const endNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.end) < epsilon
      );
      
      if (startNodeIndex === -1 || endNodeIndex === -1) {
        throw new Error(
          `Could not find nodes for span [${span.start}, ${span.end}] during shear calculation.`
        );
      }
      
      // If x is exactly at a node, return that node's shear
      const exactNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - x) < epsilon
      );
      
      if (exactNodeIndex !== -1) {
        // For start node of element, use the start shear (index 1)
        // For end node of element, use the end shear (index 7)
        if (exactNodeIndex === startNodeIndex) {
          return this.femResult.ELE_FOR[spanIndex][1]; // Fy1 (start node shear)
        } else if (exactNodeIndex === endNodeIndex) {
          return this.femResult.ELE_FOR[spanIndex][7]; // Fy2 (end node shear)
        }
      }
      
      // For a point within the element, calculate the shear
      // For a beam with uniform distributed load, shear varies linearly
      const Xi = (x - span.start) / L;
      
      // Get shears at the element ends
      const startShear = this.femResult.ELE_FOR[spanIndex][1]; // Fy1
      const endShear = this.femResult.ELE_FOR[spanIndex][7]; // Fy2
      
      // Linear interpolation for shear
      const shear = startShear * (1 - Xi) + endShear * Xi;
      
      if (!isFinite(shear)) {
        console.error("Non-finite shear calculated from FEM results", {
          x, Xi, startShear, endShear
        });
        throw new Error("Numerical instability in FEM shear calculation");
      }
      
      return shear;
    } else {
      // Use original method based on shape functions
      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      const endNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.end) < epsilon
      );

      if (startNodeIndex === -1 || endNodeIndex === -1) {
        throw new Error(
          `Could not find nodes for span [${span.start}, ${span.end}] during shear calculation.`
        );
      }

      const u1_idx = startNodeIndex * 2;
      const t1_idx = startNodeIndex * 2 + 1;
      const u2_idx = endNodeIndex * 2;
      const t2_idx = endNodeIndex * 2 + 1;

      if (t2_idx >= this.displacements.length) {
        throw new Error(
          `DOF index out of bounds accessing displacements array (length ${this.displacements.length}, needed ${t2_idx})`
        );
      }

      const u1 = this.displacements[u1_idx];
      const theta1 = this.displacements[t1_idx];
      const u2 = this.displacements[u2_idx];
      const theta2 = this.displacements[t2_idx];

      // Xi does not affect the third derivative result, but keep for consistency if needed elsewhere
      // const Xi = (x - span.start) / L;
      const L2 = L * L;
      const L3 = L2 * L;

      // Third derivatives of shape functions w.r.t x: d³N/dx³ = (1/L³) * d³N/dXi³
      const d3N1_dx3 = 12 / L3;
      const d3N2_dx3 = 6 / L2;
      const d3N3_dx3 = -12 / L3;
      const d3N4_dx3 = 6 / L2;

      // Third derivative of deflection w.r.t x: d³w/dx³
      const d3w_dx3 =
        d3N1_dx3 * u1 + d3N2_dx3 * theta1 + d3N3_dx3 * u2 + d3N4_dx3 * theta2;

      // Shear V = -EI * d³w/dx³
      const shear = -E * I * d3w_dx3;

      if (!isFinite(shear)) {
        console.error("Non-finite shear calculated", {
          x, u1, theta1, u2, theta2, d3N1_dx3, d3N2_dx3, d3N3_dx3, d3N4_dx3, d3w_dx3,
        });
        throw new Error("Numerical instability resulted in non-finite shear.");
      }
      return shear;
    }
  }
}
