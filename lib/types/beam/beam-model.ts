/**
 * BeamModel performs finite element analysis (FEA) on a beam composed of multiple spans.
 * It computes deflection, moment, and shear force along the beam using Hermite shape functions,
 * and calculates summary results like max stress ratios, deflections, and reactions.
 */

import { Span } from "@/lib/types/span/span";
import { Support } from "@/lib/types/support/support";
import { Node } from "./node";
import { LoadGroup } from "@/lib/types/load/load-group";
import { LoadType } from "@/lib/types/load/load-type";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import {
  getEquivalentNodalLoadForElement,
  getConstrainedDOFs,
} from "./beam-util";

// --- Result Types (moved from beam-summary) ---
export interface MaxResult {
  value: number;
  position: number;
  spanNumber: number; // 1-based index
  loadComboName: string;
}

export interface MaxStressRatioResult {
  bending: MaxResult;
  shear: MaxResult;
}

// Simplified Deflection Result focusing on total deflection per combo first
export interface MaxDeflectionResult {
  maxDownward: { value: number; ratio: number; loadComboName: string };
  maxUpward: { value: number; ratio: number; loadComboName: string };
}

// Type for reactions per support per load type
export interface SupportReactionMap {
  [supportIndex: number]: {
    [loadType in LoadType]?: number; // Use optional as not all types might have loads
  };
}

// New type for moment reactions
export interface SupportMomentReactionMap {
  [supportIndex: number]: {
    // 0-based index
    [loadType in LoadType]?: number; // Moment reaction
  };
}
// --- End Result Types ---

export class BeamModel {
  spans: Span[];
  supports: Support[];
  loadGroups: LoadGroup[];
  nodes: Node[];
  displacements: number[] = [];
  private K_original: number[][] = [];
  private constrainedDOFs: number[] = [];
  private dofCount: number;

  /**
   * Creates a BeamModel instance.
   * @param spans - Array of Span instances defining beam segments
   * @param supports - Array of Support instances defining boundary conditions
   * @param loadGroups - Array of LoadGroup instances containing applied loads
   */
  constructor(spans: Span[], supports: Support[], loadGroups: LoadGroup[]) {
    // Input validation
    if (!spans || spans.length === 0) {
      // If no spans, maybe initialize with a single default span or handle appropriately?
      // For now, assume valid spans are provided or handled upstream.
      console.warn("BeamModel created with no spans.");
      // Set default empty state to avoid errors later
      this.spans = [];
      this.supports = supports || [];
      this.loadGroups = loadGroups || [];
      this.nodes = [];
      this.K_original = [];
      this.constrainedDOFs = [];
      this.dofCount = 0;
      return; // Exit constructor early
    }
    if (!supports || supports.length === 0) {
      // A beam needs supports to be stable for analysis
      // Throw an error or handle based on application requirements
      console.warn("BeamModel created with no supports. Analysis might fail.");
      // Allow creation but analysis methods might throw errors
    }

    this.spans = spans;
    this.supports = supports;
    this.loadGroups = loadGroups || []; // Ensure loadGroups is an array

    try {
      this.nodes = this.generateNodesFromSpans();
      this.dofCount = this.nodes.length * 2;
      this.K_original = this.assembleGlobalStiffness(); // Assemble K once
      this.constrainedDOFs = getConstrainedDOFs(this.supports, this.nodes); // Get constrained DOFs once
    } catch (error) {
      console.error(
        "Error during BeamModel initialization (nodes/stiffness/constraints):",
        error
      );
      // Re-throw or handle error state
      throw new Error(
        `BeamModel Initialization failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Builds a sorted list of unique x-positions representing nodes from the spans.
   */
  private generateNodesFromSpans(): Node[] {
    const nodeSet = new Set<number>();
    if (!this.spans) return [];
    for (const span of this.spans) {
      // Basic check for valid span start/end
      if (typeof span.start !== "number" || typeof span.end !== "number") {
        throw new Error(
          `Invalid span definition: start/end must be numbers. Found: ${JSON.stringify(
            span
          )}`
        );
      }
      nodeSet.add(span.start);
      nodeSet.add(span.end);
    }
    if (nodeSet.size === 0) {
      return []; // Handle case of no valid spans resulting in nodes
    }
    return Array.from(nodeSet)
      .sort((a, b) => a - b)
      .map((x) => ({ x }));
  }

  /**
   * Returns the global degree-of-freedom (DOF) indices for a given span.
   */
  private getDOFMapForSpan(span: Span): number[] {
    const epsilon = 1e-9; // Tolerance for floating point comparison
    const startNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.start) < epsilon
    );
    const endNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.end) < epsilon
    );

    if (startNodeIndex === -1) {
      throw new Error(
        `Could not find start node for span [${span.start}, ${
          span.end
        }] in node list [${this.nodes.map((n) => n.x).join(", ")}]`
      );
    }
    if (endNodeIndex === -1) {
      throw new Error(
        `Could not find end node for span [${span.start}, ${
          span.end
        }] in node list [${this.nodes.map((n) => n.x).join(", ")}]`
      );
    }

    return [
      startNodeIndex * 2,
      startNodeIndex * 2 + 1,
      endNodeIndex * 2,
      endNodeIndex * 2 + 1,
    ];
  }

  /**
   * Computes the 4×4 local stiffness matrix for a beam element.
   */
  private createLocalStiffnessMatrix(
    E: number,
    I: number,
    L: number
  ): number[][] {
    if (L < 1e-9) {
      // Handle zero-length span (e.g., coincident nodes)
      // Return zero matrix or handle as rigid link depending on intent
      console.warn(
        `Creating stiffness matrix for zero-length span at start=${
          /* Need span info */ 0
        }`
      );
      return Array.from({ length: 4 }, () => Array(4).fill(0));
    }
    if (
      E === undefined ||
      I === undefined ||
      L === undefined ||
      E < 0 ||
      I < 0 ||
      L <= 0
    ) {
      throw new Error(
        `Invalid properties for stiffness matrix calculation: E=${E}, I=${I}, L=${L}`
      );
    }

    const L2 = L * L;
    const L3 = L2 * L;
    const EI = E * I;

    // Check for potential division by zero although handled by L check above
    if (L3 < 1e-12) {
      console.warn(
        `Near zero length (L^3 = ${L3}) in stiffness matrix calculation.`
      );
      return Array.from({ length: 4 }, () => Array(4).fill(0));
    }

    const factor = EI / L3;
    const k = [
      [12, 6 * L, -12, 6 * L],
      [6 * L, 4 * L2, -6 * L, 2 * L2],
      [-12, -6 * L, 12, -6 * L],
      [6 * L, 2 * L2, -6 * L, 4 * L2],
    ];
    // Apply factor, ensuring no NaN/Infinity results
    return k.map((row) =>
      row.map((value) => {
        const result = value * factor;
        if (!isFinite(result)) {
          console.error(
            `Non-finite value encountered in stiffness matrix: ${result} (value=${value}, factor=${factor})`
          );
          // Depending on context, might throw error or return a default (like 0)
          throw new Error(
            "Numerical instability detected in stiffness matrix calculation."
          );
        }
        return result;
      })
    );
  }

  /**
   * Assembles the global stiffness matrix [K] for the entire beam.
   */
  private assembleGlobalStiffness(): number[][] {
    const K = Array.from({ length: this.dofCount }, () =>
      Array(this.dofCount).fill(0)
    );
    if (!this.spans || this.spans.length === 0) return K; // Return empty if no spans

    for (const span of this.spans) {
      // Ensure span properties are valid numbers
      if (
        typeof span.E !== "number" ||
        typeof span.I !== "number" ||
        typeof span.length !== "number" ||
        span.length < 0
      ) {
        throw new Error(
          `Invalid span properties (E, I, length) for span [${span.start}, ${span.end}]: E=${span.E}, I=${span.I}, L=${span.length}`
        );
      }
      // Skip zero-length spans for stiffness assembly
      if (span.length < 1e-9) continue;

      try {
        const kLocal = this.createLocalStiffnessMatrix(
          span.E,
          span.I,
          span.length
        );
        const dofMap = this.getDOFMapForSpan(span);

        for (let i = 0; i < 4; i++) {
          for (let j = 0; j < 4; j++) {
            // Check bounds before assignment
            if (
              dofMap[i] >= this.dofCount ||
              dofMap[j] >= this.dofCount ||
              dofMap[i] < 0 ||
              dofMap[j] < 0
            ) {
              console.error(
                "DOF map index out of bounds during stiffness assembly",
                { dofMap, i, j, dofCount: this.dofCount }
              );
              throw new Error("DOF index out of bounds");
            }
            K[dofMap[i]][dofMap[j]] += kLocal[i][j];
          }
        }
      } catch (error) {
        console.error(
          `Error assembling stiffness for span [${span.start}, ${span.end}]:`,
          error
        );
        throw error; // Re-throw error after logging
      }
    }
    return K;
  }

  /**
   * Assembles the global force vector [F] for a specific load combination factor.
   * @param loadComboFactor - Contains the factors for each load type.
   */
  private assembleGlobalForceVector(
    loadComboFactor: LoadComboFactor
  ): number[] {
    const F = Array(this.dofCount).fill(0);
    if (!this.loadGroups) return F;

    const factors = loadComboFactor.coefficients[0];
    if (!factors) {
      console.warn(
        `LoadComboFactor ${loadComboFactor.name} has no coefficients defined.`
      );
      return F;
    }

    for (const group of this.loadGroups) {
      if (!group || !group.loads || group.loads.length === 0) continue;

      for (const load of group.loads) {
        if (!load || typeof load.loadType !== "string") continue;

        const factor = factors[load.loadType] ?? 0;
        if (factor === 0) continue;

        const epsilon = 1e-9;
        // Find the span this load applies to using geometric check
        const targetSpan = this.spans.find((span) => {
          // Basic overlap check: Load starts before span ends AND Load ends after span starts
          // Need to handle point loads (endPosition might be undefined or same as start)
          const loadStart = load.startPosition ?? 0;
          const loadEnd = load.endPosition ?? loadStart; // Treat point load as zero length
          // Ensure span start/end are valid numbers
          const spanStart = span.start ?? 0;
          const spanEnd = span.end ?? 0;

          // Check for overlap, consider epsilon for boundary cases?
          // Overlap = (load starts before span ends) AND (load ends after span starts)
          return loadStart < spanEnd - epsilon && loadEnd > spanStart + epsilon;
        });

        if (!targetSpan) {
          // It's possible a load doesn't fall neatly into one span (e.g. starts before first span)
          // For FEA, we typically need to assign loads to elements.
          // A load outside all defined spans might be an error or needs specific handling.
          console.warn(
            `Load (Type: ${load.loadType}, Label: ${load.label}) at [${load.startPosition}, ${load.endPosition}] does not overlap significantly with any defined span.`
          );
          continue;
        }

        try {
          const dofMap = this.getDOFMapForSpan(targetSpan);
          // Pass the target span's start/end for equivalent load calculation
          const eqLoad = getEquivalentNodalLoadForElement(
            load,
            targetSpan.start,
            targetSpan.end
          );
          if (!eqLoad || eqLoad.length !== 4) {
            throw new Error(
              `Invalid equivalent nodal load returned for load ${JSON.stringify(
                load
              )}`
            );
          }

          for (let i = 0; i < 4; i++) {
            if (dofMap[i] >= this.dofCount || dofMap[i] < 0) {
              throw new Error(`DOF index ${dofMap[i]} out of bounds`);
            }
            if (typeof eqLoad[i] !== "number" || typeof factor !== "number") {
              throw new Error(
                `Invalid load value or factor: eqLoad[${i}]=${eqLoad[i]}, factor=${factor}`
              );
            }
            F[dofMap[i]] += eqLoad[i] * factor;
          }
        } catch (error) {
          console.error(
            `Error assembling forces for load ${JSON.stringify(
              load
            )} on span [${targetSpan.start}, ${targetSpan.end}] (Combo: ${
              loadComboFactor.name
            }):`,
            error
          );
          throw error;
        }
      }
    }

    if (F.some((val) => !isFinite(val))) {
      console.error(
        "Non-finite value detected in assembled force vector:",
        F,
        loadComboFactor.name
      );
      throw new Error(
        "Numerical instability detected in force vector assembly."
      );
    }
    return F;
  }

  /**
   * Assembles the global force vector [F] for a single specified load type.
   * @param targetLoadType - The specific LoadType to assemble forces for.
   */
  private assembleGlobalForceVectorForType(targetLoadType: LoadType): number[] {
    const F = Array(this.dofCount).fill(0);
    if (!this.loadGroups) return F;

    for (const group of this.loadGroups) {
      if (!group || !group.loads || group.loads.length === 0) continue;

      for (const load of group.loads) {
        if (!load || load.loadType !== targetLoadType) continue;

        const epsilon = 1e-9;
        // Find the span this load applies to using geometric check
        const targetSpan = this.spans.find((span) => {
          const loadStart = load.startPosition ?? 0;
          const loadEnd = load.endPosition ?? loadStart;
          const spanStart = span.start ?? 0;
          const spanEnd = span.end ?? 0;
          return loadStart < spanEnd - epsilon && loadEnd > spanStart + epsilon;
        });

        if (!targetSpan) {
          console.warn(
            `Load (Type: ${load.loadType}, Label: ${load.label}) at [${load.startPosition}, ${load.endPosition}] does not overlap significantly with any defined span.`
          );
          continue;
        }
        try {
          const dofMap = this.getDOFMapForSpan(targetSpan);
          const eqLoad = getEquivalentNodalLoadForElement(
            load,
            targetSpan.start,
            targetSpan.end
          );
          if (!eqLoad || eqLoad.length !== 4) {
            throw new Error(
              `Invalid equivalent nodal load returned for load ${JSON.stringify(
                load
              )}`
            );
          }

          for (let i = 0; i < 4; i++) {
            if (dofMap[i] >= this.dofCount || dofMap[i] < 0) {
              throw new Error(`DOF index ${dofMap[i]} out of bounds`);
            }
            if (typeof eqLoad[i] !== "number") {
              throw new Error(`Invalid load value: eqLoad[${i}]=${eqLoad[i]}`);
            }
            F[dofMap[i]] += eqLoad[i];
          }
        } catch (error) {
          console.error(
            `Error assembling forces for load ${JSON.stringify(
              load
            )} (type ${targetLoadType}) on span [${targetSpan.start}, ${
              targetSpan.end
            }]:`,
            error
          );
          throw error;
        }
      }
    }
    if (F.some((val) => !isFinite(val))) {
      console.error(
        "Non-finite value detected in assembled force vector (single type):",
        F,
        targetLoadType
      );
      throw new Error(
        "Numerical instability detected in force vector assembly (single type)."
      );
    }
    return F;
  }

  /**
   * Applies boundary conditions to the global matrix and force vector.
   * Uses the pre-calculated constrainedDOFs.
   */
  private applyBoundaryConditions(
    K: number[][],
    F: number[]
  ): [number[][], number[]] {
    // Create deep copies to avoid modifying K_original
    const Kmod = K.map((row) => [...row]);
    const Fmod = [...F];

    if (!this.constrainedDOFs || this.constrainedDOFs.length === 0) {
      console.warn(
        "Applying boundary conditions with no constrained DOFs. System might be unstable."
      );
      // return [Kmod, Fmod]; // Or proceed, solver might detect singularity
    }

    for (const dof of this.constrainedDOFs) {
      if (dof < 0 || dof >= this.dofCount) {
        console.error(
          `Constrained DOF index ${dof} is out of bounds (0-${
            this.dofCount - 1
          }).`
        );
        continue; // Skip invalid DOF index
      }
      // Zero out row and column corresponding to constrained DOF in Kmod
      // Important: Modify Fmod *before* zeroing Kmod column to account for influence
      for (let i = 0; i < this.dofCount; i++) {
        if (i !== dof) {
          // Fmod[i] -= Kmod[i][dof] * 0; // Displacement is 0, so no change needed here if Fmod[dof] is set to 0 below
        }
      }
      for (let i = 0; i < this.dofCount; i++) {
        Kmod[dof][i] = 0; // Zero out row
        Kmod[i][dof] = 0; // Zero out column
      }

      // Set diagonal element to 1 and corresponding force to 0
      Kmod[dof][dof] = 1;
      Fmod[dof] = 0; // Enforce zero displacement/rotation value
    }
    return [Kmod, Fmod];
  }

  /**
   * Solves the linear system [K_mod]{u} = {F_mod} using Gaussian elimination with pivoting.
   */
  private solve(K_mod: number[][], F_mod: number[]): number[] {
    const n = F_mod.length;
    if (K_mod.length !== n || K_mod.some((row) => row.length !== n)) {
      throw new Error(
        `Solver error: Matrix dimensions (${K_mod.length}x${K_mod[0]?.length}) do not match vector length (${n}).`
      );
    }

    // Create augmented matrix [K_mod | F_mod]
    const A = K_mod.map((row, i) => [...row, F_mod[i]]);

    // Forward Elimination with Partial Pivoting
    for (let i = 0; i < n; i++) {
      // Find pivot row
      let maxRow = i;
      for (let k = i + 1; k < n; k++) {
        if (Math.abs(A[k][i]) > Math.abs(A[maxRow][i])) {
          maxRow = k;
        }
      }
      // Swap rows if necessary
      if (maxRow !== i) {
        [A[i], A[maxRow]] = [A[maxRow], A[i]];
      }

      // Check for singularity or near-singularity
      const pivot = A[i][i];
      if (Math.abs(pivot) < 1e-12) {
        // Check if the rest of the column below is also zero
        let columnIsZero = true;
        for (let k = i + 1; k < n; k++) {
          if (Math.abs(A[k][i]) > 1e-12) {
            columnIsZero = false;
            break;
          }
        }

        if (columnIsZero) {
          // Pivot is zero, and rest of column is zero.
          // This indicates either a singular matrix (no unique solution) or infinite solutions.
          // This often happens if the structure is a mechanism (not properly constrained).
          console.warn(
            `Solver warning: Potential mechanism detected or singular matrix at column ${i}. Pivot is near zero.`
          );
          // We can continue, but results might be unreliable or NaN/Infinity.
          // Consider throwing an error if a unique solution is strictly required.
          // continue; // Skip elimination for this column if pivot is zero
          // Or, if BCs are applied correctly, this might represent a free DOF that should have displacement determined by loads.
          // Let's proceed cautiously. If F_mod[i] is non-zero, it implies inconsistency.
          if (Math.abs(A[i][n]) > 1e-12) {
            console.error(
              "Solver error: System appears inconsistent (zero pivot column with non-zero force term).",
              { col: i, A_i: A[i] }
            );
            throw new Error(
              "Solver error: Inconsistent system detected during elimination."
            );
          }
          // If force term is also zero, it might just be an unconstrained DOF. Continue.
          continue;
        } else {
          // Pivot is zero, but elements below are non-zero. This shouldn't happen with correct pivoting.
          // This implies a likely logic error in pivoting or prior steps.
          console.error(
            "Solver error: Zero pivot encountered despite non-zero elements below. Pivoting logic failed?",
            { col: i, A: A.map((r) => r.slice(0, n + 1)) }
          );
          throw new Error("Solver error: Zero pivot encountered unexpectedly.");
        }
      }

      // Eliminate column elements below the pivot
      for (let k = i + 1; k < n; k++) {
        const factor = A[k][i] / pivot;
        // Iterate across the row (including the augmented part)
        for (let j = i; j <= n; j++) {
          // j starts from i because elements before are already zero
          if (!isFinite(A[k][j]) || !isFinite(A[i][j]) || !isFinite(factor)) {
            console.error("Non-finite number during elimination", {
              k,
              j,
              A_k_j: A[k][j],
              A_i_j: A[i][j],
              factor,
            });
            throw new Error("Numerical instability during elimination.");
          }
          A[k][j] -= factor * A[i][j];
        }
        A[k][i] = 0; // Set explicitly to zero to handle potential precision errors
      }
    }

    // Back Substitution
    const x = Array(n).fill(0);
    for (let i = n - 1; i >= 0; i--) {
      const pivot = A[i][i];
      if (Math.abs(pivot) < 1e-12) {
        // If pivot is zero during back-substitution, check augmented value A[i][n]
        if (Math.abs(A[i][n]) > 1e-12) {
          // Equation looks like 0 * x_i = non-zero --- Inconsistent system
          console.error(
            `Solver error: Inconsistent system detected during back-substitution at row ${i}. 0 = ${A[i][n]}.`,
            { A_i: A[i] }
          );
          throw new Error("Solver error: Inconsistent system detected.");
        } else {
          // Equation looks like 0 * x_i = 0 --- Infinite solutions / free DOF
          // This means x[i] can be anything. Typically set to 0 for a particular solution,
          // but signals potential instability or under-constrained system.
          console.warn(
            `Solver warning: Free DOF or infinite solutions detected at row ${i}. Setting x[${i}] = 0.`
          );
          x[i] = 0; // Assign a particular solution (often 0)
        }
      } else {
        // Calculate sum of known terms
        let sum = 0;
        for (let j = i + 1; j < n; j++) {
          if (!isFinite(A[i][j]) || !isFinite(x[j])) {
            console.error("Non-finite number during back-substitution sum", {
              i,
              j,
              A_i_j: A[i][j],
              x_j: x[j],
            });
            throw new Error("Numerical instability during back-substitution.");
          }
          sum += A[i][j] * x[j];
        }
        // Solve for x[i]
        const val = (A[i][n] - sum) / pivot;
        if (!isFinite(val)) {
          console.error("Non-finite result during back-substitution solve", {
            i,
            A_i_n: A[i][n],
            sum,
            pivot,
          });
          throw new Error(
            "Numerical instability during back-substitution solve."
          );
        }
        x[i] = val;
      }
    }

    return x;
  }

  /**
   * Performs analysis for a specific load combination factor.
   * Populates this.displacements for that combination.
   * @param loadComboFactor - Factors for each load type.
   */
  analyzeCombination(loadComboFactor: LoadComboFactor): void {
    if (this.dofCount === 0)
      throw new Error(
        "Cannot analyze: BeamModel has zero degrees of freedom (no nodes)."
      );
    if (this.K_original.length === 0)
      throw new Error("Cannot analyze: Stiffness matrix not assembled.");
    try {
      const F = this.assembleGlobalForceVector(loadComboFactor);
      const [Kmod, Fmod] = this.applyBoundaryConditions(this.K_original, F);
      this.displacements = this.solve(Kmod, Fmod);
    } catch (error) {
      console.error(
        `Error during analysis for combination ${loadComboFactor.name}:`,
        error
      );
      this.displacements = [];
      throw error; // Re-throw
    }
  }

  /**
   * Performs analysis for a single load type.
   * Populates this.displacements for that type.
   * @param loadType - The specific LoadType to analyze.
   */
  analyzeLoadType(loadType: LoadType): void {
    if (this.dofCount === 0)
      throw new Error(
        "Cannot analyze: BeamModel has zero degrees of freedom (no nodes)."
      );
    if (this.K_original.length === 0)
      throw new Error("Cannot analyze: Stiffness matrix not assembled.");
    try {
      const F = this.assembleGlobalForceVectorForType(loadType);
      const [Kmod, Fmod] = this.applyBoundaryConditions(this.K_original, F);
      this.displacements = this.solve(Kmod, Fmod);
    } catch (error) {
      console.error(`Error during analysis for load type ${loadType}:`, error);
      this.displacements = [];
      throw error; // Re-throw
    }
  }

  /**
   * Computes vertical deflection at global position `x`. Requires analyzeCombination/analyzeLoadType first.
   */
  deflectionAt(x: number): number {
    if (this.displacements.length === 0) {
      // Try to analyze with a default (e.g., zero load) or throw specific error
      // throw new Error("Run analyzeCombination() or analyzeLoadType() before calculating deflection.");
      console.warn(
        "deflectionAt called before analysis. Returning 0. Run analyze first for results."
      );
      return 0;
    }
    if (this.spans.length === 0) return 0; // No spans, no deflection

    const epsilon = 1e-9;
    // Find the span containing x, allowing for x to be exactly at a node (span end)
    const span = this.spans.find(
      (s) => x >= s.start - epsilon && x <= s.end + epsilon
    );

    if (!span) {
      // If x is outside the bounds of all spans
      const minX = Math.min(...this.spans.map((s) => s.start));
      const maxX = Math.max(...this.spans.map((s) => s.end));
      if (x < minX - epsilon || x > maxX + epsilon) {
        console.warn(
          `Position x = ${x} is outside the beam range [${minX}, ${maxX}]. Returning 0 deflection.`
        );
        return 0; // Or throw error? Returning 0 might be expected for outside points.
      } else {
        // This case should ideally not happen if the previous find worked correctly
        throw new Error(
          `No span found containing position x = ${x}, although it appears within beam bounds.`
        );
      }
    }

    // Check for valid span length
    const L = span.length;
    if (L < 1e-9) {
      // For zero-length span, deflection is likely the nodal displacement
      // Determine if x is closer to start or end node (or exactly at one)
      const startNodeIndex = this.nodes.findIndex(
        (n) => Math.abs(n.x - span.start) < epsilon
      );
      if (startNodeIndex !== -1) return this.displacements[startNodeIndex * 2];
      // Fallback or error if node not found (shouldn't happen if span exists)
      console.error(
        "Could not find node for zero-length span deflection calculation."
      );
      return 0;
    }

    const startNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.start) < epsilon
    );
    const endNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.end) < epsilon
    );

    if (startNodeIndex === -1 || endNodeIndex === -1) {
      // This should have been caught by getDOFMapForSpan if called earlier, but check again
      throw new Error(
        `Could not find nodes for span [${span.start}, ${span.end}] during deflection calculation.`
      );
    }

    const u1_idx = startNodeIndex * 2;
    const t1_idx = startNodeIndex * 2 + 1;
    const u2_idx = endNodeIndex * 2;
    const t2_idx = endNodeIndex * 2 + 1;

    // Check displacement array bounds
    if (t2_idx >= this.displacements.length) {
      throw new Error(
        `DOF index out of bounds accessing displacements array (length ${this.displacements.length}, needed ${t2_idx})`
      );
    }

    const u1 = this.displacements[u1_idx];
    const theta1 = this.displacements[t1_idx];
    const u2 = this.displacements[u2_idx];
    const theta2 = this.displacements[t2_idx];

    // Normalize coordinate Xi = (x - start) / L
    const Xi = (x - span.start) / L;
    // Clamp Xi to [0, 1] to avoid extrapolation issues due to floating point errors near ends
    const Xi_clamped = Math.max(0, Math.min(1, Xi));
    const Xi2 = Xi_clamped * Xi_clamped;
    const Xi3 = Xi2 * Xi_clamped;

    // Hermite Shape Functions N(Xi)
    const N1 = 1 - 3 * Xi2 + 2 * Xi3;
    const N2 = L * (Xi_clamped - 2 * Xi2 + Xi3);
    const N3 = 3 * Xi2 - 2 * Xi3;
    const N4 = L * (-Xi2 + Xi3);

    const deflection = N1 * u1 + N2 * theta1 + N3 * u2 + N4 * theta2;
    if (!isFinite(deflection)) {
      console.error("Non-finite deflection calculated", {
        x,
        Xi,
        u1,
        theta1,
        u2,
        theta2,
        N1,
        N2,
        N3,
        N4,
      });
      throw new Error(
        "Numerical instability resulted in non-finite deflection."
      );
    }
    return deflection;
  }

  /**
   * Computes internal bending moment M(x). Requires analyzeCombination/analyzeLoadType first.
   */
  momentAt(x: number): number {
    if (this.displacements.length === 0) {
      console.warn("momentAt called before analysis. Returning 0.");
      return 0;
    }
    if (this.spans.length === 0) return 0; // No spans, no moment

    const epsilon = 1e-9;
    const span = this.spans.find(
      (s) => x >= s.start - epsilon && x <= s.end + epsilon
    );
    if (!span) {
      const minX = Math.min(...this.spans.map((s) => s.start));
      const maxX = Math.max(...this.spans.map((s) => s.end));
      if (x < minX - epsilon || x > maxX + epsilon) {
        console.warn(
          `Position x = ${x} is outside the beam range [${minX}, ${maxX}]. Returning 0 moment.`
        );
        return 0;
      } else {
        throw new Error(
          `No span found containing position x = ${x} for moment calculation.`
        );
      }
    }

    if (
      typeof span.E !== "number" ||
      typeof span.I !== "number" ||
      typeof span.length !== "number"
    ) {
      throw new Error(
        `Invalid span properties (E, I, length) for moment calculation at span [${span.start}, ${span.end}]`
      );
    }

    const L = span.length;
    const E = span.E;
    const I = span.I;

    if (L < 1e-9 || E * I === 0) {
      return 0; // No moment in zero-length or zero-stiffness element
    }

    const startNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.start) < epsilon
    );
    const endNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.end) < epsilon
    );

    if (startNodeIndex === -1 || endNodeIndex === -1) {
      throw new Error(
        `Could not find nodes for span [${span.start}, ${span.end}] during moment calculation.`
      );
    }

    const u1_idx = startNodeIndex * 2;
    const t1_idx = startNodeIndex * 2 + 1;
    const u2_idx = endNodeIndex * 2;
    const t2_idx = endNodeIndex * 2 + 1;

    if (t2_idx >= this.displacements.length) {
      throw new Error(
        `DOF index out of bounds accessing displacements array (length ${this.displacements.length}, needed ${t2_idx})`
      );
    }

    const u1 = this.displacements[u1_idx];
    const theta1 = this.displacements[t1_idx];
    const u2 = this.displacements[u2_idx];
    const theta2 = this.displacements[t2_idx];

    const Xi = (x - span.start) / L;
    const Xi_clamped = Math.max(0, Math.min(1, Xi)); // Use clamped coordinate
    const L2 = L * L;

    // Second derivatives of shape functions w.r.t x: d²N/dx² = (1/L²) * d²N/dXi²
    const d2N1_dx2 = (12 * Xi_clamped - 6) / L2;
    const d2N2_dx2 = (6 * Xi_clamped - 4) / L;
    const d2N3_dx2 = (6 - 12 * Xi_clamped) / L2;
    const d2N4_dx2 = (6 * Xi_clamped - 2) / L;

    // Second derivative of deflection w.r.t x: d²w/dx²
    const d2w_dx2 =
      d2N1_dx2 * u1 + d2N2_dx2 * theta1 + d2N3_dx2 * u2 + d2N4_dx2 * theta2;

    // Moment M = -EI * d²w/dx²
    const moment = -E * I * d2w_dx2;

    if (!isFinite(moment)) {
      console.error("Non-finite moment calculated", {
        x,
        Xi,
        u1,
        theta1,
        u2,
        theta2,
        d2N1_dx2,
        d2N2_dx2,
        d2N3_dx2,
        d2N4_dx2,
        d2w_dx2,
      });
      throw new Error("Numerical instability resulted in non-finite moment.");
    }
    return moment;
  }

  /**
   * Computes internal shear force V(x). Requires analyzeCombination/analyzeLoadType first.
   */
  shearAt(x: number): number {
    if (this.displacements.length === 0) {
      console.warn("shearAt called before analysis. Returning 0.");
      return 0;
    }
    if (this.spans.length === 0) return 0; // No spans, no shear

    const epsilon = 1e-9;
    const span = this.spans.find(
      (s) => x >= s.start - epsilon && x <= s.end + epsilon
    );
    if (!span) {
      const minX = Math.min(...this.spans.map((s) => s.start));
      const maxX = Math.max(...this.spans.map((s) => s.end));
      if (x < minX - epsilon || x > maxX + epsilon) {
        console.warn(
          `Position x = ${x} is outside the beam range [${minX}, ${maxX}]. Returning 0 shear.`
        );
        return 0;
      } else {
        throw new Error(
          `No span found containing position x = ${x} for shear calculation.`
        );
      }
    }

    if (
      typeof span.E !== "number" ||
      typeof span.I !== "number" ||
      typeof span.length !== "number"
    ) {
      throw new Error(
        `Invalid span properties (E, I, length) for shear calculation at span [${span.start}, ${span.end}]`
      );
    }

    const L = span.length;
    const E = span.E;
    const I = span.I;

    if (L < 1e-9 || E * I === 0) {
      return 0; // No shear in zero-length or zero-stiffness element
    }

    const startNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.start) < epsilon
    );
    const endNodeIndex = this.nodes.findIndex(
      (n) => Math.abs(n.x - span.end) < epsilon
    );

    if (startNodeIndex === -1 || endNodeIndex === -1) {
      throw new Error(
        `Could not find nodes for span [${span.start}, ${span.end}] during shear calculation.`
      );
    }

    const u1_idx = startNodeIndex * 2;
    const t1_idx = startNodeIndex * 2 + 1;
    const u2_idx = endNodeIndex * 2;
    const t2_idx = endNodeIndex * 2 + 1;

    if (t2_idx >= this.displacements.length) {
      throw new Error(
        `DOF index out of bounds accessing displacements array (length ${this.displacements.length}, needed ${t2_idx})`
      );
    }

    const u1 = this.displacements[u1_idx];
    const theta1 = this.displacements[t1_idx];
    const u2 = this.displacements[u2_idx];
    const theta2 = this.displacements[t2_idx];

    // Xi does not affect the third derivative result, but keep for consistency if needed elsewhere
    // const Xi = (x - span.start) / L;
    const L2 = L * L;
    const L3 = L2 * L;

    // Third derivatives of shape functions w.r.t x: d³N/dx³ = (1/L³) * d³N/dXi³
    const d3N1_dx3 = 12 / L3;
    const d3N2_dx3 = 6 / L2;
    const d3N3_dx3 = -12 / L3;
    const d3N4_dx3 = 6 / L2;

    // Third derivative of deflection w.r.t x: d³w/dx³
    const d3w_dx3 =
      d3N1_dx3 * u1 + d3N2_dx3 * theta1 + d3N3_dx3 * u2 + d3N4_dx3 * theta2;

    // Shear V = -EI * d³w/dx³
    const shear = -E * I * d3w_dx3;

    if (!isFinite(shear)) {
      console.error("Non-finite shear calculated", {
        x,
        u1,
        theta1,
        u2,
        theta2,
        d3N1_dx3,
        d3N2_dx3,
        d3N3_dx3,
        d3N4_dx3,
        d3w_dx3,
      });
      throw new Error("Numerical instability resulted in non-finite shear.");
    }
    return shear;
  }

  // --- NEW METHODS --- Moved from BeamSummary ---

  // --- calculateMaxStressRatios Refactoring Helpers ---

  /**
   * @private
   * Calculates moment and shear at a specific point x.
   * Handles potential errors during calculation.
   * Requires displacements to be populated by analyzeCombination/analyzeLoadType first.
   * @param x - The global x-coordinate.
   * @param comboName - The name of the current load combination (for error context).
   * @returns An object { moment: number, shear: number } or null if calculation fails.
   */
  private _getMaxMomentShearAtPoint(x: number, comboName: string): { moment: number; shear: number } | null {
    try {
      const moment = this.momentAt(x);
      const shear = this.shearAt(x);
      // Check for NaN/Infinity which might occur despite internal checks
      if (!isFinite(moment) || !isFinite(shear)) {
        console.warn(`Non-finite moment/shear calculated at x=${x} for combo ${comboName}`, { moment, shear });
        return null; // Indicate calculation issue
      }
      return { moment, shear };
    } catch (calcError) {
      console.error(`Error calculating moment/shear at x=${x} for combo ${comboName}:`, calcError);
      return null; // Indicate calculation failure
    }
  }

  /**
   * @private
   * Updates a MaxResult object if the new candidate value is absolutely larger.
   * @param currentMax - The current MaxResult object to potentially update.
   * @param candidateValue - The new candidate value (e.g., a ratio).
   * @param position - The position (x-coordinate) where the candidate value occurred.
   * @param spanNumber - The 1-based index of the span where the candidate occurred.
   * @param loadComboName - The name of the load combination for the candidate.
   */
  private _updateMaxResult(
    currentMax: MaxResult,
    candidateValue: number,
    position: number,
    spanNumber: number,
    loadComboName: string
  ): void { // Modifies currentMax directly
    if (Math.abs(candidateValue) > Math.abs(currentMax.value)) {
      currentMax.value = candidateValue;
      currentMax.position = position;
      currentMax.spanNumber = spanNumber;
      currentMax.loadComboName = loadComboName;
    }
  }

  /**
   * @private
   * Finds the maximum bending and shear stress ratios within a single span 
   * for the *currently analyzed* load combination (uses current this.displacements).
   *
   * @param span - The Span object to analyze.
   * @param spanIndex - The 0-based index of the span.
   * @param allowableBendingDivisor - Divisor for bending ratio calculation.
   * @param allowableShearDivisor - Divisor for shear ratio calculation.
   * @param pointsPerSpan - Number of points to sample within the span.
   * @param comboName - The name of the current load combination (for context).
   * @param currentMaxBending - The current overall max bending result (used for comparison).
   * @param currentMaxShear - The current overall max shear result (used for comparison).
   */
   private _findMaxRatiosInSpan(
    span: Span,
    spanIndex: number,
    allowableBendingDivisor: number,
    allowableShearDivisor: number,
    pointsPerSpan: number,
    comboName: string,
    currentMaxBending: MaxResult, // Pass overall max for comparison
    currentMaxShear: MaxResult   // Pass overall max for comparison
  ): void { // Modifies currentMaxBending/currentMaxShear by reference
    if (span.length < 1e-9) return; // Skip zero-length spans

    for (let i = 0; i <= pointsPerSpan; i++) {
      const x = span.start + (span.length * i) / pointsPerSpan;
      const momentShear = this._getMaxMomentShearAtPoint(x, comboName);

      if (momentShear === null) {
        // Error occurred calculating moment/shear at this point, skip comparison
        continue;
      }

      const { moment, shear } = momentShear;
      const bendingRatio = moment / allowableBendingDivisor;
      const shearRatio = shear / allowableShearDivisor;

      if (!isFinite(bendingRatio) || !isFinite(shearRatio)) {
        console.warn(
          `Non-finite ratio calculated at x=${x} for combo ${comboName}`,
          { moment, shear, bendingRatio, shearRatio }
        );
        continue; // Skip non-finite results
      }
      
      // Update overall maximums directly
      this._updateMaxResult(currentMaxBending, bendingRatio, x, spanIndex + 1, comboName);
      this._updateMaxResult(currentMaxShear, shearRatio, x, spanIndex + 1, comboName);
    }
  }

  // --- Main Method --- 

  /**
   * Calculates maximum absolute bending and shear stress ratios across all spans 
   * by analyzing each provided load combination.
   * Note: Uses provided divisors which should represent AllowableStress * SectionProperty.
   *
   * @param loadCombinationFactors - A map of combination names to LoadComboFactor instances.
   * @param allowableBendingDivisor - Divisor for bending ratio (e.g., Allowable Bending Stress * Section Modulus S).
   * @param allowableShearDivisor - Divisor for shear ratio (e.g., Allowable Shear Stress * Web Area Aw).
   * @param pointsPerSpan - Number of points to sample within each span for max values.
   * @returns An object containing the overall maximum bending and shear stress ratios found.
   */
  calculateMaxStressRatios(
    loadCombinationFactors: { [comboName: string]: LoadComboFactor },
    allowableBendingDivisor: number,
    allowableShearDivisor: number,
    pointsPerSpan: number = 20
  ): MaxStressRatioResult {
    // --- Initial Validation ---
    if (this.spans.length === 0) {
      console.warn("Cannot calculate stress ratios: No spans defined.");
      return {
        bending: { value: 0, position: 0, spanNumber: 0, loadComboName: "N/A" },
        shear: { value: 0, position: 0, spanNumber: 0, loadComboName: "N/A" },
      };
    }
    if (allowableBendingDivisor <= 1e-9 || allowableShearDivisor <= 1e-9) {
      throw new Error(
        `Allowable stress divisors must be positive and non-zero. Got Bending=${allowableBendingDivisor}, Shear=${allowableShearDivisor}`
      );
    }

    // --- Initialization ---
    // Initialize with absolute value 0, will be updated by first valid result
    const overallMaxBending: MaxResult = {
      value: 0,
      position: 0,
      spanNumber: 0,
      loadComboName: "", // Will be updated
    };
    const overallMaxShear: MaxResult = {
      value: 0,
      position: 0,
      spanNumber: 0,
      loadComboName: "", // Will be updated
    };
    let anyAnalysisSucceeded = false;
    let calculationErrorOccurred = false;

    // --- Iterate Through Load Combinations ---
    Object.entries(loadCombinationFactors).forEach(
      ([comboName, loadComboFactor]) => {
        try {
          // 1. Analyze for the current combination
          this.analyzeCombination(loadComboFactor);
          anyAnalysisSucceeded = true;

          // 2. Find max ratios for this specific combination across all spans
          this.spans.forEach((span, spanIndex) => {
             try {
                this._findMaxRatiosInSpan(
                    span,
                    spanIndex,
                    allowableBendingDivisor,
                    allowableShearDivisor,
                    pointsPerSpan,
                    comboName,
                    overallMaxBending, // Pass overall max to be updated
                    overallMaxShear    // Pass overall max to be updated
                 );
             } catch (spanCalcError) {
                  console.error(`Error processing span ${spanIndex + 1} for combo ${comboName}:`, spanCalcError);
                  calculationErrorOccurred = true;
                  // Continue to next span if possible
             }
          });

        } catch (analysisError) {
          // Handle failure to analyze this specific combination
          console.error(
            `Analysis failed for load combination ${comboName}:`,
            analysisError
          );
          calculationErrorOccurred = true;
          // Continue to the next combination if one fails
        }
      }
    );

    // --- Final Result Processing ---
    if (!anyAnalysisSucceeded && Object.keys(loadCombinationFactors).length > 0) {
      console.error(
        "Stress ratio calculation failed: No load combination analysis succeeded."
      );
      // Return error state if no analysis could run
      return {
        bending: { value: 0, position: 0, spanNumber: 0, loadComboName: "Error" },
        shear: { value: 0, position: 0, spanNumber: 0, loadComboName: "Error" },
      };
    }

    // If calculation errors occurred during span processing, mark results potentially incomplete
    if (calculationErrorOccurred) {
       if (!overallMaxBending.loadComboName) overallMaxBending.loadComboName = "Error";
       if (!overallMaxShear.loadComboName) overallMaxShear.loadComboName = "Error";
    }
    
    // If analysis succeeded but loads resulted in zero stress (or no combos provided)
    if (anyAnalysisSucceeded && !calculationErrorOccurred) {
       if (overallMaxBending.loadComboName === "") overallMaxBending.loadComboName = "N/A"; // No governing combo found
       if (overallMaxShear.loadComboName === "") overallMaxShear.loadComboName = "N/A"; // No governing combo found
    }
    
    // Return final results, ensuring absolute values are used for comparison logic internally
    // but the signed value (indicating direction/type of stress) is returned.
    return { bending: overallMaxBending, shear: overallMaxShear };
  }

  /**
   * Calculates maximum upward and downward deflections and ratios across all spans for given load combinations.
   * @param loadCombinationFactors - A map of combination names to LoadComboFactor instances.
   * @param deflectionLimitDenominator - E.g., 360 for L/360 limit.
   * @param pointsPerSpan - Number of points to sample within each span.
   */
  calculateMaxDeflections(
    loadCombinationFactors: { [comboName: string]: LoadComboFactor },
    deflectionLimitDenominator: number,
    pointsPerSpan: number = 20
  ): MaxDeflectionResult {
    if (this.spans.length === 0) {
      console.warn("Cannot calculate deflections: No spans defined.");
      return {
        maxDownward: { value: 0, ratio: 0, loadComboName: "N/A" },
        maxUpward: { value: 0, ratio: 0, loadComboName: "N/A" },
      };
    }
    if (deflectionLimitDenominator <= 1e-9) {
      throw new Error(
        `Deflection limit denominator must be positive and non-zero. Got ${deflectionLimitDenominator}`
      );
    }

    const result: MaxDeflectionResult = {
      maxDownward: { value: 0, ratio: 0, loadComboName: "" },
      maxUpward: { value: 0, ratio: 0, loadComboName: "" },
    };
    let analysisPerformed = false;

    Object.entries(loadCombinationFactors).forEach(
      ([comboName, loadComboFactor]) => {
        try {
          this.analyzeCombination(loadComboFactor);
          analysisPerformed = true;
        } catch (error) {
          console.error(
            `Analysis failed for load combination ${comboName} (deflection calc):`,
            error
          );
          result.maxDownward.loadComboName =
            result.maxDownward.loadComboName || "Error";
          result.maxUpward.loadComboName =
            result.maxUpward.loadComboName || "Error";
          return;
        }

        this.spans.forEach((span) => {
          if (span.length < 1e-9) return;
          const limit = span.length / deflectionLimitDenominator;
          if (Math.abs(limit) < 1e-12) return;

          for (let i = 0; i <= pointsPerSpan; i++) {
            const x = span.start + (span.length * i) / pointsPerSpan;
            try {
              const deflection = this.deflectionAt(x);
              const ratio = Math.abs(deflection) / Math.abs(limit);

              if (!isFinite(ratio)) {
                console.warn(
                  `Non-finite deflection ratio calculated at x=${x} for combo ${comboName}`,
                  { deflection, limit, ratio }
                );
                continue;
              }

              if (deflection >= 0) {
                if (ratio > result.maxDownward.ratio) {
                  result.maxDownward = {
                    value: deflection,
                    ratio,
                    loadComboName: comboName,
                  };
                }
              } else {
                if (ratio > result.maxUpward.ratio) {
                  result.maxUpward = {
                    value: deflection,
                    ratio,
                    loadComboName: comboName,
                  };
                }
              }
            } catch (calcError) {
              console.error(
                `Error calculating deflection at x=${x} for combo ${comboName}:`,
                calcError
              );
              result.maxDownward.loadComboName =
                result.maxDownward.loadComboName || "Error";
              result.maxUpward.loadComboName =
                result.maxUpward.loadComboName || "Error";
            }
          }
        });
      }
    );

    if (!analysisPerformed && Object.keys(loadCombinationFactors).length > 0) {
      console.error(
        "Deflection calculation failed: No load combination analysis succeeded."
      );
      return {
        maxDownward: { value: 0, ratio: 0, loadComboName: "Error" },
        maxUpward: { value: 0, ratio: 0, loadComboName: "Error" },
      };
    }
    if (result.maxDownward.loadComboName === "")
      result.maxDownward.loadComboName = "N/A";
    if (result.maxUpward.loadComboName === "")
      result.maxUpward.loadComboName = "N/A";

    return result;
  }

  /** HELPER: Analyzes the beam for a single load type. */
  private _analyzeSingleLoadType(
    loadType: LoadType
  ): { F_type: number[]; U_type: number[] } | null {
    const epsilon = 1e-9;
    try {
      const F_type = this.assembleGlobalForceVectorForType(loadType);
      // If no forces for this type, analysis is trivial (zero displacements)
      if (F_type.every((f) => Math.abs(f) < epsilon)) {
        return { F_type, U_type: Array(this.dofCount).fill(0) };
      }

      const [Kmod, Fmod] = this.applyBoundaryConditions(
        this.K_original,
        F_type
      );
      const U_type = this.solve(Kmod, Fmod);
      return { F_type, U_type };
    } catch (error) {
      console.error(`Analysis failed for load type ${loadType}:`, error);
      return null; // Indicate failure
    }
  }

  /** HELPER: Calculates the raw reaction vector R = K*U - F */
  private _calculateReactionVector(
    U_type: number[],
    F_type: number[]
  ): number[] {
    const R_type = Array(this.dofCount).fill(0);
    for (let i = 0; i < this.dofCount; i++) {
      let K_U_sum = 0;
      for (let j = 0; j < this.dofCount; j++) {
        if (!isFinite(this.K_original[i][j]) || !isFinite(U_type[j])) {
          console.error(
            `Non-finite value encountered during reaction calculation K*U`,
            { i, j, Kij: this.K_original[i][j], Uj: U_type[j] }
          );
          throw new Error("Numerical error during reaction calculation (K*U).");
        }
        K_U_sum += this.K_original[i][j] * U_type[j];
      }
      if (!isFinite(K_U_sum) || !isFinite(F_type[i])) {
        console.error(
          `Non-finite value encountered during reaction calculation R = K*U - F`,
          { i, K_U_sum, Fi: F_type[i] }
        );
        throw new Error(
          "Numerical error during reaction calculation (R = K*U - F)."
        );
      }
      R_type[i] = K_U_sum - F_type[i];
    }
    return R_type;
  }

  /** HELPER: Maps reaction vector R to supports based on constrained DOFs */
  private _mapReactionsToSupports(
    R_type: number[],
    loadType: LoadType,
    forceReactions: SupportReactionMap, // Renamed for clarity
    momentReactions: SupportMomentReactionMap // Added moment map
  ): void {
    const epsilon = 1e-9;
    this.constrainedDOFs.forEach((dofIndex) => {
      const nodeIndex = Math.floor(dofIndex / 2);
      if (nodeIndex < 0 || nodeIndex >= this.nodes.length) {
        console.error(
          `Invalid node index ${nodeIndex} derived from DOF ${dofIndex}`
        );
        return;
      }
      const nodePosition = this.nodes[nodeIndex].x;
      const supportIndex = this.supports.findIndex(
        (support) => Math.abs(support.position - nodePosition) < epsilon
      );

      if (supportIndex !== -1) {
        // Initialize maps if needed
        if (!forceReactions[supportIndex]) {
          forceReactions[supportIndex] = {};
        }
        if (!momentReactions[supportIndex]) {
          // Initialize moment map entry
          momentReactions[supportIndex] = {};
        }

        const reactionValue = -R_type[dofIndex];
        if (!isFinite(reactionValue)) {
          console.error(`Non-finite reaction calculated for DOF ${dofIndex}`, {
            R_type_i: R_type[dofIndex],
          });
          // Mark both as NaN on error
          forceReactions[supportIndex][loadType] = NaN;
          momentReactions[supportIndex][loadType] = NaN;
          return;
        }

        // Check if DOF is vertical force (even index) or moment (odd index)
        if (dofIndex % 2 === 0) {
          // Vertical Force DOF
          const currentForce = forceReactions[supportIndex][loadType] ?? 0;
          forceReactions[supportIndex][loadType] = currentForce + reactionValue;
        } else {
          // Rotational Moment DOF
          const currentMoment = momentReactions[supportIndex][loadType] ?? 0; // Get current moment
          momentReactions[supportIndex][loadType] =
            currentMoment + reactionValue; // Accumulate moment
        }
      } else {
        console.warn(
          `Could not map constrained DOF ${dofIndex} (Node ${nodeIndex} at x=${nodePosition}) to any support.`
        );
      }
    });
  }

  /** HELPER: Ensures all supports have a reaction entry (default 0) for the type */
  private _ensureZeroReactionsForType(
    loadType: LoadType,
    forceReactions: SupportReactionMap,
    momentReactions: SupportMomentReactionMap
  ): void {
    this.supports.forEach((_, supportIndex) => {
      // Ensure force reaction entry
      if (!forceReactions[supportIndex]) {
        forceReactions[supportIndex] = {};
      }
      if (forceReactions[supportIndex][loadType] === undefined) {
        forceReactions[supportIndex][loadType] = 0;
      }
      // Ensure moment reaction entry
      if (!momentReactions[supportIndex]) {
        // Check moment map
        momentReactions[supportIndex] = {};
      }
      if (momentReactions[supportIndex][loadType] === undefined) {
        // Check moment map
        momentReactions[supportIndex][loadType] = 0;
      }
    });
  }

  /**
   * Calculates support reactions (forces and moments) for each support for each specified load type.
   * Uses the FEM equation R = K_original * U - F
   * @param loadTypes - An array of LoadTypes to calculate reactions for.
   * @returns An object containing force and moment reaction maps.
   */
  calculateAllSupportReactions(loadTypes: LoadType[]): {
    forces: SupportReactionMap;
    moments: SupportMomentReactionMap;
  } {
    // Initial checks
    if (this.supports.length === 0) {
      console.warn("Cannot calculate reactions: No supports defined.");
      return { forces: {}, moments: {} };
    }
    if (this.K_original.length === 0) {
      console.warn(
        "Cannot calculate reactions: Stiffness matrix not assembled."
      );
      return { forces: {}, moments: {} };
    }
    if (this.constrainedDOFs.length === 0) {
      console.warn(
        "Cannot calculate reactions: No constrained DOFs found (unstable structure?)."
      );
      return { forces: {}, moments: {} };
    }

    const forceReactions: SupportReactionMap = {};
    const momentReactions: SupportMomentReactionMap = {}; // Initialize moment map
    // Initialize map structures first
    this.supports.forEach((_, supportIndex) => {
      forceReactions[supportIndex] = {};
      momentReactions[supportIndex] = {}; // Initialize moment map structure
    });

    loadTypes.forEach((loadType) => {
      const analysisResult = this._analyzeSingleLoadType(loadType);

      if (analysisResult === null) {
        // Analysis failed, mark reactions as NaN
        this.supports.forEach((_, supportIndex) => {
          forceReactions[supportIndex][loadType] = NaN;
          momentReactions[supportIndex][loadType] = NaN; // Mark moment as NaN
        });
        return; // Skip to next load type
      }

      const { F_type, U_type } = analysisResult;
      const R_type = this._calculateReactionVector(U_type, F_type);

      // Pass both maps to the mapping function
      this._mapReactionsToSupports(
        R_type,
        loadType,
        forceReactions,
        momentReactions
      );

      // Pass both maps to the ensuring function
      this._ensureZeroReactionsForType(
        loadType,
        forceReactions,
        momentReactions
      );
    });

    // Return both maps
    return { forces: forceReactions, moments: momentReactions };
  }
}
