/**
 * Types related to beam data and properties
 */

import { LoadGroup } from "../load/load-group";
import { Support } from "../support/support";
import { LoadComboFactor } from "../load/load-combo-factor";
// Import the correct DesignValues type
import type { DesignValues } from '@/components/materials';
// Import Table5A and Table5B row types
import type { Table5ARow, Table5BRow } from '@/hooks/use-wood-data';

// Re-export Support and LoadGroup types
export type { Support } from "../support/support";
export type { LoadGroup } from "../load/load-group";

/**
 * Represents the physical and material properties of a beam
 */
export interface BeamProperties {
  length: number;
  elasticModulus: number;
  momentOfInertia: number; // This will represent Ixx for calculations
  area: number;
}

/**
 * Represents the complete data model for a beam
 */
export interface BeamData {
  properties: BeamProperties;
  loadGroups: LoadGroup[];
  supports: Support[];
  selectedLoadCombos?: string[];
  designMethod?: 'ASD' | 'LRFD';
  customLoadCombos?: Record<string, LoadComboFactor>;
}

// Placeholder type - replace with actual import or definition
// export interface DesignValues {} // Remove placeholder

export interface AdjustmentFactorDetail {
  factor: number;
  note?: string;
}

export interface AdjustmentFactorSet {
  Fb: AdjustmentFactorDetail;
  Ft: AdjustmentFactorDetail;
  Fv: AdjustmentFactorDetail;
  Fc_perp: AdjustmentFactorDetail;
  Fc: AdjustmentFactorDetail;
  E: AdjustmentFactorDetail;
  Emin: AdjustmentFactorDetail;
}

/**
 * Interface for Glued Laminated Timber specific section properties
 */
export interface GluLamSectionProperties {
  width: string;
  depth: string;
  area: number;
  Ix: number;
  Sx: number;
  rx: number;
  Iy: number;
  Sy: number;
  ry: number;
  table?: string; // Table name from NDS or other source
  speciesCombination?: string; // e.g. "Western Species", "Southern Pine"
  // Add other relevant properties if needed from your CSV
}

/**
 * Represents the selected Glulam properties including species, section details, and design values from tables.
 */
export interface GluLamSelectedProperties {
  speciesGroup: string;
  species: string;
  width: number;
  depth: number;
  sectionProperties?: GluLamSectionProperties | null;
  selectedCombinationSymbolKey?: string; // e.g., "5A_24F-V4" or "5B_1"
  selectedTable5ADetail?: Table5ARow;     // Full data row for the selected 5A symbol
  selectedTable5BDetail?: Table5BRow;     // Full data row for the selected 5B symbol
  grade?: string; // Add grade, populated if a Table 5B symbol is selected
  loadingOrientation?: 'perpendicular' | 'parallel'; // Added loading orientation
}

/**
 * Represents the state of beam properties form
 */
export interface BeamPropertiesState {
  isWetService: boolean;
  isRepetitiveMember: boolean;
  isBraced: boolean;
  lu: string;
  beamStabilityFactorCL: number | null;
  selectedSpecies: string;
  selectedSpeciesCombination: string;
  selectedGrade: string;
  selectedSizeClassification: string;
  selectedNominalSize: string;
  selectedNdsVersion: string; // Add NDS version selection
  manualWidth: string;
  manualDepth: string;
  designValues: DesignValues | null;
  lumberProperties: any;
  flatUseFactor: number;
  repetitiveMemberFactor: number;
  wetServiceFactor: AdjustmentFactorSet;
  manual_Area: number | null; // Calculated from manual width/depth
  manual_Ixx: number | null; // Calculated from manual width/depth
  manual_Iyy: number | null; // Calculated from manual width/depth
  manual_Sxx: number | null; // Calculated from manual width/depth
  manual_Syy: number | null; // Calculated from manual width/depth
  manual_E: number | null; // Renamed from manual_E_allow
  manual_Fb_allow: number | null;
  manual_Fv_allow: number | null;
  manual_E_min: number | null; // Add manual E_min
  manual_maxStressRatioLimit: number | null;
  manual_totalDeflectionLimit: number | null;
  manual_liveDeflectionLimit: number | null;
  includeBeamWeight: boolean;
  moistureContent: number | null;
  isIncised: boolean;
  incisingFactors: AdjustmentFactorSet;
  isTemperatureFactored: boolean;
  temperature: number | null; // Temperature in Fahrenheit for Ct calculation
  temperatureFactors: AdjustmentFactorSet;
  selectedGluLamProperties: GluLamSelectedProperties | null;
  lumberType: "sawn" | "glulam"; // Added lumberType
  volumeFactorCv: number | null; // Added for Glulam Volume Factor
  manual_species: string | null; // Manual species input
  manual_grade: string | null; // Manual grade input
  manual_material_type: "Sawn Lumber" | "Glulam" | "Steel" | null; // Manual material type selection
  isManualMode: boolean; // Add manual mode flag
  selectedMaterial: "steel" | "wood"; // Add material selection field
  
  // Steel-specific properties
  isSteelBraced: boolean; // Laterally braced for steel
  steelUnbracedLength: string; // Unbraced length for steel
  selectedLoadCombos?: string[]; // Names of selected load combinations
  customLoadCombos?: Record<string, LoadComboFactor>; // Custom load combinations
}