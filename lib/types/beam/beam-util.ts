import { Span } from "@/lib/types/span/span";
import { Support } from "@/lib/types/support/support";
import { Type } from "../load/load-type";
import { Node } from "./node";
import { Load } from "@/lib/types/load/load";

/**
 * Converts a single Load (point or distributed) into an equivalent nodal force vector [q1, m1, q2, m2]
 * for a beam element spanning from `elementStart` to `elementEnd`.
 *
 * The resulting 4 values represent:
 * - q1: vertical force at start node
 * - m1: moment at start node
 * - q2: vertical force at end node
 * - m2: moment at end node
 *
 * This function supports:
 * - Point loads applied at any location along the element
 * - Uniform distributed loads over the full span
 * - Trapezoidal (linearly varying) distributed loads over part or all of the span
 *
 * @param load - The load to convert (must have valid position(s) and magnitude(s))
 * @param elementStart - Global x-coordinate of the element's start
 * @param elementEnd - Global x-coordinate of the element's end
 * @returns Equivalent nodal force vector [q1, m1, q2, m2]
 *
 * @see <PERSON><PERSON><PERSON>, *Structural Analysis*, 10th ed., Appendix D
 * @see <PERSON> et al., *Concepts and Applications of Finite Element Analysis*, Wiley, 4th ed., Ch. 3–4
 */
export function getEquivalentNodalLoadForElement(
  load: Load,
  elementStart: number,
  elementEnd: number
): number[] {
  const L = elementEnd - elementStart;
  if (L <= 0) throw new Error("Invalid element length");

  if (load.type === Type.POINT) {
    return getPointLoadEquivalent(load, elementStart, L);
  }

  if (load.type === Type.DISTRIBUTED) {
    return getDistributedLoadEquivalent(load, elementStart, elementEnd, L);
  }

  return [0, 0, 0, 0]; // default fallback
}

/**
 * Converts a point load into its equivalent nodal load vector [q1, m1, q2, m2].
 *
 * The load is assumed to be applied at a distance `a` from the start of the beam element (length L),
 * and has magnitude P (positive = downward).
 *
 * Formulas (Hibbeler, Appendix D or Cook, Eq. 3.28):
 * - q1 = P·b²·(3a + b) / L³
 * - m1 = P·a·b² / L²
 * - q2 = P·a²·(3b + a) / L³
 * - m2 = -P·a²·b / L²
 *
 * @param load - Load of type POINT with valid startPosition and startMagnitude
 * @param elementStart - Start x-position of the beam element
 * @param L - Length of the beam element
 * @returns [q1, m1, q2, m2]
 */
function getPointLoadEquivalent(load: Load, elementStart: number, L: number): number[] {
  const P = load.startMagnitude ?? 0;
  const a = (load.startPosition ?? 0) - elementStart;
  const b = L - a;

  const q1 = (P * b ** 2 * (3 * a + b)) / (L ** 3);
  const m1 = (P * a * b ** 2) / (L ** 2);
  const q2 = (P * a ** 2 * (3 * b + a)) / (L ** 3);
  const m2 = -(P * a ** 2 * b) / (L ** 2);

  return [q1, m1, q2, m2];
}

/**
 * Converts a distributed load (uniform or trapezoidal) into its equivalent nodal load vector.
 *
 * Handles partial span coverage (e.g., load from 2 to 4 on a 5-meter beam).
 * Assumes linear variation if w1 ≠ w2.
 *
 * - Uniform case: Nodal values are from exact integration of shape functions.
 * - Trapezoidal case: Uses standard FEM trapezoidal nodal formulas.
 *
 * @param load - Load of type DISTRIBUTED
 * @param elementStart - Start x-position of the beam element
 * @param elementEnd - End x-position of the beam element
 * @param L - Length of the beam element
 * @returns [q1, m1, q2, m2]
 *
 * @see Cook et al., Ch. 4.4, Table 4.2
 */
function getDistributedLoadEquivalent(
  load: Load,
  elementStart: number,
  elementEnd: number,
  L: number
): number[] {
  const a = Math.max(load.startPosition ?? elementStart, elementStart);
  const b = Math.min(load.endPosition ?? elementEnd, elementEnd);
  const l = b - a;

  if (l <= 0) return [0, 0, 0, 0];

  const w1 = load.startMagnitude ?? 0;
  const w2 = load.endMagnitude ?? w1;

  return w1 === w2
    ? getUniformDistributedLoadEquivalent(w1, L)
    : getTrapezoidalDistributedLoadEquivalent(w1, w2, l);
}

/**
 * Computes the equivalent nodal load vector for a uniform distributed load over full element length.
 *
 * @param w - Magnitude of the uniform distributed load
 * @param L - Length of the beam element
 * @returns A 4-element nodal load vector [q1, m1, q2, m2]
 */
function getUniformDistributedLoadEquivalent(w: number, L: number): number[] {
  return [
    (w * L) / 2,
    (w * L ** 2) / 12,
    (w * L) / 2,
    -(w * L ** 2) / 12
  ];
}

/**
 * Computes the equivalent nodal load vector for a trapezoidal distributed load (linearly varying).
 *
 * @param w1 - Load magnitude at start of distribution
 * @param w2 - Load magnitude at end of distribution
 * @param l - Length over which the distribution is applied (should be within element length)
 * @returns A 4-element nodal load vector [q1, m1, q2, m2]
 */
function getTrapezoidalDistributedLoadEquivalent(w1: number, w2: number, l: number): number[] {
  const q1 = (l / 20) * (7 * w1 + 3 * w2);
  const m1 = (l ** 2 / 60) * (3 * w1 + 2 * w2);
  const q2 = (l / 20) * (3 * w1 + 7 * w2);
  const m2 = -(l ** 2 / 60) * (2 * w1 + 3 * w2);

  return [q1, m1, q2, m2];
}

/**
 * Maps support conditions to constrained degrees of freedom (DOFs).
 *
 * @param supports - List of support definitions
 * @param nodes - List of nodes along the beam
 * @returns Array of constrained DOF indices (vertical + rotation)
 */
export function getConstrainedDOFs(supports: Support[], nodes: Node[]): number[] {
  const dofs: number[] = [];

  for (const support of supports) {
    const nodeIndex = nodes.findIndex(n => Math.abs(n.x - support.position) < 1e-6);
    if (nodeIndex === -1) throw new Error(`No node at support position ${support.position}`);
    const vertical = nodeIndex * 2;
    const rotation = vertical + 1;

    dofs.push(vertical);
    if (support.hasRotationalRestraint()) {
      dofs.push(rotation);
    }
  }

  return dofs;
}