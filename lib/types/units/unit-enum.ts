import { UnitSystem } from "@/lib/types/units/unit-system";

export enum LengthUnit {
  METER = "m",
  FOOT = "ft",
  MILLIMETER = "mm",
  INCH = "in"
}

export enum ForceUnit {
  NEWTON = "N",
  POUND_FORCE = "lbf",
  KILO_NEWTON = "kN",
  KIP = "kip"
}

export enum PressureUnit {
  PASCAL = "Pa",
  PSI = "psi",
  MEGA_PASCAL = "MPa",
  KSI = "ksi"
}

export enum MomentUnit {
  NEWTON_METER = "N⋅m",
  POUND_FOOT = "lbf⋅ft",
  KILO_NEWTON_METER = "kN⋅m",
  KIP_FOOT = "kip⋅ft"
}

export enum DistributedLoadUnit {
  NEWTON_PER_METER = "N/m",
  POUND_PER_FOOT = "plf",
  KILO_NEWTON_PER_METER = "kN/m",
  KIP_PER_FOOT = "kip/ft"
}

export enum MomentOfInertiaUnit {
  METER4 = "m⁴",
  FOOT4 = "ft⁴",
  MILLIMETER4 = "mm⁴",
  INCH4 = "in⁴"
}

export enum AreaUnit {
  METER2 = "m²",
  FOOT2 = "ft²",
  MILLIMETER2 = "mm²",
  INCH2 = "in²"
}

export enum SectionModulusUnit {
  METER3 = "m³",
  FOOT3 = "ft³",
  MILLIMETER3 = "mm³",
  INCH3 = "in³"
}

export enum TemperatureUnit {
  CELSIUS = "°C",
  FAHRENHEIT = "°F"
}

export const imperialUnits = {
  length: LengthUnit.FOOT,
  force: ForceUnit.KIP,
  moment: MomentUnit.POUND_FOOT,
  deflection: LengthUnit.INCH,
  shear: ForceUnit.POUND_FORCE,
  pressure: PressureUnit.KSI,
  distributedLoad: DistributedLoadUnit.POUND_PER_FOOT,
  momentOfInertia: MomentOfInertiaUnit.INCH4,
  area: AreaUnit.INCH2,
  sectionModulus: SectionModulusUnit.INCH3,
  temperature: TemperatureUnit.FAHRENHEIT,
};

export const metricUnits = {
  length: LengthUnit.METER,
  force: ForceUnit.KILO_NEWTON,
  moment: MomentUnit.KILO_NEWTON_METER,
  deflection: LengthUnit.MILLIMETER,
  shear: ForceUnit.KILO_NEWTON,
  pressure: PressureUnit.MEGA_PASCAL,
  distributedLoad: DistributedLoadUnit.KILO_NEWTON_PER_METER,
  momentOfInertia: MomentOfInertiaUnit.METER4,
  area: AreaUnit.METER2,
  sectionModulus: SectionModulusUnit.METER3,
  temperature: TemperatureUnit.CELSIUS,
};

export interface Units {
  length: LengthUnit;
  size: LengthUnit;
  force: ForceUnit;
  moment: MomentUnit;
  deflection: LengthUnit;
  shear: ForceUnit;
  pressure: PressureUnit;
  distributedLoad: DistributedLoadUnit;
  momentOfInertia: MomentOfInertiaUnit;
  area: AreaUnit;
  sectionModulus: SectionModulusUnit;
  temperature: TemperatureUnit;
}

export function getUnitsBySystem(unitSystem: UnitSystem): Units {
  return {
    length: unitSystem === UnitSystem.METRIC ? LengthUnit.METER : LengthUnit.FOOT,
    force: unitSystem === UnitSystem.METRIC ? ForceUnit.NEWTON : ForceUnit.POUND_FORCE,
    moment: unitSystem === UnitSystem.METRIC ? MomentUnit.NEWTON_METER : MomentUnit.POUND_FOOT,
    deflection: unitSystem === UnitSystem.METRIC ? LengthUnit.MILLIMETER : LengthUnit.INCH,
    shear: unitSystem === UnitSystem.METRIC ? ForceUnit.NEWTON : ForceUnit.POUND_FORCE,
    pressure: unitSystem === UnitSystem.METRIC ? PressureUnit.PASCAL : PressureUnit.PSI,
    distributedLoad: unitSystem === UnitSystem.METRIC ? DistributedLoadUnit.NEWTON_PER_METER : DistributedLoadUnit.POUND_PER_FOOT,
    momentOfInertia: unitSystem === UnitSystem.METRIC ? MomentOfInertiaUnit.METER4 : MomentOfInertiaUnit.INCH4,
    area: unitSystem === UnitSystem.METRIC ? AreaUnit.METER2 : AreaUnit.INCH2,
    sectionModulus: unitSystem === UnitSystem.METRIC ? SectionModulusUnit.METER3 : SectionModulusUnit.INCH3,
    temperature: unitSystem === UnitSystem.METRIC ? TemperatureUnit.CELSIUS : TemperatureUnit.FAHRENHEIT,
    size: unitSystem === UnitSystem.METRIC ? LengthUnit.MILLIMETER : LengthUnit.INCH,
  };
}