import { DataPoint } from "./analysis-results";
import { SupportReactionMap, SupportMomentReactionMap } from "@/lib/types/beam/beam-model";
import { LoadType } from "../load/load-type";
import { DesignCheckResults } from "@/lib/utils/steel/steel-beam-orchestrator";

// Basic structure for max value results (Shear, Moment)
export interface MaxValueResult {
    value: number;
    position: number;
    loadComboName: string;
}

// Structure for ratio results (Stress)
export interface RatioResult {
}

// Structure for diagram data points
export interface DiagramData {
  shear: DataPoint[];
  moment: DataPoint[];
  deflection: DataPoint[];
}

// Structure for deflection results (includes L/delta ratio)
export interface DeflectionResult {
  value: number; // Raw deflection value
  position: number; // Position of the max deflection
  ratio: number; // L / delta
  limit: number | null; // Denominator of the limit (e.g., 240 for L/240)
  loadComboName: string;
}

// Enhanced structure for stress ratio results
export interface StressRatioResult {
  ratio: number;          // The calculated ratio (actual / allowable)
  actualStress: number;   // The calculated maximum actual stress (numerator)
  allowableStress: number;// The allowable stress used (denominator)
  position: number;       // Position of the maximum stress
  spanNumber: number;     // Span number where maximum occurs (if applicable)
  loadComboName: string;  // Controlling load combination name
}

// Structure for steel design analysis results
export interface SteelDesignAnalysis {
  designCheckResults: DesignCheckResults;
  analysisParameters: {
    unbrancedLength: number; // Lb in inches
    lateralTorsionalBucklingModifier: number; // Cb
    designMethod: 'LRFD' | 'ASD';
    stiffnessAdjustmentsApplied: boolean;
    controllingLoadCombinations: {
      flexural: string; // Load combination that controls flexural design
      shear: string;    // Load combination that controls shear design
    };
  };
  success: boolean;
  error?: string;
  warnings?: string[];
}

// Structure for sawn lumber design analysis results  
export interface SawnLumberDesignAnalysis {
  designCheckResults: any; // Results from the sawn lumber analysis API
  analysisParameters: {
    designMethod: 'LRFD' | 'ASD';
    controllingLoadCombinations: {
      flexural: string; // Load combination that controls flexural design
      shear: string;    // Load combination that controls shear design
    };
  };
  success: boolean;
  error?: string;
  warnings?: string[];
}

// Structure for glulam design analysis results
export interface GlulamDesignAnalysis {
  designCheckResults: any; // Results from the glulam analysis API
  analysisParameters: {
    designMethod: 'LRFD' | 'ASD';
    primaryAxis: string; // x_axis or y_axis
    layupType: string; // balanced or unbalanced
    controllingLoadCombinations: {
      flexural: string; // Load combination that controls flexural design
      shear: string;    // Load combination that controls shear design
    };
  };
  success: boolean;
  error?: string;
  warnings?: string[];
}

// Structure for summary results
export interface SummaryData {
  maxBendingStressRatio: StressRatioResult; // Use enhanced type
  maxShearStressRatio: StressRatioResult;   // Use enhanced type
  maxShearValue: MaxValueResult;
  maxMomentValue: MaxValueResult;
  maxTotalDeflectionDownward: DeflectionResult;
  maxTotalDeflectionUpward: DeflectionResult;
  supportReactions: {
    forces: {
      [nodeLabel: string]: {
        maxUp: number;
        maxDown: number;
        maxUpCombo: string;
        maxDownCombo: string;
      };
    };
    moments: {
      [nodeLabel: string]: {
        maxPositive: number;
        maxNegative: number;
        maxPosCombo: string;
        maxNegCombo: string;
      };
    };
  };
  individualLoadReactions?: {
    forces: { [nodeLabel: string]: Partial<Record<LoadType, number>> };
    // Add moments if needed later
    // moments: { [nodeLabel: string]: Partial<Record<LoadType, number>> };
  };
  steelDesign?: SteelDesignAnalysis; // Optional steel design results
  sawnLumberDesign?: SawnLumberDesignAnalysis; // Optional sawn lumber design results
  glulamDesign?: GlulamDesignAnalysis; // Optional glulam design results
}

// Combined analysis results structure
export interface AnalysisOutput {
  diagramData: DiagramData;
  summaryData: SummaryData;
} 