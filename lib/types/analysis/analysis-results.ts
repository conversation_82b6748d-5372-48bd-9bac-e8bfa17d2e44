/**
 * Represents a single data point for plotting diagrams.
 */
export interface DataPoint {
  x: number; // Position along the beam
  value: number; // Calculated value (Shear, Moment, Deflection)
}

/**
 * Represents the different types of analysis results that can be plotted.
 */
export type AnalysisType = "shear" | "moment" | "deflection";

/**
 * Represents the collection of data points for all analysis result types.
 */
export interface AnalysisResults {
  shear: DataPoint[];
  moment: DataPoint[];
  deflection: DataPoint[];
}

// Add chartColors definition
export const chartColors: Record<AnalysisType, string> = {
  shear: "#8884d8", // Example color (purple)
  moment: "#82ca9d", // Example color (green)
  deflection: "#ffc658", // Example color (yellow)
}; 