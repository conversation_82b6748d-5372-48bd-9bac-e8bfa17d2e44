export interface LumberProperties {
  size_classification: string;
  nominal_size_bxd: string;
  nominal_b: number;
  nominal_d: number;
  standard_dressed_size_bxd: string;
  standard_width: number;
  standard_depth: number;
  area_of_section_a_in2: number;
  Sxx: number;  // Section modulus about X-X axis (in³)
  Ixx: number;  // Moment of inertia about X-X axis (in⁴)
  Syy: number;  // Section modulus about Y-Y axis (in³)
  Iyy: number;  // Moment of inertia about Y-Y axis (in⁴)
  weight_25_lbspft3: number;  // Weight at 25 lb/ft³ density
  weight_30_lbspft3: number;  // Weight at 30 lb/ft³ density
  weight_35_lbspft3: number; // Weight at 35 lb/ft³ density
  weight_40_lbspft3: number; // Weight at 40 lb/ft³ density
  weight_45_lbspft3: number; // Weight at 45 lb/ft³ density
  weight_50_lbspft3: number; // Weight at 50 lb/ft³ density
  notes?: string;
  version: string;
}

export interface LumberData {
  sizeClassifications: string[];
  properties: {
    [sizeClassification: string]: LumberProperties[];
  };
}

export const sectionPropertyDescriptions = {
  Sxx: "Section modulus about X-X axis (edgewise bending)",
  Ixx: "Moment of inertia about X-X axis (edgewise bending)",
  Syy: "Section modulus about Y-Y axis (flatwise bending)",
  Iyy: "Moment of inertia about Y-Y axis (flatwise bending)",
};