import { Span } from "./span";
import { Load } from "@/lib/types/load/load";
import { Type } from "@/lib/types/load/load-type";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import { Support } from "@/lib/types/support/support";

/**
 * A class representing a beam fixed at both ends.
 * Supports analysis using formulas from AWC Design Aid 6.
 */
export class FixedFixed extends Span {
  constructor(
    startSupport: Support,
    endSupport: Support,
    E: number,
    I: number,
    totalSpans = 0,
    isFirstSpan = false,
    isLastSpan = false,
    loadCombo?: LoadComboFactor
  ) {
    super(startSupport, endSupport, E, I, totalSpans, isFirstSpan, isLastSpan, loadCombo);
  }

  deflectionAt(x: number): number {
    const E = this.E;
    const I = this.I;
    const L = this.length;
    const xi = x - this.start;
    let delta = 0;

    const loads = this.getAppliedLoads();

    for (const load of loads) {
      if (load.type === Type.POINT) {
        const P = load.startMagnitude!;
        const a = load.startPosition! - this.start;
        const b = L - a;

        if (xi <= a) {
          delta += (P * b * xi * (L ** 2 - b ** 2 - xi ** 2)) / (6 * L ** 3 * E * I);
        } else {
          delta += (P * a * (L - xi) * (2 * L * xi - xi ** 2 - a ** 2)) / (6 * L ** 3 * E * I);
        }
      }

      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition! - this.start;
        const b = load.endPosition! - this.start;
        const dx = (b - a) / 100;

        for (let i = 0; i <= 100; i++) {
          const localX = a + i * dx;
          const t = (localX - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          const P = w * dx;
          const dxi = xi - localX;

          if (dxi >= 0) {
            delta += (P * (L - localX) * dxi * (L ** 2 - (L - localX) ** 2 - dxi ** 2)) / (6 * L ** 3 * E * I);
          }
        }
      }
    }

    return delta;
  }

  maxDeflection(): { position: number; value: number } {
    let maxVal = -Infinity;
    let maxX = this.start;

    for (let x = this.start; x <= this.end; x += this.length / 100) {
      const def = this.deflectionAt(x);
      if (maxVal == -Infinity || Math.abs(def) > Math.abs(maxVal)) {
        maxVal = def;
        maxX = x;
      }
    }

    return { position: maxX, value: maxVal };
  }

  shearAt(x: number): number {
    let V = 0;
    const loads = this.getAppliedLoads();

    for (const load of loads) {
      if (load.type === Type.POINT && x >= load.startPosition!) {
        V -= load.startMagnitude!;
      }

      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition!;
        const b = load.endPosition!;
        const dx = this.length / 100;

        for (let i = a; i <= b && i <= x; i += dx) {
          const t = (i - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          V -= w * dx;
        }
      }
    }

    return this.reactionAtA(loads) + V;
  }

  maxShear(): { position: number; value: number } {
    let maxVal = -Infinity;
    let maxX = this.start;

    for (let x = this.start; x <= this.end; x += this.length / 100) {
      const V = this.shearAt(x);
      if (maxVal == -Infinity || Math.abs(V) > Math.abs(maxVal)) {
        maxVal = V;
        maxX = x;
      }
    }

    return { position: maxX, value: maxVal };
  }

  momentAt(x: number): number {
    let M = 0;
    const loads = this.getAppliedLoads();

    for (const load of loads) {
      if (load.type === Type.POINT && x >= load.startPosition!) {
        M -= load.startMagnitude! * (x - load.startPosition!);
      }

      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition!;
        const b = load.endPosition!;
        const dx = this.length / 100;

        for (let i = a; i <= b && i <= x; i += dx) {
          const t = (i - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          const centroid = i + dx / 2;
          if (x >= centroid) {
            M -= w * dx * (x - centroid);
          }
        }
      }
    }

    return this.reactionAtA(loads) * (x - this.start) + M;
  }

  maxMoment(): { position: number; value: number } {
    let maxVal = -Infinity;
    let maxX = this.start;

    for (let x = this.start; x <= this.end; x += this.length / 100) {
      const M = this.momentAt(x);
      if (maxVal == -Infinity || Math.abs(M) > Math.abs(maxVal)) {
        maxVal = M;
        maxX = x;
      }
    }

    return { position: maxX, value: maxVal };
  }

  private reactionAtA(loads: Load[]): number {
    const L = this.length;
    let RA = 0;

    for (const load of loads) {
      if (load.type === Type.POINT) {
        const a = load.startPosition! - this.start;
        RA += load.startMagnitude! * (1 - a / L);
      }

      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition! - this.start;
        const b = load.endPosition! - this.start;
        const dx = this.length / 100;

        for (let i = a; i <= b; i += dx) {
          const t = (i - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          const centroid = i + dx / 2;
          RA += w * dx * (1 - centroid / L);
        }
      }
    }

    return RA;
  }
}