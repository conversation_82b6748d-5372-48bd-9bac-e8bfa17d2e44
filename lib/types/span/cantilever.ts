import { Span } from "./span";
import { Load } from "@/lib/types/load/load";
import { Type } from "@/lib/types/load/load-type";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import { Support } from "@/lib/types/support/support";

/**
 * A class representing a cantilever beam (fixed at one end, free at the other).
 * Provides methods for deflection, shear, and moment based on AWC Design Aid 6.
 */
export class Cantilever extends Span {
  constructor(
    startSupport: Support,
    endSupport: Support,
    E: number,
    I: number,
    totalSpans = 0,
    isFirstSpan = false,
    isLastSpan = false,
    loadCombo?: LoadComboFactor
  ) {
    super(startSupport, endSupport, E, I, totalSpans, isFirstSpan, isLastSpan, loadCombo);
  }

  deflectionAt(x: number): number {
    const L = this.length;
    const E = this.E;
    const I = this.I;
    let delta = 0;

    const loads = this.getAppliedLoads();

    for (const load of loads) {
      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition!;
        const b = load.endPosition!;
        const dx = this.length / 100;

        for (let i = a; i <= b && i <= x; i += dx) {
          const t = (i - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          const P = w * dx;
          const d = x - i;
          delta -= (P * Math.pow(d, 2)) / (2 * E * I);
        }
      }

      if (load.type === Type.POINT && x >= load.startPosition!) {
        const P = load.startMagnitude!;
        const d = x - load.startPosition!;
        delta -= (P * Math.pow(d, 2)) / (2 * E * I);
      }
    }

    return delta;
  }

  maxDeflection(): { position: number; value: number } {
    let maxVal = -Infinity;
    let maxX = this.end;

    for (let x = this.start; x <= this.end; x += this.length / 100) {
      const def = this.deflectionAt(x);
      if (maxVal == -Infinity || Math.abs(def) > Math.abs(maxVal)) {
        maxVal = def;
        maxX = x;
      }
    }

    return { position: maxX, value: maxVal };
  }

  shearAt(x: number): number {
    let V = 0;
    const loads = this.getAppliedLoads();

    for (const load of loads) {
      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition!;
        const b = load.endPosition!;
        const dx = this.length / 100;

        for (let i = a; i <= b && i <= x; i += dx) {
          const t = (i - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          V -= w * dx;
        }
      }

      if (load.type === Type.POINT && x >= load.startPosition!) {
        V -= load.startMagnitude!;
      }
    }

    return V;
  }

  maxShear(): { position: number; value: number } {
    let maxVal = -Infinity;
    let maxX = this.start;

    for (let x = this.start; x <= this.end; x += this.length / 100) {
      const V = this.shearAt(x);
      if (maxVal == -Infinity || Math.abs(V) > Math.abs(maxVal)) {
        maxVal = V;
        maxX = x;
      }
    }

    return { position: maxX, value: maxVal };
  }

  momentAt(x: number): number {
    let M = 0;
    const loads = this.getAppliedLoads();

    for (const load of loads) {
      if (load.type === Type.POINT && x >= load.startPosition!) {
        const P = load.startMagnitude!;
        M -= P * (x - load.startPosition!);
      }

      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition!;
        const b = load.endPosition!;
        const dx = this.length / 100;

        for (let i = a; i <= b && i <= x; i += dx) {
          const t = (i - a) / (b - a);
          const w = load.startMagnitude! + t * (load.endMagnitude! - load.startMagnitude!);
          const centroid = i + dx / 2;
          if (x >= centroid) {
            M -= w * dx * (x - centroid);
          }
        }
      }
    }

    return M;
  }

  maxMoment(): { position: number; value: number } {
    let maxVal = -Infinity;
    let maxX = this.start;

    for (let x = this.start; x <= this.end; x += this.length / 100) {
      const M = this.momentAt(x);
      if (maxVal == -Infinity || Math.abs(M) > Math.abs(maxVal)) {
        maxVal = M;
        maxX = x;
      }
    }

    return { position: maxX, value: maxVal };
  }
}