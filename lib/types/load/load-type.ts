export enum LoadType {
  DEAD = "dead",          // D
  ROOF_LIVE = "roof_live",     // Lr
  LIVE = "live",          // L
  WIND = "wind",          // W
  SNOW = "snow",          // S
  RAIN = "rain",          // R
  EARTHQUAKE = "earthquake", // E
  SOIL = "soil",          // H
  FLOOD = "flood",        // F
  TEMPERATURE = "temperature", // T (if used)
}

export enum Type {
  POINT = "point",
  DISTRIBUTED = "distributed"
}