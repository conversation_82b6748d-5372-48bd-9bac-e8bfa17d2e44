import { LoadType, Type } from "./load-type";
import { Load } from "./load";
import { Position } from "@/lib/types/common";
import { LoadComboFactor } from "./load-combo-factor";

export class LoadGroup {
  id: string;
  label: string;
  loads: Load[] = [];
  level: number;
  type: Type;
  startPosition: number;
  endPosition?: number;
  tributaryWidth?: number;
  isEditable: boolean;
  isSelfWeight: boolean;
  isLinkedLoad?: boolean;
  linkedSourceCalculationId?: string;
  linkedSourceSupportPosition?: number;

  constructor(
    id: string,
    label: string,
    position: Position,
    startMagnitudes: Partial<Record<LoadType, number>>,
    endMagnitudes: Partial<Record<LoadType, number>>,
    type: Type,
    tributaryWidth?: number,
    isEditable: boolean = true,
    isSelfWeight: boolean = false,
    isLinkedLoad?: boolean,
    linkedSourceCalculationId?: string,
    linkedSourceSupportPosition?: number
  ) {
    this.id = id;
    this.label = label;
    this.level = 0;
    this.type = type;
    this.startPosition = position.start;
    this.endPosition = position.end;
    this.tributaryWidth = tributaryWidth;
    this.isEditable = isEditable;
    this.isSelfWeight = isSelfWeight;
    this.isLinkedLoad = isLinkedLoad;
    this.linkedSourceCalculationId = linkedSourceCalculationId;
    this.linkedSourceSupportPosition = linkedSourceSupportPosition;

    const length = this.endPosition !== undefined && this.type === Type.DISTRIBUTED 
                   ? Math.abs(this.endPosition - this.startPosition)
                   : 0;

    const combinedMagnitudes: Partial<Record<LoadType, {start: number, end: number}>> = {};
    for (const lt of Object.keys(startMagnitudes) as LoadType[]) {
      if (!combinedMagnitudes[lt]) combinedMagnitudes[lt] = { start: 0, end: 0 };
      combinedMagnitudes[lt]!.start = startMagnitudes[lt] ?? 0;
    }
    for (const lt of Object.keys(endMagnitudes) as LoadType[]) {
      if (!combinedMagnitudes[lt]) combinedMagnitudes[lt] = { start: 0, end: 0 };
      combinedMagnitudes[lt]!.end = endMagnitudes[lt] ?? 0;
    }

    for (const loadType of Object.keys(combinedMagnitudes) as LoadType[]) {
      const startMagValue = combinedMagnitudes[loadType]?.start ?? 0;
      const endMagValue = combinedMagnitudes[loadType]?.end ?? (type === Type.POINT ? startMagValue : 0);

      if (startMagValue === 0 && endMagValue === 0) continue;

      let scaledStartMagnitude = startMagValue;
      let scaledEndMagnitude = endMagValue;

      if (this.tributaryWidth && this.tributaryWidth > 0 && type === Type.DISTRIBUTED && length > 0) {
        scaledStartMagnitude *= this.tributaryWidth;
        scaledEndMagnitude *= this.tributaryWidth;
      }
      
      const internalLoadLabel = this.isSelfWeight ? "Self" : loadType;

      const load = new Load({
        label: internalLoadLabel,
        loadType: loadType,
        type,
        startPosition: this.startPosition,
        startMagnitude: scaledStartMagnitude,
        endPosition: type === Type.DISTRIBUTED ? this.endPosition : undefined,
        endMagnitude: type === Type.DISTRIBUTED ? scaledEndMagnitude : undefined,
      });

      this.loads.push(load);
    }
  }

  getLoads(): Load[] {
    return this.loads;
  }

  /**
   * Apply a LoadComboFactor to scale the magnitudes of each load.
   * Returns a new list of scaled Load instances.
   */
  applyFactor(factor: LoadComboFactor): Load[] {
    if (!factor) {
      return this.loads;
    }
    return this.loads.map(load => {
      const coeff = factor.coefficients[0][load.loadType] ?? 0;
      return new Load({
        label: load.label ?? load.loadType,
        loadType: load.loadType,
        type: load.type,
        startPosition: load.startPosition!,
        startMagnitude: (load.startMagnitude ?? 0) * coeff,
        endPosition: load.endPosition,
        endMagnitude: load.endMagnitude !== undefined
          ? load.endMagnitude * coeff
          : undefined
      });
    });
  }

  /**
   * Calculates total magnitude (sum of scaled magnitudes).
   * For distributed loads, integrates the load over its length.
   * For point loads, sums the magnitudes.
   */
  totalMagnitude(factor?: LoadComboFactor): number {
    const applied = factor ? this.applyFactor(factor) : this.loads;
    let total = 0;

    for (const load of applied) {
      if (load.type === Type.POINT) {
        total += Math.abs(load.startMagnitude ?? 0);
      }

      if (load.type === Type.DISTRIBUTED) {
        const a = load.startPosition ?? 0;
        const b = load.endPosition ?? a;
        const w1 = Math.abs(load.startMagnitude ?? 0);
        const w2 = Math.abs(load.endMagnitude ?? w1);
        const length = b - a;
        const area = ((w1 + w2) / 2) * length; // Trapezoidal approximation
        total += area;
      }
    }

    return total;
  }

  getMaxMagnitude(): number {
    return Math.max(...this.loads.map(load => Math.abs(load.startMagnitude ?? 0)));
  }
}