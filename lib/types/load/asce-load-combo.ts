import { LoadComboFactor } from "./load-combo-factor";
import { LoadType } from "./load-type";

export const ASCE_7_10_ASD_ComboMap: Record<string, LoadComboFactor> = {
  "D": new LoadComboFactor({
    [LoadType.DEAD]: 1.0
  }, "D"),

  "D + L": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 1.0
  }, "D + L"),

  "D + Lr": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.ROOF_LIVE]: 1.0
  }, "D + Lr"),

  "D + S": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.SNOW]: 1.0
  }, "D + S"),

  "D + R": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.RAIN]: 1.0
  }, "D + R"),

  "D + 0.75L + 0.75Lr": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.ROOF_LIVE]: 0.75
  }, "D + 0.75L + 0.75Lr"),

  "D + 0.75L + 0.75S": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.SNOW]: 0.75
  }, "D + 0.75L + 0.75S"),

  "D + 0.75L + 0.75R": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.RAIN]: 0.75
  }, "D + 0.75L + 0.75R"),

  "D + 0.6W": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.WIND]: 0.6
  }, "D + 0.6W"),

  "D + 0.7E": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.EARTHQUAKE]: 0.7
  }, "D + 0.7E"),

  "D + 0.75L + 0.45W + 0.75Lr": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.WIND]: 0.45,
    [LoadType.ROOF_LIVE]: 0.75
  }, "D + 0.75L + 0.45W + 0.75Lr"),

  "D + 0.75L + 0.45W + 0.75S": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.WIND]: 0.45,
    [LoadType.SNOW]: 0.75
  }, "D + 0.75L + 0.45W + 0.75S"),

  "D + 0.75L + 0.45W + 0.75R": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.WIND]: 0.45,
    [LoadType.RAIN]: 0.75
  }, "D + 0.75L + 0.45W + 0.75R"),

  "D + 0.75L + 0.525E + 0.75S": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 0.75,
    [LoadType.EARTHQUAKE]: 0.525,
    [LoadType.SNOW]: 0.75
  }, "D + 0.75L + 0.525E + 0.75S"),

  "0.6D + 0.6W": new LoadComboFactor({
    [LoadType.DEAD]: 0.6,
    [LoadType.WIND]: 0.6
  }, "0.6D + 0.6W"),

  "0.6D + 0.7E": new LoadComboFactor({
    [LoadType.DEAD]: 0.6,
    [LoadType.EARTHQUAKE]: 0.7
  }, "0.6D + 0.7E")
};