import { LoadType, Type } from "./load-type";

export class Load {
  type: Type;
  loadType: LoadType;
  startPosition?: number;
  endPosition?: number;
  startMagnitude?: number;
  endMagnitude?: number;
  label?: string;

  constructor(params: {
    label: string;
    loadType: LoadType;
    type: Type;
    startPosition: number;
    startMagnitude: number;
    endPosition?: number;
    endMagnitude?: number;
  }) {
    this.loadType = params.loadType;
    this.type = params.type;
    this.label = params.label;

    if (this.type === Type.POINT) {
      if (params.startPosition === undefined || params.startMagnitude === undefined) {
        throw new Error("Point loads require position and magnitude.");
      }
      this.startPosition = params.startPosition;
      this.endPosition = params.startPosition;
      this.startMagnitude = params.startMagnitude;
      this.endMagnitude = params.startMagnitude;
    }

    if (this.type === Type.DISTRIBUTED) {
      if (
        params.endPosition === undefined ||
        params.endMagnitude === undefined
      ) {
        throw new Error("Distributed loads require start/end position and start/end magnitude.");
      }
      this.startPosition = params.startPosition;
      this.endPosition = params.endPosition;
      this.startMagnitude = params.startMagnitude;
      this.endMagnitude = params.endMagnitude;
    }
  }

  isUniform(): boolean {
    return this.type === Type.DISTRIBUTED && this.startMagnitude === this.endMagnitude;
  }

  isTrapezoid(): boolean {
    return this.type === Type.DISTRIBUTED && this.startMagnitude !== this.endMagnitude;
  }
}
