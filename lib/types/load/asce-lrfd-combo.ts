import { LoadComboFactor } from "./load-combo-factor";
import { LoadType } from "./load-type";

export const ASCE_7_10_LRFD_ComboMap: Record<string, LoadComboFactor> = {
  // Service load combination for deflection checks (D + L)
  "1.0D + 1.0L": new LoadComboFactor({
    [LoadType.DEAD]: 1.0,
    [LoadType.LIVE]: 1.0
  }, "1.0D + 1.0L"),

  // 1. 1.4D
  "1.4D": new LoadComboFactor({
    [LoadType.DEAD]: 1.4
  }, "1.4D"),

  // 2. 1.2D + 1.6L + 0.5(Lr or S or R)
  "1.2D + 1.6L + 0.5Lr": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.LIVE]: 1.6,
    [LoadType.ROOF_LIVE]: 0.5
  }, "1.2D + 1.6L + 0.5Lr"),

  "1.2D + 1.6L + 0.5S": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.LIVE]: 1.6,
    [LoadType.SNOW]: 0.5
  }, "1.2D + 1.6L + 0.5S"),

  "1.2D + 1.6L + 0.5R": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.LIVE]: 1.6,
    [LoadType.RAIN]: 0.5
  }, "1.2D + 1.6L + 0.5R"),

  // 3. 1.2D + 1.6(Lr or S or R) + (L or 0.5W)
  "1.2D + 1.6Lr + L": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.ROOF_LIVE]: 1.6,
    [LoadType.LIVE]: 1.0
  }, "1.2D + 1.6Lr + L"),

  "1.2D + 1.6Lr + 0.5W": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.ROOF_LIVE]: 1.6,
    [LoadType.WIND]: 0.5
  }, "1.2D + 1.6Lr + 0.5W"),

  "1.2D + 1.6S + L": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.SNOW]: 1.6,
    [LoadType.LIVE]: 1.0
  }, "1.2D + 1.6S + L"),

  "1.2D + 1.6S + 0.5W": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.SNOW]: 1.6,
    [LoadType.WIND]: 0.5
  }, "1.2D + 1.6S + 0.5W"),

  "1.2D + 1.6R + L": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.RAIN]: 1.6,
    [LoadType.LIVE]: 1.0
  }, "1.2D + 1.6R + L"),

  "1.2D + 1.6R + 0.5W": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.RAIN]: 1.6,
    [LoadType.WIND]: 0.5
  }, "1.2D + 1.6R + 0.5W"),

  // 4. 1.2D + 1.0W + L + 0.5(Lr or S or R)
  "1.2D + 1.0W + L + 0.5Lr": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.WIND]: 1.0,
    [LoadType.LIVE]: 1.0,
    [LoadType.ROOF_LIVE]: 0.5
  }, "1.2D + 1.0W + L + 0.5Lr"),

  "1.2D + 1.0W + L + 0.5S": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.WIND]: 1.0,
    [LoadType.LIVE]: 1.0,
    [LoadType.SNOW]: 0.5
  }, "1.2D + 1.0W + L + 0.5S"),

  "1.2D + 1.0W + L + 0.5R": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.WIND]: 1.0,
    [LoadType.LIVE]: 1.0,
    [LoadType.RAIN]: 0.5
  }, "1.2D + 1.0W + L + 0.5R"),

  // 5. 0.9D + 1.0W
  "0.9D + 1.0W": new LoadComboFactor({
    [LoadType.DEAD]: 0.9,
    [LoadType.WIND]: 1.0
  }, "0.9D + 1.0W"),

  // Additional combinations for earthquake loads (following similar pattern)
  "1.2D + 1.0E + L + 0.2S": new LoadComboFactor({
    [LoadType.DEAD]: 1.2,
    [LoadType.EARTHQUAKE]: 1.0,
    [LoadType.LIVE]: 1.0,
    [LoadType.SNOW]: 0.2
  }, "1.2D + 1.0E + L + 0.2S"),

  "0.9D + 1.0E": new LoadComboFactor({
    [LoadType.DEAD]: 0.9,
    [LoadType.EARTHQUAKE]: 1.0
  }, "0.9D + 1.0E")
}; 