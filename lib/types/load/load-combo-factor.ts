import { Load } from "./load";
import { LoadType } from "./load-type";

export class LoadComboFactor {
  name: string;
  coefficients: Partial<Record<LoadType, number>>[];

  /**
   * Constructs a LoadComboFactor from a single set of coefficients.
   * @param coefficients - A mapping of LoadType to numeric coefficient.
   * @param name - Optional name for the load combination.
   */
  constructor(coefficients: Partial<Record<LoadType, number>>, name = "custom") {
    this.name = name.trim();
    this.coefficients = [coefficients];
  }

  /**
   * Applies the load combination to a list of loads.
   * Returns one or more variants of the combined loads.
   */
  applyTo(loads: Load[]): Load[][] {
    return this.coefficients.map(coeffMap => {
      return loads.map(load => {
        const coeff = coeffMap[load.loadType] ?? 0;
        return new Load({
          label: load.label || load.loadType,
          loadType: load.loadType,
          type: load.type,
          startPosition: load.startPosition!,
          startMagnitude: (load.startMagnitude ?? 0) * coeff,
          endPosition: load.endPosition,
          endMagnitude: load.endMagnitude !== undefined ? load.endMagnitude * coeff : undefined
        });
      });
    });
  }
}