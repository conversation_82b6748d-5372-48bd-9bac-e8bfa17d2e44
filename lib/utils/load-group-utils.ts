import { LoadType, Type } from "@/lib/types/load/load-type";
import { LoadGroup } from "@/lib/types/load/load-group";
import type { Load } from "@/lib/types/load/load";

export interface LoadGroupForm {
  id: string;
  label: string;
  type: Type;
  startPosition: string;
  endPosition?: string;
  tributaryWidth?: string;
  startMagnitudes: Partial<Record<LoadType, number | string>>;
  endMagnitudes: Partial<Record<LoadType, number | string>>;
  originalLabel?: string;
  isEditable: boolean;
  isSelfWeight: boolean;
  isLinkedLoad?: boolean;
  linkedSourceCalculationId?: string;
  linkedSourceSupportPosition?: number;
  loads?: Load[];
}

/**
 * Creates a new LoadGroupForm with default values
 */
export function createLoadGroupForm(
  beamLength: number,
  options?: {
    isLinkedLoad?: boolean;
    linkedSourceCalculationId?: string;
    linkedSourceSupportPosition?: number;
    label?: string;
    type?: Type;
    startPosition?: string;
    startMagnitudes?: Partial<Record<LoadType, number | string>>;
    isEditable?: boolean;
  }
): LoadGroupForm {
  const type = options?.type || Type.POINT;
  return {
    id: `lg_${Math.random().toString(36).substring(2, 9)}`,
    label: options?.label || "",
    type: type,
    startPosition: options?.startPosition || (beamLength / 2).toString(),
    endPosition: type === Type.DISTRIBUTED ? beamLength.toString() : "",
    tributaryWidth: "1",
    startMagnitudes: options?.startMagnitudes || {},
    endMagnitudes: {},
    isEditable: options?.isEditable !== undefined ? options.isEditable : true,
    isSelfWeight: false,
    isLinkedLoad: options?.isLinkedLoad || false,
    linkedSourceCalculationId: options?.linkedSourceCalculationId,
    linkedSourceSupportPosition: options?.linkedSourceSupportPosition,
  };
}

/**
 * Converts LoadGroups to LoadGroupForms
 */
export function loadGroupsToForms(loadGroups: LoadGroup[]): LoadGroupForm[] {
  return loadGroups.map(lg => {
    const form: LoadGroupForm = {
      id: lg.id,
      label: lg.label,
      originalLabel: lg.label,
      type: lg.type,
      startPosition: lg.startPosition.toString(),
      endPosition: lg.type === Type.DISTRIBUTED ? (lg.endPosition ?? lg.startPosition).toString() : "",
      tributaryWidth: lg.tributaryWidth?.toString() ?? "1",
      startMagnitudes: {},
      endMagnitudes: {},
      isEditable: lg.isEditable,
      isSelfWeight: lg.isSelfWeight,
      isLinkedLoad: lg.isLinkedLoad || false,
      linkedSourceCalculationId: lg.linkedSourceCalculationId,
      linkedSourceSupportPosition: lg.linkedSourceSupportPosition,
      loads: lg.loads,
    };

    lg.loads.forEach(load => {
      // Unscale the magnitudes to show the original user input values
      // The LoadGroup constructor scales magnitudes by tributary width, so we need to divide by it
      let unscaledStartMagnitude = load.startMagnitude ?? 0;
      let unscaledEndMagnitude = load.endMagnitude ?? load.startMagnitude ?? 0;
      
      if (lg.tributaryWidth && lg.tributaryWidth > 0 && lg.type === Type.DISTRIBUTED) {
        unscaledStartMagnitude = unscaledStartMagnitude / lg.tributaryWidth;
        unscaledEndMagnitude = unscaledEndMagnitude / lg.tributaryWidth;
      }
      
      form.startMagnitudes[load.loadType] = unscaledStartMagnitude.toString();
      if (lg.type === Type.DISTRIBUTED) {
        form.endMagnitudes[load.loadType] = unscaledEndMagnitude.toString();
      } else {
        form.endMagnitudes[load.loadType] = unscaledStartMagnitude.toString();
      }
    });
    return form;
  });
}

/**
 * Creates a LoadGroup from a LoadGroupForm
 */
export function createLoadGroupFromForm(form: LoadGroupForm): LoadGroup | null {
  const startMags: Partial<Record<LoadType, number>> = {};
  const endMags: Partial<Record<LoadType, number>> = {};
  let hasMagnitudes = false;

  Object.entries(form.startMagnitudes).forEach(([ltStr, val]) => {
    const loadType = ltStr as LoadType;
    const numVal = typeof val === 'string' ? parseFloat(val) : val;
    if (numVal !== undefined && isFinite(numVal) && numVal !== 0) {
      startMags[loadType] = numVal;
      hasMagnitudes = true;
    }
  });

  if (form.type === Type.DISTRIBUTED) {
    Object.entries(form.endMagnitudes).forEach(([ltStr, val]) => {
      const loadType = ltStr as LoadType;
      const numVal = typeof val === 'string' ? parseFloat(val) : val;
      if (numVal !== undefined && isFinite(numVal) && numVal !== 0) {
        endMags[loadType] = numVal;
        hasMagnitudes = true;
      }
    });
  } else {
    Object.keys(startMags).forEach(lt => {
      const loadType = lt as LoadType;
      if (startMags[loadType] !== undefined) {
        endMags[loadType] = startMags[loadType];
      }
    });
  }

  if (!hasMagnitudes) return null;

  const parsedStartPosition = parseFloat(form.startPosition);
  const parsedEndPosition = form.type === Type.DISTRIBUTED && form.endPosition ? parseFloat(form.endPosition) : parsedStartPosition;
  
  if (isNaN(parsedStartPosition) || isNaN(parsedEndPosition)) return null;

  return new LoadGroup(
    form.id,
    form.label,
    {
      start: parsedStartPosition,
      end: parsedEndPosition,
    },
    startMags,
    endMags,
    form.type,
    form.tributaryWidth ? parseFloat(form.tributaryWidth) : undefined,
    form.isEditable,
    form.isSelfWeight,
    form.isLinkedLoad,
    form.linkedSourceCalculationId,
    form.linkedSourceSupportPosition
  );
}

/**
 * Updates a LoadGroupForm with new values
 */
export function updateLoadGroupForm(
  form: LoadGroupForm,
  updates: Partial<LoadGroupForm>
): LoadGroupForm {
  const updatedForm = { ...form, ...updates };

  if (updates.type === Type.DISTRIBUTED && form.type === Type.POINT) {
    updatedForm.endMagnitudes = { ...updatedForm.startMagnitudes };
  }

  return updatedForm;
}

/**
 * Filters out LoadGroups by ID (previously by label)
 */
export function filterLoadGroupsById(
  loadGroups: LoadGroup[],
  idToRemove: string | undefined
): LoadGroup[] {
  if (!idToRemove) return loadGroups;
  return loadGroups.filter(loadGroup => loadGroup.id !== idToRemove);
}

/**
 * Filters out LoadGroups with a specific label. 
 * This might be used for replacing groups if IDs are not stable across form edits.
 */
export function filterLoadGroupsByLabel(
  loadGroups: LoadGroup[],
  labelToFilter: string | undefined
): LoadGroup[] {
  if (!labelToFilter) return loadGroups;
  return loadGroups.filter(lg => lg.label !== labelToFilter);
}

/**
 * Reconstructs LoadGroup instances from JSON-parsed data
 * This is needed because JSON.parse() creates plain objects without LoadGroup methods
 */
export function reconstructLoadGroupsFromJson(parsedData: any[]): LoadGroup[] {
  if (!Array.isArray(parsedData)) {
    console.warn("reconstructLoadGroupsFromJson: parsedData is not an array:", parsedData);
    return [];
  }

  return parsedData.map(data => {
    try {
      // Extract magnitudes from the loads array
      const startMagnitudes: Partial<Record<LoadType, number>> = {};
      const endMagnitudes: Partial<Record<LoadType, number>> = {};

      if (data.loads && Array.isArray(data.loads)) {
        data.loads.forEach((load: any) => {
          if (load.loadType && typeof load.startMagnitude === 'number') {
            startMagnitudes[load.loadType as LoadType] = load.startMagnitude;
            endMagnitudes[load.loadType as LoadType] = load.endMagnitude || load.startMagnitude;
          }
        });
      }

      // Create a new LoadGroup instance with the reconstructed data
      return new LoadGroup(
        data.id || `lg_${Math.random().toString(36).substring(2, 9)}`,
        data.label || "",
        {
          start: data.startPosition || 0,
          end: data.endPosition || data.startPosition || 0,
        },
        startMagnitudes,
        endMagnitudes,
        data.type || Type.POINT,
        data.tributaryWidth,
        data.isEditable !== undefined ? data.isEditable : true,
        data.isSelfWeight || false,
        data.isLinkedLoad || false,
        data.linkedSourceCalculationId,
        data.linkedSourceSupportPosition
      );
    } catch (error) {
      console.error("Error reconstructing LoadGroup from JSON data:", error, data);
      // Return a fallback empty LoadGroup to prevent crashes
      return new LoadGroup(
        `lg_${Math.random().toString(36).substring(2, 9)}`,
        "Error Load",
        { start: 0, end: 0 },
        {},
        {},
        Type.POINT
      );
    }
  }).filter(lg => lg.loads.length > 0); // Filter out empty load groups
}