// import { CanvasRenderingContext2D } from "canvas";
import { LoadGroup } from "@/lib/types/load/load-group";
import { LoadType, Type } from "@/lib/types/load/load-type";

interface LoadDrawingConfig {
  ctx: CanvasRenderingContext2D;
  margin: number;
  scale: number;
  centerY: number;
  baseArrowHeight: number;
  levelPadding: number;
  maxMagnitude: number;
  loadTypeColors: Record<LoadType, string>;
  loadTypeSymbols: Record<LoadType, string>;
}

export function drawLoadLabel(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  label: string,
  description: string,
  color: string,
  align: CanvasTextAlign = "left"
) {
  ctx.font = "12px Arial";
  ctx.fillStyle = color;
  ctx.textAlign = align;
  const labelText = label !== 'default' ? `${label}: ${description}` : description;
  ctx.fillText(labelText, x, y);
}

export function drawArrowhead(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  color: string,
  size: number = 5
) {
  ctx.beginPath();
  ctx.moveTo(x, y);
  ctx.lineTo(x - size, y - size * 2);
  ctx.lineTo(x + size, y - size * 2);
  ctx.closePath();
  ctx.fillStyle = color;
  ctx.fill();
}

export function drawPointLoad(
  config: LoadDrawingConfig,
  group: LoadGroup,
  levelOffset: number,
  groupsWithLevels: LoadGroup[]
) {
  const { ctx, margin, scale, centerY, baseArrowHeight, levelPadding, maxMagnitude, loadTypeColors } = config;
  const x = margin + group.startPosition * scale;
  
  const groupMaxMag = Math.max(...(group.loads || []).map(load => Math.abs(load.startMagnitude ?? 0)), 0);
  const heightScale = maxMagnitude > 1e-6 ? groupMaxMag / maxMagnitude : 1;
  const arrowHeight = Math.max(10, Math.min(baseArrowHeight * heightScale, 20));
  
  const startY = centerY - levelOffset;
  
  // Draw arrow shaft
  ctx.beginPath();
  ctx.moveTo(x, startY - arrowHeight);
  ctx.lineTo(x, startY);
  ctx.strokeStyle = loadTypeColors[group.loads[0].loadType];
  ctx.lineWidth = 2;
  ctx.stroke();

  // Draw arrowhead
  drawArrowhead(ctx, x, startY, loadTypeColors[group.loads[0].loadType]);

  // Draw label
  const loadDescription = formatLoadDescription(group, config.loadTypeSymbols);
  drawLoadLabel(
    ctx,
    x + 5,
    startY - arrowHeight - 5,
    group.loads[0].label || 'default',
    loadDescription,
    loadTypeColors[group.loads[0].loadType]
  );
}

export function drawDistributedLoad(
  config: LoadDrawingConfig,
  group: LoadGroup,
  levelOffset: number
) {
  const { ctx, margin, scale, centerY, baseArrowHeight, maxMagnitude, loadTypeColors } = config;
  const startPos = group.startPosition ?? group.loads[0]?.startPosition ?? 0;
  const endPos = group.endPosition ?? group.loads[0]?.endPosition ?? startPos;
  const loadType = group.type === Type.DISTRIBUTED ? group.loads[0]?.loadType : LoadType.DEAD;
  
  const startX = margin + startPos * scale;
  const endX = margin + endPos * scale;
  
  const groupMaxMag = Math.max(...(group.loads || []).map(load => Math.abs(load.startMagnitude ?? 0)), 0);
  const heightScale = maxMagnitude > 1e-6 ? groupMaxMag / maxMagnitude : 1;
  const arrowHeight = Math.max(10, Math.min(baseArrowHeight * heightScale, 20));
  
  // Draw distributed arrows
  const arrowSpacing = 30;
  const arrowCount = Math.floor((endX - startX) / arrowSpacing);
  
  for (let i = 0; i <= arrowCount; i++) {
    const x = startX + (i * (endX - startX)) / arrowCount;
    
    // Draw arrow shaft
    ctx.beginPath();
    ctx.moveTo(x, centerY - arrowHeight - levelOffset);
    ctx.lineTo(x, centerY - levelOffset);
    ctx.strokeStyle = loadTypeColors[loadType];
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw arrowhead
    drawArrowhead(ctx, x, centerY - levelOffset, loadTypeColors[loadType], 3);
  }

  // Draw horizontal line
  ctx.beginPath();
  ctx.moveTo(startX, centerY - arrowHeight - levelOffset);
  ctx.lineTo(endX, centerY - arrowHeight - levelOffset);
  ctx.strokeStyle = loadTypeColors[loadType];
  ctx.lineWidth = 2;
  ctx.stroke();

  // Draw label
  const label = group.loads?.[0]?.label || 'default';
  const description = formatLoadDescription(group, config.loadTypeSymbols);
  const color = loadTypeColors[loadType];
  
  drawLoadLabel(
    ctx,
    (startX + endX) / 2,
    centerY - arrowHeight - levelOffset - 5,
    label,
    description,
    color,
    "center"
  );
}

function formatLoadDescription(group: LoadGroup, loadTypeSymbols: Record<LoadType, string>): string {
  return group.loads
    .filter(load => load.startMagnitude !== 0)
    .map(load => `${loadTypeSymbols[load.loadType]}(${load.startMagnitude!.toFixed(0)})`)
    .join(", ");
}

function findOverlappingDistributedLoad(x: number, currentGroup: LoadGroup, groups: LoadGroup[]): number | null {
  let maxHeight = 0;
  
  for (const group of groups) {
    if (group === currentGroup) continue;
    if (group.type === Type.DISTRIBUTED) {
      const startPos = group.startPosition ?? 0;
      const endPos = group.endPosition ?? startPos;
      if (x >= startPos && x <= endPos) {
        const groupMaxMag = Math.max(...(group.loads || []).map(load => Math.abs(load.startMagnitude ?? 0)), 0);
        maxHeight = Math.max(maxHeight, groupMaxMag);
      }
    }
  }
  
  return maxHeight > 0 ? maxHeight : null;
}