# Steel Beam Design Utilities

A comprehensive TypeScript library for steel beam design calculations according to AISC 360-22 (Specification for Structural Steel Buildings). This library provides utilities for calculating flexural strength, shear strength, slenderness classification, and performing complete design checks using both LRFD and ASD methods.

## Features

- **Complete Steel Beam Design**: Perform comprehensive design checks for flexure and shear
- **Deflection Checks**: Serviceability checks for both upward and downward deflections
- **AISC 360-22 Compliance**: Fully compliant with the latest AISC specifications
- **LRFD & ASD Support**: Both Load and Resistance Factor Design and Allowable Strength Design methods
- **Multiple Section Types**: Support for W-shapes, I-shapes, HSS (rectangular and round), and more
- **Stiffness Adjustments**: Optional AISC 360-22 Section C2.3 stiffness reduction factors
- **Comprehensive Validation**: Input validation with detailed error messages
- **TypeScript Support**: Full TypeScript definitions for type safety

## Quick Start

### Basic Design Check

```typescript
import { 
  getDesignCheckResults, 
  performLRFDDesignCheck,
  performASDDesignCheck,
  DesignMethod 
} from './steel-beam-orchestrator';
import { Material } from './constants';

// Define section properties (W18x50 beam)
const section = {
  name: "W18X50",
  Type: "W" as const,
  A: 14.7,        // Cross-sectional area (in²)
  d: 18.0,        // Depth (in)
  tw: 0.355,      // Web thickness (in)
  bf: 7.5,        // Flange width (in)
  tf: 0.57,       // Flange thickness (in)
  Ix: 800,        // Strong-axis moment of inertia (in⁴)
  Zx: 101,        // Strong-axis plastic section modulus (in³)
  Sx: 88.9,       // Strong-axis elastic section modulus (in³)
  rx: 7.38,       // Strong-axis radius of gyration (in)
  Iy: 40.1,       // Weak-axis moment of inertia (in⁴)
  Zy: 18.7,       // Weak-axis plastic section modulus (in³)
  Sy: 10.7,       // Weak-axis elastic section modulus (in³)
  ry: 1.65,       // Weak-axis radius of gyration (in)
  J: 1.43,        // Torsional constant (in⁴)
  Cw: 1910,       // Warping constant (in⁶)
};

// Define material properties (A992 Grade 50)
const material: Material = { 
  Fy: 50,  // Yield strength (ksi)
  Fu: 65   // Ultimate strength (ksi)
};

// Perform LRFD design check
const results = performLRFDDesignCheck(
  section,
  material,
  120,    // Unbraced length Lb (in)
  3000,   // Required moment Mu (kip-in)
  100,    // Required shear Vu (kips)
  1.0     // Lateral-torsional buckling factor Cb (optional, defaults to 1.0)
);

console.log(`Flexural DCR: ${results.flexure.dcr.toFixed(3)}`);
console.log(`Shear DCR: ${results.shear.dcr.toFixed(3)}`);
console.log(`Design Adequate: ${results.overallAdequate}`);
```

### Comprehensive Design Check with Advanced Options

```typescript
import { BeamAnalysisInputs, DesignCheckResults } from './steel-beam-orchestrator';

const inputs: BeamAnalysisInputs = {
  section: section,
  material: material,
  ltbParameters: {
    Lb: 144,      // Unbraced length (in)
    Cb: 1.3       // Lateral-torsional buckling modification factor
  },
  demandForces: {
    Mu: 3500,     // Required flexural strength (kip-in)
    Vu: 120,      // Required shear strength (kips)
    Pu: 50        // Required axial strength (kips) - optional
  },
  designMethod: DesignMethod.LRFD,
  applyStiffnessAdjustments: true,    // Apply AISC 360-22 Section C2.3 adjustments
  nominalAxialStrength: 200           // Required if applyStiffnessAdjustments is true
};

const results: DesignCheckResults = getDesignCheckResults(inputs);

// Access detailed results
console.log(`Section: ${results.sectionName}`);
console.log(`Material: ${results.materialGrade}`);
console.log(`Design Method: ${results.designMethod}`);

// Flexural results  
console.log(`\nFlexural Design:`);
console.log(`  Nominal Strength: ${results.flexure.nominalStrength.toFixed(1)} kip-in`);
console.log(`  Available Strength: ${results.flexure.availableStrength.toFixed(1)} kip-in`);
console.log(`  Governing Limit State: ${results.flexure.governingLimitState}`);
console.log(`  DCR: ${results.flexure.dcr.toFixed(3)}`);
console.log(`  Adequate: ${results.flexure.adequate}`);

// Shear results
console.log(`\nShear Design:`);
console.log(`  Nominal Strength: ${results.shear.nominalStrength.toFixed(1)} kips`);
console.log(`  Available Strength: ${results.shear.availableStrength.toFixed(1)} kips`);
console.log(`  Governing Limit State: ${results.shear.governingLimitState}`);
console.log(`  DCR: ${results.shear.dcr.toFixed(3)}`);
console.log(`  Adequate: ${results.shear.adequate}`);

// Stiffness adjustments (if applied)
if (results.stiffnessAdjustments.applied) {
  console.log(`\nStiffness Adjustments:`);
  console.log(`  Base Reduction Factor: ${results.stiffnessAdjustments.baseReductionFactor}`);
  console.log(`  Tau-B Factor: ${results.stiffnessAdjustments.tauB.toFixed(3)}`);
  console.log(`  Total Flexural Reduction: ${results.stiffnessAdjustments.totalFlexuralReduction.toFixed(3)}`);
}
```

### ASD Design Check

```typescript
// Perform ASD design check with service loads
const asdResults = performASDDesignCheck(
  section,
  material,
  120,    // Unbraced length Lb (in)
  2000,   // Service moment Ma (kip-in)
  75,     // Service shear Va (kips)
  1.2     // Cb factor (optional)
);

// ASD uses safety factors (Ω) instead of resistance factors (φ)
console.log(`Flexural Safety Factor: ${asdResults.flexure.resistanceFactor}`); // Ω = 1.67
console.log(`Available Strength: ${asdResults.flexure.availableStrength.toFixed(1)} kip-in`);
```

### Design Check with Deflection

```typescript
import { 
  performLRFDDesignCheckWithDeflection,
  performASDDesignCheckWithDeflection 
} from './steel-beam-orchestrator';

// LRFD design check with deflection
const lrfdResults = performLRFDDesignCheckWithDeflection(
  section,
  material,
  120,    // Unbraced length Lb (in)
  3000,   // Required moment Mu (kip-in)
  100,    // Required shear Vu (kips)
  240,    // Beam length (in) - 20 feet
  -0.8,   // Max upward deflection (in) - negative value
  0.9,    // Max downward deflection (in) - positive value
  1.0,    // Cb factor (optional)
  240     // Deflection limit (L/240) - optional, defaults to 240
);

// ASD design check with deflection (office floor beam)
const asdResults = performASDDesignCheckWithDeflection(
  section,
  material,
  120,    // Unbraced length Lb (in)
  2000,   // Service moment Ma (kip-in)
  75,     // Service shear Va (kips)
  360,    // Beam length (in) - 30 feet
  -0.7,   // Max upward deflection (in)
  1.0,    // Max downward deflection (in)
  1.2,    // Cb factor
  360     // L/360 for floors
);

// Check deflection results
console.log(`Upward Deflection: ${lrfdResults.deflection?.upward.adequate ? 'OK' : 'FAIL'}`);
console.log(`Downward Deflection: ${lrfdResults.deflection?.downward.adequate ? 'OK' : 'FAIL'}`);
console.log(`Overall Deflection: ${lrfdResults.deflection?.overallAdequate ? 'OK' : 'FAIL'}`);
console.log(`Controlling Direction: ${lrfdResults.deflection?.controllingDirection}`);
```

## API Reference

### Main Functions

#### `getDesignCheckResults(inputs: BeamAnalysisInputs): DesignCheckResults`

Comprehensive steel beam design check function that orchestrates all calculations.

**Parameters:**
- `inputs`: Complete beam analysis input object

**Returns:** Complete design check results including flexure, shear, and stiffness adjustments

#### `performLRFDDesignCheck(section, material, Lb, Mu, Vu, Cb?): DesignCheckResults`

Simplified LRFD design check interface.

**Parameters:**
- `section`: Section properties object
- `material`: Material properties object  
- `Lb`: Unbraced length (in)
- `Mu`: Required moment (kip-in)
- `Vu`: Required shear (kips)
- `Cb`: Lateral-torsional buckling factor (optional, defaults to 1.0)

#### `performASDDesignCheck(section, material, Lb, Ma, Va, Cb?): DesignCheckResults`

Simplified ASD design check interface.

**Parameters:**
- `section`: Section properties object
- `material`: Material properties object
- `Lb`: Unbraced length (in)
- `Ma`: Service moment (kip-in)
- `Va`: Service shear (kips)
- `Cb`: Lateral-torsional buckling factor (optional, defaults to 1.0)

#### `performLRFDDesignCheckWithDeflection(section, material, Lb, Mu, Vu, beamLength, maxUpwardDeflection, maxDownwardDeflection, Cb?, deflectionLimit?): DesignCheckResults`

LRFD design check with deflection serviceability check.

**Parameters:**
- `section`: Section properties object
- `material`: Material properties object  
- `Lb`: Unbraced length (in)
- `Mu`: Required moment (kip-in)
- `Vu`: Required shear (kips)
- `beamLength`: Beam length for deflection check (in)
- `maxUpwardDeflection`: Maximum upward deflection (in, negative value)
- `maxDownwardDeflection`: Maximum downward deflection (in, positive value)
- `Cb`: Lateral-torsional buckling factor (optional, defaults to 1.0)
- `deflectionLimit`: L/deflectionLimit (optional, defaults to 240)

#### `performASDDesignCheckWithDeflection(section, material, Lb, Ma, Va, beamLength, maxUpwardDeflection, maxDownwardDeflection, Cb?, deflectionLimit?): DesignCheckResults`

ASD design check with deflection serviceability check.

**Parameters:**
- `section`: Section properties object
- `material`: Material properties object
- `Lb`: Unbraced length (in)
- `Ma`: Service moment (kip-in)
- `Va`: Service shear (kips)
- `beamLength`: Beam length for deflection check (in)
- `maxUpwardDeflection`: Maximum upward deflection (in, negative value)
- `maxDownwardDeflection`: Maximum downward deflection (in, positive value)
- `Cb`: Lateral-torsional buckling factor (optional, defaults to 1.0)
- `deflectionLimit`: L/deflectionLimit (optional, defaults to 240)

### Key Interfaces

#### `BeamAnalysisInputs`
```typescript
interface BeamAnalysisInputs {
  section: Section;                      // Cross-section properties
  material: Material;                    // Material properties (Fy, Fu)
  ltbParameters: LTBParameters;          // Lateral-torsional buckling parameters
  demandForces: DemandForces;           // Applied demand forces
  designMethod: DesignMethod;           // LRFD or ASD
  applyStiffnessAdjustments?: boolean;  // Apply AISC 360-22 Section C2.3 adjustments
  nominalAxialStrength?: number;        // Pn for stiffness adjustments
  deflectionParameters?: DeflectionParameters; // Optional deflection check parameters
}
```

#### `DesignCheckResults`
```typescript
interface DesignCheckResults {
  flexure: FlexuralDesignResult;         // Flexural design results
  shear: ShearDesignResult;             // Shear design results
  stiffnessAdjustments: StiffnessAdjustmentSummary; // Stiffness adjustment details
  deflection?: DeflectionCheckResult;   // Optional deflection check results
  overallAdequate: boolean;             // Overall design adequacy (includes deflection if checked)
  designMethod: DesignMethod;           // Design method used
  sectionName: string;                  // Section designation
  materialGrade: string;                // Material grade description
}
```

#### `Section`
```typescript
interface Section {
  name: string;           // Section designation (e.g., "W18X50")
  Type: SectionType;      // Section type ("W", "HSS-RECT", etc.)
  A: number;              // Cross-sectional area (in²)
  d: number;              // Depth (in)
  tw: number;             // Web thickness (in)
  bf: number;             // Flange width (in)
  tf: number;             // Flange thickness (in)
  Ix?: number;            // Strong-axis moment of inertia (in⁴)
  Zx?: number;            // Strong-axis plastic section modulus (in³)
  Sx?: number;            // Strong-axis elastic section modulus (in³)
  rx?: number;            // Strong-axis radius of gyration (in)
  Iy?: number;            // Weak-axis moment of inertia (in⁴)
  Zy?: number;            // Weak-axis plastic section modulus (in³)
  Sy?: number;            // Weak-axis elastic section modulus (in³)
  ry?: number;            // Weak-axis radius of gyration (in)
  J?: number;             // Torsional constant (in⁴)
  Cw?: number;            // Warping constant (in⁶)
}
```

#### `DeflectionParameters`
```typescript
interface DeflectionParameters {
  beamLength: number;           // Beam length in inches
  maxUpwardDeflection: number;  // Maximum upward deflection in inches (negative value)
  maxDownwardDeflection: number; // Maximum downward deflection in inches (positive value)
  deflectionLimit?: number;     // L/deflectionLimit (defaults to 240)
}
```

#### `DeflectionCheckResult`
```typescript
interface DeflectionCheckResult {
  upward: SingleDeflectionResult;      // Upward deflection check
  downward: SingleDeflectionResult;    // Downward deflection check
  overallAdequate: boolean;           // True if both directions are adequate
  controllingDirection: "upward" | "downward" | "both"; // Which direction controls
  deflectionLimit: number;            // Applied deflection limit (L/limit)
  beamLength: number;                 // Beam length used in calculation
}
```

#### `SingleDeflectionResult`
```typescript
interface SingleDeflectionResult {
  actualDeflection: number;     // Actual deflection value in inches
  allowableDeflection: number;  // Allowable deflection in inches
  deflectionRatio: number;      // Actual/allowable ratio
  lOverDelta: number;          // L/Δ ratio (beam length / deflection)
  adequate: boolean;           // True if deflection is within limits
}
```

## Supported Section Types

- **W-Shapes**: Wide flange beams (W18X50, W24X68, etc.)
- **I-Shapes**: Standard I-beams
- **S-Shapes**: Standard beams  
- **M-Shapes**: Miscellaneous shapes
- **C-Shapes**: Channels
- **MC-Shapes**: Miscellaneous channels
- **HSS-RECT**: Rectangular hollow structural sections
- **HSS-ROUND**: Round hollow structural sections

## Design Methods

### LRFD (Load and Resistance Factor Design)
- Uses resistance factors (φ) applied to nominal strengths
- Flexure: φ = 0.90
- Shear Yielding: φ = 1.00  
- Shear Buckling: φ = 0.90

### ASD (Allowable Strength Design)
- Uses safety factors (Ω) to reduce nominal strengths
- Flexure: Ω = 1.67
- Shear Yielding: Ω = 1.67
- Shear Buckling: Ω = 1.67

## Limit States Evaluated

### Flexural Limit States
- **Yielding**: AISC F2.1
- **Lateral-Torsional Buckling**: AISC F2.2  
- **Flange Local Buckling**: AISC F3
- **Web Local Buckling**: AISC F4/F5
- **HSS Local Buckling**: AISC F7

### Shear Limit States
- **Shear Yielding**: AISC G2.1(a)
- **Shear Buckling**: AISC G2.1(b)

## Deflection Limits

The library supports common deflection serviceability criteria:

### Default Deflection Limits
- **General/Total Load**: L/240
- **Live Load**: L/360
- **Floors**: L/360
- **Roofs**: L/240
- **Cantilevers**: L/120

### Recommended Usage by Application
```typescript
import { getRecommendedDeflectionLimit } from './deflection-utils';

const floorLimit = getRecommendedDeflectionLimit("floor");     // Returns 360
const roofLimit = getRecommendedDeflectionLimit("roof");       // Returns 240
const cantileverLimit = getRecommendedDeflectionLimit("cantilever"); // Returns 120
const generalLimit = getRecommendedDeflectionLimit("general"); // Returns 240
```

### Deflection Check Features
- **Bidirectional Checks**: Separate evaluation of upward and downward deflections
- **Flexible Limits**: Customizable L/deflectionLimit ratios
- **Comprehensive Results**: Deflection ratios, L/Δ values, and controlling directions
- **Integration**: Seamless integration with strength checks for overall design adequacy

## Stiffness Adjustments

The library supports optional stiffness adjustments per AISC 360-22 Section C2.3 for members subject to combined axial and flexural forces:

```typescript
// Enable stiffness adjustments
const inputs: BeamAnalysisInputs = {
  // ... other parameters
  applyStiffnessAdjustments: true,
  nominalAxialStrength: 200,  // Required when adjustments are enabled
  demandForces: {
    Mu: 3000,
    Vu: 100,
    Pu: 50  // Required when adjustments are enabled
  }
};
```

The adjustments include:
- Base reduction factor of 0.80 for flexural stiffness
- Additional τB factor based on axial load ratio
- Adjusted moment of inertia values for deflection calculations

## Module Structure

The library is organized into specialized utility modules:

- **`steel-beam-orchestrator.ts`**: Main orchestration and design check functions
- **`flexural-strength-utils.ts`**: Flexural strength calculations per AISC Chapter F
- **`shear-strength-utils.ts`**: Shear strength calculations per AISC Chapter G  
- **`slenderness-utils.ts`**: Slenderness classification per AISC Table B4.1
- **`deflection-utils.ts`**: Deflection serviceability checks and limits
- **`stiffness-adjustment-utils.ts`**: Stiffness reduction factors per AISC C2.3
- **`analysis-utils.ts`**: Design check utilities and resistance factors
- **`constants.ts`**: Material constants, enums, and lookup tables

## Error Handling

The library provides comprehensive input validation with descriptive error messages:

```typescript
try {
  const results = getDesignCheckResults(inputs);
} catch (error) {
  console.error(error.message);
  // Examples:
  // "section is required in beamAnalysisInputs."
  // "demandForces.Mu must be a non-negative number."
  // "ltbParameters.Lb must be a non-negative number."
}
```

## Units

All calculations use **US Customary Units**:
- Forces: kips (k)
- Moments: kip-inches (kip-in)  
- Lengths: inches (in)
- Stresses: ksi (kips per square inch)
- Areas: square inches (in²)
- Section moduli: cubic inches (in³)
- Moments of inertia: quartic inches (in⁴)

## Testing

The library includes comprehensive test suites with over 95% code coverage:

```bash
# Run all steel utility tests
npm test -- lib/utils/steel/

# Run specific test files
npm test steel-beam-orchestrator.test.ts
npm test flexural-strength-utils.test.ts
npm test shear-strength-utils.test.ts
```

## References

- ANSI/AISC 360-22: Specification for Structural Steel Buildings
- AISC Steel Construction Manual, 16th Edition
- AISC Design Guide 25: Frame Design Using Web-Tapered Members

## License

This library is designed for educational and professional use in structural engineering applications. Users are responsible for verifying results and ensuring compliance with local building codes and engineering judgment. 