/**
 * @file Tests for Steel Beam Deflection Check Utilities
 */

import {
  checkDeflection,
  performSimpleDeflectionCheck,
  getRecommendedDeflectionLimit,
  DEFAULT_DEFLECTION_LIMITS,
  DeflectionInputs,
  DeflectionCheckResult,
  SingleDeflectionResult,
} from "./deflection/index";
import { Section } from "./slenderness/types";
import { Material } from "./constants";

// Test section (W18x50)
const testSection: Section = {
  name: "W18X50",
  Type: "W",
  A: 14.7,
  d: 18.0,
  tw: 0.355,
  bf: 7.5,
  tf: 0.57,
  Ix: 800,
  Zx: 101,
  Sx: 88.9,
  rx: 7.38,
  Iy: 40.1,
  Zy: 18.7,
  Sy: 10.7,
  ry: 1.65,
  J: 1.43,
  Cw: 1910,
};

// Test material (A992 Grade 50)
const testMaterial: Material = {
  Fy: 50,
  Fu: 65,
};

describe("Deflection Utils", () => {
  describe("DEFAULT_DEFLECTION_LIMITS", () => {
    test("should have correct default values", () => {
      expect(DEFAULT_DEFLECTION_LIMITS.TOTAL).toBe(240);
      expect(DEFAULT_DEFLECTION_LIMITS.LIVE).toBe(360);
      expect(DEFAULT_DEFLECTION_LIMITS.FLOORS).toBe(360);
      expect(DEFAULT_DEFLECTION_LIMITS.ROOFS).toBe(240);
      expect(DEFAULT_DEFLECTION_LIMITS.CANTILEVER).toBe(120);
    });
  });

  describe("getRecommendedDeflectionLimit", () => {
    test("should return correct limits for different applications", () => {
      expect(getRecommendedDeflectionLimit("floor")).toBe(360);
      expect(getRecommendedDeflectionLimit("roof")).toBe(240);
      expect(getRecommendedDeflectionLimit("cantilever")).toBe(120);
      expect(getRecommendedDeflectionLimit("general")).toBe(240);
    });

    test("should default to general limit for unknown application", () => {
      expect(getRecommendedDeflectionLimit("unknown" as any)).toBe(240);
    });
  });

  describe("performSimpleDeflectionCheck", () => {
    test("should check deflection with adequate results", () => {
      const beamLength = 240; // 20 feet in inches
      const maxUpward = -0.8; // 0.8 inches upward (negative)
      const maxDownward = 0.9; // 0.9 inches downward (positive)
      const limit = 240; // L/240

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward, limit);

      // Allowable deflection = 240/240 = 1.0 inch
      expect(result.upward.allowableDeflection).toBe(1.0);
      expect(result.downward.allowableDeflection).toBe(1.0);
      
      // Both should be adequate (less than 1.0 inch)
      expect(result.upward.adequate).toBe(true);
      expect(result.downward.adequate).toBe(true);
      expect(result.overallAdequate).toBe(true);
      
      // Check ratios
      expect(result.upward.deflectionRatio).toBeCloseTo(0.8, 3);
      expect(result.downward.deflectionRatio).toBeCloseTo(0.9, 3);
      
      // Check L/delta ratios
      expect(result.upward.lOverDelta).toBeCloseTo(300, 1); // 240/0.8
      expect(result.downward.lOverDelta).toBeCloseTo(266.67, 1); // 240/0.9
      
      // Downward should control (higher ratio)
      expect(result.controllingDirection).toBe("downward");
    });

    test("should check deflection with inadequate results", () => {
      const beamLength = 360; // 30 feet in inches
      const maxUpward = -2.0; // 2.0 inches upward (excessive)
      const maxDownward = 1.8; // 1.8 inches downward (excessive)
      const limit = 240; // L/240

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward, limit);

      // Allowable deflection = 360/240 = 1.5 inches
      expect(result.upward.allowableDeflection).toBe(1.5);
      expect(result.downward.allowableDeflection).toBe(1.5);
      
      // Both should be inadequate
      expect(result.upward.adequate).toBe(false);
      expect(result.downward.adequate).toBe(false);
      expect(result.overallAdequate).toBe(false);
      
      // Check ratios (both > 1.0)
      expect(result.upward.deflectionRatio).toBeCloseTo(1.333, 3);
      expect(result.downward.deflectionRatio).toBeCloseTo(1.2, 3);
      
      // Both should control
      expect(result.controllingDirection).toBe("both");
    });

    test("should handle zero deflections", () => {
      const beamLength = 240;
      const maxUpward = 0;
      const maxDownward = 0;
      const limit = 240;

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward, limit);

      expect(result.upward.adequate).toBe(true);
      expect(result.downward.adequate).toBe(true);
      expect(result.overallAdequate).toBe(true);
      expect(result.upward.deflectionRatio).toBe(0);
      expect(result.downward.deflectionRatio).toBe(0);
      expect(result.upward.lOverDelta).toBe(Infinity);
      expect(result.downward.lOverDelta).toBe(Infinity);
    });

    test("should use default deflection limit when not specified", () => {
      const beamLength = 240;
      const maxUpward = -0.5;
      const maxDownward = 0.5;

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward);

      expect(result.deflectionLimit).toBe(DEFAULT_DEFLECTION_LIMITS.TOTAL);
      expect(result.upward.allowableDeflection).toBe(1.0); // 240/240
    });
  });

  describe("checkDeflection", () => {
    test("should perform complete deflection check", () => {
      const inputs: DeflectionInputs = {
        section: testSection,
        material: testMaterial,
        beamLength: 300, // 25 feet
        maxUpwardDeflection: -0.6, // 0.6 inches upward
        maxDownwardDeflection: 0.8, // 0.8 inches downward
        deflectionLimit: 360, // L/360
      };

      const result = checkDeflection(inputs);

      // Allowable deflection = 300/360 = 0.833 inches
      expect(result.upward.allowableDeflection).toBeCloseTo(0.833, 3);
      expect(result.downward.allowableDeflection).toBeCloseTo(0.833, 3);
      
      // Check adequacy
      expect(result.upward.adequate).toBe(true); // 0.6 < 0.833
      expect(result.downward.adequate).toBe(true); // 0.8 < 0.833 (adequate, not inadequate)
      expect(result.overallAdequate).toBe(true);
      
      // Downward should control (higher ratio)
      expect(result.controllingDirection).toBe("downward");
      
      // Check ratios
      expect(result.upward.deflectionRatio).toBeCloseTo(0.72, 2);
      expect(result.downward.deflectionRatio).toBeCloseTo(0.96, 2);
    });

    test("should handle mixed adequacy scenarios", () => {
      const inputs: DeflectionInputs = {
        section: testSection,
        material: testMaterial,
        beamLength: 240,
        maxUpwardDeflection: -1.5, // Excessive upward
        maxDownwardDeflection: 0.5, // Adequate downward
        deflectionLimit: 240,
      };

      const result = checkDeflection(inputs);

      expect(result.upward.adequate).toBe(false);
      expect(result.downward.adequate).toBe(true);
      expect(result.overallAdequate).toBe(false);
      expect(result.controllingDirection).toBe("upward");
    });

    test("should validate inputs properly", () => {
      // Test null inputs
      expect(() => checkDeflection(null as any)).toThrow("Deflection inputs are required.");

      // Test missing section
      const inputsNoSection = {
        material: testMaterial,
        beamLength: 240,
        maxUpwardDeflection: -0.5,
        maxDownwardDeflection: 0.5,
      } as DeflectionInputs;
      expect(() => checkDeflection(inputsNoSection)).toThrow("Section is required");

      // Test missing material
      const inputsNoMaterial = {
        section: testSection,
        beamLength: 240,
        maxUpwardDeflection: -0.5,
        maxDownwardDeflection: 0.5,
      } as DeflectionInputs;
      expect(() => checkDeflection(inputsNoMaterial)).toThrow("Material is required");

      // Test invalid beam length
      const inputsInvalidLength = {
        section: testSection,
        material: testMaterial,
        beamLength: -240,
        maxUpwardDeflection: -0.5,
        maxDownwardDeflection: 0.5,
      };
      expect(() => checkDeflection(inputsInvalidLength)).toThrow("Beam length must be a positive number");

      // Test invalid deflection values
      const inputsInvalidDeflection = {
        section: testSection,
        material: testMaterial,
        beamLength: 240,
        maxUpwardDeflection: "invalid" as any,
        maxDownwardDeflection: 0.5,
      };
      expect(() => checkDeflection(inputsInvalidDeflection)).toThrow("Max upward deflection must be a number");

      // Test invalid deflection limit
      const inputsInvalidLimit = {
        section: testSection,
        material: testMaterial,
        beamLength: 240,
        maxUpwardDeflection: -0.5,
        maxDownwardDeflection: 0.5,
        deflectionLimit: -240,
      };
      expect(() => checkDeflection(inputsInvalidLimit)).toThrow("Deflection limit must be a positive number");
    });

    test("should use default deflection limit when not provided", () => {
      const inputs: DeflectionInputs = {
        section: testSection,
        material: testMaterial,
        beamLength: 240,
        maxUpwardDeflection: -0.5,
        maxDownwardDeflection: 0.5,
      };

      const result = checkDeflection(inputs);

      expect(result.deflectionLimit).toBe(DEFAULT_DEFLECTION_LIMITS.TOTAL);
    });
  });

  describe("Edge cases and special scenarios", () => {
    test("should handle very small deflections", () => {
      const result = performSimpleDeflectionCheck(240, -0.001, 0.001, 240);
      
      expect(result.upward.adequate).toBe(true);
      expect(result.downward.adequate).toBe(true);
      expect(result.overallAdequate).toBe(true);
      expect(result.upward.deflectionRatio).toBeCloseTo(0.001, 6);
      expect(result.downward.deflectionRatio).toBeCloseTo(0.001, 6);
    });

    test("should handle very large beam lengths", () => {
      const beamLength = 7200; // 600 feet (very long beam)
      const result = performSimpleDeflectionCheck(beamLength, -10, 12, 240);
      
      // Allowable = 7200/240 = 30 inches
      expect(result.upward.allowableDeflection).toBe(30);
      expect(result.downward.allowableDeflection).toBe(30);
      expect(result.upward.adequate).toBe(true);
      expect(result.downward.adequate).toBe(true);
    });

    test("should handle strict deflection limits", () => {
      // Very strict limit (L/1000)
      const result = performSimpleDeflectionCheck(240, -0.3, 0.3, 1000);
      
      // Allowable = 240/1000 = 0.24 inches
      expect(result.upward.allowableDeflection).toBe(0.24);
      expect(result.downward.allowableDeflection).toBe(0.24);
      expect(result.upward.adequate).toBe(false); // 0.3 > 0.24
      expect(result.downward.adequate).toBe(false); // 0.3 > 0.24
    });

    test("should properly identify controlling direction when ratios are equal", () => {
      const result = performSimpleDeflectionCheck(240, -1.0, 1.0, 240);
      
      // Both have same ratio
      expect(result.upward.deflectionRatio).toBe(result.downward.deflectionRatio);
      expect(result.controllingDirection).toBe("upward"); // Upward wins ties
    });
  });

  describe("Real-world scenarios", () => {
    test("should check typical office building floor beam", () => {
      // 30-foot span office building beam, L/360 limit for floors
      const beamLength = 360; // 30 feet
      const maxUpward = -0.8; // Typical upward deflection
      const maxDownward = 1.2; // Typical downward deflection
      const limit = 360; // L/360 for floors

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward, limit);

      // Allowable = 360/360 = 1.0 inch
      expect(result.upward.adequate).toBe(true); // 0.8 < 1.0
      expect(result.downward.adequate).toBe(false); // 1.2 > 1.0
      expect(result.overallAdequate).toBe(false);
      expect(result.controllingDirection).toBe("downward");
    });

    test("should check typical roof beam", () => {
      // 24-foot span roof beam, L/240 limit for roofs
      const beamLength = 288; // 24 feet
      const maxUpward = -0.9; // Wind uplift
      const maxDownward = 1.0; // Snow/dead load
      const limit = 240; // L/240 for roofs

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward, limit);

      // Allowable = 288/240 = 1.2 inches
      expect(result.upward.adequate).toBe(true); // 0.9 < 1.2
      expect(result.downward.adequate).toBe(true); // 1.0 < 1.2
      expect(result.overallAdequate).toBe(true);
      expect(result.controllingDirection).toBe("downward"); // Higher ratio
    });

    test("should check cantilever beam", () => {
      // 8-foot cantilever, L/120 limit
      const beamLength = 96; // 8 feet
      const maxUpward = -0.6; // Upward load case
      const maxDownward = 1.0; // Downward load case
      const limit = 120; // L/120 for cantilevers

      const result = performSimpleDeflectionCheck(beamLength, maxUpward, maxDownward, limit);

      // Allowable = 96/120 = 0.8 inches
      expect(result.upward.adequate).toBe(true); // 0.6 < 0.8
      expect(result.downward.adequate).toBe(false); // 1.0 > 0.8
      expect(result.overallAdequate).toBe(false);
      expect(result.controllingDirection).toBe("downward");
    });
  });
}); 