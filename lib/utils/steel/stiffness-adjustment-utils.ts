/**
 * @file This file contains utility functions for stiffness adjustments
 * in accordance with AISC 360-22 Section C2.3, "Adjustments to Stiffness".
 */

import { DesignMethod } from "./constants";

/**
 * Interface for stiffness adjustment parameters.
 */
export interface StiffnessAdjustmentParams {
  Pr: number; // Required axial compressive strength (kips)
  Pn: number; // Nominal axial compressive strength (kips)
  designMethod: DesignMethod; // LRFD or ASD
}

/**
 * Interface for stiffness adjustment results.
 */
export interface StiffnessAdjustmentResult {
  baseReductionFactor: number; // 0.80 base reduction factor
  tauB: number; // Additional stiffness reduction parameter τb
  totalFlexuralReduction: number; // Combined reduction factor for flexural stiffness
  totalStiffnessReduction: number; // Total reduction factor for general stiffness
}

/**
 * Calculates the stiffness reduction parameter τb (tau_b) per AISC 360-22 Equations C2-2a and C2-2b.
 * 
 * @param {number} Pr Required axial compressive strength using LRFD or ASD load combinations (kips).
 * @param {number} Pn Nominal axial compressive strength (kips).
 * @param {DesignMethod} designMethod LRFD or ASD design method.
 * @returns {number} The stiffness reduction parameter τb.
 * @throws {Error} If Pr or Pn are negative or if Pn is zero.
 * @source AISC 360-22, Section C2.3, Equations C2-2a and C2-2b.
 */
export function calculateTauB(
  Pr: number,
  Pn: number,
  designMethod: DesignMethod
): number {
  if (Pr < 0) {
    throw new Error("Required axial strength (Pr) must be non-negative.");
  }
  if (Pn <= 0) {
    throw new Error("Nominal axial strength (Pn) must be positive.");
  }

  // Load combination factor α per AISC 360-22 Section C2.3
  const alpha = designMethod === DesignMethod.LRFD ? 1.0 : 1.6;

  // Calculate the ratio αPr/Pn
  const alphaPrOverPn = (alpha * Pr) / Pn;

  // Apply AISC Equations C2-2a and C2-2b
  if (alphaPrOverPn <= 0.5) {
    // Equation C2-2a: When αPr/Pn ≤ 0.5
    return 1.0;
  } else {
    // Equation C2-2b: When αPr/Pn > 0.5
    // τb = 4(αPr/Pn)[1-(αPr/Pn)]
    return 4 * alphaPrOverPn * (1 - alphaPrOverPn);
  }
}

/**
 * Calculates the complete stiffness adjustments per AISC 360-22 Section C2.3.
 * 
 * @param {StiffnessAdjustmentParams} params Parameters for stiffness adjustment calculation.
 * @returns {StiffnessAdjustmentResult} Complete stiffness adjustment factors.
 * @source AISC 360-22, Section C2.3, "Adjustments to Stiffness".
 */
export function calculateStiffnessAdjustments(
  params: StiffnessAdjustmentParams
): StiffnessAdjustmentResult {
  const { Pr, Pn, designMethod } = params;

  // Base stiffness reduction factor per AISC 360-22 Section C2.3(a)
  const baseReductionFactor = 0.80;

  // Calculate τb per AISC 360-22 Section C2.3(b)
  const tauB = calculateTauB(Pr, Pn, designMethod);

  // Total flexural stiffness reduction factor
  // Applied to flexural stiffnesses that contribute to stability
  const totalFlexuralReduction = baseReductionFactor * tauB;

  // Total general stiffness reduction factor
  // Applied to all stiffnesses that contribute to stability
  const totalStiffnessReduction = baseReductionFactor;

  return {
    baseReductionFactor,
    tauB,
    totalFlexuralReduction,
    totalStiffnessReduction,
  };
}

/**
 * Applies stiffness adjustments to member moment of inertia values.
 * 
 * @param {number} Ix Strong-axis moment of inertia (in⁴).
 * @param {number} Iy Weak-axis moment of inertia (in⁴).
 * @param {StiffnessAdjustmentParams} adjustmentParams Parameters for stiffness adjustment.
 * @returns {{ IxAdjusted: number, IyAdjusted: number }} Adjusted moment of inertia values.
 * @source AISC 360-22, Section C2.3.
 */
export function applyStiffnessAdjustments(
  Ix: number,
  Iy: number,
  adjustmentParams: StiffnessAdjustmentParams
): { IxAdjusted: number; IyAdjusted: number } {
  if (Ix <= 0 || Iy <= 0) {
    throw new Error("Moment of inertia values must be positive.");
  }

  const adjustments = calculateStiffnessAdjustments(adjustmentParams);

  // Apply total flexural reduction to both axes
  const IxAdjusted = Ix * adjustments.totalFlexuralReduction;
  const IyAdjusted = Iy * adjustments.totalFlexuralReduction;

  return { IxAdjusted, IyAdjusted };
}

/**
 * Calculates adjusted effective length factors considering stiffness reductions.
 * This is a simplified approach - actual stability analysis would require more complex calculations.
 * 
 * @param {number} K Effective length factor before stiffness adjustments.
 * @param {StiffnessAdjustmentParams} adjustmentParams Parameters for stiffness adjustment.
 * @returns {number} Adjusted effective length factor.
 * @source AISC 360-22, Section C2.3 (conceptual application).
 */
export function calculateAdjustedEffectiveLengthFactor(
  K: number,
  adjustmentParams: StiffnessAdjustmentParams
): number {
  if (K <= 0) {
    throw new Error("Effective length factor (K) must be positive.");
  }

  const adjustments = calculateStiffnessAdjustments(adjustmentParams);

  // Simplified approach: adjust K based on stiffness reduction
  // In practice, this would require iterative stability analysis
  const KAdjusted = K / Math.sqrt(adjustments.totalStiffnessReduction);

  return KAdjusted;
} 