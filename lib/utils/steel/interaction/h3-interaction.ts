/**
 * AISC 360-22 Chapter H, Section H3 - Torsion and Combined Forces
 *
 * Implements AISC H3 for members subjected to torsion and combined torsion,
 * flexure, shear, and/or axial force.
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Chapter H, Section H3
 */

import { INTERACTION_CONSTANTS, E } from "../constants";
import {
  H3_TorsionalParameters,
  H3_CombinedLoads,
  H3_CombinedCapacities,
  InteractionResult,
  InteractionLimitState,
  InteractionMetadata,
  InteractionOptions,
} from "./types";

/**
 * Calculates torsional critical stress for round HSS members.
 *
 * Uses the larger of AISC equations H3-2a and H3-2b, but not exceeding 0.6Fy.
 *
 * @param params - Torsional parameters for round HSS
 * @returns Critical stress Fcr in ksi
 *
 * @source AISC 360-22, Section H3.1(a), Equations H3-2a and H3-2b
 */
function calculateFcr_RoundHSS(params: H3_TorsionalParameters): number {
  const { D, L, t, Fy, E: E_steel = E } = params;

  if (!D) {
    throw new Error("Outside diameter (D) is required for round HSS");
  }

  // AISC Eq. H3-2a: 1.23E / [(L/D)(D/t)^(5/4)]
  const LoverD = L / D;
  const DoverT = D / t;
  const fcr_2a =
    (INTERACTION_CONSTANTS.H3_ROUND_COEFF_1 * E_steel) /
    (LoverD * Math.pow(DoverT, INTERACTION_CONSTANTS.H3_ROUND_EXPONENT_1));

  // AISC Eq. H3-2b: 0.60E / (D/t)^(3/2)
  const fcr_2b =
    (INTERACTION_CONSTANTS.H3_ROUND_COEFF_2 * E_steel) /
    Math.pow(DoverT, INTERACTION_CONSTANTS.H3_ROUND_EXPONENT_2);

  // Take larger value but not exceeding 0.6Fy
  const fcr = Math.max(fcr_2a, fcr_2b);
  const maxFcr = INTERACTION_CONSTANTS.H3_YIELD_STRESS_FACTOR * Fy;

  return Math.min(fcr, maxFcr);
}

/**
 * Calculates torsional critical stress for rectangular HSS members.
 *
 * Uses different equations based on h/t ratio limits per AISC H3.1(b).
 *
 * @param params - Torsional parameters for rectangular HSS
 * @returns Critical stress Fcr in ksi
 *
 * @source AISC 360-22, Section H3.1(b), Equations H3-3, H3-4, H3-5
 */
function calculateFcr_RectangularHSS(params: H3_TorsionalParameters): number {
  const { h, t, Fy, E: E_steel = E } = params;

  if (!h) {
    throw new Error(
      "Flat width of longer side (h) is required for rectangular HSS"
    );
  }

  const hOverT = h / t;
  const sqrtEoverFy = Math.sqrt(E_steel / Fy);

  // Calculate h/t limits
  const limit1 = INTERACTION_CONSTANTS.H3_RECT_LIMIT_1 * sqrtEoverFy;
  const limit2 = INTERACTION_CONSTANTS.H3_RECT_LIMIT_2 * sqrtEoverFy;
  const limit3 = INTERACTION_CONSTANTS.H3_RECT_LIMIT_3;

  let fcr: number;

  if (hOverT <= limit1) {
    // AISC Eq. H3-3: Fcr = 0.6Fy
    fcr = INTERACTION_CONSTANTS.H3_RECT_COEFF_1 * Fy;
  } else if (hOverT <= limit2) {
    // AISC Eq. H3-4: Fcr = 0.6Fy * (2.45√(E/Fy)) / (h/t)
    fcr =
      (INTERACTION_CONSTANTS.H3_RECT_COEFF_1 *
        Fy *
        (INTERACTION_CONSTANTS.H3_RECT_COEFF_2 * sqrtEoverFy)) /
      hOverT;
  } else if (hOverT <= limit3) {
    // AISC Eq. H3-5: Fcr = 0.458π²E / (h/t)²
    fcr =
      (INTERACTION_CONSTANTS.H3_RECT_COEFF_3 *
        INTERACTION_CONSTANTS.H3_PI_SQUARED *
        E_steel) /
      (hOverT * hOverT);
  } else {
    throw new Error(
      `h/t ratio of ${hOverT.toFixed(2)} exceeds maximum limit of ${limit3}`
    );
  }

  return fcr;
}

/**
 * Calculates available torsional strength for HSS members.
 *
 * AISC Equation H3-1: Tn = Fcr * C
 *
 * @param params - Torsional parameters
 * @param sectionType - "round" or "rectangular"
 * @returns Available torsional strength in kip-in
 *
 * @source AISC 360-22, Section H3.1, Equation H3-1
 */
export function calculateTorsionalStrength_HSS(
  params: H3_TorsionalParameters,
  sectionType: "round" | "rectangular"
): { Tn: number; Fcr: number } {
  // Validate inputs
  if (params.L <= 0 || params.t <= 0 || params.C <= 0 || params.Fy <= 0) {
    throw new Error("All torsional parameters must be positive");
  }

  // Calculate critical stress based on section type
  let Fcr: number;
  try {
    if (sectionType === "round") {
      Fcr = calculateFcr_RoundHSS(params);
    } else {
      Fcr = calculateFcr_RectangularHSS(params);
    }
  } catch (error) {
    throw new Error(
      `Failed to calculate Fcr for ${sectionType} HSS: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  }

  // AISC Eq. H3-1: Tn = Fcr * C
  const Tn = Fcr * params.C;

  return { Tn, Fcr };
}

/**
 * Calculates H3 combined force interaction for HSS members when Tr > 20% of Tc.
 *
 * AISC Equation H3-6:
 * (Pr/Pc + Mrx/Mcx + Mry/Mcy) + (Vr/Vc + Tr/Tc)² ≤ 1.0
 *
 * @param loads - Applied loads including torsion and shear
 * @param capacities - Available capacities including torsion and shear
 * @param options - Analysis options
 * @returns H3 interaction analysis result
 *
 * @source AISC 360-22, Section H3.2, Equation H3-6
 */
export function calculateH3_CombinedInteraction(
  loads: H3_CombinedLoads,
  capacities: H3_CombinedCapacities,
  options: InteractionOptions = {}
): InteractionResult {
  // Validate inputs
  validateH3CombinedInputs(loads, capacities);

  const { includeMetadata = true } = options;

  // Extract loads and capacities
  const { Pr, Mrx, Mry, Vr, Tr } = loads;
  const { Pc, Mcx, Mcy, Vc, Tc } = capacities;

  // Check if combined analysis is required (Tr > 20% of Tc)
  const torsionalRatio = Tr / Tc;
  const usedCombinedAnalysis =
    torsionalRatio > INTERACTION_CONSTANTS.H3_TORSION_THRESHOLD;

  if (!usedCombinedAnalysis) {
    throw new Error(
      `Torsional ratio ${torsionalRatio.toFixed(
        3
      )} ≤ 0.2. Use H1 analysis and neglect torsional effects per AISC H3.2`
    );
  }

  // Calculate individual ratios
  const flexuralAxialTerm = Pr / Pc + Mrx / Mcx + Mry / Mcy;
  const shearTorsionTerm = Vr / Vc + Tr / Tc;

  // AISC Eq. H3-6: (Pr/Pc + Mrx/Mcx + Mry/Mcy) + (Vr/Vc + Tr/Tc)² ≤ 1.0
  const h3InteractionRatio = flexuralAxialTerm + Math.pow(shearTorsionTerm, 2);

  // Determine if member satisfies interaction requirements
  const satisfiesInteraction =
    h3InteractionRatio <= INTERACTION_CONSTANTS.H3_UNITY_LIMIT;
  const governingRatio = h3InteractionRatio;
  const governingLimitState = satisfiesInteraction
    ? InteractionLimitState.SATISFACTORY
    : InteractionLimitState.TORSION_INTERACTION;

  // Build metadata if requested
  let metadata: InteractionMetadata | undefined;
  if (includeMetadata) {
    metadata = buildH3Metadata(
      loads,
      capacities,
      flexuralAxialTerm,
      shearTorsionTerm,
      h3InteractionRatio,
      torsionalRatio,
      usedCombinedAnalysis
    );
  }

  return {
    satisfiesInteraction,
    governingRatio,
    inPlaneRatio: h3InteractionRatio,
    outOfPlaneRatio: undefined,
    governingLimitState,
    metadata,
  };
}

/**
 * Determines if H3 combined analysis is required based on torsional load ratio.
 *
 * @param Tr - Required torsional strength
 * @param Tc - Available torsional strength
 * @returns True if Tr > 20% of Tc, requiring H3 combined analysis
 *
 * @source AISC 360-22, Section H3.2
 */
export function isH3CombinedAnalysisRequired(Tr: number, Tc: number): boolean {
  if (Tc <= 0) {
    throw new Error("Available torsional strength (Tc) must be positive");
  }
  return Tr / Tc > INTERACTION_CONSTANTS.H3_TORSION_THRESHOLD;
}

/**
 * Validates input parameters for H3 combined force analysis.
 *
 * @param loads - Applied loads to validate
 * @param capacities - Available capacities to validate
 * @throws Error if any input is invalid
 */
function validateH3CombinedInputs(
  loads: H3_CombinedLoads,
  capacities: H3_CombinedCapacities
): void {
  const { Pr, Mrx, Mry, Vr, Tr } = loads;
  const { Pc, Mcx, Mcy, Vc, Tc } = capacities;

  // Validate applied loads (all positive per AISC user note)
  if (Pr < 0 || Mrx < 0 || Mry < 0 || Vr < 0 || Tr < 0) {
    throw new Error("All applied loads must be positive (per AISC user note)");
  }

  // Validate available capacities (must be positive)
  if (Pc <= 0 || Mcx <= 0 || Mcy <= 0 || Vc <= 0 || Tc <= 0) {
    throw new Error("All available capacities must be positive");
  }

  // Check for reasonable ratios
  const ratios = [Pr / Pc, Mrx / Mcx, Mry / Mcy, Vr / Vc, Tr / Tc];
  if (ratios.some((ratio) => ratio > 10)) {
    throw new Error(
      "Load to capacity ratios are unusually high (>10). Check input values."
    );
  }
}

/**
 * Builds comprehensive metadata for H3 combined force analysis.
 *
 * @param loads - Applied loads used in analysis
 * @param capacities - Available capacities used in analysis
 * @param flexuralAxialTerm - First term of H3-6 equation
 * @param shearTorsionTerm - Second term base of H3-6 equation
 * @param totalRatio - Total H3 interaction ratio
 * @param torsionalRatio - Tr/Tc ratio
 * @param usedCombinedAnalysis - Whether combined analysis was used
 * @returns Comprehensive metadata object
 */
function buildH3Metadata(
  loads: H3_CombinedLoads,
  capacities: H3_CombinedCapacities,
  flexuralAxialTerm: number,
  shearTorsionTerm: number,
  totalRatio: number,
  torsionalRatio: number,
  usedCombinedAnalysis: boolean
): InteractionMetadata {
  return {
    // Basic interaction metadata
    PrOverPc: loads.Pr / capacities.Pc,
    usedH1_1a: false,
    usedH1_3: false,
    usedH3: true,
    momentRatios: {
      MrxOverMcx: loads.Mrx / capacities.Mcx,
      MryOverMcy: loads.Mry / capacities.Mcy,
    },
    loadingType: "compression", // HSS torsion typically involves compression
    axialLoadThreshold: INTERACTION_CONSTANTS.H3_TORSION_THRESHOLD,
    equationsUsed: ["H3-6"],

    // H3-specific results
    h3Results: {
      torsionalRatio,
      usedCombinedAnalysis,
      torsionalThreshold: INTERACTION_CONSTANTS.H3_TORSION_THRESHOLD,
    },

    // Additional ratios
    totalInteractionRatio: totalRatio,
    governingComponent: determineH3GoverningComponent(
      flexuralAxialTerm,
      shearTorsionTerm
    ),
  };
}

/**
 * Determines which component governs the H3 interaction.
 *
 * @param flexuralAxialTerm - First term of H3-6 equation
 * @param shearTorsionTerm - Second term base of H3-6 equation
 * @returns String describing the governing component
 */
function determineH3GoverningComponent(
  flexuralAxialTerm: number,
  shearTorsionTerm: number
): string {
  const shearTorsionContribution = Math.pow(shearTorsionTerm, 2);

  if (flexuralAxialTerm > shearTorsionContribution) {
    return "Flexural and axial forces (Pr/Pc + Mrx/Mcx + Mry/Mcy)";
  } else {
    return "Shear and torsion forces (Vr/Vc + Tr/Tc)²";
  }
}

/**
 * Provides guidance on H3 torsional analysis considerations.
 *
 * @returns Object with analysis considerations and recommendations
 */
export function getH3AnalysisGuidance(): {
  considerations: string[];
  recommendations: string[];
  limitations: string[];
} {
  return {
    considerations: [
      "H3 applies to HSS members subjected to torsion and combined forces",
      "When Tr ≤ 20% of Tc, use Section H1 and neglect torsional effects",
      "When Tr > 20% of Tc, use H3-6 combined interaction equation",
      "Round HSS uses equations H3-2a and H3-2b for critical stress",
      "Rectangular HSS uses equations H3-3, H3-4, or H3-5 based on h/t ratio",
      "All terms in H3-6 equation are taken as positive per AISC user note",
    ],
    recommendations: [
      "Calculate available torsional strength using appropriate HSS equations",
      "Check torsional ratio first to determine analysis method",
      "For non-HSS members, use appropriate limit states (yielding, shear, buckling)",
      "Consider warping effects for open sections not covered by H3",
      "Verify HSS geometric limits and slenderness ratios",
    ],
    limitations: [
      "H3.1 and H3.2 specifically apply to HSS members only",
      "Non-HSS torsional analysis requires different approaches",
      "Does not account for warping torsion in open sections",
      "Assumes adequate lateral bracing for stability",
      "Limited to members where torsion is a significant load effect",
    ],
  };
}
