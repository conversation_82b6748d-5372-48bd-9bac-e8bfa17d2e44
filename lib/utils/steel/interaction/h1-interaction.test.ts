import { describe, it, expect } from "@jest/globals";
import { E } from "../constants";
import {
  calculateH1_Interaction,
  calculateH1_Interaction_Simplified,
  calculateEnhancedCb_H1_2,
  getH1_ResistanceFactors,
} from "./h1-interaction";
import {
  AppliedLoads,
  AvailableCapacities,
  H1_3_Parameters,
  InteractionLimitState,
} from "./types";

describe("H1 Interaction Analysis (AISC 360-22)", () => {
  // Test data representing typical beam-column scenarios
  const basicLoads: AppliedLoads = {
    Pr: 100, // 100 kips compression
    Mrx: 500, // 500 kip-in major axis moment
    Mry: 50, // 50 kip-in minor axis moment
  };

  const basicCapacities: AvailableCapacities = {
    Pc: 800, // 800 kips axial capacity
    Mcx: 2000, // 2000 kip-in major axis capacity
    Mcy: 800, // 800 kip-in minor axis capacity
  };

  const h1_3_params: H1_3_Parameters = {
    Lb: 180, // 15 feet unbraced length
    E: E, // 29000 ksi
    Iy: 204, // W16X50 minor axis moment of inertia
    Cb: 1.0, // Conservative lateral-torsional buckling factor
    Mcx_Cb1: 1800, // Major axis capacity with Cb = 1.0
  };

  describe("H1.1 - In-Plane Instability (Compression)", () => {
    it("should use H1-1a equation when Pr/Pc ≥ 0.2", () => {
      // Pr/Pc = 100/800 = 0.125 < 0.2, should use H1-1b
      const loads1: AppliedLoads = { Pr: 100, Mrx: 500, Mry: 50 };
      const result1 = calculateH1_Interaction(loads1, basicCapacities);

      expect(result1.metadata!.usedH1_1a).toBe(false);
      expect(result1.metadata!.equationsUsed).toContain("H1-1b");

      // Pr/Pc = 200/800 = 0.25 ≥ 0.2, should use H1-1a
      const loads2: AppliedLoads = { Pr: 200, Mrx: 500, Mry: 50 };
      const result2 = calculateH1_Interaction(loads2, basicCapacities);

      expect(result2.metadata!.usedH1_1a).toBe(true);
      expect(result2.metadata!.equationsUsed).toContain("H1-1a");
    });

    it("should calculate H1-1a interaction correctly", () => {
      const loads: AppliedLoads = { Pr: 200, Mrx: 800, Mry: 200 };
      const result = calculateH1_Interaction(loads, basicCapacities);

      // Manual calculation: Pr/Pc + (8/9)(Mrx/Mcx + Mry/Mcy)
      // = 200/800 + (8/9)(800/2000 + 200/800)
      // = 0.25 + (8/9)(0.4 + 0.25)
      // = 0.25 + (8/9)(0.65)
      // = 0.25 + 0.5778 = 0.8278

      expect(result.inPlaneRatio).toBeCloseTo(0.8278, 4);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.metadata!.usedH1_1a).toBe(true);
    });

    it("should calculate H1-1b interaction correctly", () => {
      const loads: AppliedLoads = { Pr: 100, Mrx: 800, Mry: 200 };
      const result = calculateH1_Interaction(loads, basicCapacities);

      // Manual calculation: Pr/(2Pc) + (Mrx/Mcx + Mry/Mcy)
      // = 100/(2*800) + (800/2000 + 200/800)
      // = 0.0625 + (0.4 + 0.25)
      // = 0.0625 + 0.65 = 0.7125

      expect(result.inPlaneRatio).toBeCloseTo(0.7125, 4);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.metadata!.usedH1_1a).toBe(false);
    });

    it("should identify overstressed conditions", () => {
      const heavyLoads: AppliedLoads = { Pr: 600, Mrx: 1500, Mry: 600 };
      const result = calculateH1_Interaction(heavyLoads, basicCapacities);

      // This should result in interaction ratio > 1.0
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.governingRatio).toBeGreaterThan(1.0);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.IN_PLANE_INSTABILITY
      );
    });

    it("should handle edge case at threshold", () => {
      const thresholdLoads: AppliedLoads = { Pr: 160, Mrx: 500, Mry: 50 };
      const result = calculateH1_Interaction(thresholdLoads, basicCapacities);

      // Pr/Pc = 160/800 = 0.2 exactly at threshold
      expect(result.metadata!.PrOverPc).toBeCloseTo(0.2, 10);
      expect(result.metadata!.usedH1_1a).toBe(true);
    });
  });

  describe("H1.2 - Tension with Flexure", () => {
    it("should handle tension loading", () => {
      const tensionLoads: AppliedLoads = { Pr: 100, Mrx: 500, Mry: 50 };
      const tensionCapacities: AvailableCapacities = {
        Pc: 800, // Available tensile capacity
        Mcx: 2000,
        Mcy: 800,
      };

      const result = calculateH1_Interaction(
        tensionLoads,
        tensionCapacities,
        undefined,
        { loadingTypeOverride: "tension" }
      );

      expect(result.metadata!.loadingType).toBe("tension");
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.inPlaneRatio).toBeGreaterThan(0);
    });

    it("should allow enhanced Cb factors for tension", () => {
      const section = {
        name: "W16X50",
        Type: "W" as const,
        A: 14.7,
        d: 16.3,
        tw: 0.38,
        Iy: 204,
      };
      const Pr = 100; // kips tension

      // This should return 1.0 with warning (conservative)
      const CbEnhanced = calculateEnhancedCb_H1_2(section, Pr);
      expect(CbEnhanced).toBe(1.0);
    });

    it("should throw error for invalid enhanced Cb inputs", () => {
      const invalidSection = {
        name: "Invalid",
        Type: "W" as const,
        A: 14.7,
        d: 16.3,
        tw: 0.38,
        Iy: undefined,
      };
      expect(() =>
        calculateEnhancedCb_H1_2(invalidSection as any, 100)
      ).toThrow("Moment of inertia about y-axis (Iy) is required");

      const validSection = {
        name: "W16X50",
        Type: "W" as const,
        A: 14.7,
        d: 16.3,
        tw: 0.38,
        Iy: 204,
      };
      expect(() => calculateEnhancedCb_H1_2(validSection, -100)).toThrow(
        "Axial tensile strength (Pr) must be positive"
      );
    });
  });

  describe("H1.3 - Separate Stability Check", () => {
    it("should perform H1.3 check when applicable", () => {
      const loads: AppliedLoads = { Pr: 100, Mrx: 800, Mry: 80 }; // Mry/Mcy = 80/800 = 0.1 > 0.05
      const result = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );

      expect(result.metadata!.usedH1_3).toBe(true);
      expect(result.outOfPlaneRatio).toBeDefined();
      expect(result.metadata!.Pey).toBeDefined();
      expect(result.metadata!.equationsUsed).toContain("H1-3");
    });

    it("should calculate H1.3 interaction correctly", () => {
      const loads: AppliedLoads = { Pr: 100, Mrx: 800, Mry: 80 };
      const result = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );

      // Manual calculation of Pey: π²EIy/Lb²
      const expectedPey = (Math.PI ** 2 * E * 204) / 180 ** 2;
      expect(result.metadata!.Pey).toBeCloseTo(expectedPey, 0);

      // H1-3: Pr/Pey * (1.5 - 0.5*Pr/Pey) + (Mrx/(Cb*Mcx))²
      const PrOverPey = 100 / expectedPey;
      const term1 = PrOverPey * (1.5 - 0.5 * PrOverPey);
      const term2 = Math.pow(800 / (1.0 * 1800), 2);
      const expectedRatio = term1 + term2;

      expect(result.outOfPlaneRatio).toBeCloseTo(expectedRatio, 4);
    });

    it("should skip H1.3 when not applicable", () => {
      const loads: AppliedLoads = { Pr: 100, Mrx: 800, Mry: 20 }; // Mry/Mcy = 20/800 = 0.025 < 0.05
      const result = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );

      expect(result.metadata!.usedH1_3).toBe(false);
      expect(result.outOfPlaneRatio).toBeUndefined();
      expect(result.metadata!.Pey).toBeUndefined();
    });

    it("should allow custom H1.3 threshold", () => {
      const loads: AppliedLoads = { Pr: 100, Mrx: 800, Mry: 60 }; // Mry/Mcy = 0.075

      // With default threshold (0.05), should use H1.3
      const result1 = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );
      expect(result1.metadata!.usedH1_3).toBe(true);

      // With higher threshold (0.1), should not use H1.3
      const result2 = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params,
        { h1_3Threshold: 0.1 }
      );
      expect(result2.metadata!.usedH1_3).toBe(false);
    });

    it("should handle governing limit state selection", () => {
      // Create scenario where H1.3 governs
      const loads: AppliedLoads = { Pr: 300, Mrx: 1200, Mry: 100 };
      const result = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );

      if (
        result.outOfPlaneRatio &&
        result.outOfPlaneRatio > result.inPlaneRatio
      ) {
        expect(result.governingLimitState).toBe(
          InteractionLimitState.OUT_OF_PLANE_BUCKLING
        );
        expect(result.governingRatio).toBe(result.outOfPlaneRatio);
      } else {
        expect(result.governingLimitState).toBe(
          InteractionLimitState.IN_PLANE_INSTABILITY
        );
        expect(result.governingRatio).toBe(result.inPlaneRatio);
      }
    });
  });

  describe("Input Validation", () => {
    it("should validate positive loads", () => {
      const invalidLoads: AppliedLoads = { Pr: -100, Mrx: 500, Mry: 50 };
      expect(() =>
        calculateH1_Interaction(invalidLoads, basicCapacities)
      ).toThrow("All applied loads must be positive");
    });

    it("should validate positive capacities", () => {
      const invalidCapacities: AvailableCapacities = {
        Pc: 0,
        Mcx: 2000,
        Mcy: 800,
      };
      expect(() =>
        calculateH1_Interaction(basicLoads, invalidCapacities)
      ).toThrow("All available capacities must be positive");
    });

    it("should validate H1.3 parameters", () => {
      const invalidParams: H1_3_Parameters = {
        Lb: -180,
        E: E,
        Iy: 204,
        Cb: 1.0,
        Mcx_Cb1: 1800,
      };

      expect(() =>
        calculateH1_Interaction(basicLoads, basicCapacities, invalidParams)
      ).toThrow("Effective length (Lb) must be a positive number");
    });

    it("should handle zero moments gracefully", () => {
      const zeroMomentLoads: AppliedLoads = { Pr: 100, Mrx: 0, Mry: 0 };
      const result = calculateH1_Interaction(zeroMomentLoads, basicCapacities);

      expect(result.satisfiesInteraction).toBe(true);
      expect(result.metadata!.momentRatios.MrxOverMcx).toBe(0);
      expect(result.metadata!.momentRatios.MryOverMcy).toBe(0);
    });
  });

  describe("Simplified Interaction Check", () => {
    it("should provide simplified analysis without H1.3", () => {
      const result = calculateH1_Interaction_Simplified(
        basicLoads,
        basicCapacities
      );

      expect(result.metadata!.usedH1_3).toBe(false);
      expect(result.outOfPlaneRatio).toBeUndefined();
      expect(result.metadata!.loadingType).toBe("compression");
    });

    it("should handle tension in simplified analysis", () => {
      const result = calculateH1_Interaction_Simplified(
        basicLoads,
        basicCapacities,
        false
      );

      expect(result.metadata!.loadingType).toBe("tension");
      expect(result.metadata!.usedH1_3).toBe(false);
    });
  });

  describe("Resistance Factors", () => {
    it("should provide resistance factor information", () => {
      const factors = getH1_ResistanceFactors();

      expect(factors.phi_compression).toBe(0.9);
      expect(factors.phi_flexure).toBe(0.9);
      expect(factors.omega_compression).toBe(1.67);
      expect(factors.omega_flexure).toBe(1.67);
      expect(factors.note).toContain("individual member resistance factors");
    });
  });

  describe("Real-World Examples", () => {
    it("should analyze typical beam-column (W16X50)", () => {
      // Typical W16X50 beam-column scenario
      const loads: AppliedLoads = { Pr: 150, Mrx: 600, Mry: 75 };
      const capacities: AvailableCapacities = {
        Pc: 520, // Typical compression capacity
        Mcx: 1580, // Typical major-axis capacity
        Mcy: 345, // Typical minor-axis capacity
      };

      const result = calculateH1_Interaction(loads, capacities, h1_3_params);

      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingRatio).toBeLessThan(1.0);
      expect(result.metadata!.PrOverPc).toBeCloseTo(150 / 520, 3);
    });

    it("should analyze high moment scenario", () => {
      // High moment, low axial load scenario
      const loads: AppliedLoads = { Pr: 50, Mrx: 1400, Mry: 100 };
      const result = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );

      // Should use H1-1b due to low axial load ratio
      expect(result.metadata!.usedH1_1a).toBe(false);
      expect(result.metadata!.PrOverPc).toBeLessThan(0.2);

      // Moment ratios should be significant
      expect(result.metadata!.momentRatios.MrxOverMcx).toBeCloseTo(0.7, 1);
    });

    it("should analyze balanced loading", () => {
      // Balanced axial and moment loading
      const loads: AppliedLoads = { Pr: 400, Mrx: 800, Mry: 200 };
      const result = calculateH1_Interaction(
        loads,
        basicCapacities,
        h1_3_params
      );

      // Should use H1-1a due to high axial load ratio
      expect(result.metadata!.usedH1_1a).toBe(true);
      expect(result.metadata!.PrOverPc).toBeCloseTo(0.5, 1);

      // Check that interaction is reasonable
      expect(result.governingRatio).toBeGreaterThan(0.8);
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle very small loads", () => {
      const smallLoads: AppliedLoads = { Pr: 0.1, Mrx: 1, Mry: 0.5 };
      const result = calculateH1_Interaction(smallLoads, basicCapacities);

      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingRatio).toBeLessThan(0.01);
    });

    it("should handle unity interaction ratio", () => {
      // Design loads to get close to unity ratio
      const unityLoads: AppliedLoads = { Pr: 320, Mrx: 720, Mry: 320 };
      const result = calculateH1_Interaction(unityLoads, basicCapacities);

      // Should be close to 1.0 but may slightly exceed
      expect(result.governingRatio).toBeGreaterThan(1.0);
      expect(result.governingRatio).toBeLessThan(1.2);
      expect(result.satisfiesInteraction).toBe(false);
    });

    it("should handle different design methods for enhanced Cb", () => {
      const section = {
        name: "W16X50",
        Type: "W" as const,
        A: 14.7,
        d: 16.3,
        tw: 0.38,
        Iy: 204,
      };

      const CbLRFD = calculateEnhancedCb_H1_2(section, 100, "LRFD");
      const CbASD = calculateEnhancedCb_H1_2(section, 100, "ASD");

      // Both should return 1.0 (conservative) in current implementation
      expect(CbLRFD).toBe(1.0);
      expect(CbASD).toBe(1.0);
    });
  });

  describe("Metadata and Documentation", () => {
    it("should provide comprehensive metadata", () => {
      const result = calculateH1_Interaction(
        basicLoads,
        basicCapacities,
        h1_3_params
      );

      expect(result.metadata!.PrOverPc).toBeCloseTo(
        basicLoads.Pr / basicCapacities.Pc,
        6
      );
      expect(result.metadata!.momentRatios.MrxOverMcx).toBeCloseTo(
        basicLoads.Mrx / basicCapacities.Mcx,
        6
      );
      expect(result.metadata!.momentRatios.MryOverMcy).toBeCloseTo(
        basicLoads.Mry / basicCapacities.Mcy,
        6
      );
      expect(result.metadata!.axialLoadThreshold).toBe(0.2);
      expect(result.metadata!.equationsUsed.length).toBeGreaterThan(0);
    });

    it("should allow metadata to be excluded", () => {
      const result = calculateH1_Interaction(
        basicLoads,
        basicCapacities,
        h1_3_params,
        { includeMetadata: false }
      );

      // Metadata should be undefined when excluded
      expect(result.metadata).toBeUndefined();
    });

    it("should track equations used correctly", () => {
      const loads1: AppliedLoads = { Pr: 100, Mrx: 500, Mry: 50 }; // H1-1b
      const result1 = calculateH1_Interaction(loads1, basicCapacities);
      expect(result1.metadata!.equationsUsed).toContain("H1-1b");

      const loads2: AppliedLoads = { Pr: 200, Mrx: 500, Mry: 50 }; // H1-1a
      const result2 = calculateH1_Interaction(loads2, basicCapacities);
      expect(result2.metadata!.equationsUsed).toContain("H1-1a");

      // With H1.3 applicable
      const loads3: AppliedLoads = { Pr: 100, Mrx: 500, Mry: 80 };
      const result3 = calculateH1_Interaction(
        loads3,
        basicCapacities,
        h1_3_params
      );
      if (result3.metadata!.usedH1_3) {
        expect(result3.metadata!.equationsUsed).toContain("H1-3");
      }
    });
  });
});
