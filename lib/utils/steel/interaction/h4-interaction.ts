/**
 * AISC 360-22 Chapter H, Section H4 - Rupture of Flanges with Bolt Holes and Subjected to Tension
 *
 * Implements AISC H4 for rupture analysis of flanges with bolt holes
 * subjected to combined axial force and major-axis flexure.
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Chapter H, Section H4
 */

import { INTERACTION_CONSTANTS } from "../constants";
import {
  H4_FlangeParameters,
  InteractionResult,
  InteractionLimitState,
  InteractionMetadata,
  InteractionOptions,
} from "./types";

/**
 * Calculates H4 flange rupture interaction for bolt hole locations.
 *
 * AISC Equation H4-1:
 * Pr/Pc + Mrx/Mcx ≤ 1.0
 *
 * Each flange subjected to tension due to axial force and flexure
 * shall be checked separately per AISC H4.
 *
 * @param params - Flange rupture parameters
 * @param options - Analysis options
 * @returns H4 flange rupture analysis result
 *
 * @source AISC 360-22, Section H4, Equation H4-1
 */
export function calculateH4_FlangeRupture(
  params: H4_FlangeParameters,
  options: InteractionOptions = {}
): InteractionResult {
  // Validate inputs
  validateH4Inputs(params);

  const { includeMetadata = true } = options;
  const { Pr, Pc, Mrx, Mcx } = params;

  // AISC Eq. H4-1: Pr/Pc + Mrx/Mcx ≤ 1.0
  const axialRatio = Pr / Pc;
  const flexuralRatio = Mrx / Mcx;
  const flangeRuptureRatio = axialRatio + flexuralRatio;

  // Determine if flange satisfies rupture requirements
  const satisfiesInteraction =
    flangeRuptureRatio <= INTERACTION_CONSTANTS.H4_UNITY_LIMIT;
  const governingRatio = flangeRuptureRatio;
  const governingLimitState = satisfiesInteraction
    ? InteractionLimitState.SATISFACTORY
    : InteractionLimitState.FLANGE_RUPTURE;

  // Build metadata if requested
  let metadata: InteractionMetadata | undefined;
  if (includeMetadata) {
    metadata = buildH4Metadata(
      params,
      axialRatio,
      flexuralRatio,
      flangeRuptureRatio
    );
  }

  return {
    satisfiesInteraction,
    governingRatio,
    inPlaneRatio: flangeRuptureRatio,
    outOfPlaneRatio: undefined,
    governingLimitState,
    metadata,
  };
}

/**
 * Determines the ratio for H4 flange rupture analysis.
 *
 * @param params - Flange rupture parameters
 * @returns H4-1 interaction ratio
 */
export function calculateH4_InteractionRatio(
  params: H4_FlangeParameters
): number {
  const result = calculateH4_FlangeRupture(params, { includeMetadata: false });
  return result.governingRatio;
}

/**
 * Determines if H4 analysis is applicable for the given conditions.
 *
 * @param hasBoltHoles - Whether flanges have bolt holes
 * @param isInTension - Whether member is subjected to tension
 * @param hasMajorAxisMoment - Whether member has major-axis moment
 * @returns True if H4 analysis is applicable
 *
 * @source AISC 360-22, Section H4
 */
export function isH4Applicable(
  hasBoltHoles: boolean,
  isInTension: boolean,
  hasMajorAxisMoment: boolean = false
): boolean {
  return hasBoltHoles && isInTension && (hasMajorAxisMoment || true);
}

/**
 * Validates input parameters for H4 flange rupture analysis.
 *
 * @param params - Flange parameters to validate
 * @throws Error if any input is invalid
 */
function validateH4Inputs(params: H4_FlangeParameters): void {
  const { Pr, Pc, Mrx, Mcx } = params;

  // Validate basic parameter types
  if (typeof Pr !== "number" || isNaN(Pr)) {
    throw new Error("Required axial strength (Pr) must be a valid number");
  }
  if (typeof Pc !== "number" || isNaN(Pc)) {
    throw new Error("Available axial strength (Pc) must be a valid number");
  }
  if (typeof Mrx !== "number" || isNaN(Mrx)) {
    throw new Error("Required flexural strength (Mrx) must be a valid number");
  }
  if (typeof Mcx !== "number" || isNaN(Mcx)) {
    throw new Error("Available flexural strength (Mcx) must be a valid number");
  }

  // Validate sign conventions (per AISC: positive in tension, negative in compression)
  if (Pr < 0) {
    throw new Error(
      "Required axial strength (Pr) must be positive in tension for H4 analysis"
    );
  }

  // Validate available capacities (must be positive)
  if (Pc <= 0) {
    throw new Error("Available axial strength (Pc) must be positive");
  }
  if (Mcx <= 0) {
    throw new Error("Available flexural strength (Mcx) must be positive");
  }

  // Check for reasonable ratios to prevent numerical issues
  const axialRatio = Pr / Pc;
  const flexuralRatio = Math.abs(Mrx) / Mcx;

  if (axialRatio > 5.0 || flexuralRatio > 5.0) {
    throw new Error(
      "Load to capacity ratios are unusually high (>5.0). Check input values."
    );
  }
}

/**
 * Builds comprehensive metadata for H4 flange rupture analysis.
 *
 * @param params - Flange parameters used in analysis
 * @param axialRatio - Pr/Pc ratio
 * @param flexuralRatio - Mrx/Mcx ratio
 * @param totalRatio - Total H4 interaction ratio
 * @returns Comprehensive metadata object
 */
function buildH4Metadata(
  params: H4_FlangeParameters,
  axialRatio: number,
  flexuralRatio: number,
  totalRatio: number
): InteractionMetadata {
  return {
    // Basic interaction metadata
    PrOverPc: axialRatio,
    usedH1_1a: false,
    usedH1_3: false,
    usedH4: true,
    momentRatios: {
      MrxOverMcx: flexuralRatio,
      MryOverMcy: 0, // H4 only considers major-axis moment
    },
    loadingType: "tension", // H4 specifically for tension rupture
    axialLoadThreshold: 0, // No threshold for H4
    equationsUsed: ["H4-1"],

    // H4-specific results
    h4Results: {
      flangeRuptureRatio: totalRatio,
      boltHoleLocation: "Each flange checked separately",
    },

    // Additional ratios
    totalInteractionRatio: totalRatio,
    governingComponent: determineH4GoverningComponent(
      axialRatio,
      flexuralRatio
    ),
  };
}

/**
 * Determines which component governs the H4 flange rupture interaction.
 *
 * @param axialRatio - Pr/Pc ratio
 * @param flexuralRatio - Mrx/Mcx ratio
 * @returns String describing the governing component
 */
function determineH4GoverningComponent(
  axialRatio: number,
  flexuralRatio: number
): string {
  const absFlexuralRatio = Math.abs(flexuralRatio);

  if (axialRatio > absFlexuralRatio) {
    return "Axial tension (Pr/Pc)";
  } else if (absFlexuralRatio > axialRatio) {
    return "Major-axis flexure (Mrx/Mcx)";
  } else {
    return "Combined axial and flexural effects";
  }
}

/**
 * Provides guidance on H4 flange rupture analysis considerations.
 *
 * @returns Object with analysis considerations and recommendations
 */
export function getH4AnalysisGuidance(): {
  considerations: string[];
  recommendations: string[];
  limitations: string[];
} {
  return {
    considerations: [
      "H4 applies at locations of bolt holes in flanges subjected to tension",
      "Combined axial force and major-axis flexure creates tension in flanges",
      "Each flange subjected to tension shall be checked separately",
      "Uses tensile rupture strength of net section at bolt hole locations",
      "Sign convention: Pr positive in tension, Mrx positive/negative for compression/tension in flange",
    ],
    recommendations: [
      "Calculate net section properties considering bolt hole area reduction",
      "Determine which flange experiences tension under combined loading",
      "Use appropriate tensile rupture strength (φPn or Pn/Ωt) per Section D2(b)",
      "Consider both top and bottom flanges if loading varies",
      "Apply plastic moment (Mp) if tensile rupture in flexure does not apply",
    ],
    limitations: [
      "Only applies to bolt hole locations in flanges",
      "Limited to tensile rupture limit state",
      "Does not consider other limit states (yielding, local buckling, etc.)",
      "Assumes adequate bolt hole spacing and edge distances",
      "Does not account for minor-axis bending effects",
    ],
  };
}
