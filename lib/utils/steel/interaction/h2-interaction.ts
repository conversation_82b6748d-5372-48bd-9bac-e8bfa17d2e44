/**
 * AISC 360-22 Chapter H, Section H2 - Stress-Based Interaction Checks
 *
 * Implements AISC H2 for unsymmetric and other members subjected to flexure
 * and axial force using stress-based interaction equation.
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Chapter H, Section H2
 */

import { INTERACTION_CONSTANTS, ERROR_MESSAGES } from "../constants";
import {
  AppliedStresses,
  AvailableStresses,
  InteractionResult,
  InteractionLimitState,
  InteractionMetadata,
  InteractionOptions,
} from "./types";

/**
 * Calculates H2 stress-based interaction for unsymmetric and other members.
 *
 * AISC Equation H2-1:
 * |fra/Fca + frbw/Fcbw + frbz/Fcbz| ≤ 1.0
 *
 * @param appliedStresses - Required stresses at point of consideration
 * @param availableStresses - Available stresses at point of consideration
 * @param options - Analysis options
 * @returns Interaction analysis result
 *
 * @example
 * ```typescript
 * const appliedStresses = { fra: 20.5, frbw: 30.2, frbz: 8.7 };
 * const availableStresses = { Fca: 36.0, Fcbw: 36.0, Fcbz: 36.0 };
 * const result = calculateH2_StressInteraction(appliedStresses, availableStresses);
 * console.log(result.governingRatio); // 0.85
 * ```
 *
 * @source AISC 360-22, Section H2, Equation H2-1
 */
export function calculateH2_StressInteraction(
  appliedStresses: AppliedStresses,
  availableStresses: AvailableStresses,
  options: InteractionOptions = {}
): InteractionResult {
  // Validate inputs
  validateH2Inputs(appliedStresses, availableStresses);

  const {
    includeMetadata = true,
    includeSecondOrderEffects = false,
    usePrincipalAxes = true,
  } = options;

  // Extract stresses
  const { fra, frbw, frbz } = appliedStresses;
  const { Fca, Fcbw, Fcbz } = availableStresses;

  // Calculate individual stress ratios
  const axialRatio = Math.abs(fra) / Fca;
  const majorFlexuralRatio = Math.abs(frbw) / Fcbw;
  const minorFlexuralRatio = Math.abs(frbz) / Fcbz;

  // AISC Eq. H2-1: Stress interaction
  // Note: The equation considers the sense of flexural stresses
  // For simplicity, we use absolute values as worst case
  const stressInteractionRatio =
    axialRatio + majorFlexuralRatio + minorFlexuralRatio;

  // Determine if member satisfies interaction requirements
  const satisfiesInteraction =
    stressInteractionRatio <= INTERACTION_CONSTANTS.H2_UNITY_LIMIT;
  const governingRatio = stressInteractionRatio;
  const governingLimitState = satisfiesInteraction
    ? InteractionLimitState.SATISFACTORY
    : InteractionLimitState.STRESS_INTERACTION;

  // Build metadata if requested
  let metadata: InteractionMetadata | undefined;
  if (includeMetadata) {
    metadata = buildH2Metadata(
      appliedStresses,
      availableStresses,
      axialRatio,
      majorFlexuralRatio,
      minorFlexuralRatio,
      stressInteractionRatio,
      { includeSecondOrderEffects, usePrincipalAxes }
    );
  }

  return {
    satisfiesInteraction,
    governingRatio,
    governingLimitState,
    inPlaneRatio: stressInteractionRatio,
    outOfPlaneRatio: undefined, // H2 is a single interaction check
    metadata,
  };
}

/**
 * Validates input parameters for H2 stress interaction analysis.
 *
 * @param appliedStresses - Applied stresses to validate
 * @param availableStresses - Available stresses to validate
 * @throws Error if any input is invalid
 */
function validateH2Inputs(
  appliedStresses: AppliedStresses,
  availableStresses: AvailableStresses
): void {
  const { fra, frbw, frbz } = appliedStresses;
  const { Fca, Fcbw, Fcbz } = availableStresses;

  // Validate applied stresses (can be positive or negative)
  if (typeof fra !== "number" || isNaN(fra)) {
    throw new Error("Required axial stress (fra) must be a valid number");
  }
  if (typeof frbw !== "number" || isNaN(frbw)) {
    throw new Error(
      "Required major-axis flexural stress (frbw) must be a valid number"
    );
  }
  if (typeof frbz !== "number" || isNaN(frbz)) {
    throw new Error(
      "Required minor-axis flexural stress (frbz) must be a valid number"
    );
  }

  // Validate available stresses (must be positive)
  if (typeof Fca !== "number" || Fca <= 0) {
    throw new Error("Available axial stress (Fca) must be a positive number");
  }
  if (typeof Fcbw !== "number" || Fcbw <= 0) {
    throw new Error(
      "Available major-axis flexural stress (Fcbw) must be a positive number"
    );
  }
  if (typeof Fcbz !== "number" || Fcbz <= 0) {
    throw new Error(
      "Available minor-axis flexural stress (Fcbz) must be a positive number"
    );
  }

  // Check for reasonable stress values (prevent division by very small numbers)
  const minStress = INTERACTION_CONSTANTS.H2_MIN_STRESS_RATIO;
  if (Fca < minStress || Fcbw < minStress || Fcbz < minStress) {
    throw new Error(
      `Available stresses must be at least ${minStress} ksi to prevent numerical instability`
    );
  }
}

/**
 * Builds comprehensive metadata for H2 stress interaction analysis.
 *
 * @param appliedStresses - Applied stresses used in analysis
 * @param availableStresses - Available stresses used in analysis
 * @param axialRatio - Calculated axial stress ratio
 * @param majorFlexuralRatio - Calculated major-axis flexural stress ratio
 * @param minorFlexuralRatio - Calculated minor-axis flexural stress ratio
 * @param totalRatio - Total interaction ratio
 * @param options - Analysis options used
 * @returns Comprehensive metadata object
 */
function buildH2Metadata(
  appliedStresses: AppliedStresses,
  availableStresses: AvailableStresses,
  axialRatio: number,
  majorFlexuralRatio: number,
  minorFlexuralRatio: number,
  totalRatio: number,
  options: { includeSecondOrderEffects?: boolean; usePrincipalAxes?: boolean }
): InteractionMetadata {
  return {
    // Load and capacity information
    PrOverPc: axialRatio, // Reusing naming convention for consistency
    momentRatios: {
      MrxOverMcx: majorFlexuralRatio,
      MryOverMcy: minorFlexuralRatio,
    },

    // H2-specific stress ratios
    stressRatios: {
      fraOverFca: axialRatio,
      frbwOverFcbw: majorFlexuralRatio,
      frbzOverFcbz: minorFlexuralRatio,
    },

    // Applied and available values
    appliedStresses,
    availableStresses,

    // Analysis metadata
    loadingType: appliedStresses.fra >= 0 ? "compression" : "tension",
    axialLoadThreshold: INTERACTION_CONSTANTS.AXIAL_LOAD_THRESHOLD, // Not directly applicable but included for consistency
    equationsUsed: ["H2-1"],
    usedH1_1a: false,
    usedH1_3: false,
    usedH2: true,

    // H2-specific options
    includeSecondOrderEffects: options.includeSecondOrderEffects || false,
    usePrincipalAxes: options.usePrincipalAxes !== false, // Default to true

    // Analysis results
    totalInteractionRatio: totalRatio,
    governingComponent: determineGoverningComponent(
      axialRatio,
      majorFlexuralRatio,
      minorFlexuralRatio
    ),
  };
}

/**
 * Determines which stress component governs the H2 interaction.
 *
 * @param axialRatio - Axial stress ratio
 * @param majorFlexuralRatio - Major-axis flexural stress ratio
 * @param minorFlexuralRatio - Minor-axis flexural stress ratio
 * @returns String describing the governing component
 */
function determineGoverningComponent(
  axialRatio: number,
  majorFlexuralRatio: number,
  minorFlexuralRatio: number
): string {
  const maxRatio = Math.max(axialRatio, majorFlexuralRatio, minorFlexuralRatio);

  if (maxRatio === axialRatio) {
    return "Axial stress (fra/Fca)";
  } else if (maxRatio === majorFlexuralRatio) {
    return "Major-axis flexural stress (frbw/Fcbw)";
  } else {
    return "Minor-axis flexural stress (frbz/Fcbz)";
  }
}

/**
 * Calculates H2 interaction ratio for simplified analysis.
 * Returns only the interaction ratio without detailed metadata.
 *
 * @param appliedStresses - Applied stresses at point of consideration
 * @param availableStresses - Available stresses at point of consideration
 * @returns H2 interaction ratio
 *
 * @example
 * ```typescript
 * const ratio = calculateH2_InteractionRatio(
 *   { fra: 15.0, frbw: 25.0, frbz: 5.0 },
 *   { Fca: 36.0, Fcbw: 36.0, Fcbz: 36.0 }
 * );
 * console.log(ratio); // 1.25
 * ```
 */
export function calculateH2_InteractionRatio(
  appliedStresses: AppliedStresses,
  availableStresses: AvailableStresses
): number {
  const result = calculateH2_StressInteraction(
    appliedStresses,
    availableStresses,
    {
      includeMetadata: false,
    }
  );
  return result.governingRatio;
}

/**
 * Checks if H2 stress interaction method is applicable for given conditions.
 *
 * H2 is applicable when:
 * - Member has unsymmetric cross-section, or
 * - More detailed analysis is desired, or
 * - H1 method is not suitable for the member type
 *
 * @param isUnsymmetric - Whether the cross-section is unsymmetric
 * @param requiresDetailedAnalysis - Whether detailed stress analysis is required
 * @returns True if H2 method should be used
 *
 * @source AISC 360-22, Section H2 commentary
 */
export function isH2Applicable(
  isUnsymmetric: boolean = false,
  requiresDetailedAnalysis: boolean = false
): boolean {
  return isUnsymmetric || requiresDetailedAnalysis;
}

/**
 * Provides guidance on H2 stress interaction analysis considerations.
 *
 * @returns Object with analysis considerations and recommendations
 */
export function getH2AnalysisGuidance(): {
  considerations: string[];
  recommendations: string[];
  limitations: string[];
} {
  return {
    considerations: [
      "H2-1 shall be evaluated using principal bending axes for unsymmetric sections",
      "Consider the sense of flexural stresses at critical points of the cross section",
      "Flexural terms are added to or subtracted from axial term as applicable",
      "Include second-order effects when axial force is compression",
      "Use section modulus S for specific location in cross section",
    ],
    recommendations: [
      "Use H2 method for unsymmetric cross-sections",
      "Consider H2 when H1 method is not suitable for member type",
      "Perform detailed analysis of stress distribution across section",
      "Verify principal axis orientation for unsymmetric sections",
      "Check both tension and compression scenarios if applicable",
    ],
    limitations: [
      "Requires detailed stress analysis at critical points",
      "More complex than H1 force-based interaction",
      "Requires accurate determination of available stresses",
      "Second-order effects must be included for compression members",
      "Principal axes determination needed for unsymmetric sections",
    ],
  };
}
