/**
 * @file Type definitions for AISC Chapter H - Combined Forces and Interaction Analysis
 *
 * This module provides type definitions for interaction checks according to AISC 360-22
 * Chapter H - Design of Members for Combined Forces and Torsion.
 */

/**
 * Interface for applied loads/required strengths used in interaction checks.
 * All values are considered positive as per AISC user note.
 * 
 * @source AISC 360-22, Chapter H
 */
export interface AppliedLoads {
  /** Required axial strength (compression positive), kips (N) */
  Pr: number;
  /** Required major-axis flexural strength, kip-in (N-mm) */
  Mrx: number;
  /** Required minor-axis flexural strength, kip-in (N-mm) */
  Mry: number;
  /** Required torsional strength, kip-in (N-mm) - for H3 */
  Tr?: number;
}

/**
 * Interface for applied stresses used in H2 stress-based interaction checks.
 * All values are considered positive as per AISC user note.
 * 
 * @source AISC 360-22, Section H2
 */
export interface AppliedStresses {
  /** Required axial stress at the point of consideration, ksi (MPa) */
  fra: number;
  /** Required flexural stress about major principal axis (w), ksi (MPa) */
  frbw: number;
  /** Required flexural stress about minor principal axis (z), ksi (MPa) */
  frbz: number;
}

/**
 * Interface for available capacities used in interaction checks.
 * All values are positive nominal strengths.
 * 
 * @source AISC 360-22, Chapter H
 */
export interface AvailableCapacities {
  /** Available axial strength (compression or tension), kips (N) */
  Pc: number;
  /** Available major-axis flexural strength, kip-in (N-mm) */
  Mcx: number;
  /** Available minor-axis flexural strength, kip-in (N-mm) */
  Mcy: number;
  /** Available torsional strength, kip-in (N-mm) - for H3 */
  Tc?: number;
}

/**
 * Interface for available stresses used in H2 stress-based interaction checks.
 * All values are positive allowable stresses.
 * 
 * @source AISC 360-22, Section H2
 */
export interface AvailableStresses {
  /** Available axial stress at the point of consideration, ksi (MPa) */
  Fca: number;
  /** Available flexural stress about major principal axis (w), ksi (MPa) */
  Fcbw: number;
  /** Available flexural stress about minor principal axis (z), ksi (MPa) */
  Fcbz: number;
}

/**
 * Interface for H3 torsional analysis parameters for HSS members.
 * 
 * @source AISC 360-22, Section H3.1
 */
export interface H3_TorsionalParameters {
  /** Outside diameter for round HSS, in (mm) */
  D?: number;
  /** Length of member, in (mm) */
  L: number;
  /** Design wall thickness, in (mm) */
  t: number;
  /** Flat width of longer side for rectangular HSS, in (mm) */
  h?: number;
  /** Flat width of shorter side for rectangular HSS, in (mm) */
  b?: number;
  /** HSS torsional constant, in³ (mm³) */
  C: number;
  /** Yield strength, ksi (MPa) */
  Fy: number;
  /** Young's modulus, ksi (MPa) */
  E?: number;
}

/**
 * Interface for H3 combined force analysis when Tr > 20% of Tc.
 * Extends basic applied loads to include shear and torsion.
 * 
 * @source AISC 360-22, Section H3.2
 */
export interface H3_CombinedLoads extends AppliedLoads {
  /** Required shear strength, kips (N) */
  Vr: number;
  /** Required torsional strength, kip-in (N-mm) */
  Tr: number;
}

/**
 * Interface for H3 combined capacity analysis.
 * Extends basic available capacities to include shear and torsion.
 * 
 * @source AISC 360-22, Section H3.2
 */
export interface H3_CombinedCapacities extends AvailableCapacities {
  /** Available shear strength, kips (N) */
  Vc: number;
  /** Available torsional strength, kip-in (N-mm) */
  Tc: number;
}

/**
 * Interface for H4 flange rupture analysis parameters.
 * 
 * @source AISC 360-22, Section H4
 */
export interface H4_FlangeParameters {
  /** Required axial strength at bolt hole location, kips (N) */
  Pr: number;
  /** Available axial strength for tensile rupture at bolt holes, kips (N) */
  Pc: number;
  /** Required flexural strength at bolt hole location, kip-in (N-mm) */
  Mrx: number;
  /** Available flexural strength for tensile rupture, kip-in (N-mm) */
  Mcx: number;
}

/**
 * Interface for interaction parameters specific to H1.3 calculations.
 *
 * @source AISC 360-22, Section H1.3
 */
export interface H1_3_Parameters {
  /** Effective length for y-axis buckling, in (mm) */
  Lb: number;
  /** Young's modulus, ksi (MPa) */
  E: number;
  /** Moment of inertia about y-axis, in⁴ (mm⁴) */
  Iy: number;
  /** Lateral-torsional buckling modification factor */
  Cb: number;
  /** Lateral-torsional strength for major-axis flexure with Cb = 1.0, kip-in (N-mm) */
  Mcx_Cb1: number;
}

/**
 * Enumeration of AISC interaction limit states.
 */
export enum InteractionLimitState {
  /** H1.1 - In-plane instability limit state */
  IN_PLANE_INSTABILITY = "In-Plane Instability (AISC H1.1)",
  /** H1.3 - Out-of-plane buckling and lateral-torsional buckling */
  OUT_OF_PLANE_BUCKLING = "Out-of-Plane Buckling and Lateral-Torsional Buckling (AISC H1.3)",
  /** H2 - Stress-based interaction for unsymmetric and other members */
  STRESS_INTERACTION = "Stress Interaction for Unsymmetric Members (AISC H2.1)",
  /** H3 - Torsion and combined forces */
  TORSION_INTERACTION = "Torsion and Combined Forces (AISC H3)",
  /** H4 - Rupture of flanges with bolt holes */
  FLANGE_RUPTURE = "Rupture of Flanges with Bolt Holes (AISC H4.1)",
  /** Member satisfies interaction requirements */
  SATISFACTORY = "Interaction Check Satisfied",
}

/**
 * Result interface for AISC interaction analysis.
 */
export interface InteractionResult {
  /** Whether the member satisfies the interaction requirements */
  satisfiesInteraction: boolean;
  /** Maximum interaction ratio (should be ≤ 1.0) */
  governingRatio: number;
  /** Interaction ratio for in-plane instability check (H1.1) */
  inPlaneRatio: number;
  /** Interaction ratio for out-of-plane buckling check (H1.3), if applicable */
  outOfPlaneRatio?: number;
  /** Governing interaction limit state */
  governingLimitState: InteractionLimitState;
  /** Additional metadata about the analysis */
  metadata?: InteractionMetadata;
}

/**
 * Metadata interface providing detailed information about the interaction analysis.
 */
export interface InteractionMetadata {
  /** Ratio Pr/Pc used to determine which H1.1 equation applies */
  PrOverPc: number;
  /** Whether H1.1(a) equation was used (Pr/Pc ≥ 0.2) */
  usedH1_1a: boolean;
  /** Whether H1.3 separate stability check was performed */
  usedH1_3: boolean;
  /** Whether H2 stress interaction was performed */
  usedH2?: boolean;
  /** Whether H3 torsion interaction was performed */
  usedH3?: boolean;
  /** Whether H4 flange rupture was performed */
  usedH4?: boolean;
  /** Elastic buckling stress Pey for H1.3, kips (N) - if applicable */
  Pey?: number;
  /** Individual moment ratio components */
  momentRatios: {
    /** Mrx/Mcx ratio */
    MrxOverMcx: number;
    /** Mry/Mcy ratio */
    MryOverMcy: number;
  };
  /** Loading type: 'compression' or 'tension' */
  loadingType: "compression" | "tension";
  /** Applied axial load ratio threshold for equation selection */
  axialLoadThreshold: number;
  /** Source AISC equations used */
  equationsUsed: string[];
  /** H2-specific stress ratios (if applicable) */
  stressRatios?: {
    fraOverFca: number;
    frbwOverFcbw: number;
    frbzOverFcbz: number;
  };
  /** Applied stresses for H2 analysis (if applicable) */
  appliedStresses?: AppliedStresses;
  /** Available stresses for H2 analysis (if applicable) */
  availableStresses?: AvailableStresses;
  /** Whether second-order effects were included in H2 */
  includeSecondOrderEffects?: boolean;
  /** Whether principal axes were used in H2 */
  usePrincipalAxes?: boolean;
  /** Total interaction ratio for H2 */
  totalInteractionRatio?: number;
  /** Governing component description for H2 */
  governingComponent?: string;
  /** H3-specific parameters and results */
  h3Results?: {
    torsionalRatio?: number;
    usedCombinedAnalysis?: boolean;
    Fcr?: number;
    sectionType?: "round" | "rectangular";
    torsionalThreshold?: number;
  };
  /** H4-specific parameters and results */
  h4Results?: {
    flangeRuptureRatio?: number;
    boltHoleLocation?: string;
  };
}

/**
 * Options interface for controlling interaction analysis behavior.
 */
export interface InteractionOptions {
  /** Whether to perform H1.3 separate stability check for compact sections */
  performH1_3Check?: boolean;
  /** Custom threshold for H1.3 applicability (Mry/Mcy ratio) */
  h1_3Threshold?: number;
  /** Whether to include detailed calculation metadata */
  includeMetadata?: boolean;
  /** Loading type override ('compression' or 'tension') */
  loadingTypeOverride?: "compression" | "tension";
  /** Whether to include second-order effects for compression in H2 */
  includeSecondOrderEffects?: boolean;
  /** Whether to use principal axes (w,z) or geometric axes (x,y) for H2 */
  usePrincipalAxes?: boolean;
}
