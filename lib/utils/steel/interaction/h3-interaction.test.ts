/**
 * Tests for AISC 360-22 Chapter H, Section H3 - Torsion and Combined Forces
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 */

import {
  calculateTorsionalStrength_HSS,
  calculateH3_CombinedInteraction,
  isH3CombinedAnalysisRequired,
  getH3AnalysisGuidance,
} from "./h3-interaction";
import {
  H3_TorsionalParameters,
  H3_CombinedLoads,
  H3_CombinedCapacities,
  InteractionLimitState,
} from "./types";
import { INTERACTION_CONSTANTS, E } from "../constants";

describe("H3 Torsional Strength Analysis", () => {
  describe("Round HSS Torsional Strength", () => {
    it("should calculate torsional strength for round HSS using H3-2a and H3-2b", () => {
      const roundHSS: H3_TorsionalParameters = {
        D: 8.625, // 8.625" diameter
        L: 120, // 120" length
        t: 0.322, // 0.322" wall thickness
        C: 5.15, // Torsional constant
        Fy: 46, // ksi
        E: E,
      };

      const result = calculateTorsionalStrength_HSS(roundHSS, "round");

      // Manual calculation verification
      const LoverD = 120 / 8.625; // = 13.913
      const DoverT = 8.625 / 0.322; // = 26.789

      // H3-2a: 1.23E / [(L/D)(D/t)^(5/4)]
      const fcr_2a = (1.23 * E) / (LoverD * Math.pow(DoverT, 5 / 4)); // = 13.85 ksi

      // H3-2b: 0.60E / (D/t)^(3/2)
      const fcr_2b = (0.6 * E) / Math.pow(DoverT, 3 / 2); // = 12.27 ksi

      const expectedFcr = Math.min(Math.max(fcr_2a, fcr_2b), 0.6 * 46); // = 13.85 ksi (H3-2a governs)
      const expectedTn = expectedFcr * 5.15; // = 71.3 kip-in

      expect(result.Fcr).toBeCloseTo(expectedFcr, 1);
      expect(result.Tn).toBeCloseTo(expectedTn, 0);
    });

    it("should limit round HSS critical stress to 0.6Fy", () => {
      const shortRoundHSS: H3_TorsionalParameters = {
        D: 6.0,
        L: 12, // Very short length
        t: 0.5, // Thick wall
        C: 3.0,
        Fy: 50,
      };

      const result = calculateTorsionalStrength_HSS(shortRoundHSS, "round");

      // Should be limited to 0.6Fy = 30 ksi
      expect(result.Fcr).toBeCloseTo(30, 1);
      expect(result.Tn).toBeCloseTo(90, 0); // 30 * 3.0
    });
  });

  describe("Rectangular HSS Torsional Strength", () => {
    it("should calculate torsional strength using H3-3 for compact section", () => {
      const compactRectHSS: H3_TorsionalParameters = {
        h: 8.0, // 8" flat width
        t: 0.5, // 0.5" wall thickness
        L: 120,
        C: 6.8,
        Fy: 50,
      };

      const result = calculateTorsionalStrength_HSS(
        compactRectHSS,
        "rectangular"
      );

      const hOverT = 8.0 / 0.5; // = 16
      const limit1 = 2.45 * Math.sqrt(E / 50); // = 63.84

      // Since h/t = 16 < 63.84, use H3-3: Fcr = 0.6Fy
      expect(result.Fcr).toBeCloseTo(30, 1); // 0.6 * 50
      expect(result.Tn).toBeCloseTo(204, 0); // 30 * 6.8
    });

    it("should calculate torsional strength using H3-4 for intermediate section", () => {
      const intermediateRectHSS: H3_TorsionalParameters = {
        h: 12.0,
        t: 0.188, // Thin wall to get h/t = 63.83
        L: 144,
        C: 8.2,
        Fy: 50,
      };

      const result = calculateTorsionalStrength_HSS(
        intermediateRectHSS,
        "rectangular"
      );

      const hOverT = 12.0 / 0.188; // = 63.83
      const sqrtEoverFy = Math.sqrt(E / 50); // = 26.06
      const limit1 = 2.45 * sqrtEoverFy; // = 63.84
      const limit2 = 3.07 * sqrtEoverFy; // = 80.00

      // Since 63.84 < h/t < 80.00, use H3-4
      const expectedFcr = (0.6 * 50 * 2.45 * sqrtEoverFy) / hOverT; // = 30.0 ksi
      expect(result.Fcr).toBeCloseTo(expectedFcr, 1);
    });

    it("should calculate torsional strength using H3-5 for slender section", () => {
      const slenderRectHSS: H3_TorsionalParameters = {
        h: 16.0,
        t: 0.125, // Very thin wall to get h/t = 128
        L: 180,
        C: 12.5,
        Fy: 50,
      };

      const result = calculateTorsionalStrength_HSS(
        slenderRectHSS,
        "rectangular"
      );

      const hOverT = 16.0 / 0.125; // = 128
      const limit2 = 3.07 * Math.sqrt(E / 50); // = 80.00

      // Since h/t = 128 > 80.00, use H3-5
      const expectedFcr = (0.458 * Math.PI ** 2 * E) / (hOverT * hOverT); // Calculate actual Fcr
      expect(result.Fcr).toBeCloseTo(expectedFcr, 1);
      expect(result.Tn).toBeCloseTo(expectedFcr * 12.5, 0); // Fcr * C
    });

    it("should throw error for excessive h/t ratio", () => {
      const excessiveRectHSS: H3_TorsionalParameters = {
        h: 20.0,
        t: 0.075, // h/t = 266.67 > 260
        L: 144,
        C: 8.0,
        Fy: 50,
      };

      expect(() =>
        calculateTorsionalStrength_HSS(excessiveRectHSS, "rectangular")
      ).toThrow("h/t ratio of 266.67 exceeds maximum limit of 260");
    });
  });

  describe("Input Validation", () => {
    it("should validate positive parameters", () => {
      const invalidParams = {
        D: 8.0,
        L: -120, // Negative length
        t: 0.5,
        C: 5.0,
        Fy: 50,
      };

      expect(() =>
        calculateTorsionalStrength_HSS(invalidParams, "round")
      ).toThrow("All torsional parameters must be positive");
    });

    it("should require diameter for round HSS", () => {
      const missingD: H3_TorsionalParameters = {
        L: 120,
        t: 0.5,
        C: 5.0,
        Fy: 50,
      };

      expect(() => calculateTorsionalStrength_HSS(missingD, "round")).toThrow(
        "Outside diameter (D) is required for round HSS"
      );
    });

    it("should require flat width for rectangular HSS", () => {
      const missingH: H3_TorsionalParameters = {
        L: 120,
        t: 0.5,
        C: 5.0,
        Fy: 50,
      };

      expect(() =>
        calculateTorsionalStrength_HSS(missingH, "rectangular")
      ).toThrow(
        "Flat width of longer side (h) is required for rectangular HSS"
      );
    });
  });
});

describe("H3 Combined Force Interaction", () => {
  // Test data for combined analysis
  const basicH3Loads: H3_CombinedLoads = {
    Pr: 120, // 120 kips axial
    Mrx: 1800, // 1800 kip-in major-axis moment
    Mry: 600, // 600 kip-in minor-axis moment
    Vr: 45, // 45 kips shear
    Tr: 180, // 180 kip-in torsion
  };

  const basicH3Capacities: H3_CombinedCapacities = {
    Pc: 400, // 400 kips axial capacity
    Mcx: 3000, // 3000 kip-in major-axis capacity
    Mcy: 1500, // 1500 kip-in minor-axis capacity
    Vc: 150, // 150 kips shear capacity
    Tc: 600, // 600 kip-in torsional capacity
  };

  describe("Basic H3-6 Calculations", () => {
    it("should calculate H3-6 combined interaction correctly", () => {
      const result = calculateH3_CombinedInteraction(
        basicH3Loads,
        basicH3Capacities
      );

      // Manual calculation verification
      // Flexural-axial term: Pr/Pc + Mrx/Mcx + Mry/Mcy
      const flexAxialTerm = 120 / 400 + 1800 / 3000 + 600 / 1500; // = 0.3 + 0.6 + 0.4 = 1.3

      // Shear-torsion term: Vr/Vc + Tr/Tc
      const shearTorsionTerm = 45 / 150 + 180 / 600; // = 0.3 + 0.3 = 0.6

      // H3-6: (flexural-axial) + (shear-torsion)²
      const expectedRatio = 1.3 + 0.6 ** 2; // = 1.3 + 0.36 = 1.66

      expect(result.governingRatio).toBeCloseTo(expectedRatio, 3);
      expect(result.satisfiesInteraction).toBe(false); // > 1.0
      expect(result.governingLimitState).toBe(
        InteractionLimitState.TORSION_INTERACTION
      );
      expect(result.inPlaneRatio).toBeCloseTo(expectedRatio, 3);
      expect(result.outOfPlaneRatio).toBeUndefined();
    });

    it("should pass when interaction ratio ≤ 1.0", () => {
      const lowLoads: H3_CombinedLoads = {
        Pr: 80, // Reduced loads
        Mrx: 1200,
        Mry: 400,
        Vr: 30,
        Tr: 125, // Must be > 20% of 600 = 120, so use 125
      };

      const result = calculateH3_CombinedInteraction(
        lowLoads,
        basicH3Capacities
      );

      // Manual: (80/400 + 1200/3000 + 400/1500) + (30/150 + 125/600)²
      // = (0.2 + 0.4 + 0.267) + (0.2 + 0.208)² = 0.867 + 0.166 = 1.033
      expect(result.governingRatio).toBeCloseTo(1.033, 2);
      expect(result.satisfiesInteraction).toBe(false); // Still > 1.0

      // Try even lower loads with torsion > 20%
      const veryLowLoads: H3_CombinedLoads = {
        Pr: 60,
        Mrx: 900,
        Mry: 300,
        Vr: 22.5,
        Tr: 130, // Must be > 20% of 600 = 120
      };

      const result2 = calculateH3_CombinedInteraction(
        veryLowLoads,
        basicH3Capacities
      );
      // = (0.15 + 0.3 + 0.2) + (0.15 + 0.217)² = 0.65 + 0.135 = 0.785
      expect(result2.governingRatio).toBeCloseTo(0.785, 2);
      expect(result2.satisfiesInteraction).toBe(true);
      expect(result2.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });
  });

  describe("Torsional Threshold Analysis", () => {
    it("should determine when H3 combined analysis is required", () => {
      expect(isH3CombinedAnalysisRequired(180, 600)).toBe(true); // 30% > 20%
      expect(isH3CombinedAnalysisRequired(120, 600)).toBe(false); // 20% = 20% (not > 20%)
      expect(isH3CombinedAnalysisRequired(121, 600)).toBe(true); // 20.17% > 20%
      expect(isH3CombinedAnalysisRequired(100, 600)).toBe(false); // 16.67% < 20%
    });

    it("should throw error when torsional ratio ≤ 20%", () => {
      const lowTorsionLoads: H3_CombinedLoads = {
        ...basicH3Loads,
        Tr: 100, // 100/600 = 16.67% < 20%
      };

      expect(() =>
        calculateH3_CombinedInteraction(lowTorsionLoads, basicH3Capacities)
      ).toThrow(
        "Torsional ratio 0.167 ≤ 0.2. Use H1 analysis and neglect torsional effects per AISC H3.2"
      );
    });

    it("should validate torsional capacity", () => {
      expect(() => isH3CombinedAnalysisRequired(100, 0)).toThrow(
        "Available torsional strength (Tc) must be positive"
      );
    });
  });

  describe("Input Validation", () => {
    it("should validate positive applied loads", () => {
      const invalidLoads = {
        ...basicH3Loads,
        Vr: -45, // Negative shear
      };

      expect(() =>
        calculateH3_CombinedInteraction(invalidLoads, basicH3Capacities)
      ).toThrow("All applied loads must be positive (per AISC user note)");
    });

    it("should validate positive available capacities", () => {
      const invalidCapacities = {
        ...basicH3Capacities,
        Tc: 0, // Zero torsional capacity
      };

      expect(() =>
        calculateH3_CombinedInteraction(basicH3Loads, invalidCapacities)
      ).toThrow("All available capacities must be positive");
    });

    it("should detect unusually high load ratios", () => {
      const highRatioCapacities = {
        ...basicH3Capacities,
        Pc: 10, // Very low capacity creates high ratio
      };

      expect(() =>
        calculateH3_CombinedInteraction(basicH3Loads, highRatioCapacities)
      ).toThrow(
        "Load to capacity ratios are unusually high (>10). Check input values."
      );
    });
  });

  describe("Metadata and Analysis Details", () => {
    it("should provide comprehensive H3 metadata", () => {
      const result = calculateH3_CombinedInteraction(
        basicH3Loads,
        basicH3Capacities
      );

      expect(result.metadata).toBeDefined();
      expect(result.metadata!.usedH3).toBe(true);
      expect(result.metadata!.usedH1_1a).toBe(false);
      expect(result.metadata!.usedH1_3).toBe(false);

      // Check H3-specific results
      expect(result.metadata!.h3Results).toBeDefined();
      expect(result.metadata!.h3Results!.torsionalRatio).toBeCloseTo(0.3, 2); // 180/600
      expect(result.metadata!.h3Results!.usedCombinedAnalysis).toBe(true);
      expect(result.metadata!.h3Results!.torsionalThreshold).toBe(0.2);

      // Check load ratios
      expect(result.metadata!.PrOverPc).toBeCloseTo(0.3, 2); // 120/400
      expect(result.metadata!.momentRatios.MrxOverMcx).toBeCloseTo(0.6, 2); // 1800/3000
      expect(result.metadata!.momentRatios.MryOverMcy).toBeCloseTo(0.4, 2); // 600/1500

      // Check governing component
      expect(result.metadata!.governingComponent).toBe(
        "Flexural and axial forces (Pr/Pc + Mrx/Mcx + Mry/Mcy)"
      );

      // Check equations used
      expect(result.metadata!.equationsUsed).toContain("H3-6");
    });

    it("should identify shear-torsion as governing when dominant", () => {
      const shearTorsionDominant: H3_CombinedLoads = {
        Pr: 40, // Low axial
        Mrx: 600, // Low moments
        Mry: 300,
        Vr: 90, // High shear
        Tr: 240, // High torsion
      };

      const result = calculateH3_CombinedInteraction(
        shearTorsionDominant,
        basicH3Capacities
      );

      // Flexural-axial: 40/400 + 600/3000 + 300/1500 = 0.1 + 0.2 + 0.2 = 0.5
      // Shear-torsion: (90/150 + 240/600)² = (0.6 + 0.4)² = 1.0² = 1.0
      // Total: 0.5 + 1.0 = 1.5

      expect(result.metadata!.governingComponent).toBe(
        "Shear and torsion forces (Vr/Vc + Tr/Tc)²"
      );
    });
  });

  describe("Real-world Examples", () => {
    it("should analyze HSS beam with significant torsion", () => {
      // HSS8×8×1/2 under combined loading
      const hssBeamLoads: H3_CombinedLoads = {
        Pr: 150, // Moderate axial load
        Mrx: 2400, // High major-axis moment
        Mry: 800, // Moderate minor-axis moment
        Vr: 60, // Moderate shear
        Tr: 320, // Significant torsion (40% of capacity)
      };

      const hssBeamCapacities: H3_CombinedCapacities = {
        Pc: 520, // HSS compression capacity
        Mcx: 4200, // Major-axis capacity
        Mcy: 2100, // Minor-axis capacity
        Vc: 180, // Shear capacity
        Tc: 800, // Torsional capacity
      };

      const result = calculateH3_CombinedInteraction(
        hssBeamLoads,
        hssBeamCapacities
      );

      // Manual: (150/520 + 2400/4200 + 800/2100) + (60/180 + 320/800)²
      // = (0.288 + 0.571 + 0.381) + (0.333 + 0.4)² = 1.24 + 0.537 = 1.777

      expect(result.governingRatio).toBeCloseTo(1.777, 2);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.metadata!.h3Results!.torsionalRatio).toBeCloseTo(0.4, 2);
    });

    it("should analyze HSS column with torsional loading", () => {
      // HSS12×12×5/8 column
      const hssColumnLoads: H3_CombinedLoads = {
        Pr: 280, // High axial load
        Mrx: 1500, // Moderate major-axis moment
        Mry: 900, // Moderate minor-axis moment
        Vr: 40, // Low shear
        Tr: 180, // Moderate torsion (25% of capacity)
      };

      const hssColumnCapacities: H3_CombinedCapacities = {
        Pc: 850, // High compression capacity
        Mcx: 5400, // High major-axis capacity
        Mcy: 3600, // High minor-axis capacity
        Vc: 220, // High shear capacity
        Tc: 720, // Torsional capacity
      };

      const result = calculateH3_CombinedInteraction(
        hssColumnLoads,
        hssColumnCapacities
      );

      // Manual: (280/850 + 1500/5400 + 900/3600) + (40/220 + 180/720)²
      // = (0.329 + 0.278 + 0.25) + (0.182 + 0.25)² = 0.857 + 0.187 = 1.044

      expect(result.governingRatio).toBeCloseTo(1.044, 2);
      expect(result.satisfiesInteraction).toBe(false); // Just over 1.0
      expect(result.metadata!.h3Results!.torsionalRatio).toBeCloseTo(0.25, 2);
    });
  });

  describe("Constants Integration", () => {
    it("should use proper H3 constants", () => {
      expect(INTERACTION_CONSTANTS.H3_TORSION_THRESHOLD).toBe(0.2);
      expect(INTERACTION_CONSTANTS.H3_UNITY_LIMIT).toBe(1.0);
      expect(INTERACTION_CONSTANTS.H3_ROUND_COEFF_1).toBe(1.23);
      expect(INTERACTION_CONSTANTS.H3_ROUND_COEFF_2).toBe(0.6);

      const result = calculateH3_CombinedInteraction(
        {
          Pr: 60,
          Mrx: 900,
          Mry: 300,
          Vr: 22.5,
          Tr: 130, // Must be > 20% of 600 = 120
        },
        basicH3Capacities
      );

      expect(result.satisfiesInteraction).toBe(
        result.governingRatio <= INTERACTION_CONSTANTS.H3_UNITY_LIMIT
      );
    });
  });

  describe("Analysis Guidance", () => {
    it("should provide H3 analysis guidance", () => {
      const guidance = getH3AnalysisGuidance();

      expect(guidance.considerations).toContain(
        "H3 applies to HSS members subjected to torsion and combined forces"
      );
      expect(guidance.considerations).toContain(
        "When Tr ≤ 20% of Tc, use Section H1 and neglect torsional effects"
      );

      expect(guidance.recommendations).toContain(
        "Calculate available torsional strength using appropriate HSS equations"
      );

      expect(guidance.limitations).toContain(
        "H3.1 and H3.2 specifically apply to HSS members only"
      );
    });
  });
});
