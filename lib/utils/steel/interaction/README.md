# AISC Chapter H - Combined Forces and Interaction Analysis

This module implements AISC 360-22 Chapter H interaction analysis for steel members subjected to combined forces. The implementation includes both H1 force-based and H2 stress-based interaction methods.

## Overview

The module provides comprehensive interaction analysis capabilities:

- **H1 Force-Based Interaction**: For doubly symmetric members and singly symmetric members bent about the axis of symmetry
- **H2 Stress-Based Interaction**: For unsymmetric and other members subjected to flexure and axial force

## H1 Force-Based Interaction Analysis

### Implementation

The H1 implementation covers:

- **H1.1**: In-plane instability checks using equations H1-1a and H1-1b
- **H1.2**: Tension members with flexure
- **H1.3**: Separate stability checks for out-of-plane buckling

### Key Features

- Automatic equation selection based on axial load ratio (Pr/Pc ≥ 0.2)
- Optional H1.3 separate stability analysis for compact sections
- Enhanced Cb factor calculations for tension members
- Comprehensive metadata and analysis tracking

### Usage Examples

#### Basic H1 Analysis

```typescript
import { calculateH1_Interaction } from './interaction';

const loads = {
  Pr: 150,    // kips
  Mrx: 2400,  // kip-in
  Mry: 800,   // kip-in
};

const capacities = {
  Pc: 520,    // kips
  Mcx: 3600,  // kip-in
  Mcy: 1200,  // kip-in
};

const result = calculateH1_Interaction(loads, capacities);
console.log(`Interaction ratio: ${result.governingRatio}`);
console.log(`Satisfies: ${result.satisfiesInteraction}`);
```

#### H1.3 Separate Stability Check

```typescript
const h1_3_params = {
  Lb: 120,      // in
  E: 29000,     // ksi
  Iy: 144,      // in⁴
  Cb: 1.0,      // lateral-torsional buckling modification factor
  Mcx_Cb1: 3600 // kip-in
};

const result = calculateH1_Interaction(loads, capacities, h1_3_params, {
  performH1_3Check: true,
  h1_3Threshold: 0.05
});
```

#### Tension Member Analysis

```typescript
const tensionLoads = {
  Pr: -100,   // Negative for tension
  Mrx: 1800,
  Mry: 600,
};

const result = calculateH1_Interaction(tensionLoads, capacities, undefined, {
  loadingTypeOverride: "tension"
});
```

## H2 Stress-Based Interaction Analysis

### Implementation

The H2 implementation provides stress-based interaction analysis according to AISC Equation H2-1:

```
|fra/Fca + frbw/Fcbw + frbz/Fcbz| ≤ 1.0
```

Where:
- `fra` = required axial stress at point of consideration
- `frbw`, `frbz` = required flexural stresses about principal axes w and z
- `Fca` = available axial stress
- `Fcbw`, `Fcbz` = available flexural stresses about principal axes w and z

### Key Features

- Stress-based interaction for unsymmetric cross-sections
- Principal axis consideration for bending stresses
- Support for both compression and tension loading
- Comprehensive stress ratio analysis and metadata
- Governing component identification

### Usage Examples

#### Basic H2 Analysis

```typescript
import { calculateH2_StressInteraction } from './interaction';

const appliedStresses = {
  fra: 15.0,   // ksi axial stress
  frbw: 24.0,  // ksi major-axis flexural stress
  frbz: 8.0,   // ksi minor-axis flexural stress
};

const availableStresses = {
  Fca: 36.0,   // ksi available axial stress
  Fcbw: 36.0,  // ksi available major-axis flexural stress
  Fcbz: 36.0,  // ksi available minor-axis flexural stress
};

const result = calculateH2_StressInteraction(appliedStresses, availableStresses);
console.log(`Stress interaction ratio: ${result.governingRatio}`);
console.log(`Governing component: ${result.metadata?.governingComponent}`);
```

#### Unsymmetric Channel Analysis

```typescript
// Channel section with significant minor-axis bending due to asymmetry
const channelStresses = {
  fra: 12.0,   // Moderate axial stress
  frbw: 28.0,  // High major-axis bending
  frbz: 15.0,  // Significant minor-axis bending
};

const channelCapacities = {
  Fca: 32.0,   // Reduced due to slenderness
  Fcbw: 35.0,  // Good major-axis capacity
  Fcbz: 25.0,  // Reduced minor-axis capacity
};

const result = calculateH2_StressInteraction(channelStresses, channelCapacities, {
  usePrincipalAxes: true,
  includeSecondOrderEffects: true
});
```

#### Simplified H2 Analysis

```typescript
import { calculateH2_InteractionRatio } from './interaction';

// Quick ratio calculation without metadata
const ratio = calculateH2_InteractionRatio(appliedStresses, availableStresses);
console.log(`Interaction ratio: ${ratio}`);
```

### H2 Applicability

Use the `isH2Applicable` function to determine when H2 analysis is appropriate:

```typescript
import { isH2Applicable } from './interaction';

const useH2 = isH2Applicable(
  true,  // isUnsymmetric - section has unsymmetric cross-section
  false  // requiresDetailedAnalysis - detailed stress analysis desired
);
```

## Constants and Configuration

### Interaction Constants

The module uses constants from `INTERACTION_CONSTANTS`:

```typescript
// H1 Constants
AXIAL_LOAD_THRESHOLD: 0.2,        // Pr/Pc threshold for equation selection
H1_1A_MOMENT_COEFF: 8/9,          // Coefficient for H1-1a equation
H1_1B_AXIAL_COEFF: 0.5,           // Coefficient for H1-1b equation
H1_3_COEFF_1: 1.5,                // H1.3 out-of-plane buckling coefficient
H1_3_COEFF_2: 0.5,                // H1.3 out-of-plane buckling coefficient
DEFAULT_H1_3_THRESHOLD: 0.05,     // Default H1.3 applicability threshold

// H2 Constants
H2_UNITY_LIMIT: 1.0,              // Unity limit for H2-1 equation
H2_MIN_STRESS_RATIO: 0.01,        // Minimum stress ratio for numerical stability
```

## Analysis Options

### InteractionOptions Interface

```typescript
interface InteractionOptions {
  // H1 Options
  performH1_3Check?: boolean;           // Enable H1.3 separate stability check
  h1_3Threshold?: number;               // Custom H1.3 applicability threshold
  loadingTypeOverride?: "compression" | "tension";
  
  // H2 Options
  includeSecondOrderEffects?: boolean;  // Include second-order effects for compression
  usePrincipalAxes?: boolean;           // Use principal axes (w,z) vs geometric (x,y)
  
  // General Options
  includeMetadata?: boolean;            // Include detailed calculation metadata
}
```

## Result Interfaces

### InteractionResult

```typescript
interface InteractionResult {
  satisfiesInteraction: boolean;        // Whether member satisfies interaction requirements
  governingRatio: number;               // Maximum interaction ratio (≤ 1.0 for satisfaction)
  inPlaneRatio: number;                 // In-plane interaction ratio
  outOfPlaneRatio?: number;             // Out-of-plane ratio (H1.3 only)
  governingLimitState: InteractionLimitState;
  metadata?: InteractionMetadata;       // Detailed analysis information
}
```

### InteractionMetadata

Comprehensive metadata includes:

- Load and capacity ratios
- Equations used in analysis
- Loading type identification
- H2-specific stress ratios and governing components
- Analysis options and parameters

## Technical Specifications

### H1 Equations Implemented

- **H1-1a**: `Pr/Pc + (8/9)(Mrx/Mcx + Mry/Mcy) ≤ 1.0` when `Pr/Pc ≥ 0.2`
- **H1-1b**: `Pr/(2Pc) + (Mrx/Mcx + Mry/Mcy) ≤ 1.0` when `Pr/Pc < 0.2`
- **H1-3**: `Pr/Pey(1.5 - 0.5Pr/Pey) + (Mrx/CbMcx)² ≤ 1.0`

### H2 Equations Implemented

- **H2-1**: `|fra/Fca + frbw/Fcbw + frbz/Fcbz| ≤ 1.0`

### Validation and Error Handling

Both H1 and H2 implementations include:

- Comprehensive input validation
- Numerical stability checks
- Clear error messages for invalid inputs
- Graceful handling of edge cases

## Testing

The module includes comprehensive test suites:

- **H1 Tests**: 29 tests covering all H1 scenarios, edge cases, and validation
- **H2 Tests**: 24 tests covering stress-based interaction, real-world examples, and boundary conditions

### Running Tests

```bash
# Run all interaction tests
npm test -- lib/utils/steel/interaction/

# Run H1 tests only
npm test -- lib/utils/steel/interaction/h1-interaction.test.ts

# Run H2 tests only
npm test -- lib/utils/steel/interaction/h2-interaction.test.ts
```

## Future Enhancements

### Planned Features

1. **H3 Torsion Analysis**: Implementation of AISC H3 for members subjected to torsion and combined forces
2. **Enhanced Cb Calculations**: Complete implementation of enhanced Cb factors for tension members
3. **Second-Order Effects**: Detailed second-order analysis integration for H2
4. **Principal Axis Transformation**: Automatic principal axis determination for unsymmetric sections

### Integration Opportunities

- Connection with existing flexural and axial strength modules
- Integration with section property databases
- Advanced load combination handling
- Automated design optimization workflows

## References

- AISC 360-22: Specification for Structural Steel Buildings
- AISC Steel Construction Manual, 15th Edition
- AISC Design Guide 28: Stability Design of Steel Buildings

## Code Quality

The implementation follows established patterns:

- **Modular Design**: Separate modules for H1 and H2 with clean interfaces
- **Type Safety**: Full TypeScript type definitions and validation
- **Documentation**: Comprehensive JSDoc comments with AISC equation references
- **Testing**: Thorough test coverage with real-world examples
- **Constants Reuse**: Leverages existing steel design constants and utilities 