/**
 * @file H1 Interaction calculations according to AISC 360-22 Section H1.
 *
 * This module implements interaction checks for doubly and singly symmetric members
 * subjected to combined flexure and axial force, including provisions for:
 * - H1.1: In-plane instability (compression and tension)
 * - H1.2: Flexure and tension with enhanced Cb factors
 * - H1.3: Separate stability checks for compact sections
 *
 * @source AISC 360-22, Chapter H - Design of Members for Combined Forces and Torsion
 */

import { E, INTERACTION_CONSTANTS, ERROR_MESSAGES } from "../constants";
import { Section } from "../slenderness/types";
import {
  AppliedLoads,
  AvailableCapacities,
  H1_3_Parameters,
  InteractionResult,
  InteractionLimitState,
  InteractionMetadata,
  InteractionOptions,
} from "./types";

/**
 * Calculates the elastic buckling stress Pey for H1.3 calculations.
 *
 * @param section - Cross-section properties
 * @param Lb - Effective length for y-axis buckling in inches
 * @param E_steel - Modulus of elasticity in ksi (defaults to 29000 ksi)
 * @returns Elastic buckling stress Pey in kips
 *
 * @source AISC 360-22, Equation H1-2
 */
function calculatePey(
  section: Section,
  Lb: number,
  E_steel: number = E
): number {
  const { Iy } = section;

  if (typeof Iy !== "number" || Iy <= 0) {
    throw new Error(
      "Moment of inertia about y-axis (Iy) must be a positive number"
    );
  }

  if (Lb <= 0) {
    throw new Error("Effective length (Lb) must be a positive number");
  }

  // AISC Eq. H1-2: Pey = π²EIy / Lb²
  return (Math.PI ** 2 * E_steel * Iy) / Lb ** 2;
}

/**
 * Performs H1.1 in-plane instability check for compression.
 *
 * This function implements AISC equations H1-1a and H1-1b based on the axial load ratio.
 *
 * @param loads - Applied loads/required strengths
 * @param capacities - Available capacities
 * @returns Interaction ratio for H1.1 check
 *
 * @source AISC 360-22, Section H1.1, Equations H1-1a and H1-1b
 */
function calculateH1_1_Compression(
  loads: AppliedLoads,
  capacities: AvailableCapacities
): { ratio: number; usedH1_1a: boolean; equationsUsed: string[] } {
  const { Pr, Mrx, Mry } = loads;
  const { Pc, Mcx, Mcy } = capacities;

  // Calculate ratios
  const PrOverPc = Pr / Pc;
  const MrxOverMcx = Mrx / Mcx;
  const MryOverMcy = Mry / Mcy;

  let ratio: number;
  let usedH1_1a: boolean;
  let equationsUsed: string[];

  if (PrOverPc >= INTERACTION_CONSTANTS.AXIAL_LOAD_THRESHOLD) {
    // H1.1(a) - AISC Eq. H1-1a: When Pr/Pc ≥ 0.2
    // Pr/Pc + (8/9)(Mrx/Mcx + Mry/Mcy) ≤ 1.0
    ratio =
      PrOverPc +
      INTERACTION_CONSTANTS.H1_1A_MOMENT_COEFF * (MrxOverMcx + MryOverMcy);
    usedH1_1a = true;
    equationsUsed = ["H1-1a"];
  } else {
    // H1.1(b) - AISC Eq. H1-1b: When Pr/Pc < 0.2
    // Pr/(2Pc) + (Mrx/Mcx + Mry/Mcy) ≤ 1.0
    ratio = Pr / (2 * Pc) + (MrxOverMcx + MryOverMcy);
    usedH1_1a = false;
    equationsUsed = ["H1-1b"];
  }

  return { ratio, usedH1_1a, equationsUsed };
}

/**
 * Performs H1.2 in-plane instability check for tension.
 *
 * For tension members, the same equations H1-1a and H1-1b apply, but with enhanced
 * Cb factors permitted when axial tension acts concurrently with flexure.
 *
 * @param loads - Applied loads/required strengths
 * @param capacities - Available capacities
 * @returns Interaction ratio for H1.2 check
 *
 * @source AISC 360-22, Section H1.2
 */
function calculateH1_2_Tension(
  loads: AppliedLoads,
  capacities: AvailableCapacities
): { ratio: number; usedH1_1a: boolean; equationsUsed: string[] } {
  // For tension, use the same equations as H1.1 but note enhanced Cb is permitted
  // The enhancement is typically applied during the flexural strength calculation
  return calculateH1_1_Compression(loads, capacities);
}

/**
 * Performs H1.3 separate stability check for compact sections.
 *
 * This check applies to doubly symmetric rolled compact members with moments
 * primarily about their major axis and specific geometric constraints.
 *
 * @param loads - Applied loads/required strengths
 * @param capacities - Available capacities
 * @param h1_3_params - H1.3 specific parameters
 * @returns Interaction ratio for H1.3 check and elastic buckling stress
 *
 * @source AISC 360-22, Section H1.3, Equation H1-3
 */
function calculateH1_3_SeparateStability(
  loads: AppliedLoads,
  capacities: AvailableCapacities,
  h1_3_params: H1_3_Parameters
): { ratio: number; Pey: number; equationsUsed: string[] } {
  const { Pr, Mrx } = loads;
  const { Lb, E: E_steel, Iy, Cb, Mcx_Cb1 } = h1_3_params;

  // Calculate elastic buckling stress Pey
  const Pey = calculatePey({ Iy } as Section, Lb, E_steel);

  // AISC Eq. H1-3: Pr/Pey * (1.5 - 0.5*Pr/Pey) + (Mrx/(Cb*Mcx))² ≤ 1.0
  const PrOverPey = Pr / Pey;
  const term1 =
    PrOverPey *
    (INTERACTION_CONSTANTS.H1_3_COEFF_1 -
      INTERACTION_CONSTANTS.H1_3_COEFF_2 * PrOverPey);
  const term2 = Math.pow(Mrx / (Cb * Mcx_Cb1), 2);

  const ratio = term1 + term2;

  return { ratio, Pey, equationsUsed: ["H1-3"] };
}

/**
 * Determines whether H1.3 separate stability check is applicable.
 *
 * H1.3 applies to doubly symmetric rolled compact members when:
 * - Effective length conditions are met
 * - Moments are primarily about major axis (Mry/Mcy ≥ 0.05 is used as threshold)
 *
 * @param loads - Applied loads
 * @param capacities - Available capacities
 * @param h1_3_params - H1.3 parameters
 * @param h1_3_threshold - Threshold for moment ratio (default 0.05)
 * @returns Whether H1.3 check is applicable
 *
 * @source AISC 360-22, Section H1.3
 */
function isH1_3Applicable(
  loads: AppliedLoads,
  capacities: AvailableCapacities,
  h1_3_params: H1_3_Parameters,
  h1_3_threshold: number = INTERACTION_CONSTANTS.DEFAULT_H1_3_THRESHOLD
): boolean {
  const { Mry } = loads;
  const { Mcy } = capacities;

  // Check if moment ratio exceeds threshold (indicating primarily major-axis bending)
  const MryOverMcy = Mry / Mcy;

  // H1.3 applies when Mry/Mcy ≥ threshold (meaning moments are primarily about major axis)
  // and other geometric conditions are met (assumed to be checked externally)
  return MryOverMcy >= h1_3_threshold;
}

/**
 * Calculates interaction ratios according to AISC 360-22 Section H1.
 *
 * This is the main function that performs comprehensive H1 interaction checks for
 * doubly and singly symmetric members subjected to flexure and axial force.
 *
 * @param loads - Applied loads/required strengths
 * @param capacities - Available capacities/strengths
 * @param h1_3_params - Parameters for H1.3 separate stability check (optional)
 * @param options - Analysis options (optional)
 * @returns Complete interaction analysis results
 *
 * @source AISC 360-22, Section H1
 * @throws {Error} If required inputs are invalid or missing
 *
 * @example
 * ```typescript
 * const loads: AppliedLoads = { Pr: 100, Mrx: 500, Mry: 50 };
 * const capacities: AvailableCapacities = { Pc: 800, Mcx: 2000, Mcy: 800 };
 * const h1_3_params: H1_3_Parameters = {
 *   Lb: 180, E: 29000, Iy: 204, Cb: 1.0, Mcx_Cb1: 1800
 * };
 *
 * const result = calculateH1_Interaction(loads, capacities, h1_3_params);
 * console.log(`Interaction ratio: ${result.maxRatio.toFixed(3)}, Satisfies: ${result.satisfies}`);
 * ```
 */
export function calculateH1_Interaction(
  loads: AppliedLoads,
  capacities: AvailableCapacities,
  h1_3_params?: H1_3_Parameters,
  options: InteractionOptions = {}
): InteractionResult {
  const {
    performH1_3Check = true,
    h1_3Threshold = INTERACTION_CONSTANTS.DEFAULT_H1_3_THRESHOLD,
    includeMetadata = true,
    loadingTypeOverride,
  } = options;

  // Validate inputs
  if (loads.Pr < 0 || loads.Mrx < 0 || loads.Mry < 0) {
    throw new Error(
      "All applied loads must be positive (as per AISC user note)"
    );
  }

  if (capacities.Pc <= 0 || capacities.Mcx <= 0 || capacities.Mcy <= 0) {
    throw new Error("All available capacities must be positive");
  }

  // Determine loading type
  const loadingType =
    loadingTypeOverride || (loads.Pr > 0 ? "compression" : "tension");

  // Calculate moment ratios
  const MrxOverMcx = loads.Mrx / capacities.Mcx;
  const MryOverMcy = loads.Mry / capacities.Mcy;
  const PrOverPc = loads.Pr / capacities.Pc;

  let inPlaneResult: {
    ratio: number;
    usedH1_1a: boolean;
    equationsUsed: string[];
  };
  let outOfPlaneResult:
    | { ratio: number; Pey: number; equationsUsed: string[] }
    | undefined;
  let usedH1_3 = false;

  // Perform H1.1 or H1.2 in-plane instability check
  if (loadingType === "compression") {
    inPlaneResult = calculateH1_1_Compression(loads, capacities);
  } else {
    inPlaneResult = calculateH1_2_Tension(loads, capacities);
  }

  // Perform H1.3 separate stability check if applicable and requested
  if (performH1_3Check && h1_3_params && loadingType === "compression") {
    if (isH1_3Applicable(loads, capacities, h1_3_params, h1_3Threshold)) {
      outOfPlaneResult = calculateH1_3_SeparateStability(
        loads,
        capacities,
        h1_3_params
      );
      usedH1_3 = true;
    }
  }

  // Determine governing interaction ratio and limit state
  const inPlaneRatio = inPlaneResult.ratio;
  const outOfPlaneRatio = outOfPlaneResult?.ratio;

  let maxRatio = inPlaneRatio;
  let governingLimitState = InteractionLimitState.IN_PLANE_INSTABILITY;

  if (outOfPlaneRatio !== undefined && outOfPlaneRatio > maxRatio) {
    maxRatio = outOfPlaneRatio;
    governingLimitState = InteractionLimitState.OUT_OF_PLANE_BUCKLING;
  }

  // Check if requirements are satisfied
  const satisfies = maxRatio <= 1.0;
  if (satisfies && maxRatio < 1.0) {
    governingLimitState = InteractionLimitState.SATISFACTORY;
  }

  // Compile equations used
  const allEquationsUsed = [
    ...inPlaneResult.equationsUsed,
    ...(outOfPlaneResult?.equationsUsed || []),
  ];

  // Build metadata
  const metadata: InteractionMetadata = {
    PrOverPc,
    usedH1_1a: inPlaneResult.usedH1_1a,
    usedH1_3,
    Pey: outOfPlaneResult?.Pey,
    momentRatios: {
      MrxOverMcx,
      MryOverMcy,
    },
    loadingType,
    axialLoadThreshold: INTERACTION_CONSTANTS.AXIAL_LOAD_THRESHOLD,
    equationsUsed: allEquationsUsed,
  };

  return {
    satisfiesInteraction: satisfies,
    governingRatio: maxRatio,
    inPlaneRatio,
    outOfPlaneRatio,
    governingLimitState,
    metadata: includeMetadata ? metadata : undefined,
  };
}

/**
 * Simplified H1 interaction check using conservative assumptions.
 *
 * This function provides a streamlined approach for preliminary design,
 * using only H1.1 equations without H1.3 separate stability checks.
 *
 * @param loads - Applied loads/required strengths
 * @param capacities - Available capacities
 * @param isCompression - Whether the axial load is compression (default: true)
 * @returns Simplified interaction result
 *
 * @source AISC 360-22, Section H1.1
 */
export function calculateH1_Interaction_Simplified(
  loads: AppliedLoads,
  capacities: AvailableCapacities,
  isCompression: boolean = true
): InteractionResult {
  return calculateH1_Interaction(loads, capacities, undefined, {
    performH1_3Check: false,
    includeMetadata: true,
    loadingTypeOverride: isCompression ? "compression" : "tension",
  });
}

/**
 * Calculates the enhanced Cb factor for tension members per H1.2.
 *
 * For doubly symmetric members with axial tension concurrent with flexure,
 * Cb can be enhanced using specific formulations.
 *
 * @param section - Cross-section properties
 * @param Pr - Required axial tensile strength in kips
 * @param designMethod - LRFD (α=1.0) or ASD (α=1.6)
 * @returns Enhanced Cb factor
 *
 * @source AISC 360-22, Section H1.2, Equation H1-2
 */
export function calculateEnhancedCb_H1_2(
  section: Section,
  Pr: number,
  designMethod: "LRFD" | "ASD" = "LRFD"
): number {
  const { Iy } = section;

  if (typeof Iy !== "number" || Iy <= 0) {
    throw new Error(
      "Moment of inertia about y-axis (Iy) is required for enhanced Cb calculation"
    );
  }

  if (Pr <= 0) {
    throw new Error(
      "Axial tensile strength (Pr) must be positive for enhanced Cb calculation"
    );
  }

  // Load combination factor α
  const alpha = designMethod === "LRFD" ? 1.0 : 1.6;

  // Calculate Pey (this would typically require effective length)
  // For now, return a conservative factor of 1.0
  // In practice, this would use: sqrt(1 + αPr/Pey)

  console.warn(
    "Enhanced Cb calculation requires effective length parameter (Lb). Returning conservative Cb = 1.0"
  );
  return 1.0;
}

/**
 * Determines resistance factors for H1 interaction checks.
 *
 * Interaction checks use the individual member resistance factors
 * from the component strength calculations.
 *
 * @returns Object containing resistance factor information
 *
 * @source AISC 360-22, Section H1
 */
export function getH1_ResistanceFactors(): {
  note: string;
  phi_compression: number;
  phi_flexure: number;
  omega_compression: number;
  omega_flexure: number;
} {
  return {
    note: "H1 interaction checks use individual member resistance factors from component strength calculations",
    phi_compression: 0.9, // Typical compression resistance factor
    phi_flexure: 0.9, // Typical flexural resistance factor
    omega_compression: 1.67, // Typical compression safety factor
    omega_flexure: 1.67, // Typical flexural safety factor
  };
}
