/**
 * @file Main entry point for steel interaction calculations.
 * Exports all interaction analysis functions and types.
 */

// Re-export types
export type {
  AppliedLoads,
  AvailableCapacities,
  AppliedStresses,
  AvailableStresses,
  H1_3_Parameters,
  H3_TorsionalParameters,
  H3_CombinedLoads,
  H3_CombinedCapacities,
  H4_FlangeParameters,
  InteractionResult,
  InteractionMetadata,
  InteractionOptions,
} from "./types";

export { InteractionLimitState } from "./types";

// Re-export H1 functions
export {
  calculateH1_Interaction,
  calculateH1_Interaction_Simplified,
  calculateEnhancedCb_H1_2,
  getH1_ResistanceFactors,
} from "./h1-interaction";

// Re-export H2 functions
export {
  calculateH2_StressInteraction,
  calculateH2_InteractionRatio,
  isH2Applicable,
  getH2AnalysisGuidance,
} from "./h2-interaction";

// Re-export H3 functions
export {
  calculateTorsionalStrength_HSS,
  calculateH3_CombinedInteraction,
  isH3CombinedAnalysisRequired,
  getH3AnalysisGuidance,
} from "./h3-interaction";

// Re-export H4 functions
export {
  calculateH4_FlangeRupture,
  calculateH4_InteractionRatio,
  isH4Applicable,
  getH4AnalysisGuidance,
} from "./h4-interaction";
