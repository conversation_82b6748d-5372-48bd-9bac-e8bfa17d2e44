/**
 * @file Test suite for AISC H2 stress-based interaction analysis
 *
 * Tests the implementation of AISC 360-22 Section H2 for unsymmetric
 * and other members subjected to flexure and axial force.
 */

import {
  calculateH2_StressInteraction,
  calculateH2_InteractionRatio,
  isH2Applicable,
  getH2AnalysisGuidance,
} from "./h2-interaction";
import {
  AppliedStresses,
  AvailableStresses,
  InteractionLimitState,
  InteractionOptions,
} from "./types";
import { INTERACTION_CONSTANTS } from "../constants";

describe("H2 Stress-Based Interaction Analysis (AISC 360-22)", () => {
  // Test data - typical steel stresses
  const basicAppliedStresses: AppliedStresses = {
    fra: 15.0, // 15 ksi axial stress
    frbw: 24.0, // 24 ksi major-axis flexural stress
    frbz: 8.0, // 8 ksi minor-axis flexural stress
  };

  const basicAvailableStresses: AvailableStresses = {
    Fca: 36.0, // 36 ksi available axial stress (Fy = 50 ksi)
    Fcbw: 36.0, // 36 ksi available major-axis flexural stress
    Fcbz: 36.0, // 36 ksi available minor-axis flexural stress
  };

  describe("Basic H2 Stress Interaction", () => {
    it("should calculate H2-1 interaction correctly", () => {
      const result = calculateH2_StressInteraction(
        basicAppliedStresses,
        basicAvailableStresses
      );

      // Manual calculation: |15/36 + 24/36 + 8/36| = |0.4167 + 0.6667 + 0.2222| = 1.3056
      const expectedRatio = 15 / 36 + 24 / 36 + 8 / 36;
      expect(result.governingRatio).toBeCloseTo(expectedRatio, 4);
      expect(result.satisfiesInteraction).toBe(false); // > 1.0
      expect(result.governingLimitState).toBe(
        InteractionLimitState.STRESS_INTERACTION
      );
      expect(result.inPlaneRatio).toBeCloseTo(expectedRatio, 4);
      expect(result.outOfPlaneRatio).toBeUndefined();
    });

    it("should identify satisfactory interaction when ratio ≤ 1.0", () => {
      const smallStresses: AppliedStresses = {
        fra: 8.0,
        frbw: 12.0,
        frbz: 4.0,
      };

      const result = calculateH2_StressInteraction(
        smallStresses,
        basicAvailableStresses
      );

      // Manual calculation: |8/36 + 12/36 + 4/36| = 0.6667
      expect(result.governingRatio).toBeCloseTo(0.6667, 4);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });

    it("should handle negative stresses correctly", () => {
      const negativeStresses: AppliedStresses = {
        fra: -10.0, // Tension
        frbw: 20.0,
        frbz: -5.0, // Negative bending
      };

      const result = calculateH2_StressInteraction(
        negativeStresses,
        basicAvailableStresses
      );

      // Uses absolute values: |10/36 + 20/36 + 5/36| = 0.9722
      const expectedRatio = 10 / 36 + 20 / 36 + 5 / 36;
      expect(result.governingRatio).toBeCloseTo(expectedRatio, 4);
      expect(result.satisfiesInteraction).toBe(true);
    });

    it("should use simplified calculation method", () => {
      const ratio = calculateH2_InteractionRatio(
        basicAppliedStresses,
        basicAvailableStresses
      );

      const expectedRatio = 15 / 36 + 24 / 36 + 8 / 36;
      expect(ratio).toBeCloseTo(expectedRatio, 4);
    });
  });

  describe("Input Validation", () => {
    it("should validate applied stress inputs", () => {
      const invalidFra = { fra: NaN, frbw: 20.0, frbz: 5.0 };
      expect(() =>
        calculateH2_StressInteraction(invalidFra, basicAvailableStresses)
      ).toThrow("Required axial stress (fra) must be a valid number");

      const invalidFrbw = { fra: 10.0, frbw: NaN, frbz: 5.0 };
      expect(() =>
        calculateH2_StressInteraction(invalidFrbw, basicAvailableStresses)
      ).toThrow(
        "Required major-axis flexural stress (frbw) must be a valid number"
      );

      const invalidFrbz = { fra: 10.0, frbw: 20.0, frbz: undefined as any };
      expect(() =>
        calculateH2_StressInteraction(invalidFrbz, basicAvailableStresses)
      ).toThrow(
        "Required minor-axis flexural stress (frbz) must be a valid number"
      );
    });

    it("should validate available stress inputs", () => {
      const invalidFca = { Fca: 0, Fcbw: 36.0, Fcbz: 36.0 };
      expect(() =>
        calculateH2_StressInteraction(basicAppliedStresses, invalidFca)
      ).toThrow("Available axial stress (Fca) must be a positive number");

      const invalidFcbw = { Fca: 36.0, Fcbw: -5.0, Fcbz: 36.0 };
      expect(() =>
        calculateH2_StressInteraction(basicAppliedStresses, invalidFcbw)
      ).toThrow(
        "Available major-axis flexural stress (Fcbw) must be a positive number"
      );

      const invalidFcbz = { Fca: 36.0, Fcbw: 36.0, Fcbz: 0 };
      expect(() =>
        calculateH2_StressInteraction(basicAppliedStresses, invalidFcbz)
      ).toThrow(
        "Available minor-axis flexural stress (Fcbz) must be a positive number"
      );
    });

    it("should prevent numerical instability with very small stresses", () => {
      const tinyStresses: AvailableStresses = {
        Fca: 0.005, // Below minimum threshold
        Fcbw: 36.0,
        Fcbz: 36.0,
      };

      expect(() =>
        calculateH2_StressInteraction(basicAppliedStresses, tinyStresses)
      ).toThrow(
        "Available stresses must be at least 0.01 ksi to prevent numerical instability"
      );
    });

    it("should handle zero applied stresses gracefully", () => {
      const zeroStresses: AppliedStresses = {
        fra: 0,
        frbw: 0,
        frbz: 0,
      };

      const result = calculateH2_StressInteraction(
        zeroStresses,
        basicAvailableStresses
      );

      expect(result.governingRatio).toBe(0);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });
  });

  describe("Metadata and Analysis Options", () => {
    it("should provide comprehensive metadata", () => {
      const result = calculateH2_StressInteraction(
        basicAppliedStresses,
        basicAvailableStresses,
        { includeMetadata: true }
      );

      expect(result.metadata).toBeDefined();
      expect(result.metadata!.usedH2).toBe(true);
      expect(result.metadata!.usedH1_1a).toBe(false);
      expect(result.metadata!.usedH1_3).toBe(false);
      expect(result.metadata!.equationsUsed).toContain("H2-1");
      expect(result.metadata!.loadingType).toBe("compression"); // fra > 0

      // Check stress ratios
      expect(result.metadata!.stressRatios!.fraOverFca).toBeCloseTo(15 / 36, 4);
      expect(result.metadata!.stressRatios!.frbwOverFcbw).toBeCloseTo(
        24 / 36,
        4
      );
      expect(result.metadata!.stressRatios!.frbzOverFcbz).toBeCloseTo(
        8 / 36,
        4
      );

      // Check applied and available stresses
      expect(result.metadata!.appliedStresses).toEqual(basicAppliedStresses);
      expect(result.metadata!.availableStresses).toEqual(
        basicAvailableStresses
      );

      // Check governing component
      expect(result.metadata!.governingComponent).toBe(
        "Major-axis flexural stress (frbw/Fcbw)"
      );
    });

    it("should identify loading type correctly", () => {
      const tensionStresses: AppliedStresses = {
        fra: -15.0, // Negative = tension
        frbw: 20.0,
        frbz: 5.0,
      };

      const result = calculateH2_StressInteraction(
        tensionStresses,
        basicAvailableStresses
      );

      expect(result.metadata!.loadingType).toBe("tension");
    });

    it("should handle analysis options", () => {
      const options: InteractionOptions = {
        includeSecondOrderEffects: true,
        usePrincipalAxes: false,
        includeMetadata: true,
      };

      const result = calculateH2_StressInteraction(
        basicAppliedStresses,
        basicAvailableStresses,
        options
      );

      expect(result.metadata!.includeSecondOrderEffects).toBe(true);
      expect(result.metadata!.usePrincipalAxes).toBe(false);
    });

    it("should allow metadata to be excluded", () => {
      const result = calculateH2_StressInteraction(
        basicAppliedStresses,
        basicAvailableStresses,
        { includeMetadata: false }
      );

      expect(result.metadata).toBeUndefined();
    });

    it("should identify governing stress components correctly", () => {
      // Test with axial governing
      const axialGoverning: AppliedStresses = {
        fra: 30.0,
        frbw: 10.0,
        frbz: 5.0,
      };
      const result1 = calculateH2_StressInteraction(
        axialGoverning,
        basicAvailableStresses
      );
      expect(result1.metadata!.governingComponent).toBe(
        "Axial stress (fra/Fca)"
      );

      // Test with minor-axis governing
      const minorGoverning: AppliedStresses = {
        fra: 5.0,
        frbw: 10.0,
        frbz: 30.0,
      };
      const result2 = calculateH2_StressInteraction(
        minorGoverning,
        basicAvailableStresses
      );
      expect(result2.metadata!.governingComponent).toBe(
        "Minor-axis flexural stress (frbz/Fcbz)"
      );
    });
  });

  describe("Real-World Examples", () => {
    it("should analyze unsymmetric channel section", () => {
      // Typical unsymmetric channel under combined loading
      const channelStresses: AppliedStresses = {
        fra: 12.0, // Moderate axial stress
        frbw: 28.0, // High major-axis bending
        frbz: 15.0, // Significant minor-axis bending due to asymmetry
      };

      const channelCapacities: AvailableStresses = {
        Fca: 32.0, // Reduced due to slenderness
        Fcbw: 35.0, // Good major-axis capacity
        Fcbz: 25.0, // Reduced minor-axis capacity
      };

      const result = calculateH2_StressInteraction(
        channelStresses,
        channelCapacities
      );

      // Manual: 12/32 + 28/35 + 15/25 = 0.375 + 0.8 + 0.6 = 1.775
      expect(result.governingRatio).toBeCloseTo(1.775, 3);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.metadata!.governingComponent).toBe(
        "Major-axis flexural stress (frbw/Fcbw)"
      );
    });

    it("should analyze angle member under biaxial bending", () => {
      // Single angle with biaxial moments about principal axes
      const angleStresses: AppliedStresses = {
        fra: 8.0, // Light axial load
        frbw: 20.0, // Principal axis w
        frbz: 18.0, // Principal axis z
      };

      const angleCapacities: AvailableStresses = {
        Fca: 28.0, // Reduced for angle slenderness
        Fcbw: 30.0, // About strong principal axis
        Fcbz: 25.0, // About weak principal axis
      };

      const result = calculateH2_StressInteraction(
        angleStresses,
        angleCapacities,
        { usePrincipalAxes: true }
      );

      // Manual: 8/28 + 20/30 + 18/25 = 0.2857 + 0.6667 + 0.72 = 1.6724
      expect(result.governingRatio).toBeCloseTo(1.6724, 3);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.metadata!.usePrincipalAxes).toBe(true);
    });

    it("should analyze lightly loaded member", () => {
      const lightStresses: AppliedStresses = {
        fra: 3.0,
        frbw: 8.0,
        frbz: 2.0,
      };

      const result = calculateH2_StressInteraction(
        lightStresses,
        basicAvailableStresses
      );

      // Manual: 3/36 + 8/36 + 2/36 = 0.361
      expect(result.governingRatio).toBeCloseTo(0.361, 3);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle unity interaction ratio", () => {
      // Design exactly at limit
      const unityStresses: AppliedStresses = {
        fra: 12.0, // 12/36 = 1/3
        frbw: 18.0, // 18/36 = 1/2
        frbz: 6.0, // 6/36 = 1/6
      };
      // Total: 1/3 + 1/2 + 1/6 = 1.0

      const result = calculateH2_StressInteraction(
        unityStresses,
        basicAvailableStresses
      );

      expect(result.governingRatio).toBeCloseTo(1.0, 4);
      expect(result.satisfiesInteraction).toBe(true); // Exactly at limit
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });

    it("should handle very high stress ratios", () => {
      const highStresses: AppliedStresses = {
        fra: 50.0, // Higher than available
        frbw: 40.0,
        frbz: 30.0,
      };

      const result = calculateH2_StressInteraction(
        highStresses,
        basicAvailableStresses
      );

      expect(result.governingRatio).toBeGreaterThan(2.0);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.STRESS_INTERACTION
      );
    });

    it("should handle different stress capacity combinations", () => {
      const variedCapacities: AvailableStresses = {
        Fca: 50.0, // High axial capacity
        Fcbw: 20.0, // Low major-axis capacity
        Fcbz: 40.0, // Medium minor-axis capacity
      };

      const result = calculateH2_StressInteraction(
        basicAppliedStresses,
        variedCapacities
      );

      // Manual: 15/50 + 24/20 + 8/40 = 0.3 + 1.2 + 0.2 = 1.7
      expect(result.governingRatio).toBeCloseTo(1.7, 3);
      expect(result.metadata!.governingComponent).toBe(
        "Major-axis flexural stress (frbw/Fcbw)"
      );
    });
  });

  describe("Applicability and Guidance", () => {
    it("should determine H2 applicability correctly", () => {
      expect(isH2Applicable(true, false)).toBe(true); // Unsymmetric section
      expect(isH2Applicable(false, true)).toBe(true); // Detailed analysis requested
      expect(isH2Applicable(true, true)).toBe(true); // Both conditions
      expect(isH2Applicable(false, false)).toBe(false); // Neither condition
    });

    it("should provide analysis guidance", () => {
      const guidance = getH2AnalysisGuidance();

      expect(guidance.considerations).toBeInstanceOf(Array);
      expect(guidance.recommendations).toBeInstanceOf(Array);
      expect(guidance.limitations).toBeInstanceOf(Array);

      expect(guidance.considerations.length).toBeGreaterThan(0);
      expect(guidance.recommendations.length).toBeGreaterThan(0);
      expect(guidance.limitations.length).toBeGreaterThan(0);

      // Check for key considerations
      expect(guidance.considerations).toEqual(
        expect.arrayContaining([
          expect.stringContaining("principal bending axes"),
          expect.stringContaining("second-order effects"),
        ])
      );
    });
  });

  describe("Comparison with Unity Limit", () => {
    it("should respect AISC H2-1 unity limit constant", () => {
      const result = calculateH2_StressInteraction(
        basicAppliedStresses,
        basicAvailableStresses
      );

      const unityLimit = INTERACTION_CONSTANTS.H2_UNITY_LIMIT;
      expect(unityLimit).toBe(1.0);
      expect(result.satisfiesInteraction).toBe(
        result.governingRatio <= unityLimit
      );
    });

    it("should handle stress ratios at and slightly above unity", () => {
      // Slightly over unity
      const overUnityStresses: AppliedStresses = {
        fra: 12.24, // Makes total = 1.01
        frbw: 18.0,
        frbz: 6.0,
      };

      const result = calculateH2_StressInteraction(
        overUnityStresses,
        basicAvailableStresses
      );

      expect(result.governingRatio).toBeGreaterThan(1.0);
      expect(result.governingRatio).toBeLessThan(1.02);
      expect(result.satisfiesInteraction).toBe(false);
    });
  });

  describe("Integration with Existing Constants", () => {
    it("should use correct minimum stress ratio threshold", () => {
      const minThreshold = INTERACTION_CONSTANTS.H2_MIN_STRESS_RATIO;
      expect(minThreshold).toBe(0.01);

      const belowThreshold: AvailableStresses = {
        Fca: 0.005, // Below threshold
        Fcbw: 36.0,
        Fcbz: 36.0,
      };

      expect(() =>
        calculateH2_StressInteraction(basicAppliedStresses, belowThreshold)
      ).toThrow(
        "Available stresses must be at least 0.01 ksi to prevent numerical instability"
      );
    });
  });
});
