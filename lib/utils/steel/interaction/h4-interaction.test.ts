/**
 * Tests for AISC 360-22 Chapter H, Section H4 - Rupture of Flanges with Bolt Holes
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 */

import {
  calculateH4_FlangeRupture,
  calculateH4_InteractionRatio,
  isH4Applicable,
  getH4AnalysisGuidance,
} from "./h4-interaction";
import { H4_FlangeParameters, InteractionLimitState } from "./types";
import { INTERACTION_CONSTANTS } from "../constants";

describe("H4 Flange Rupture Analysis", () => {
  // Test data - typical flange with bolt holes
  const basicFlangeParams: H4_FlangeParameters = {
    Pr: 80, // 80 kips tension
    Pc: 120, // 120 kips net section rupture capacity
    Mrx: 1200, // 1200 kip-in major-axis moment
    Mcx: 2000, // 2000 kip-in available flexural capacity
  };

  describe("Basic H4-1 Calculations", () => {
    it("should calculate H4-1 flange rupture interaction correctly", () => {
      const result = calculateH4_FlangeRupture(basicFlangeParams);

      // Manual calculation: Pr/Pc + Mrx/Mcx = 80/120 + 1200/2000 = 0.667 + 0.6 = 1.267
      const expectedRatio = 80 / 120 + 1200 / 2000;
      expect(result.governingRatio).toBeCloseTo(expectedRatio, 4);
      expect(result.satisfiesInteraction).toBe(false); // > 1.0
      expect(result.governingLimitState).toBe(
        InteractionLimitState.FLANGE_RUPTURE
      );
      expect(result.inPlaneRatio).toBeCloseTo(expectedRatio, 4);
      expect(result.outOfPlaneRatio).toBeUndefined();
    });

    it("should pass when H4-1 ratio ≤ 1.0", () => {
      const passingParams: H4_FlangeParameters = {
        Pr: 60, // Reduced axial load
        Pc: 120,
        Mrx: 800, // Reduced moment
        Mcx: 2000,
      };

      const result = calculateH4_FlangeRupture(passingParams);

      // Manual: 60/120 + 800/2000 = 0.5 + 0.4 = 0.9
      expect(result.governingRatio).toBeCloseTo(0.9, 4);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });

    it("should handle negative moments correctly", () => {
      const negativeMomentParams: H4_FlangeParameters = {
        Pr: 80,
        Pc: 120,
        Mrx: -1200, // Negative moment (compression in top flange)
        Mcx: 2000,
      };

      const result = calculateH4_FlangeRupture(negativeMomentParams);

      // Manual: 80/120 + (-1200)/2000 = 0.667 - 0.6 = 0.067
      expect(result.governingRatio).toBeCloseTo(0.067, 3);
      expect(result.satisfiesInteraction).toBe(true);
    });

    it("should calculate interaction ratio directly", () => {
      const ratio = calculateH4_InteractionRatio(basicFlangeParams);
      const expectedRatio = 80 / 120 + 1200 / 2000;
      expect(ratio).toBeCloseTo(expectedRatio, 4);
    });
  });

  describe("Input Validation", () => {
    it("should validate required axial strength", () => {
      const invalidPr = { ...basicFlangeParams, Pr: undefined as any };
      expect(() => calculateH4_FlangeRupture(invalidPr)).toThrow(
        "Required axial strength (Pr) must be a valid number"
      );

      const negativeAxial = { ...basicFlangeParams, Pr: -50 };
      expect(() => calculateH4_FlangeRupture(negativeAxial)).toThrow(
        "Required axial strength (Pr) must be positive in tension for H4 analysis"
      );
    });

    it("should validate available axial strength", () => {
      const invalidPc = { ...basicFlangeParams, Pc: 0 };
      expect(() => calculateH4_FlangeRupture(invalidPc)).toThrow(
        "Available axial strength (Pc) must be positive"
      );

      const nanPc = { ...basicFlangeParams, Pc: NaN };
      expect(() => calculateH4_FlangeRupture(nanPc)).toThrow(
        "Available axial strength (Pc) must be a valid number"
      );
    });

    it("should validate flexural parameters", () => {
      const invalidMrx = { ...basicFlangeParams, Mrx: "invalid" as any };
      expect(() => calculateH4_FlangeRupture(invalidMrx)).toThrow(
        "Required flexural strength (Mrx) must be a valid number"
      );

      const invalidMcx = { ...basicFlangeParams, Mcx: -2000 };
      expect(() => calculateH4_FlangeRupture(invalidMcx)).toThrow(
        "Available flexural strength (Mcx) must be positive"
      );
    });

    it("should detect unusually high load ratios", () => {
      const highRatioParams = { ...basicFlangeParams, Pc: 10 }; // Creates Pr/Pc = 8.0
      expect(() => calculateH4_FlangeRupture(highRatioParams)).toThrow(
        "Load to capacity ratios are unusually high (>5.0). Check input values."
      );
    });
  });

  describe("Metadata and Analysis Details", () => {
    it("should provide comprehensive H4 metadata", () => {
      const result = calculateH4_FlangeRupture(basicFlangeParams);

      expect(result.metadata).toBeDefined();
      expect(result.metadata!.usedH4).toBe(true);
      expect(result.metadata!.usedH1_1a).toBe(false);
      expect(result.metadata!.usedH1_3).toBe(false);

      // Check load ratios
      expect(result.metadata!.PrOverPc).toBeCloseTo(80 / 120, 4);
      expect(result.metadata!.momentRatios.MrxOverMcx).toBeCloseTo(
        1200 / 2000,
        4
      );
      expect(result.metadata!.momentRatios.MryOverMcy).toBe(0); // H4 only considers major-axis

      // Check H4-specific results
      expect(result.metadata!.h4Results).toBeDefined();
      expect(result.metadata!.h4Results!.flangeRuptureRatio).toBeCloseTo(
        80 / 120 + 1200 / 2000,
        4
      );
      expect(result.metadata!.h4Results!.boltHoleLocation).toBe(
        "Each flange checked separately"
      );

      // Check analysis details
      expect(result.metadata!.loadingType).toBe("tension");
      expect(result.metadata!.axialLoadThreshold).toBe(0);
      expect(result.metadata!.equationsUsed).toContain("H4-1");
    });

    it("should identify governing stress components correctly", () => {
      // Test with axial governing
      const axialGoverning: H4_FlangeParameters = {
        Pr: 100, // High axial
        Pc: 120,
        Mrx: 400, // Low moment
        Mcx: 2000,
      };

      const result1 = calculateH4_FlangeRupture(axialGoverning);
      expect(result1.metadata!.governingComponent).toBe(
        "Axial tension (Pr/Pc)"
      );

      // Test with flexural governing
      const flexuralGoverning: H4_FlangeParameters = {
        Pr: 40, // Low axial
        Pc: 120,
        Mrx: 1600, // High moment
        Mcx: 2000,
      };

      const result2 = calculateH4_FlangeRupture(flexuralGoverning);
      expect(result2.metadata!.governingComponent).toBe(
        "Major-axis flexure (Mrx/Mcx)"
      );

      // Test with balanced loads
      const balancedLoads: H4_FlangeParameters = {
        Pr: 60, // Equal ratios
        Pc: 120,
        Mrx: 1000, // Equal ratios (both = 0.5)
        Mcx: 2000,
      };

      const result3 = calculateH4_FlangeRupture(balancedLoads);
      expect(result3.metadata!.governingComponent).toBe(
        "Combined axial and flexural effects"
      );
    });
  });

  describe("Real-world Examples", () => {
    it("should analyze W-section flange with bolt holes", () => {
      // W24×104 flange at bolt hole location
      const wSectionFlange: H4_FlangeParameters = {
        Pr: 180, // 180 kips tension
        Pc: 240, // Net section rupture capacity
        Mrx: 4200, // 4200 kip-in moment
        Mcx: 6800, // Available flexural capacity
      };

      const result = calculateH4_FlangeRupture(wSectionFlange);

      // Manual: 180/240 + 4200/6800 = 0.75 + 0.618 = 1.368
      expect(result.governingRatio).toBeCloseTo(1.368, 2);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.metadata!.governingComponent).toBe("Axial tension (Pr/Pc)");
    });

    it("should analyze built-up girder flange", () => {
      // Built-up girder with cover plates and bolt holes
      const builtUpFlange: H4_FlangeParameters = {
        Pr: 320, // High tension load
        Pc: 450, // Net section capacity
        Mrx: 3600, // Moderate moment
        Mcx: 8500, // High flexural capacity
      };

      const result = calculateH4_FlangeRupture(builtUpFlange);

      // Manual: 320/450 + 3600/8500 = 0.711 + 0.424 = 1.135
      expect(result.governingRatio).toBeCloseTo(1.135, 2);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.metadata!.governingComponent).toBe("Axial tension (Pr/Pc)");
    });

    it("should analyze lightly loaded connection", () => {
      // Connection with adequate capacity
      const lightlyLoaded: H4_FlangeParameters = {
        Pr: 25, // Light tension
        Pc: 80, // Adequate capacity
        Mrx: 300, // Light moment
        Mcx: 1200, // Adequate capacity
      };

      const result = calculateH4_FlangeRupture(lightlyLoaded);

      // Manual: 25/80 + 300/1200 = 0.3125 + 0.25 = 0.5625
      expect(result.governingRatio).toBeCloseTo(0.5625, 3);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });
  });

  describe("Edge Cases", () => {
    it("should handle zero applied loads", () => {
      const zeroLoads: H4_FlangeParameters = {
        Pr: 0, // No axial load
        Pc: 120,
        Mrx: 0, // No moment
        Mcx: 2000,
      };

      const result = calculateH4_FlangeRupture(zeroLoads);

      expect(result.governingRatio).toBe(0);
      expect(result.satisfiesInteraction).toBe(true);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });

    it("should handle unity ratio exactly", () => {
      const unityParams: H4_FlangeParameters = {
        Pr: 60, // 60/120 = 0.5
        Pc: 120,
        Mrx: 1000, // 1000/2000 = 0.5
        Mcx: 2000,
      };
      // Total: 0.5 + 0.5 = 1.0

      const result = calculateH4_FlangeRupture(unityParams);

      expect(result.governingRatio).toBeCloseTo(1.0, 4);
      expect(result.satisfiesInteraction).toBe(true); // Exactly at limit
      expect(result.governingLimitState).toBe(
        InteractionLimitState.SATISFACTORY
      );
    });

    it("should handle very high loads", () => {
      const veryHighLoads: H4_FlangeParameters = {
        Pr: 200, // High loads
        Pc: 120,
        Mrx: 3000,
        Mcx: 2000,
      };

      const result = calculateH4_FlangeRupture(veryHighLoads);

      expect(result.governingRatio).toBeGreaterThan(2.0);
      expect(result.satisfiesInteraction).toBe(false);
      expect(result.governingLimitState).toBe(
        InteractionLimitState.FLANGE_RUPTURE
      );
    });
  });

  describe("Applicability Assessment", () => {
    it("should determine H4 applicability correctly", () => {
      expect(isH4Applicable(true, true, true)).toBe(true); // All conditions met
      expect(isH4Applicable(true, true, false)).toBe(true); // Bolt holes + tension
      expect(isH4Applicable(false, true, true)).toBe(false); // No bolt holes
      expect(isH4Applicable(true, false, true)).toBe(false); // No tension
      expect(isH4Applicable(false, false, false)).toBe(false); // No conditions met
    });
  });

  describe("Constants Integration", () => {
    it("should use proper H4 constants", () => {
      const unityLimit = INTERACTION_CONSTANTS.H4_UNITY_LIMIT;
      expect(unityLimit).toBe(1.0);

      const result = calculateH4_FlangeRupture(basicFlangeParams);
      expect(result.satisfiesInteraction).toBe(
        result.governingRatio <= unityLimit
      );
    });
  });

  describe("Analysis Guidance", () => {
    it("should provide H4 analysis guidance", () => {
      const guidance = getH4AnalysisGuidance();

      expect(guidance.considerations).toContain(
        "H4 applies at locations of bolt holes in flanges subjected to tension"
      );
      expect(guidance.considerations).toContain(
        "Each flange subjected to tension shall be checked separately"
      );

      expect(guidance.recommendations).toContain(
        "Calculate net section properties considering bolt hole area reduction"
      );
      expect(guidance.recommendations).toContain(
        "Use appropriate tensile rupture strength (φPn or Pn/Ωt) per Section D2(b)"
      );

      expect(guidance.limitations).toContain(
        "Only applies to bolt hole locations in flanges"
      );
      expect(guidance.limitations).toContain(
        "Limited to tensile rupture limit state"
      );
    });
  });

  describe("Options and Metadata Control", () => {
    it("should support metadata inclusion control", () => {
      const resultWithMetadata = calculateH4_FlangeRupture(basicFlangeParams, {
        includeMetadata: true,
      });
      expect(resultWithMetadata.metadata).toBeDefined();

      const resultWithoutMetadata = calculateH4_FlangeRupture(
        basicFlangeParams,
        {
          includeMetadata: false,
        }
      );
      expect(resultWithoutMetadata.metadata).toBeUndefined();
    });

    it("should include metadata by default", () => {
      const result = calculateH4_FlangeRupture(basicFlangeParams);
      expect(result.metadata).toBeDefined();
    });
  });
});
