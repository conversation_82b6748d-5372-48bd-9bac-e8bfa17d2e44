import { describe, it, expect } from "@jest/globals";
import { DesignMethod } from "../constants";
import {
  BracingType,
  BracedMemberType,
  ColumnBracingInput,
  calculateColumnPanelShearStrength,
  calculateColumnPanelStiffness,
  calculateColumnPointStrength,
  calculateColumnPointStiffness,
  designColumnBracing,
} from "./index";

describe("Column Bracing - AISC Section 6.2", () => {
  
  describe("Panel Bracing", () => {
    
    describe("calculateColumnPanelShearStrength", () => {
      it("should calculate correct shear strength per AISC Eq. A-6-1", () => {
        // Test case: Pr = 200 kips
        // Vbr = 0.005 * Pr = 0.005 * 200 = 1.0 kip
        const Pr = 200;
        const expectedVbr = 1.0;
        
        const result = calculateColumnPanelShearStrength(Pr);
        expect(result).toBeCloseTo(expectedVbr, 3);
      });

      it("should handle different load magnitudes", () => {
        expect(calculateColumnPanelShearStrength(100)).toBeCloseTo(0.5, 3);
        expect(calculateColumnPanelShearStrength(500)).toBeCloseTo(2.5, 3);
        expect(calculateColumnPanelShearStrength(1000)).toBeCloseTo(5.0, 3);
      });

      it("should throw error for zero or negative Pr", () => {
        expect(() => calculateColumnPanelShearStrength(0)).toThrow("Required axial strength Pr must be positive");
        expect(() => calculateColumnPanelShearStrength(-100)).toThrow("Required axial strength Pr must be positive");
      });
    });

    describe("calculateColumnPanelStiffness", () => {
      it("should calculate correct stiffness for LRFD per AISC Eq. A-6-2a", () => {
        // Test case: Pr = 200 kips, Lbr = 120 in, LRFD
        // βbr = (1/φ) * (2Pr/Lbr) = (1/0.75) * (2*200/120) = 4.44 kips/in
        const Pr = 200;
        const Lbr = 120;
        const phi = 0.75;
        const expectedBeta = (1 / phi) * (2 * Pr) / Lbr;
        
        const result = calculateColumnPanelStiffness(Pr, Lbr, DesignMethod.LRFD);
        expect(result).toBeCloseTo(expectedBeta, 2);
      });

      it("should calculate correct stiffness for ASD per AISC Eq. A-6-2b", () => {
        // Test case: Pr = 200 kips, Lbr = 120 in, ASD
        // βbr = Ω * (2Pr/Lbr) = 2.0 * (2*200/120) = 6.67 kips/in
        const Pr = 200;
        const Lbr = 120;
        const omega = 2.0;
        const expectedBeta = omega * (2 * Pr) / Lbr;
        
        const result = calculateColumnPanelStiffness(Pr, Lbr, DesignMethod.ASD);
        expect(result).toBeCloseTo(expectedBeta, 2);
      });

      it("should throw error for invalid inputs", () => {
        expect(() => calculateColumnPanelStiffness(0, 120, DesignMethod.LRFD)).toThrow("Required axial strength Pr must be positive");
        expect(() => calculateColumnPanelStiffness(200, 0, DesignMethod.LRFD)).toThrow("Unbraced length Lbr must be positive");
        expect(() => calculateColumnPanelStiffness(200, 120, "INVALID" as any)).toThrow("Invalid design method");
      });
    });
  });

  describe("Point Bracing", () => {
    
    describe("calculateColumnPointStrength", () => {
      it("should calculate correct strength per AISC Eq. A-6-3", () => {
        // Test case: Pr = 200 kips
        // Pbr = 0.01 * Pr = 0.01 * 200 = 2.0 kips
        const Pr = 200;
        const expectedPbr = 2.0;
        
        const result = calculateColumnPointStrength(Pr);
        expect(result).toBeCloseTo(expectedPbr, 3);
      });

      it("should handle different load magnitudes", () => {
        expect(calculateColumnPointStrength(100)).toBeCloseTo(1.0, 3);
        expect(calculateColumnPointStrength(500)).toBeCloseTo(5.0, 3);
        expect(calculateColumnPointStrength(1000)).toBeCloseTo(10.0, 3);
      });

      it("should throw error for zero or negative Pr", () => {
        expect(() => calculateColumnPointStrength(0)).toThrow("Required axial strength Pr must be positive");
        expect(() => calculateColumnPointStrength(-100)).toThrow("Required axial strength Pr must be positive");
      });
    });

    describe("calculateColumnPointStiffness", () => {
      it("should calculate correct stiffness for LRFD per AISC Eq. A-6-4a", () => {
        // Test case: Pr = 200 kips, Lbr = 120 in, LRFD
        // βbr = (1/φ) * (8Pr/Lbr) = (1/0.75) * (8*200/120) = 17.78 kips/in
        const Pr = 200;
        const Lbr = 120;
        const phi = 0.75;
        const expectedBeta = (1 / phi) * (8 * Pr) / Lbr;
        
        const result = calculateColumnPointStiffness(Pr, Lbr, DesignMethod.LRFD);
        expect(result).toBeCloseTo(expectedBeta, 2);
      });

      it("should calculate correct stiffness for ASD per AISC Eq. A-6-4b", () => {
        // Test case: Pr = 200 kips, Lbr = 120 in, ASD
        // βbr = Ω * (8Pr/Lbr) = 2.0 * (8*200/120) = 26.67 kips/in
        const Pr = 200;
        const Lbr = 120;
        const omega = 2.0;
        const expectedBeta = omega * (8 * Pr) / Lbr;
        
        const result = calculateColumnPointStiffness(Pr, Lbr, DesignMethod.ASD);
        expect(result).toBeCloseTo(expectedBeta, 2);
      });

      it("should throw error for invalid inputs", () => {
        expect(() => calculateColumnPointStiffness(0, 120, DesignMethod.LRFD)).toThrow("Required axial strength Pr must be positive");
        expect(() => calculateColumnPointStiffness(200, 0, DesignMethod.LRFD)).toThrow("Unbraced length Lbr must be positive");
        expect(() => calculateColumnPointStiffness(200, 120, "INVALID" as any)).toThrow("Invalid design method");
      });
    });
  });

  describe("designColumnBracing", () => {
    
    it("should design panel bracing correctly for LRFD", () => {
      const input: ColumnBracingInput = {
        Pr: 200,
        Lbr: 120,
        designMethod: DesignMethod.LRFD,
        bracingType: BracingType.PANEL
      };

      const result = designColumnBracing(input);
      
      expect(result.memberType).toBe(BracedMemberType.COLUMN);
      expect(result.bracingType).toBe(BracingType.PANEL);
      expect(result.source).toBe("AISC 360-22, Section 6.2 - Column Bracing");
      
      // Check strength results
      expect(result.strength.Vbr).toBeCloseTo(1.0, 3);
      expect(result.strength.governingEquation).toBe("AISC Eq. A-6-1");
      expect(result.strength.description).toBe("Required shear strength for column panel bracing");
      
      // Check stiffness results
      expect(result.stiffness.βbr).toBeCloseTo((1/0.75) * (2*200)/120, 2);
      expect(result.stiffness.units).toBe("kips/in");
      expect(result.stiffness.governingEquation).toBe("AISC Eq. A-6-2a");
      expect(result.stiffness.description).toBe("Required shear stiffness for column panel bracing");
    });

    it("should design point bracing correctly for ASD", () => {
      const input: ColumnBracingInput = {
        Pr: 200,
        Lbr: 120,
        designMethod: DesignMethod.ASD,
        bracingType: BracingType.POINT
      };

      const result = designColumnBracing(input);
      
      expect(result.memberType).toBe(BracedMemberType.COLUMN);
      expect(result.bracingType).toBe(BracingType.POINT);
      expect(result.source).toBe("AISC 360-22, Section 6.2 - Column Bracing");
      
      // Check strength results
      expect(result.strength.Pbr).toBeCloseTo(2.0, 3);
      expect(result.strength.governingEquation).toBe("AISC Eq. A-6-3");
      expect(result.strength.description).toBe("Required strength for column point bracing");
      
      // Check stiffness results
      expect(result.stiffness.βbr).toBeCloseTo(2.0 * (8*200)/120, 2);
      expect(result.stiffness.units).toBe("kips/in");
      expect(result.stiffness.governingEquation).toBe("AISC Eq. A-6-4b");
      expect(result.stiffness.description).toBe("Required stiffness for column point bracing");
    });

    it("should throw error for unsupported bracing type", () => {
      const input: ColumnBracingInput = {
        Pr: 200,
        Lbr: 120,
        designMethod: DesignMethod.LRFD,
        bracingType: BracingType.CONTINUOUS
      };

      expect(() => designColumnBracing(input)).toThrow("Unsupported bracing type for columns");
    });

    it("should throw error for invalid inputs", () => {
      const baseInput: ColumnBracingInput = {
        Pr: 200,
        Lbr: 120,
        designMethod: DesignMethod.LRFD,
        bracingType: BracingType.PANEL
      };

      expect(() => designColumnBracing({ ...baseInput, Pr: 0 })).toThrow("Required axial strength Pr must be positive");
      expect(() => designColumnBracing({ ...baseInput, Lbr: 0 })).toThrow("Unbraced length Lbr must be positive");
    });
  });

  describe("Real-world Examples", () => {
    
    it("should handle typical column loads", () => {
      // Typical building column with 150 kip axial load
      const input: ColumnBracingInput = {
        Pr: 150,
        Lbr: 144, // 12 ft unbraced length
        designMethod: DesignMethod.LRFD,
        bracingType: BracingType.PANEL
      };

      const result = designColumnBracing(input);
      
      expect(result.strength.Vbr).toBeCloseTo(0.75, 3); // 0.005 * 150
      expect(result.stiffness.βbr).toBeCloseTo(2.78, 2); // (1/0.75) * (2*150)/144
    });

    it("should handle heavy column loads", () => {
      // Heavy industrial column with 500 kip axial load
      const input: ColumnBracingInput = {
        Pr: 500,
        Lbr: 168, // 14 ft unbraced length  
        designMethod: DesignMethod.ASD,
        bracingType: BracingType.POINT
      };

      const result = designColumnBracing(input);
      
      expect(result.strength.Pbr).toBeCloseTo(5.0, 3); // 0.01 * 500
      expect(result.stiffness.βbr).toBeCloseTo(47.62, 2); // 2.0 * (8*500)/168
    });
  });
}); 