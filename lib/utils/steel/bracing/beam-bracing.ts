/**
 * @file Beam Bracing Calculations
 * @source AISC 360-22, Appendix 6.3 - Beam Bracing
 *
 * This module implements bracing requirements for beams according to AISC 360-22.
 * It provides calculations for lateral bracing, torsional bracing, and continuous bracing.
 */

import { BRACING_CONSTANTS, DesignMethod, E } from "../constants";
import {
  BeamBracingInput,
  BracingDesignResult,
  BracingStrengthResult,
  BracingStiffnessResult,
  BracingType,
  BracedMemberType,
  BracingDirection,
  TorsionalBracingParameters,
  WebDistortionalParameters,
} from "./types";

/**
 * Calculate required shear strength for beam panel bracing
 * @param Mr Required flexural strength of beam (kip-in)
 * @param ho Distance between flange centroids (in)
 * @param Cd Coefficient (1.0 standard, 2.0 for inflection point in double curvature)
 * @returns Required shear strength (kips)
 * @source AISC 360-22, Equation A-6-5
 */
export function calculateBeamPanelShearStrength(
  Mr: number,
  ho: number,
  Cd: number = 1.0
): number {
  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (Cd <= 0) {
    throw new Error("Coefficient Cd must be positive");
  }

  return (BRACING_CONSTANTS.BEAM_PANEL.SHEAR_STRENGTH_COEFF * (Mr * Cd)) / ho;
}

/**
 * Calculate required stiffness for beam panel bracing
 * @param Mr Required flexural strength of beam (kip-in)
 * @param ho Distance between flange centroids (in)
 * @param Lbr Unbraced length within panel (in)
 * @param designMethod Design method (LRFD or ASD)
 * @param Cd Coefficient (1.0 standard, 2.0 for inflection point in double curvature)
 * @returns Required stiffness (kips/in)
 * @source AISC 360-22, Equations A-6-6a and A-6-6b
 */
export function calculateBeamPanelStiffness(
  Mr: number,
  ho: number,
  Lbr: number,
  designMethod: DesignMethod,
  Cd: number = 1.0
): number {
  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }
  if (Cd <= 0) {
    throw new Error("Coefficient Cd must be positive");
  }

  const { PHI_LRFD, OMEGA_ASD } = BRACING_CONSTANTS.FACTORS;
  const { LRFD_MULTIPLIER, ASD_MULTIPLIER } =
    BRACING_CONSTANTS.STIFFNESS.PANEL_STIFFNESS;

  const baseTerm = (LRFD_MULTIPLIER * Mr * Cd) / (Lbr * ho);

  if (designMethod === DesignMethod.LRFD) {
    return (1 / PHI_LRFD) * baseTerm;
  } else if (designMethod === DesignMethod.ASD) {
    return OMEGA_ASD * baseTerm;
  } else {
    throw new Error(`Invalid design method: ${designMethod}`);
  }
}

/**
 * Calculate required strength for beam point bracing
 * @param Mr Required flexural strength of beam (kip-in)
 * @param ho Distance between flange centroids (in)
 * @param Cd Coefficient (1.0 standard, 2.0 for inflection point in double curvature)
 * @returns Required strength (kips)
 * @source AISC 360-22, Equation A-6-7
 */
export function calculateBeamPointStrength(
  Mr: number,
  ho: number,
  Cd: number = 1.0
): number {
  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (Cd <= 0) {
    throw new Error("Coefficient Cd must be positive");
  }

  return (BRACING_CONSTANTS.BEAM_POINT.STRENGTH_COEFF * (Mr * Cd)) / ho;
}

/**
 * Calculate required stiffness for beam point bracing
 * @param Mr Required flexural strength of beam (kip-in)
 * @param ho Distance between flange centroids (in)
 * @param Lbr Unbraced length adjacent to point brace (in)
 * @param designMethod Design method (LRFD or ASD)
 * @param Cd Coefficient (1.0 standard, 2.0 for inflection point in double curvature)
 * @returns Required stiffness (kips/in)
 * @source AISC 360-22, Equations A-6-8a and A-6-8b
 */
export function calculateBeamPointStiffness(
  Mr: number,
  ho: number,
  Lbr: number,
  designMethod: DesignMethod,
  Cd: number = 1.0
): number {
  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }
  if (Cd <= 0) {
    throw new Error("Coefficient Cd must be positive");
  }

  const { PHI_LRFD, OMEGA_ASD } = BRACING_CONSTANTS.FACTORS;
  const { LRFD_MULTIPLIER, ASD_MULTIPLIER } =
    BRACING_CONSTANTS.STIFFNESS.POINT_STIFFNESS;

  const baseTerm = (10 * Mr * Cd) / (Lbr * ho);

  if (designMethod === DesignMethod.LRFD) {
    return (1 / PHI_LRFD) * baseTerm;
  } else if (designMethod === DesignMethod.ASD) {
    return OMEGA_ASD * baseTerm;
  } else {
    throw new Error(`Invalid design method: ${designMethod}`);
  }
}

/**
 * Calculate required flexural strength for torsional bracing
 * @param Mr Required flexural strength of beam (kip-in)
 * @param Cb Lateral-torsional buckling modification factor
 * @param Lbr Unbraced length (in)
 * @param ho Distance between flange centroids (in)
 * @param L Length of span (in)
 * @param nEIyeff Product of number of braced points and effective moment of inertia
 * @returns Required flexural strength (kip-in)
 * @source AISC 360-22, Equation A-6-9
 */
export function calculateTorsionalBracingStrength(
  Mr: number,
  Cb: number,
  Lbr: number,
  ho: number,
  L: number,
  nEIyeff: number
): number {
  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (Cb <= 0) {
    throw new Error("Cb factor must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (L <= 0) {
    throw new Error("Span length L must be positive");
  }
  if (nEIyeff <= 0) {
    throw new Error("nEIyeff must be positive");
  }

  const { FLEXURAL_STRENGTH_COEFF, LENGTH_RATIO_COEFF, MIN_FLEXURAL_RATIO } =
    BRACING_CONSTANTS.BEAM_TORSIONAL;

  const calculatedStrength =
    (FLEXURAL_STRENGTH_COEFF *
      L *
      (Mr / Cb) ** 2 *
      (Lbr / (LENGTH_RATIO_COEFF * ho))) /
    nEIyeff;
  const minimumStrength = MIN_FLEXURAL_RATIO * Mr;

  return Math.max(calculatedStrength, minimumStrength);
}

/**
 * Calculate required stiffness for torsional bracing
 * @param params Torsional bracing parameters
 * @returns Required stiffness (kip-in/rad)
 * @source AISC 360-22, Equations A-6-11a and A-6-11b
 */
export function calculateTorsionalBracingStiffness(
  params: TorsionalBracingParameters
): number {
  const { Mr, Cb, E, Iyeff, n, L, designMethod } = params;

  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (Cb <= 0) {
    throw new Error("Cb factor must be positive");
  }
  if (E <= 0) {
    throw new Error("Modulus of elasticity E must be positive");
  }
  if (Iyeff <= 0) {
    throw new Error("Effective moment of inertia Iyeff must be positive");
  }
  if (n <= 0) {
    throw new Error("Number of braced points n must be positive");
  }
  if (L <= 0) {
    throw new Error("Span length L must be positive");
  }

  const { PHI_TORSIONAL_LRFD, OMEGA_TORSIONAL_ASD } = BRACING_CONSTANTS.FACTORS;
  const { LRFD_MULTIPLIER, ASD_MULTIPLIER } =
    BRACING_CONSTANTS.STIFFNESS.TORSIONAL_STIFFNESS;

  const baseTerm = (LRFD_MULTIPLIER * L * (Mr / Cb) ** 2) / (n * E * Iyeff);

  if (designMethod === DesignMethod.LRFD) {
    return (1 / PHI_TORSIONAL_LRFD) * baseTerm;
  } else if (designMethod === DesignMethod.ASD) {
    return OMEGA_TORSIONAL_ASD * baseTerm;
  } else {
    throw new Error(`Invalid design method: ${designMethod}`);
  }
}

/**
 * Calculate web distortional stiffness
 * @param params Web distortional parameters
 * @returns Web distortional stiffness (kip-in/rad)
 * @source AISC 360-22, Equation A-6-12
 */
export function calculateWebDistortionalStiffness(
  params: WebDistortionalParameters
): number {
  const { E, ho, tw, bs, tst } = params;

  if (E <= 0) {
    throw new Error("Modulus of elasticity E must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (tw <= 0) {
    throw new Error("Web thickness tw must be positive");
  }
  if (bs <= 0) {
    throw new Error("Stiffener width bs must be positive");
  }
  if (tst <= 0) {
    throw new Error("Stiffener thickness tst must be positive");
  }

  const { OVERALL_COEFF, FLEXURAL_COEFF, TORSIONAL_COEFF, DENOMINATOR } =
    BRACING_CONSTANTS.WEB_DISTORTIONAL;

  const flexuralTerm = FLEXURAL_COEFF * ho * tw ** 3;
  const torsionalTerm = TORSIONAL_COEFF * bs ** 3;

  return (OVERALL_COEFF * E * (flexuralTerm + torsionalTerm)) / DENOMINATOR;
}

/**
 * Design beam bracing system according to AISC 360-22 Section 6.3
 * @param input Beam bracing input parameters
 * @returns Complete bracing design result
 * @source AISC 360-22, Section 6.3 - Beam Bracing
 */
export function designBeamBracing(
  input: BeamBracingInput
): BracingDesignResult {
  const {
    Mr,
    ho,
    Lbr,
    designMethod,
    bracingType,
    bracingDirection,
    Cd = 1.0,
    E: modulusE = E,
    Iyeff,
    bs,
    tw,
    tst,
    L,
    n,
  } = input;

  // Validate inputs
  if (Mr <= 0) {
    throw new Error("Required flexural strength Mr must be positive");
  }
  if (ho <= 0) {
    throw new Error("Distance between flange centroids ho must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }

  let strength: BracingStrengthResult;
  let stiffness: BracingStiffnessResult;

  if (bracingDirection === BracingDirection.LATERAL) {
    if (bracingType === BracingType.PANEL) {
      // Lateral panel bracing
      const Vbr = calculateBeamPanelShearStrength(Mr, ho, Cd);
      const βbr = calculateBeamPanelStiffness(Mr, ho, Lbr, designMethod, Cd);

      strength = {
        Vbr,
        governingEquation: "AISC Eq. A-6-5",
        description: "Required shear strength for beam panel bracing",
      };

      stiffness = {
        βbr,
        units: "kips/in",
        governingEquation:
          designMethod === DesignMethod.LRFD
            ? "AISC Eq. A-6-6a"
            : "AISC Eq. A-6-6b",
        description: "Required stiffness for beam panel bracing",
      };
    } else if (bracingType === BracingType.POINT) {
      // Lateral point bracing
      const Pbr = calculateBeamPointStrength(Mr, ho, Cd);
      const βbr = calculateBeamPointStiffness(Mr, ho, Lbr, designMethod, Cd);

      strength = {
        Pbr,
        governingEquation: "AISC Eq. A-6-7",
        description: "Required strength for beam point bracing",
      };

      stiffness = {
        βbr,
        units: "kips/in",
        governingEquation:
          designMethod === DesignMethod.LRFD
            ? "AISC Eq. A-6-8a"
            : "AISC Eq. A-6-8b",
        description: "Required stiffness for beam point bracing",
      };
    } else {
      throw new Error(
        `Unsupported bracing type for lateral beam bracing: ${bracingType}`
      );
    }
  } else if (bracingDirection === BracingDirection.TORSIONAL) {
    // Torsional bracing
    if (!Iyeff || !L || !n) {
      throw new Error(
        "Iyeff, L, and n are required for torsional bracing calculations"
      );
    }

    const nEIyeff = n * modulusE * Iyeff;
    const Mbr = calculateTorsionalBracingStrength(Mr, Cd, Lbr, ho, L, nEIyeff);
    const βbr = calculateTorsionalBracingStiffness({
      Mr,
      Cb: Cd,
      E: modulusE,
      Iyeff,
      n,
      L,
      designMethod,
    });

    strength = {
      Mbr,
      governingEquation: "AISC Eq. A-6-9",
      description: "Required flexural strength for torsional bracing",
    };

    stiffness = {
      βbr,
      units: "kip-in/rad",
      governingEquation:
        designMethod === DesignMethod.LRFD
          ? "AISC Eq. A-6-11a"
          : "AISC Eq. A-6-11b",
      description: "Required stiffness for torsional bracing",
    };
  } else {
    throw new Error(`Unsupported bracing direction: ${bracingDirection}`);
  }

  return {
    strength,
    stiffness,
    bracingType,
    memberType: BracedMemberType.BEAM,
    source: "AISC 360-22, Section 6.3 - Beam Bracing",
  };
}
