/**
 * @file Example usage of the bracing module
 * This file demonstrates how to use the member stability bracing calculations
 * according to AISC 360-22 Appendix 6.
 */

import {
  designColumnBracing,
  designBeamBracing,
  designBeamColumnBracing,
  BracingType,
  BracingDirection,
} from "./index.js";
import { DesignMethod } from "../constants.js";

// Example 1: Column Panel Bracing (LRFD)
console.log("=== Example 1: Column Panel Bracing (LRFD) ===");
const columnResult = designColumnBracing({
  Pr: 200, // Required axial strength (kips)
  Lbr: 144, // Unbraced length (12 ft = 144 in)
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.PANEL,
});

console.log(
  `Required shear strength: ${columnResult.strength.Vbr?.toFixed(2)} kips`
);
console.log(
  `Required stiffness: ${columnResult.stiffness.βbr.toFixed(2)} kips/in`
);
console.log(`Governing equation: ${columnResult.strength.governingEquation}`);
console.log("");

// Example 2: Beam Lateral Point Bracing (ASD)
console.log("=== Example 2: Beam Lateral Point Bracing (ASD) ===");
const beamResult = designBeamBracing({
  Mr: 3000, // Required flexural strength (kip-in)
  ho: 12, // Distance between flange centroids (in)
  Lbr: 120, // Unbraced length (10 ft = 120 in)
  designMethod: DesignMethod.ASD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.LATERAL,
});

console.log(`Required strength: ${beamResult.strength.Pbr?.toFixed(2)} kips`);
console.log(
  `Required stiffness: ${beamResult.stiffness.βbr.toFixed(2)} kips/in`
);
console.log(`Governing equation: ${beamResult.strength.governingEquation}`);
console.log("");

// Example 3: Beam Torsional Bracing (LRFD)
console.log("=== Example 3: Beam Torsional Bracing (LRFD) ===");
const torsionalResult = designBeamBracing({
  Mr: 4000, // Required flexural strength (kip-in)
  ho: 18, // Distance between flange centroids (in)
  Lbr: 120, // Unbraced length (in)
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.TORSIONAL,
  Iyeff: 50, // Effective out-of-plane moment of inertia (in⁴)
  L: 480, // Span length (40 ft = 480 in)
  n: 2, // Number of braced points
  Cd: 1.0, // Standard coefficient
});

console.log(
  `Required flexural strength: ${torsionalResult.strength.Mbr?.toFixed(
    2
  )} kip-in`
);
console.log(
  `Required stiffness: ${torsionalResult.stiffness.βbr.toFixed(2)} kip-in/rad`
);
console.log(
  `Governing equation: ${torsionalResult.strength.governingEquation}`
);
console.log("");

// Example 4: Beam-Column Combined Bracing (LRFD)
console.log("=== Example 4: Beam-Column Combined Bracing (LRFD) ===");
const beamColumnResult = designBeamColumnBracing({
  columnBracing: {
    Pr: 150,
    Lbr: 144,
    designMethod: DesignMethod.LRFD,
    bracingType: BracingType.PANEL,
  },
  beamBracing: {
    Mr: 2500,
    ho: 14,
    Lbr: 144,
    designMethod: DesignMethod.LRFD,
    bracingType: BracingType.PANEL,
    bracingDirection: BracingDirection.LATERAL,
  },
});

console.log(
  `Combined shear strength: ${beamColumnResult.strength.Vbr?.toFixed(2)} kips`
);
console.log(
  `Combined stiffness: ${beamColumnResult.stiffness.βbr.toFixed(2)} kips/in`
);
console.log(
  `Governing equation: ${beamColumnResult.strength.governingEquation}`
);
console.log(`Member type: ${beamColumnResult.memberType}`);
console.log("");

console.log("=== All examples completed successfully! ===");
