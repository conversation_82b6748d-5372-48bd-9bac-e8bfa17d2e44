# Member Stability Bracing Module

This module implements member stability bracing calculations according to **AISC 360-22, Appendix 6 - Member Stability Bracing**.

## Overview

Member stability bracing is required to develop the full strength of structural steel members by preventing lateral-torsional buckling, web crippling, and other instability phenomena. This module provides comprehensive calculations for:

- **Column Bracing** (Section 6.2)
- **Beam Bracing** (Section 6.3) 
- **Beam-Column Bracing** (Section 6.4)

## Key Features

✅ **Complete AISC 360-22 Implementation**
- All equations from Appendix 6 (A-6-1 through A-6-13)
- Both LRFD and ASD design methods
- Panel, point, and torsional bracing types

✅ **Comprehensive Validation**
- Input parameter validation
- Physics-based constraints
- Detailed error messages

✅ **TypeScript Support**
- Full type safety
- Comprehensive interfaces
- IntelliSense support

✅ **Extensive Testing**
- Unit tests for all functions
- Integration tests for design workflows
- Real-world example validation

## Installation

```typescript
import {
  // Design functions
  designColumnBracing,
  designBeamBracing,
  designBeamColumnBracing,
  
  // Individual calculation functions
  calculateColumnPanelShearStrength,
  calculateBeamPointStiffness,
  calculateTorsionalBracingStrength,
  
  // Types and enums
  BracingType,
  BracingDirection,
  ColumnBracingInput,
  BeamBracingInput
} from "@/steel/bracing";
```

## Quick Start

### Column Bracing

```typescript
import { designColumnBracing, BracingType, DesignMethod } from "@/steel/bracing";

// Design panel bracing for a column
const columnResult = designColumnBracing({
  Pr: 200,              // Required axial strength (kips)
  Lbr: 120,             // Unbraced length (in)
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.PANEL
});

console.log(`Required shear strength: ${columnResult.strength.Vbr} kips`);
console.log(`Required stiffness: ${columnResult.stiffness.βbr} kips/in`);
```

### Beam Bracing

```typescript
import { designBeamBracing, BracingDirection } from "@/steel/bracing";

// Design lateral point bracing for a beam
const beamResult = designBeamBracing({
  Mr: 3000,             // Required flexural strength (kip-in)
  ho: 12,               // Distance between flange centroids (in)
  Lbr: 120,             // Unbraced length (in)
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.LATERAL
});

console.log(`Required strength: ${beamResult.strength.Pbr} kips`);
console.log(`Required stiffness: ${beamResult.stiffness.βbr} kips/in`);
```

### Torsional Bracing

```typescript
// Design torsional bracing for a beam
const torsionalResult = designBeamBracing({
  Mr: 3000,             // Required flexural strength (kip-in)
  ho: 12,               // Distance between flange centroids (in)
  Lbr: 120,             // Unbraced length (in)
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.TORSIONAL,
  Iyeff: 100,           // Effective out-of-plane moment of inertia (in⁴)
  L: 360,               // Span length (in)
  n: 2,                 // Number of braced points
  Cd: 1.0               // Coefficient (2.0 for double curvature inflection point)
});

console.log(`Required flexural strength: ${torsionalResult.strength.Mbr} kip-in`);
console.log(`Required stiffness: ${torsionalResult.stiffness.βbr} kip-in/rad`);
```

### Beam-Column Bracing

```typescript
import { designBeamColumnBracing } from "@/steel/bracing";

// Design combined bracing for a beam-column
const beamColumnResult = designBeamColumnBracing({
  columnBracing: {
    Pr: 200,
    Lbr: 120,
    designMethod: DesignMethod.LRFD,
    bracingType: BracingType.PANEL
  },
  beamBracing: {
    Mr: 3000,
    ho: 12,
    Lbr: 120,
    designMethod: DesignMethod.LRFD,
    bracingType: BracingType.PANEL,
    bracingDirection: BracingDirection.LATERAL
  }
});

// Combined requirements (sum of column + beam)
console.log(`Total shear strength: ${beamColumnResult.strength.Vbr} kips`);
console.log(`Total stiffness: ${beamColumnResult.stiffness.βbr} kips/in`);
```

## API Reference

### Enums

#### BracingType
- `PANEL` - Panel bracing system
- `POINT` - Point bracing system  
- `CONTINUOUS` - Continuous bracing system

#### BracingDirection
- `LATERAL` - Lateral bracing
- `TORSIONAL` - Torsional bracing
- `COMBINED` - Combined lateral and torsional

#### BracedMemberType
- `COLUMN` - Column member
- `BEAM` - Beam member
- `BEAM_COLUMN` - Beam-column member

### Column Bracing Functions

#### `calculateColumnPanelShearStrength(Pr: number): number`
**AISC Equation A-6-1**
```
Vbr = 0.005 × Pr
```
- `Pr`: Required axial strength (kips)
- Returns: Required shear strength (kips)

#### `calculateColumnPanelStiffness(Pr: number, Lbr: number, designMethod: DesignMethod): number`
**AISC Equations A-6-2a/2b**
```
LRFD: βbr = (1/φ) × (2Pr/Lbr)    where φ = 0.75
ASD:  βbr = Ω × (2Pr/Lbr)        where Ω = 2.00
```
- `Pr`: Required axial strength (kips)
- `Lbr`: Unbraced length within panel (in)
- Returns: Required stiffness (kips/in)

#### `calculateColumnPointStrength(Pr: number): number`
**AISC Equation A-6-3**
```
Pbr = 0.01 × Pr
```
- `Pr`: Required axial strength (kips)
- Returns: Required strength (kips)

#### `calculateColumnPointStiffness(Pr: number, Lbr: number, designMethod: DesignMethod): number`
**AISC Equations A-6-4a/4b**
```
LRFD: βbr = (1/φ) × (8Pr/Lbr)    where φ = 0.75
ASD:  βbr = Ω × (8Pr/Lbr)        where Ω = 2.00
```
- `Pr`: Required axial strength (kips)
- `Lbr`: Unbraced length adjacent to point brace (in)
- Returns: Required stiffness (kips/in)

### Beam Bracing Functions

#### `calculateBeamPanelShearStrength(Mr: number, ho: number, Cd: number = 1.0): number`
**AISC Equation A-6-5**
```
Vbr = 0.01 × (Mr × Cd) / ho
```
- `Mr`: Required flexural strength (kip-in)
- `ho`: Distance between flange centroids (in)
- `Cd`: Coefficient (1.0 standard, 2.0 for double curvature inflection point)
- Returns: Required shear strength (kips)

#### `calculateBeamPointStrength(Mr: number, ho: number, Cd: number = 1.0): number`
**AISC Equation A-6-7**
```
Pbr = 0.02 × (Mr × Cd) / ho
```
- `Mr`: Required flexural strength (kip-in)
- `ho`: Distance between flange centroids (in)
- `Cd`: Coefficient (1.0 standard, 2.0 for double curvature inflection point)
- Returns: Required strength (kips)

#### `calculateTorsionalBracingStrength(Mr: number, Cb: number, Lbr: number, ho: number, L: number, nEIyeff: number): number`
**AISC Equation A-6-9**
```
Mbr = (3.6L/nEIyeff) × (Mr/Cb)² × (Lbr/500ho) ≥ 0.02Mr
```
- `Mr`: Required flexural strength (kip-in)
- `Cb`: Lateral-torsional buckling modification factor
- `Lbr`: Unbraced length (in)
- `ho`: Distance between flange centroids (in)
- `L`: Length of span (in)
- `nEIyeff`: Product of number of braced points and effective moment of inertia
- Returns: Required flexural strength (kip-in)

### Stiffness Calculations

#### Panel Bracing Stiffness (A-6-6a/6b, A-6-8a/8b)
```typescript
// Beam panel stiffness
LRFD: βbr = (1/φ) × (4MrCd)/(LbrHo)    where φ = 0.75
ASD:  βbr = Ω × (4MrCd)/(LbrHo)        where Ω = 2.00

// Beam point stiffness  
LRFD: βbr = (1/φ) × (10MrCd)/(LbrHo)   where φ = 0.75
ASD:  βbr = Ω × (10MrCd)/(LbrHo)       where Ω = 2.00
```

#### Torsional Bracing Stiffness (A-6-11a/11b)
```typescript
LRFD: βbr = (1/φ) × (3.6L/nEIyeff) × (Mr/Cb)²    where φ = 0.75
ASD:  βbr = Ω × (3.6L/nEIyeff) × (Mr/Cb)²        where Ω = 3.00
```

#### Web Distortional Stiffness (A-6-12)
```typescript
βsec = (3.3E/12) × (1.5hotwᶟ + twtbsᶟ)
```

## Design Guidelines

### When to Use Different Bracing Types

#### Panel Bracing
- **Use when**: Continuous bracing elements (e.g., metal deck, sheathing)
- **Advantages**: Lower force requirements, distributed load path
- **Considerations**: Must extend full unbraced length

#### Point Bracing  
- **Use when**: Discrete bracing elements (e.g., struts, ties)
- **Advantages**: Flexible placement, easier detailing
- **Considerations**: Higher force requirements, concentrated loads

#### Torsional Bracing
- **Use when**: Lateral bracing alone is insufficient
- **Required for**: Members prone to lateral-torsional buckling
- **Applications**: Long-span beams, cantilevers, unsymmetric loading

### Design Process

1. **Determine bracing requirements** based on member analysis
2. **Select bracing type** based on structural system and constructability
3. **Calculate strength requirements** using appropriate equations
4. **Calculate stiffness requirements** for chosen design method
5. **Design bracing elements** to meet both strength and stiffness
6. **Detail connections** to transfer calculated forces

### Common Applications

#### Building Frames
```typescript
// Typical moment frame column
const columnBracing = designColumnBracing({
  Pr: 150,              // Typical gravity + lateral load
  Lbr: 144,             // Story height (12 ft)
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.PANEL  // Metal deck diaphragm
});
```

#### Long-Span Beams
```typescript
// Long-span beam requiring torsional bracing
const beamBracing = designBeamBracing({
  Mr: 5000,             // High flexural demand
  ho: 18,               // Deep section
  Lbr: 240,             // 20 ft unbraced length
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.TORSIONAL,
  Iyeff: 25,            // Weak-axis moment of inertia
  L: 480,               // 40 ft span
  n: 2                  // Mid-span and quarter-point braces
});
```

## Validation Examples

### Example 1: Column Panel Bracing (LRFD)
```typescript
// Given: W14x68 column, Pr = 200 kips, Lbr = 12 ft
const result = designColumnBracing({
  Pr: 200,
  Lbr: 144,
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.PANEL
});

// Results:
// Vbr = 0.005 × 200 = 1.0 kip
// βbr = (1/0.75) × (2×200)/144 = 3.7 kips/in
```

### Example 2: Beam Point Bracing (ASD)
```typescript
// Given: W21x44 beam, Mr = 3000 kip-in, ho = 20.7 in, Lbr = 10 ft  
const result = designBeamBracing({
  Mr: 3000,
  ho: 20.7,
  Lbr: 120,
  designMethod: DesignMethod.ASD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.LATERAL
});

// Results:
// Pbr = 0.02 × 3000/20.7 = 2.9 kips
// βbr = 2.0 × (10×3000)/(120×20.7) = 24.2 kips/in
```

### Example 3: Torsional Bracing
```typescript
// Given: W24x55 beam with torsional bracing requirements
const result = designBeamBracing({
  Mr: 4000,
  ho: 23.6,
  Lbr: 120,
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.TORSIONAL,
  Iyeff: 29.1,
  L: 360,
  n: 2,
  Cd: 1.0
});

// Results calculated per AISC Eq. A-6-9 and A-6-11a
```

## Error Handling

The module provides comprehensive input validation:

```typescript
// Validates positive values
calculateColumnPanelShearStrength(-100);  // ❌ Throws: "Required axial strength Pr must be positive"

// Validates required parameters
designBeamBracing({
  Mr: 3000,
  ho: 12,
  Lbr: 120,
  designMethod: DesignMethod.LRFD,
  bracingType: BracingType.POINT,
  bracingDirection: BracingDirection.TORSIONAL
  // ❌ Missing Iyeff, L, n - Throws: "Iyeff, L, and n are required for torsional bracing calculations"
});

// Validates consistency
designBeamColumnBracing({
  columnBracing: { /* LRFD, PANEL */ },
  beamBracing: { /* ASD, POINT */ }
  // ❌ Throws: "Column and beam design methods must be the same"
});
```

## References

- **AISC 360-22**: Specification for Structural Steel Buildings, American Institute of Steel Construction, 2022
- **Appendix 6**: Member Stability Bracing, pp. A-6-1 through A-6-13
- **User Notes**: Commentary provides additional guidance on complex bracing conditions

## Contributing

When adding new functionality:

1. Follow AISC equation numbering conventions
2. Include comprehensive parameter validation
3. Add unit tests with hand-calculated examples
4. Update documentation with usage examples
5. Cite specific AISC sections and equations

---

*This module implements the complete AISC 360-22 Appendix 6 specification for member stability bracing. All calculations are validated against the standard and include comprehensive error handling for production use.* 