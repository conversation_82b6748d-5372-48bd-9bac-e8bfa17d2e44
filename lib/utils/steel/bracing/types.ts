/**
 * @file Types and interfaces for member stability bracing calculations
 * @source AISC 360-22, Appendix 6 - Member Stability Bracing
 */

import { DesignMethod } from "../constants";

/**
 * Types of bracing systems according to AISC Appendix 6
 */
export enum BracingType {
  PANEL = "Panel Bracing",
  POINT = "Point Bracing",
  CONTINUOUS = "Continuous Bracing",
}

/**
 * Member types that can be braced
 */
export enum BracedMemberType {
  COLUMN = "Column",
  BEAM = "Beam",
  BEAM_COLUMN = "Beam-Column",
}

/**
 * Bracing direction for lateral-torsional buckling resistance
 */
export enum BracingDirection {
  LATERAL = "Lateral",
  TORSIONAL = "Torsional",
  COMBINED = "Combined",
}

/**
 * Input parameters for column bracing calculations - AISC Section 6.2
 */
export interface ColumnBracingInput {
  Pr: number; // Required axial strength of column (kips)
  Lbr: number; // Unbraced length within panel or adjacent to point brace (in)
  designMethod: DesignMethod;
  bracingType: BracingType;
}

/**
 * Input parameters for beam bracing calculations - AISC Section 6.3
 */
export interface BeamBracingInput {
  Mr: number; // Required flexural strength of beam (kip-in)
  ho: number; // Distance between flange centroids (in)
  Lbr: number; // Unbraced length within panel or adjacent to point brace (in)
  designMethod: DesignMethod;
  bracingType: BracingType;
  bracingDirection: BracingDirection;
  Cd?: number; // Coefficient for bracing requirements (default: 1.0, except 2.0 for double curvature inflection point)
  E?: number; // Modulus of elasticity (ksi) - default: 29000
  Iyeff?: number; // Effective out-of-plane moment of inertia (in⁴) - for torsional bracing
  bs?: number; // Stiffener width (in) - for torsional bracing
  tw?: number; // Web thickness (in) - for torsional bracing
  tst?: number; // Web stiffener thickness (in) - for torsional bracing
  L?: number; // Length of span (in) - for torsional bracing
  n?: number; // Number of braced points within span - for torsional bracing
}

/**
 * Input parameters for beam-column bracing calculations - AISC Section 6.4
 */
export interface BeamColumnBracingInput {
  columnBracing: ColumnBracingInput;
  beamBracing: BeamBracingInput;
}

/**
 * Result interface for bracing strength requirements
 */
export interface BracingStrengthResult {
  Pbr?: number; // Required strength for axial bracing (kips)
  Vbr?: number; // Required shear strength for lateral bracing (kips)
  Mbr?: number; // Required flexural strength for torsional bracing (kip-in)
  governingEquation: string;
  description: string;
}

/**
 * Result interface for bracing stiffness requirements
 */
export interface BracingStiffnessResult {
  βbr: number; // Required stiffness (kips/in for lateral, kip-in/rad for torsional)
  units: string; // Units for the stiffness
  governingEquation: string;
  description: string;
}

/**
 * Complete bracing design result
 */
export interface BracingDesignResult {
  strength: BracingStrengthResult;
  stiffness: BracingStiffnessResult;
  bracingType: BracingType;
  memberType: BracedMemberType;
  source: string;
}

/**
 * Parameters for torsional bracing βT calculation - AISC Equation A-6-11
 */
export interface TorsionalBracingParameters {
  Mr: number; // Required flexural strength (kip-in)
  Cb: number; // Lateral-torsional buckling modification factor
  E: number; // Modulus of elasticity (ksi)
  Iyeff: number; // Effective out-of-plane moment of inertia (in⁴)
  n: number; // Number of braced points within span
  L: number; // Length of span (in)
  designMethod: DesignMethod;
}

/**
 * Parameters for web distortional stiffness calculation - AISC Equation A-6-12
 */
export interface WebDistortionalParameters {
  E: number; // Modulus of elasticity (ksi)
  ho: number; // Distance between flange centroids (in)
  tw: number; // Web thickness (in)
  bs: number; // Stiffener width (in)
  tst: number; // Web stiffener thickness (in)
}
