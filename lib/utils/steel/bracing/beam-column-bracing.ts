/**
 * @file Beam-Column Bracing Calculations
 * @source AISC 360-22, Appendix 6.4 - Beam-Column Bracing
 *
 * This module implements bracing requirements for beam-columns according to AISC 360-22.
 * It combines the requirements from both column bracing and beam bracing.
 */

import { DesignMethod } from "../constants";
import {
  BeamColumnBracingInput,
  BracingDesignResult,
  BracingStrengthResult,
  BracingStiffnessResult,
  BracingType,
  BracedMemberType,
  BracingDirection,
} from "./types";
import { designColumnBracing } from "./column-bracing";
import { designBeamBracing } from "./beam-bracing";

/**
 * Design beam-column bracing system according to AISC 360-22 Section 6.4
 *
 * For bracing of beam-columns, the required strength and stiffness for the axial force
 * shall be determined as specified in Section 6.2, and the required strength and stiffness
 * for flexure shall be determined as specified in Section 6.3. The values so determined
 * shall be combined as follows:
 *
 * (a) When panel bracing is used, the required strength shall be taken as the sum of the
 *     values determined using Equations A-6-1 and A-6-5, and the required stiffness
 *     shall be taken as the sum of the values determined using Equations A-6-2 and A-6-6.
 *
 * (b) When point bracing is used, the required strength shall be taken as the sum of the
 *     values determined using Equations A-6-3 and A-6-7, and the required stiffness
 *     shall be taken as the sum of the values determined using Equations A-6-4 and A-6-8.
 *
 * @param input Beam-column bracing input parameters
 * @returns Complete bracing design result
 * @source AISC 360-22, Section 6.4 - Beam-Column Bracing
 */
export function designBeamColumnBracing(
  input: BeamColumnBracingInput
): BracingDesignResult {
  const { columnBracing, beamBracing } = input;

  // Validate that both bracing types are the same
  if (columnBracing.bracingType !== beamBracing.bracingType) {
    throw new Error(
      "Column and beam bracing types must be the same for beam-column bracing"
    );
  }

  // Validate that design methods are the same
  if (columnBracing.designMethod !== beamBracing.designMethod) {
    throw new Error(
      "Column and beam design methods must be the same for beam-column bracing"
    );
  }

  // Validate that beam bracing is lateral (only lateral bracing is combined for beam-columns)
  if (beamBracing.bracingDirection !== BracingDirection.LATERAL) {
    throw new Error(
      "Only lateral beam bracing is combined with column bracing for beam-columns"
    );
  }

  const bracingType = columnBracing.bracingType;
  const designMethod = columnBracing.designMethod;

  // Calculate individual bracing requirements
  const columnResult = designColumnBracing(columnBracing);
  const beamResult = designBeamBracing(beamBracing);

  let combinedStrength: BracingStrengthResult;
  let combinedStiffness: BracingStiffnessResult;

  if (bracingType === BracingType.PANEL) {
    // Panel bracing - combine shear strengths and stiffnesses
    const columnVbr = columnResult.strength.Vbr || 0;
    const beamVbr = beamResult.strength.Vbr || 0;
    const totalVbr = columnVbr + beamVbr;

    const columnStiffness = columnResult.stiffness.βbr;
    const beamStiffness = beamResult.stiffness.βbr;
    const totalStiffness = columnStiffness + beamStiffness;

    combinedStrength = {
      Vbr: totalVbr,
      governingEquation: "AISC Eq. A-6-1 + A-6-5",
      description: `Combined shear strength: Column (${columnVbr.toFixed(
        2
      )} kips) + Beam (${beamVbr.toFixed(2)} kips)`,
    };

    combinedStiffness = {
      βbr: totalStiffness,
      units: "kips/in",
      governingEquation:
        designMethod === DesignMethod.LRFD
          ? "AISC Eq. A-6-2a + A-6-6a"
          : "AISC Eq. A-6-2b + A-6-6b",
      description: `Combined stiffness: Column (${columnStiffness.toFixed(
        2
      )}) + Beam (${beamStiffness.toFixed(2)}) kips/in`,
    };
  } else if (bracingType === BracingType.POINT) {
    // Point bracing - combine strengths and stiffnesses
    const columnPbr = columnResult.strength.Pbr || 0;
    const beamPbr = beamResult.strength.Pbr || 0;
    const totalPbr = columnPbr + beamPbr;

    const columnStiffness = columnResult.stiffness.βbr;
    const beamStiffness = beamResult.stiffness.βbr;
    const totalStiffness = columnStiffness + beamStiffness;

    combinedStrength = {
      Pbr: totalPbr,
      governingEquation: "AISC Eq. A-6-3 + A-6-7",
      description: `Combined strength: Column (${columnPbr.toFixed(
        2
      )} kips) + Beam (${beamPbr.toFixed(2)} kips)`,
    };

    combinedStiffness = {
      βbr: totalStiffness,
      units: "kips/in",
      governingEquation:
        designMethod === DesignMethod.LRFD
          ? "AISC Eq. A-6-4a + A-6-8a"
          : "AISC Eq. A-6-4b + A-6-8b",
      description: `Combined stiffness: Column (${columnStiffness.toFixed(
        2
      )}) + Beam (${beamStiffness.toFixed(2)}) kips/in`,
    };
  } else {
    throw new Error(
      `Unsupported bracing type for beam-column bracing: ${bracingType}`
    );
  }

  return {
    strength: combinedStrength,
    stiffness: combinedStiffness,
    bracingType,
    memberType: BracedMemberType.BEAM_COLUMN,
    source: "AISC 360-22, Section 6.4 - Beam-Column Bracing",
  };
}

/**
 * Validate beam-column bracing input parameters
 * @param input Beam-column bracing input parameters
 * @throws Error if validation fails
 */
export function validateBeamColumnBracingInput(
  input: BeamColumnBracingInput
): void {
  const { columnBracing, beamBracing } = input;

  // Validate column bracing input
  if (columnBracing.Pr <= 0) {
    throw new Error("Column required axial strength Pr must be positive");
  }
  if (columnBracing.Lbr <= 0) {
    throw new Error("Column unbraced length Lbr must be positive");
  }

  // Validate beam bracing input
  if (beamBracing.Mr <= 0) {
    throw new Error("Beam required flexural strength Mr must be positive");
  }
  if (beamBracing.ho <= 0) {
    throw new Error(
      "Beam distance between flange centroids ho must be positive"
    );
  }
  if (beamBracing.Lbr <= 0) {
    throw new Error("Beam unbraced length Lbr must be positive");
  }

  // Validate consistency
  if (columnBracing.bracingType !== beamBracing.bracingType) {
    throw new Error("Column and beam bracing types must be the same");
  }
  if (columnBracing.designMethod !== beamBracing.designMethod) {
    throw new Error("Column and beam design methods must be the same");
  }
  if (beamBracing.bracingDirection !== BracingDirection.LATERAL) {
    throw new Error(
      "Only lateral beam bracing is supported for beam-column bracing"
    );
  }
}
