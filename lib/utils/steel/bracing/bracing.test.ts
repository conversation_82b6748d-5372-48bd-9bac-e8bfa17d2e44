import { describe, it, expect } from "@jest/globals";
import { DesignMethod } from "../constants";
import {
  // Types
  BracingType,
  BracedMemberType,
  BracingDirection,
  ColumnBracingInput,
  BeamBracingInput,
  BeamColumnBracingInput,
  
  // Column bracing functions
  calculateColumnPanelShearStrength,
  calculateColumnPanelStiffness,
  calculateColumnPointStrength,
  calculateColumnPointStiffness,
  designColumnBracing,
  
  // Beam bracing functions
  calculateBeamPanelShearStrength,
  calculateBeamPanelStiffness,
  calculateBeamPointStrength,
  calculateBeamPointStiffness,
  calculateTorsionalBracingStrength,
  calculateTorsionalBracingStiffness,
  calculateWebDistortionalStiffness,
  designBeamBracing,
  
  // Beam-column bracing functions
  designBeamColumnBracing,
  validateBeamColumnBracingInput,
} from "./index";

describe("Member Stability Bracing", () => {
  
  describe("Column Bracing - AISC Section 6.2", () => {
    
    describe("Panel Bracing", () => {
      
      describe("calculateColumnPanelShearStrength", () => {
        it("should calculate correct shear strength per AISC Eq. A-6-1", () => {
          const Pr = 200; // kips
          const expectedVbr = 0.005 * 200; // 1.0 kip
          
          const result = calculateColumnPanelShearStrength(Pr);
          expect(result).toBeCloseTo(expectedVbr, 3);
        });

        it("should throw error for zero or negative Pr", () => {
          expect(() => calculateColumnPanelShearStrength(0)).toThrow();
          expect(() => calculateColumnPanelShearStrength(-100)).toThrow();
        });
      });

      describe("calculateColumnPanelStiffness", () => {
        it("should calculate correct stiffness for LRFD per AISC Eq. A-6-2a", () => {
          const Pr = 200; // kips
          const Lbr = 120; // in
          const phi = 0.75;
          const multiplier = 2.0;
          const expectedBeta = (1 / phi) * (multiplier * Pr) / Lbr;
          
          const result = calculateColumnPanelStiffness(Pr, Lbr, DesignMethod.LRFD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });

        it("should calculate correct stiffness for ASD per AISC Eq. A-6-2b", () => {
          const Pr = 200; // kips
          const Lbr = 120; // in
          const omega = 2.0;
          const multiplier = 2.0;
          const expectedBeta = omega * (multiplier * Pr) / Lbr;
          
          const result = calculateColumnPanelStiffness(Pr, Lbr, DesignMethod.ASD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });

        it("should throw error for invalid inputs", () => {
          expect(() => calculateColumnPanelStiffness(0, 120, DesignMethod.LRFD)).toThrow();
          expect(() => calculateColumnPanelStiffness(200, 0, DesignMethod.LRFD)).toThrow();
          expect(() => calculateColumnPanelStiffness(200, 120, "INVALID" as any)).toThrow();
        });
      });
    });

    describe("Point Bracing", () => {
      
      describe("calculateColumnPointStrength", () => {
        it("should calculate correct strength per AISC Eq. A-6-3", () => {
          const Pr = 200; // kips
          const expectedPbr = 0.01 * 200; // 2.0 kips
          
          const result = calculateColumnPointStrength(Pr);
          expect(result).toBeCloseTo(expectedPbr, 3);
        });

        it("should throw error for zero or negative Pr", () => {
          expect(() => calculateColumnPointStrength(0)).toThrow();
          expect(() => calculateColumnPointStrength(-100)).toThrow();
        });
      });

      describe("calculateColumnPointStiffness", () => {
        it("should calculate correct stiffness for LRFD per AISC Eq. A-6-4a", () => {
          const Pr = 200; // kips
          const Lbr = 120; // in
          const phi = 0.75;
          const multiplier = 8.0;
          const expectedBeta = (1 / phi) * (multiplier * Pr) / Lbr;
          
          const result = calculateColumnPointStiffness(Pr, Lbr, DesignMethod.LRFD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });

        it("should calculate correct stiffness for ASD per AISC Eq. A-6-4b", () => {
          const Pr = 200; // kips
          const Lbr = 120; // in
          const omega = 2.0;
          const multiplier = 8.0;
          const expectedBeta = omega * (multiplier * Pr) / Lbr;
          
          const result = calculateColumnPointStiffness(Pr, Lbr, DesignMethod.ASD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });
      });
    });

    describe("designColumnBracing", () => {
      
      it("should design panel bracing correctly", () => {
        const input: ColumnBracingInput = {
          Pr: 200,
          Lbr: 120,
          designMethod: DesignMethod.LRFD,
          bracingType: BracingType.PANEL
        };

        const result = designColumnBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.COLUMN);
        expect(result.bracingType).toBe(BracingType.PANEL);
        expect(result.strength.Vbr).toBeCloseTo(1.0, 3);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-1");
        expect(result.stiffness.units).toBe("kips/in");
        expect(result.stiffness.governingEquation).toBe("AISC Eq. A-6-2a");
      });

      it("should design point bracing correctly", () => {
        const input: ColumnBracingInput = {
          Pr: 200,
          Lbr: 120,
          designMethod: DesignMethod.ASD,
          bracingType: BracingType.POINT
        };

        const result = designColumnBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.COLUMN);
        expect(result.bracingType).toBe(BracingType.POINT);
        expect(result.strength.Pbr).toBeCloseTo(2.0, 3);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-3");
        expect(result.stiffness.units).toBe("kips/in");
        expect(result.stiffness.governingEquation).toBe("AISC Eq. A-6-4b");
      });

      it("should throw error for unsupported bracing type", () => {
        const input: ColumnBracingInput = {
          Pr: 200,
          Lbr: 120,
          designMethod: DesignMethod.LRFD,
          bracingType: BracingType.CONTINUOUS
        };

        expect(() => designColumnBracing(input)).toThrow();
      });
    });
  });

  describe("Beam Bracing - AISC Section 6.3", () => {
    
    describe("Lateral Panel Bracing", () => {
      
      describe("calculateBeamPanelShearStrength", () => {
        it("should calculate correct shear strength per AISC Eq. A-6-5", () => {
          const Mr = 3000; // kip-in
          const ho = 12; // in
          const expectedVbr = 0.01 * (Mr * 1.0) / ho; // 2.5 kips
          
          const result = calculateBeamPanelShearStrength(Mr, ho);
          expect(result).toBeCloseTo(expectedVbr, 3);
        });

        it("should handle double curvature coefficient Cd = 2.0", () => {
          const Mr = 3000; // kip-in
          const ho = 12; // in
          const Cd = 2.0;
          const expectedVbr = 0.01 * (Mr * Cd) / ho; // 5.0 kips
          
          const result = calculateBeamPanelShearStrength(Mr, ho, Cd);
          expect(result).toBeCloseTo(expectedVbr, 3);
        });

        it("should throw error for invalid inputs", () => {
          expect(() => calculateBeamPanelShearStrength(0, 12)).toThrow();
          expect(() => calculateBeamPanelShearStrength(3000, 0)).toThrow();
          expect(() => calculateBeamPanelShearStrength(3000, 12, 0)).toThrow();
        });
      });

      describe("calculateBeamPanelStiffness", () => {
        it("should calculate correct stiffness for LRFD per AISC Eq. A-6-6a", () => {
          const Mr = 3000; // kip-in
          const ho = 12; // in
          const Lbr = 120; // in
          const phi = 0.75;
          const multiplier = 2.0;
          const expectedBeta = (1 / phi) * (multiplier * Mr * 1.0) / (Lbr * ho);
          
          const result = calculateBeamPanelStiffness(Mr, ho, Lbr, DesignMethod.LRFD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });

        it("should calculate correct stiffness for ASD per AISC Eq. A-6-6b", () => {
          const Mr = 3000; // kip-in
          const ho = 12; // in
          const Lbr = 120; // in
          const omega = 2.0;
          const multiplier = 2.0;
          const expectedBeta = omega * (multiplier * Mr * 1.0) / (Lbr * ho);
          
          const result = calculateBeamPanelStiffness(Mr, ho, Lbr, DesignMethod.ASD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });
      });
    });

    describe("Lateral Point Bracing", () => {
      
      describe("calculateBeamPointStrength", () => {
        it("should calculate correct strength per AISC Eq. A-6-7", () => {
          const Mr = 3000; // kip-in
          const ho = 12; // in
          const expectedPbr = 0.02 * (Mr * 1.0) / ho; // 5.0 kips
          
          const result = calculateBeamPointStrength(Mr, ho);
          expect(result).toBeCloseTo(expectedPbr, 3);
        });
      });

      describe("calculateBeamPointStiffness", () => {
        it("should calculate correct stiffness for LRFD per AISC Eq. A-6-8a", () => {
          const Mr = 3000; // kip-in
          const ho = 12; // in
          const Lbr = 120; // in
          const phi = 0.75;
          const expectedBeta = (1 / phi) * (10 * Mr * 1.0) / (Lbr * ho);
          
          const result = calculateBeamPointStiffness(Mr, ho, Lbr, DesignMethod.LRFD);
          expect(result).toBeCloseTo(expectedBeta, 3);
        });
      });
    });

    describe("Torsional Bracing", () => {
      
      describe("calculateTorsionalBracingStrength", () => {
        it("should calculate correct strength per AISC Eq. A-6-9", () => {
          const Mr = 3000; // kip-in
          const Cb = 1.0; // Lateral-torsional buckling modification factor
          const Lbr = 120; // in
          const ho = 12; // in
          const L = 360; // in
          const nEIyeff = 29000 * 2 * 100; // n*E*Iyeff = 2 * 29000 * 100

          const result = calculateTorsionalBracingStrength(Mr, Cb, Lbr, ho, L, nEIyeff);
          
          // Should be at least 0.02*Mr = 60 kip-in (minimum)
          expect(result).toBeGreaterThanOrEqual(0.02 * Mr);
        });

        it("should return minimum flexural strength when calculated is less", () => {
          const Mr = 3000; // kip-in
          const Cb = 1.0;
          const Lbr = 1; // very small unbraced length
          const ho = 12; // in
          const L = 360; // in
          const nEIyeff = 29000 * 10 * 1000; // large stiffness

          const result = calculateTorsionalBracingStrength(Mr, Cb, Lbr, ho, L, nEIyeff);
          
          // Should return minimum = 0.02*Mr = 60 kip-in
          expect(result).toBeCloseTo(0.02 * Mr, 1);
        });
      });

      describe("calculateTorsionalBracingStiffness", () => {
        it("should calculate correct stiffness for LRFD per AISC Eq. A-6-11a", () => {
          const params = {
            Mr: 3000,
            Cb: 1.0,
            E: 29000,
            Iyeff: 100,
            n: 2,
            L: 360,
            designMethod: DesignMethod.LRFD
          };

          const phi = 0.75;
          const multiplier = 3.6;
          const expectedBeta = (1 / phi) * (multiplier * params.L * (params.Mr / params.Cb) ** 2) / (params.n * params.E * params.Iyeff);
          
          const result = calculateTorsionalBracingStiffness(params);
          expect(result).toBeCloseTo(expectedBeta, 1);
        });

        it("should calculate correct stiffness for ASD per AISC Eq. A-6-11b", () => {
          const params = {
            Mr: 3000,
            Cb: 1.0,
            E: 29000,
            Iyeff: 100,
            n: 2,
            L: 360,
            designMethod: DesignMethod.ASD
          };

          const omega = 3.0;
          const multiplier = 3.6;
          const expectedBeta = omega * (multiplier * params.L * (params.Mr / params.Cb) ** 2) / (params.n * params.E * params.Iyeff);
          
          const result = calculateTorsionalBracingStiffness(params);
          expect(result).toBeCloseTo(expectedBeta, 1);
        });
      });
    });

    describe("Web Distortional Stiffness", () => {
      
      describe("calculateWebDistortionalStiffness", () => {
        it("should calculate correct stiffness per AISC Eq. A-6-12", () => {
          const params = {
            E: 29000,
            ho: 12,
            tw: 0.25,
            bs: 4,
            tst: 0.375
          };

          const flexuralTerm = 1.5 * params.ho * params.tw ** 3;
          const torsionalTerm = 1.0 * params.bs ** 3;
          const expectedBeta = (3.3 * params.E * (flexuralTerm + torsionalTerm)) / 12;
          
          const result = calculateWebDistortionalStiffness(params);
          expect(result).toBeCloseTo(expectedBeta, 1);
        });

        it("should throw error for invalid inputs", () => {
          const baseParams = { E: 29000, ho: 12, tw: 0.25, bs: 4, tst: 0.375 };
          
          expect(() => calculateWebDistortionalStiffness({ ...baseParams, E: 0 })).toThrow();
          expect(() => calculateWebDistortionalStiffness({ ...baseParams, ho: 0 })).toThrow();
          expect(() => calculateWebDistortionalStiffness({ ...baseParams, tw: 0 })).toThrow();
          expect(() => calculateWebDistortionalStiffness({ ...baseParams, bs: 0 })).toThrow();
          expect(() => calculateWebDistortionalStiffness({ ...baseParams, tst: 0 })).toThrow();
        });
      });
    });

    describe("designBeamBracing", () => {
      
      it("should design lateral panel bracing correctly", () => {
        const input: BeamBracingInput = {
          Mr: 3000,
          ho: 12,
          Lbr: 120,
          designMethod: DesignMethod.LRFD,
          bracingType: BracingType.PANEL,
          bracingDirection: BracingDirection.LATERAL
        };

        const result = designBeamBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.BEAM);
        expect(result.bracingType).toBe(BracingType.PANEL);
        expect(result.strength.Vbr).toBeCloseTo(2.5, 3);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-5");
        expect(result.stiffness.units).toBe("kips/in");
      });

      it("should design lateral point bracing correctly", () => {
        const input: BeamBracingInput = {
          Mr: 3000,
          ho: 12,
          Lbr: 120,
          designMethod: DesignMethod.ASD,
          bracingType: BracingType.POINT,
          bracingDirection: BracingDirection.LATERAL
        };

        const result = designBeamBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.BEAM);
        expect(result.bracingType).toBe(BracingType.POINT);
        expect(result.strength.Pbr).toBeCloseTo(5.0, 3);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-7");
      });

      it("should design torsional bracing correctly", () => {
        const input: BeamBracingInput = {
          Mr: 3000,
          ho: 12,
          Lbr: 120,
          designMethod: DesignMethod.LRFD,
          bracingType: BracingType.POINT,
          bracingDirection: BracingDirection.TORSIONAL,
          Iyeff: 100,
          L: 360,
          n: 2
        };

        const result = designBeamBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.BEAM);
        expect(result.strength.Mbr).toBeGreaterThan(0);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-9");
        expect(result.stiffness.units).toBe("kip-in/rad");
      });

      it("should throw error for missing torsional parameters", () => {
        const input: BeamBracingInput = {
          Mr: 3000,
          ho: 12,
          Lbr: 120,
          designMethod: DesignMethod.LRFD,
          bracingType: BracingType.POINT,
          bracingDirection: BracingDirection.TORSIONAL
          // Missing Iyeff, L, n
        };

        expect(() => designBeamBracing(input)).toThrow("Iyeff, L, and n are required for torsional bracing calculations");
      });
    });
  });

  describe("Beam-Column Bracing - AISC Section 6.4", () => {
    
    describe("designBeamColumnBracing", () => {
      
      it("should combine panel bracing requirements correctly", () => {
        const input: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 200,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL,
            bracingDirection: BracingDirection.LATERAL
          }
        };

        const result = designBeamColumnBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.BEAM_COLUMN);
        expect(result.bracingType).toBe(BracingType.PANEL);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-1 + A-6-5");
        expect(result.stiffness.governingEquation).toBe("AISC Eq. A-6-2a + A-6-6a");
        
        // Should be sum of individual requirements
        const expectedVbr = 1.0 + 2.5; // Column + Beam
        expect(result.strength.Vbr).toBeCloseTo(expectedVbr, 3);
      });

      it("should combine point bracing requirements correctly", () => {
        const input: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 200,
            Lbr: 120,
            designMethod: DesignMethod.ASD,
            bracingType: BracingType.POINT
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.ASD,
            bracingType: BracingType.POINT,
            bracingDirection: BracingDirection.LATERAL
          }
        };

        const result = designBeamColumnBracing(input);
        
        expect(result.memberType).toBe(BracedMemberType.BEAM_COLUMN);
        expect(result.bracingType).toBe(BracingType.POINT);
        expect(result.strength.governingEquation).toBe("AISC Eq. A-6-3 + A-6-7");
        expect(result.stiffness.governingEquation).toBe("AISC Eq. A-6-4b + A-6-8b");
        
        // Should be sum of individual requirements
        const expectedPbr = 2.0 + 5.0; // Column + Beam
        expect(result.strength.Pbr).toBeCloseTo(expectedPbr, 3);
      });

      it("should throw error for mismatched bracing types", () => {
        const input: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 200,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.POINT, // Different type
            bracingDirection: BracingDirection.LATERAL
          }
        };

        expect(() => designBeamColumnBracing(input)).toThrow("Column and beam bracing types must be the same");
      });

      it("should throw error for mismatched design methods", () => {
        const input: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 200,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.ASD, // Different method
            bracingType: BracingType.PANEL,
            bracingDirection: BracingDirection.LATERAL
          }
        };

        expect(() => designBeamColumnBracing(input)).toThrow("Column and beam design methods must be the same");
      });

      it("should throw error for non-lateral beam bracing", () => {
        const input: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 200,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL,
            bracingDirection: BracingDirection.TORSIONAL // Not lateral
          }
        };

        expect(() => designBeamColumnBracing(input)).toThrow("Only lateral beam bracing is combined with column bracing");
      });
    });

    describe("validateBeamColumnBracingInput", () => {
      
      it("should validate input parameters correctly", () => {
        const validInput: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 200,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL,
            bracingDirection: BracingDirection.LATERAL
          }
        };

        expect(() => validateBeamColumnBracingInput(validInput)).not.toThrow();
      });

      it("should throw error for invalid column parameters", () => {
        const invalidInput: BeamColumnBracingInput = {
          columnBracing: {
            Pr: 0, // Invalid
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL
          },
          beamBracing: {
            Mr: 3000,
            ho: 12,
            Lbr: 120,
            designMethod: DesignMethod.LRFD,
            bracingType: BracingType.PANEL,
            bracingDirection: BracingDirection.LATERAL
          }
        };

        expect(() => validateBeamColumnBracingInput(invalidInput)).toThrow("Column required axial strength Pr must be positive");
      });
    });
  });

  describe("Edge Cases and Error Handling", () => {
    
    it("should handle very small forces correctly", () => {
      const smallPr = 0.001; // Very small but positive
      const result = calculateColumnPanelShearStrength(smallPr);
      expect(result).toBeCloseTo(0.000005, 6);
    });

    it("should handle very large forces correctly", () => {
      const largePr = 10000; // Very large force
      const result = calculateColumnPanelShearStrength(largePr);
      expect(result).toBeCloseTo(50, 3);
    });

    it("should handle very small dimensions correctly", () => {
      const Mr = 1000;
      const smallHo = 0.1; // Very small depth
      const result = calculateBeamPanelShearStrength(Mr, smallHo);
      expect(result).toBeCloseTo(100, 3);
    });

    it("should validate all required parameters are positive", () => {
      // Test comprehensive parameter validation
      expect(() => calculateColumnPanelShearStrength(-1)).toThrow();
      expect(() => calculateBeamPanelShearStrength(-1, 12)).toThrow();
      expect(() => calculateBeamPanelShearStrength(1000, -1)).toThrow();
      expect(() => calculateTorsionalBracingStrength(-1, 1, 120, 12, 360, 1000)).toThrow();
      expect(() => calculateTorsionalBracingStrength(1000, -1, 120, 12, 360, 1000)).toThrow();
    });
  });
}); 