/**
 * @file Column Bracing Calculations
 * @source AISC 360-22, Appendix 6.2 - Column Bracing
 *
 * This module implements bracing requirements for columns according to AISC 360-22.
 * It provides calculations for both panel bracing and point bracing systems.
 */

import { BRACING_CONSTANTS, DesignMethod, E } from "../constants";
import {
  ColumnBracingInput,
  BracingDesignResult,
  BracingStrengthResult,
  BracingStiffnessResult,
  BracingType,
  BracedMemberType,
} from "./types";

/**
 * Calculate required shear strength for column panel bracing
 * @param Pr Required axial strength of column (kips)
 * @returns Required shear strength (kips)
 * @source AISC 360-22, Equation A-6-1
 */
export function calculateColumnPanelShearStrength(Pr: number): number {
  if (Pr <= 0) {
    throw new Error("Required axial strength Pr must be positive");
  }

  return BRACING_CONSTANTS.COLUMN_PANEL.SHEAR_STRENGTH_COEFF * Pr;
}

/**
 * Calculate required stiffness for column panel bracing
 * @param Pr Required axial strength of column (kips)
 * @param Lbr Unbraced length within panel (in)
 * @param designMethod Design method (LRFD or ASD)
 * @returns Required stiffness (kips/in)
 * @source AISC 360-22, Equations A-6-2a and A-6-2b
 */
export function calculateColumnPanelStiffness(
  Pr: number,
  Lbr: number,
  designMethod: DesignMethod
): number {
  if (Pr <= 0) {
    throw new Error("Required axial strength Pr must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }

  const { PHI_LRFD, OMEGA_ASD } = BRACING_CONSTANTS.FACTORS;
  const { LRFD_MULTIPLIER, ASD_MULTIPLIER } =
    BRACING_CONSTANTS.STIFFNESS.PANEL_STIFFNESS;

  if (designMethod === DesignMethod.LRFD) {
    return ((1 / PHI_LRFD) * (LRFD_MULTIPLIER * Pr)) / Lbr;
  } else if (designMethod === DesignMethod.ASD) {
    return (OMEGA_ASD * (ASD_MULTIPLIER * Pr)) / Lbr;
  } else {
    throw new Error(`Invalid design method: ${designMethod}`);
  }
}

/**
 * Calculate required strength for column point bracing
 * @param Pr Required axial strength of column (kips)
 * @returns Required strength (kips)
 * @source AISC 360-22, Equation A-6-3
 */
export function calculateColumnPointStrength(Pr: number): number {
  if (Pr <= 0) {
    throw new Error("Required axial strength Pr must be positive");
  }

  return BRACING_CONSTANTS.COLUMN_POINT.STRENGTH_COEFF * Pr;
}

/**
 * Calculate required stiffness for column point bracing
 * @param Pr Required axial strength of column (kips)
 * @param Lbr Unbraced length adjacent to point brace (in)
 * @param designMethod Design method (LRFD or ASD)
 * @returns Required stiffness (kips/in)
 * @source AISC 360-22, Equations A-6-4a and A-6-4b
 */
export function calculateColumnPointStiffness(
  Pr: number,
  Lbr: number,
  designMethod: DesignMethod
): number {
  if (Pr <= 0) {
    throw new Error("Required axial strength Pr must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }

  const { PHI_LRFD, OMEGA_ASD } = BRACING_CONSTANTS.FACTORS;
  const { LRFD_MULTIPLIER, ASD_MULTIPLIER } =
    BRACING_CONSTANTS.STIFFNESS.POINT_STIFFNESS;

  if (designMethod === DesignMethod.LRFD) {
    return ((1 / PHI_LRFD) * (LRFD_MULTIPLIER * Pr)) / Lbr;
  } else if (designMethod === DesignMethod.ASD) {
    return (OMEGA_ASD * (ASD_MULTIPLIER * Pr)) / Lbr;
  } else {
    throw new Error(`Invalid design method: ${designMethod}`);
  }
}

/**
 * Design column bracing system according to AISC 360-22 Section 6.2
 * @param input Column bracing input parameters
 * @returns Complete bracing design result
 * @source AISC 360-22, Section 6.2 - Column Bracing
 */
export function designColumnBracing(
  input: ColumnBracingInput
): BracingDesignResult {
  const { Pr, Lbr, designMethod, bracingType } = input;

  // Validate inputs
  if (Pr <= 0) {
    throw new Error("Required axial strength Pr must be positive");
  }
  if (Lbr <= 0) {
    throw new Error("Unbraced length Lbr must be positive");
  }

  let strength: BracingStrengthResult;
  let stiffness: BracingStiffnessResult;

  if (bracingType === BracingType.PANEL) {
    // Panel bracing calculations
    const Vbr = calculateColumnPanelShearStrength(Pr);
    const βbr = calculateColumnPanelStiffness(Pr, Lbr, designMethod);

    strength = {
      Vbr,
      governingEquation: "AISC Eq. A-6-1",
      description: "Required shear strength for column panel bracing",
    };

    stiffness = {
      βbr,
      units: "kips/in",
      governingEquation:
        designMethod === DesignMethod.LRFD
          ? "AISC Eq. A-6-2a"
          : "AISC Eq. A-6-2b",
      description: "Required shear stiffness for column panel bracing",
    };
  } else if (bracingType === BracingType.POINT) {
    // Point bracing calculations
    const Pbr = calculateColumnPointStrength(Pr);
    const βbr = calculateColumnPointStiffness(Pr, Lbr, designMethod);

    strength = {
      Pbr,
      governingEquation: "AISC Eq. A-6-3",
      description: "Required strength for column point bracing",
    };

    stiffness = {
      βbr,
      units: "kips/in",
      governingEquation:
        designMethod === DesignMethod.LRFD
          ? "AISC Eq. A-6-4a"
          : "AISC Eq. A-6-4b",
      description: "Required stiffness for column point bracing",
    };
  } else {
    throw new Error(`Unsupported bracing type for columns: ${bracingType}`);
  }

  return {
    strength,
    stiffness,
    bracingType,
    memberType: BracedMemberType.COLUMN,
    source: "AISC 360-22, Section 6.2 - Column Bracing",
  };
}
