/**
 * @file Section slenderness classification according to AISC 360-22 Section B4.
 */

import {
  SlendernessClassification,
  ERROR_MESSAGES,
  ELEMENT_TYPES,
  SECTION_TYPE_STRINGS,
} from "../constants";
import {
  Section,
  SlendernessClassificationResult,
} from "./types";
import { SlendernessLimits } from "../constants";
import { getSlendernessLimits } from "./limits";

/**
 * Classifies the flanges and web of a given section as Compact, Noncompact, or Slender.
 *
 * This function evaluates the slenderness ratios of section elements and compares them
 * against AISC 360-22 limits to determine if local buckling will govern the member's behavior.
 *
 * @param section - The section properties object. It must contain Type, d, bf, tf, and tw
 * @param Fy - The material yield strength in ksi
 * @returns An object indicating the classification of each element (flange and web)
 *
 * @source AISC 360-22, Section B4, "Classification of Sections for Local Buckling"
 * @throws {Error} If the section type is not supported or required properties are missing
 *
 * @example
 * ```typescript
 * const section: Section = {
 *   name: "W12x26",
 *   Type: "W",
 *   A: 7.65,
 *   d: 12.22,
 *   tw: 0.230,
 *   bf: 6.490,
 *   tf: 0.380
 * };
 *
 * const classification = classifySectionSlenderness(section, 50);
 * console.log(`Flange: ${classification.flange}, Web: ${classification.web}`);
 * // Output: "Flange: Compact, Web: Compact"
 * ```
 */
export function classifySectionSlenderness(
  section: Section,
  Fy: number
): SlendernessClassificationResult {
  if (!section || !section.Type) {
    throw new Error(ERROR_MESSAGES.SECTION_TYPE_REQUIRED);
  }

  // Validate required properties
  if (typeof section.d !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("d"));
  }
  if (typeof section.bf !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("bf"));
  }
  if (typeof section.tf !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("tf"));
  }
  if (typeof section.tw !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("tw"));
  }

  let flangeLimitType: string;
  let webLimitType: string;
  let flangeSlendernessRatio: number;
  let webSlendernessRatio: number;

  // This logic can be expanded to handle more section types like 'C', 'HSS', etc.
  if (
    section.Type === SECTION_TYPE_STRINGS.W ||
    section.Type.startsWith(SECTION_TYPE_STRINGS.I)
  ) {
    flangeLimitType = ELEMENT_TYPES.I_FLANGE_FLEXURE;
    webLimitType = ELEMENT_TYPES.I_WEB_FLEXURE;

    // Slenderness for unstiffened elements (flanges)
    // AISC B4.1, Table B4.1b, Case 10
    flangeSlendernessRatio = section.bf / (2 * section.tf);

    // Slenderness for stiffened elements (web)
    // AISC B4.1, Table B4.1b, Case 15
    // h is the clear distance between flanges less the fillet or corner radius for rolled shapes
    // A common approximation for rolled shapes is h = d - 2k_det.
    // A more conservative and simpler approximation is h = d - 2tf, when k is not available.
    const h = section.d - 2 * section.tf;
    webSlendernessRatio = h / section.tw;
  } else {
    throw new Error(`Unsupported section type: ${section.Type}`);
  }

  const flangeLimits = getSlendernessLimits(flangeLimitType, Fy);
  const webLimits = getSlendernessLimits(webLimitType, Fy);

  const flangeClassification = classifyElement(
    flangeSlendernessRatio,
    flangeLimits
  );
  const webClassification = classifyElement(webSlendernessRatio, webLimits);

  return { flange: flangeClassification, web: webClassification };
}

/**
 * Classifies a single element based on its slenderness ratio and limits.
 *
 * @param lambda - The slenderness ratio of the element
 * @param limits - The slenderness limits (lambda_p and lambda_r)
 * @returns The classification string (Compact, Noncompact, or Slender)
 *
 * @example
 * ```typescript
 * const limits = { lambda_p: 9.15, lambda_r: 25.7 };
 * const classification = classifyElement(8.5, limits); // Returns "Compact"
 * ```
 */
export function classifyElement(
  lambda: number,
  limits: SlendernessLimits
): string {
  if (lambda <= limits.lambda_p) {
    return SlendernessClassification.COMPACT;
  } else if (lambda <= limits.lambda_r) {
    return SlendernessClassification.NONCOMPACT;
  } else {
    return SlendernessClassification.SLENDER;
  }
}
