/**
 * @file Main entry point for steel slenderness calculations.
 * Provides access to all slenderness classification and limit functions.
 */

// Re-export types and interfaces
export type {
  Section,
  SlendernessClassificationResult,
} from "./types";
export type { SlendernessLimits } from "../constants";

// Re-export main functions
export { getSlendernessLimits } from "./limits";
export { classifySectionSlenderness, classifyElement } from "./classification";

// Re-export legacy helper functions for backward compatibility
export {
  getIFlangeSlendernessLimits,
  getIWebFlexureSlendernessLimits,
  getHSSWallSlendernessLimits,
} from "./helpers";
