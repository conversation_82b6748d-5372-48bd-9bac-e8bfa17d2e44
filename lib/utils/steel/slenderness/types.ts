/**
 * @file Shared types and interfaces for slenderness calculations.
 */

import { type SupportedSectionType, type SlendernessLimits } from "../constants";

/**
 * Main section properties interface for steel design calculations.
 *
 * @example
 * ```typescript
 * const wSection: Section = {
 *   name: "W12x26",
 *   Type: "W",
 *   A: 7.65,
 *   d: 12.22,
 *   tw: 0.230,
 *   bf: 6.490,
 *   tf: 0.380,
 *   Ix: 204,
 *   Zx: 35.0,
 *   Sx: 33.4,
 *   rx: 5.17,
 *   Iy: 17.3,
 *   Zy: 10.5,
 *   Sy: 5.34,
 *   ry: 1.51,
 *   J: 0.457,
 *   Cw: 695
 * };
 * ```
 */
export interface Section {
  name: string;
  Type: SupportedSectionType;
  A: number; // Cross-sectional area (in²)
  d: number; // Depth (in) - For angles: longer leg length, For round HSS: outside diameter (D)
  tw: number; // Web thickness (in) - For angles: leg thickness, For round HSS: wall thickness (t)
  bf?: number; // Flange width (in) - For angles: shorter leg length (optional if equal angle)
  tf?: number; // Flange thickness (in) - For angles: leg thickness (same as tw)
  D?: number; // Outside diameter for round HSS (in)
  t?: number; // Wall thickness for round HSS (in)
  Ix?: number; // Strong-axis moment of inertia (in⁴)
  Zx?: number; // Strong-axis plastic section modulus (in³)
  Sx?: number; // Strong-axis elastic section modulus (in³)
  rx?: number; // Strong-axis radius of gyration (in)
  Iy?: number; // Weak-axis moment of inertia (in⁴)
  Zy?: number; // Weak-axis plastic section modulus (in³)
  Sy?: number; // Weak-axis elastic section modulus (in³)
  ry?: number; // Weak-axis radius of gyration (in)
  J?: number; // Torsional constant (in⁴)
  Cw?: number; // Warping constant (in⁶)
}

/**
 * Result interface for section slenderness classification.
 */
export interface SlendernessClassificationResult {
  flange: string; // Classification of flange elements (Compact, Noncompact, or Slender)
  web: string; // Classification of web elements (Compact, Noncompact, or Slender)
}
