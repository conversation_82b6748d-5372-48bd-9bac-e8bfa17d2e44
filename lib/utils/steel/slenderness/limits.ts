/**
 * @file Slenderness limits calculations according to AISC 360-22 Table B4.1.
 */

import {
  E,
  ERROR_MESSAGES,
  ELEMENT_TYPES,
  SLENDERNESS_COEFFICIENTS,
} from "../constants";
import { SlendernessLimits } from "../constants";

/**
 * Retrieves the compact (λp) and noncompact (λr) slenderness limits from AISC 360-22 Table B4.1.
 *
 * This function calculates the slenderness limits for various structural elements based on
 * AISC 360-22 specifications. The limits are used to classify elements as compact, noncompact,
 * or slender for local buckling considerations.
 *
 * @param limitType - A key identifying the element (e.g., 'I-flange-flexure', 'I-web-flexure', 'I-flange-compression')
 * @param Fy - The material yield strength in ksi
 * @param kc - Plate buckling coefficient for compression elements (optional, defaults to 4.0 for built-up sections)
 * @returns An object with the slenderness limits (lambda_p and lambda_r)
 *
 * @source AISC 360-22, Table B4.1a (compression elements) and Table B4.1b (flexure elements)
 * @throws {Error} If limitType is not recognized or Fy is not a positive number
 *
 * @example
 * ```typescript
 * // Get flange slenderness limits for a W-shape in flexure
 * const limits = getSlendernessLimits('I-flange-flexure', 50);
 * console.log(`λp = ${limits.lambda_p.toFixed(1)}, λr = ${limits.lambda_r.toFixed(1)}`);
 *
 * // Get web slenderness limits for compression
 * const webLimits = getSlendernessLimits('I-web-compression', 36);
 * ```
 */
export function getSlendernessLimits(
  limitType: string,
  Fy: number,
  kc: number = SLENDERNESS_COEFFICIENTS.SPECIAL.DEFAULT_KC
): SlendernessLimits {
  if (Fy <= 0) {
    throw new Error(ERROR_MESSAGES.FY_POSITIVE);
  }

  const sqrtEFy = Math.sqrt(E / Fy);
  const sqrtKcEFy = Math.sqrt((kc * E) / Fy);

  // AISC 360-22 Table B4.1a - Compression Elements (Members Subject to Axial Compression)
  // AISC 360-22 Table B4.1b - Compression Elements (Members Subject to Flexure)
  const limits: { [key: string]: SlendernessLimits } = {
    // Table B4.1a - Compression Elements
    [ELEMENT_TYPES.I_FLANGE_COMPRESSION]: {
      // Case 1: Flanges of rolled I-shaped sections (only slender limit - no compact/noncompact)
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.I_FLANGE_ROLLED.LAMBDA_P * sqrtEFy, // This is actually the slender limit, but using as lambda_p for consistency
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.I_FLANGE_ROLLED.LAMBDA_R * sqrtEFy, // Same value as lambda_p since there's only one limit
    },
    [ELEMENT_TYPES.I_FLANGE_BUILDUP_COMPRESSION]: {
      // Case 2: Flanges of built-up I-shaped sections
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.I_FLANGE_BUILDUP.LAMBDA_P *
        sqrtKcEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.I_FLANGE_BUILDUP.LAMBDA_R *
        sqrtKcEFy,
    },
    [ELEMENT_TYPES.ANGLE_LEG_COMPRESSION]: {
      // Case 3: Legs of single angles, Legs of double angles with separators, All other unstiffened elements
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.ANGLE_LEG.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.ANGLE_LEG.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.TEE_STEM_COMPRESSION]: {
      // Case 4: Stems of tees
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.TEE_STEM.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.TEE_STEM.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.I_WEB_COMPRESSION]: {
      // Case 5: Webs of doubly symmetric I-shaped sections and channels
      lambda_p: SLENDERNESS_COEFFICIENTS.COMPRESSION.I_WEB.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.COMPRESSION.I_WEB.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.HSS_WALL_COMPRESSION]: {
      // Case 6: Walls of rectangular HSS
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.HSS_WALL.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.HSS_WALL.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.FLANGE_COVER_PLATE_COMPRESSION]: {
      // Case 7: Flange cover plates between lines of fasteners or welds
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.COVER_PLATE.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.COVER_PLATE.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.STIFFENED_ELEMENT_COMPRESSION]: {
      // Case 8: All other stiffened elements
      lambda_p:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.STIFFENED_ELEMENT.LAMBDA_P *
        sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.COMPRESSION.STIFFENED_ELEMENT.LAMBDA_R *
        sqrtEFy,
    },
    [ELEMENT_TYPES.HSS_ROUND_COMPRESSION]: {
      // Case 9: Round HSS
      lambda_p:
        (SLENDERNESS_COEFFICIENTS.COMPRESSION.HSS_ROUND.LAMBDA_P * E) / Fy,
      lambda_r:
        (SLENDERNESS_COEFFICIENTS.COMPRESSION.HSS_ROUND.LAMBDA_R * E) / Fy,
    },

    // Table B4.1b - Flexure Elements
    [ELEMENT_TYPES.I_FLANGE_FLEXURE]: {
      // Case 10: Flanges of rolled I-shaped sections and channels in flexure
      lambda_p:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_ROLLED.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_ROLLED.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.I_FLANGE_BUILDUP_FLEXURE]: {
      // Case 11: Flanges of doubly and singly symmetric I-shaped built-up sections
      lambda_p:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_BUILDUP.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_BUILDUP.LAMBDA_R * sqrtKcEFy,
    },
    [ELEMENT_TYPES.ANGLE_LEG_FLEXURE]: {
      // Case 12: Legs of single angles in flexure (AISC Table B4.1b)
      lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.ANGLE_LEG.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.ANGLE_LEG.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.I_FLANGE_MINOR_AXIS_FLEXURE]: {
      // Case 13: Flanges of all I-shaped sections and channels in flexure about the minor axis
      lambda_p:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_MINOR_AXIS.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_MINOR_AXIS.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.TEE_STEM_FLEXURE]: {
      // Case 14: Stems of tees
      lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.TEE_STEM.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.TEE_STEM.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.TEE_FLANGE_FLEXURE]: {
      // Case 14a: Flanges of tees (similar to I-shape flanges per AISC F9)
      lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.TEE_FLANGE.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.TEE_FLANGE.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.I_WEB_FLEXURE]: {
      // Case 15: Webs of doubly symmetric I-shaped sections and channels in flexure
      lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.I_WEB_SINGLY_SYMMETRIC_FLEXURE]: {
      // Case 16: Webs of singly symmetric I-shaped sections
      // Note: This is a simplified version. The actual formula requires Mp/My ratio
      // λp = (hc/tw) * sqrt(E/Fy) / sqrt(1 - (0.54*Mp/My - 0.09))
      // For conservative design, using Mp/My ≈ 1.0 gives the simplified formula below
      lambda_p:
        (SLENDERNESS_COEFFICIENTS.SPECIAL.SINGLY_SYMMETRIC_MP_MY_COEFF *
          sqrtEFy) /
        Math.sqrt(
          Math.max(
            SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB_SINGLY_SYMMETRIC.LAMBDA_P,
            1 - SLENDERNESS_COEFFICIENTS.SPECIAL.SINGLY_SYMMETRIC_REDUCTION
          )
        ), // Prevent division by zero
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB_SINGLY_SYMMETRIC.LAMBDA_R *
        sqrtEFy,
    },
    [ELEMENT_TYPES.HSS_RECT_FLEXURE]: {
      // Case 17: Flanges of rectangular HSS in flexure
      lambda_p:
        SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_RECT_FLANGE.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_RECT_FLANGE.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.FLANGE_COVER_PLATE_FLEXURE]: {
      // Case 18: Flange cover plates between lines of fasteners or welds
      lambda_p:
        SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_RECT_FLANGE.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_RECT_FLANGE.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.HSS_WEB_FLEXURE]: {
      // Case 19: Webs of rectangular HSS and box sections in flexure
      lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_WEB.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_WEB.LAMBDA_R * sqrtEFy,
    },
    [ELEMENT_TYPES.HSS_ROUND_FLEXURE]: {
      // Case 20: Round HSS in flexure
      lambda_p: (SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_ROUND.LAMBDA_P * E) / Fy,
      lambda_r: (SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_ROUND.LAMBDA_R * E) / Fy,
    },
    [ELEMENT_TYPES.BOX_FLANGE_FLEXURE]: {
      // Case 21: Flanges of box sections
      lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.BOX_FLANGE.LAMBDA_P * sqrtEFy,
      lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.BOX_FLANGE.LAMBDA_R * sqrtEFy,
    },

    // Legacy aliases for backward compatibility
    [ELEMENT_TYPES.I_FLANGE]: {
      // Default to flexure case for backward compatibility
      lambda_p:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_ROLLED.LAMBDA_P * sqrtEFy,
      lambda_r:
        SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_ROLLED.LAMBDA_R * sqrtEFy,
    },
  };

  if (limitType in limits) {
    return limits[limitType];
  } else {
    throw new Error(
      `Unknown limitType: ${limitType}. Available types: ${Object.keys(
        limits
      ).join(", ")}`
    );
  }
}
