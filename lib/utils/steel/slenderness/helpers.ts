/**
 * @file Helper functions for slenderness calculations.
 * These are primarily legacy functions maintained for backward compatibility.
 */

import { SLENDERNESS_LIMITS, ELEMENT_TYPES } from "../constants";
import { SlendernessLimits } from "../constants";

/**
 * Calculates slenderness limits for I-shaped flanges.
 *
 * @param Fy - Yield strength in ksi
 * @returns Compact and noncompact limits
 *
 * @deprecated Use `getSlendernessLimits(ELEMENT_TYPES.I_FLANGE_FLEXURE, Fy)` instead
 *
 * @example
 * ```typescript
 * const limits = getIFlangeSlendernessLimits(50);
 * console.log(`λp = ${limits.lambda_p.toFixed(1)}`);
 * ```
 */
export function getIFlangeSlendernessLimits(Fy: number): SlendernessLimits {
  return SLENDERNESS_LIMITS[ELEMENT_TYPES.I_FLANGE](Fy);
}

/**
 * Calculates slenderness limits for I-shaped webs in flexure.
 *
 * @param Fy - Yield strength in ksi
 * @returns Compact and noncompact limits
 *
 * @deprecated Use `getSlendernessLimits(ELEMENT_TYPES.I_WEB_FLEXURE, Fy)` instead
 *
 * @example
 * ```typescript
 * const limits = getIWebFlexureSlendernessLimits(50);
 * console.log(`λp = ${limits.lambda_p.toFixed(1)}`);
 * ```
 */
export function getIWebFlexureSlendernessLimits(Fy: number): SlendernessLimits {
  return SLENDERNESS_LIMITS["I-web-flexure"](Fy);
}

/**
 * Calculates slenderness limits for HSS walls.
 *
 * @param Fy - Yield strength in ksi
 * @returns Compact and noncompact limits
 *
 * @deprecated Use `getSlendernessLimits(ELEMENT_TYPES.HSS_WALL_COMPRESSION, Fy)` instead
 *
 * @example
 * ```typescript
 * const limits = getHSSWallSlendernessLimits(46);
 * console.log(`λp = ${limits.lambda_p.toFixed(1)}`);
 * ```
 */
export function getHSSWallSlendernessLimits(Fy: number): SlendernessLimits {
  return SLENDERNESS_LIMITS["HSS-wall"](Fy);
}
