import { describe, it, expect } from "@jest/globals";
import { Section } from "./slenderness/types";
import {
  Material,
  FlexuralLimitState,
  ShearLimitState,
  DesignMethod,
} from "./constants";
import {
  BeamAnalysisInputs,
  DemandForces,
  LTBParameters,
  DeflectionParameters,
  DesignCheckResults,
  getDesignCheckResults,
  performLRFDDesignCheck,
  performASDDesignCheck,
  performLRFDDesignCheckWithDeflection,
  performASDDesignCheckWithDeflection,
} from "./steel-beam-orchestrator";
import { DEFAULT_DEFLECTION_LIMITS } from "./deflection/index";

describe("Steel Design Orchestrator", () => {
  // Standard test sections and materials
  const W18X50: Section = {
    name: "W18X50",
    Type: "W",
    A: 14.7,
    d: 18.0,
    tw: 0.355,
    bf: 7.5,
    tf: 0.57,
    Ix: 800,
    Zx: 101,
    Sx: 88.9,
    rx: 7.38,
    Iy: 40.1,
    Zy: 18.7,
    Sy: 10.7,
    ry: 1.65,
    J: 1.43,
    Cw: 1910,
  };

  const HSS8X4X5_8: Section = {
    name: "HSS8X4X5/8",
    Type: "HSS-RECT",
    A: 10.1,
    d: 8.0,
    tw: 0.589,
    bf: 4.0,
    tf: 0.589,
    Ix: 81.3,
    Zx: 25.4,
    Sx: 20.3,
    Iy: 23.4,
    Zy: 10.3,
    Sy: 8.71,
    J: 76.5,
    Cw: 0,
    ry: 1.52,
  };

  const A992_MATERIAL: Material = { Fy: 50, Fu: 65 }; // ksi
  const A500_GR_C: Material = { Fy: 50, Fu: 62 }; // ksi

  describe("getDesignCheckResults", () => {
    describe("Standard Cases - LRFD", () => {
      it("should perform complete LRFD design check for W18x50", () => {
        const inputs: BeamAnalysisInputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120, Cb: 1.0 }, // 10 ft unbraced length
          demandForces: { Mu: 3000, Vu: 100 }, // kip-in, kips
          designMethod: DesignMethod.LRFD,
        };

        const results = getDesignCheckResults(inputs);

        // Verify structure
        expect(results.flexure).toBeDefined();
        expect(results.shear).toBeDefined();
        expect(results.designMethod).toBe(DesignMethod.LRFD);
        expect(results.sectionName).toBe("W18X50");
        expect(results.materialGrade).toBe("Fy = 50 ksi, Fu = 65 ksi");

        // Verify flexural results
        expect(results.flexure.nominalStrength).toBeGreaterThan(0);
        expect(results.flexure.governingLimitState).toBeDefined();
        expect(results.flexure.resistanceFactor).toBe(0.9); // φ for flexure
        expect(results.flexure.availableStrength).toBeCloseTo(
          results.flexure.nominalStrength * 0.9,
          1
        );
        expect(results.flexure.demand).toBe(3000);
        expect(results.flexure.dcr).toBe(
          results.flexure.demand / results.flexure.availableStrength
        );
        expect(results.flexure.adequate).toBe(results.flexure.dcr <= 1.0);

        // Verify shear results
        expect(results.shear.nominalStrength).toBeGreaterThan(0);
        expect(results.shear.governingLimitState).toBeDefined();
        expect(results.shear.resistanceFactor).toBe(1.0); // φ for shear yielding (most likely)
        expect(results.shear.availableStrength).toBeCloseTo(
          results.shear.nominalStrength * 1.0,
          1
        );
        expect(results.shear.demand).toBe(100);
        expect(results.shear.dcr).toBe(
          results.shear.demand / results.shear.availableStrength
        );
        expect(results.shear.adequate).toBe(results.shear.dcr <= 1.0);

        // Overall adequacy
        expect(results.overallAdequate).toBe(
          results.flexure.adequate && results.shear.adequate
        );
      });

      it("should perform complete LRFD design check for HSS section", () => {
        const inputs: BeamAnalysisInputs = {
          section: HSS8X4X5_8,
          material: A500_GR_C,
          ltbParameters: { Lb: 96 }, // 8 ft, Cb defaults to 1.0
          demandForces: { Mu: 800, Vu: 50 }, // kip-in, kips
          designMethod: DesignMethod.LRFD,
        };

        const results = getDesignCheckResults(inputs);

        expect(results.designMethod).toBe(DesignMethod.LRFD);
        expect(results.sectionName).toBe("HSS8X4X5/8");
        expect(results.flexure.resistanceFactor).toBe(0.9);
        expect(results.shear.resistanceFactor).toBe(1.0); // Shear yielding expected
        expect(results.flexure.nominalStrength).toBeGreaterThan(0);
        expect(results.shear.nominalStrength).toBeGreaterThan(0);
      });
    });

    describe("Standard Cases - ASD", () => {
      it("should perform complete ASD design check for W18x50", () => {
        const inputs: BeamAnalysisInputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120, Cb: 1.3 }, // With Cb factor
          demandForces: { Mu: 2000, Vu: 75 }, // Service loads
          designMethod: DesignMethod.ASD,
        };

        const results = getDesignCheckResults(inputs);

        expect(results.designMethod).toBe(DesignMethod.ASD);
        expect(results.flexure.resistanceFactor).toBe(1.67); // Ω for flexure
        expect(results.shear.resistanceFactor).toBe(1.67); // Ω for shear

        // ASD uses division: Available = Nominal / Ω
        expect(results.flexure.availableStrength).toBeCloseTo(
          results.flexure.nominalStrength / 1.67,
          1
        );
        expect(results.shear.availableStrength).toBeCloseTo(
          results.shear.nominalStrength / 1.67,
          1
        );
      });
    });

    describe("Edge Cases", () => {
      it("should handle zero demand forces correctly", () => {
        const inputs: BeamAnalysisInputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 0, Vu: 0 }, // Zero demands
          designMethod: DesignMethod.LRFD,
        };

        const results = getDesignCheckResults(inputs);

        expect(results.flexure.demand).toBe(0);
        expect(results.shear.demand).toBe(0);
        expect(results.flexure.dcr).toBe(0);
        expect(results.shear.dcr).toBe(0);
        expect(results.flexure.adequate).toBe(true);
        expect(results.shear.adequate).toBe(true);
        expect(results.overallAdequate).toBe(true);
      });

      it("should handle demand exactly equal to capacity (DCR = 1.0)", () => {
        const inputs: BeamAnalysisInputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 60 }, // Short unbraced length
          demandForces: { Mu: 1000, Vu: 50 },
          designMethod: DesignMethod.LRFD,
        };

        // First get the capacities
        const initialResults = getDesignCheckResults(inputs);

        // Now set demands equal to available capacities
        inputs.demandForces = {
          Mu: initialResults.flexure.availableStrength,
          Vu: initialResults.shear.availableStrength,
        };

        const results = getDesignCheckResults(inputs);

        expect(results.flexure.dcr).toBeCloseTo(1.0, 6);
        expect(results.shear.dcr).toBeCloseTo(1.0, 6);
        expect(results.flexure.adequate).toBe(true); // DCR = 1.0 is adequate
        expect(results.shear.adequate).toBe(true);
        expect(results.overallAdequate).toBe(true);
      });

      it("should identify inadequate design when demand exceeds capacity", () => {
        const inputs: BeamAnalysisInputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 10000, Vu: 500 }, // Very high demands
          designMethod: DesignMethod.LRFD,
        };

        const results = getDesignCheckResults(inputs);

        expect(results.flexure.dcr).toBeGreaterThan(1.0);
        expect(results.shear.dcr).toBeGreaterThan(1.0);
        expect(results.flexure.adequate).toBe(false);
        expect(results.shear.adequate).toBe(false);
        expect(results.overallAdequate).toBe(false);
      });
    });

    describe("Invalid Inputs", () => {
      it("should throw error for missing beamAnalysisInputs", () => {
        expect(() => getDesignCheckResults(null as any)).toThrow(
          "beamAnalysisInputs is required."
        );
      });

      it("should throw error for missing section", () => {
        const inputs = {
          section: null,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 1000, Vu: 50 },
          designMethod: DesignMethod.LRFD,
        } as any;

        expect(() => getDesignCheckResults(inputs)).toThrow(
          "section is required in beamAnalysisInputs."
        );
      });

      it("should throw error for missing material", () => {
        const inputs = {
          section: W18X50,
          material: null,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 1000, Vu: 50 },
          designMethod: DesignMethod.LRFD,
        } as any;

        expect(() => getDesignCheckResults(inputs)).toThrow(
          "material is required in beamAnalysisInputs."
        );
      });

      it("should throw error for missing ltbParameters", () => {
        const inputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: null,
          demandForces: { Mu: 1000, Vu: 50 },
          designMethod: DesignMethod.LRFD,
        } as any;

        expect(() => getDesignCheckResults(inputs)).toThrow(
          "ltbParameters is required in beamAnalysisInputs."
        );
      });

      it("should throw error for missing demandForces", () => {
        const inputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: null,
          designMethod: DesignMethod.LRFD,
        } as any;

        expect(() => getDesignCheckResults(inputs)).toThrow(
          "demandForces is required in beamAnalysisInputs."
        );
      });

      it("should throw error for negative demand forces", () => {
        const inputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: -1000, Vu: 50 },
          designMethod: DesignMethod.LRFD,
        } as any;

        expect(() => getDesignCheckResults(inputs)).toThrow(
          "demandForces.Mu must be a non-negative number."
        );
      });

      it("should throw error for negative unbraced length", () => {
        const inputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: -120 },
          demandForces: { Mu: 1000, Vu: 50 },
          designMethod: DesignMethod.LRFD,
        } as any;

        expect(() => getDesignCheckResults(inputs)).toThrow(
          "ltbParameters.Lb must be a non-negative number."
        );
      });
    });

    describe("Realistic Design Examples", () => {
      it("should handle a typical AISC design example scenario", () => {
        // Based on typical steel beam design
        const inputs: BeamAnalysisInputs = {
          section: W18X50,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 20 * 12, Cb: 1.0 }, // 20 ft span with simple supports
          demandForces: {
            Mu: 1350, // kip-in (~70% of available capacity ~1907 kip-in for elastic LTB)
            Vu: 25, // kips (reasonable shear)
          },
          designMethod: DesignMethod.LRFD,
        };

        const results = getDesignCheckResults(inputs);

        // Should be adequate design for reasonable loads
        expect(results.flexure.adequate).toBe(true);
        expect(results.shear.adequate).toBe(true);
        expect(results.overallAdequate).toBe(true);

        // DCRs should be reasonable (< 0.9 typically for good design)
        expect(results.flexure.dcr).toBeLessThan(0.9);
        expect(results.shear.dcr).toBeLessThan(0.9);

        // Verify governing limit states are reasonable
        expect([
          FlexuralLimitState.YIELDING,
          FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING,
        ]).toContain(results.flexure.governingLimitState);

        expect([
          ShearLimitState.SHEAR_YIELDING,
          ShearLimitState.SHEAR_BUCKLING,
        ]).toContain(results.shear.governingLimitState);
      });
    });
  });

  describe("performLRFDDesignCheck", () => {
    it("should perform LRFD design check with simplified interface", () => {
      const results = performLRFDDesignCheck(
        W18X50,
        A992_MATERIAL,
        120, // Lb
        3000, // Mu
        100, // Vu
        1.2 // Cb
      );

      expect(results.designMethod).toBe(DesignMethod.LRFD);
      expect(results.flexure.resistanceFactor).toBe(0.9);
      expect(results.shear.resistanceFactor).toBe(1.0);
      expect(results.flexure.demand).toBe(3000);
      expect(results.shear.demand).toBe(100);
    });

    it("should use default Cb = 1.0 when not provided", () => {
      const results = performLRFDDesignCheck(
        W18X50,
        A992_MATERIAL,
        120, // Lb
        3000, // Mu
        100 // Vu (Cb defaults to 1.0)
      );

      expect(results.designMethod).toBe(DesignMethod.LRFD);
      expect(results.flexure.nominalStrength).toBeGreaterThan(0);
    });
  });

  describe("performASDDesignCheck", () => {
    it("should perform ASD design check with simplified interface", () => {
      const results = performASDDesignCheck(
        W18X50,
        A992_MATERIAL,
        120, // Lb
        2000, // Ma (service moment)
        75, // Va (service shear)
        1.1 // Cb
      );

      expect(results.designMethod).toBe(DesignMethod.ASD);
      expect(results.flexure.resistanceFactor).toBe(1.67);
      expect(results.shear.resistanceFactor).toBe(1.67);
      expect(results.flexure.demand).toBe(2000);
      expect(results.shear.demand).toBe(75);
    });

    it("should use default Cb = 1.0 when not provided", () => {
      const results = performASDDesignCheck(
        HSS8X4X5_8,
        A500_GR_C,
        96, // Lb
        800, // Ma
        50 // Va (Cb defaults to 1.0)
      );

      expect(results.designMethod).toBe(DesignMethod.ASD);
      expect(results.flexure.nominalStrength).toBeGreaterThan(0);
    });
  });

  describe("Integration Tests", () => {
    it("should integrate all steel utility functions correctly", () => {
      const inputs: BeamAnalysisInputs = {
        section: W18X50,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 140, Cb: 1.3 }, // Intermediate LTB range
        demandForces: { Mu: 3500, Vu: 120 },
        designMethod: DesignMethod.LRFD,
      };

      const results = getDesignCheckResults(inputs);

      // Should integrate slenderness, flexural, shear, and analysis utils
      expect(results.flexure.governingLimitState).toBeDefined();
      expect(results.shear.governingLimitState).toBeDefined();

      // Results should be mathematically consistent
      expect(results.flexure.dcr).toBeCloseTo(
        results.flexure.demand / results.flexure.availableStrength,
        6
      );
      expect(results.shear.dcr).toBeCloseTo(
        results.shear.demand / results.shear.availableStrength,
        6
      );

      // Available strength should be nominal strength modified by resistance factor
      if (results.designMethod === DesignMethod.LRFD) {
        expect(results.flexure.availableStrength).toBeCloseTo(
          results.flexure.nominalStrength * results.flexure.resistanceFactor,
          2
        );
        expect(results.shear.availableStrength).toBeCloseTo(
          results.shear.nominalStrength * results.shear.resistanceFactor,
          2
        );
      }
    });
  });

  describe("Stiffness Adjustments Integration", () => {
    const testSection: Section = {
      name: "W18X50-STIFFNESS-TEST",
      Type: "W",
      A: 14.7,
      d: 18.0,
      tw: 0.355,
      bf: 7.5,
      tf: 0.57,
      Ix: 800,
      Zx: 101,
      Sx: 88.9,
      ry: 1.65,
      Iy: 40.1,
      J: 1.43,
    };

    it("should apply stiffness adjustments when requested with valid inputs", () => {
      const inputs: BeamAnalysisInputs = {
        section: testSection,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50, Pu: 150 }, // Pu required for stiffness adjustments
        designMethod: DesignMethod.LRFD,
        applyStiffnessAdjustments: true,
        nominalAxialStrength: 200, // Required for stiffness adjustments
      };

      const result = getDesignCheckResults(inputs);

      // Verify stiffness adjustments were applied
      expect(result.stiffnessAdjustments.applied).toBe(true);
      expect(result.stiffnessAdjustments.baseReductionFactor).toBe(0.80);
      expect(result.stiffnessAdjustments.tauB).toBeCloseTo(0.75, 3); // 4 * 0.75 * 0.25
      expect(result.stiffnessAdjustments.totalFlexuralReduction).toBeCloseTo(0.60, 3); // 0.8 * 0.75

      // Verify adjusted section properties
      expect(result.stiffnessAdjustments.adjustedSection).toBeDefined();
      expect(result.stiffnessAdjustments.adjustedSection!.IxAdjusted).toBeCloseTo(480, 1); // 800 * 0.6
      expect(result.stiffnessAdjustments.adjustedSection!.IyAdjusted).toBeCloseTo(24.06, 1); // 40.1 * 0.6
    });

    it("should not apply stiffness adjustments when flag is false", () => {
      const inputs: BeamAnalysisInputs = {
        section: testSection,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50 },
        designMethod: DesignMethod.LRFD,
        applyStiffnessAdjustments: false, // Explicitly false
      };

      const result = getDesignCheckResults(inputs);

      // Verify stiffness adjustments were not applied
      expect(result.stiffnessAdjustments.applied).toBe(false);
      expect(result.stiffnessAdjustments.baseReductionFactor).toBe(1.0);
      expect(result.stiffnessAdjustments.tauB).toBe(1.0);
      expect(result.stiffnessAdjustments.totalFlexuralReduction).toBe(1.0);
      expect(result.stiffnessAdjustments.adjustedSection).toBeUndefined();
    });

    it("should handle ASD design method for stiffness adjustments", () => {
      const inputs: BeamAnalysisInputs = {
        section: testSection,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 2000, Vu: 40, Pu: 100 }, // Service loads for ASD
        designMethod: DesignMethod.ASD,
        applyStiffnessAdjustments: true,
        nominalAxialStrength: 200,
      };

      const result = getDesignCheckResults(inputs);

      // For ASD: αPr/Pn = 1.6 * 100/200 = 0.8
      // τb = 4 * 0.8 * (1 - 0.8) = 4 * 0.8 * 0.2 = 0.64
      expect(result.stiffnessAdjustments.applied).toBe(true);
      expect(result.stiffnessAdjustments.tauB).toBeCloseTo(0.64, 3);
      expect(result.stiffnessAdjustments.totalFlexuralReduction).toBeCloseTo(0.512, 3); // 0.8 * 0.64
    });

    it("should handle low axial load case (τb = 1.0)", () => {
      const inputs: BeamAnalysisInputs = {
        section: testSection,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50, Pu: 50 }, // Low axial load
        designMethod: DesignMethod.LRFD,
        applyStiffnessAdjustments: true,
        nominalAxialStrength: 200,
      };

      const result = getDesignCheckResults(inputs);

      // αPr/Pn = 1.0 * 50/200 = 0.25 ≤ 0.5, so τb = 1.0
      expect(result.stiffnessAdjustments.applied).toBe(true);
      expect(result.stiffnessAdjustments.tauB).toBe(1.0);
      expect(result.stiffnessAdjustments.totalFlexuralReduction).toBeCloseTo(0.80, 3); // 0.8 * 1.0
    });

    it("should throw error when nominalAxialStrength is missing but adjustments are requested", () => {
      const inputs: BeamAnalysisInputs = {
        section: testSection,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50, Pu: 150 },
        designMethod: DesignMethod.LRFD,
        applyStiffnessAdjustments: true,
        // nominalAxialStrength missing
      };

      expect(() => getDesignCheckResults(inputs)).toThrow(
        "nominalAxialStrength is required and must be positive when applyStiffnessAdjustments is true."
      );
    });

    it("should throw error when Pu is missing but adjustments are requested", () => {
      const inputs: BeamAnalysisInputs = {
        section: testSection,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50 }, // Pu missing
        designMethod: DesignMethod.LRFD,
        applyStiffnessAdjustments: true,
        nominalAxialStrength: 200,
      };

      expect(() => getDesignCheckResults(inputs)).toThrow(
        "demandForces.Pu is required and must be non-negative when applyStiffnessAdjustments is true."
      );
    });

    it("should handle sections without Ix/Iy properties", () => {
      const sectionWithoutInertia: Section = {
        ...testSection,
        Ix: undefined,
        Iy: undefined,
      };

      const inputs: BeamAnalysisInputs = {
        section: sectionWithoutInertia,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50, Pu: 150 },
        designMethod: DesignMethod.LRFD,
        applyStiffnessAdjustments: true,
        nominalAxialStrength: 200,
      };

      const result = getDesignCheckResults(inputs);

      // Should still apply adjustment factors but without adjusted section properties
      expect(result.stiffnessAdjustments.applied).toBe(true);
      expect(result.stiffnessAdjustments.tauB).toBeCloseTo(0.75, 3);
      expect(result.stiffnessAdjustments.adjustedSection).toBeUndefined();
    });
  });

  describe("Deflection Checks Integration", () => {
    const testSection: Section = {
      name: "W18X50-DEFLECTION-TEST",
      Type: "W",
      A: 14.7,
      d: 18.0,
      tw: 0.355,
      bf: 7.5,
      tf: 0.57,
      Ix: 800,
      Zx: 101,
      Sx: 88.9,
      rx: 7.38,
      Iy: 40.1,
      Zy: 18.7,
      Sy: 10.7,
      ry: 1.65,
      J: 1.43,
      Cw: 1910,
    };

    describe("Design check with deflection parameters", () => {
      it("should perform complete design check with adequate deflection", () => {
        const deflectionParams: DeflectionParameters = {
          beamLength: 240, // 20 feet in inches
          maxUpwardDeflection: -0.8, // 0.8 inches upward (negative)
          maxDownwardDeflection: 0.9, // 0.9 inches downward (positive)
          deflectionLimit: 240, // L/240
        };

        const inputs: BeamAnalysisInputs = {
          section: testSection,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 3000, Vu: 100 },
          designMethod: DesignMethod.LRFD,
          deflectionParameters: deflectionParams,
        };

        const result = getDesignCheckResults(inputs);

        // Verify deflection results are included
        expect(result.deflection).toBeDefined();
        expect(result.deflection!.upward.adequate).toBe(true);
        expect(result.deflection!.downward.adequate).toBe(true);
        expect(result.deflection!.overallAdequate).toBe(true);
        expect(result.deflection!.deflectionLimit).toBe(240);
        expect(result.deflection!.beamLength).toBe(240);

        // Check ratios
        expect(result.deflection!.upward.deflectionRatio).toBeCloseTo(0.8, 3);
        expect(result.deflection!.downward.deflectionRatio).toBeCloseTo(0.9, 3);

        // Overall adequacy should include deflection
        expect(result.overallAdequate).toBe(
          result.flexure.adequate && result.shear.adequate && result.deflection!.overallAdequate
        );
      });

      it("should perform complete design check with inadequate deflection", () => {
        const deflectionParams: DeflectionParameters = {
          beamLength: 300, // 25 feet
          maxUpwardDeflection: -0.6, // Adequate upward
          maxDownwardDeflection: 1.2, // Excessive downward (> 300/360 = 0.833)
          deflectionLimit: 360, // L/360
        };

        const inputs: BeamAnalysisInputs = {
          section: testSection,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 2500, Vu: 80 }, // Assume flexure/shear are adequate
          designMethod: DesignMethod.ASD,
          deflectionParameters: deflectionParams,
        };

        const result = getDesignCheckResults(inputs);

        expect(result.deflection).toBeDefined();
        expect(result.deflection!.upward.adequate).toBe(true); // 0.6 < 0.833
        expect(result.deflection!.downward.adequate).toBe(false); // 1.2 > 0.833
        expect(result.deflection!.overallAdequate).toBe(false);
        expect(result.deflection!.controllingDirection).toBe("downward");

        // Overall should be false due to deflection even if flexure/shear pass
        expect(result.overallAdequate).toBe(false);
      });

      it("should work without deflection parameters", () => {
        const inputs: BeamAnalysisInputs = {
          section: testSection,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 3000, Vu: 100 },
          designMethod: DesignMethod.LRFD,
          // No deflection parameters
        };

        const result = getDesignCheckResults(inputs);

        expect(result.deflection).toBeUndefined();
        expect(result.overallAdequate).toBe(
          result.flexure.adequate && result.shear.adequate
        );
      });
    });

    describe("Convenience functions with deflection", () => {
      it("should perform LRFD design check with deflection using convenience function", () => {
        const result = performLRFDDesignCheckWithDeflection(
          testSection,
          A992_MATERIAL,
          120, // Lb
          3000, // Mu
          100, // Vu
          240, // beamLength
          -0.8, // maxUpwardDeflection
          0.9, // maxDownwardDeflection
          1.0, // Cb
          240 // deflectionLimit
        );

        expect(result.designMethod).toBe(DesignMethod.LRFD);
        expect(result.deflection).toBeDefined();
        expect(result.deflection!.upward.adequate).toBe(true);
        expect(result.deflection!.downward.adequate).toBe(true);
        expect(result.deflection!.overallAdequate).toBe(true);
      });

      it("should perform ASD design check with deflection using convenience function", () => {
        const result = performASDDesignCheckWithDeflection(
          testSection,
          A992_MATERIAL,
          120, // Lb
          2000, // Ma (service load)
          75, // Va (service load)
          360, // beamLength (30 feet)
          -1.0, // maxUpwardDeflection
          1.2, // maxDownwardDeflection (excessive for L/360)
          1.2, // Cb
          360 // deflectionLimit
        );

        expect(result.designMethod).toBe(DesignMethod.ASD);
        expect(result.deflection).toBeDefined();
        expect(result.deflection!.upward.adequate).toBe(true); // 1.0 < 1.0
        expect(result.deflection!.downward.adequate).toBe(false); // 1.2 > 1.0
        expect(result.deflection!.overallAdequate).toBe(false);
        expect(result.overallAdequate).toBe(false);
      });

      it("should use default deflection limit when not specified", () => {
        const result = performLRFDDesignCheckWithDeflection(
          testSection,
          A992_MATERIAL,
          120, // Lb
          3000, // Mu
          100, // Vu
          240, // beamLength
          -0.5, // maxUpwardDeflection
          0.5, // maxDownwardDeflection
          1.0 // Cb (deflectionLimit will default)
        );

        expect(result.deflection!.deflectionLimit).toBe(DEFAULT_DEFLECTION_LIMITS.TOTAL);
      });
    });

    describe("Real-world deflection scenarios", () => {
      it("should handle typical office floor beam deflection", () => {
        const result = performLRFDDesignCheckWithDeflection(
          testSection,
          A992_MATERIAL,
          120, // 10 ft unbraced length
          3500, // Factored moment
          120, // Factored shear
          360, // 30 ft span
          -0.8, // Upward deflection
          1.2, // Downward deflection (excessive for L/360)
          1.0, // Cb
          360 // L/360 for floors
        );

        expect(result.deflection!.deflectionLimit).toBe(360);
        expect(result.deflection!.upward.adequate).toBe(true); // 0.8 < 1.0
        expect(result.deflection!.downward.adequate).toBe(false); // 1.2 > 1.0
        expect(result.deflection!.controllingDirection).toBe("downward");
      });

      it("should handle typical roof beam deflection", () => {
        const result = performASDDesignCheckWithDeflection(
          testSection,
          A992_MATERIAL,
          144, // 12 ft unbraced length
          2500, // Service moment
          100, // Service shear
          288, // 24 ft span
          -0.9, // Wind uplift
          1.0, // Snow/dead load
          1.3, // Cb
          240 // L/240 for roofs
        );

        expect(result.deflection!.deflectionLimit).toBe(240);
        // Allowable = 288/240 = 1.2 inches
        expect(result.deflection!.upward.adequate).toBe(true); // 0.9 < 1.2
        expect(result.deflection!.downward.adequate).toBe(true); // 1.0 < 1.2
        expect(result.deflection!.overallAdequate).toBe(true);
      });

      it("should handle cantilever beam deflection", () => {
        const result = performLRFDDesignCheckWithDeflection(
          testSection,
          A992_MATERIAL,
          96, // 8 ft cantilever length
          1500, // Factored moment
          80, // Factored shear
          96, // 8 ft cantilever
          -0.6, // Upward case
          1.0, // Downward case (excessive for L/120)
          1.0, // Cb
          120 // L/120 for cantilevers
        );

        expect(result.deflection!.deflectionLimit).toBe(120);
        // Allowable = 96/120 = 0.8 inches
        expect(result.deflection!.upward.adequate).toBe(true); // 0.6 < 0.8
        expect(result.deflection!.downward.adequate).toBe(false); // 1.0 > 0.8
        expect(result.deflection!.overallAdequate).toBe(false);
      });
    });

    describe("Integration with stiffness adjustments", () => {
      it("should perform deflection check with stiffness adjustments", () => {
        const deflectionParams: DeflectionParameters = {
          beamLength: 240,
          maxUpwardDeflection: -0.7,
          maxDownwardDeflection: 0.8,
          deflectionLimit: 240,
        };

        const inputs: BeamAnalysisInputs = {
          section: testSection,
          material: A992_MATERIAL,
          ltbParameters: { Lb: 120 },
          demandForces: { Mu: 3000, Vu: 100, Pu: 150 },
          designMethod: DesignMethod.LRFD,
          applyStiffnessAdjustments: true,
          nominalAxialStrength: 200,
          deflectionParameters: deflectionParams,
        };

        const result = getDesignCheckResults(inputs);

        // Both stiffness adjustments and deflection should be applied
        expect(result.stiffnessAdjustments.applied).toBe(true);
        expect(result.deflection).toBeDefined();
        expect(result.deflection!.upward.adequate).toBe(true);
        expect(result.deflection!.downward.adequate).toBe(true);
        
        // Overall adequacy should consider all checks
        expect(result.overallAdequate).toBe(
          result.flexure.adequate && 
          result.shear.adequate && 
          result.deflection!.overallAdequate
        );
      });
    });
  });

  describe("Angle Section Support", () => {
    // User's specific double angle section
    const DOUBLE_L102X102X19: Section = {
      name: "2L102X102X19X19",
      Type: "2L",
      A: 7.5,      // Approximate area for double L102x102x19
      d: 102/25.4, // Convert mm to inches: 102mm ≈ 4.02 in
      tw: 19/25.4, // Convert mm to inches: 19mm ≈ 0.75 in
      bf: 102/25.4, // Equal legs
      tf: 19/25.4, // Same as tw for angles
      Ix: 100,     // Approximate values for testing
      Sx: 25,
      Iy: 100,
      Sy: 25,
      rx: 3.6,
      ry: 3.6,
      J: 1.0,
      Cw: 0,
    };

    it("should calculate design strength for user's double angle 2L102X102X19X19", () => {
      const inputs: BeamAnalysisInputs = {
        section: DOUBLE_L102X102X19,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 3000, Vu: 50 },
        designMethod: DesignMethod.ASD,
      };

      const result = getDesignCheckResults(inputs);

      // Should successfully calculate shear strength (no more NaN!)
      expect(result.shear.nominalStrength).toBeGreaterThan(0);
      expect(result.shear.nominalStrength).not.toBeNaN();
      expect(result.shear.availableStrength).toBeGreaterThan(0);
      expect(result.shear.availableStrength).not.toBeNaN();
      
      // Should identify as double angle
      expect(result.shear.governingLimitState).toBe("Shear Yielding (AISC G2.1(a))");
      
      // Flexural strength should also work
      expect(result.flexure.nominalStrength).toBeGreaterThan(0);
      expect(result.flexure.nominalStrength).not.toBeNaN();
      
      // Should have calculated design check ratios
      expect(result.shear.dcr).toBeGreaterThan(0);
      expect(result.shear.dcr).not.toBeNaN();
      expect(result.flexure.dcr).toBeGreaterThan(0);
      expect(result.flexure.dcr).not.toBeNaN();
    });

    it("should handle single angle sections", () => {
      const singleAngle: Section = {
        name: "L102X102X19",
        Type: "L",
        A: 3.75,
        d: 102/25.4,
        tw: 19/25.4,
        bf: 102/25.4,
        tf: 19/25.4,
        Sx: 12,
        Ix: 50,
      };

      const inputs: BeamAnalysisInputs = {
        section: singleAngle,
        material: A992_MATERIAL,
        ltbParameters: { Lb: 120 },
        demandForces: { Mu: 1500, Vu: 25 },
        designMethod: DesignMethod.ASD,
      };

      const result = getDesignCheckResults(inputs);

      // Should work for single angles too
      expect(result.shear.nominalStrength).toBeGreaterThan(0);
      expect(result.shear.nominalStrength).not.toBeNaN();
      expect(result.flexure.nominalStrength).toBeGreaterThan(0);
      expect(result.flexure.nominalStrength).not.toBeNaN();
    });
  });
});
