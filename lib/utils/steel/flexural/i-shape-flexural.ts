/**
 * @file I-Shape flexural strength calculations according to AISC 360-22 Sections F4, F5, and F6.
 */

import {
  E,
  FlexuralLimitState,
  ERROR_MESSAGES,
  LTB_CONSTANTS,
  FLEXURAL_CONSTANTS,
  SLENDERNESS_COEFFICIENTS,
  ELEMENT_TYPES,
} from "../constants";
import { Section } from "../slenderness/types";
import { getSlendernessLimits } from "../slenderness/limits";
import { FlexuralStrengthResult } from "./types";
import { getLTBParameters_I_Shape, calculateWebPlastificationFactors } from "./helpers";

/**
 * Calculates nominal flexural strength according to AISC F4 - Doubly Symmetric I-shaped Members 
 * with Compact or Noncompact Webs Bent About Their Major Axis.
 * 
 * This section applies when the web slenderness ratio h/tw ≤ λrw.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F4
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { Zx: 39.6, Sx: 35.0, bf: 6.080, tf: 0.515, d: 8.11, tw: 0.315 };
 * const result = calculateMn_F4_DoublySymmetric(section, 50, 120, 1.0);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_F4_DoublySymmetric(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number
): FlexuralStrengthResult {
  const { Zx, Sx, bf, tf, d, tw } = section;
  
  // Validate required properties
  if (typeof Zx !== "number" || typeof Sx !== "number" || typeof bf !== "number" || typeof tf !== "number") {
    throw new Error(ERROR_MESSAGES.F4_PROPS_REQUIRED);
  }
  
  // Calculate basic parameters
  const Mp = Fy * Zx;
  const My = Fy * Sx;
  const FL = LTB_CONSTANTS.FL_RATIO * Fy; // AISC F4-6a and F4-6b
  
  // Get web plastification factors
  const { Rpc, Rpt } = calculateWebPlastificationFactors(section, Fy);
  
  // Calculate compression and tension flange section moduli
  // For doubly symmetric sections, Sxc = Sxt = Sx
  const Sxc = Sx;
  const Sxt = Sx;
  
  // Calculate yield moments for compression and tension flanges
  const Myc = Fy * Sxc;
  const Myt = Fy * Sxt;
  
  // Get flange slenderness parameters
  const lambda_f = bf / (2 * tf);
  const flangeLimits = getSlendernessLimits(ELEMENT_TYPES.I_FLANGE_FLEXURE, Fy);
  const lambda_pf = flangeLimits.lambda_p;
  const lambda_rf = flangeLimits.lambda_r;
  
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;
  
  // Check all limit states and take minimum
  
  // 1. Yielding (F4-1)
  const Mn_yielding = Mp;
  
  // 2. Lateral-Torsional Buckling (F4-2, F4-3, F4-4)
  const { Lp, Lr } = getLTBParameters_I_Shape(section, Fy);
  let Mn_LTB;
  
  if (Lb <= Lp) {
    Mn_LTB = Mp;
  } else if (Lb <= Lr) {
    // AISC Eq. F4-2
    Mn_LTB = Cb * (Mp - (Mp - FL * Sx) * ((Lb - Lp) / (Lr - Lp)));
    Mn_LTB = Math.min(Mn_LTB, Mp);
  } else {
    // AISC Eq. F4-3
    const { rts } = getLTBParameters_I_Shape(section, Fy);
    const Fcr = (Cb * Math.pow(Math.PI, 2) * E) / Math.pow(Lb / rts, 2);
    Mn_LTB = Math.min(Fcr * Sx, Mp);
  }
  
  // 3. Compression Flange Local Buckling (F4-13, F4-14)
  let Mn_CFLB;
  
  if (lambda_f <= lambda_pf) {
    // Compact flanges - F4-12a
    Mn_CFLB = Mp;
  } else if (lambda_f <= lambda_rf) {
    // Noncompact flanges - AISC Eq. F4-13
    Mn_CFLB = Rpc * Myc - (Rpc * Myc - FL * Sxc) * ((lambda_f - lambda_pf) / (lambda_rf - lambda_pf));
  } else {
    // Slender flanges - AISC Eq. F4-14
    const h = d - 2 * tf;
    let kc = 4 / Math.sqrt(h / tw);
    kc = Math.max(FLEXURAL_CONSTANTS.KC_BOUNDS.MIN, Math.min(FLEXURAL_CONSTANTS.KC_BOUNDS.MAX, kc)); // Bounds per specification
    const Fcr = (FLEXURAL_CONSTANTS.ELASTIC_BUCKLING_COEFF * E * kc) / Math.pow(lambda_f, 2);
    Mn_CFLB = Fcr * Sxc;
  }
  
  // 4. Tension Flange Yielding (F4-15)
  let Mn_TFY;
  
  // Check if tension flange yielding applies
  // Assume Sxt ≈ Sxc for doubly symmetric sections
  if (Sxt >= Sxc) {
    // Tension flange yielding does not apply
    Mn_TFY = Mp;
  } else {
    // AISC Eq. F4-15
    Mn_TFY = Rpt * Myt;
  }
  
  // Find governing limit state
  const candidates = [
    { Mn: Mn_yielding, state: FlexuralLimitState.YIELDING },
    { Mn: Mn_LTB, state: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING },
    { Mn: Mn_CFLB, state: FlexuralLimitState.FLANGE_LOCAL_BUCKLING },
    { Mn: Mn_TFY, state: FlexuralLimitState.YIELDING }
  ];
  
  const governing = candidates.reduce((min, current) => 
    current.Mn < min.Mn ? current : min
  );
  
  return { Mn: governing.Mn, governingLimitState: governing.state };
}

/**
 * Calculates nominal flexural strength according to AISC F5 - Doubly Symmetric and Singly Symmetric
 * I-shaped Members with Slender Webs Bent About Their Major Axis.
 * 
 * This section applies when the web slenderness ratio h/tw > λrw.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F5
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { Zx: 175, Sx: 155, ry: 2.5, d: 18, tf: 0.5, tw: 0.25 };
 * const result = calculateMn_F5_SlenderWebs(section, 50, 200, 1.0);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_F5_SlenderWebs(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number
): FlexuralStrengthResult {
  const { Zx, Sx, bf, tf, d, tw, ry } = section;
  
  // Validate required properties
  if (typeof Zx !== "number" || typeof Sx !== "number" || typeof ry !== "number") {
    throw new Error(ERROR_MESSAGES.F5_PROPS_REQUIRED);
  }
  
  // Calculate web plastification factors
  const { Rpg } = calculateWebPlastificationFactors(section, Fy);
  
  // Calculate compression flange section modulus (assume Sxc = Sx for rolled sections)
  const Sxc = Sx;
  const Sxt = Sx; // For doubly symmetric
  
  let Mn = Fy * Zx;
  let governingLimitState = FlexuralLimitState.YIELDING;
  
  // 1. Compression Flange Yielding - AISC Eq. F5-1
  const Mn_CFY = Rpg * Fy * Sxc;
  
  // 2. Lateral-Torsional Buckling - AISC Eq. F5-2
  let Mn_LTB;
  
  // Calculate F5 LTB parameters
  const Lp = LTB_CONSTANTS.F5_LP_COEFF * ry * Math.sqrt(E / Fy); // F4-7 reference
  const Lr = Math.PI * ry * Math.sqrt(E / (LTB_CONSTANTS.FL_RATIO * Fy)); // AISC Eq. F5-5
  
  if (Lb <= Lp) {
    Mn_LTB = Rpg * Fy * Sxc;
  } else if (Lb <= Lr) {
    // AISC Eq. F5-3
    let Fcr = Cb * (Fy - (LTB_CONSTANTS.F5_STRESS_REDUCTION * Fy) * ((Lb - Lp) / (Lr - Lp)));
    Fcr = Math.min(Fcr, Fy);
    Mn_LTB = Rpg * Fcr * Sxc;
  } else {
    // AISC Eq. F5-4
    const rt = ry; // Simplified for rolled sections
    let Fcr = (Cb * Math.pow(Math.PI, 2) * E) / Math.pow(Lb / rt, 2);
    Fcr = Math.min(Fcr, Fy);
    Mn_LTB = Rpg * Fcr * Sxc;
  }
  
  // 3. Compression Flange Local Buckling - AISC Eq. F5-7
  if (typeof bf !== "number" || typeof tf !== "number") {
    throw new Error(ERROR_MESSAGES.FLANGE_PROPS_REQUIRED);
  }
  
  const lambda_f = bf / (2 * tf);
  const flangeLimits = getSlendernessLimits(ELEMENT_TYPES.I_FLANGE_FLEXURE, Fy);
  const lambda_pf = flangeLimits.lambda_p;
  const lambda_rf = flangeLimits.lambda_r;
  
  let Mn_CFLB;
  
  if (lambda_f <= lambda_pf) {
    // Compact flanges
    Mn_CFLB = Rpg * Fy * Sxc;
  } else if (lambda_f <= lambda_rf) {
    // Noncompact flanges - AISC Eq. F5-8
    const Fcr = Fy - (LTB_CONSTANTS.F5_STRESS_REDUCTION * Fy) * ((lambda_f - lambda_pf) / (lambda_rf - lambda_pf));
    Mn_CFLB = Rpg * Fcr * Sxc;
  } else {
    // Slender flanges - AISC Eq. F5-9
    let kc = 4 / Math.sqrt(d / tw);
    kc = Math.max(FLEXURAL_CONSTANTS.KC_BOUNDS.MIN, Math.min(FLEXURAL_CONSTANTS.KC_BOUNDS.MAX, kc));
    const Fcr = (FLEXURAL_CONSTANTS.ELASTIC_BUCKLING_COEFF * E * kc) / Math.pow(lambda_f, 2);
    Mn_CFLB = Rpg * Fcr * Sxc;
  }
  
  // 4. Tension Flange Yielding - AISC Eq. F5-10
  let Mn_TFY;
  if (Sxt >= Sxc) {
    Mn_TFY = Rpg * Fy * Sxc; // Does not apply
  } else {
    Mn_TFY = Fy * Sxt;
  }
  
  // Find governing limit state
  const candidates = [
    { Mn: Mn_CFY, state: FlexuralLimitState.YIELDING },
    { Mn: Mn_LTB, state: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING },
    { Mn: Mn_CFLB, state: FlexuralLimitState.FLANGE_LOCAL_BUCKLING },
    { Mn: Mn_TFY, state: FlexuralLimitState.YIELDING }
  ];
  
  const governing = candidates.reduce((min, current) => 
    current.Mn < min.Mn ? current : min
  );
  
  return { Mn: governing.Mn, governingLimitState: governing.state };
}

/**
 * Calculates nominal flexural strength according to AISC F6 - I-shaped Members and Channels
 * Bent About Their Minor Axis.
 * 
 * For minor axis bending, lateral-torsional buckling does not apply, but flange local
 * buckling may govern.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F6
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { Sy: 8.5, Zy: 12.8, d: 8.11, tf: 0.515 };
 * const result = calculateMn_F6_MinorAxis(section, 50);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_F6_MinorAxis(
  section: Section,
  Fy: number
): FlexuralStrengthResult {
  const { Zy, Sy, d, tf } = section;
  
  // Validate required properties
  if (typeof Sy !== "number" || typeof d !== "number" || typeof tf !== "number") {
    throw new Error(ERROR_MESSAGES.MINOR_AXIS_PROPS_REQUIRED);
  }
  
  let Mn = Fy * (Zy || Sy);
  let governingLimitState = FlexuralLimitState.YIELDING;
  
  // 1. Yielding - AISC Eq. F6-1
  const Mp = Fy * (Zy || Sy);
  const My_limit = FLEXURAL_CONSTANTS.MINOR_AXIS_LIMIT_FACTOR * Fy * Sy;
  const Mn_yielding = Math.min(Mp, My_limit);
  
  // 2. Flange Local Buckling
  // For minor axis bending, the "flange" is actually the web depth
  const b = d; // For I-shapes, this is the full depth
  const tf_equiv = tf; // Flange thickness
  const lambda = b / tf_equiv;
  
  const flangeLimits = getSlendernessLimits(ELEMENT_TYPES.I_FLANGE_MINOR_AXIS_FLEXURE, Fy);
  const lambda_pf = flangeLimits.lambda_p;
  const lambda_rf = flangeLimits.lambda_r;
  
  let Mn_FLB;
  
  if (lambda <= lambda_pf) {
    // Compact - F6-1
    Mn_FLB = Mn_yielding;
  } else if (lambda <= lambda_rf) {
    // Noncompact - AISC Eq. F6-2
    Mn_FLB = Mp - (Mp - FLEXURAL_CONSTANTS.STRESS_REDUCTION_FACTOR * Fy * Sy) * ((lambda - lambda_pf) / (lambda_rf - lambda_pf));
  } else {
    // Slender - AISC Eq. F6-3, F6-4
    const Fcr = (FLEXURAL_CONSTANTS.STRESS_REDUCTION_FACTOR * E) / Math.pow(lambda, 2);
    Mn_FLB = Fcr * Sy;
  }
  
  // Take minimum
  if (Mn_FLB < Mn_yielding) {
    Mn = Mn_FLB;
    governingLimitState = FlexuralLimitState.FLANGE_LOCAL_BUCKLING;
  } else {
    Mn = Mn_yielding;
    governingLimitState = FlexuralLimitState.YIELDING;
  }
  
  return { Mn, governingLimitState };
}

/**
 * Determines which AISC section (F4, F5, or F6) applies and calculates flexural strength accordingly.
 * This is the main router function for I-shaped member flexural strength calculations.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @param minorAxis - Whether bending is about minor axis (default: false)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Chapter F
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * // Major axis bending
 * const section = { Zx: 39.6, Sx: 35.0, d: 8.11, tf: 0.515, tw: 0.315, ... };
 * const result = calculateMn_I_Shape(section, 50, 120, 1.0, false);
 * 
 * // Minor axis bending
 * const resultMinor = calculateMn_I_Shape(section, 50, 120, 1.0, true);
 * ```
 */
export function calculateMn_I_Shape(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number,
  minorAxis: boolean = false
): FlexuralStrengthResult {
  const { d, tf, tw } = section;
  
  // For minor axis bending, use F6
  if (minorAxis) {
    return calculateMn_F6_MinorAxis(section, Fy);
  }
  
  // Validate required properties for major axis bending
  if (typeof d !== "number" || typeof tf !== "number" || typeof tw !== "number") {
    throw new Error(ERROR_MESSAGES.MAJOR_AXIS_PROPS_REQUIRED);
  }
  
  // For major axis bending, determine if F4 or F5 applies based on web slenderness
  const h = d - 2 * tf;
  const lambda_w = h / tw;
  const lambda_rw = SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_R * Math.sqrt(E / Fy); // Web slenderness limit for F4/F5
  
  if (lambda_w <= lambda_rw) {
    // Compact or noncompact web - use F4
    return calculateMn_F4_DoublySymmetric(section, Fy, Lb, Cb);
  } else {
    // Slender web - use F5
    return calculateMn_F5_SlenderWebs(section, Fy, Lb, Cb);
  }
} 