/**
 * @file Rectangular bars and rounds flexural strength calculations according to AISC 360-22 Section F11.
 * Implements F11: Rectangular Bars and Rounds - yielding and lateral-torsional buckling.
 */

import {
  E,
  FlexuralLimitState,
  ERROR_MESSAGES,
} from "../constants";
import { Section } from "../slenderness/types";
import { FlexuralStrengthResult } from "./types";

/**
 * Calculates lateral-torsional buckling strength for rectangular bars according to AISC F11.2(b).
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @param My - The yield moment capacity
 * @param Mp - The plastic moment capacity
 * @returns Flexural strength result for lateral-torsional buckling
 * 
 * @source AISC 360-22, Equation F11-3
 */
function calculateMn_RectBar_InelasticLTB(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number,
  My: number,
  Mp: number
): { Mn: number; limitState: FlexuralLimitState } {
  const { d, tw } = section;
  
  if (typeof d !== "number" || typeof tw !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("d and tw"));
  }

  // Calculate Lb*d/t²
  const LbDoverT2 = (Lb * d) / Math.pow(tw, 2);
  
  // AISC Eq. F11-3: Mn = Cb[1.52 - 0.274(Lb*d/t²)(Fy/E)]My ≤ Mp
  const term = 1.52 - 0.274 * LbDoverT2 * (Fy / E);
  const Mn = Math.min(Cb * term * My, Mp);
  
  return { Mn: Math.max(Mn, 0), limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING };
}

/**
 * Calculates lateral-torsional buckling strength for rectangular bars according to AISC F11.2(c).
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @param Sx - The elastic section modulus
 * @param Mp - The plastic moment capacity
 * @returns Flexural strength result for lateral-torsional buckling
 * 
 * @source AISC 360-22, Equations F11-4 and F11-5
 */
function calculateMn_RectBar_ElasticLTB(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number,
  Sx: number,
  Mp: number
): { Mn: number; limitState: FlexuralLimitState } {
  const { d, tw } = section;
  
  if (typeof d !== "number" || typeof tw !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("d and tw"));
  }

  // Calculate Lb*d/t²
  const LbDoverT2 = (Lb * d) / Math.pow(tw, 2);
  
  // AISC Eq. F11-5: Fcr = 1.9*E*Cb/(Lb*d/t²)
  const Fcr = (1.9 * E * Cb) / LbDoverT2;
  
  // AISC Eq. F11-4: Mn = Fcr*Sx ≤ Mp
  const Mn = Math.min(Fcr * Sx, Mp);
  
  return { Mn: Math.max(Mn, 0), limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING };
}

/**
 * Calculates the nominal flexural strength (Mn) for rectangular bar members
 * according to AISC 360-22 Section F11.
 * 
 * This function implements F11 limit states for rectangular bars:
 * - F11.1: Yielding (plastic moment = Fy*Z ≤ 1.5*Fy*Sx)
 * - F11.2: Lateral-torsional buckling (for major axis bending with Lb*d/t² > 0.08*E/Fy)
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor (defaults to 1.0)
 * @param bendingAboutMajorAxis - Whether bending is about the major axis (true) or minor axis (false)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F11: "Rectangular Bars and Rounds"
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { 
 *   Type: 'RECT-BAR', 
 *   name: '3x1 Bar',
 *   Zx: 1.5, 
 *   Sx: 1.0, 
 *   A: 3.0,
 *   d: 3.0, 
 *   tw: 1.0,  // thickness
 * };
 * const result = calculateMn_RectangularBar(section, 36, 120, 1.0);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_RectangularBar(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number = 1.0,
  bendingAboutMajorAxis: boolean = true
): FlexuralStrengthResult {
  const { Sx, Zx, d, tw } = section;

  // Validate required properties
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Zx"));
  }

  // F11.1 - Yielding (AISC Eq. F11-1)
  const My = Fy * Sx;
  const Mp = Math.min(Fy * Zx, 1.5 * Fy * Sx); // Fy*Z ≤ 1.5*Fy*Sx
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;

  // F11.2 - Lateral-Torsional Buckling (only for major axis bending)
  if (bendingAboutMajorAxis && typeof d === "number" && typeof tw === "number") {
    // Calculate Lb*d/t²
    const LbDoverT2 = (Lb * d) / Math.pow(tw, 2);
    const threshold1 = (0.08 * E) / Fy; // First threshold
    const threshold2 = (1.9 * E) / Fy;  // Second threshold

    // F11.2(a) - LTB does not apply for short lengths
    if (LbDoverT2 <= threshold1) {
      // The limit state of lateral-torsional buckling does not apply
      // Mn remains as Mp from yielding
    }
    
    // F11.2(b) - Inelastic LTB for intermediate lengths
    else if (LbDoverT2 <= threshold2) {
      const ltbResult = calculateMn_RectBar_InelasticLTB(section, Fy, Lb, Cb, My, Mp);
      if (ltbResult.Mn < Mn) {
        Mn = ltbResult.Mn;
        governingLimitState = ltbResult.limitState;
      }
    }
    
    // F11.2(c) - Elastic LTB for long lengths
    else {
      const ltbResult = calculateMn_RectBar_ElasticLTB(section, Fy, Lb, Cb, Sx, Mp);
      if (ltbResult.Mn < Mn) {
        Mn = ltbResult.Mn;
        governingLimitState = ltbResult.limitState;
      }
    }
  }
  
  // F11.2(a) also states: LTB does not apply for minor axis bending
  // So if bendingAboutMajorAxis = false, only yielding applies

  return { Mn: Math.max(Mn, 0), governingLimitState };
}

/**
 * Calculates the nominal flexural strength (Mn) for round bar members
 * according to AISC 360-22 Section F11.
 * 
 * This function implements F11 limit states for rounds:
 * - F11.1: Yielding (plastic moment = Fy*Z ≤ 1.6*Fy*Sx)
 * - F11.2: Lateral-torsional buckling does not apply for rounds
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches (not used for rounds per F11.2(a))
 * @param Cb - The lateral-torsional buckling modification factor (not used for rounds)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F11: "Rectangular Bars and Rounds"
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { 
 *   Type: 'ROUND-BAR', 
 *   name: '2.5" Round',
 *   Zx: 3.07, 
 *   Sx: 2.45, 
 *   A: 4.91,
 *   d: 2.5, // diameter
 * };
 * const result = calculateMn_RoundBar(section, 36, 120, 1.0);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_RoundBar(
  section: Section,
  Fy: number,
  Lb: number = 0, // Not used for rounds
  Cb: number = 1.0 // Not used for rounds
): FlexuralStrengthResult {
  const { Sx, Zx } = section;

  // Validate required properties
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Zx"));
  }

  // F11.1 - Yielding (AISC Eq. F11-2)
  // For rounds: Mn = Mp = Fy*Z ≤ 1.6*Fy*Sx
  const Mp = Math.min(Fy * Zx, 1.6 * Fy * Sx);
  
  // F11.2(a) - LTB does not apply for rounds
  // Therefore, only yielding governs
  
  return { 
    Mn: Math.max(Mp, 0), 
    governingLimitState: FlexuralLimitState.YIELDING 
  };
}

/**
 * Main router function for bar flexural strength calculations.
 * Routes to appropriate calculation based on bar type.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor (defaults to 1.0)
 * @param bendingAboutMajorAxis - Whether bending is about the major axis (only applies to rectangular bars)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F11
 * @throws {Error} If section type is not a supported bar type
 */
export function calculateMn_Bar(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number = 1.0,
  bendingAboutMajorAxis: boolean = true
): FlexuralStrengthResult {
  switch (section.Type) {
    case "RECT-BAR":
      return calculateMn_RectangularBar(section, Fy, Lb, Cb, bendingAboutMajorAxis);
    case "ROUND-BAR":
      return calculateMn_RoundBar(section, Fy, Lb, Cb);
    default:
      throw new Error(
        ERROR_MESSAGES.UNSUPPORTED_SECTION_TYPE(section.Type, "Bar flexural strength")
      );
  }
} 