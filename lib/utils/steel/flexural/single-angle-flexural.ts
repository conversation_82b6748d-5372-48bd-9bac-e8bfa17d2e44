/**
 * @file Single angle flexural strength calculations according to AISC 360-22 Section F10.
 * Implements F10: Single Angles - comprehensive implementation with all limit states.
 */

import {
  E,
  FlexuralLimitState,
  ERROR_MESSAGES,
  ELEMENT_TYPES,
} from "../constants";
import { Section } from "../slenderness/types";
import { FlexuralStrengthResult } from "./types";
import { getSlendernessLimits } from "../slenderness/limits";

/**
 * Interface for single angle geometric properties and βw parameter.
 */
interface SingleAngleProperties {
  /** Width of leg in compression, in. */
  b: number;
  /** Leg thickness, in. */
  t: number;
  /** βw parameter for unequal leg angles, in. (positive for short leg in compression, negative for long leg in compression, zero for equal leg) */
  betaW: number;
  /** Radius of gyration about minor principal axis, in. */
  rz: number;
  /** Gross area, in² */
  Ag: number;
}

/**
 * Calculates the βw parameter for single angles according to AISC F10.
 *
 * @param section - The cross-section properties
 * @param longLegInCompression - Whether the long leg is in compression (for unequal angles)
 * @returns The βw parameter value
 *
 * @source AISC 360-22, Section F10.2
 *
 * @example
 * ```typescript
 * const betaW = calculateBetaW(section, true);
 * ```
 */
function calculateBetaW(
  section: Section,
  longLegInCompression: boolean = false
): number {
  const { d, bf } = section;

  if (typeof d !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("d"));
  }

  // For equal leg angles, βw = 0
  if (!bf || Math.abs(d - bf) < 0.01) {
    return 0;
  }

  // For unequal leg angles
  // βw is positive with short legs in compression and negative with long legs in compression
  // Note: This is a simplified calculation - actual values are typically provided in AISC commentary
  const longLeg = Math.max(d, bf);
  const shortLeg = Math.min(d, bf);
  const ratio = shortLeg / longLeg;

  // Simplified βw calculation based on leg ratio
  // In practice, these values would come from AISC commentary tables
  const betaMagnitude = 0.5 * (1 - ratio); // Simplified approximation

  return longLegInCompression ? -betaMagnitude : betaMagnitude;
}

/**
 * Calculates elastic lateral-torsional buckling moment for major principal axis bending (F10.2(1)).
 *
 * @param section - The cross-section properties
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @param betaW - The βw parameter
 * @param rz - Radius of gyration about minor principal axis
 * @returns The elastic LTB moment Mcr
 *
 * @source AISC 360-22, Equation F10-4
 */
function calculateMcr_MajorPrincipalAxis(
  section: Section,
  Lb: number,
  Cb: number,
  betaW: number,
  rz: number
): number {
  const { A } = section;

  if (typeof A !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("A"));
  }

  // AISC Eq. F10-4: Mcr = (9*E*Ag*rz*t*Cb / 8*Lb) * sqrt(1 + 4.4*(βw*rz/Lb*t)² + 4.4*βw*rz/Lb*t)
  const t = section.tw; // Use tw as thickness for angles
  if (typeof t !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("tw (thickness)"));
  }

  const term1 = (9 * E * A * rz * t * Cb) / (8 * Lb);
  const dimensionlessRatio = (betaW * rz) / (Lb * t);
  const term2 = Math.sqrt(
    1 + 4.4 * Math.pow(dimensionlessRatio, 2) + 4.4 * dimensionlessRatio
  );

  return term1 * term2;
}

/**
 * Calculates elastic lateral-torsional buckling moment for geometric axis bending (F10.2(2)).
 *
 * @param section - The cross-section properties
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @param maxCompressionAtToe - Whether maximum compression is at the toe (true) or tension (false)
 * @returns The elastic LTB moment Mcr
 *
 * @source AISC 360-22, Equations F10-5a and F10-5b
 */
function calculateMcr_GeometricAxis(
  section: Section,
  Lb: number,
  Cb: number,
  maxCompressionAtToe: boolean,
  hasLateralRestraint: boolean = false
): number {
  const { bf } = section;
  const b = bf || section.d; // Use bf if available, otherwise d
  const t = section.tw;

  if (typeof b !== "number" || typeof t !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("b and t"));
  }

  // Base calculation using F10-5a or F10-5b
  const baseTerms = (0.58 * E * Math.pow(b, 4) * t * Cb) / Math.pow(Lb, 2);
  const ratio = (Lb * t) / Math.pow(b, 2);

  let sqrtTerm: number;
  if (maxCompressionAtToe) {
    // AISC Eq. F10-5a: sqrt(1 + 0.88*(Lb*t/b²)² - 1)
    sqrtTerm = Math.sqrt(1 + 0.88 * Math.pow(ratio, 2)) - 1;
  } else {
    // AISC Eq. F10-5b: sqrt(1 + 0.88*(Lb*t/b²)² + 1)
    sqrtTerm = Math.sqrt(1 + 0.88 * Math.pow(ratio, 2)) + 1;
  }

  let Mcr = baseTerms * sqrtTerm;

  // Apply lateral-torsional restraint factor if applicable
  if (hasLateralRestraint) {
    Mcr *= 1.25; // F10.2(2)(ii)
  }

  return Math.max(Mcr, 0);
}

/**
 * Calculates single angle lateral-torsional buckling strength according to AISC F10.2.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @param My - The yield moment capacity
 * @param bendingAboutPrincipalAxis - Whether bending is about principal axis (true) or geometric axis (false)
 * @param maxCompressionAtToe - Whether maximum compression is at the toe (for geometric axis bending)
 * @param hasLateralRestraint - Whether there is lateral-torsional restraint
 * @param longLegInCompression - Whether the long leg is in compression (for unequal angles)
 * @returns Flexural strength result for lateral-torsional buckling
 *
 * @source AISC 360-22, Section F10.2
 */
function calculateMn_SingleAngle_LateralTorsionalBuckling(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number,
  My: number,
  bendingAboutPrincipalAxis: boolean = true,
  maxCompressionAtToe: boolean = true,
  hasLateralRestraint: boolean = false,
  longLegInCompression: boolean = false
): { Mn: number; limitState: FlexuralLimitState } {
  const { A, ry } = section;

  if (typeof A !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("A"));
  }

  // Calculate Mcr based on bending axis
  let Mcr: number;

  if (bendingAboutPrincipalAxis) {
    // F10.2(1) - Major principal axis bending
    const betaW = calculateBetaW(section, longLegInCompression);
    const rz = ry || Math.sqrt(section.Iy! / A); // Use ry or calculate from Iy
    Mcr = calculateMcr_MajorPrincipalAxis(section, Lb, Cb, betaW, rz);
  } else {
    // F10.2(2) - Geometric axis bending
    Mcr = calculateMcr_GeometricAxis(
      section,
      Lb,
      Cb,
      maxCompressionAtToe,
      hasLateralRestraint
    );
  }

  const ratio = My / Mcr;

  // F10.2(a) - When My/Mcr ≤ 1.0
  if (ratio <= 1.0) {
    // AISC Eq. F10-2
    const Mn = Math.min((1.92 - 1.17 * Math.sqrt(ratio)) * My, 1.5 * My);
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING,
    };
  }

  // F10.2(b) - When My/Mcr > 1.0
  else {
    // AISC Eq. F10-3
    const Mn = (0.92 - (0.17 * Mcr) / My) * Mcr;
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING,
    };
  }
}

/**
 * Calculates single angle leg local buckling strength according to AISC F10.3.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param toeInCompression - Whether the toe of the leg is in compression
 * @returns Flexural strength result for leg local buckling
 *
 * @source AISC 360-22, Section F10.3
 */
function calculateMn_SingleAngle_LegLocalBuckling(
  section: Section,
  Fy: number,
  toeInCompression: boolean = true
): { Mn: number; limitState: FlexuralLimitState } {
  const { bf, d, tw } = section;

  // Determine which leg is in compression and its dimensions
  const b = toeInCompression ? bf || d : d; // Full width of leg in compression
  const t = tw;

  if (typeof b !== "number" || typeof t !== "number") {
    throw new Error(
      ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("leg dimensions (b, t)")
    );
  }

  // The limit state applies when the toe of the leg is in compression
  if (!toeInCompression) {
    // If toe is not in compression, leg local buckling doesn't apply
    return {
      Mn: Number.POSITIVE_INFINITY,
      limitState: FlexuralLimitState.YIELDING,
    };
  }

  const lambda = b / t;

  // Get slenderness limits for angle legs in flexure
  const limits = getSlendernessLimits(ELEMENT_TYPES.ANGLE_LEG_FLEXURE, Fy);
  const lambda_p = limits.lambda_p;
  const lambda_r = limits.lambda_r;

  // Calculate elastic section modulus to compression toe
  // For geometric axis bending with no LTB restraint, Sc = 0.80 * geometric axis section modulus
  const Sx = section.Sx || section.Zx! * 0.9; // Approximate if Sx not available
  let Sc = Sx;

  // Apply reduction for equal-leg angle geometric axis bending if applicable
  const isEqualLeg = !bf || Math.abs(d - bf) < 0.01;
  if (isEqualLeg) {
    Sc = 0.8 * Sx; // Per F10.3 note
  }

  // F10.3(a) - Compact sections
  if (lambda <= lambda_p) {
    // The limit state of leg local buckling does not apply
    return {
      Mn: Number.POSITIVE_INFINITY,
      limitState: FlexuralLimitState.YIELDING,
    };
  }

  // F10.3(b) - Noncompact legs
  else if (lambda <= lambda_r) {
    // AISC Eq. F10-6
    const Mn = Fy * Sc * (2.43 - 1.72 * (b / t) * Math.sqrt(Fy / E));
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.ANGLE_LEG_LOCAL_BUCKLING,
    };
  }

  // F10.3(c) - Slender legs
  else {
    // AISC Eq. F10-7 and F10-8
    const Fcr = (0.71 * E) / Math.pow(b / t, 2);
    const Mn = Fcr * Sc;
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.ANGLE_LEG_LOCAL_BUCKLING,
    };
  }
}

/**
 * Determines if simplified design approach can be used for single angles.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @returns Whether simplified approach (Mn = My) can be used
 *
 * @source AISC 360-22, F10 User Note
 */
function canUseSimplifiedApproach(
  section: Section,
  Fy: number,
  Lb: number
): boolean {
  const { d, bf, tw } = section;

  if (typeof d !== "number" || typeof tw !== "number") {
    return false;
  }

  const isEqualLeg = !bf || Math.abs(d - bf) < 0.01;
  if (!isEqualLeg) {
    return false; // Only applies to equal-leg angles
  }

  // Check if vertical leg toe is in compression and span-to-depth ratio criteria
  const spanToDepthLimit =
    ((1.64 * E) / Fy) * Math.pow(tw / d, 2) - 1.4 * (Fy / E);
  const actualSpanToDepthRatio = Lb / d;

  return actualSpanToDepthRatio <= spanToDepthLimit;
}

/**
 * Calculates the nominal flexural strength (Mn) for single angle members
 * according to AISC 360-22 Section F10.
 *
 * This function implements all F10 limit states:
 * - F10.1: Yielding (plastic moment = 1.5 * My)
 * - F10.2: Lateral-torsional buckling (complex calculations for different axes)
 * - F10.3: Leg local buckling
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor (defaults to 1.0)
 * @param bendingAboutPrincipalAxis - Whether bending is about principal axis (true) or geometric axis (false)
 * @param hasLateralRestraint - Whether there is continuous lateral-torsional restraint
 * @param maxCompressionAtToe - Whether maximum compression is at the toe (for geometric axis bending)
 * @param longLegInCompression - Whether the long leg is in compression (for unequal angles)
 * @returns The nominal flexural strength and governing limit state
 *
 * @source AISC 360-22, Section F10: "Single Angles"
 * @throws {Error} If required section properties are missing
 *
 * @example
 * ```typescript
 * const section = {
 *   Type: 'L',
 *   name: 'L4x4x1/2',
 *   Zx: 1.48,
 *   Sx: 1.23,
 *   A: 3.75,
 *   d: 4.0,
 *   tw: 0.5,
 *   ry: 0.78
 * };
 * const result = calculateMn_SingleAngle(section, 36, 120, 1.0);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_SingleAngle(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number = 1.0,
  bendingAboutPrincipalAxis: boolean = true,
  hasLateralRestraint: boolean = false,
  maxCompressionAtToe: boolean = true,
  longLegInCompression: boolean = false
): FlexuralStrengthResult {
  const { Sx, Zx } = section;

  // Validate required properties
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.ANGLE_SX_REQUIRED);
  }

  // Check if simplified approach can be used
  if (canUseSimplifiedApproach(section, Fy, Lb)) {
    const My = Fy * Sx;
    return { Mn: My, governingLimitState: FlexuralLimitState.YIELDING };
  }

  // F10.1 - Yielding (AISC Eq. F10-1)
  const My = Fy * Sx;
  const Mp = 1.5 * My; // For single angles without continuous lateral restraint
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;

  // For angles with continuous lateral-torsional restraint, can use geometric axis properties
  if (hasLateralRestraint) {
    // Can design on basis of geometric axis bending - use yielding only
    return { Mn: My, governingLimitState: FlexuralLimitState.YIELDING };
  }

  // F10.2 - Lateral-Torsional Buckling (for angles without continuous restraint)
  try {
    const ltbResult = calculateMn_SingleAngle_LateralTorsionalBuckling(
      section,
      Fy,
      Lb,
      Cb,
      My,
      bendingAboutPrincipalAxis,
      maxCompressionAtToe,
      hasLateralRestraint,
      longLegInCompression
    );
    if (ltbResult.Mn < Mn) {
      Mn = ltbResult.Mn;
      governingLimitState = ltbResult.limitState;
    }
  } catch (error) {
    console.warn("Single angle LTB calculation skipped:", error);
  }

  // F10.3 - Leg Local Buckling
  try {
    const legResult = calculateMn_SingleAngle_LegLocalBuckling(
      section,
      Fy,
      maxCompressionAtToe
    );
    if (legResult.Mn < Mn) {
      Mn = legResult.Mn;
      governingLimitState = legResult.limitState;
    }
  } catch (error) {
    console.warn("Single angle leg local buckling calculation skipped:", error);
  }

  return { Mn: Math.max(Mn, 0), governingLimitState };
}
