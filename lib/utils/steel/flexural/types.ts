/**
 * @file Shared types and interfaces for flexural strength calculations.
 */

import { FlexuralLimitState } from "../constants";

/**
 * Result interface for flexural strength calculations.
 */
export interface FlexuralStrengthResult {
  Mn: number; // Nominal flexural strength in kip-in
  governingLimitState: FlexuralLimitState;
}

/**
 * Interface for web plastification factors according to AISC 360-22 Section F4.
 */
export interface WebPlastificationFactors {
  Rpc: number; // Web plastification factor for compression flange local buckling
  Rpt: number; // Web plastification factor for tension flange yielding  
  Rpg: number; // Web plastification factor for slender webs
  aw: number;  // Ratio of web area to compression flange area
}

/**
 * Interface for lateral-torsional buckling parameters for I-shaped sections.
 */
export interface LTBParameters {
  Lp: number;  // Limiting laterally unbraced length for yielding
  Lr: number;  // Limiting laterally unbraced length for inelastic LTB
  rts: number; // Effective radius of gyration for LTB
} 