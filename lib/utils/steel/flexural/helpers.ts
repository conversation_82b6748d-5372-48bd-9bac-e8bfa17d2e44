/**
 * @file Shared helper functions for flexural strength calculations.
 */

import {
  E,
  ERROR_MESSAGES,
  LTB_CONSTANTS,
  FLEXURAL_CONSTANTS,
  SLENDERNESS_COEFFICIENTS,
} from "../constants";
import { Section } from "../slenderness/types";
import { WebPlastificationFactors, LTBParameters } from "./types";

/**
 * Calculates lateral-torsional buckling parameters for I-shaped sections.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns LTB parameters including Lp, Lr, and rts
 * 
 * @source AISC 360-22, Section F2.2
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { ry: 2.5, Sx: 35.0, J: 0.843, tf: 0.515, bf: 6.080, d: 8.11, tw: 0.315 };
 * const ltbParams = getLTBParameters_I_Shape(section, 50);
 * console.log(`Lp = ${ltbParams.Lp.toFixed(1)} in`);
 * ```
 */
export function getLTBParameters_I_Shape(
  section: Section,
  Fy: number
): LTBParameters {
  const { ry, Sx, J, Cw, d, tf, bf, tw } = section;

  // Validate required properties for I-shapes
  if (typeof ry !== "number") {
    throw new Error(ERROR_MESSAGES.RY_REQUIRED_LTB);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.SX_REQUIRED_LTB);
  }
  if (typeof J !== "number") {
    throw new Error(ERROR_MESSAGES.J_REQUIRED_LTB);
  }
  if (typeof tf !== "number") {
    throw new Error(ERROR_MESSAGES.TF_REQUIRED_LTB);
  }
  if (typeof bf !== "number") {
    throw new Error(ERROR_MESSAGES.BF_REQUIRED_LTB);
  }

  const ho = d - tf; // Distance between flange centroids

  // Calculate limiting laterally unbraced length for yielding
  // AISC Equation F2-5
  const Lp = LTB_CONSTANTS.LTB_LP_COEFF * ry * Math.sqrt(E / Fy);

  // Calculate effective radius of gyration for LTB, rts
  // AISC Commentary F2, Eq C-F2-5
  const rts = bf / Math.sqrt(12 * (ho / d) + (1 / 6) * ((d * tw) / (ho * tf)));

  // Calculate limiting laterally unbraced length for inelastic LTB
  // AISC Equation F2-6
  const c = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR; // For doubly symmetric I-shapes
  const FL = LTB_CONSTANTS.FL_RATIO * Fy;
  const term1 = (J * c) / (Sx * ho);
  const term2 = LTB_CONSTANTS.LTB_TERM2_COEFF * Math.pow(FL / E, 2);
  const Lr =
    LTB_CONSTANTS.LTB_LR_COEFF *
    rts *
    (E / FL) *
    Math.sqrt(term1 + Math.sqrt(Math.pow(term1, 2) + term2));

  return { Lp, Lr, rts };
}

/**
 * Calculates web plastification factors according to AISC 360-22 Section F4.
 * These factors account for the effect of web slenderness on flexural strength.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns Web plastification factors including Rpc, Rpt, Rpg, and aw
 * 
 * @source AISC 360-22, Section F4
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { d: 8.11, tf: 0.515, tw: 0.315, bf: 6.080, Sx: 35.0, Zx: 39.6 };
 * const factors = calculateWebPlastificationFactors(section, 50);
 * console.log(`Rpc = ${factors.Rpc.toFixed(3)}`);
 * ```
 */
export function calculateWebPlastificationFactors(
  section: Section,
  Fy: number
): WebPlastificationFactors {
  const { d, tf, tw, bf, Iy, Sx, Zx } = section;
  
  // Validate required properties
  if (typeof tf !== "number" || typeof tw !== "number" || typeof bf !== "number" || typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.WEB_PLASTIFICATION_PROPS_REQUIRED);
  }
  
  // Calculate basic parameters
  const h = d - 2 * tf; // Clear web height
  const lambda = h / tw; // Web slenderness
  const aw = (h * tw) / (bf * tf); // Ratio of web area to compression flange area
  
  // Web slenderness limits
  const lambda_pw = SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_P * Math.sqrt(E / Fy); // Compact limit
  const lambda_rw = SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_R * Math.sqrt(E / Fy); // Noncompact limit
  
  // Initialize factors
  let Rpc: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR;
  let Rpt: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR;
  let Rpg: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR;
  
  // Calculate Mp and My
  const Mp = Fy * (Zx || Sx); // Plastic moment
  const My = Fy * Sx; // Yield moment
  
  // Calculate Rpc (F4-9a, F4-9b, F4-10)
  if (lambda <= lambda_pw) {
    // Compact web
    Rpc = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR;
  } else if (lambda <= lambda_rw) {
    // Noncompact web - AISC Eq. F4-9a
    const calculatedRpc = Mp / My - (Mp / My - FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR) * ((lambda - lambda_pw) / (lambda_rw - lambda_pw));
    Rpc = Math.max(FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR, Math.min(calculatedRpc, Mp / My));
  } else {
    // Slender web - AISC Eq. F4-10
    const calculatedRpc = Mp / My - (Mp / My - FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR) * ((lambda - lambda_pw) / (lambda_rw - lambda_pw));
    Rpc = Math.max(FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR, calculatedRpc);
  }
  
  // Calculate Rpt (F4-16a, F4-16b, F4-17)
  // Assume Iyc ≈ Iy for rolled sections (conservative)
  const Iyc_Iy = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR; // For rolled sections - simplified assumption
  
  if (Iyc_Iy <= FLEXURAL_CONSTANTS.IYC_IY_THRESHOLD) {
    // AISC Eq. F4-17
    Rpt = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR;
  } else {
    // Check web compactness for tension flange yielding
    if (lambda <= lambda_pw) {
      // AISC Eq. F4-16a
      Rpt = Mp / My;
    } else {
      // AISC Eq. F4-16b
      const calculatedRpt = Mp / My - (Mp / My - FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR) * ((lambda - lambda_pw) / (lambda_rw - lambda_pw));
      Rpt = Math.min(calculatedRpt, Mp / My);
    }
  }
  
  // Calculate Rpg for slender webs (F5-6)
  if (lambda > lambda_rw) {
    // AISC Eq. F5-6
    const hc_tw = lambda; // Assuming hc ≈ h for simplicity
    const calculatedRpg = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR - 
          (aw / (FLEXURAL_CONSTANTS.WEB_PLASTIFICATION.DENOMINATOR_BASE + FLEXURAL_CONSTANTS.WEB_PLASTIFICATION.DENOMINATOR_COEFF * aw)) * 
          (hc_tw - SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_R * Math.sqrt(E / Fy));
    Rpg = Math.min(FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR, Math.max(FLEXURAL_CONSTANTS.DIVISION_PROTECTION, calculatedRpg)); // Bounds check
  } else {
    Rpg = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR;
  }
  
  return { Rpc, Rpt, Rpg, aw };
} 