/**
 * @file Angle flexural strength calculations according to AISC 360-22 Section F9.
 */

import {
  E,
  FlexuralLimitState,
  ERROR_MESSAGES,
  FLEXURAL_CONSTANTS,
  SLENDERNESS_COEFFICIENTS,
} from "../constants";
import { Section } from "../slenderness/types";
import { FlexuralStrengthResult } from "./types";

/**
 * Calculates the nominal flexural strength (Mn) for angle sections.
 * 
 * For angles, typically only yielding and local buckling are considered.
 * Lateral-torsional buckling is usually not critical for angles due to their geometry,
 * except for very long unbraced lengths with loading conditions that can cause twisting.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F9 - Single Angles
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { 
 *   Type: 'L', 
 *   Sx: 1.23, 
 *   Zx: 1.48, 
 *   d: 4.0, 
 *   tw: 0.375 
 * };
 * const result = calculateMn_Angle(section, 36);
 * console.log(`Mn = ${result.Mn.toFixed(1)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_Angle(
  section: Section,
  Fy: number
): FlexuralStrengthResult {
  const { Zx, Sx, d, tw } = section;

  // Validate required properties
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.ANGLE_SX_REQUIRED);
  }

  // For angles, typically only yielding and local buckling are considered
  // Lateral-torsional buckling is usually not critical for angles due to their geometry
  
  // Yielding moment
  let Mp = Fy * Sx; // Conservative - use elastic section modulus
  if (typeof Zx === "number" && Zx > 0) {
    Mp = Fy * Zx; // Use plastic section modulus if available
  }
  
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;

  // Check local buckling of angle legs (simplified approach)
  if (typeof d === "number" && typeof tw === "number") {
    // Leg slenderness ratio
    const lambda = d / tw;
    const lambda_p = SLENDERNESS_COEFFICIENTS.FLEXURE.ANGLE_LEG.LAMBDA_P * Math.sqrt(E / Fy); // From AISC Table B4.1b Case 12
    const lambda_r = SLENDERNESS_COEFFICIENTS.FLEXURE.ANGLE_LEG.LAMBDA_R * Math.sqrt(E / Fy);

    if (lambda > lambda_p) {
      // Local buckling may control
      if (lambda <= lambda_r) {
        // Noncompact - linear interpolation
        const Mn_local = Mp - (Mp - FLEXURAL_CONSTANTS.STRESS_REDUCTION_FACTOR * Fy * Sx) * ((lambda - lambda_p) / (lambda_r - lambda_p));
        if (Mn_local < Mn) {
          Mn = Mn_local;
          governingLimitState = FlexuralLimitState.ANGLE_LEG_LOCAL_BUCKLING;
        }
      } else {
        // Slender
        const Fcr = (FLEXURAL_CONSTANTS.ELASTIC_BUCKLING_COEFF * E) / Math.pow(lambda, 2);
        const Mn_local = Fcr * Sx;
        if (Mn_local < Mn) {
          Mn = Mn_local;
          governingLimitState = FlexuralLimitState.ANGLE_LEG_LOCAL_BUCKLING;
        }
      }
    }
  }

  return { Mn, governingLimitState };
} 