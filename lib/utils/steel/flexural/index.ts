/**
 * @file Main entry point for steel flexural strength calculations.
 * Routes calculations to appropriate section-specific modules.
 */

import {
  Material,
  ERROR_MESSAGES,
  FLEXURAL_CONSTANTS,
  SECTION_TYPE_STRINGS,
} from "../constants";
import { Section } from "../slenderness/types";
import { FlexuralStrengthResult } from "./types";
import { calculateMn_I_Shape } from "./i-shape-flexural";
import { calculateMn_RectHSS, calculateMn_RoundHSS } from "./hss-flexural";
import { calculateMn_Angle } from "./angle-flexural";
import { calculateMn_SingleAngle } from "./single-angle-flexural";
import { calculateMn_Tee } from "./tee-flexural";
import { calculateMn_Bar, calculateMn_RectangularBar, calculateMn_RoundBar } from "./bar-flexural";

// Re-export types and individual calculation functions
export type { FlexuralStrengthResult, WebPlastificationFactors, LTBParameters } from "./types";
export { getLTBParameters_I_Shape, calculateWebPlastificationFactors } from "./helpers";
export { 
  calculateMn_F4_DoublySymmetric, 
  calculateMn_F5_SlenderWebs, 
  calculateMn_F6_MinorAxis, 
  calculateMn_I_Shape 
} from "./i-shape-flexural";
export { 
  calculateMn_RectHSS,
  calculateMn_RoundHSS 
} from "./hss-flexural";
export { calculateMn_Angle } from "./angle-flexural";
export { calculateMn_SingleAngle } from "./single-angle-flexural";
export { calculateMn_Tee } from "./tee-flexural";
export { calculateMn_Bar, calculateMn_RectangularBar, calculateMn_RoundBar } from "./bar-flexural";

/**
 * Router function to calculate the nominal flexural strength (Mn) for a given shape.
 * This is the main entry point for flexural strength calculations across all section types.
 * 
 * @param section - The cross-section properties
 * @param material - The material properties
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor (defaults to 1.0)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Chapter F
 * @throws {Error} If the section type is not supported, unbraced length is negative, or Cb < 1.0
 * 
 * @example
 * ```typescript
 * // W-shape major axis bending
 * const wSection = { Type: 'W', Zx: 39.6, Sx: 35.0, d: 8.11, ... };
 * const steel = { Fy: 50, Fu: 65 };
 * const result = calculateNominalFlexuralStrength(wSection, steel, 120, 1.0);
 * 
 * // HSS rectangular section
 * const hssSection = { Type: 'HSS-RECT', Zx: 12.5, Sx: 10.8, ... };
 * const hssResult = calculateNominalFlexuralStrength(hssSection, steel, 96, 1.0);
 * 
 * // Single angle section (F10)
 * const angleSection = { Type: 'L', Sx: 1.23, A: 3.75, d: 4.0, tw: 0.5, ry: 0.78 };
 * const angleResult = calculateNominalFlexuralStrength(angleSection, steel, 72, 1.0);
 * 
 * // Rectangular bar section (F11)
 * const rectBarSection = { Type: 'RECT-BAR', Sx: 1.0, Zx: 1.5, d: 3.0, tw: 1.0 };
 * const rectBarResult = calculateNominalFlexuralStrength(rectBarSection, steel, 120, 1.0);
 * ```
 */
export function calculateNominalFlexuralStrength(
  section: Section,
  material: Material,
  Lb: number,
  Cb: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR
): FlexuralStrengthResult {
  if (Lb < 0) {
    throw new Error(ERROR_MESSAGES.UNBRACED_LENGTH_NON_NEGATIVE);
  }
  if (Cb < FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR) {
    throw new Error(ERROR_MESSAGES.CB_LESS_THAN_ONE);
  }

  const { Fy } = material;

  switch (section.Type) {
    case SECTION_TYPE_STRINGS.W:
    case SECTION_TYPE_STRINGS.I:
    case SECTION_TYPE_STRINGS.S:
    case SECTION_TYPE_STRINGS.M:
    case SECTION_TYPE_STRINGS.C:
    case SECTION_TYPE_STRINGS.MC:
      return calculateMn_I_Shape(section, Fy, Lb, Cb);
    case SECTION_TYPE_STRINGS.HSS_RECT:
      return calculateMn_RectHSS(section, Fy, Lb);
    case SECTION_TYPE_STRINGS.HSS_ROUND:
      return calculateMn_RoundHSS(section, Fy, Lb);
    case SECTION_TYPE_STRINGS.L:
      // F10: Single Angles - comprehensive AISC 360-22 implementation
      return calculateMn_SingleAngle(section, Fy, Lb, Cb);
    case SECTION_TYPE_STRINGS.DOUBLE_L:
      // For double angles, use the basic angle implementation for now
      return calculateMn_Angle(section, Fy);
    case SECTION_TYPE_STRINGS.TEE:
    case SECTION_TYPE_STRINGS.WT:
      // For tees, default to stem in tension - in practice this would be determined by load case
      return calculateMn_Tee(section, Fy, Lb, true, false);
    case SECTION_TYPE_STRINGS.RECT_BAR:
    case SECTION_TYPE_STRINGS.ROUND_BAR:
      // F11: Rectangular Bars and Rounds
      return calculateMn_Bar(section, Fy, Lb, Cb);
    default:
      throw new Error(
        ERROR_MESSAGES.UNSUPPORTED_SECTION_TYPE(section.Type, "Flexural strength")
      );
  }
} 