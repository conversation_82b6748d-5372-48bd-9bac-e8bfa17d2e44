/**
 * @file Tee and double angle flexural strength calculations according to AISC 360-22 Section F9.
 * Implements F9: Te<PERSON> and Double Angles Loaded in the Plane of Symmetry.
 */

import {
  E,
  FlexuralLimitState,
  ERROR_MESSAGES,
  ELEMENT_TYPES,
} from "../constants";
import { Section } from "../slenderness/types";
import { FlexuralStrengthResult } from "./types";
import { getSlendernessLimits } from "../slenderness/limits";

/**
 * Calculates plastic moment capacity for tees and double angles according to AISC F9.1.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param stemInTension - Whether the tee stem/web legs are in tension (true) or compression (false)
 * @param isDoubleAngle - Whether this is a double angle (true) or tee (false)
 * @returns The plastic moment capacity
 *
 * @source AISC 360-22, Section F9.1
 *
 * @example
 * ```typescript
 * const Mp = calculateMp_Tee(section, 50, true, false);
 * console.log(`Mp = ${Mp.toFixed(0)} kip-in`);
 * ```
 */
function calculateMp_Tee(
  section: Section,
  Fy: number,
  stemInTension: boolean,
  isDoubleAngle: boolean = false
): number {
  const { Zx, Sx } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.FLEXURAL_ZX_REQUIRED);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }

  const My = Fy * Sx; // AISC Eq. F9-3

  if (stemInTension) {
    if (isDoubleAngle) {
      // F9.1(a) - For tee stems and web legs in tension
      // Mp = Fy * Zx ≤ 1.6 * My (AISC Eq. F9-2)
      return Math.min(Fy * Zx, 1.6 * My);
    } else {
      // F9.1(a) - For tee stems in tension
      return Math.min(Fy * Zx, 1.6 * My);
    }
  } else {
    if (isDoubleAngle) {
      // F9.1(c) - For double angles with web legs in compression
      // Mp = 1.5 * My (AISC Eq. F9-5)
      return 1.5 * My;
    } else {
      // F9.1(b) - For tee stems in compression
      // Mp = My (AISC Eq. F9-4)
      return My;
    }
  }
}

/**
 * Calculates lateral-torsional buckling parameters for tees and double angles.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param stemInTension - Whether the tee stem/web legs are in tension
 * @returns Object containing Lp, Lr, and other LTB parameters
 *
 * @source AISC 360-22, Section F9.2
 */
function calculateLTBParameters_Tee(
  section: Section,
  Fy: number,
  stemInTension: boolean
): { Lp: number; Lr: number; B: number } {
  const { ry, J, Sx, d } = section;

  // Validate required properties
  if (typeof ry !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("ry"));
  }
  if (typeof J !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("J"));
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }
  if (typeof d !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("d"));
  }

  // AISC Eq. F9-8: Lp = 1.76 * ry * sqrt(E / Fy)
  const Lp = 1.76 * ry * Math.sqrt(E / Fy);

  // Calculate B factor (AISC Eq. F9-11 for tension, F9-12 for compression)
  const Iy_over_J = Math.sqrt((section.Iy || 0) / J);
  let B: number;

  if (stemInTension) {
    // AISC Eq. F9-11: B = 2.3 * (d / Lb) * sqrt(Iy / J)
    B = 2.3 * Iy_over_J; // Note: d/Lb ratio will be applied later
  } else {
    // AISC Eq. F9-12: B = -2.3 * (d / Lb) * sqrt(Iy / J)
    B = -2.3 * Iy_over_J; // Note: d/Lb ratio will be applied later
  }

  // AISC Eq. F9-9: Lr = 1.95 * (E / Fy) * sqrt(Iy * J / Sx) * sqrt(2.36 * (Fy / E) * (d * Sx / J) + 1)
  const term1 = 1.95 * (E / Fy);
  const term2 = Math.sqrt(((section.Iy || 0) * J) / Sx);
  const term3 = Math.sqrt(2.36 * (Fy / E) * ((d * Sx) / J) + 1);
  const Lr = term1 * term2 * term3;

  return { Lp, Lr, B };
}

/**
 * Calculates tee lateral-torsional buckling strength according to AISC F9.2.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Mp - The plastic moment capacity
 * @param My - The yield moment capacity
 * @param stemInTension - Whether the tee stem/web legs are in tension
 * @returns Flexural strength result for lateral-torsional buckling
 *
 * @source AISC 360-22, Section F9.2
 */
function calculateMn_Tee_LateralTorsionalBuckling(
  section: Section,
  Fy: number,
  Lb: number,
  Mp: number,
  My: number,
  stemInTension: boolean
): { Mn: number; limitState: FlexuralLimitState } {
  const { d, J } = section;

  if (typeof d !== "number" || typeof J !== "number") {
    throw new Error("Section must have d and J properties for LTB calculation");
  }

  const ltbParams = calculateLTBParameters_Tee(section, Fy, stemInTension);
  const { Lp, Lr, B } = ltbParams;

  // F9.2(a) - When Lb ≤ Lp
  if (Lb <= Lp) {
    // The limit state of lateral-torsional buckling does not apply
    return { Mn: Mp, limitState: FlexuralLimitState.YIELDING };
  }

  // F9.2(a)(2) - When Lp < Lb ≤ Lr (Inelastic LTB)
  else if (Lb <= Lr) {
    // AISC Eq. F9-6: Mn = Mp - (Mp - My) * (Lb - Lp) / (Lr - Lp)
    const Mn = Mp - (Mp - My) * ((Lb - Lp) / (Lr - Lp));
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING,
    };
  }

  // F9.2(a)(3) - When Lb > Lr (Elastic LTB)
  else {
    // AISC Eq. F9-7: Mn = Mcr
    // AISC Eq. F9-10: Mcr = (1.95 * E / Lb) * sqrt(Iy * J) * (B + sqrt(1 + B²))
    const B_with_ratio = B * (d / Lb); // Apply the d/Lb ratio to B factor
    const sqrt_IyJ = Math.sqrt((section.Iy || 0) * J);
    const Mcr =
      ((1.95 * E) / Lb) *
      sqrt_IyJ *
      (B_with_ratio + Math.sqrt(1 + Math.pow(B_with_ratio, 2)));

    return {
      Mn: Math.max(Mcr, 0),
      limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING,
    };
  }
}

/**
 * Calculates tee flange local buckling strength according to AISC F9.3.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Mp - The plastic moment capacity
 * @returns Flexural strength result for flange local buckling
 *
 * @source AISC 360-22, Section F9.3
 */
function calculateMn_Tee_FlangeLocalBuckling(
  section: Section,
  Fy: number,
  Mp: number
): { Mn: number; limitState: FlexuralLimitState } {
  const { bf, tf, Sx } = section;

  // Validate required properties
  if (
    typeof bf !== "number" ||
    typeof tf !== "number" ||
    typeof Sx !== "number"
  ) {
    throw new Error(
      "Section must have bf, tf, and Sx properties for flange local buckling calculation"
    );
  }

  // Calculate flange slenderness for tees
  const lambda = bf / (2 * tf); // Half-width to thickness ratio for tees

  // Get slenderness limits for tee flanges
  const limits = getSlendernessLimits(ELEMENT_TYPES.TEE_FLANGE_FLEXURE, Fy);
  const lambda_pf = limits.lambda_p;
  const lambda_rf = limits.lambda_r;

  // F9.3(a)(1) - Compact flange
  if (lambda <= lambda_pf) {
    // The limit state of flange local buckling does not apply
    return { Mn: Mp, limitState: FlexuralLimitState.YIELDING };
  }

  // F9.3(a)(2) - Noncompact flange
  else if (lambda <= lambda_rf) {
    // AISC Eq. F9-14: Mn = [Mp - (Mp - 0.7 * Fy * Sxc) * (λ - λpf) / (λrf - λpf)] ≤ 1.6 * My
    const My = Fy * Sx;
    const Sxc = Sx; // Elastic section modulus referred to compression flange
    const Mn = Math.min(
      Mp -
        (Mp - 0.7 * Fy * Sxc) *
          ((lambda - lambda_pf) / (lambda_rf - lambda_pf)),
      1.6 * My
    );
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.FLANGE_LOCAL_BUCKLING,
    };
  }

  // F9.3(a)(3) - Slender flange
  else {
    // AISC Eq. F9-15: Mn = 0.7 * E * Sxc / (bf / 2tf)²
    const Sxc = Sx; // Elastic section modulus referred to compression flange
    const Mn = (0.7 * E * Sxc) / Math.pow(lambda, 2);
    return {
      Mn: Math.max(Mn, 0),
      limitState: FlexuralLimitState.FLANGE_LOCAL_BUCKLING,
    };
  }
}

/**
 * Calculates tee stem local buckling strength according to AISC F9.4.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns Flexural strength result for stem local buckling
 *
 * @source AISC 360-22, Section F9.4
 */
function calculateMn_Tee_StemLocalBuckling(
  section: Section,
  Fy: number
): { Mn: number; limitState: FlexuralLimitState } {
  const { d, tw, Sx } = section;

  // Validate required properties
  if (
    typeof d !== "number" ||
    typeof tw !== "number" ||
    typeof Sx !== "number"
  ) {
    throw new Error(
      "Section must have d, tw, and Sx properties for stem local buckling calculation"
    );
  }

  // Calculate stem slenderness
  const lambda = d / tw;

  // Calculate critical stress Fcr based on slenderness ranges
  let Fcr: number;

  // AISC Eq. F9-17, F9-18, F9-19
  const limit1 = 0.84 * Math.sqrt(E / Fy);
  const limit2 = 1.52 * Math.sqrt(E / Fy);

  if (lambda <= limit1) {
    // F9.4(a)(1): Fcr = Fy
    Fcr = Fy;
  } else if (lambda <= limit2) {
    // F9.4(a)(2): Fcr = [1.43 - 0.515 * (d/tw) * sqrt(Fy/E)] * Fy
    Fcr = (1.43 - 0.515 * lambda * Math.sqrt(Fy / E)) * Fy;
  } else {
    // F9.4(a)(3): Fcr = 1.52 * E / (d/tw)²
    Fcr = (1.52 * E) / Math.pow(lambda, 2);
  }

  // AISC Eq. F9-16: Mn = Fcr * Sx
  const Mn = Fcr * Sx;

  return {
    Mn: Math.max(Mn, 0),
    limitState: FlexuralLimitState.WEB_LOCAL_BUCKLING,
  };
}

/**
 * Calculates the nominal flexural strength (Mn) for tee and double angle members
 * according to AISC 360-22 Section F9.
 *
 * This function implements all F9 limit states:
 * - F9.1: Yielding (plastic moment with various factors)
 * - F9.2: Lateral-torsional buckling
 * - F9.3: Flange local buckling
 * - F9.4: Local buckling of stems and web legs
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param stemInTension - Whether the tee stem/web legs are in tension (true) or compression (false)
 * @param isDoubleAngle - Whether this is a double angle (true) or tee (false, defaults to false)
 * @returns The nominal flexural strength and governing limit state
 *
 * @source AISC 360-22, Section F9: "Tees and Double Angles Loaded in the Plane of Symmetry"
 * @throws {Error} If required section properties are missing
 *
 * @example
 * ```typescript
 * const section = {
 *   Type: 'TEE',
 *   name: 'WT18x97',
 *   Zx: 78.4,
 *   Sx: 70.9,
 *   A: 28.5,
 *   d: 17.96,
 *   tw: 0.655,
 *   bf: 11.145,
 *   tf: 1.02,
 *   ry: 2.82,
 *   J: 4.49,
 *   Iy: 228
 * };
 * const result = calculateMn_Tee(section, 50, 120, true, false);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_Tee(
  section: Section,
  Fy: number,
  Lb: number,
  stemInTension: boolean,
  isDoubleAngle: boolean = false
): FlexuralStrengthResult {
  const { Zx, Sx } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.FLEXURAL_ZX_REQUIRED);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }

  // F9.1 - Yielding (Calculate plastic moment capacity)
  const Mp = calculateMp_Tee(section, Fy, stemInTension, isDoubleAngle);
  const My = Fy * Sx;
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;

  // F9.2 - Lateral-Torsional Buckling
  try {
    const ltbResult = calculateMn_Tee_LateralTorsionalBuckling(
      section,
      Fy,
      Lb,
      Mp,
      My,
      stemInTension
    );
    if (ltbResult.Mn < Mn) {
      Mn = ltbResult.Mn;
      governingLimitState = ltbResult.limitState;
    }
  } catch (error) {
    console.warn("Tee LTB calculation skipped:", error);
  }

  // F9.3 - Flange Local Buckling (for tees only)
  if (!isDoubleAngle) {
    try {
      const flangeResult = calculateMn_Tee_FlangeLocalBuckling(section, Fy, Mp);
      if (flangeResult.Mn < Mn) {
        Mn = flangeResult.Mn;
        governingLimitState = flangeResult.limitState;
      }
    } catch (error) {
      console.warn("Tee flange local buckling calculation skipped:", error);
    }
  }

  // F9.4 - Stem/Web Leg Local Buckling
  try {
    const stemResult = calculateMn_Tee_StemLocalBuckling(section, Fy);
    if (stemResult.Mn < Mn) {
      Mn = stemResult.Mn;
      governingLimitState = stemResult.limitState;
    }
  } catch (error) {
    console.warn("Tee stem local buckling calculation skipped:", error);
  }

  return { Mn: Math.max(Mn, 0), governingLimitState };
}
