/**
 * @file HSS flexural strength calculations according to AISC 360-22 Sections F7 and F8.
 * Implements F7: Square and Rectangular HSS and Box Sections.
 * Implements F8: Round HSS.
 */

import {
  E,
  FlexuralLimitState,
  ERROR_MESSAGES,
  ELEMENT_TYPES,
} from "../constants";
import { Section } from "../slenderness/types";
import { FlexuralStrengthResult } from "./types";
import { getSlendernessLimits } from "../slenderness/limits";

/**
 * Calculates the effective section modulus for slender HSS flanges according to AISC F7.2(c).
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param isHSS - Whether this is an HSS (true) or box section (false)
 * @returns The effective section modulus in in³
 * 
 * @source AISC 360-22, Equations F7-4 and F7-5
 * 
 * @example
 * ```typescript
 * const Se = calculateEffectiveSectionModulus_HSS_Flange(section, 46, true);
 * ```
 */
function calculateEffectiveSectionModulus_HSS_Flange(
  section: Section,
  Fy: number,
  isHSS: boolean = true
): number {
  const { bf, tf, Sx } = section;

  if (typeof bf !== "number" || typeof tf !== "number" || typeof Sx !== "number") {
    throw new Error("Section must have bf, tf, and Sx properties for effective section modulus calculation");
  }

  // Calculate slenderness ratio
  const lambda = bf / tf;

  // Calculate effective width using appropriate equation
  let be: number;
  if (isHSS) {
    // AISC Eq. F7-4 for HSS
    const factor = 0.38 * Math.sqrt(E / Fy);
    be = Math.min(1.92 * tf * Math.sqrt(E / Fy) * (1 - factor / lambda), bf);
  } else {
    // AISC Eq. F7-5 for box sections
    const factor = 0.34 * Math.sqrt(E / Fy);
    be = Math.min(1.92 * tf * Math.sqrt(E / Fy) * (1 - factor / lambda), bf);
  }

  // Calculate effective section modulus (simplified approach)
  // In practice, this requires detailed analysis of the cross-section
  // For now, use a conservative reduction factor based on effective width ratio
  const effectiveWidthRatio = be / bf;
  return Sx * effectiveWidthRatio;
}

/**
 * Calculates HSS flange local buckling strength according to AISC F7.2.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param isHSS - Whether this is an HSS (true) or box section (false)
 * @returns Flexural strength result for flange local buckling
 * 
 * @source AISC 360-22, Section F7.2
 * 
 * @example
 * ```typescript
 * const result = calculateMn_HSS_FlangeLocalBuckling(section, 46, true);
 * console.log(`FLB Mn = ${result.Mn.toFixed(0)} kip-in`);
 * ```
 */
function calculateMn_HSS_FlangeLocalBuckling(
  section: Section,
  Fy: number,
  isHSS: boolean = true
): { Mn: number; limitState: FlexuralLimitState } {
  const { Zx, Sx, bf, tf } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.HSS_FLB_ZX_REQUIRED);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.HSS_FLB_SX_REQUIRED);
  }
  if (typeof bf !== "number") {
    throw new Error(ERROR_MESSAGES.HSS_FLB_BF_REQUIRED);
  }
  if (typeof tf !== "number") {
    throw new Error(ERROR_MESSAGES.HSS_FLB_TF_REQUIRED);
  }

  const Mp = Fy * Zx;

  // Calculate slenderness ratio for flanges
  const lambda = bf / tf;

  // Get slenderness limits for HSS flanges in flexure
  const limits = getSlendernessLimits(ELEMENT_TYPES.HSS_RECT_FLEXURE, Fy);
  const lambda_pf = limits.lambda_p;
  const lambda_rf = limits.lambda_r;

  // F7.2(a) - Compact sections
  if (lambda <= lambda_pf) {
    // The limit state of flange local buckling does not apply
    return { Mn: Mp, limitState: FlexuralLimitState.YIELDING };
  }
  
  // F7.2(b) - Noncompact flanges
  else if (lambda <= lambda_rf) {
    // AISC Eq. F7-2
    const Mn = Math.min(
      Mp - (Mp - Fy * Sx) * ((lambda - lambda_pf) / (lambda_rf - lambda_pf)),
      Mp
    );
    return { Mn, limitState: FlexuralLimitState.HSS_FLANGE_LOCAL_BUCKLING };
  }
  
  // F7.2(c) - Slender flanges
  else {
    // AISC Eq. F7-3
    const Se = calculateEffectiveSectionModulus_HSS_Flange(section, Fy, isHSS);
    const Mn = Fy * Se;
    return { Mn, limitState: FlexuralLimitState.HSS_FLANGE_LOCAL_BUCKLING };
  }
}

/**
 * Calculates HSS web local buckling strength according to AISC F7.3.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns Flexural strength result for web local buckling
 * 
 * @source AISC 360-22, Section F7.3
 * 
 * @example
 * ```typescript
 * const result = calculateMn_HSS_WebLocalBuckling(section, 46);
 * console.log(`WLB Mn = ${result.Mn.toFixed(0)} kip-in`);
 * ```
 */
function calculateMn_HSS_WebLocalBuckling(
  section: Section,
  Fy: number
): { Mn: number; limitState: FlexuralLimitState } {
  const { Zx, Sx, d, tw } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.HSS_WLB_ZX_REQUIRED);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.HSS_WLB_SX_REQUIRED);
  }
  if (typeof d !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("d"));
  }
  if (typeof tw !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("tw"));
  }

  const Mp = Fy * Zx;

  // Calculate slenderness ratio for web
  const h = d; // For HSS, h = d (depth of web)
  const lambda = h / tw;

  // Get slenderness limits for HSS webs in flexure
  const limits = getSlendernessLimits(ELEMENT_TYPES.HSS_WEB_FLEXURE, Fy);
  const lambda_pw = limits.lambda_p;
  const lambda_rw = limits.lambda_r;

  // F7.3(a) - Compact sections
  if (lambda <= lambda_pw) {
    // The limit state of web local buckling does not apply
    return { Mn: Mp, limitState: FlexuralLimitState.YIELDING };
  }
  
  // F7.3(b) - Noncompact webs
  else if (lambda <= lambda_rw) {
    // AISC Eq. F7-6
    const Mn = Math.min(
      Mp - (Mp - Fy * Sx) * ((lambda - lambda_pw) / (lambda_rw - lambda_pw)),
      Mp
    );
    return { Mn, limitState: FlexuralLimitState.HSS_WEB_LOCAL_BUCKLING };
  }
  
  // F7.3(c) - Slender webs with compact or noncompact flanges
  else {
    // AISC Eq. F7-7: Mn = Rpg * Fy * S
    // Note: User Note states "There are no HSS with slender webs"
    // This case is included for completeness but may not occur in practice
    
    // Rpg is defined by Equation F5-6 with aw = 2*h*tw/(bt*f)
    // For simplicity, using a conservative approach
    const Rpg = 1.0; // Conservative assumption, would need detailed calculation per F5-6
    const Mn = Rpg * Fy * Sx;
    return { Mn, limitState: FlexuralLimitState.HSS_WEB_LOCAL_BUCKLING };
  }
}

/**
 * Calculates HSS lateral-torsional buckling strength according to AISC F7.4.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor
 * @returns Flexural strength result for lateral-torsional buckling
 * 
 * @source AISC 360-22, Section F7.4
 * 
 * @example
 * ```typescript
 * const result = calculateMn_HSS_LateralTorsionalBuckling(section, 46, 120, 1.0);
 * console.log(`LTB Mn = ${result.Mn.toFixed(0)} kip-in`);
 * ```
 */
function calculateMn_HSS_LateralTorsionalBuckling(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number = 1.0
): { Mn: number; limitState: FlexuralLimitState } {
  const { Zx, Sx, A, ry, J } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.FLEXURAL_ZX_REQUIRED);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }
  if (typeof A !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("A"));
  }
  if (typeof ry !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("ry"));
  }

  const Mp = Fy * Zx;

  // Calculate limiting unbraced lengths
  // AISC Eq. F7-10: Lp = 0.13 * E * ry * sqrt(J * A / Mp)
  const JA = (J || 0) * A;
  const Lp = 0.13 * E * ry * Math.sqrt(JA / Mp);

  // AISC Eq. F7-11: Lr = 2 * E * ry * sqrt(J * A) / (0.7 * Fy * Sx)
  const Lr = (2 * E * ry * Math.sqrt(JA)) / (0.7 * Fy * Sx);

  // F7.4(a) - When Lb ≤ Lp
  if (Lb <= Lp) {
    // The limit state of lateral-torsional buckling does not apply
    return { Mn: Mp, limitState: FlexuralLimitState.YIELDING };
  }
  
  // F7.4(b) - When Lp < Lb ≤ Lr (Inelastic LTB)
  else if (Lb <= Lr) {
    // AISC Eq. F7-8
    const Mn = Math.min(
      Cb * (Mp - (Mp - 0.7 * Fy * Sx) * ((Lb - Lp) / (Lr - Lp))),
      Mp
    );
    return { Mn, limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING };
  }
  
  // F7.4(c) - When Lb > Lr (Elastic LTB)
  else {
    // AISC Eq. F7-9
    const Mn = Math.min(
      (2 * E * Cb * Math.sqrt(JA)) / (Lb / ry),
      Mp
    );
    return { Mn, limitState: FlexuralLimitState.LATERAL_TORSIONAL_BUCKLING };
  }
}

/**
 * Calculates the nominal flexural strength (Mn) for rectangular HSS members
 * according to AISC 360-22 Section F7.
 * 
 * This function implements all F7 limit states:
 * - F7.1: Yielding (plastic moment)
 * - F7.2: Flange local buckling
 * - F7.3: Web local buckling  
 * - F7.4: Lateral-torsional buckling
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches
 * @param Cb - The lateral-torsional buckling modification factor (defaults to 1.0)
 * @param isHSS - Whether this is an HSS (true) or box section (false, defaults to true)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F7: "Square and Rectangular HSS and Box Sections"
 * @throws {Error} If required section properties are missing
 * 
 * @example
 * ```typescript
 * const section = { 
 *   Type: 'HSS-RECT', 
 *   name: 'HSS6x4x1/4',
 *   Zx: 12.5, 
 *   Sx: 10.8, 
 *   A: 4.75,
 *   bf: 4.0, 
 *   tf: 0.233, 
 *   d: 6.0, 
 *   tw: 0.233,
 *   ry: 1.39,
 *   J: 17.5
 * };
 * const result = calculateMn_RectHSS(section, 46, 120, 1.0);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_RectHSS(
  section: Section,
  Fy: number,
  Lb: number,
  Cb: number = 1.0,
  isHSS: boolean = true
): FlexuralStrengthResult {
  const { Zx } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.FLEXURAL_ZX_REQUIRED);
  }

  // F7.1 - Yielding (AISC Eq. F7-1)
  const Mp = Fy * Zx;
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;

  // F7.2 - Flange Local Buckling
  const flangeResult = calculateMn_HSS_FlangeLocalBuckling(section, Fy, isHSS);
  if (flangeResult.Mn < Mn) {
    Mn = flangeResult.Mn;
    governingLimitState = flangeResult.limitState;
  }

  // F7.3 - Web Local Buckling
  const webResult = calculateMn_HSS_WebLocalBuckling(section, Fy);
  if (webResult.Mn < Mn) {
    Mn = webResult.Mn;
    governingLimitState = webResult.limitState;
  }

  // F7.4 - Lateral-Torsional Buckling
  // Note: Per AISC User Note, LTB will not occur in square sections or sections 
  // bending about their minor axis. In HSS sizes, deflection will usually control 
  // before there is a significant reduction in flexural strength due to LTB.
  // Only check LTB if section has required properties and is not square
  if (typeof section.ry === "number" && section.ry > 0 && 
      typeof section.A === "number" && section.A > 0 &&
      typeof section.bf === "number" && typeof section.d === "number" &&
      section.bf !== section.d) {  // Skip for square sections
    try {
      const ltbResult = calculateMn_HSS_LateralTorsionalBuckling(section, Fy, Lb, Cb);
      if (ltbResult.Mn < Mn) {
        Mn = ltbResult.Mn;
        governingLimitState = ltbResult.limitState;
      }
    } catch (error) {
      // If LTB calculation fails, continue without it (common for HSS)
      console.warn("HSS LTB calculation skipped:", error);
    }
  }

  return { Mn, governingLimitState };
}

/**
 * Calculates Round HSS local buckling strength according to AISC F8.2.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns Flexural strength result for local buckling
 * 
 * @source AISC 360-22, Section F8.2
 * 
 * @example
 * ```typescript
 * const result = calculateMn_RoundHSS_LocalBuckling(section, 46);
 * console.log(`Round HSS LB Mn = ${result.Mn.toFixed(0)} kip-in`);
 * ```
 */
function calculateMn_RoundHSS_LocalBuckling(
  section: Section,
  Fy: number
): { Mn: number; limitState: FlexuralLimitState } {
  const { Zx, Sx, D, t } = section;
  
  // For round HSS, use D and t if available, otherwise use d and tw
  const diameter = D || section.d;
  const thickness = t || section.tw;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.FLEXURAL_ZX_REQUIRED);
  }
  if (typeof Sx !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("Sx"));
  }
  if (typeof diameter !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("diameter (D or d)"));
  }
  if (typeof thickness !== "number") {
    throw new Error(ERROR_MESSAGES.NUMERIC_PROPERTY_REQUIRED("thickness (t or tw)"));
  }

  const Mp = Fy * Zx;

  // Calculate D/t ratio
  const DoverT = diameter / thickness;

  // Check if section is applicable for F8 (D/t < 0.45E/Fy)
  const limitRatio = (0.45 * E) / Fy;
  if (DoverT >= limitRatio) {
    throw new Error(`Round HSS section with D/t = ${DoverT.toFixed(1)} exceeds F8 limit of ${limitRatio.toFixed(1)}`);
  }

  // Get slenderness limits for round HSS
  const limits = getSlendernessLimits(ELEMENT_TYPES.HSS_ROUND_FLEXURE, Fy);
  const lambda_p = limits.lambda_p;
  const lambda_r = limits.lambda_r;

  // F8.2(a) - Compact sections
  if (DoverT <= lambda_p) {
    // The limit state of local buckling does not apply
    return { Mn: Mp, limitState: FlexuralLimitState.YIELDING };
  }
  
  // F8.2(b) - Noncompact sections
  else if (DoverT <= lambda_r) {
    // AISC Eq. F8-2
    const Mn = (0.021 * E / DoverT + Fy) * Sx;
    return { Mn: Math.min(Mn, Mp), limitState: FlexuralLimitState.HSS_ROUND_LOCAL_BUCKLING };
  }
  
  // F8.2(c) - Slender walls
  else {
    // AISC Eq. F8-3 and F8-4
    const Fcr = (0.33 * E) / DoverT;
    const Mn = Fcr * Sx;
    return { Mn, limitState: FlexuralLimitState.HSS_ROUND_LOCAL_BUCKLING };
  }
}

/**
 * Calculates the nominal flexural strength (Mn) for round HSS members
 * according to AISC 360-22 Section F8.
 * 
 * This function implements all F8 limit states:
 * - F8.1: Yielding (plastic moment)
 * - F8.2: Local buckling
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param Lb - The unbraced length in inches (not typically used for round HSS)
 * @returns The nominal flexural strength and governing limit state
 * 
 * @source AISC 360-22, Section F8: "Round HSS"
 * @throws {Error} If required section properties are missing or D/t ratio exceeds F8 limits
 * 
 * @example
 * ```typescript
 * const section = { 
 *   Type: 'HSS-ROUND', 
 *   name: 'HSS6.625x0.280',
 *   Zx: 5.82, 
 *   Sx: 4.47, 
 *   A: 5.38,
 *   D: 6.625, 
 *   t: 0.280
 * };
 * const result = calculateMn_RoundHSS(section, 46, 120);
 * console.log(`Mn = ${result.Mn.toFixed(0)} kip-in, ${result.governingLimitState}`);
 * ```
 */
export function calculateMn_RoundHSS(
  section: Section,
  Fy: number,
  Lb: number
): FlexuralStrengthResult {
  const { Zx } = section;

  // Validate required properties
  if (typeof Zx !== "number") {
    throw new Error(ERROR_MESSAGES.FLEXURAL_ZX_REQUIRED);
  }

  // F8.1 - Yielding (AISC Eq. F8-1)
  const Mp = Fy * Zx;
  let Mn = Mp;
  let governingLimitState = FlexuralLimitState.YIELDING;

  // F8.2 - Local Buckling
  const localBucklingResult = calculateMn_RoundHSS_LocalBuckling(section, Fy);
  if (localBucklingResult.Mn < Mn) {
    Mn = localBucklingResult.Mn;
    governingLimitState = localBucklingResult.limitState;
  }

  return { Mn, governingLimitState };
} 