/**
 * @file Steel Design Constants and Enums
 * This file contains all common constants, enums, interfaces, and lookup tables
 * used across the steel design utility modules according to AISC 360-22.
 */

// =============================================================================
// PHYSICAL CONSTANTS
// =============================================================================

/**
 * Modulus of Elasticity for steel in ksi
 * @source AISC 360-22
 */
export const E = 29000;

/**
 * Shear Modulus for steel in ksi  
 * @source AISC 360-22
 */
export const G = 11200;

// =============================================================================
// AISC DESIGN COEFFICIENTS AND FACTORS
// =============================================================================

/**
 * Lateral-Torsional Buckling (LTB) Constants
 * @source AISC 360-22, Section F2
 */
export const LTB_CONSTANTS = {
  /** Coefficient for compact length limit Lp calculation - AISC Eq. F2-5 */
  LTB_LP_COEFF: 1.76,
  
  /** Stress ratio for lateral-torsional buckling - AISC F4-6a and F4-6b */
  FL_RATIO: 0.7,
  
  /** Coefficient for inelastic LTB range - AISC Eq. F2-6 */
  LTB_LR_COEFF: 1.95,
  
  /** Coefficient for LTB critical stress calculation - AISC Eq. F2-6 */
  LTB_TERM2_COEFF: 6.76,
  
  /** Modified coefficient for F5 compact length limit - AISC F4-7 reference */
  F5_LP_COEFF: 1.1,
  
  /** Coefficient for F5 stress reduction - AISC Eq. F5-3 */
  F5_STRESS_REDUCTION: 0.3,
} as const;

/**
 * Flexural Design Constants
 * @source AISC 360-22, Chapter F
 */
export const FLEXURAL_CONSTANTS = {
  /** Elastic buckling coefficient - AISC Eq. F4-14, F5-9, F6-4, F7-3 */
  ELASTIC_BUCKLING_COEFF: 0.9,
  
  /** Minor axis plastic moment limit factor - AISC Eq. F6-1 */
  MINOR_AXIS_LIMIT_FACTOR: 1.6,
  
  /** Stress reduction factor for noncompact elements - Multiple AISC equations */
  STRESS_REDUCTION_FACTOR: 0.7,
  
  /** Web plastification calculation constants - AISC F5-6 */
  WEB_PLASTIFICATION: {
    DENOMINATOR_BASE: 1200,
    DENOMINATOR_COEFF: 300,
  },
  
  /** Plate buckling coefficient bounds - AISC specifications */
  KC_BOUNDS: {
    MIN: 0.35,
    MAX: 0.76,
  },
  
  /** Initial values for plastification factors */
  INITIAL_PLASTIFICATION_FACTOR: 1.0 as const,
  
  /** Compression/tension ratio threshold for F4-17 - AISC F4-17 */
  IYC_IY_THRESHOLD: 0.23,
  
  /** Division by zero prevention for web calculations */
  DIVISION_PROTECTION: 0.1,
  
  /** Coefficient for singly symmetric web calculation - AISC F4 */
  SINGLY_SYMMETRIC_COEFF: 0.09,
} as const;

/**
 * Interaction Design Constants - AISC 360-22 Chapter H
 * @source AISC 360-22, Chapter H
 */
export const INTERACTION_CONSTANTS = {
  /** Axial load threshold for H1.1 equation selection - AISC H1.1 */
  AXIAL_LOAD_THRESHOLD: 0.2,
  
  /** Coefficient for H1.1(a) equation - AISC Eq. H1-1a */
  H1_1A_MOMENT_COEFF: 8 / 9,
  
  /** Coefficient for H1.1(b) equation - AISC Eq. H1-1b */
  H1_1B_AXIAL_COEFF: 0.5,
  
  /** Coefficient for H1.3 out-of-plane buckling - AISC Eq. H1-3 */
  H1_3_COEFF_1: 1.5,
  H1_3_COEFF_2: 0.5,
  
  /** Coefficient for H1.2 tension enhancement - AISC Eq. H1-2 */
  TENSION_ENHANCEMENT_COEFF: 1.0, // α = 1.0 for LRFD, 1.6 for ASD
  
  /** Minimum effective length ratio for H1.3 applicability */
  MIN_EFFECTIVE_LENGTH_RATIO: 0.05,
  
  /** Default H1.3 threshold for moment ratio (Mry/Mcy) */
  DEFAULT_H1_3_THRESHOLD: 0.05,
  
  /** H2 stress interaction unity limit - AISC Eq. H2-1 */
  H2_UNITY_LIMIT: 1.0,
  
  /** Default minimum stress ratio for H2 analysis consideration */
  H2_MIN_STRESS_RATIO: 0.01,
  
  /** H3 torsional analysis constants - AISC Section H3 */
  H3_TORSION_THRESHOLD: 0.2,        // 20% threshold for combined analysis
  H3_UNITY_LIMIT: 1.0,              // Unity limit for H3-6 equation
  
  /** H3 torsional critical stress coefficients - AISC H3-2a, H3-2b */
  H3_ROUND_COEFF_1: 1.23,           // Coefficient for H3-2a equation
  H3_ROUND_COEFF_2: 0.60,           // Coefficient for H3-2b equation
  H3_ROUND_EXPONENT_1: 5/4,         // Exponent for H3-2a (L/D)(D/t)^(5/4)
  H3_ROUND_EXPONENT_2: 3/2,         // Exponent for H3-2b (D/t)^(3/2)
  H3_YIELD_STRESS_FACTOR: 0.6,      // Factor for maximum Fcr (0.6Fy)
  
  /** H3 rectangular HSS coefficients - AISC H3-3, H3-4, H3-5 */
  H3_RECT_LIMIT_1: 2.45,            // First h/t limit (2.45√(E/Fy))
  H3_RECT_LIMIT_2: 3.07,            // Second h/t limit (3.07√(E/Fy))
  H3_RECT_LIMIT_3: 260,             // Third h/t limit (260)
  H3_RECT_COEFF_1: 0.6,             // Coefficient for H3-3
  H3_RECT_COEFF_2: 2.45,            // Coefficient for H3-4
  H3_RECT_COEFF_3: 0.458,           // Coefficient for H3-5
  H3_PI_SQUARED: Math.PI ** 2,      // π² for H3-5 equation
  
  /** H4 flange rupture constants - AISC Section H4 */
  H4_UNITY_LIMIT: 1.0,              // Unity limit for H4-1 equation
} as const;

/**
 * Shear Design Constants
 * @source AISC 360-22, Section G2
 */
export const SHEAR_CONSTANTS = {
  /** Shear yielding coefficient - AISC Eq. G2-1 */
  SHEAR_YIELDING_COEFF: 0.6,
  
  /** Web slenderness limit for shear calculations - AISC G2.1 */
  WEB_SLENDERNESS_LIMIT: 2.24,
  
  /** Shear buckling coefficients for unstiffened webs - AISC G2.1(b) */
  SHEAR_BUCKLING: {
    /** Plate buckling coefficient for unstiffened webs */
    KV_UNSTIFFENED: 5.0,
    
    /** Shear coefficient threshold 1 - AISC G2.1(b) */
    CV1_THRESHOLD_1: 1.1,
    
    /** Shear coefficient threshold 2 - AISC G2.1(b) */
    CV1_THRESHOLD_2: 1.37,
    
    /** Shear coefficient for threshold 1 - AISC G2.1(b) */
    CV1_VALUE_1: 1.1,
    
    /** Shear coefficient for elastic buckling - AISC G2.1(b) */
    CV1_COEFF_ELASTIC: 1.51,
    
    /** Initial shear coefficient (no reduction) */
    CV1_INITIAL: 1.0,
  },
} as const;

/**
 * Member Stability Bracing Constants
 * @source AISC 360-22, Appendix 6 - Member Stability Bracing
 */
export const BRACING_CONSTANTS = {
  /** Column Panel Bracing - AISC Eq. A-6-1 */
  COLUMN_PANEL: {
    /** Required shear strength coefficient - AISC Eq. A-6-1 */
    SHEAR_STRENGTH_COEFF: 0.005,
  },

  /** Column Point Bracing - AISC Eq. A-6-3 */
  COLUMN_POINT: {
    /** Required strength coefficient - AISC Eq. A-6-3 */
    STRENGTH_COEFF: 0.01,
  },

  /** Beam Panel Bracing - AISC Eq. A-6-5 */  
  BEAM_PANEL: {
    /** Required shear strength coefficient - AISC Eq. A-6-5 */
    SHEAR_STRENGTH_COEFF: 0.01,
  },

  /** Beam Point Bracing - AISC Eq. A-6-7 */
  BEAM_POINT: {
    /** Required strength coefficient - AISC Eq. A-6-7 */
    STRENGTH_COEFF: 0.02,
  },

  /** Beam Torsional Bracing - AISC Eq. A-6-9 */
  BEAM_TORSIONAL: {
    /** Required flexural strength coefficient - AISC Eq. A-6-9 */
    FLEXURAL_STRENGTH_COEFF: 3.6,
    /** Length ratio coefficient for minimum requirement - AISC Eq. A-6-9 */
    LENGTH_RATIO_COEFF: 500,
    /** Minimum flexural strength ratio - AISC Eq. A-6-9 */
    MIN_FLEXURAL_RATIO: 0.02,
  },

  /** Stiffness calculation coefficients */
  STIFFNESS: {
    /** Panel bracing stiffness multipliers - AISC Eq. A-6-2a/2b and A-6-6a/6b */
    PANEL_STIFFNESS: {
      /** LRFD stiffness coefficient for panels */
      LRFD_MULTIPLIER: 2.0,
      /** ASD stiffness coefficient for panels */
      ASD_MULTIPLIER: 2.0,
    },

    /** Point bracing stiffness multipliers - AISC Eq. A-6-4a/4b and A-6-8a/8b */
    POINT_STIFFNESS: {
      /** LRFD stiffness coefficient for point braces */
      LRFD_MULTIPLIER: 8.0,
      /** ASD stiffness coefficient for point braces */
      ASD_MULTIPLIER: 8.0,
    },

    /** Torsional bracing stiffness multipliers - AISC Eq. A-6-11a/11b */
    TORSIONAL_STIFFNESS: {
      /** LRFD torsional stiffness coefficient */
      LRFD_MULTIPLIER: 3.6,
      /** ASD torsional stiffness coefficient */
      ASD_MULTIPLIER: 3.6,
    },
  },

  /** Resistance factors and safety factors */
  FACTORS: {
    /** LRFD resistance factor - AISC 360-22 */
    PHI_LRFD: 0.75,
    /** ASD safety factor - AISC 360-22 */
    OMEGA_ASD: 2.00,
    /** Alternative LRFD resistance factor for torsional bracing - AISC Eq. A-6-11a */
    PHI_TORSIONAL_LRFD: 0.75,
    /** Alternative ASD safety factor for torsional bracing - AISC Eq. A-6-11a */
    OMEGA_TORSIONAL_ASD: 3.00,
  },

  /** Web distortional stiffness constants - AISC Eq. A-6-12 */
  WEB_DISTORTIONAL: {
    /** Coefficient for flexural term in βsec calculation */
    FLEXURAL_COEFF: 1.5,
    /** Coefficient for torsional term in βsec calculation */
    TORSIONAL_COEFF: 1.0,
    /** Denominator constant in βsec calculation */
    DENOMINATOR: 12,
    /** Overall coefficient for web distortional stiffness */
    OVERALL_COEFF: 3.3,
  },
} as const;

/**
 * Slenderness Limit Coefficients
 * @source AISC 360-22, Table B4.1a and B4.1b
 */
export const SLENDERNESS_COEFFICIENTS = {
  /** Table B4.1a - Compression Elements */
  COMPRESSION: {
    I_FLANGE_ROLLED: { LAMBDA_P: 0.56, LAMBDA_R: 0.56 },
    I_FLANGE_BUILDUP: { LAMBDA_P: 0.64, LAMBDA_R: 0.64 },
    ANGLE_LEG: { LAMBDA_P: 0.45, LAMBDA_R: 0.45 },
    TEE_STEM: { LAMBDA_P: 0.75, LAMBDA_R: 0.75 },
    I_WEB: { LAMBDA_P: 1.49, LAMBDA_R: 1.49 },
    HSS_WALL: { LAMBDA_P: 1.40, LAMBDA_R: 1.40 },
    COVER_PLATE: { LAMBDA_P: 1.40, LAMBDA_R: 1.40 },
    STIFFENED_ELEMENT: { LAMBDA_P: 1.49, LAMBDA_R: 1.49 },
    HSS_ROUND: { LAMBDA_P: 0.11, LAMBDA_R: 0.11 }, // Note: multiplied by E/Fy
  },
  
  /** Table B4.1b - Flexure Elements */
  FLEXURE: {
    I_FLANGE_ROLLED: { LAMBDA_P: 0.38, LAMBDA_R: 1.0 },
    I_FLANGE_BUILDUP: { LAMBDA_P: 0.38, LAMBDA_R: 0.95 },
    ANGLE_LEG: { LAMBDA_P: 0.54, LAMBDA_R: 0.91 },
    I_FLANGE_MINOR_AXIS: { LAMBDA_P: 0.38, LAMBDA_R: 1.0 },
    TEE_STEM: { LAMBDA_P: 0.84, LAMBDA_R: 1.52 },
    TEE_FLANGE: { LAMBDA_P: 0.38, LAMBDA_R: 1.0 }, // Similar to I-shape flanges per AISC F9
    I_WEB: { LAMBDA_P: 3.76, LAMBDA_R: 5.70 },
    I_WEB_SINGLY_SYMMETRIC: { LAMBDA_P: 0.54, LAMBDA_R: 5.70 },
    HSS_RECT_FLANGE: { LAMBDA_P: 1.12, LAMBDA_R: 1.40 },
    HSS_WEB: { LAMBDA_P: 2.42, LAMBDA_R: 5.70 },
    HSS_ROUND: { LAMBDA_P: 0.07, LAMBDA_R: 0.31 }, // Note: multiplied by E/Fy
    BOX_FLANGE: { LAMBDA_P: 1.12, LAMBDA_R: 1.49 },
  },
  
  /** Special coefficients for complex calculations */
  SPECIAL: {
    /** Singly symmetric web calculation - AISC Table B4.1b Case 16 */
    SINGLY_SYMMETRIC_MP_MY_COEFF: 0.54,
    SINGLY_SYMMETRIC_REDUCTION: 0.09,
    
    /** Default plate buckling coefficient */
    DEFAULT_KC: 4.0,
  },
} as const;

/**
 * HSS-Specific Design Constants
 * @source AISC 360-22, Section F7
 */
export const HSS_CONSTANTS = {
  /** HSS flange slenderness calculation - Section F7 */
  FLANGE_SLENDERNESS_REDUCTION: 3,
  
  /** HSS web slenderness calculation - Section F7 */
  WEB_SLENDERNESS_REDUCTION: 3,
} as const;

/**
 * Deflection Constants
 * @source Common engineering practice and codes
 */
export const DEFLECTION_CONSTANTS = {
  /** Deflection limit ratios (L/limit) for different applications */
  DEFLECTION_LIMITS: {
    /** Live load deflection for floors: L/360 */
    FLOOR: 360,
    /** Live load deflection limit: L/360 */
    LIVE_LOAD: 360,
    /** Total load deflection for roofs: L/240 */
    ROOF: 240,
    /** Cantilever beam deflection limit: L/120 */
    CANTILEVER: 120,
    /** General purpose deflection limit: L/240 */
    GENERAL: 240,
  },
  /** Adequacy threshold for deflection checks */
  ADEQUACY_THRESHOLD: 1.0,
} as const;

// =============================================================================
// MATERIAL INTERFACES
// =============================================================================

/**
 * Interface for material properties.
 */
export interface Material {
  Fy: number; // Yield strength in ksi
  Fu: number; // Ultimate strength in ksi
}

// =============================================================================
// DESIGN METHOD ENUMS
// =============================================================================

/**
 * Enumeration of design methods.
 * @source AISC 360-22, Chapter B
 */
export enum DesignMethod {
  LRFD = "LRFD", // Load and Resistance Factor Design
  ASD = "ASD",   // Allowable Strength Design
}

// =============================================================================
// SECTION TYPE ENUMS
// =============================================================================

/**
 * Enumeration of section types for resistance factor lookup and general use.
 */
export enum SectionType {
  W = "W",
  I = "I", 
  S = "S",
  M = "M",
  C = "C",
  MC = "MC",
  HSS_RECT = "HSS-RECT",
  HSS_ROUND = "HSS-ROUND",
  L = "L",           // Single angles
  DOUBLE_L = "2L",   // Double angles
  RECT_BAR = "RECT-BAR",  // Rectangular bars
  ROUND_BAR = "ROUND-BAR", // Round bars
}

// =============================================================================
// SLENDERNESS CLASSIFICATION ENUMS
// =============================================================================

/**
 * Enumeration of slenderness classifications.
 * @source AISC 360-22, Table B4.1a and B4.1b
 */
export enum SlendernessClassification {
  COMPACT = "Compact",
  NONCOMPACT = "Noncompact", 
  SLENDER = "Slender",
}

// =============================================================================
// LIMIT STATE ENUMS
// =============================================================================

/**
 * Enumeration of flexural limit states.
 * @source AISC 360-22, Chapter F
 */
export enum FlexuralLimitState {
  YIELDING = "Yielding (AISC F2.1)",
  LATERAL_TORSIONAL_BUCKLING = "Lateral-Torsional Buckling (AISC F2.2)",
  FLANGE_LOCAL_BUCKLING = "Flange Local Buckling (AISC F3)",
  WEB_LOCAL_BUCKLING = "Web Local Buckling (AISC F4/F5)",
  HSS_FLANGE_LOCAL_BUCKLING = "HSS Flange Local Buckling (AISC F7.1)",
  HSS_WEB_LOCAL_BUCKLING = "HSS Web Local Buckling (AISC F7.2)",
  HSS_ROUND_LOCAL_BUCKLING = "Round HSS Local Buckling (AISC F8.2)",
  ANGLE_LEG_LOCAL_BUCKLING = "Single Angle Leg Local Buckling (AISC F10.3)",
  NOT_IMPLEMENTED = "Not Implemented",
}

/**
 * Enumeration of shear limit states.
 * @source AISC 360-22, Chapter G
 */
export enum ShearLimitState {
  SHEAR_YIELDING = "Shear Yielding (AISC G2.1(a))",
  SHEAR_BUCKLING = "Shear Buckling (AISC G2.1(b))",
  NOT_IMPLEMENTED = "Not Implemented",
}

/**
 * Enumeration of limit state types for resistance factor lookup.
 * @source AISC 360-22, Chapters F and G
 */
export enum LimitStateType {
  FLEXURE = "flexure",
  SHEAR_YIELDING = "shear_yielding",
  SHEAR_BUCKLING = "shear_buckling",
}

// =============================================================================
// RESISTANCE FACTORS
// =============================================================================

/**
 * Interface for resistance factor lookup table entry.
 */
export interface ResistanceFactors {
  phi: number; // LRFD resistance factor
  omega: number; // ASD safety factor
}

/**
 * Resistance factor lookup table based on AISC 360-22.
 * @source AISC 360-22, Sections F1 and G1
 */
export const RESISTANCE_FACTOR_TABLE: Record<LimitStateType, Record<string, ResistanceFactors>> = {
  [LimitStateType.FLEXURE]: {
    // All section types have the same flexural resistance factors
    default: { phi: 0.90, omega: 1.67 }
  },
  [LimitStateType.SHEAR_YIELDING]: {
    // All section types have the same shear yielding resistance factors
    default: { phi: 1.00, omega: 1.67 }
  },
  [LimitStateType.SHEAR_BUCKLING]: {
    // All section types have the same shear buckling resistance factors
    default: { phi: 0.90, omega: 1.67 }
  }
};

// =============================================================================
// SLENDERNESS LIMITS
// =============================================================================

/**
 * Interface for slenderness limits.
 */
export interface SlendernessLimits {
  lambda_p: number; // Limit for compact elements
  lambda_r: number; // Limit for noncompact elements
}

/**
 * Slenderness limit calculation functions.
 * @source AISC 360-22, Table B4.1a and B4.1b
 */
export const SLENDERNESS_LIMITS = {
  "I-flange": (Fy: number): SlendernessLimits => ({
    lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_ROLLED.LAMBDA_P * Math.sqrt(E / Fy),
    lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.I_FLANGE_ROLLED.LAMBDA_R * Math.sqrt(E / Fy),
  }),
  "I-web-flexure": (Fy: number): SlendernessLimits => ({
    lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_P * Math.sqrt(E / Fy),
    lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.I_WEB.LAMBDA_R * Math.sqrt(E / Fy),
  }),
  "HSS-wall": (Fy: number): SlendernessLimits => ({
    lambda_p: SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_RECT_FLANGE.LAMBDA_P * Math.sqrt(E / Fy),
    lambda_r: SLENDERNESS_COEFFICIENTS.FLEXURE.HSS_RECT_FLANGE.LAMBDA_R * Math.sqrt(E / Fy),
  }),
} as const;

// =============================================================================
// STRING CONSTANTS
// =============================================================================

/**
 * Element type strings for slenderness calculations
 * @source AISC 360-22, Table B4.1a and B4.1b
 */
export const ELEMENT_TYPES = {
  // Compression elements
  I_FLANGE_COMPRESSION: "I-flange-compression",
  I_FLANGE_BUILDUP_COMPRESSION: "I-flange-buildup-compression", 
  ANGLE_LEG_COMPRESSION: "angle-leg-compression",
  TEE_STEM_COMPRESSION: "tee-stem-compression",
  I_WEB_COMPRESSION: "I-web-compression",
  HSS_WALL_COMPRESSION: "HSS-wall-compression",
  FLANGE_COVER_PLATE_COMPRESSION: "flange-cover-plate-compression",
  STIFFENED_ELEMENT_COMPRESSION: "stiffened-element-compression",
  HSS_ROUND_COMPRESSION: "HSS-round-compression",
  
  // Flexure elements
  I_FLANGE_FLEXURE: "I-flange-flexure",
  I_FLANGE_BUILDUP_FLEXURE: "I-flange-buildup-flexure",
  ANGLE_LEG_FLEXURE: "angle-leg-flexure",
  I_FLANGE_MINOR_AXIS_FLEXURE: "I-flange-minor-axis-flexure",
  TEE_STEM_FLEXURE: "tee-stem-flexure",
  TEE_FLANGE_FLEXURE: "tee-flange-flexure", 
  I_WEB_FLEXURE: "I-web-flexure",
  I_WEB_SINGLY_SYMMETRIC_FLEXURE: "I-web-singly-symmetric-flexure",
  HSS_RECT_FLEXURE: "HSS-rect-flexure",
  FLANGE_COVER_PLATE_FLEXURE: "flange-cover-plate-flexure",
  HSS_WEB_FLEXURE: "HSS-web-flexure",
  HSS_ROUND_FLEXURE: "HSS-round-flexure",
  BOX_FLANGE_FLEXURE: "box-flange-flexure",
  
  // Legacy
  I_FLANGE: "I-flange",
} as const;

/**
 * Section type strings for type checking and routing
 */
export const SECTION_TYPE_STRINGS = {
  W: "W",
  I: "I", 
  S: "S",
  M: "M",
  C: "C",
  MC: "MC",
  HSS_RECT: "HSS-RECT",
  HSS_ROUND: "HSS-ROUND",
  L: "L",
  DOUBLE_L: "2L",
  TEE: "TEE",
  WT: "WT",
  RECT_BAR: "RECT-BAR",
  ROUND_BAR: "ROUND-BAR",
} as const;

/**
 * Deflection control direction strings
 */
export const DEFLECTION_DIRECTIONS = {
  UPWARD: "upward",
  DOWNWARD: "downward", 
  BOTH: "both",
} as const;

/**
 * Deflection application strings
 */
export const DEFLECTION_APPLICATIONS = {
  FLOOR: "floor",
  ROOF: "roof",
  CANTILEVER: "cantilever", 
  GENERAL: "general",
} as const;

// =============================================================================
// COMMON ERROR MESSAGES
// =============================================================================

export const ERROR_MESSAGES = {
  INVALID_DESIGN_METHOD: (method: string) => `Invalid design method: '${method}'. Must be 'LRFD' or 'ASD'.`,
  INVALID_LIMIT_STATE: (limitState: string) => `Invalid limit state type: '${limitState}'. Must be 'flexure', 'shear_yielding', or 'shear_buckling'.`,
  INVALID_SECTION_TYPE: (sectionType: string) => `Invalid section type: '${sectionType}'.`,
  NEGATIVE_VALUE: (paramName: string) => `${paramName} must be non-negative.`,
  POSITIVE_VALUE: (paramName: string) => `${paramName} must be positive.`,
  MISSING_PROPERTY: (propName: string) => `${propName} is required.`,
  MISSING_SECTION_PROPERTIES: (props: string) => `Section must contain numeric ${props} properties.`,
  CB_LESS_THAN_ONE: "Lateral-torsional buckling modification factor (Cb) must be >= 1.0.",
  SAFETY_FACTOR_ZERO: "Safety factor (Ω) cannot be zero for ASD calculations.",
  UNSUPPORTED_SECTION_TYPE: (sectionType: string, calculation: string) => 
    `${calculation} calculation for section type '${sectionType}' is not implemented.`,
  
  // Specific property validation messages
  SECTION_TYPE_REQUIRED: "Section object with 'Type' property is required.",
  NUMERIC_PROPERTY_REQUIRED: (prop: string) => `Section object must contain a numeric '${prop}' property.`,
  YIELD_STRENGTH_POSITIVE: "Yield strength (Fy) must be positive.",
  FY_POSITIVE: "Fy must be a positive number.",
  
  // Beam analysis input validation
  BEAM_ANALYSIS_INPUTS_REQUIRED: "beamAnalysisInputs is required.",
  SECTION_REQUIRED: "section is required in beamAnalysisInputs.",
  MATERIAL_REQUIRED: "material is required in beamAnalysisInputs.",
  LTB_PARAMETERS_REQUIRED: "ltbParameters is required in beamAnalysisInputs.",
  DEMAND_FORCES_REQUIRED: "demandForces is required in beamAnalysisInputs.",
  DESIGN_METHOD_REQUIRED: "designMethod is required in beamAnalysisInputs.",
  
  // Force validation
  MU_NON_NEGATIVE: "demandForces.Mu must be a non-negative number.",
  VU_NON_NEGATIVE: "demandForces.Vu must be a non-negative number.",
  LB_NON_NEGATIVE: "ltbParameters.Lb must be a non-negative number.",
  
  // Stiffness adjustment validation
  NOMINAL_AXIAL_STRENGTH_REQUIRED: "nominalAxialStrength is required and must be positive when applyStiffnessAdjustments is true.",
  PU_NON_NEGATIVE_REQUIRED: "demandForces.Pu is required and must be non-negative when applyStiffnessAdjustments is true.",
  
  // Deflection validation
  DEFLECTION_INPUTS_REQUIRED: "Deflection inputs are required.",
  SECTION_REQUIRED_DEFLECTION: "Section is required for deflection check.",
  MATERIAL_REQUIRED_DEFLECTION: "Material is required for deflection check.",
  BEAM_LENGTH_POSITIVE: "Beam length must be a positive number.",
  MAX_UPWARD_DEFLECTION_NUMBER: "Max upward deflection must be a number.",
  MAX_DOWNWARD_DEFLECTION_NUMBER: "Max downward deflection must be a number.",
  DEFLECTION_LIMIT_POSITIVE: "Deflection limit must be a positive number.",
  
  // Section property validation for specific calculations
  RY_REQUIRED_LTB: "Section must have 'ry' property for LTB calculations.",
  SX_REQUIRED_LTB: "Section must have 'Sx' property for LTB calculations.",
  J_REQUIRED_LTB: "Section must have 'J' property for LTB calculations.",
  TF_REQUIRED_LTB: "Section must have 'tf' property for LTB calculations.",
  BF_REQUIRED_LTB: "Section must have 'bf' property for LTB calculations.",
  
  // Web plastification validation
  WEB_PLASTIFICATION_PROPS_REQUIRED: "Section must have 'tf', 'tw', 'bf', and 'Sx' properties for web plastification calculations.",
  
  // F4 calculation validation
  F4_PROPS_REQUIRED: "Section must have 'Zx', 'Sx', 'bf', and 'tf' properties for F4 calculations.",
  
  // F5 calculation validation  
  F5_PROPS_REQUIRED: "Section must have 'Zx', 'Sx', and 'ry' properties for F5 calculations.",
  FLANGE_PROPS_REQUIRED: "Section must have 'bf' and 'tf' properties for flange calculations.",
  
  // F6 calculation validation
  MINOR_AXIS_PROPS_REQUIRED: "Section must have 'Sy', 'd', and 'tf' properties for minor axis calculations.",
  MAJOR_AXIS_PROPS_REQUIRED: "Section must have 'd', 'tf', and 'tw' properties for major axis calculations.",
  
  // HSS calculation validation
  HSS_FLB_ZX_REQUIRED: "Section must have 'Zx' property for HSS FLB calculations.",
  HSS_FLB_SX_REQUIRED: "Section must have 'Sx' property for HSS FLB calculations.",
  HSS_FLB_BF_REQUIRED: "Section must have 'bf' property for HSS FLB calculations.",
  HSS_FLB_TF_REQUIRED: "Section must have 'tf' property for HSS FLB calculations.",
  HSS_WLB_ZX_REQUIRED: "Section must have 'Zx' property for HSS WLB calculations.",
  HSS_WLB_SX_REQUIRED: "Section must have 'Sx' property for HSS WLB calculations.",
  
  // Angle calculation validation
  ANGLE_SX_REQUIRED: "Section must have 'Sx' property for angle flexural calculations.",
  FLEXURAL_ZX_REQUIRED: "Section must have 'Zx' property for flexural calculations.",
  
  // Shear calculation validation
  SHEAR_SECTION_PROPS_REQUIRED: "Section must contain numeric 'd', 'tw', and 'tf' properties for shear calculation.",
  HSS_SHEAR_PROPS_REQUIRED: "Section must contain numeric 'd' and 'tw' properties for HSS shear calculation.",
  ANGLE_SHEAR_PROPS_REQUIRED: "Section must contain numeric 'A', 'd', and 'tw' properties for angle shear calculation.",
  
  // General validation
  UNBRACED_LENGTH_NON_NEGATIVE: "Unbraced length (Lb) must be non-negative.",
  BEAM_LENGTH_POSITIVE_GENERAL: "Beam length must be positive.",
  DEFLECTION_LIMIT_POSITIVE_GENERAL: "Deflection limit must be positive.",

  // Analysis utils error messages
  INVALID_LIMIT_STATE_TYPE: (limitState: string) => `Invalid limit state type: '${limitState}'. Must be 'flexure', 'shear_yielding', or 'shear_buckling'.`,
  NO_RESISTANCE_FACTORS: (limitStateType: string, sectionType: string) => `No resistance factors found for limit state '${limitStateType}' and section type '${sectionType}'.`,
  NOMINAL_STRENGTH_NON_NEGATIVE: "Nominal strength must be non-negative.",
  FACTOR_NON_NEGATIVE: "Resistance/safety factor must be non-negative.",
  SAFETY_FACTOR_ZERO_ASD: "Safety factor (Ω) cannot be zero for ASD calculations.",
  DEMAND_NON_NEGATIVE: "Demand must be non-negative.",
  CAPACITY_NON_NEGATIVE: "Capacity must be non-negative.",
} as const;

// =============================================================================
// UTILITY TYPES
// =============================================================================

/**
 * Type for section types that support I-shape calculations.
 */
export type IShapeSectionType = "W" | "I" | "S" | "M" | "C" | "MC";

/**
 * Type for section types that support HSS calculations.
 */
export type HSSSectionType = "HSS-RECT" | "HSS-ROUND";

/**
 * Type for section types that support angle calculations.
 */
export type AngleSectionType = "L" | "2L";

/**
 * Type for section types that support tee calculations.
 */
export type TeeSectionType = "TEE" | "WT";

/**
 * Type for section types that support bar calculations.
 */
export type BarSectionType = "RECT-BAR" | "ROUND-BAR";

/**
 * Union type for all supported section types.
 */
export type SupportedSectionType = IShapeSectionType | HSSSectionType | AngleSectionType | TeeSectionType | BarSectionType;

// =============================================================================
// ANALYSIS CONSTANTS
// =============================================================================

/**
 * Analysis constants for design checks
 */
export const ANALYSIS_CONSTANTS = {
  /** DCR adequacy threshold - design is adequate if DCR ≤ 1.0 */
  DCR_ADEQUACY_THRESHOLD: 1.0,
} as const; 