import { describe, it, expect } from "@jest/globals";
import {
  calculateTauB,
  calculateStiffnessAdjustments,
  applyStiffnessAdjustments,
  calculateAdjustedEffectiveLengthFactor,
  StiffnessAdjustmentParams,
} from "./stiffness-adjustment-utils";
import { DesignMethod } from "./constants";

describe("Stiffness Adjustment Utilities", () => {
  describe("calculateTauB", () => {
    it("should return 1.0 when αPr/Pn ≤ 0.5 (LRFD)", () => {
      const Pr = 100; // kips
      const Pn = 250; // kips (αPr/Pn = 1.0 * 100 / 250 = 0.4)
      const designMethod = DesignMethod.LRFD;

      const tauB = calculateTauB(Pr, Pn, designMethod);

      expect(tauB).toBe(1.0);
    });

    it("should return 1.0 when αPr/Pn ≤ 0.5 (ASD)", () => {
      const Pr = 100; // kips
      const Pn = 400; // kips (αPr/Pn = 1.6 * 100 / 400 = 0.4)
      const designMethod = DesignMethod.ASD;

      const tauB = calculateTauB(Pr, Pn, designMethod);

      expect(tauB).toBe(1.0);
    });

    it("should calculate τb = 4(αPr/Pn)[1-(αPr/Pn)] when αPr/Pn > 0.5 (LRFD)", () => {
      const Pr = 150; // kips
      const Pn = 200; // kips (αPr/Pn = 1.0 * 150 / 200 = 0.75)
      const designMethod = DesignMethod.LRFD;

      const tauB = calculateTauB(Pr, Pn, designMethod);

      // Expected: τb = 4 * 0.75 * (1 - 0.75) = 4 * 0.75 * 0.25 = 0.75
      expect(tauB).toBeCloseTo(0.75, 3);
    });

    it("should calculate τb = 4(αPr/Pn)[1-(αPr/Pn)] when αPr/Pn > 0.5 (ASD)", () => {
      const Pr = 100; // kips
      const Pn = 200; // kips (αPr/Pn = 1.6 * 100 / 200 = 0.8)
      const designMethod = DesignMethod.ASD;

      const tauB = calculateTauB(Pr, Pn, designMethod);

      // Expected: τb = 4 * 0.8 * (1 - 0.8) = 4 * 0.8 * 0.2 = 0.64
      expect(tauB).toBeCloseTo(0.64, 3);
    });

    it("should handle the boundary case αPr/Pn = 0.5 exactly", () => {
      const Pr = 100; // kips
      const Pn = 200; // kips (αPr/Pn = 1.0 * 100 / 200 = 0.5)
      const designMethod = DesignMethod.LRFD;

      const tauB = calculateTauB(Pr, Pn, designMethod);

      expect(tauB).toBe(1.0);
    });

    it("should handle zero axial load (Pr = 0)", () => {
      const Pr = 0; // kips
      const Pn = 200; // kips (αPr/Pn = 0)
      const designMethod = DesignMethod.LRFD;

      const tauB = calculateTauB(Pr, Pn, designMethod);

      expect(tauB).toBe(1.0);
    });

    it("should throw error for negative Pr", () => {
      const Pr = -50; // kips
      const Pn = 200; // kips
      const designMethod = DesignMethod.LRFD;

      expect(() => calculateTauB(Pr, Pn, designMethod)).toThrow(
        "Required axial strength (Pr) must be non-negative."
      );
    });

    it("should throw error for zero or negative Pn", () => {
      const Pr = 100; // kips
      const Pn = 0; // kips
      const designMethod = DesignMethod.LRFD;

      expect(() => calculateTauB(Pr, Pn, designMethod)).toThrow(
        "Nominal axial strength (Pn) must be positive."
      );
    });
  });

  describe("calculateStiffnessAdjustments", () => {
    it("should return correct adjustment factors for low axial load", () => {
      const params: StiffnessAdjustmentParams = {
        Pr: 50, // kips
        Pn: 200, // kips (αPr/Pn = 0.25 for LRFD)
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateStiffnessAdjustments(params);

      expect(result.baseReductionFactor).toBe(0.80);
      expect(result.tauB).toBe(1.0);
      expect(result.totalFlexuralReduction).toBeCloseTo(0.80, 3);
      expect(result.totalStiffnessReduction).toBe(0.80);
    });

    it("should return correct adjustment factors for high axial load", () => {
      const params: StiffnessAdjustmentParams = {
        Pr: 150, // kips
        Pn: 200, // kips (αPr/Pn = 0.75 for LRFD)
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateStiffnessAdjustments(params);

      expect(result.baseReductionFactor).toBe(0.80);
      expect(result.tauB).toBeCloseTo(0.75, 3);
      expect(result.totalFlexuralReduction).toBeCloseTo(0.80 * 0.75, 3); // 0.60
      expect(result.totalStiffnessReduction).toBe(0.80);
    });

    it("should handle ASD design method correctly", () => {
      const params: StiffnessAdjustmentParams = {
        Pr: 100, // kips
        Pn: 200, // kips (αPr/Pn = 1.6 * 100 / 200 = 0.8 for ASD)
        designMethod: DesignMethod.ASD,
      };

      const result = calculateStiffnessAdjustments(params);

      expect(result.baseReductionFactor).toBe(0.80);
      expect(result.tauB).toBeCloseTo(0.64, 3); // 4 * 0.8 * 0.2
      expect(result.totalFlexuralReduction).toBeCloseTo(0.80 * 0.64, 3); // 0.512
      expect(result.totalStiffnessReduction).toBe(0.80);
    });
  });

  describe("applyStiffnessAdjustments", () => {
    it("should apply stiffness adjustments to moment of inertia values", () => {
      const Ix = 800; // in⁴
      const Iy = 40; // in⁴
      const params: StiffnessAdjustmentParams = {
        Pr: 150, // kips
        Pn: 200, // kips (results in τb = 0.75)
        designMethod: DesignMethod.LRFD,
      };

      const result = applyStiffnessAdjustments(Ix, Iy, params);

      // Expected reduction: 0.80 * 0.75 = 0.60
      expect(result.IxAdjusted).toBeCloseTo(800 * 0.60, 1); // 480
      expect(result.IyAdjusted).toBeCloseTo(40 * 0.60, 1); // 24
    });

    it("should throw error for zero or negative moment of inertia", () => {
      const params: StiffnessAdjustmentParams = {
        Pr: 100,
        Pn: 200,
        designMethod: DesignMethod.LRFD,
      };

      expect(() => applyStiffnessAdjustments(0, 40, params)).toThrow(
        "Moment of inertia values must be positive."
      );

      expect(() => applyStiffnessAdjustments(800, -40, params)).toThrow(
        "Moment of inertia values must be positive."
      );
    });
  });

  describe("calculateAdjustedEffectiveLengthFactor", () => {
    it("should calculate adjusted effective length factor", () => {
      const K = 1.0;
      const params: StiffnessAdjustmentParams = {
        Pr: 100, // kips
        Pn: 250, // kips (low axial load, τb = 1.0)
        designMethod: DesignMethod.LRFD,
      };

      const KAdjusted = calculateAdjustedEffectiveLengthFactor(K, params);

      // Expected: K / sqrt(0.80) ≈ 1.0 / 0.894 ≈ 1.118
      expect(KAdjusted).toBeCloseTo(1.118, 3);
    });

    it("should handle higher axial loads with additional τb reduction", () => {
      const K = 1.2;
      const params: StiffnessAdjustmentParams = {
        Pr: 150, // kips
        Pn: 200, // kips (high axial load, τb = 0.75)
        designMethod: DesignMethod.LRFD,
      };

      const KAdjusted = calculateAdjustedEffectiveLengthFactor(K, params);

      // Total stiffness reduction = 0.80
      // Expected: 1.2 / sqrt(0.80) ≈ 1.2 / 0.894 ≈ 1.342
      expect(KAdjusted).toBeCloseTo(1.342, 3);
    });

    it("should throw error for zero or negative K", () => {
      const params: StiffnessAdjustmentParams = {
        Pr: 100,
        Pn: 200,
        designMethod: DesignMethod.LRFD,
      };

      expect(() => calculateAdjustedEffectiveLengthFactor(0, params)).toThrow(
        "Effective length factor (K) must be positive."
      );

      expect(() => calculateAdjustedEffectiveLengthFactor(-1.2, params)).toThrow(
        "Effective length factor (K) must be positive."
      );
    });
  });

  describe("Verification against AISC 360-22 Section C2.3", () => {
    it("should match the specification example values", () => {
      // Test case based on typical values
      const params: StiffnessAdjustmentParams = {
        Pr: 200, // kips
        Pn: 300, // kips
        designMethod: DesignMethod.LRFD,
      };

      // αPr/Pn = 1.0 * 200 / 300 = 0.667
      const alphaPrOverPn = 200 / 300;
      const expectedTauB = 4 * alphaPrOverPn * (1 - alphaPrOverPn);
      // = 4 * 0.667 * 0.333 ≈ 0.889

      const result = calculateStiffnessAdjustments(params);

      expect(result.tauB).toBeCloseTo(expectedTauB, 3);
      expect(result.totalFlexuralReduction).toBeCloseTo(0.80 * expectedTauB, 3);
    });
  });
}); 