import { describe, it, expect } from "@jest/globals";
import { Section } from "./slenderness/types";
import { ShearLimitState } from "./constants";
import { calculateNominalShearStrength } from "./shear/index";

describe("Shear Strength Utilities", () => {
  // Test sections
  const W18X50: Section = {
    name: "W18X50",
    Type: "W",
    A: 14.7,
    d: 18.0,
    tw: 0.355,
    bf: 7.5,
    tf: 0.57,
    Ix: 800,
    Zx: 101,
    Sx: 88.9,
    rx: 7.38,
    Iy: 40.1,
    Zy: 18.7,
    Sy: 10.7,
    ry: 1.65,
    J: 1.43,
    Cw: 1910,
  };

  // Compact HSS section
  const HSS8X4X5_8: Section = {
    name: "HSS8X4X5/8",
    Type: "HSS-RECT",
    A: 10.1,
    d: 8.0,
    tw: 0.589,
    bf: 4.0,
    tf: 0.589,
    Ix: 81.3,
    Zx: 25.4,
    Sx: 20.3,
    Iy: 23.4,
    Zy: 10.3,
    Sy: 8.71,
    J: 76.5,
    Cw: 0,
    ry: 1.52,
  };

  // Test angle sections
  const L6X4X1_2: Section = {
    name: "L6X4X1/2",
    Type: "L",
    A: 4.75,
    d: 6.0,      // Longer leg
    tw: 0.5,     // Leg thickness
    bf: 4.0,     // Shorter leg
    tf: 0.5,     // Same as tw for angles
    Ix: 17.4,
    Zx: 4.25,
    Sx: 4.33,
    rx: 1.91,
    Iy: 6.27,
    Zy: 2.08,
    Sy: 2.15,
    ry: 1.15,
    J: 0.04,
    Cw: 0,
  };

  const DOUBLE_L4X4X1_2: Section = {
    name: "2L4X4X1/2",
    Type: "2L",
    A: 7.5,      // Two angles
    d: 4.0,      // Equal legs
    tw: 0.5,     // Leg thickness
    bf: 4.0,     // Equal legs
    tf: 0.5,     // Same as tw
    Ix: 7.7,
    Zx: 3.84,
    Sx: 3.79,
    rx: 1.01,
    Iy: 7.7,
    Zy: 3.84,
    Sy: 3.79,
    ry: 1.01,
    J: 0.04,
    Cw: 0,
  };

  // Slender web section for testing shear buckling
  const SLENDER_SECTION: Section = {
    name: "SlenderTest",
    Type: "W",
    A: 10.0,
    d: 24.0,
    tw: 0.1, // Very thin web to trigger shear buckling
    bf: 6.0,
    tf: 0.4,
    Ix: 600,
    Zx: 50,
    Sx: 50,
    rx: 8.7,
    Iy: 20,
    Zy: 6.7,
    Sy: 6.7,
    ry: 1.6,
    J: 0.5,
    Cw: 400,
  };

  const Fy = 50; // ksi

  describe("calculateNominalShearStrength for I-Shapes", () => {
    it("should be governed by Shear Yielding for compact web (Cv1 = 1.0)", () => {
      const result = calculateNominalShearStrength(W18X50, Fy);

      // Calculate expected values
      const Aw = W18X50.d * W18X50.tw; // 18.0 * 0.355 = 6.39 in²
      const expectedVn = 0.6 * Fy * Aw * 1.0; // 0.6 * 50 * 6.39 = 191.7 kips

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should be governed by Shear Buckling for slender web (Cv1 < 1.0)", () => {
      const result = calculateNominalShearStrength(SLENDER_SECTION, Fy);

      // Calculate expected values manually
      const h = SLENDER_SECTION.d - 2 * (SLENDER_SECTION.tf || 0); // 24 - 2*0.4 = 23.2
      const h_tw = h / SLENDER_SECTION.tw; // 23.2 / 0.1 = 232
      const lambda_limit = 2.24 * Math.sqrt(29000 / Fy); // 2.24 * sqrt(580) = 53.96

      // Should trigger shear buckling since h_tw (232) > lambda_limit (53.96)
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.Vn).toBeLessThan(
        0.6 * Fy * SLENDER_SECTION.d * SLENDER_SECTION.tw
      );
      expect(result.Vn).toBeGreaterThan(0); // Should still be positive
    });

    it("should handle edge case where h/tw equals the limiting ratio", () => {
      // Create a section with h/tw exactly at the limit
      const lambda_limit = 2.24 * Math.sqrt(29000 / Fy); // ≈ 53.96

      const edgeCaseSection: Section = {
        ...W18X50,
        name: "EdgeCase",
        d: 10.0,
        tw: 0.18,
        tf: 0.1,
      };

      // h = d - 2*tf = 10 - 0.2 = 9.8
      // h/tw = 9.8 / 0.18 ≈ 54.44 (slightly above limit to test boundary)

      const result = calculateNominalShearStrength(edgeCaseSection, Fy);
      const Aw = edgeCaseSection.d * edgeCaseSection.tw;

      // Should be close to shear yielding capacity but might be slightly governed by buckling
      expect(result.Vn).toBeCloseTo(0.6 * Fy * Aw, 0);
    });

    it("should calculate correct shear strength for various I-shape types", () => {
      const testSections = [
        { ...W18X50, Type: "W" as const },
        { ...W18X50, Type: "I" as const },
        { ...W18X50, Type: "S" as const },
        { ...W18X50, Type: "M" as const },
        { ...W18X50, Type: "C" as const },
        { ...W18X50, Type: "MC" as const },
      ];

      testSections.forEach((section) => {
        const result = calculateNominalShearStrength(section, Fy);
        expect(result.Vn).toBeGreaterThan(0);
        expect([
          ShearLimitState.SHEAR_YIELDING,
          ShearLimitState.SHEAR_BUCKLING,
        ]).toContain(result.governingLimitState);
      });
    });
  });

  describe("calculateNominalShearStrength for HSS-RECT", () => {
    it("should be governed by Shear Yielding for compact HSS section", () => {
      const result = calculateNominalShearStrength(HSS8X4X5_8, Fy);

      // HSS calculation uses h = d - 2*tf for web height
      const h = HSS8X4X5_8.d - 2 * (HSS8X4X5_8.tf || 0); // 8.0 - 2*0.589 = 6.822 in
      const Aw = h * HSS8X4X5_8.tw; // 6.822 * 0.589 = 4.018 in²
      const expectedVn = 0.6 * Fy * Aw * 1.0; // 0.6 * 50 * 4.018 = 120.5 kips

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should handle slender HSS web correctly", () => {
      // Create HSS with slender web
      const slenderHSS: Section = {
        ...HSS8X4X5_8,
        name: "SlenderHSS",
        d: 12.0,
        tw: 0.05, // Very thin wall to ensure slenderness
      };

      const result = calculateNominalShearStrength(slenderHSS, Fy);

      // Should trigger buckling for very slender web
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.Vn).toBeGreaterThan(0);
      expect(result.Vn).toBeLessThan(0.6 * Fy * slenderHSS.d * slenderHSS.tw);
    });
  });

  describe("calculateNominalShearStrength for Angles", () => {
    it("should calculate shear strength for single angle (L) sections", () => {
      const result = calculateNominalShearStrength(L6X4X1_2, Fy);

      // For angles, should use gross area and assume shear yielding
      const expectedVn = 0.6 * Fy * L6X4X1_2.A; // 0.6 * 50 * 4.75 = 142.5 kips

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should calculate shear strength for double angle (2L) sections", () => {
      const result = calculateNominalShearStrength(DOUBLE_L4X4X1_2, Fy);

      // For double angles, new implementation calculates for two angles
      // Total area = 7.5, so each angle = 3.75, and 2L gives 2 * (0.6 * 50 * 7.5) = 450 kips
      const expectedVn = 0.6 * Fy * DOUBLE_L4X4X1_2.A * 2; // 0.6 * 50 * 7.5 * 2 = 450 kips

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should handle angles with minimal properties", () => {
      const minimalAngle: Section = {
        name: "L3X3X1/4",
        Type: "L",
        A: 1.44,
        d: 3.0,
        tw: 0.25,
        Ix: 1.24,
        Sx: 0.577,
      };

      const result = calculateNominalShearStrength(minimalAngle, Fy);

      // Should still work with minimal properties
      const expectedVn = 0.6 * Fy * minimalAngle.A; // 0.6 * 50 * 1.44 = 43.2 kips
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
    });
  });

  describe("Router Functionality", () => {
    it("should call calculateVn_I_ShapeWeb for W-shape sections", () => {
      const result = calculateNominalShearStrength(W18X50, Fy);

      // Verify it returns a valid result (indicates I-shape function was called)
      expect(result.Vn).toBeGreaterThan(0);
      expect(result.governingLimitState).toBeDefined();
    });

    it("should call calculateVn_HSSWeb for HSS-RECT sections", () => {
      const result = calculateNominalShearStrength(HSS8X4X5_8, Fy);

      // Verify it returns a valid result (indicates HSS function was called)
      expect(result.Vn).toBeGreaterThan(0);
      expect(result.governingLimitState).toBeDefined();
    });

    it("should throw an error for unsupported section type", () => {
      const unsupportedSection: Section = {
        ...W18X50,
        Type: "BUILT-UP" as any,
      };

      expect(() =>
        calculateNominalShearStrength(unsupportedSection, Fy)
      ).toThrow(
        "Shear strength calculation for section type 'BUILT-UP' is not implemented."
      );
    });

    it("should throw an error for negative or zero yield strength", () => {
      expect(() => calculateNominalShearStrength(W18X50, 0)).toThrow(
        "Yield strength (Fy) must be positive."
      );

      expect(() => calculateNominalShearStrength(W18X50, -10)).toThrow(
        "Yield strength (Fy) must be positive."
      );
    });
  });

  describe("Invalid Inputs", () => {
    it("should throw error for I-shape section missing required properties", () => {
      const invalidSection1 = { ...W18X50, d: undefined };
      const invalidSection2 = { ...W18X50, tw: undefined };
      const invalidSection3 = { ...W18X50, tf: undefined };

      expect(() =>
        calculateNominalShearStrength(invalidSection1 as any, Fy)
      ).toThrow(
        "Section must contain numeric 'd', 'tw', and 'tf' properties for shear calculation."
      );

      expect(() =>
        calculateNominalShearStrength(invalidSection2 as any, Fy)
      ).toThrow(
        "Section must contain numeric 'd', 'tw', and 'tf' properties for shear calculation."
      );

      expect(() =>
        calculateNominalShearStrength(invalidSection3 as any, Fy)
      ).toThrow(
        "Section must contain numeric 'd', 'tw', and 'tf' properties for shear calculation."
      );
    });

    it("should throw error for HSS section missing required properties", () => {
      const invalidHSS1 = { ...HSS8X4X5_8, d: undefined };
      const invalidHSS2 = { ...HSS8X4X5_8, tw: undefined };

      expect(() =>
        calculateNominalShearStrength(invalidHSS1 as any, Fy)
      ).toThrow(
        "Section must contain numeric 'd' and 'tw' properties for HSS shear calculation."
      );

      expect(() =>
        calculateNominalShearStrength(invalidHSS2 as any, Fy)
      ).toThrow(
        "Section must contain numeric 'd' and 'tw' properties for HSS shear calculation."
      );
    });
  });

  describe("Manual Verification", () => {
    it("should verify shear yielding calculation manually", () => {
      // Test case where we know shear yielding governs
      const testSection: Section = {
        name: "ManualTest",
        Type: "W",
        A: 10.0,
        d: 12.0,
        tw: 0.5,
        bf: 8.0,
        tf: 0.75,
        Ix: 400,
        Zx: 70,
        Sx: 67,
        rx: 6.3,
        Iy: 50,
        Zy: 12,
        Sy: 12.5,
        ry: 2.2,
        J: 2.0,
        Cw: 800,
      };

      const result = calculateNominalShearStrength(testSection, Fy);

      // Manual calculation
      const h = testSection.d - 2 * (testSection.tf || 0); // 12 - 1.5 = 10.5
      const h_tw = h / testSection.tw; // 10.5 / 0.5 = 21
      const lambda_limit = 2.24 * Math.sqrt(29000 / Fy); // ≈ 53.96

      // Since h_tw (21) < lambda_limit (53.96), should be shear yielding
      const Aw = testSection.d * testSection.tw; // 12 * 0.5 = 6.0
      const expectedVn = 0.6 * Fy * Aw; // 0.6 * 50 * 6.0 = 180 kips

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should verify shear buckling calculation manually", () => {
      // Create section that definitely has shear buckling
      const slenderSection: Section = {
        name: "VerySlender",
        Type: "W",
        A: 8.0,
        d: 24.0,
        tw: 0.08, // Very thin web
        bf: 6.0,
        tf: 0.4,
        Ix: 600,
        Zx: 50,
        Sx: 50,
        rx: 8.7,
        Iy: 20,
        Zy: 6.7,
        Sy: 6.7,
        ry: 1.6,
        J: 0.5,
        Cw: 400,
      };

      const result = calculateNominalShearStrength(slenderSection, Fy);

      // Manual calculation
      const h = slenderSection.d - 2 * (slenderSection.tf || 0); // 24 - 0.8 = 23.2
      const h_tw = h / slenderSection.tw; // 23.2 / 0.08 = 290
      const lambda_limit = 2.24 * Math.sqrt(29000 / Fy); // ≈ 53.96

      // Since h_tw (290) >> lambda_limit (53.96), should be shear buckling
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);

      // Should be significantly less than shear yielding capacity
      const Aw = slenderSection.d * slenderSection.tw;
      const yieldingCapacity = 0.6 * Fy * Aw;
      expect(result.Vn).toBeLessThan(yieldingCapacity);
      expect(result.Vn).toBeGreaterThan(0);
    });
  });
});
