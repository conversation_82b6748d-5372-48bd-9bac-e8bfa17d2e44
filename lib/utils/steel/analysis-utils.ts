/**
 * @file This file contains utility functions for steel analysis including resistance factors,
 * available strength calculations, and demand-to-capacity ratios in accordance with ANSI/AISC 360-22.
 */

import {
  DesignMethod,
  LimitStateType,
  SectionType,
  RESISTANCE_FACTOR_TABLE,
  ERROR_MESSAGES,
  ANALYSIS_CONSTANTS,
  type ResistanceFactors,
} from "./constants";

/**
 * Retrieves the correct resistance factor (φ) or safety factor (Ω) based on the design method,
 * limit state type, and section type.
 * @param {LimitStateType} limitStateType The type of limit state ('flexure', 'shear_yielding', 'shear_buckling').
 * @param {SectionType | string} sectionType The section type (e.g., 'W', 'HSS-RECT').
 * @param {DesignMethod} designMethod The design method ('LRFD' or 'ASD').
 * @returns {number} The resistance factor (φ) for LRFD or safety factor (Ω) for ASD.
 * @throws {Error} if invalid inputs are provided.
 * @source AISC 360-22, Sections F1 and G1
 */
export function getResistanceFactor(
  limitStateType: LimitStateType | string,
  sectionType: SectionType | string,
  designMethod: DesignMethod | string
): number {
  // Validate design method
  if (!Object.values(DesignMethod).includes(designMethod as DesignMethod)) {
    throw new Error(ERROR_MESSAGES.INVALID_DESIGN_METHOD(designMethod));
  }

  // Validate limit state type
  if (
    !Object.values(LimitStateType).includes(limitStateType as LimitStateType)
  ) {
    throw new Error(ERROR_MESSAGES.INVALID_LIMIT_STATE_TYPE(limitStateType));
  }

  // Get resistance factors for the limit state
  const limitStateFactors =
    RESISTANCE_FACTOR_TABLE[limitStateType as LimitStateType];

  // For now, all section types use the same factors for each limit state
  // This could be expanded in the future if AISC introduces section-specific factors
  const factors =
    limitStateFactors.default ||
    limitStateFactors[sectionType] ||
    limitStateFactors.default;

  if (!factors) {
    throw new Error(ERROR_MESSAGES.NO_RESISTANCE_FACTORS(limitStateType, sectionType));
  }

  // Return appropriate factor based on design method
  if (designMethod === DesignMethod.LRFD) {
    return factors.phi;
  } else if (designMethod === DesignMethod.ASD) {
    return factors.omega;
  } else {
    throw new Error(ERROR_MESSAGES.INVALID_DESIGN_METHOD(designMethod));
  }
}

/**
 * Calculates the available strength by applying the appropriate resistance or safety factor
 * to the nominal strength based on the design method.
 * @param {number} nominalStrength The nominal strength (Mn, Vn, etc.) in appropriate units.
 * @param {number} factor The resistance factor (φ) for LRFD or safety factor (Ω) for ASD.
 * @param {DesignMethod} designMethod The design method ('LRFD' or 'ASD').
 * @returns {number} The available strength in the same units as nominalStrength.
 * @throws {Error} if invalid inputs are provided.
 * @source AISC 360-22, Chapter B - Fundamental definitions of LRFD and ASD
 */
export function calculateAvailableStrength(
  nominalStrength: number,
  factor: number,
  designMethod: DesignMethod | string
): number {
  // Validate design method
  if (!Object.values(DesignMethod).includes(designMethod as DesignMethod)) {
    throw new Error(ERROR_MESSAGES.INVALID_DESIGN_METHOD(designMethod));
  }

  // Validate inputs
  if (nominalStrength < 0) {
    throw new Error(ERROR_MESSAGES.NOMINAL_STRENGTH_NON_NEGATIVE);
  }

  if (factor < 0) {
    throw new Error(ERROR_MESSAGES.FACTOR_NON_NEGATIVE);
  }

  // Handle special cases
  if (nominalStrength === 0) {
    return 0;
  }

  if (designMethod === DesignMethod.LRFD) {
    // LRFD: Available strength = φ × Nominal strength
    return nominalStrength * factor;
  } else if (designMethod === DesignMethod.ASD) {
    // ASD: Available strength = Nominal strength / Ω
    if (factor === 0) {
      throw new Error(ERROR_MESSAGES.SAFETY_FACTOR_ZERO_ASD);
    }
    return nominalStrength / factor;
  } else {
    throw new Error(ERROR_MESSAGES.INVALID_DESIGN_METHOD(designMethod));
  }
}

/**
 * Calculates the Demand-to-Capacity Ratio (DCR), which is the ratio of applied demand
 * to available capacity. A DCR ≤ 1.0 indicates adequate capacity.
 * @param {number} demand The applied demand (moment, shear, etc.) in appropriate units.
 * @param {number} capacity The available capacity in the same units as demand.
 * @returns {number} The demand-to-capacity ratio. Returns Infinity if capacity is zero.
 * @source Fundamental engineering principle for design checks
 */
export function calculateDCR(demand: number, capacity: number): number {
  // Validate inputs
  if (demand < 0) {
    throw new Error(ERROR_MESSAGES.DEMAND_NON_NEGATIVE);
  }

  if (capacity < 0) {
    throw new Error(ERROR_MESSAGES.CAPACITY_NON_NEGATIVE);
  }

  // Handle special cases
  if (demand === 0) {
    return 0;
  }

  if (capacity === 0) {
    return Infinity;
  }

  return demand / capacity;
}

/**
 * Interface for a comprehensive design check result.
 */
export interface DesignCheckResult {
  nominalStrength: number;
  resistanceFactor: number;
  availableStrength: number;
  demand: number;
  dcr: number;
  adequate: boolean;
  designMethod: DesignMethod;
  limitStateType: LimitStateType;
}

/**
 * Performs a complete design check for a given limit state.
 * @param {number} nominalStrength The nominal strength (Mn, Vn, etc.).
 * @param {number} demand The applied demand.
 * @param {LimitStateType} limitStateType The limit state being checked.
 * @param {SectionType | string} sectionType The section type.
 * @param {DesignMethod} designMethod The design method.
 * @returns {DesignCheckResult} Complete design check results.
 * @source AISC 360-22, fundamental design check procedure
 */
export function performDesignCheck(
  nominalStrength: number,
  demand: number,
  limitStateType: LimitStateType | string,
  sectionType: SectionType | string,
  designMethod: DesignMethod | string
): DesignCheckResult {
  // Get the appropriate resistance factor
  const resistanceFactor = getResistanceFactor(
    limitStateType,
    sectionType,
    designMethod
  );

  // Calculate available strength
  const availableStrength = calculateAvailableStrength(
    nominalStrength,
    resistanceFactor,
    designMethod
  );

  // Calculate DCR
  const dcr = calculateDCR(demand, availableStrength);

  // Check adequacy (DCR ≤ 1.0 is adequate)
  const adequate = dcr <= ANALYSIS_CONSTANTS.DCR_ADEQUACY_THRESHOLD;

  return {
    nominalStrength,
    resistanceFactor,
    availableStrength,
    demand,
    dcr,
    adequate,
    designMethod: designMethod as DesignMethod,
    limitStateType: limitStateType as LimitStateType,
  };
}
