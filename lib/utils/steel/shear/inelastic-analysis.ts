/**
 * @file Inelastic Analysis Support for Shear Strength Calculations
 *
 * Implements AISC 360-22 Appendix 1.3 "Design by Inelastic Analysis"
 * requirements for stiffness reduction in shear calculations.
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 1.3
 */

import { DesignMethod, E, G } from "../constants";
import { calculateTauB } from "../stiffness-adjustment-utils";
import { Section } from "../slenderness/types";
import { InelasticAnalysisOptions, DuctilityValidationResult } from "./types";

// Re-export types for external use
export type { DuctilityValidationResult } from "./types";

/**
 * Interface for reduced material properties for inelastic analysis.
 */
export interface ReducedMaterialProperties {
  /** Reduced modulus of elasticity, ksi */
  E_reduced: number;
  /** Reduced shear modulus, ksi */
  G_reduced: number;
  /** Original modulus of elasticity, ksi */
  E_original: number;
  /** Original shear modulus, ksi */
  G_original: number;
  /** Stiffness reduction factor applied */
  reductionFactor: number;
  /** τb parameter used in calculation */
  tauB: number;
}

/**
 * Calculates reduced material properties for inelastic analysis per AISC Appendix 1.3.
 *
 * The user note states: "One practical method of including stiffness reduction is to
 * reduce E and G by 0.8τb, thereby leaving all cross-section geometric properties
 * at their nominal value."
 *
 * @param options - Inelastic analysis options
 * @param E_original - Original modulus of elasticity, ksi (defaults to AISC standard)
 * @param G_original - Original shear modulus, ksi (defaults to AISC standard)
 * @returns Reduced material properties
 *
 * @source AISC 360-22, Appendix 1.3, User Note
 */
export function calculateReducedMaterialProperties(
  options: InelasticAnalysisOptions,
  E_original: number = E,
  G_original: number = G
): ReducedMaterialProperties {
  // Validate inputs
  if (!options.applyStiffnessReduction) {
    return {
      E_reduced: E_original,
      G_reduced: G_original,
      E_original,
      G_original,
      reductionFactor: 1.0,
      tauB: 1.0,
    };
  }

  let reductionFactor: number;
  let tauB: number;

  if (options.customReductionFactor !== undefined) {
    // Use custom reduction factor
    reductionFactor = options.customReductionFactor;
    tauB = 1.0; // Not applicable for custom factor

    if (reductionFactor <= 0 || reductionFactor > 1.0) {
      throw new Error("Custom reduction factor must be between 0 and 1.0");
    }
  } else {
    // Calculate 0.8τb per AISC Appendix 1.3
    if (options.Pr === undefined || !options.Pn || !options.designMethod) {
      throw new Error(
        "Pr, Pn, and designMethod are required when not using custom reduction factor"
      );
    }

    // Calculate τb per AISC Section C2.3
    tauB = calculateTauB(options.Pr, options.Pn, options.designMethod);

    // Base reduction factor per AISC Appendix 1.3
    const baseReduction = 0.8;
    reductionFactor = baseReduction * tauB;
  }

  // Apply reduction to moduli per AISC Appendix 1.3 User Note
  const E_reduced = E_original * reductionFactor;
  const G_reduced = G_original * reductionFactor;

  return {
    E_reduced,
    G_reduced,
    E_original,
    G_original,
    reductionFactor,
    tauB,
  };
}

/**
 * Determines if inelastic analysis stiffness reduction should be applied.
 *
 * Per AISC Appendix 1.3: "Stiffness reduction should be applied to all member
 * properties including torsional properties (GJ and ECw) affecting twist of
 * the member cross section."
 *
 * @param options - Inelastic analysis options
 * @returns True if stiffness reduction should be applied
 *
 * @source AISC 360-22, Appendix 1.3
 */
export function shouldApplyStiffnessReduction(
  options?: InelasticAnalysisOptions
): boolean {
  if (!options) return false;
  return options.applyStiffnessReduction === true;
}

/**
 * Validates inelastic analysis options for consistency and completeness.
 *
 * @param options - Inelastic analysis options to validate
 * @throws Error if options are invalid or incomplete
 */
export function validateInelasticAnalysisOptions(
  options: InelasticAnalysisOptions
): void {
  if (options.applyStiffnessReduction) {
    if (options.customReductionFactor === undefined) {
      // Validate required parameters for τb calculation
      if (options.Pr === undefined) {
        throw new Error(
          "Required axial strength (Pr) is required for stiffness reduction"
        );
      }
      if (options.Pn === undefined) {
        throw new Error(
          "Nominal axial strength (Pn) is required for stiffness reduction"
        );
      }
      if (options.designMethod === undefined) {
        throw new Error("Design method is required for stiffness reduction");
      }
      if (options.Pr < 0) {
        throw new Error("Required axial strength (Pr) must be non-negative");
      }
      if (options.Pn <= 0) {
        throw new Error("Nominal axial strength (Pn) must be positive");
      }
    } else {
      // Validate custom reduction factor
      if (
        options.customReductionFactor <= 0 ||
        options.customReductionFactor > 1.0
      ) {
        throw new Error("Custom reduction factor must be between 0 and 1.0");
      }
    }
  }
}

/**
 * Provides guidance on inelastic analysis considerations for shear design.
 *
 * @returns Object with analysis considerations and recommendations
 *
 * @source AISC 360-22, Appendix 1.3
 */
export function getInelasticAnalysisGuidance(): {
  considerations: string[];
  recommendations: string[];
  limitations: string[];
} {
  return {
    considerations: [
      "Inelastic analysis requires second-order elastic analysis with direct modeling of imperfections",
      "Stiffness reduction (0.8τb) should be applied to E and G while keeping geometric properties nominal",
      "Reduction should be applied to ALL members, including those not contributing to stability",
      "Torsional properties (GJ and ECw) should also be reduced for members affecting twist",
      "Available strengths calculated per Chapters D-K with no further stability considerations",
    ],
    recommendations: [
      "Apply stiffness reduction consistently to avoid artificial force redistribution",
      "Use cross-section compressive strength (FyAg) for members with adequate lateral support",
      "Consider FyAe for members with slender elements per Section E7",
      "Verify that second-order analysis includes all relevant imperfections",
      "Document stiffness reduction assumptions clearly in analysis",
    ],
    limitations: [
      "Only applicable when using second-order analysis with direct imperfection modeling",
      "Not applicable to first-order analysis or effective length methods",
      "Requires careful consideration of which members contribute to stability",
      "May not be suitable for highly indeterminate structures without proper analysis",
      "Stiffness reduction factors may need iteration for convergence",
    ],
  };
}

/**
 * Calculates adjusted shear modulus for specific applications.
 * This is useful when different reduction factors apply to different properties.
 *
 * @param G_original - Original shear modulus, ksi
 * @param reductionFactor - Reduction factor to apply
 * @returns Reduced shear modulus, ksi
 */
export function applyShearModulusReduction(
  G_original: number,
  reductionFactor: number
): number {
  if (G_original <= 0) {
    throw new Error("Original shear modulus must be positive");
  }
  if (reductionFactor <= 0 || reductionFactor > 1.0) {
    throw new Error("Reduction factor must be between 0 and 1.0");
  }

  return G_original * reductionFactor;
}

/**
 * Determines nominal compressive strength for inelastic analysis.
 * 
 * Per AISC Appendix 1.3: "The nominal compressive strength of members, Pn, 
 * may be taken as the cross-section compressive strength, FyAg, or as FyAe 
 * for members with slender elements."
 * 
 * @param Fy - Yield strength, ksi
 * @param Ag - Gross area, in²
 * @param Ae - Effective area for slender elements, in² (optional)
 * @param hasSlenderElements - Whether member has slender elements
 * @returns Nominal compressive strength for inelastic analysis, kips
 * 
 * @source AISC 360-22, Appendix 1.3, Section 3
 */
export function calculateInelasticAnalysisPn(
  Fy: number,
  Ag: number,
  Ae?: number,
  hasSlenderElements: boolean = false
): number {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  if (Ag <= 0) {
    throw new Error("Gross area (Ag) must be positive");
  }
  
  if (hasSlenderElements) {
    if (Ae === undefined || Ae <= 0) {
      throw new Error(
        "Effective area (Ae) is required and must be positive for slender elements"
      );
    }
    return Fy * Ae;
  } else {
    return Fy * Ag;
  }
}

/**
 * Validates material requirements for inelastic analysis per AISC Appendix 1.3, Section 2a.
 * 
 * The specified minimum yield stress, Fy, of members subject to plastic hinging 
 * shall not exceed 65 ksi (450 MPa).
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @param subjectToPlasticHinging - Whether member is subject to plastic hinging
 * @returns Whether material requirements are satisfied
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2a
 */
export function validateMaterialRequirements(
  Fy: number,
  subjectToPlasticHinging: boolean = true
): boolean {
  if (!subjectToPlasticHinging) {
    return true; // No restriction if not subject to plastic hinging
  }
  return Fy <= 65.0; // 65 ksi (450 MPa) limit per AISC A1.3.2a
}

/**
 * Calculates web slenderness limit (λpd) for plastic hinging per AISC Appendix 1.3, Section 2b.
 * 
 * For webs of I-shaped members, rectangular HSS, and box sections subjected to 
 * combined flexure and compression.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @param Pu - Required axial strength in compression using LRFD, kips
 * @param Py - Axial yield strength (FyAg), kips
 * @param phiC - Resistance factor for compression (default 0.90)
 * @returns Web slenderness limit λpd
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2b, Equations A-1-1 and A-1-2
 */
export function calculateWebSlendernessLimit(
  Fy: number,
  Pu: number,
  Py: number,
  phiC: number = 0.90
): number {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  if (Pu < 0) {
    throw new Error("Required axial strength (Pu) must be non-negative");
  }
  if (Py <= 0) {
    throw new Error("Axial yield strength (Py) must be positive");
  }
  if (phiC <= 0 || phiC > 1.0) {
    throw new Error("Resistance factor (φc) must be between 0 and 1.0");
  }

  const ratio = Pu / (phiC * Py);

  if (ratio <= 0.125) {
    // Equation A-1-1: When Pu/(φcPy) ≤ 0.125
    const term1 = Math.sqrt(E / Fy);
    const term2 = 1 - (2.75 * Pu) / (phiC * Py);
    return 3.76 * term1 * term2;
  } else {
    // Equation A-1-2: When Pu/(φcPy) > 0.125
    const term1 = Math.sqrt(E / Fy);
    const term2 = 2.33 - Pu / (phiC * Py);
    const calculated = 1.12 * term1 * term2;
    const minimum = 1.49 * Math.sqrt(E / Fy);
    return Math.max(calculated, minimum);
  }
}

/**
 * Calculates flange slenderness limit (λpd) for rectangular HSS and box sections 
 * per AISC Appendix 1.3, Section 2b.
 * 
 * For flanges of rectangular HSS and box sections, and for flange cover plates.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @returns Flange slenderness limit λpd
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2b, Equation A-1-3
 */
export function calculateFlangeSlendernessLimit(Fy: number): number {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  return 0.94 * Math.sqrt(E / Fy);
}

/**
 * Calculates slenderness limit (λpd) for round HSS in flexure 
 * per AISC Appendix 1.3, Section 2b.
 * 
 * For the diameter-to-thickness ratio, D/t, of round HSS in flexure.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @returns D/t slenderness limit λpd
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2b, Equation A-1-4
 */
export function calculateRoundHSSSlendernessLimit(Fy: number): number {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  return 0.045 * E / Fy;
}

/**
 * Validates cross-section requirements for plastic hinging per AISC Appendix 1.3, Section 2b.
 * 
 * Cross sections at plastic hinge locations shall be doubly symmetric with 
 * width-to-thickness ratios not exceeding λpd values.
 * 
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress, ksi
 * @param options - Additional parameters for validation
 * @returns Comprehensive ductility validation result
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2b
 */
export function validateCrossSectionRequirements(
  section: Section,
  Fy: number,
  options: {
    Pu?: number;
    Py?: number;
    phiC?: number;
    subjectToPlasticHinging?: boolean;
  } = {}
): DuctilityValidationResult {
  const {
    Pu = 0,
    Py,
    phiC = 0.90,
    subjectToPlasticHinging = true,
  } = options;

  const result: DuctilityValidationResult = {
    materialCompliant: validateMaterialRequirements(Fy, subjectToPlasticHinging),
    doublySymmetric: isDoublySymmetric(section),
    overallCompliant: false,
    complianceMessages: [],
    warnings: [],
  };

  // Material compliance check
  if (!result.materialCompliant) {
    result.complianceMessages.push(
      `Material non-compliant: Fy = ${Fy} ksi exceeds 65 ksi limit for plastic hinging`
    );
  }

  // Doubly symmetric check
  if (!result.doublySymmetric) {
    result.complianceMessages.push(
      "Cross-section must be doubly symmetric for plastic hinging locations"
    );
  }

  // Skip slenderness checks if not subject to plastic hinging
  if (!subjectToPlasticHinging) {
    result.overallCompliant = result.materialCompliant && result.doublySymmetric;
    if (result.overallCompliant) {
      result.complianceMessages.push("Section compliant - not subject to plastic hinging");
    }
    return result;
  }

  // Web slenderness validation for I-shapes, rectangular HSS, and box sections
  if (isIShapeOrRectangularHSS(section)) {
    if (Py === undefined) {
      result.warnings.push("Py (axial yield strength) not provided - cannot validate web slenderness");
    } else {
      result.webSlendernessLimit = calculateWebSlendernessLimit(Fy, Pu, Py, phiC);
      result.actualWebSlenderness = calculateActualWebSlenderness(section);
      result.webSlendernessCompliant = result.actualWebSlenderness <= result.webSlendernessLimit;

      if (!result.webSlendernessCompliant) {
        result.complianceMessages.push(
          `Web slenderness non-compliant: h/tw = ${result.actualWebSlenderness.toFixed(2)} > λpd = ${result.webSlendernessLimit.toFixed(2)}`
        );
      }
    }
  }

  // Flange slenderness validation for rectangular HSS and box sections
  if (isRectangularHSSOrBox(section)) {
    result.flangeSlendernessLimit = calculateFlangeSlendernessLimit(Fy);
    result.actualFlangeSlenderness = calculateActualFlangeSlenderness(section);
    result.flangeSlendernessCompliant = result.actualFlangeSlenderness <= result.flangeSlendernessLimit;

    if (!result.flangeSlendernessCompliant) {
      result.complianceMessages.push(
        `Flange slenderness non-compliant: b/t = ${result.actualFlangeSlenderness.toFixed(2)} > λpd = ${result.flangeSlendernessLimit.toFixed(2)}`
      );
    }
  }

  // Round HSS slenderness validation
  if (isRoundHSS(section)) {
    result.flangeSlendernessLimit = calculateRoundHSSSlendernessLimit(Fy);
    result.actualFlangeSlenderness = calculateActualRoundHSSSlenderness(section);
    result.flangeSlendernessCompliant = result.actualFlangeSlenderness <= result.flangeSlendernessLimit;

    if (!result.flangeSlendernessCompliant) {
      result.complianceMessages.push(
        `Round HSS slenderness non-compliant: D/t = ${result.actualFlangeSlenderness.toFixed(2)} > λpd = ${result.flangeSlendernessLimit.toFixed(2)}`
      );
    }
  }

  // Overall compliance
  result.overallCompliant = 
    result.materialCompliant &&
    result.doublySymmetric &&
    (result.webSlendernessCompliant !== false) &&
    (result.flangeSlendernessCompliant !== false);

  if (result.overallCompliant) {
    result.complianceMessages.push("Section fully compliant with inelastic analysis ductility requirements");
  }

  return result;
}

// Helper functions for section type identification and slenderness calculations

function isDoublySymmetric(section: Section): boolean {
  // For standard AISC shapes, most are doubly symmetric
  // This is a simplified check - in practice would need more detailed geometry
  const type = section.Type.toUpperCase();
  
  // W, I, S, M, HP shapes are doubly symmetric
  if (['W', 'I', 'S', 'M', 'HP'].includes(type)) {
    return true;
  }
  
  // Rectangular HSS are doubly symmetric
  if (type === 'HSS-RECT') {
    return true;
  }
  
  // Round HSS are doubly symmetric
  if (type === 'HSS-ROUND') {
    return true;
  }
  
  // Channels, angles, and tees are not doubly symmetric
  if (['C', 'MC', 'L', 'WT', 'MT', 'ST'].includes(type)) {
    return false;
  }
  
  return false; // Conservative assumption for unknown types
}

function isIShapeOrRectangularHSS(section: Section): boolean {
  const type = section.Type.toUpperCase();
  return ['W', 'I', 'S', 'M', 'HP', 'HSS-RECT'].includes(type);
}

function isRectangularHSSOrBox(section: Section): boolean {
  const type = section.Type.toUpperCase();
  return type === 'HSS-RECT'; // Box sections would be similar
}

function isRoundHSS(section: Section): boolean {
  return section.Type.toUpperCase() === 'HSS-ROUND';
}

function calculateActualWebSlenderness(section: Section): number {
  if (!section.tf || !section.tw) {
    throw new Error("Flange thickness (tf) and web thickness (tw) required for web slenderness");
  }
  
  const h = section.d - 2 * section.tf; // Clear distance between flanges
  return h / section.tw;
}

function calculateActualFlangeSlenderness(section: Section): number {
  if (!section.bf || !section.tf) {
    throw new Error("Flange width (bf) and thickness (tf) required for flange slenderness");
  }
  
  // For rectangular HSS: b/t where b is clear width
  // Assuming bf is outside width, so clear width is approximately bf - 2*tw
  const tw = section.tw || section.tf; // Use tw if available, otherwise tf
  const b = section.bf - 2 * tw;
  return b / section.tf;
}

function calculateActualRoundHSSSlenderness(section: Section): number {
  if (!section.tw) {
    throw new Error("Wall thickness (tw) required for round HSS slenderness");
  }
  
  // D/t ratio for round HSS
  return section.d / section.tw;
}

/**
 * Calculates effective moment M1' for unbraced length calculations per AISC Appendix 1.3, Section 2c.
 * 
 * The effective moment M1' depends on the moment distribution and is used in 
 * calculating the laterally unbraced length limit Lpd for plastic hinges.
 * 
 * @param M1 - Smaller moment at end of unbraced length, kip-in (taken as positive)
 * @param M2 - Larger moment at end of unbraced length, kip-in (taken as positive) 
 * @param Mmid - Moment at middle of unbraced length, kip-in
 * @returns Effective moment M1' for use in Lpd calculations
 * 
 * @source AISC 360-22, Appendix 1.3, Equations A-1-6a, A-1-6b, A-1-6c
 */
export function calculateEffectiveMoment(
  M1: number,
  M2: number,
  Mmid: number
): number {
  // Ensure M2 is the larger moment (taken as positive in all cases)
  const M2_abs = Math.abs(M2);
  const M1_abs = Math.abs(M1);
  const Mmid_abs = Math.abs(Mmid);
  
  // Order moments so M2 >= M1
  const M_larger = Math.max(M2_abs, M1_abs);
  const M_smaller = Math.min(M2_abs, M1_abs);
  
  // (1) When the magnitude of bending moment at any location within 
  // the unbraced length exceeds M2
  if (Mmid_abs > M_larger) {
    // AISC Eq. A-1-6a: M1' / M2 = +1
    return M_larger; // M1' = M2
  }
  
  // (2) When Mmid ≤ (M1 + M2) / 2  
  else if (Mmid_abs <= (M_smaller + M_larger) / 2) {
    // AISC Eq. A-1-6b: M1' = M1
    return M_smaller;
  }
  
  // (3) When Mmid > (M1 + M2) / 2
  else {
    // AISC Eq. A-1-6c: M1' = (2*Mmid - M2) < M2
    const M1_prime = Math.min(2 * Mmid_abs - M_larger, M_larger);
    return M1_prime;
  }
}

/**
 * Calculates laterally unbraced length limit (Lpd) for I-shaped members with plastic hinges 
 * per AISC Appendix 1.3, Section 2c.
 * 
 * For I-shaped members bent about their major axis in segments containing plastic hinges.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @param ry - Radius of gyration about minor axis, in
 * @param M1_prime - Effective moment at end opposite from M2, kip-in
 * @param M2 - Larger moment at end of unbraced length, kip-in (taken as positive)
 * @returns Laterally unbraced length limit Lpd, inches
 * 
 * @source AISC 360-22, Appendix 1.3, Equation A-1-5
 */
export function calculateLpd_I_Shape(
  Fy: number,
  ry: number,
  M1_prime: number,
  M2: number
): number {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  if (ry <= 0) {
    throw new Error("Radius of gyration (ry) must be positive");
  }
  if (M2 <= 0) {
    throw new Error("Larger end moment (M2) must be positive");
  }
  
  // AISC Eq. A-1-5: Lpd = (0.12 - 0.076 * M1'/M2) * (E/Fy) * ry
  const moment_ratio = M1_prime / M2;
  const Lpd = (0.12 - 0.076 * moment_ratio) * (E / Fy) * ry;
  
  return Math.max(Lpd, 0); // Ensure non-negative result
}

/**
 * Calculates laterally unbraced length limit (Lpd) for rectangular bars and rectangular HSS/box sections 
 * with plastic hinges per AISC Appendix 1.3, Section 2c.
 * 
 * For solid rectangular bars and rectangular HSS and box sections bent about their major axis.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @param ry - Radius of gyration about minor axis, in
 * @param M1_prime - Effective moment at end opposite from M2, kip-in
 * @param M2 - Larger moment at end of unbraced length, kip-in (taken as positive)
 * @returns Laterally unbraced length limit Lpd, inches
 * 
 * @source AISC 360-22, Appendix 1.3, Equation A-1-7
 */
export function calculateLpd_RectangularSection(
  Fy: number,
  ry: number,
  M1_prime: number,
  M2: number
): number {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  if (ry <= 0) {
    throw new Error("Radius of gyration (ry) must be positive");
  }
  if (M2 <= 0) {
    throw new Error("Larger end moment (M2) must be positive");
  }
  
  // AISC Eq. A-1-7: Lpd = (0.17 - 0.10 * M1'/M2) * (E/Fy) * ry ≥ 0.10 * (E/Fy) * ry
  const moment_ratio = M1_prime / M2;
  const calculated = (0.17 - 0.10 * moment_ratio) * (E / Fy) * ry;
  const minimum = 0.10 * (E / Fy) * ry;
  
  return Math.max(calculated, minimum);
}

/**
 * Calculates unbraced length limits for members under axial compression with plastic hinges 
 * per AISC Appendix 1.3, Section 2c.
 * 
 * For all types of members subjected to axial compression and containing plastic hinges,
 * the laterally unbraced lengths about both major and minor axes shall not exceed these limits.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @param rx - Radius of gyration about major axis, in
 * @param ry - Radius of gyration about minor axis, in
 * @returns Object with unbraced length limits for major and minor axes
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2c
 */
export function calculateAxialCompressionUnbracedLengthLimits(
  Fy: number,
  rx: number,
  ry: number
): {
  majorAxisLimit: number;
  minorAxisLimit: number;
} {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  if (rx <= 0) {
    throw new Error("Major axis radius of gyration (rx) must be positive");
  }
  if (ry <= 0) {
    throw new Error("Minor axis radius of gyration (ry) must be positive");
  }
  
  // For members under axial compression: Lb ≤ 4.71 * r * √(E/Fy)
  const baseLimit = 4.71 * Math.sqrt(E / Fy);
  
  return {
    majorAxisLimit: baseLimit * rx,
    minorAxisLimit: baseLimit * ry,
  };
}

/**
 * Determines if unbraced length limits apply for a given loading condition 
 * per AISC Appendix 1.3, Section 2c.
 * 
 * There is no Lpd limit for certain member conditions.
 * 
 * @param section - Cross-section properties
 * @param loadingCondition - Type of loading on the member
 * @param bendingAxis - Axis about which bending occurs
 * @returns Whether unbraced length limits apply
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2c
 */
export function unbracedLengthLimitsApply(
  section: Section,
  loadingCondition: 'flexure-only' | 'flexure-tension' | 'flexure-compression' | 'tension-only',
  bendingAxis: 'major' | 'minor' = 'major'
): boolean {
  const sectionType = section.Type.toUpperCase();
  
  // No Lpd limit for the following cases:
  
  // (a) Members with round or square cross sections subjected only to flexure 
  // or to combined flexure and tension
  if ((sectionType === 'HSS-ROUND' || 
       (sectionType === 'HSS-RECT' && section.bf === section.d)) &&
      (loadingCondition === 'flexure-only' || loadingCondition === 'flexure-tension')) {
    return false;
  }
  
  // (b) Members subjected only to flexure about their minor axis or 
  // combined tension and flexure about their minor axis
  if (bendingAxis === 'minor' && 
      (loadingCondition === 'flexure-only' || loadingCondition === 'flexure-tension')) {
    return false;
  }
  
  // (c) Members subjected only to tension
  if (loadingCondition === 'tension-only') {
    return false;
  }
  
  // In all other cases, unbraced length limits apply
  return true;
}

/**
 * Interface for unbraced length validation results.
 */
export interface UnbracedLengthValidation {
  /** Whether unbraced length limits apply for this member */
  limitsApply: boolean;
  /** Calculated Lpd limit if applicable, inches */
  Lpd?: number;
  /** Actual unbraced length provided, inches */
  Lb_actual: number;
  /** Whether unbraced length is compliant */
  compliant: boolean;
  /** Major axis unbraced length limit for compression members, inches */
  majorAxisLimit?: number;
  /** Minor axis unbraced length limit for compression members, inches */
  minorAxisLimit?: number;
  /** Compliance messages */
  messages: string[];
  /** Type of section analyzed */
  sectionType: string;
  /** Loading condition analyzed */
  loadingCondition: string;
}

/**
 * Validates unbraced length requirements for members with plastic hinges 
 * per AISC Appendix 1.3, Section 2c.
 * 
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress, ksi
 * @param Lb_actual - Actual unbraced length, inches
 * @param loadingCondition - Type of loading on the member
 * @param bendingAxis - Axis about which bending occurs
 * @param moments - Moment values for Lpd calculation (if applicable)
 * @returns Comprehensive unbraced length validation results
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2c
 */
export function validateUnbracedLengthRequirements(
  section: Section,
  Fy: number,
  Lb_actual: number,
  loadingCondition: 'flexure-only' | 'flexure-tension' | 'flexure-compression' | 'tension-only',
  bendingAxis: 'major' | 'minor' = 'major',
  moments?: {
    M1: number;
    M2: number;
    Mmid: number;
  }
): UnbracedLengthValidation {
  const result: UnbracedLengthValidation = {
    limitsApply: unbracedLengthLimitsApply(section, loadingCondition, bendingAxis),
    Lb_actual,
    compliant: true,
    messages: [],
    sectionType: section.Type,
    loadingCondition,
  };
  
  // Check if limits apply
  if (!result.limitsApply) {
    result.messages.push(
      `No unbraced length limit applies for ${section.Type} under ${loadingCondition} with ${bendingAxis} axis bending`
    );
    return result;
  }
  
  // For flexure cases requiring Lpd calculation
  if ((loadingCondition === 'flexure-only' || 
       loadingCondition === 'flexure-tension' || 
       loadingCondition === 'flexure-compression') && 
      bendingAxis === 'major') {
    
    if (!moments) {
      result.messages.push("Moment values (M1, M2, Mmid) required for Lpd calculation");
      result.compliant = false;
      return result;
    }
    
    if (!section.ry) {
      result.messages.push("Minor axis radius of gyration (ry) required for Lpd calculation");
      result.compliant = false;
      return result;
    }
    
    // Calculate effective moment
    const M1_prime = calculateEffectiveMoment(moments.M1, moments.M2, moments.Mmid);
    
    // Calculate Lpd based on section type
    const sectionType = section.Type.toUpperCase();
    
    if (['W', 'I', 'S', 'M', 'HP'].includes(sectionType)) {
      // I-shaped members
      result.Lpd = calculateLpd_I_Shape(Fy, section.ry, M1_prime, Math.abs(moments.M2));
    } else if (['HSS-RECT', 'RECT-BAR'].includes(sectionType)) {
      // Rectangular HSS and bars
      result.Lpd = calculateLpd_RectangularSection(Fy, section.ry, M1_prime, Math.abs(moments.M2));
    } else {
      result.messages.push(`Lpd calculation not implemented for section type: ${sectionType}`);
      result.compliant = false;
      return result;
    }
    
    // Check compliance
    result.compliant = Lb_actual <= result.Lpd;
    
    if (result.compliant) {
      result.messages.push(
        `Unbraced length compliant: Lb = ${Lb_actual.toFixed(1)} in ≤ Lpd = ${result.Lpd.toFixed(1)} in`
      );
    } else {
      result.messages.push(
        `Unbraced length non-compliant: Lb = ${Lb_actual.toFixed(1)} in > Lpd = ${result.Lpd.toFixed(1)} in`
      );
    }
  }
  
  // Additional checks for compression members
  if (loadingCondition === 'flexure-compression') {
    if (!section.rx || !section.ry) {
      result.messages.push("Both rx and ry required for compression member unbraced length limits");
      result.compliant = false;
      return result;
    }
    
    const axialLimits = calculateAxialCompressionUnbracedLengthLimits(Fy, section.rx, section.ry);
    result.majorAxisLimit = axialLimits.majorAxisLimit;
    result.minorAxisLimit = axialLimits.minorAxisLimit;
    
    // Check both axis limits
    const majorAxisCompliant = Lb_actual <= axialLimits.majorAxisLimit;
    const minorAxisCompliant = Lb_actual <= axialLimits.minorAxisLimit;
    
    if (!majorAxisCompliant) {
      result.compliant = false;
      result.messages.push(
        `Major axis unbraced length non-compliant: Lb = ${Lb_actual.toFixed(1)} in > ${axialLimits.majorAxisLimit.toFixed(1)} in`
      );
    }
    
    if (!minorAxisCompliant) {
      result.compliant = false;
      result.messages.push(
        `Minor axis unbraced length non-compliant: Lb = ${Lb_actual.toFixed(1)} in > ${axialLimits.minorAxisLimit.toFixed(1)} in`
      );
    }
    
    if (majorAxisCompliant && minorAxisCompliant) {
      result.messages.push("Compression member unbraced length limits satisfied for both axes");
    }
  }
  
  return result;
}

/**
 * Validates axial force requirements for compression members with plastic hinges 
 * per AISC Appendix 1.3, Section 2d.
 * 
 * To ensure ductility in compression members with plastic hinges, the design strength 
 * in compression shall not exceed 0.75FyA.
 * 
 * @param Fy - Specified minimum yield stress, ksi
 * @param A - Gross area of the section, in²
 * @param Pu_required - Required axial compressive strength, kips
 * @param subjectToPlasticHinging - Whether member is subject to plastic hinging
 * @returns Validation result with compliance status
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2d
 */
export function validateAxialForceRequirements(
  Fy: number,
  A: number,
  Pu_required: number,
  subjectToPlasticHinging: boolean = true
): {
  compliant: boolean;
  allowableStrength: number;
  requiredStrength: number;
  utilizationRatio: number;
  message: string;
} {
  if (Fy <= 0) {
    throw new Error("Yield strength (Fy) must be positive");
  }
  if (A <= 0) {
    throw new Error("Gross area (A) must be positive");
  }
  if (Pu_required < 0) {
    throw new Error("Required axial strength (Pu) must be non-negative");
  }
  
  // If not subject to plastic hinging, this requirement doesn't apply
  if (!subjectToPlasticHinging) {
    return {
      compliant: true,
      allowableStrength: Fy * A, // Standard FyA
      requiredStrength: Pu_required,
      utilizationRatio: Pu_required / (Fy * A),
      message: "Section not subject to plastic hinging - no axial force restriction"
    };
  }
  
  // AISC A1.3.2d: Design strength in compression shall not exceed 0.75FyA
  const allowableStrength = 0.75 * Fy * A;
  const compliant = Pu_required <= allowableStrength;
  const utilizationRatio = Pu_required / allowableStrength;
  
  const message = compliant 
    ? `Axial force compliant: Pu = ${Pu_required.toFixed(1)} kips ≤ 0.75FyA = ${allowableStrength.toFixed(1)} kips`
    : `Axial force non-compliant: Pu = ${Pu_required.toFixed(1)} kips > 0.75FyA = ${allowableStrength.toFixed(1)} kips`;
  
  return {
    compliant,
    allowableStrength,
    requiredStrength: Pu_required,
    utilizationRatio,
    message
  };
}
