import { describe, it, expect } from "@jest/globals";
import { Section } from "../slenderness/types";
import { ShearLimitState } from "../constants";
import { calculateVn_Angle, calculateVn_Angle_Simplified } from "./angle-shear";

describe("Angle Shear Strength (AISC G3)", () => {
  // Test sections for single angles
  const L6X4X1_2: Section = {
    name: "L6X4X1/2",
    Type: "L",
    A: 4.75,
    d: 6.0, // Longer leg
    tw: 0.5, // Leg thickness
    bf: 4.0, // Shorter leg
    tf: 0.5, // Same as tw for angles
    Ix: 17.4,
    Zx: 4.25,
    Sx: 4.33,
  };

  const L4X4X3_8: Section = {
    name: "L4X4X3/8",
    Type: "L",
    A: 2.86,
    d: 4.0,
    tw: 0.375,
    bf: 4.0,
    tf: 0.375,
    Ix: 5.56,
    Zx: 2.29,
    Sx: 2.78,
  };

  // Test sections for double angles
  const DOUBLE_L4X4X1_2: Section = {
    name: "2L4X4X1/2",
    Type: "2L",
    A: 7.5, // Two angles
    d: 4.0, // Equal legs
    tw: 0.5, // Leg thickness
    bf: 4.0, // Equal legs
    tf: 0.5, // Same as tw
    Ix: 7.7,
    Zx: 3.84,
    Sx: 3.79,
  };

  const DOUBLE_L6X6X5_8: Section = {
    name: "2L6X6X5/8",
    Type: "2L",
    A: 14.4,
    d: 6.0,
    tw: 0.625,
    bf: 6.0,
    tf: 0.625,
    Ix: 35.5,
    Zx: 8.57,
    Sx: 11.8,
  };

  const Fy = 50; // ksi (A36 steel)

  describe("AISC G3 - Single Angles", () => {
    it("should calculate shear strength for single angle with conservative approach", () => {
      const result = calculateVn_Angle(L6X4X1_2, Fy);

      // Conservative calculation: Vn = 0.6 * Fy * A
      const expectedVn = 0.6 * Fy * L6X4X1_2.A; // 0.6 * 50 * 4.75 = 142.5 kips

      expect(result.Vn).toBeCloseTo(expectedVn, 1);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.Cv1).toBe(1.0);
      expect(result.metadata?.effectiveArea).toBe(L6X4X1_2.A);
    });

    it("should calculate shear strength for different connection types", () => {
      const weldedResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "welded",
      });

      const boltedResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "bolted",
      });

      const conservativeResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "conservative",
      });

      // All should give same result for basic calculation
      expect(weldedResult.Vn).toBeCloseTo(boltedResult.Vn, 1);
      expect(boltedResult.Vn).toBeCloseTo(conservativeResult.Vn, 1);

      expect(weldedResult.metadata?.connectionType).toBe("welded");
      expect(boltedResult.metadata?.connectionType).toBe("bolted");
      expect(conservativeResult.metadata?.connectionType).toBe("conservative");
    });

    it("should apply eccentricity reduction for bolted connections", () => {
      const normalResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "bolted",
        considerEccentricity: false,
      });

      const eccentricResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "bolted",
        considerEccentricity: true,
      });

      // Eccentric connection should have reduced strength
      expect(eccentricResult.Vn).toBeLessThan(normalResult.Vn);
      expect(eccentricResult.metadata?.Cv1).toBe(0.9);
      expect(eccentricResult.metadata?.considerEccentricity).toBe(true);
    });

    it("should not apply eccentricity reduction for welded connections", () => {
      const normalResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "welded",
        considerEccentricity: false,
      });

      const eccentricResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "welded",
        considerEccentricity: true,
      });

      // Welded connections should not be affected by eccentricity flag
      expect(eccentricResult.Vn).toBeCloseTo(normalResult.Vn, 1);
      expect(eccentricResult.metadata?.Cv1).toBe(1.0);
    });
  });

  describe("AISC G3 - Double Angles", () => {
    it("should calculate shear strength for double angles", () => {
      const result = calculateVn_Angle(DOUBLE_L4X4X1_2, Fy);

      // Double angle calculation: 2 * (0.6 * Fy * A_single)
      // Total area = 7.5, so each angle = 3.75
      // Vn = 2 * (0.6 * 50 * 7.5) = 450 kips
      const expectedVn = 2 * 0.6 * Fy * DOUBLE_L4X4X1_2.A;

      expect(result.Vn).toBeCloseTo(expectedVn, 1);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.effectiveArea).toBe(2 * DOUBLE_L4X4X1_2.A);
    });

    it("should handle large double angle sections", () => {
      const result = calculateVn_Angle(DOUBLE_L6X6X5_8, Fy);

      // Should scale appropriately with larger sections
      expect(result.Vn).toBeGreaterThan(800); // Much larger than single angles
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
    });

    it("should apply eccentricity effects to double angles", () => {
      const normalResult = calculateVn_Angle(DOUBLE_L4X4X1_2, Fy, {
        connectionType: "bolted",
        considerEccentricity: false,
      });

      const eccentricResult = calculateVn_Angle(DOUBLE_L4X4X1_2, Fy, {
        connectionType: "bolted",
        considerEccentricity: true,
      });

      // Both angles should be affected by eccentricity
      expect(eccentricResult.Vn).toBeLessThan(normalResult.Vn);
      expect(eccentricResult.Vn).toBeCloseTo(normalResult.Vn * 0.9, 1);
    });
  });

  describe("Simplified Calculation", () => {
    it("should provide same result as conservative default", () => {
      const simplifiedResult = calculateVn_Angle_Simplified(L6X4X1_2, Fy);
      const defaultResult = calculateVn_Angle(L6X4X1_2, Fy);

      expect(simplifiedResult.Vn).toBeCloseTo(defaultResult.Vn, 1);
      expect(simplifiedResult.governingLimitState).toBe(
        defaultResult.governingLimitState
      );
    });

    it("should use conservative assumptions", () => {
      const result = calculateVn_Angle_Simplified(L4X4X3_8, Fy);

      expect(result.metadata?.connectionType).toBe("conservative");
      expect(result.metadata?.considerEccentricity).toBe(false);
      expect(result.metadata?.Cv1).toBe(1.0);
    });
  });

  describe("Input Validation", () => {
    it("should throw error for missing area", () => {
      expect(() =>
        calculateVn_Angle({ ...L6X4X1_2, A: undefined } as any, Fy)
      ).toThrow();
    });

    it("should throw error for negative area", () => {
      expect(() => calculateVn_Angle({ ...L6X4X1_2, A: -1 }, Fy)).toThrow();
    });

    it("should throw error for zero area", () => {
      expect(() => calculateVn_Angle({ ...L6X4X1_2, A: 0 }, Fy)).toThrow();
    });

    it("should throw error for invalid yield strength", () => {
      expect(() => calculateVn_Angle(L6X4X1_2, 0)).toThrow(
        "Yield strength (Fy) must be a positive number"
      );

      expect(() => calculateVn_Angle(L6X4X1_2, -10)).toThrow(
        "Yield strength (Fy) must be a positive number"
      );
    });
  });

  describe("Manual Verification", () => {
    it("should provide consistent calculations", () => {
      const result1 = calculateVn_Angle(L6X4X1_2, Fy);
      const result2 = calculateVn_Angle(L6X4X1_2, Fy);

      expect(result1.Vn).toBe(result2.Vn);
      expect(result1.governingLimitState).toBe(result2.governingLimitState);
    });

    it("should calculate expected values for L6X4X1/2", () => {
      const result = calculateVn_Angle(L6X4X1_2, Fy);

      // Manual verification: Vn = 0.6 * Fy * A
      const expectedVn = 0.6 * Fy * L6X4X1_2.A; // 0.6 * 50 * 4.75 = 142.5

      expect(result.Vn).toBeCloseTo(expectedVn, 1);
      expect(result.metadata?.effectiveArea).toBe(L6X4X1_2.A);

      // Should include h/tw if available
      if (L6X4X1_2.d && L6X4X1_2.tw) {
        const expectedRatio = L6X4X1_2.d / L6X4X1_2.tw;
        expect(result.metadata?.h_over_tw).toBeCloseTo(expectedRatio, 1);
      }
    });

    it("should calculate expected values for double angles", () => {
      const result = calculateVn_Angle(DOUBLE_L4X4X1_2, Fy);

      // Manual verification for double angles
      const singleAngleVn = 0.6 * Fy * DOUBLE_L4X4X1_2.A;
      const expectedVn = 2 * singleAngleVn; // Double the single angle capacity

      expect(result.Vn).toBeCloseTo(expectedVn, 1);
      expect(result.metadata?.effectiveArea).toBe(2 * DOUBLE_L4X4X1_2.A);
    });
  });

  describe("Edge Cases", () => {
    it("should handle minimal angle properties", () => {
      const minimalAngle: Section = {
        name: "L3X3X1/4",
        Type: "L",
        A: 1.44,
        d: 3.0,
        tw: 0.25,
        // Other properties optional for this test
      };

      const result = calculateVn_Angle(minimalAngle, Fy);

      expect(result.Vn).toBeGreaterThan(0);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.h_over_tw).toBeCloseTo(12, 1); // 3.0 / 0.25 = 12
    });

    it("should handle very small angles", () => {
      const smallAngle: Section = {
        ...L4X4X3_8,
        A: 0.5, // Very small area
      };

      const result = calculateVn_Angle(smallAngle, Fy);

      expect(result.Vn).toBeGreaterThan(0);
      expect(result.Vn).toBeLessThan(20); // Should be small
    });

    it("should handle very large angles", () => {
      const largeAngle: Section = {
        ...L6X4X1_2,
        A: 20.0, // Very large area
      };

      const result = calculateVn_Angle(largeAngle, Fy);

      expect(result.Vn).toBeGreaterThan(500); // Should be large
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
    });
  });

  describe("Connection Type Effects", () => {
    it("should document connection type in metadata", () => {
      const weldedResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "welded",
      });

      const boltedResult = calculateVn_Angle(L6X4X1_2, Fy, {
        connectionType: "bolted",
      });

      expect(weldedResult.metadata?.connectionType).toBe("welded");
      expect(boltedResult.metadata?.connectionType).toBe("bolted");
    });

    it("should handle all connection type options", () => {
      const connectionTypes: Array<"welded" | "bolted" | "conservative"> = [
        "welded",
        "bolted",
        "conservative",
      ];

      connectionTypes.forEach((type) => {
        const result = calculateVn_Angle(L6X4X1_2, Fy, {
          connectionType: type,
        });

        expect(result.Vn).toBeGreaterThan(0);
        expect(result.metadata?.connectionType).toBe(type);
      });
    });
  });
});
