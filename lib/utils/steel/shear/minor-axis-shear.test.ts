import { describe, it, expect } from "@jest/globals";
import { Section } from "../slenderness/types";
import { ShearLimitState, E } from "../constants";
import {
  calculateVn_MinorAxis,
  getMinorAxisShearResistanceFactors,
  calculateVn_MinorAxis_Simplified,
} from "./minor-axis-shear";

describe("Minor-Axis Shear Strength (AISC G6)", () => {
  // Test sections for different types
  const W18X50: Section = {
    name: "W18X50",
    Type: "W",
    A: 14.7,
    d: 18.0,
    tw: 0.355,
    bf: 7.5,
    tf: 0.57,
    Ix: 800,
    Zx: 101,
    Sx: 88.9,
  };

  const W24X55: Section = {
    name: "W24X55",
    Type: "W",
    A: 16.2,
    d: 23.57,
    tw: 0.395,
    bf: 7.005,
    tf: 0.505,
    Ix: 1350,
    Zx: 134,
    Sx: 114,
  };

  const C12X25: Section = {
    name: "C12X25",
    Type: "C",
    A: 7.35,
    d: 12.0,
    tw: 0.387,
    bf: 3.05,
    tf: 0.501,
    Ix: 144,
    Zx: 28.1,
    Sx: 24.1,
  };

  const WT9X25: Section = {
    name: "WT9X25",
    Type: "TEE",
    A: 7.37,
    d: 9.0,
    tw: 0.394,
    bf: 7.245,
    tf: 0.615,
    Ix: 92.9,
    Zx: 24.5,
    Sx: 20.6,
  };

  // High strength steel section
  const W16X26_A992_65: Section = {
    name: "W16X26_A992_Fy65",
    Type: "W",
    A: 7.68,
    d: 15.69,
    tw: 0.25,
    bf: 5.5,
    tf: 0.345,
    Ix: 301,
    Zx: 44.2,
    Sx: 38.4,
  };

  const Fy = 50; // ksi (A992 steel)
  const FyHigh = 65; // ksi (high strength steel)

  describe("AISC G6 - General Provisions", () => {
    it("should calculate minor-axis shear strength for W-shape", () => {
      const result = calculateVn_MinorAxis(W18X50, Fy);

      // bf/(2*tf) = 7.5/(2*0.57) = 6.58
      // Av = bf * tf = 7.5 * 0.57 = 4.275 in²
      // For W-shape with Fy ≤ 70 ksi, Cv2 = 1.0 per User Note
      // Vn = 0.6 * 50 * 4.275 * 1.0 = 128.25 kips

      expect(result.Vn).toBeCloseTo(128.25, 1);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.Cv1).toBe(1.0); // Cv2 stored in Cv1 field
      expect(result.metadata?.h_over_tw).toBeCloseTo(6.58, 2);
      expect(result.metadata?.kv).toBe(1.2);
    });

    it("should calculate minor-axis shear strength for channel", () => {
      const result = calculateVn_MinorAxis(C12X25, Fy);

      // For channels: bf/tf = 3.05/0.501 = 6.09
      // Av = bf * tf = 3.05 * 0.501 = 1.528 in²
      // Should be compact flange (Cv2 = 1.0)

      expect(result.Vn).toBeCloseTo(45.84, 1); // 0.6 * 50 * 1.528 * 1.0
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.h_over_tw).toBeCloseTo(6.09, 2);
      expect(result.metadata?.sectionType).toBe("C");
    });

    it("should calculate minor-axis shear strength for tee", () => {
      const result = calculateVn_MinorAxis(WT9X25, Fy);

      // For tees: bf/(2*tf) = 7.245/(2*0.615) = 5.89
      // Av = bf * tf = 7.245 * 0.615 = 4.456 in²

      expect(result.Vn).toBeCloseTo(133.68, 1); // 0.6 * 50 * 4.456 * 1.0
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.h_over_tw).toBeCloseTo(5.89, 2);
    });
  });

  describe("User Note - Cv2 = 1.0 for Standard Shapes", () => {
    it("should use Cv2 = 1.0 for W-shapes with Fy ≤ 70 ksi", () => {
      const result = calculateVn_MinorAxis(W18X50, Fy);

      expect(result.metadata?.Cv1).toBe(1.0);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
    });

    it("should calculate Cv2 for high strength steel (Fy > 70 ksi)", () => {
      const result = calculateVn_MinorAxis(W16X26_A992_65, 75); // Fy = 75 ksi

      // Should not automatically use Cv2 = 1.0 since Fy > 70 ksi
      // Will calculate based on actual slenderness
      expect(result.metadata?.Cv1).toBeGreaterThan(0);
      expect(result.Vn).toBeGreaterThan(0);
    });

    it("should use general calculation for non-W shapes", () => {
      const result = calculateVn_MinorAxis(C12X25, Fy);

      // Channels don't qualify for automatic Cv2 = 1.0 under User Note
      expect(result.metadata?.Cv1).toBeGreaterThan(0);
      expect(result.Vn).toBeGreaterThan(0);
    });
  });

  describe("Flange Slenderness Effects", () => {
    it("should handle compact flanges (λv ≤ 1.10)", () => {
      // Most standard sections should have compact flanges for minor-axis shear
      const result = calculateVn_MinorAxis(W24X55, Fy);

      expect(result.metadata?.Cv1).toBe(1.0);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
    });

    it("should calculate Cv2 for slender flanges", () => {
      // Create a section with slender flanges
      const slenderSection: Section = {
        ...W18X50,
        name: "SlenderFlanges",
        bf: 12.0,
        tf: 0.2, // Very thin flange
      };

      const result = calculateVn_MinorAxis(slenderSection, Fy);

      // bf/(2*tf) = 12.0/(2*0.2) = 30 (slender)
      // Should not use User Note due to high slenderness
      expect(result.metadata?.Cv1).toBeLessThan(1.0);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.metadata?.h_over_tw).toBeCloseTo(30, 1);
    });

    it("should handle intermediate slenderness", () => {
      // Create a section with intermediate flange slenderness
      const intermediateSection: Section = {
        ...W18X50,
        name: "IntermediateFlanges",
        bf: 10.0,
        tf: 0.4,
      };

      const result = calculateVn_MinorAxis(intermediateSection, Fy);

      // bf/(2*tf) = 10.0/(2*0.4) = 12.5
      expect(result.metadata?.Cv1).toBeLessThanOrEqual(1.0);
      expect(result.metadata?.h_over_tw).toBeCloseTo(12.5, 1);
    });
  });

  describe("Resistance Factors", () => {
    it("should return general G1(a) resistance factors", () => {
      const factors = getMinorAxisShearResistanceFactors(W18X50, Fy);

      expect(factors.phi_v).toBe(0.9); // General LRFD factor
      expect(factors.omega_v).toBe(1.67); // General ASD factor
      expect(factors.source).toContain("G1(a)");
      expect(factors.source).toContain("G6");
    });

    it("should provide consistent resistance factors for all sections", () => {
      const factorsW = getMinorAxisShearResistanceFactors(W18X50, Fy);
      const factorsC = getMinorAxisShearResistanceFactors(C12X25, Fy);
      const factorsT = getMinorAxisShearResistanceFactors(WT9X25, Fy);

      expect(factorsW.phi_v).toBe(factorsC.phi_v);
      expect(factorsC.phi_v).toBe(factorsT.phi_v);
      expect(factorsW.omega_v).toBe(factorsC.omega_v);
    });
  });

  describe("Input Validation", () => {
    it("should throw error for missing flange width", () => {
      expect(() =>
        calculateVn_MinorAxis({ ...W18X50, bf: undefined } as any, Fy)
      ).toThrow("Flange width (bf) and thickness (tf)");
    });

    it("should throw error for missing flange thickness", () => {
      expect(() =>
        calculateVn_MinorAxis({ ...W18X50, tf: undefined } as any, Fy)
      ).toThrow("Flange width (bf) and thickness (tf)");
    });

    it("should throw error for negative dimensions", () => {
      expect(() =>
        calculateVn_MinorAxis({ ...W18X50, bf: -1 }, Fy)
      ).toThrow("must be positive numbers");

      expect(() =>
        calculateVn_MinorAxis({ ...W18X50, tf: 0 }, Fy)
      ).toThrow("must be positive numbers");
    });

    it("should throw error for invalid yield strength", () => {
      expect(() => calculateVn_MinorAxis(W18X50, 0)).toThrow(
        "Yield strength (Fy) must be a positive number"
      );

      expect(() => calculateVn_MinorAxis(W18X50, -10)).toThrow(
        "Yield strength (Fy) must be a positive number"
      );
    });
  });

  describe("Manual Verification", () => {
    it("should provide consistent calculations", () => {
      const result1 = calculateVn_MinorAxis(W18X50, Fy);
      const result2 = calculateVn_MinorAxis(W18X50, Fy);

      expect(result1.Vn).toBe(result2.Vn);
      expect(result1.governingLimitState).toBe(result2.governingLimitState);
    });

    it("should calculate expected values for W18X50", () => {
      const result = calculateVn_MinorAxis(W18X50, Fy);

      // Manual verification:
      // bf/(2*tf) = 7.5/(2*0.57) = 6.58
      // Av = bf * tf = 7.5 * 0.57 = 4.275 in²
      // Cv2 = 1.0 (User Note for W-shape with Fy ≤ 70 ksi)
      // Vn = 0.6 * 50 * 4.275 * 1.0 = 128.25 kips

      const expectedAv = (W18X50.bf || 0) * (W18X50.tf || 0);
      const expectedVn = 0.6 * Fy * expectedAv * 1.0;

      expect(result.metadata?.effectiveArea).toBeCloseTo(expectedAv, 2);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
      expect(result.metadata?.h_over_tw).toBeCloseTo(6.58, 2);
    });

    it("should calculate expected values for channel", () => {
      const result = calculateVn_MinorAxis(C12X25, Fy);

      // Manual verification for channel:
      // bf/tf = 3.05/0.501 = 6.09
      // Av = bf * tf = 3.05 * 0.501 = 1.528 in²

      const expectedAv = (C12X25.bf || 0) * (C12X25.tf || 0);
      const expectedSlenderness = (C12X25.bf || 0) / (C12X25.tf || 1);

      expect(result.metadata?.effectiveArea).toBeCloseTo(expectedAv, 3);
      expect(result.metadata?.h_over_tw).toBeCloseTo(expectedSlenderness, 2);
    });
  });

  describe("Torsion Considerations", () => {
    it("should handle torsion flag without error", () => {
      const result = calculateVn_MinorAxis(W18X50, Fy, {
        considerTorsion: true,
      });

      expect(result.Vn).toBeGreaterThan(0);
      expect(result.metadata?.considerTorsion).toBe(true);
    });

    it("should provide same result regardless of torsion flag", () => {
      const resultWithoutTorsion = calculateVn_MinorAxis(W18X50, Fy, {
        considerTorsion: false,
      });

      const resultWithTorsion = calculateVn_MinorAxis(W18X50, Fy, {
        considerTorsion: true,
      });

      // G6 doesn't consider torsional effects, so results should be same
      expect(resultWithTorsion.Vn).toBeCloseTo(resultWithoutTorsion.Vn, 1);
    });
  });

  describe("Simplified Calculation", () => {
    it("should provide same result as default calculation", () => {
      const simplifiedResult = calculateVn_MinorAxis_Simplified(W18X50, Fy);
      const defaultResult = calculateVn_MinorAxis(W18X50, Fy);

      expect(simplifiedResult.Vn).toBeCloseTo(defaultResult.Vn, 1);
      expect(simplifiedResult.governingLimitState).toBe(
        defaultResult.governingLimitState
      );
    });

    it("should use conservative assumptions", () => {
      const result = calculateVn_MinorAxis_Simplified(W24X55, Fy);

      expect(result.metadata?.considerTorsion).toBe(false);
      expect(result.Vn).toBeGreaterThan(0);
    });
  });

  describe("Edge Cases", () => {
    it("should handle very wide thin flanges", () => {
      const wideSection: Section = {
        ...W18X50,
        name: "WideThinFlange",
        bf: 20.0,
        tf: 0.15,
      };

      const result = calculateVn_MinorAxis(wideSection, Fy);

      // bf/(2*tf) = 20.0/(2*0.15) = 66.67 (very slender)
      // Should not use User Note due to very high slenderness
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.metadata?.Cv1).toBeLessThan(0.5);
    });

    it("should handle very thick flanges", () => {
      const thickSection: Section = {
        ...W18X50,
        name: "ThickFlange",
        bf: 8.0,
        tf: 2.0,
      };

      const result = calculateVn_MinorAxis(thickSection, Fy);

      // bf/(2*tf) = 8.0/(2*2.0) = 2.0 (very compact)
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.Cv1).toBe(1.0);
    });

    it("should handle different section types", () => {
      const sections = [W18X50, C12X25, WT9X25];
      const sectionTypes = ["W", "C", "TEE"];

      sections.forEach((section, index) => {
        const result = calculateVn_MinorAxis(section, Fy);

        expect(result.Vn).toBeGreaterThan(0);
        expect(result.metadata?.sectionType).toBe(sectionTypes[index]);
      });
    });
  });

  describe("High Strength Steel", () => {
    it("should handle high strength steel correctly", () => {
      const result = calculateVn_MinorAxis(W16X26_A992_65, FyHigh);

      expect(result.Vn).toBeGreaterThan(0);
      expect(result.metadata?.lambda_v).toBeGreaterThan(0);

      // Higher yield strength should generally increase capacity
      const normalResult = calculateVn_MinorAxis(W16X26_A992_65, Fy);
      expect(result.Vn).toBeGreaterThan(normalResult.Vn);
    });

    it("should not automatically use Cv2 = 1.0 for Fy > 70 ksi", () => {
      const highStrengthResult = calculateVn_MinorAxis(W18X50, 80); // Fy = 80 ksi
      const normalResult = calculateVn_MinorAxis(W18X50, Fy); // Fy = 50 ksi

      // Should calculate Cv2 based on slenderness rather than using User Note
      expect(highStrengthResult.metadata?.Cv1).toBeGreaterThan(0);
    });
  });
}); 