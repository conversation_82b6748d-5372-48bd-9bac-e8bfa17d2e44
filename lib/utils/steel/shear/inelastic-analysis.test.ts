/**
 * @file Tests for inelastic analysis support in shear strength calculations.
 *
 * Tests the implementation of AISC 360-22 Appendix 1.3 "Design by Inelastic Analysis"
 * requirements for stiffness reduction in shear calculations.
 *
 * <AUTHOR> Engineering App
 * @version 1.0.0
 */

import { DesignMethod, E, G, SupportedSectionType } from "../constants";
import {
  calculateReducedMaterialProperties,
  shouldApplyStiffnessReduction,
  validateInelasticAnalysisOptions,
  getInelasticAnalysisGuidance,
  applyShearModulusReduction,
  calculateInelasticAnalysisPn,
  validateMaterialRequirements,
  calculateWebSlendernessLimit,
  calculateFlangeSlendernessLimit,
  calculateRoundHSSSlendernessLimit,
  validateCrossSectionRequirements,
  calculateEffectiveMoment,
  calculateLpd_I_Shape,
  calculateLpd_RectangularSection,
  calculateAxialCompressionUnbracedLengthLimits,
  unbracedLengthLimitsApply,
  validateUnbracedLengthRequirements,
  validateAxialForceRequirements,
} from "./inelastic-analysis";
import { calculateVn_I_Shape } from "./i-shape-shear";
import { InelasticAnalysisOptions } from "./types";

describe("Inelastic Analysis Support", () => {
  describe("calculateReducedMaterialProperties", () => {
    it("should return original properties when stiffness reduction is not applied", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: false,
      };

      const result = calculateReducedMaterialProperties(options);

      expect(result.E_reduced).toBe(E);
      expect(result.G_reduced).toBe(G);
      expect(result.reductionFactor).toBe(1.0);
      expect(result.tauB).toBe(1.0);
    });

    it("should calculate 0.8τb reduction factor for LRFD with αPr/Pn ≤ 0.5", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 100, // kips
        Pn: 400, // kips
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateReducedMaterialProperties(options);

      // For LRFD: α = 1.0, αPr/Pn = 100/400 = 0.25 ≤ 0.5
      // Therefore, τb = 1.0 (per AISC C2-2a)
      expect(result.tauB).toBe(1.0);
      expect(result.reductionFactor).toBe(0.8); // 0.8 * 1.0
      expect(result.E_reduced).toBeCloseTo(E * 0.8, 1);
      expect(result.G_reduced).toBeCloseTo(G * 0.8, 1);
    });

    it("should calculate 0.8τb reduction factor for LRFD with αPr/Pn > 0.5", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 300, // kips
        Pn: 400, // kips
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateReducedMaterialProperties(options);

      // For LRFD: α = 1.0, αPr/Pn = 300/400 = 0.75 > 0.5
      // Therefore, τb = 4 * 0.75 * (1 - 0.75) = 4 * 0.75 * 0.25 = 0.75
      const expectedTauB = 4 * 0.75 * 0.25;
      expect(result.tauB).toBeCloseTo(expectedTauB, 3);
      expect(result.reductionFactor).toBeCloseTo(0.8 * expectedTauB, 3);
      expect(result.E_reduced).toBeCloseTo(E * 0.8 * expectedTauB, 1);
    });

    it("should calculate 0.8τb reduction factor for ASD", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 150, // kips
        Pn: 300, // kips
        designMethod: DesignMethod.ASD,
      };

      const result = calculateReducedMaterialProperties(options);

      // For ASD: α = 1.6, αPr/Pn = 1.6 * 150/300 = 0.8 > 0.5
      // Therefore, τb = 4 * 0.8 * (1 - 0.8) = 4 * 0.8 * 0.2 = 0.64
      const expectedTauB = 4 * 0.8 * 0.2;
      expect(result.tauB).toBeCloseTo(expectedTauB, 3);
      expect(result.reductionFactor).toBeCloseTo(0.8 * expectedTauB, 3);
    });

    it("should use custom reduction factor when provided", () => {
      const customFactor = 0.65;
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: customFactor,
      };

      const result = calculateReducedMaterialProperties(options);

      expect(result.reductionFactor).toBe(customFactor);
      expect(result.E_reduced).toBeCloseTo(E * customFactor, 1);
      expect(result.G_reduced).toBeCloseTo(G * customFactor, 1);
      expect(result.tauB).toBe(1.0); // Not applicable for custom factor
    });

    it("should handle custom material properties", () => {
      const E_custom = 25000; // ksi
      const G_custom = 10000; // ksi
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: 0.7,
      };

      const result = calculateReducedMaterialProperties(
        options,
        E_custom,
        G_custom
      );

      expect(result.E_original).toBe(E_custom);
      expect(result.G_original).toBe(G_custom);
      expect(result.E_reduced).toBeCloseTo(E_custom * 0.7, 1);
      expect(result.G_reduced).toBeCloseTo(G_custom * 0.7, 1);
    });

    it("should throw error for invalid custom reduction factor", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: 1.5, // Invalid > 1.0
      };

      expect(() => calculateReducedMaterialProperties(options)).toThrow(
        "Custom reduction factor must be between 0 and 1.0"
      );
    });

    it("should throw error when required parameters are missing", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        // Missing Pr, Pn, designMethod
      };

      expect(() => calculateReducedMaterialProperties(options)).toThrow(
        "Pr, Pn, and designMethod are required when not using custom reduction factor"
      );
    });
  });

  describe("shouldApplyStiffnessReduction", () => {
    it("should return false when no options provided", () => {
      expect(shouldApplyStiffnessReduction()).toBe(false);
      expect(shouldApplyStiffnessReduction(undefined)).toBe(false);
    });

    it("should return true when applyStiffnessReduction is true", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
      };
      expect(shouldApplyStiffnessReduction(options)).toBe(true);
    });

    it("should return false when applyStiffnessReduction is false", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: false,
      };
      expect(shouldApplyStiffnessReduction(options)).toBe(false);
    });
  });

  describe("validateInelasticAnalysisOptions", () => {
    it("should pass validation for complete options", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 150,
        Pn: 300,
        designMethod: DesignMethod.LRFD,
      };

      expect(() => validateInelasticAnalysisOptions(options)).not.toThrow();
    });

    it("should pass validation when stiffness reduction is not applied", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: false,
      };

      expect(() => validateInelasticAnalysisOptions(options)).not.toThrow();
    });

    it("should pass validation for custom reduction factor", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: 0.7,
      };

      expect(() => validateInelasticAnalysisOptions(options)).not.toThrow();
    });

    it("should throw error for missing Pr", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pn: 300,
        designMethod: DesignMethod.LRFD,
      };

      expect(() => validateInelasticAnalysisOptions(options)).toThrow(
        "Required axial strength (Pr) is required for stiffness reduction"
      );
    });

    it("should throw error for negative Pr", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: -50,
        Pn: 300,
        designMethod: DesignMethod.LRFD,
      };

      expect(() => validateInelasticAnalysisOptions(options)).toThrow(
        "Required axial strength (Pr) must be non-negative"
      );
    });

    it("should throw error for invalid custom reduction factor", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: 0,
      };

      expect(() => validateInelasticAnalysisOptions(options)).toThrow(
        "Custom reduction factor must be between 0 and 1.0"
      );
    });
  });

  describe("getInelasticAnalysisGuidance", () => {
    it("should return comprehensive guidance", () => {
      const guidance = getInelasticAnalysisGuidance();

      expect(guidance.considerations).toHaveLength(5);
      expect(guidance.recommendations).toHaveLength(5);
      expect(guidance.limitations).toHaveLength(5);

      // Check for key content
      expect(guidance.considerations[0]).toContain(
        "second-order elastic analysis"
      );
      expect(guidance.recommendations[0]).toContain("consistently");
      expect(guidance.limitations[0]).toContain("second-order analysis");
    });
  });

  describe("applyShearModulusReduction", () => {
    it("should apply reduction factor correctly", () => {
      const G_original = 11200; // ksi
      const reductionFactor = 0.75;

      const result = applyShearModulusReduction(G_original, reductionFactor);

      expect(result).toBeCloseTo(G_original * reductionFactor, 1);
    });

    it("should throw error for invalid inputs", () => {
      expect(() => applyShearModulusReduction(-1000, 0.8)).toThrow(
        "Original shear modulus must be positive"
      );
      expect(() => applyShearModulusReduction(11200, 1.5)).toThrow(
        "Reduction factor must be between 0 and 1.0"
      );
    });
  });

  describe("calculateInelasticAnalysisPn", () => {
    it("should calculate Pn = FyAg for non-slender sections", () => {
      const Fy = 50; // ksi
      const Ag = 15.8; // in²

      const result = calculateInelasticAnalysisPn(Fy, Ag);

      expect(result).toBeCloseTo(Fy * Ag, 1); // 790 kips
    });

    it("should calculate Pn = FyAe for slender sections", () => {
      const Fy = 50; // ksi
      const Ag = 15.8; // in²
      const Ae = 14.5; // in² (reduced for slender elements)

      const result = calculateInelasticAnalysisPn(Fy, Ag, Ae, true);

      expect(result).toBeCloseTo(Fy * Ae, 1); // 725 kips
    });

    it("should throw error for slender sections without Ae", () => {
      const Fy = 50;
      const Ag = 15.8;

      expect(() =>
        calculateInelasticAnalysisPn(Fy, Ag, undefined, true)
      ).toThrow(
        "Effective area (Ae) is required and must be positive for slender elements"
      );
    });

    it("should throw error for invalid inputs", () => {
      expect(() => calculateInelasticAnalysisPn(-50, 15.8)).toThrow(
        "Yield strength (Fy) must be positive"
      );
      expect(() => calculateInelasticAnalysisPn(50, -15.8)).toThrow(
        "Gross area (Ag) must be positive"
      );
    });
  });

  describe("Integration with Shear Strength Calculations", () => {
    const testSection = {
      name: "W18X50",
      Type: "W" as const,
      A: 14.7,
      d: 18.0,
      tw: 0.355,
      tf: 0.57,
      bf: 7.5,
    };

    it("should calculate normal shear strength without inelastic analysis", () => {
      const result = calculateVn_I_Shape(testSection, 50);

      expect(result.Vn).toBeGreaterThan(0);
      expect(result.metadata?.stiffnessReduction).toBeUndefined();
    });

    it("should apply stiffness reduction in shear strength calculation", () => {
      const inelasticOptions: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 200,
        Pn: 500,
        designMethod: DesignMethod.LRFD,
      };

      const resultInelastic = calculateVn_I_Shape(testSection, 50, {
        inelasticAnalysis: inelasticOptions,
      });
      const resultNormal = calculateVn_I_Shape(testSection, 50);

      // Reduced E should result in different Cv1 and potentially different Vn
      expect(resultInelastic.metadata?.stiffnessReduction).toBeDefined();
      expect(
        resultInelastic.metadata?.stiffnessReduction?.appliedToModulus
      ).toBe(true);
      expect(
        resultInelastic.metadata?.stiffnessReduction?.baseReductionFactor
      ).toBe(0.8);

      // The exact difference depends on the web slenderness and buckling behavior
      // For compact webs, Cv1 = 1.0 regardless of E, so Vn might be the same
      // For slender webs, reduced E will affect Cv1 and thus Vn
    });

    it("should use custom reduction factor in shear calculations", () => {
      const customFactor = 0.6;
      const inelasticOptions: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: customFactor,
      };

      const result = calculateVn_I_Shape(testSection, 50, {
        inelasticAnalysis: inelasticOptions,
      });

      expect(result.metadata?.stiffnessReduction?.totalReduction).toBe(
        customFactor
      );
      expect(result.metadata?.stiffnessReduction?.tauB).toBe(1.0);
    });

    it("should validate inelastic analysis options in shear calculations", () => {
      const invalidOptions: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        customReductionFactor: 1.5, // Invalid
      };

      expect(() =>
        calculateVn_I_Shape(testSection, 50, {
          inelasticAnalysis: invalidOptions,
        })
      ).toThrow("Custom reduction factor must be between 0 and 1.0");
    });

    it("should demonstrate complete inelastic analysis workflow", () => {
      // Real-world example: W18×50 beam with compression loads
      const beam = {
        name: "W18X50",
        Type: "W" as const,
        d: 18.0,
        tw: 0.355,
        tf: 0.57,
        bf: 7.5,
        A: 14.7, // in²
      };

      // Member is subject to compression
      const Pr = 180; // kips (required compression)
      const Fy = 50; // ksi
      const Ag = beam.A;

      // Calculate nominal strength for inelastic analysis (cross-section strength)
      const Pn = calculateInelasticAnalysisPn(Fy, Ag); // FyAg = 50 * 14.7 = 735 kips

      // Apply inelastic analysis in shear calculation
      const inelasticOptions: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr,
        Pn,
        designMethod: DesignMethod.LRFD,
        useCrossSectionStrength: true,
      };

      const result = calculateVn_I_Shape(beam, Fy, {
        inelasticAnalysis: inelasticOptions,
      });

      // Verify comprehensive results
      expect(result.Vn).toBeGreaterThan(0);
      expect(result.metadata?.stiffnessReduction).toBeDefined();

      const stiffness = result.metadata!.stiffnessReduction!;
      expect(stiffness.baseReductionFactor).toBe(0.8);
      expect(stiffness.appliedToModulus).toBe(true);

      // For LRFD with αPr/Pn = 1.0 * 180/735 = 0.245 ≤ 0.5
      // τb should be 1.0, total reduction should be 0.8
      expect(stiffness.tauB).toBe(1.0);
      expect(stiffness.totalReduction).toBe(0.8);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle zero axial load (Pr = 0)", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 0,
        Pn: 500,
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateReducedMaterialProperties(options);

      // αPr/Pn = 0 ≤ 0.5, so τb = 1.0
      expect(result.tauB).toBe(1.0);
      expect(result.reductionFactor).toBe(0.8);
    });

    it("should handle very high axial loads approaching Pn", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 490,
        Pn: 500,
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateReducedMaterialProperties(options);

      // αPr/Pn = 490/500 = 0.98, τb = 4 * 0.98 * 0.02 = 0.0784
      const expectedTauB = 4 * 0.98 * 0.02;
      expect(result.tauB).toBeCloseTo(expectedTauB, 4);
      expect(result.reductionFactor).toBeCloseTo(0.8 * expectedTauB, 4);
    });

    it("should handle boundary condition αPr/Pn = 0.5", () => {
      const options: InelasticAnalysisOptions = {
        applyStiffnessReduction: true,
        Pr: 250,
        Pn: 500,
        designMethod: DesignMethod.LRFD,
      };

      const result = calculateReducedMaterialProperties(options);

      // At boundary, τb should be 1.0
      expect(result.tauB).toBe(1.0);
      expect(result.reductionFactor).toBe(0.8);
    });
  });

  describe("Ductility Requirements (AISC Appendix 1.3, Section 2)", () => {
    describe("Material Requirements (Section 2a)", () => {
      it("should validate Fy ≤ 65 ksi for plastic hinging", () => {
        expect(validateMaterialRequirements(50, true)).toBe(true);
        expect(validateMaterialRequirements(65, true)).toBe(true);
        expect(validateMaterialRequirements(70, true)).toBe(false);
      });

      it("should allow any Fy when not subject to plastic hinging", () => {
        expect(validateMaterialRequirements(70, false)).toBe(true);
        expect(validateMaterialRequirements(100, false)).toBe(true);
      });
    });

    describe("Web Slenderness Limits (Section 2b, Equations A-1-1 and A-1-2)", () => {
      it("should calculate λpd using Equation A-1-1 when Pu/(φcPy) ≤ 0.125", () => {
        const Fy = 50; // ksi
        const Pu = 50; // kips
        const Py = 500; // kips (FyAg)
        const phiC = 0.90;

        // Pu/(φcPy) = 50/(0.9*500) = 0.111 ≤ 0.125
        const result = calculateWebSlendernessLimit(Fy, Pu, Py, phiC);

        // Expected: λpd = 3.76 * √(E/Fy) * [1 - (2.75*Pu)/(φcPy)]
        const term1 = 3.76 * Math.sqrt(E / Fy);
        const term2 = 1 - (2.75 * Pu) / (phiC * Py);
        const expected = term1 * term2;

        expect(result).toBeCloseTo(expected, 3);
      });

      it("should calculate λpd using Equation A-1-2 when Pu/(φcPy) > 0.125", () => {
        const Fy = 50; // ksi
        const Pu = 100; // kips
        const Py = 500; // kips (FyAg)
        const phiC = 0.90;

        // Pu/(φcPy) = 100/(0.9*500) = 0.222 > 0.125
        const result = calculateWebSlendernessLimit(Fy, Pu, Py, phiC);

        // Expected: λpd = max(1.12 * √(E/Fy) * [2.33 - Pu/(φcPy)], 1.49 * √(E/Fy))
        const term1 = Math.sqrt(E / Fy);
        const calculated = 1.12 * term1 * (2.33 - Pu / (phiC * Py));
        const minimum = 1.49 * term1;
        const expected = Math.max(calculated, minimum);

        expect(result).toBeCloseTo(expected, 3);
      });

      it("should enforce minimum value in Equation A-1-2", () => {
        const Fy = 50; // ksi
        const Pu = 450; // kips (extremely high load to trigger minimum)
        const Py = 500; // kips
        const phiC = 0.90;

        // Calculate minimum value
        const minimum = 1.49 * Math.sqrt(E / Fy);
        
        // Calculate what A-1-2 gives without minimum constraint
        const term1 = Math.sqrt(E / Fy);
        const calculated = 1.12 * term1 * (2.33 - Pu / (phiC * Py));
        
        // With extremely high Pu, calculated should be less than minimum
        // At Pu=450, phiC=0.9, Py=500: Pu/(phiC*Py) = 450/450 = 1.0
        // calculated = 1.12 * sqrt(E/Fy) * (2.33 - 1.0) = 1.12 * sqrt(E/Fy) * 1.33
        expect(calculated).toBeLessThan(minimum);
        
        // The function should return the minimum value
        const result = calculateWebSlendernessLimit(Fy, Pu, Py, phiC);
        expect(result).toBeCloseTo(minimum, 3);
      });

      it("should handle boundary condition Pu/(φcPy) = 0.125", () => {
        const Fy = 50; // ksi
        const Py = 500; // kips
        const phiC = 0.90;
        const Pu = 0.125 * phiC * Py; // Exactly at boundary

        const result = calculateWebSlendernessLimit(Fy, Pu, Py, phiC);

        // Should use Equation A-1-1
        const term1 = 3.76 * Math.sqrt(E / Fy);
        const term2 = 1 - (2.75 * Pu) / (phiC * Py);
        const expected = term1 * term2;

        expect(result).toBeCloseTo(expected, 3);
      });
    });

    describe("Flange Slenderness Limits (Section 2b, Equation A-1-3)", () => {
      it("should calculate λpd for rectangular HSS flanges", () => {
        const Fy = 50; // ksi
        const result = calculateFlangeSlendernessLimit(Fy);

        // Expected: λpd = 0.94 * √(E/Fy)
        const expected = 0.94 * Math.sqrt(E / Fy);

        expect(result).toBeCloseTo(expected, 3);
      });

      it("should vary with yield strength", () => {
        const result36 = calculateFlangeSlendernessLimit(36);
        const result50 = calculateFlangeSlendernessLimit(50);
        const result65 = calculateFlangeSlendernessLimit(65);

        // Higher Fy should give lower limits
        expect(result36).toBeGreaterThan(result50);
        expect(result50).toBeGreaterThan(result65);
      });
    });

    describe("Round HSS Slenderness Limits (Section 2b, Equation A-1-4)", () => {
      it("should calculate λpd for round HSS", () => {
        const Fy = 50; // ksi
        const result = calculateRoundHSSSlendernessLimit(Fy);

        // Expected: λpd = 0.045 * E / Fy
        const expected = 0.045 * E / Fy;

        expect(result).toBeCloseTo(expected, 3);
      });

      it("should be inversely proportional to yield strength", () => {
        const result36 = calculateRoundHSSSlendernessLimit(36);
        const result50 = calculateRoundHSSSlendernessLimit(50);

        expect(result36).toBeGreaterThan(result50);
        expect(result36 / result50).toBeCloseTo(50 / 36, 3);
      });
    });

    describe("Cross-Section Requirements Validation", () => {
      const wSection = {
        name: "W18X50",
        Type: "W" as const,
        A: 14.7,
        d: 18.0,
        tw: 0.355,
        tf: 0.57,
        bf: 7.5,
      };

      const rectHSS = {
        name: "HSS8X4X1/4",
        Type: "HSS-RECT" as const,
        A: 5.73,
        d: 8.0,
        tw: 0.25,
        tf: 0.25,
        bf: 4.0,
      };

      const roundHSS = {
        name: "HSS6.625X0.25",
        Type: "HSS-ROUND" as const,
        A: 4.78,
        d: 6.625,
        tw: 0.25,
        tf: 0.25,
        bf: 6.625,
      };

      it("should validate W-section compliance", () => {
        const Fy = 50; // ksi
        const Py = Fy * wSection.A; // 735 kips
        const Pu = 50; // kips

        const result = validateCrossSectionRequirements(wSection, Fy, {
          Pu,
          Py,
          subjectToPlasticHinging: true,
        });

        expect(result.materialCompliant).toBe(true); // Fy = 50 ≤ 65
        expect(result.doublySymmetric).toBe(true); // W-sections are doubly symmetric
        expect(result.webSlendernessLimit).toBeDefined();
        expect(result.actualWebSlenderness).toBeDefined();
        expect(result.complianceMessages).toContain(
          "Section fully compliant with inelastic analysis ductility requirements"
        );
      });

      it("should detect material non-compliance for high strength steel", () => {
        const Fy = 70; // ksi > 65 ksi limit
        const result = validateCrossSectionRequirements(wSection, Fy, {
          subjectToPlasticHinging: true,
        });

        expect(result.materialCompliant).toBe(false);
        expect(result.overallCompliant).toBe(false);
        expect(result.complianceMessages[0]).toContain("Material non-compliant");
      });

      it("should validate rectangular HSS with flange limits", () => {
        const Fy = 50; // ksi
        const Py = Fy * rectHSS.A;
        const Pu = 30; // kips

        const result = validateCrossSectionRequirements(rectHSS, Fy, {
          Pu,
          Py,
          subjectToPlasticHinging: true,
        });

        expect(result.doublySymmetric).toBe(true);
        expect(result.webSlendernessLimit).toBeDefined();
        expect(result.flangeSlendernessLimit).toBeDefined();
        expect(result.actualFlangeSlenderness).toBeDefined();
      });

      it("should validate round HSS with D/t limits", () => {
        const Fy = 50; // ksi

        const result = validateCrossSectionRequirements(roundHSS, Fy, {
          subjectToPlasticHinging: true,
        });

        expect(result.doublySymmetric).toBe(true);
        expect(result.flangeSlendernessLimit).toBeDefined(); // Actually D/t limit
        expect(result.actualFlangeSlenderness).toBeDefined(); // Actually D/t ratio

        // D/t = 6.625/0.25 = 26.5
        const expectedDoverT = roundHSS.d / roundHSS.tw;
        expect(result.actualFlangeSlenderness).toBeCloseTo(expectedDoverT, 1);
      });

      it("should skip slenderness checks when not subject to plastic hinging", () => {
        const result = validateCrossSectionRequirements(wSection, 70, {
          subjectToPlasticHinging: false,
        });

        expect(result.webSlendernessLimit).toBeUndefined();
        expect(result.flangeSlendernessLimit).toBeUndefined();
        // When not subject to plastic hinging, material requirements are relaxed
        expect(result.materialCompliant).toBe(true);
        expect(result.overallCompliant).toBe(true); // Should be compliant since doubly symmetric and material OK when not plastic hinging
      });

      it("should handle missing Py for web slenderness calculation", () => {
        const result = validateCrossSectionRequirements(wSection, 50, {
          Pu: 100,
          // Py missing
          subjectToPlasticHinging: true,
        });

        expect(result.warnings).toContain(
          "Py (axial yield strength) not provided - cannot validate web slenderness"
        );
        expect(result.webSlendernessLimit).toBeUndefined();
      });

      it("should detect non-doubly symmetric sections", () => {
        const channel = {
          name: "C12X20.7",
          Type: "C" as const,
          A: 6.09,
          d: 12.0,
          tw: 0.282,
          tf: 0.501,
          bf: 2.94,
        };

        const result = validateCrossSectionRequirements(channel, 50, {
          subjectToPlasticHinging: true,
        });

        expect(result.doublySymmetric).toBe(false);
        expect(result.overallCompliant).toBe(false);
        expect(result.complianceMessages).toContain(
          "Cross-section must be doubly symmetric for plastic hinging locations"
        );
      });
    });

    describe("Input Validation for Ductility Functions", () => {
      it("should validate inputs for web slenderness calculation", () => {
        expect(() => calculateWebSlendernessLimit(-50, 100, 500)).toThrow(
          "Yield strength (Fy) must be positive"
        );
        expect(() => calculateWebSlendernessLimit(50, -100, 500)).toThrow(
          "Required axial strength (Pu) must be non-negative"
        );
        expect(() => calculateWebSlendernessLimit(50, 100, -500)).toThrow(
          "Axial yield strength (Py) must be positive"
        );
        expect(() => calculateWebSlendernessLimit(50, 100, 500, 1.5)).toThrow(
          "Resistance factor (φc) must be between 0 and 1.0"
        );
      });

      it("should validate inputs for flange slenderness calculation", () => {
        expect(() => calculateFlangeSlendernessLimit(-50)).toThrow(
          "Yield strength (Fy) must be positive"
        );
      });

      it("should validate inputs for round HSS slenderness calculation", () => {
        expect(() => calculateRoundHSSSlendernessLimit(0)).toThrow(
          "Yield strength (Fy) must be positive"
        );
      });
    });
  });

  describe("Integration with Shear Calculations - Ductility Validation", () => {
    const testSection = {
      name: "W18X50",
      Type: "W" as const,
      A: 14.7,
      d: 18.0,
      tw: 0.355,
      tf: 0.57,
      bf: 7.5,
    };

    it("should include ductility validation in shear calculation metadata", () => {
      const Fy = 50; // ksi
      const Py = Fy * testSection.A; // 735 kips
      const Pu = 100; // kips

      const result = calculateVn_I_Shape(testSection, Fy, {
        inelasticAnalysis: {
          applyStiffnessReduction: true,
          validateDuctilityRequirements: true,
          Pr: 180,
          Pn: 735,
          designMethod: DesignMethod.LRFD,
          Pu,
          Py,
          subjectToPlasticHinging: true,
        },
      });

      expect(result.metadata?.ductilityValidation).toBeDefined();
      expect(result.metadata?.ductilityValidation?.materialCompliant).toBe(true);
      expect(result.metadata?.ductilityValidation?.doublySymmetric).toBe(true);
      expect(result.metadata?.ductilityValidation?.overallCompliant).toBe(true);
    });

    it("should handle both stiffness reduction and ductility validation", () => {
      const result = calculateVn_I_Shape(testSection, 50, {
        inelasticAnalysis: {
          applyStiffnessReduction: true,
          validateDuctilityRequirements: true,
          Pr: 180,
          Pn: 735,
          designMethod: DesignMethod.LRFD,
          Pu: 50,
          Py: 735,
          subjectToPlasticHinging: true,
        },
      });

      // Should have both stiffness reduction and ductility validation
      expect(result.metadata?.stiffnessReduction).toBeDefined();
      expect(result.metadata?.ductilityValidation).toBeDefined();

      // Both should indicate compliance
      expect(result.metadata?.stiffnessReduction?.totalReduction).toBe(0.8);
             expect(result.metadata?.ductilityValidation?.overallCompliant).toBe(true);
     });
   });

   describe("Unbraced Length Requirements (AISC Appendix 1.3, Section 2c)", () => {
     describe("Effective Moment Calculation (Equations A-1-6a, A-1-6b, A-1-6c)", () => {
       it("should use Equation A-1-6a when Mmid > M2", () => {
         const M1 = 100; // kip-in
         const M2 = 200; // kip-in  
         const Mmid = 250; // exceeds M2

         const result = calculateEffectiveMoment(M1, M2, Mmid);

         // When magnitude at any location exceeds M2: M1' = M2
         expect(result).toBe(200); // M1' = M2 (larger moment)
       });

       it("should use Equation A-1-6b when Mmid ≤ (M1 + M2)/2", () => {
         const M1 = 100; // kip-in
         const M2 = 200; // kip-in
         const Mmid = 120; // ≤ (100+200)/2 = 150

         const result = calculateEffectiveMoment(M1, M2, Mmid);

         // When Mmid ≤ (M1+M2)/2: M1' = M1
         expect(result).toBe(100); // M1' = M1 (smaller moment)
       });

       it("should use Equation A-1-6c when Mmid > (M1 + M2)/2", () => {
         const M1 = 100; // kip-in
         const M2 = 200; // kip-in
         const Mmid = 180; // > (100+200)/2 = 150, but < 200

         const result = calculateEffectiveMoment(M1, M2, Mmid);

         // When Mmid > (M1+M2)/2: M1' = min(2*Mmid - M2, M2)
         const expected = Math.min(2 * 180 - 200, 200); // min(160, 200) = 160
         expect(result).toBe(expected);
       });

       it("should handle negative moments by using absolute values", () => {
         const M1 = -100; // kip-in (compression)
         const M2 = 200; // kip-in (tension)
         const Mmid = 120; // kip-in

         const result = calculateEffectiveMoment(M1, M2, Mmid);

         // Should work with absolute values: abs(-100) = 100, abs(200) = 200
         // Mmid = 120 ≤ (100+200)/2 = 150, so M1' = M1 = 100
         expect(result).toBe(100);
       });
     });

     describe("I-Shape Unbraced Length Limits (Equation A-1-5)", () => {
       it("should calculate Lpd for I-shaped members", () => {
         const Fy = 50; // ksi
         const ry = 1.65; // in (W18x50)
         const M1_prime = 100; // kip-in
         const M2 = 200; // kip-in

         const result = calculateLpd_I_Shape(Fy, ry, M1_prime, M2);

         // Lpd = (0.12 - 0.076 * M1'/M2) * (E/Fy) * ry
         const moment_ratio = M1_prime / M2; // 0.5
         const expected = (0.12 - 0.076 * moment_ratio) * (E / Fy) * ry;
         // = (0.12 - 0.076 * 0.5) * (29000/50) * 1.65
         // = (0.12 - 0.038) * 580 * 1.65 = 0.082 * 957 = 78.5 in

         expect(result).toBeCloseTo(expected, 1);
       });

       it("should ensure non-negative result", () => {
         const Fy = 50; // ksi
         const ry = 1.65; // in
         const M1_prime = 200; // larger than M2
         const M2 = 100; // kip-in

         const result = calculateLpd_I_Shape(Fy, ry, M1_prime, M2);

         // Even if calculation gives negative, should return 0
         expect(result).toBeGreaterThanOrEqual(0);
       });

       it("should validate input parameters", () => {
         expect(() => calculateLpd_I_Shape(-50, 1.65, 100, 200)).toThrow(
           "Yield strength (Fy) must be positive"
         );
         expect(() => calculateLpd_I_Shape(50, -1.65, 100, 200)).toThrow(
           "Radius of gyration (ry) must be positive"
         );
         expect(() => calculateLpd_I_Shape(50, 1.65, 100, -200)).toThrow(
           "Larger end moment (M2) must be positive"
         );
       });
     });

     describe("Rectangular Section Unbraced Length Limits (Equation A-1-7)", () => {
       it("should calculate Lpd for rectangular HSS with minimum enforcement", () => {
         const Fy = 50; // ksi
         const ry = 1.39; // in (HSS section)
         const M1_prime = 100; // kip-in
         const M2 = 200; // kip-in

         const result = calculateLpd_RectangularSection(Fy, ry, M1_prime, M2);

         // Lpd = max((0.17 - 0.10 * M1'/M2) * (E/Fy) * ry, 0.10 * (E/Fy) * ry)
         const moment_ratio = M1_prime / M2; // 0.5
         const calculated = (0.17 - 0.10 * moment_ratio) * (E / Fy) * ry;
         const minimum = 0.10 * (E / Fy) * ry;
         const expected = Math.max(calculated, minimum);

         expect(result).toBeCloseTo(expected, 1);
         expect(result).toBeGreaterThanOrEqual(minimum);
       });

       it("should enforce minimum value when calculated value is too low", () => {
         const Fy = 50; // ksi
         const ry = 1.39; // in
         const M1_prime = 180; // high ratio
         const M2 = 200; // kip-in

         const result = calculateLpd_RectangularSection(Fy, ry, M1_prime, M2);

         // High moment ratio should trigger minimum value
         const minimum = 0.10 * (E / Fy) * ry;
         expect(result).toBeCloseTo(minimum, 1);
       });
     });

     describe("Axial Compression Unbraced Length Limits", () => {
       it("should calculate limits for both axes", () => {
         const Fy = 50; // ksi
         const rx = 7.38; // in (W18x50)
         const ry = 1.65; // in

         const result = calculateAxialCompressionUnbracedLengthLimits(Fy, rx, ry);

         // Limits = 4.71 * r * √(E/Fy)
         const baseLimit = 4.71 * Math.sqrt(E / Fy);
         const expectedMajor = baseLimit * rx;
         const expectedMinor = baseLimit * ry;

         expect(result.majorAxisLimit).toBeCloseTo(expectedMajor, 1);
         expect(result.minorAxisLimit).toBeCloseTo(expectedMinor, 1);
         expect(result.majorAxisLimit).toBeGreaterThan(result.minorAxisLimit);
       });

       it("should validate input parameters", () => {
         expect(() => calculateAxialCompressionUnbracedLengthLimits(-50, 7.38, 1.65)).toThrow(
           "Yield strength (Fy) must be positive"
         );
         expect(() => calculateAxialCompressionUnbracedLengthLimits(50, -7.38, 1.65)).toThrow(
           "Major axis radius of gyration (rx) must be positive"
         );
         expect(() => calculateAxialCompressionUnbracedLengthLimits(50, 7.38, -1.65)).toThrow(
           "Minor axis radius of gyration (ry) must be positive"
         );
       });
     });

            describe("Unbraced Length Limits Applicability", () => {
         const rectHSS = {
           name: "HSS8X4X1/4",
           Type: "HSS-RECT" as const,
           A: 5.73,
           tw: 0.25,
           bf: 4.0,
           d: 8.0,
         };

         const squareHSS = {
           name: "HSS6X6X1/4",
           Type: "HSS-RECT" as const,
           A: 5.73,
           tw: 0.25,
           bf: 6.0,
           d: 6.0,
         };

         const roundHSS = {
           name: "HSS6.625X0.25",
           Type: "HSS-ROUND" as const,
           A: 4.78,
           tw: 0.25,
           bf: 6.625,
           d: 6.625,
         };

       it("should not apply limits for round sections under flexure only", () => {
         const result = unbracedLengthLimitsApply(roundHSS, 'flexure-only', 'major');
         expect(result).toBe(false);
       });

       it("should not apply limits for square sections under flexure + tension", () => {
         const result = unbracedLengthLimitsApply(squareHSS, 'flexure-tension', 'major');
         expect(result).toBe(false);
       });

       it("should not apply limits for minor axis flexure", () => {
         const result = unbracedLengthLimitsApply(rectHSS, 'flexure-only', 'minor');
         expect(result).toBe(false);
       });

       it("should not apply limits for tension only", () => {
         const result = unbracedLengthLimitsApply(rectHSS, 'tension-only', 'major');
         expect(result).toBe(false);
       });

       it("should apply limits for rectangular sections under flexure + compression", () => {
         const result = unbracedLengthLimitsApply(rectHSS, 'flexure-compression', 'major');
         expect(result).toBe(true);
       });
     });

     describe("Comprehensive Unbraced Length Validation", () => {
       const wSection = {
         name: "W18X50",
         Type: "W" as const,
         A: 14.7,
         d: 18.0,
         tw: 0.355,
         tf: 0.57,
         bf: 7.5,
         rx: 7.38,
         ry: 1.65,
       };

       it("should validate compliant I-shape under flexure", () => {
         const Fy = 50; // ksi
         const Lb_actual = 60; // in
         const moments = { M1: 100, M2: 200, Mmid: 120 };

         const result = validateUnbracedLengthRequirements(
           wSection,
           Fy,
           Lb_actual,
           'flexure-only',
           'major',
           moments
         );

         expect(result.limitsApply).toBe(true);
         expect(result.Lpd).toBeDefined();
         expect(result.compliant).toBe(true);
         expect(result.messages[0]).toContain("compliant");
       });

       it("should detect non-compliant unbraced length", () => {
         const Fy = 50; // ksi
         const Lb_actual = 200; // in (very long)
         const moments = { M1: 100, M2: 200, Mmid: 120 };

         const result = validateUnbracedLengthRequirements(
           wSection,
           Fy,
           Lb_actual,
           'flexure-only',
           'major',
           moments
         );

         expect(result.compliant).toBe(false);
         expect(result.messages[0]).toContain("non-compliant");
       });

       it("should handle compression members with both axis checks", () => {
         const Fy = 50; // ksi
         const Lb_actual = 80; // in
         const moments = { M1: 100, M2: 200, Mmid: 120 };

         const result = validateUnbracedLengthRequirements(
           wSection,
           Fy,
           Lb_actual,
           'flexure-compression',
           'major',
           moments
         );

         expect(result.majorAxisLimit).toBeDefined();
         expect(result.minorAxisLimit).toBeDefined();
         expect(result.limitsApply).toBe(true);
       });

                it("should skip validation when limits don't apply", () => {
           const roundSection = {
             Type: "HSS-ROUND" as const,
             name: "HSS6.625X0.25",
             A: 4.78,
             tw: 0.25,
             bf: 6.625,
             d: 6.625,
           };

           const result = validateUnbracedLengthRequirements(
             roundSection,
             50,
             100,
             'flexure-only',
             'major'
           );

         expect(result.limitsApply).toBe(false);
         expect(result.compliant).toBe(true);
         expect(result.messages[0]).toContain("No unbraced length limit applies");
       });

       it("should handle missing moment data", () => {
         const result = validateUnbracedLengthRequirements(
           wSection,
           50,
           100,
           'flexure-only',
           'major'
           // No moments provided
         );

         expect(result.compliant).toBe(false);
         expect(result.messages[0]).toContain("Moment values");
       });
     });
   });

   describe("Axial Force Requirements (AISC Appendix 1.3, Section 2d)", () => {
     describe("Basic Validation", () => {
       it("should validate compliant axial force for plastic hinging", () => {
         const Fy = 50; // ksi
         const A = 14.7; // in² (W18x50)
         const Pu_required = 400; // kips

         const result = validateAxialForceRequirements(Fy, A, Pu_required, true);

         // 0.75 * Fy * A = 0.75 * 50 * 14.7 = 551.25 kips
         const expectedAllowable = 0.75 * Fy * A;

         expect(result.compliant).toBe(true);
         expect(result.allowableStrength).toBeCloseTo(expectedAllowable, 1);
         expect(result.requiredStrength).toBe(Pu_required);
         expect(result.utilizationRatio).toBeCloseTo(Pu_required / expectedAllowable, 3);
         expect(result.message).toContain("compliant");
       });

       it("should detect non-compliant axial force", () => {
         const Fy = 50; // ksi
         const A = 14.7; // in²
         const Pu_required = 600; // kips (exceeds 0.75FyA = 551.25)

         const result = validateAxialForceRequirements(Fy, A, Pu_required, true);

         expect(result.compliant).toBe(false);
         expect(result.utilizationRatio).toBeGreaterThan(1.0);
         expect(result.message).toContain("non-compliant");
       });

       it("should allow higher forces when not subject to plastic hinging", () => {
         const Fy = 50; // ksi
         const A = 14.7; // in²
         const Pu_required = 600; // kips

         const result = validateAxialForceRequirements(Fy, A, Pu_required, false);

         expect(result.compliant).toBe(true);
         expect(result.allowableStrength).toBe(Fy * A); // Full FyA, not 0.75FyA
         expect(result.message).toContain("not subject to plastic hinging");
       });

       it("should handle zero axial load", () => {
         const result = validateAxialForceRequirements(50, 14.7, 0, true);

         expect(result.compliant).toBe(true);
         expect(result.utilizationRatio).toBe(0);
       });

       it("should validate input parameters", () => {
         expect(() => validateAxialForceRequirements(-50, 14.7, 100)).toThrow(
           "Yield strength (Fy) must be positive"
         );
         expect(() => validateAxialForceRequirements(50, -14.7, 100)).toThrow(
           "Gross area (A) must be positive"
         );
         expect(() => validateAxialForceRequirements(50, 14.7, -100)).toThrow(
           "Required axial strength (Pu) must be non-negative"
         );
       });
     });

     describe("Practical Examples", () => {
       it("should validate typical beam-column under plastic hinging", () => {
         // W14x74 properties
         const Fy = 50; // ksi
         const A = 21.8; // in²
         const Pu_required = 650; // kips

         const result = validateAxialForceRequirements(Fy, A, Pu_required, true);

         // 0.75 * 50 * 21.8 = 817.5 kips allowable
         expect(result.allowableStrength).toBeCloseTo(817.5, 1);
         expect(result.compliant).toBe(true);
         expect(result.utilizationRatio).toBeCloseTo(0.795, 2);
       });

       it("should demonstrate capacity reduction for plastic hinging", () => {
         const Fy = 50; // ksi
         const A = 21.8; // in²
         const Pu_required = 900; // kips

         const withPlasticHinging = validateAxialForceRequirements(Fy, A, Pu_required, true);
         const withoutPlasticHinging = validateAxialForceRequirements(Fy, A, Pu_required, false);

         // With plastic hinging: 0.75FyA = 817.5 kips
         // Without plastic hinging: FyA = 1090 kips
         expect(withPlasticHinging.compliant).toBe(false);
         expect(withoutPlasticHinging.compliant).toBe(true);
         expect(withoutPlasticHinging.allowableStrength).toBeGreaterThan(withPlasticHinging.allowableStrength);
       });
     });
   });
 });
