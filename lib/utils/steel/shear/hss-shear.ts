/**
 * @file HSS shear strength calculations according to AISC 360-22, Sections G4 and G5.
 * 
 * This module implements shear strength provisions for hollow structural sections (HSS),
 * including both rectangular HSS (Section G4) and round HSS (Section G5).
 * 
 * @source AISC 360-22, Chapter G - Design of Members for Shear
 */

import {
  E,
  ShearLimitState,
  ERROR_MESSAGES,
} from "../constants";
import { Section } from "../slenderness/types";
import { ShearStrengthResult } from "./types";

/**
 * AISC 360-22 Section G4 - Rectangular HSS, Box Sections, and Other Singly and Doubly Symmetric Members
 * 
 * @source AISC 360-22, Section G4
 */
const G4_CONSTANTS = {
  /** Shear yielding coefficient - AISC Eq. G4-1 */
  SHEAR_COEFFICIENT: 0.6,
  /** Web plate shear buckling coefficient for unstiffened rectangular HSS webs */
  KV_UNSTIFFENED: 5.34,
  /** Web shear strength coefficient threshold 1 - similar to G2 provisions */
  CV1_THRESHOLD_1: 1.10,
  /** Web shear strength coefficient threshold 2 - similar to G2 provisions */
  CV1_THRESHOLD_2: 1.37,
} as const;

/**
 * AISC 360-22 Section G5 - Round HSS
 * 
 * @source AISC 360-22, Section G5
 */
const G5_CONSTANTS = {
  /** Shear yielding coefficient for round HSS - AISC Eq. G5-1 */
  SHEAR_COEFFICIENT: 0.6,
  /** Limiting D/t ratio for round HSS elastic buckling */
  ROUND_HSS_LIMIT_1: 0.07,
  /** Limiting D/t ratio for round HSS inelastic buckling */
  ROUND_HSS_LIMIT_2: 0.31,
} as const;

/**
 * Determines the web shear strength coefficient Cv for rectangular HSS sections.
 * 
 * @param h_over_t - Web slenderness ratio (h/t) where h is clear distance between flanges
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Web shear strength coefficient Cv
 * 
 * @source AISC 360-22, Section G4 (similar to G2.1(b) provisions)
 */
function getHSSWebShearStrengthCoefficient(h_over_t: number, Fy: number): number {
  const kv = G4_CONSTANTS.KV_UNSTIFFENED;
  const lambda_v = h_over_t / Math.sqrt(kv * E / Fy);

  if (lambda_v <= G4_CONSTANTS.CV1_THRESHOLD_1) {
    // Cv = 1.0 - compact web
    return 1.0;
  } else if (lambda_v <= G4_CONSTANTS.CV1_THRESHOLD_2) {
    // Cv = 1.10√(kvE/Fy) / (h/t) - inelastic buckling
    return G4_CONSTANTS.CV1_THRESHOLD_1 * Math.sqrt(kv * E / Fy) / h_over_t;
  } else {
    // Cv = 1.51kvE / [(h/t)²Fy] - elastic buckling
    return 1.51 * kv * E / (Math.pow(h_over_t, 2) * Fy);
  }
}

/**
 * Calculates shear strength for round HSS sections.
 * 
 * @param D - Outside diameter in inches
 * @param t - Wall thickness in inches
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Shear strength calculation result
 * 
 * @source AISC 360-22, Section G5
 */
function calculateRoundHSSShear(D: number, t: number, Fy: number): ShearStrengthResult {
  const A = Math.PI * D * t; // Shear area for round HSS
  const D_over_t = D / t;
  
  // Check D/t limits for round HSS - AISC G5
  const limit1 = G5_CONSTANTS.ROUND_HSS_LIMIT_1 * E / Fy; // 0.07E/Fy
  const limit2 = G5_CONSTANTS.ROUND_HSS_LIMIT_2 * E / Fy; // 0.31E/Fy
  
  let Cv: number;
  let governingLimitState: ShearLimitState;
  
  if (D_over_t <= limit1) {
    // Compact - no reduction
    Cv = 1.0;
    governingLimitState = ShearLimitState.SHEAR_YIELDING;
  } else if (D_over_t <= limit2) {
    // Inelastic buckling
    Cv = (0.31 * E / Fy) / D_over_t;
    governingLimitState = ShearLimitState.SHEAR_BUCKLING;
  } else {
    // Elastic buckling
    Cv = (0.31 * E / Fy) / D_over_t;
    governingLimitState = ShearLimitState.SHEAR_BUCKLING;
  }
  
  // AISC Eq. G5-1
  const Vn = G5_CONSTANTS.SHEAR_COEFFICIENT * Fy * A * Cv;
  
  return {
    Vn,
    governingLimitState,
    metadata: {
      h_over_tw: D_over_t, // Using D/t for round sections
      Cv1: Cv,
    }
  };
}

/**
 * Calculates the nominal shear strength (Vn) for HSS members (rectangular and round).
 * 
 * This function implements AISC 360-22 provisions for both rectangular HSS (Section G4)
 * and round HSS (Section G5), with proper handling of web slenderness and buckling.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @returns The nominal shear strength and governing limit state
 * 
 * @throws {Error} If required section properties are missing or invalid
 * 
 * @source AISC 360-22, Sections G4 and G5
 * 
 * @example
 * ```typescript
 * // Rectangular HSS
 * const rectHSS: Section = { d: 8.0, tw: 0.375, bf: 4.0, tf: 0.375, ... };
 * const result = calculateVn_HSS(rectHSS, 50);
 * 
 * // Round HSS  
 * const roundHSS: Section = { d: 8.625, tw: 0.322, Type: "HSS-ROUND", ... };
 * const result = calculateVn_HSS(roundHSS, 50);
 * ```
 */
export function calculateVn_HSS(
  section: Section,
  Fy: number
): ShearStrengthResult {
  const { d, tw, Type } = section;

  // Validate required properties
  if (typeof d !== "number" || typeof tw !== "number" || d <= 0 || tw <= 0) {
    throw new Error(ERROR_MESSAGES.HSS_SHEAR_PROPS_REQUIRED);
  }

  // Handle round HSS differently than rectangular HSS
  if (Type === "HSS-ROUND") {
    return calculateRoundHSSShear(d, tw, Fy);
  }

  // Rectangular HSS calculation - AISC Section G4
  const { tf, bf } = section;
  
  if (typeof tf !== "number" || typeof bf !== "number" || tf <= 0 || bf <= 0) {
    throw new Error("Rectangular HSS requires numeric 'd', 'tw', 'tf', and 'bf' properties.");
  }

  // Calculate web geometry for rectangular HSS
  // For HSS, h is the clear distance between flanges
  const h = d - 2 * tf;
  const h_over_t = h / tw;
  
  // Web area for shear (one web for rectangular HSS)
  const Aw = h * tw;
  
  // Calculate web shear strength coefficient
  const Cv = getHSSWebShearStrengthCoefficient(h_over_t, Fy);
  
  // Determine governing limit state
  const governingLimitState = Cv >= 1.0 
    ? ShearLimitState.SHEAR_YIELDING 
    : ShearLimitState.SHEAR_BUCKLING;
  
  // Calculate nominal shear strength - AISC Eq. G4-1
  const Vn = G4_CONSTANTS.SHEAR_COEFFICIENT * Fy * Aw * Cv;
  
  return {
    Vn,
    governingLimitState,
    metadata: {
      h,
      h_over_tw: h_over_t,
      kv: G4_CONSTANTS.KV_UNSTIFFENED,
      Cv1: Cv,
    }
  };
} 