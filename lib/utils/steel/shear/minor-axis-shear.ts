/**
 * @file Minor-axis shear strength calculations according to AISC 360-22, Section G6.
 * 
 * This module implements shear strength provisions for doubly and singly symmetric 
 * members loaded in the minor axis without torsion, including I-shaped members, 
 * tees, and channels.
 * 
 * @source AISC 360-22, Chapter G - Design of Members for Shear
 */

import {
  E,
  ShearLimitState,
  ERROR_MESSAGES,
} from "../constants";
import { Section } from "../slenderness/types";
import { ShearStrengthResult } from "./types";

/**
 * AISC 360-22 Section G6 - Doubly Symmetric and Singly Symmetric Members Subjected to Minor-Axis Shear
 * 
 * @source AISC 360-22, Section G6
 */
const G6_CONSTANTS = {
  /** Shear yielding coefficient - AISC Eq. G6-1 */
  SHEAR_COEFFICIENT: 0.6,
  /** Web plate shear buckling coefficient for minor axis shear */
  KV_MINOR_AXIS: 1.2,
  /** Web shear strength coefficient threshold 1 - similar to G2 provisions */
  CV2_THRESHOLD_1: 1.10,
  /** Web shear strength coefficient threshold 2 - similar to G2 provisions */
  CV2_THRESHOLD_2: 1.37,
  /** Maximum yield strength for Cv2 = 1.0 per User Note */
  MAX_FY_FOR_CV2_UNITY: 70, // ksi
} as const;

/**
 * Determines the web shear strength coefficient Cv2 for minor-axis shear.
 * 
 * For minor-axis shear, the slenderness parameter is calculated differently:
 * - I-shaped members and tees: h/tw = bf/(2*tf)
 * - Channels: h/tw = bf/tf
 * 
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Web shear strength coefficient Cv2
 * 
 * @source AISC 360-22, Section G6 with reference to G2.2
 */
function getMinorAxisShearStrengthCoefficient(
  section: Section, 
  Fy: number
): { Cv2: number; lambda_v: number; h_over_tw_equiv: number } {
  const { bf, tf, Type } = section;
  
  if (typeof bf !== "number" || typeof tf !== "number") {
    throw new Error("Flange width (bf) and thickness (tf) are required for minor-axis shear calculations");
  }

  // Calculate equivalent h/tw for minor-axis shear
  let h_over_tw_equiv: number;
  
  if (Type === "C") {
    // Channels: h/tw = bf/tf
    h_over_tw_equiv = bf / tf;
  } else {
    // I-shaped members and tees: h/tw = bf/(2*tf)
    h_over_tw_equiv = bf / (2 * tf);
  }

  const kv = G6_CONSTANTS.KV_MINOR_AXIS; // kv = 1.2 for minor-axis shear
  const lambda_v = h_over_tw_equiv / Math.sqrt(kv * E / Fy);

  // User Note: Cv2 = 1.0 for all ASTM A6/A6M W, S, M, and HP shapes when Fy ≤ 70 ksi
  // Note: HP shapes are not in the current Type enum, but W, S, M are covered by "I" type
  // Only apply this for standard rolled sections, not modified or built-up sections
  const isStandardRolledSection = (Type === "W" || Type === "I") && 
                                  h_over_tw_equiv < 15; // Reasonable limit for standard sections
  
  if (Fy <= G6_CONSTANTS.MAX_FY_FOR_CV2_UNITY && isStandardRolledSection) {
    return { Cv2: 1.0, lambda_v, h_over_tw_equiv };
  }

  let Cv2: number;

  if (lambda_v <= G6_CONSTANTS.CV2_THRESHOLD_1) {
    // Cv2 = 1.0 - compact flange
    Cv2 = 1.0;
  } else if (lambda_v <= G6_CONSTANTS.CV2_THRESHOLD_2) {
    // Cv2 = 1.10√(kvE/Fy) / (h/tw) - inelastic buckling
    Cv2 = G6_CONSTANTS.CV2_THRESHOLD_1 * Math.sqrt(kv * E / Fy) / h_over_tw_equiv;
  } else {
    // Cv2 = 1.51kvE / [(h/tw)²Fy] - elastic buckling
    Cv2 = 1.51 * kv * E / (Math.pow(h_over_tw_equiv, 2) * Fy);
  }

  return { Cv2, lambda_v, h_over_tw_equiv };
}

/**
 * Calculates the nominal minor-axis shear strength (Vn) for doubly and singly symmetric members.
 * 
 * This function implements AISC 360-22 Section G6 provisions for members loaded in the 
 * minor axis without torsion. The calculation considers flange local buckling effects
 * and uses the appropriate slenderness parameters for different section types.
 * 
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param options - Optional parameters for analysis
 * @param options.considerTorsion - Whether to consider torsional effects (not covered by G6)
 * @returns The nominal minor-axis shear strength and governing limit state
 * 
 * @throws {Error} If required section properties are missing or invalid
 * 
 * @source AISC 360-22, Section G6 - "Doubly Symmetric and Singly Symmetric Members Subjected to Minor-Axis Shear"
 * 
 * @example
 * ```typescript
 * const beam: Section = { bf: 8.0, tf: 0.5, Type: "W", ... };
 * const result = calculateVn_MinorAxis(beam, 50);
 * console.log(`Minor-axis Vn = ${result.Vn} kips, ${result.governingLimitState}`);
 * ```
 */
export function calculateVn_MinorAxis(
  section: Section,
  Fy: number,
  options: {
    considerTorsion?: boolean;
  } = {}
): ShearStrengthResult {
  const { bf, tf, Type } = section;

  // Validate required properties
  if (typeof bf !== "number" || typeof tf !== "number" || bf <= 0 || tf <= 0) {
    throw new Error("Flange width (bf) and thickness (tf) must be positive numbers for minor-axis shear calculations");
  }

  if (typeof Fy !== "number" || Fy <= 0) {
    throw new Error("Yield strength (Fy) must be a positive number");
  }

  const { considerTorsion = false } = options;

  // Warning for torsion - G6 does not consider torsional effects
  if (considerTorsion) {
    console.warn("G6 provisions do not consider torsional effects. Use appropriate torsional analysis if required.");
  }

  // Calculate web shear strength coefficient Cv2
  const { Cv2, lambda_v, h_over_tw_equiv } = getMinorAxisShearStrengthCoefficient(section, Fy);

  // Calculate shear area for minor-axis shear
  // Each shear resisting element: Av = bf * tf
  const Av = bf * tf;

  // Determine governing limit state
  const governingLimitState = Cv2 >= 1.0 
    ? ShearLimitState.SHEAR_YIELDING 
    : ShearLimitState.SHEAR_BUCKLING;

  // Calculate nominal minor-axis shear strength - AISC Eq. G6-1
  // Vn = 0.6 * Fy * bf * tf * Cv2
  const Vn = G6_CONSTANTS.SHEAR_COEFFICIENT * Fy * Av * Cv2;

  return {
    Vn,
    governingLimitState,
    metadata: {
      h_over_tw: h_over_tw_equiv, // Equivalent slenderness for minor-axis
      kv: G6_CONSTANTS.KV_MINOR_AXIS,
      Cv1: Cv2, // Using Cv1 field to store Cv2 for consistency
      qualifiesForG2_1a: false, // G6 doesn't have enhanced resistance factors
      effectiveArea: Av,
      lambda_v,
      sectionType: Type,
      considerTorsion,
    }
  };
}

/**
 * Determines the applicable shear resistance factors for minor-axis shear.
 * 
 * Minor-axis shear uses the general resistance factors from G1(a) since
 * there are no enhanced resistance factors for G6 provisions.
 * 
 * @param section - Cross-section properties (for future enhancements)
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Resistance factors and source information
 * 
 * @source AISC 360-22, Section G1(a) - General provisions apply to G6
 */
export function getMinorAxisShearResistanceFactors(
  section: Section,
  Fy: number
): {
  phi_v: number;
  omega_v: number;
  source: string;
} {
  // G6 uses general resistance factors from G1(a)
  return {
    phi_v: 0.90, // LRFD resistance factor
    omega_v: 1.67, // ASD safety factor
    source: "AISC 360-22, Section G1(a) - General provisions (applied to G6 minor-axis shear)"
  };
}

/**
 * Simplified calculation for preliminary design of minor-axis shear strength.
 * 
 * This function provides a quick estimate using conservative assumptions
 * suitable for preliminary design of members under minor-axis shear.
 * 
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Simplified minor-axis shear strength calculation
 * 
 * @source AISC 360-22, Section G6 (simplified approach)
 */
export function calculateVn_MinorAxis_Simplified(
  section: Section,
  Fy: number
): ShearStrengthResult {
  return calculateVn_MinorAxis(section, Fy, { 
    considerTorsion: false 
  });
} 