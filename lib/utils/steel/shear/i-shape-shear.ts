/**
 * @file I-Shape shear strength calculations according to AISC 360-22, Section G2.
 *
 * This module implements the shear strength provisions for I-shaped members and channels,
 * including determination of web shear strength coefficient Cv1 and proper handling
 * of both stiffened and unstiffened webs.
 *
 * @source AISC 360-22, Chapter G - Design of Members for Shear
 */

import { E, ShearLimitState, ERROR_MESSAGES } from "../constants";
import { Section } from "../slenderness/types";
import { ShearStrengthResult, InelasticAnalysisOptions } from "./types";
import {
  calculateReducedMaterialProperties,
  shouldApplyStiffnessReduction,
  validateInelasticAnalysisOptions,
  validateCrossSectionRequirements,
} from "./inelastic-analysis";

/**
 * AISC 360-22 Section G1 - General Provisions
 * Design shear strength: φvVn (LRFD) or Vn/Ωv (ASD)
 *
 * @source AISC 360-22, Section G1(a)
 */
const G1_RESISTANCE_FACTORS = {
  /** LRFD resistance factor for all sections except G2.1(a) - AISC G1(a) */
  PHI_V_GENERAL: 0.9,
  /** ASD safety factor for all sections except G2.1(a) - AISC G1(a) */
  OMEGA_V_GENERAL: 1.67,
  /** LRFD resistance factor for rolled I-shapes meeting G2.1(a) criteria - AISC G2.1(a) */
  PHI_V_ROLLED_COMPACT: 1.0,
  /** ASD safety factor for rolled I-shapes meeting G2.1(a) criteria - AISC G2.1(a) */
  OMEGA_V_ROLLED_COMPACT: 1.5,
} as const;

/**
 * AISC 360-22 Section G2 - I-Shaped Members and Channels
 *
 * @source AISC 360-22, Section G2
 */
const G2_CONSTANTS = {
  /** Shear yielding coefficient - AISC Eq. G2-1 */
  SHEAR_COEFFICIENT: 0.6,
  /** Web slenderness limit for compact webs in rolled I-shapes - AISC G2.1(a) */
  ROLLED_WEB_SLENDERNESS_LIMIT: 2.24,
  /** Web shear strength coefficient threshold 1 - AISC G2.1(b)(1)(i) */
  CV1_THRESHOLD_1: 1.1,
  /** Web shear strength coefficient threshold 2 - AISC G2.1(b)(1)(ii) */
  CV1_THRESHOLD_2: 1.37,
  /** Web plate shear buckling coefficient for unstiffened webs - AISC G2.1(b)(2)(i) */
  KV_UNSTIFFENED: 5.34,
} as const;

/**
 * Determines the web plate shear buckling coefficient kv for I-shaped sections.
 *
 * @param hasTransverseStiffeners - Whether the web has transverse stiffeners
 * @param a_over_h - Ratio of clear distance between stiffeners to web depth (for stiffened webs)
 * @returns Web plate shear buckling coefficient kv
 *
 * @source AISC 360-22, Section G2.1(b)(2)
 */
function getWebPlateBucklingCoefficient(
  hasTransverseStiffeners: boolean = false,
  a_over_h?: number
): number {
  if (!hasTransverseStiffeners) {
    // For webs without transverse stiffeners - AISC G2.1(b)(2)(i)
    return G2_CONSTANTS.KV_UNSTIFFENED;
  } else {
    // For webs with transverse stiffeners - AISC G2.1(b)(2)(ii)
    // kv = 5 + 5/(a/h)²
    if (a_over_h && a_over_h > 0) {
      const kv = 5 + 5 / Math.pow(a_over_h, 2);
      // When a/h > 3.0, kv = 5.34 - AISC G2.1(b)(2)(ii)
      return a_over_h > 3.0 ? G2_CONSTANTS.KV_UNSTIFFENED : kv;
    } else {
      // Default to unstiffened if a/h not provided
      return G2_CONSTANTS.KV_UNSTIFFENED;
    }
  }
}

/**
 * Determines the web shear strength coefficient Cv1 for I-shaped sections.
 *
 * @param h_over_tw - Web slenderness ratio (h/tw)
 * @param Fy - Specified minimum yield stress in ksi
 * @param kv - Web plate shear buckling coefficient
 * @param E_effective - Effective modulus of elasticity (may be reduced for inelastic analysis)
 * @returns Web shear strength coefficient Cv1
 *
 * @source AISC 360-22, Section G2.1(b)(1)
 */
function getWebShearStrengthCoefficient(
  h_over_tw: number,
  Fy: number,
  kv: number,
  E_effective: number = E
): number {
  const lambda_v = h_over_tw / Math.sqrt((kv * E_effective) / Fy);

  if (lambda_v <= G2_CONSTANTS.CV1_THRESHOLD_1) {
    // Cv1 = 1.0 - AISC G2.1(b)(1)(i)
    return 1.0;
  } else if (lambda_v <= G2_CONSTANTS.CV1_THRESHOLD_2) {
    // Cv1 = 1.10√(kvE/Fy) / (h/tw) - AISC Eq. G2-4
    return (
      (G2_CONSTANTS.CV1_THRESHOLD_1 * Math.sqrt((kv * E_effective) / Fy)) / h_over_tw
    );
  } else {
    // Cv1 = 1.51kvE / [(h/tw)²Fy] - AISC Eq. G2-11 (corrected from specification)
    return (1.51 * kv * E_effective) / (Math.pow(h_over_tw, 2) * Fy);
  }
}

/**
 * Checks if a rolled I-shaped section qualifies for the special provisions
 * of AISC Section G2.1(a) with enhanced resistance factors.
 *
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress in ksi
 * @returns True if section qualifies for G2.1(a) provisions
 *
 * @source AISC 360-22, Section G2.1(a)
 */
function qualifiesForG2_1a(section: Section, Fy: number): boolean {
  if (!section.tf || !section.tw) return false;

  const h = section.d - 2 * section.tf;
  const h_over_tw = h / section.tw;
  const limit = G2_CONSTANTS.ROLLED_WEB_SLENDERNESS_LIMIT * Math.sqrt(E / Fy);

  // Must be rolled I-shape with h/tw ≤ 2.24√(E/Fy) and Fy = 345 MPa (50 ksi)
  // Note: AISC specifies this applies to current ASTM A6/A6M W, S, and HP shapes
  return h_over_tw <= limit && Fy <= 50;
}

/**
 * Calculates the nominal shear strength (Vn) for I-shaped sections and channels.
 *
 * This function implements the complete shear strength calculation per AISC 360-22,
 * including proper determination of the web shear strength coefficient Cv1 and
 * handling of both compact and slender webs. Supports inelastic analysis with
 * stiffness reduction per AISC Appendix 1.3.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param options - Optional parameters for advanced analysis
 * @param options.hasTransverseStiffeners - Whether the web has transverse stiffeners
 * @param options.a_over_h - Ratio of stiffener spacing to web depth (for stiffened webs)
 * @param options.inelasticAnalysis - Inelastic analysis options for stiffness reduction
 * @returns The nominal shear strength and governing limit state
 *
 * @throws {Error} If required section properties are missing or invalid
 *
 * @source AISC 360-22, Section G2.1 - "Shear Strength of Webs"
 * @source AISC 360-22, Appendix 1.3 - "Design by Inelastic Analysis"
 *
 * @example
 * ```typescript
 * const section: Section = { d: 18.0, tw: 0.355, tf: 0.57, ... };
 * const result = calculateVn_I_Shape(section, 50);
 * console.log(`Vn = ${result.Vn} kips, ${result.governingLimitState}`);
 * 
 * // With inelastic analysis stiffness reduction
 * const resultInelastic = calculateVn_I_Shape(section, 50, {
 *   inelasticAnalysis: {
 *     applyStiffnessReduction: true,
 *     Pr: 150, Pn: 300,
 *     designMethod: DesignMethod.LRFD
 *   }
 * });
 * ```
 */
export function calculateVn_I_Shape(
  section: Section,
  Fy: number,
  options: {
    hasTransverseStiffeners?: boolean;
    a_over_h?: number;
    inelasticAnalysis?: InelasticAnalysisOptions;
  } = {}
): ShearStrengthResult {
  const { d, tf, tw } = section;

  // Validate required properties per AISC G2
  if (
    typeof d !== "number" ||
    typeof tw !== "number" ||
    typeof tf !== "number" ||
    d <= 0 ||
    tw <= 0 ||
    tf <= 0
  ) {
    throw new Error(ERROR_MESSAGES.SHEAR_SECTION_PROPS_REQUIRED);
  }

  // Handle inelastic analysis stiffness reduction per AISC Appendix 1.3
  let E_effective = E;
  let stiffnessReduction;
  let ductilityValidation;
  
  if (options.inelasticAnalysis && shouldApplyStiffnessReduction(options.inelasticAnalysis)) {
    validateInelasticAnalysisOptions(options.inelasticAnalysis);
    const materialProps = calculateReducedMaterialProperties(options.inelasticAnalysis);
    E_effective = materialProps.E_reduced;
    
    stiffnessReduction = {
      baseReductionFactor: 0.8,
      tauB: materialProps.tauB,
      totalReduction: materialProps.reductionFactor,
      appliedToModulus: true,
    };
  }

  // Validate ductility requirements if requested
  if (options.inelasticAnalysis && options.inelasticAnalysis.validateDuctilityRequirements) {
    ductilityValidation = validateCrossSectionRequirements(section, Fy, {
      Pu: options.inelasticAnalysis.Pu,
      Py: options.inelasticAnalysis.Py,
      phiC: options.inelasticAnalysis.phiC,
      subjectToPlasticHinging: options.inelasticAnalysis.subjectToPlasticHinging,
    });
  }

  // Calculate web geometry - AISC G2
  const h = d - 2 * tf; // Clear distance between flanges
  const Aw = d * tw; // Area of the web (use full depth per AISC convention)
  const h_over_tw = h / tw; // Web slenderness ratio

  // Determine web plate shear buckling coefficient - AISC G2.1(b)(2)
  const kv = getWebPlateBucklingCoefficient(
    options.hasTransverseStiffeners,
    options.a_over_h
  );

  // Calculate web shear strength coefficient with effective modulus - AISC G2.1(b)(1)
  const Cv1 = getWebShearStrengthCoefficient(h_over_tw, Fy, kv, E_effective);

  // Determine governing limit state based on Cv1 value
  let governingLimitState: ShearLimitState;
  if (Cv1 >= 1.0) {
    governingLimitState = ShearLimitState.SHEAR_YIELDING;
  } else {
    governingLimitState = ShearLimitState.SHEAR_BUCKLING;
  }

  // Calculate nominal shear strength - AISC Eq. G2-1
  const Vn = G2_CONSTANTS.SHEAR_COEFFICIENT * Fy * Aw * Cv1;

  return {
    Vn,
    governingLimitState,
    // Additional metadata for transparency
    metadata: {
      h,
      h_over_tw,
      kv,
      Cv1,
      qualifiesForG2_1a: qualifiesForG2_1a(section, Fy),
      ...(stiffnessReduction && { stiffnessReduction }),
      ...(ductilityValidation && { ductilityValidation }),
    },
  };
}

/**
 * Gets the appropriate resistance factors for I-shaped shear design.
 *
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Object containing LRFD and ASD resistance factors
 *
 * @source AISC 360-22, Sections G1(a) and G2.1(a)
 */
export function getShearResistanceFactors(section: Section, Fy: number) {
  if (qualifiesForG2_1a(section, Fy)) {
    return {
      phi_v: G1_RESISTANCE_FACTORS.PHI_V_ROLLED_COMPACT,
      omega_v: G1_RESISTANCE_FACTORS.OMEGA_V_ROLLED_COMPACT,
      source: "AISC G2.1(a) - Rolled I-shapes with compact webs",
    };
  } else {
    return {
      phi_v: G1_RESISTANCE_FACTORS.PHI_V_GENERAL,
      omega_v: G1_RESISTANCE_FACTORS.OMEGA_V_GENERAL,
      source: "AISC G1(a) - General provisions",
    };
  }
}
