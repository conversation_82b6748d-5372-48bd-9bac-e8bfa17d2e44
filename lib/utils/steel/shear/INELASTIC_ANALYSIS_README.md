# Inelastic Analysis Support for Shear Strength Calculations

## Overview

This implementation adds comprehensive support for **AISC 360-22 Appendix 1.3 "Design by Inelastic Analysis"** requirements to the shear strength calculation module. The implementation follows the stiffness reduction methodology shown in the attached AISC specification image.

## Key Features

### ✅ Stiffness Reduction Implementation
- **0.8τb reduction factor** applied to E and G per AISC Appendix 1.3 User Note
- **τb calculation** per AISC Section C2.3 (Equations C2-2a and C2-2b)
- **Torsional properties** (GJ and ECw) reduction support
- **Cross-section strength** (FyAg) option for nominal compressive strength

### ✅ Complete Integration
- **Seamless integration** with existing shear strength calculations
- **Backward compatibility** - no changes to existing API
- **Optional parameters** - inelastic analysis is opt-in
- **Comprehensive validation** and error handling

### ✅ Production Ready
- **32 comprehensive tests** covering all scenarios
- **Manual verification** against AISC calculations
- **Edge case handling** and robust error messages
- **Type safety** with TypeScript interfaces

## Implementation Details

### Core Functions

#### `calculateReducedMaterialProperties()`
Calculates reduced E and G values per AISC Appendix 1.3:

```typescript
const options: InelasticAnalysisOptions = {
  applyStiffnessReduction: true,
  Pr: 200, // Required axial strength, kips
  Pn: 500, // Nominal axial strength, kips
  designMethod: DesignMethod.LRFD
};

const result = calculateReducedMaterialProperties(options);
// Returns: E_reduced, G_reduced, reductionFactor, tauB
```

#### `calculateVn_I_Shape()` with Inelastic Analysis
Enhanced I-shape shear calculation with stiffness reduction:

```typescript
const section = { name: "W18X50", Type: "W", d: 18.0, tw: 0.355, ... };

const result = calculateVn_I_Shape(section, 50, {
  inelasticAnalysis: {
    applyStiffnessReduction: true,
    Pr: 180,
    Pn: 735, // FyAg for cross-section strength
    designMethod: DesignMethod.LRFD
  }
});
```

### AISC Appendix 1.3 Compliance

#### 1. Stiffness Reduction (User Note)
> "One practical method of including stiffness reduction is to reduce E and G by 0.8τb, thereby leaving all cross-section geometric properties at their nominal value."

**Implementation:**
- ✅ E and G reduced by 0.8τb factor
- ✅ Geometric properties remain nominal
- ✅ Applied to all relevant calculations

#### 2. τb Calculation (AISC C2.3)
**LRFD:** α = 1.0, **ASD:** α = 1.6

- **When αPr/Pn ≤ 0.5:** τb = 1.0 (Equation C2-2a)
- **When αPr/Pn > 0.5:** τb = 4(αPr/Pn)[1-(αPr/Pn)] (Equation C2-2b)

#### 3. Available Strengths (Section 3)
> "The nominal compressive strength of members, Pn, may be taken as the cross-section compressive strength, FyAg, or as FyAe for members with slender elements."

**Implementation:**
```typescript
// For non-slender sections
const Pn = calculateInelasticAnalysisPn(Fy, Ag); // FyAg

// For slender sections  
const Pn = calculateInelasticAnalysisPn(Fy, Ag, Ae, true); // FyAe
```

#### 4. Universal Application
> "Stiffness reduction should be applied to all member properties including torsional properties (GJ and ECw) affecting twist of the member cross section."

**Implementation:**
- ✅ E reduction affects all elastic buckling calculations
- ✅ G reduction available for torsional properties
- ✅ Consistent application across all members

## Usage Examples

### Basic Inelastic Analysis
```typescript
import { calculateVn_I_Shape, DesignMethod } from '@/shear';

const beam = {
  name: "W18X50",
  Type: "W" as const,
  A: 14.7, d: 18.0, tw: 0.355, tf: 0.57, bf: 7.5
};

// Standard calculation
const standardResult = calculateVn_I_Shape(beam, 50);

// With inelastic analysis stiffness reduction
const inelasticResult = calculateVn_I_Shape(beam, 50, {
  inelasticAnalysis: {
    applyStiffnessReduction: true,
    Pr: 180, // Required compression, kips
    Pn: 735, // FyAg = 50 * 14.7, kips
    designMethod: DesignMethod.LRFD
  }
});

console.log('Standard Vn:', standardResult.Vn);
console.log('Inelastic Vn:', inelasticResult.Vn);
console.log('Reduction factor:', inelasticResult.metadata?.stiffnessReduction?.totalReduction);
```

### Custom Reduction Factor
```typescript
const customResult = calculateVn_I_Shape(beam, 50, {
  inelasticAnalysis: {
    applyStiffnessReduction: true,
    customReductionFactor: 0.65 // Direct specification
  }
});
```

### Complete Workflow
```typescript
import { 
  calculateInelasticAnalysisPn,
  calculateReducedMaterialProperties,
  getInelasticAnalysisGuidance 
} from '@/shear';

// 1. Calculate nominal strength for inelastic analysis
const Fy = 50; // ksi
const Ag = 14.7; // in²
const Pn = calculateInelasticAnalysisPn(Fy, Ag); // 735 kips

// 2. Get reduced material properties
const materialProps = calculateReducedMaterialProperties({
  applyStiffnessReduction: true,
  Pr: 180,
  Pn: 735,
  designMethod: DesignMethod.LRFD
});

console.log('E reduced:', materialProps.E_reduced); // ~23,200 ksi
console.log('G reduced:', materialProps.G_reduced); // ~8,960 ksi
console.log('τb:', materialProps.tauB); // 1.0 (since αPr/Pn = 0.245 ≤ 0.5)

// 3. Apply to shear calculation
const result = calculateVn_I_Shape(beam, Fy, {
  inelasticAnalysis: {
    applyStiffnessReduction: true,
    Pr: 180,
    Pn: 735,
    designMethod: DesignMethod.LRFD
  }
});

// 4. Get guidance for implementation
const guidance = getInelasticAnalysisGuidance();
console.log('Considerations:', guidance.considerations);
console.log('Recommendations:', guidance.recommendations);
```

## Technical Implementation

### Stiffness Reduction in Shear Calculations

The implementation modifies the web shear strength coefficient (Cv1) calculation by using reduced modulus:

```typescript
// Standard calculation
const lambda_v = h_over_tw / Math.sqrt((kv * E) / Fy);

// With inelastic analysis
const E_effective = E * reductionFactor; // 0.8τb
const lambda_v = h_over_tw / Math.sqrt((kv * E_effective) / Fy);
```

This affects:
- **Compact webs:** Cv1 = 1.0 (no change)
- **Intermediate webs:** Cv1 = 1.10√(kvE/Fy) / (h/tw) (reduced E affects result)
- **Slender webs:** Cv1 = 1.51kvE / [(h/tw)²Fy] (reduced E affects result)

### Metadata Tracking

All calculations include comprehensive metadata:

```typescript
interface StiffnessReductionMetadata {
  baseReductionFactor: number; // 0.80
  tauB: number; // Calculated τb value
  totalReduction: number; // Combined factor
  appliedToModulus: boolean; // true
}
```

## Validation and Testing

### Test Coverage (32 Tests)

#### Core Functionality (8 tests)
- ✅ Original properties when reduction not applied
- ✅ 0.8τb calculation for LRFD (αPr/Pn ≤ 0.5)
- ✅ 0.8τb calculation for LRFD (αPr/Pn > 0.5)
- ✅ 0.8τb calculation for ASD
- ✅ Custom reduction factor handling
- ✅ Custom material properties
- ✅ Error handling for invalid inputs
- ✅ Missing parameter validation

#### Utility Functions (16 tests)
- ✅ Stiffness reduction applicability checks
- ✅ Comprehensive input validation
- ✅ Analysis guidance generation
- ✅ Shear modulus reduction
- ✅ Nominal strength calculation (FyAg vs FyAe)

#### Integration Tests (5 tests)
- ✅ Normal shear calculation (baseline)
- ✅ Stiffness reduction in shear calculation
- ✅ Custom reduction factor integration
- ✅ Validation error propagation
- ✅ Complete workflow demonstration

#### Edge Cases (3 tests)
- ✅ Zero axial load (Pr = 0)
- ✅ High axial loads approaching Pn
- ✅ Boundary condition (αPr/Pn = 0.5)

### Manual Verification

All calculations manually verified against AISC equations:

**Example: W18×50 with Pr = 180 kips**
- Pn = FyAg = 50 × 14.7 = 735 kips
- αPr/Pn = 1.0 × 180/735 = 0.245 ≤ 0.5
- τb = 1.0 (per C2-2a)
- Reduction factor = 0.8 × 1.0 = 0.8
- E_reduced = 29,000 × 0.8 = 23,200 ksi ✅

## Design Considerations

### When to Use Inelastic Analysis

**Applicable for:**
- ✅ Second-order elastic analysis with direct imperfection modeling
- ✅ Members subject to compression loads
- ✅ Structures where stability is critical
- ✅ Advanced analysis requiring stiffness reduction

**Not applicable for:**
- ❌ First-order analysis
- ❌ Effective length method
- ❌ Members with tension-only loads
- ❌ Simple beam analysis without compression

### Implementation Guidelines

1. **Consistent Application**
   - Apply stiffness reduction to ALL members
   - Avoid selective application to prevent force redistribution
   - Document assumptions clearly

2. **Strength Calculations**
   - Use cross-section strength (FyAg) when appropriate
   - Consider slender elements (FyAe) when applicable
   - No additional stability considerations needed

3. **Analysis Considerations**
   - Ensure second-order analysis includes imperfections
   - Verify convergence with reduced stiffnesses
   - Consider iteration requirements

## Future Enhancements

### Planned Extensions
- [ ] HSS shear integration with inelastic analysis
- [ ] Angle shear integration with inelastic analysis  
- [ ] Minor-axis shear integration with inelastic analysis
- [ ] Torsional property reduction (GJ, ECw) utilities
- [ ] Advanced analysis workflow examples

### Advanced Features
- [ ] Iterative stiffness reduction for convergence
- [ ] Multiple load case handling
- [ ] Stability analysis integration
- [ ] Performance optimization for large structures

## References

- **AISC 360-22, Appendix 1.3** - "Design by Inelastic Analysis"
- **AISC 360-22, Section C2.3** - "Adjustments to Stiffness"
- **AISC 360-22, Chapter G** - "Design of Members for Shear"
- **AISC Steel Construction Manual, 16th Edition**

---

*This implementation provides production-ready support for inelastic analysis in shear strength calculations, fully compliant with AISC 360-22 requirements and extensively tested for reliability.* 