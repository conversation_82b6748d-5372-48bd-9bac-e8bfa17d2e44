/**
 * @file Main entry point for steel shear strength calculations.
 * Routes calculations to appropriate section-specific modules.
 */

import {
  Material,
  ERROR_MESSAGES,
  SECTION_TYPE_STRINGS,
} from "../constants";
import { Section } from "../slenderness/types";
import { ShearStrengthResult } from "./types";
import { calculateVn_I_Shape } from "./i-shape-shear";
import { calculateVn_HSS } from "./hss-shear";
import { calculateVn_Angle } from "./angle-shear";
import { calculateVn_MinorAxis } from "./minor-axis-shear";

// Re-export types and individual calculation functions
export type { ShearStrengthResult, InelasticAnalysisOptions } from "./types";
export { calculateVn_I_Shape } from "./i-shape-shear";
export { calculateVn_HSS } from "./hss-shear";
export { calculateVn_Angle } from "./angle-shear";
export { calculateVn_MinorAxis } from "./minor-axis-shear";

// Re-export inelastic analysis functionality
export {
  calculateReducedMaterialProperties,
  shouldApplyStiffnessReduction,
  validateInelasticAnalysisOptions,
  getInelasticAnalysisGuidance,
  applyShearModulusReduction,
  calculateInelasticAnalysisPn,
  validateMaterialRequirements,
  calculateWebSlendernessLimit,
  calculateFlangeSlendernessLimit,
  calculateRoundHSSSlendernessLimit,
  validateCrossSectionRequirements,
  calculateEffectiveMoment,
  calculateLpd_I_Shape,
  calculateLpd_RectangularSection,
  calculateAxialCompressionUnbracedLengthLimits,
  unbracedLengthLimitsApply,
  validateUnbracedLengthRequirements,
  validateAxialForceRequirements,
} from "./inelastic-analysis";
export type { ReducedMaterialProperties, DuctilityValidationResult, UnbracedLengthValidation } from "./inelastic-analysis";

/**
 * Router function to calculate the nominal shear strength (Vn) for a given shape.
 * @param {Section} section The cross-section properties.
 * @param {Material | number} material The material properties or yield strength in ksi.
 * @returns {ShearStrengthResult} The nominal shear strength and governing limit state.
 * @throws {Error} if the section type is not supported or Fy is not positive.
 * @source AISC 360-22, Chapter G.
 */
export function calculateNominalShearStrength(
  section: Section,
  material: Material | number
): ShearStrengthResult {
  // Handle both Material object and direct Fy number
  const Fy = typeof material === 'number' ? material : material.Fy;

  if (Fy <= 0) {
    throw new Error(ERROR_MESSAGES.YIELD_STRENGTH_POSITIVE);
  }

  switch (section.Type) {
    case SECTION_TYPE_STRINGS.W:
    case SECTION_TYPE_STRINGS.I:
    case SECTION_TYPE_STRINGS.S:
    case SECTION_TYPE_STRINGS.M:
    case SECTION_TYPE_STRINGS.C:
    case SECTION_TYPE_STRINGS.MC:
      return calculateVn_I_Shape(section, Fy);
    case SECTION_TYPE_STRINGS.HSS_RECT:
      return calculateVn_HSS(section, Fy);
    case SECTION_TYPE_STRINGS.L:
    case SECTION_TYPE_STRINGS.DOUBLE_L:
      return calculateVn_Angle(section, Fy);
    default:
      throw new Error(
        ERROR_MESSAGES.UNSUPPORTED_SECTION_TYPE(section.Type, "Shear strength")
      );
  }
} 