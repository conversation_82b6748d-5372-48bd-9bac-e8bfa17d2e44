/**
 * @file Shared types and interfaces for shear strength calculations.
 */

import { ShearLimitState, DesignMethod } from "../constants";

/**
 * Optional metadata for shear strength calculations providing detailed calculation values.
 */
export interface ShearStrengthMetadata {
  /** Clear distance between flanges, in. */
  h?: number;
  /** Web slenderness ratio (h/tw) */
  h_over_tw?: number;
  /** Web plate shear buckling coefficient */
  kv?: number;
  /** Web shear strength coefficient */
  Cv1?: number;
  /** Whether section qualifies for enhanced G2.1(a) resistance factors */
  qualifiesForG2_1a?: boolean;
  /** Effective shear area, in² */
  effectiveArea?: number;
  /** Connection type for angles */
  connectionType?: string;
  /** Whether eccentricity effects are considered */
  considerEccentricity?: boolean;
  /** Normalized slenderness parameter */
  lambda_v?: number;
  /** Section type for reference */
  sectionType?: string;
  /** Whether torsional effects are considered */
  considerTorsion?: boolean;
  /** Stiffness reduction factors for inelastic analysis */
  stiffnessReduction?: {
    baseReductionFactor: number; // 0.80 base factor
    tauB: number; // Additional reduction parameter τb
    totalReduction: number; // Combined reduction factor
    appliedToModulus: boolean; // Whether applied to E and G
  };
  /** Ductility validation results for inelastic analysis */
  ductilityValidation?: DuctilityValidationResult;
}

/**
 * Parameters for inelastic analysis stiffness adjustments.
 * Used when design includes second-order analysis with direct modeling of imperfections.
 * 
 * @source AISC 360-22, Appendix 1.3, "Design by Inelastic Analysis"
 */
export interface InelasticAnalysisOptions {
  /** Whether to apply stiffness reduction for inelastic analysis */
  applyStiffnessReduction: boolean;
  /** Required axial compressive strength for τb calculation, kips */
  Pr?: number;
  /** Nominal axial compressive strength for τb calculation, kips */
  Pn?: number;
  /** Design method (LRFD or ASD) */
  designMethod?: DesignMethod;
  /** Whether to use cross-section strength (FyAg) instead of column buckling */
  useCrossSectionStrength?: boolean;
  /** Custom stiffness reduction factor (overrides calculated 0.8τb) */
  customReductionFactor?: number;
  /** Whether to validate ductility requirements for plastic hinging */
  validateDuctilityRequirements?: boolean;
  /** Whether member is subject to plastic hinging */
  subjectToPlasticHinging?: boolean;
  /** Required axial strength in compression using LRFD, kips (for λpd calculations) */
  Pu?: number;
  /** Axial yield strength, kips (Py = FyAg) */
  Py?: number;
  /** Resistance factor for compression (φc = 0.90) */
  phiC?: number;
}

/**
 * Interface for ductility requirement validation results.
 * 
 * @source AISC 360-22, Appendix 1.3, Section 2
 */
export interface DuctilityValidationResult {
  /** Whether material requirements are satisfied (Fy ≤ 65 ksi) */
  materialCompliant: boolean;
  /** Whether cross-section is doubly symmetric */
  doublySymmetric: boolean;
  /** Web slenderness limit for plastic hinging (λpd) */
  webSlendernessLimit?: number;
  /** Actual web slenderness ratio */
  actualWebSlenderness?: number;
  /** Whether web slenderness requirements are satisfied */
  webSlendernessCompliant?: boolean;
  /** Flange slenderness limit for plastic hinging (λpd) */
  flangeSlendernessLimit?: number;
  /** Actual flange slenderness ratio */
  actualFlangeSlenderness?: number;
  /** Whether flange slenderness requirements are satisfied */
  flangeSlendernessCompliant?: boolean;
  /** Overall compliance with ductility requirements */
  overallCompliant: boolean;
  /** Detailed compliance messages */
  complianceMessages: string[];
  /** Warnings for potential issues */
  warnings: string[];
}

/**
 * Result interface for shear strength calculations.
 */
export interface ShearStrengthResult {
  /** Nominal shear strength in kips */
  Vn: number;
  /** Governing limit state for the calculation */
  governingLimitState: ShearLimitState;
  /** Optional detailed calculation metadata */
  metadata?: ShearStrengthMetadata;
}
