/**
 * @file Angle and Tee shear strength calculations according to AISC 360-22, Section G3.
 *
 * This module implements shear strength provisions for single angles and tees,
 * with consideration for connection eccentricity and member geometry.
 *
 * @source AISC 360-22, Chapter G - Design of Members for Shear
 */

import { ShearLimitState, ERROR_MESSAGES } from "../constants";
import { Section } from "../slenderness/types";
import { <PERSON>r<PERSON>trengthR<PERSON>ult } from "./types";

/**
 * AISC 360-22 Section G3 - Single Angles and Tees
 *
 * @source AISC 360-22, Section G3
 */
const G3_CONSTANTS = {
  /** Shear yielding coefficient - AISC Eq. G3-1 */
  SHEAR_COEFFICIENT: 0.6,
  /** Factor for single angles without end connections - conservative */
  CONSERVATIVE_FACTOR: 1.0,
  /** Reduction factor for eccentric bolted connections */
  ECCENTRIC_BOLTED_FACTOR: 0.9,
} as const;

/**
 * Determines the effective shear area for angle sections.
 *
 * For single angles and tees, the shear strength is typically governed by
 * yielding, and the effective area depends on the connection configuration.
 *
 * @param section - Cross-section properties
 * @param connectionType - Type of connection ('welded' | 'bolted' | 'conservative')
 * @returns Effective shear area in in²
 *
 * @source AISC 360-22, Section G3
 */
function getEffectiveShearArea(
  section: Section,
  connectionType: "welded" | "bolted" | "conservative" = "conservative"
): number {
  const { A, d, tw } = section;

  if (typeof A !== "number") {
    throw new Error(
      "Section area (A) is required for angle shear calculations"
    );
  }

  switch (connectionType) {
    case "welded":
      // For welded connections, full area can be effective
      return A;

    case "bolted":
      // For bolted connections, may need to consider connection eccentricity
      // Use conservative approach unless more detailed analysis is performed
      return A;

    case "conservative":
    default:
      // Conservative approach - use full gross area
      // This is appropriate for preliminary design
      return A;
  }
}

/**
 * Calculates the nominal shear strength (Vn) for single angle and tee sections.
 *
 * This function implements AISC 360-22 Section G3 provisions for single angles and tees.
 * The calculation considers the unique behavior of these sections under shear loading,
 * including potential connection eccentricity effects.
 *
 * @param section - The cross-section properties
 * @param Fy - The specified minimum yield stress in ksi
 * @param options - Optional parameters for connection type and analysis
 * @param options.connectionType - Type of connection affecting shear transfer
 * @param options.considerEccentricity - Whether to consider connection eccentricity
 * @returns The nominal shear strength and governing limit state
 *
 * @throws {Error} If required section properties are missing or invalid
 *
 * @source AISC 360-22, Section G3 - "Single Angles and Tees"
 *
 * @example
 * ```typescript
 * const angle: Section = { A: 4.75, d: 6.0, tw: 0.5, Type: "L", ... };
 * const result = calculateVn_Angle(angle, 50);
 * console.log(`Vn = ${result.Vn} kips, ${result.governingLimitState}`);
 * ```
 */
export function calculateVn_Angle(
  section: Section,
  Fy: number,
  options: {
    connectionType?: "welded" | "bolted" | "conservative";
    considerEccentricity?: boolean;
  } = {}
): ShearStrengthResult {
  const { A, Type } = section;

  // Validate required properties
  if (typeof A !== "number" || A <= 0) {
    throw new Error(ERROR_MESSAGES.ANGLE_SHEAR_PROPS_REQUIRED);
  }

  if (typeof Fy !== "number" || Fy <= 0) {
    throw new Error("Yield strength (Fy) must be a positive number");
  }

  const { connectionType = "conservative", considerEccentricity = false } =
    options;

  // Determine effective shear area
  const Av = getEffectiveShearArea(section, connectionType);

  // For single angles and tees, shear yielding typically governs
  // Shear buckling is generally not a concern due to the open cross-section geometry
  let governingLimitState = ShearLimitState.SHEAR_YIELDING;
  let reductionFactor: number = G3_CONSTANTS.CONSERVATIVE_FACTOR;

  // Consider connection eccentricity effects if specified
  if (considerEccentricity) {
    // For bolted connections with eccentricity, a reduction might be appropriate
    // This would require more detailed analysis per AISC specifications
    if (connectionType === "bolted") {
      // Conservative reduction for eccentric connections
      reductionFactor = G3_CONSTANTS.ECCENTRIC_BOLTED_FACTOR;
    }
  }

  // Calculate nominal shear strength - AISC Eq. G3-1
  const Vn = G3_CONSTANTS.SHEAR_COEFFICIENT * Fy * Av * reductionFactor;

  // Handle double angles (treat as two single angles)
  const finalVn = Type === "2L" ? 2 * Vn : Vn;

  return {
    Vn: finalVn,
    governingLimitState,
    metadata: {
      h_over_tw: section.d && section.tw ? section.d / section.tw : undefined,
      Cv1: reductionFactor,
      effectiveArea: Type === "2L" ? 2 * Av : Av,
      connectionType,
      considerEccentricity,
    },
  };
}

/**
 * Simplified calculation for preliminary design of angle sections.
 *
 * This function provides a quick estimate of shear strength for angle sections
 * using conservative assumptions suitable for preliminary design.
 *
 * @param section - Cross-section properties
 * @param Fy - Specified minimum yield stress in ksi
 * @returns Simplified shear strength calculation
 *
 * @source AISC 360-22, Section G3 (simplified approach)
 */
export function calculateVn_Angle_Simplified(
  section: Section,
  Fy: number
): ShearStrengthResult {
  return calculateVn_Angle(section, Fy, {
    connectionType: "conservative",
    considerEccentricity: false,
  });
}
