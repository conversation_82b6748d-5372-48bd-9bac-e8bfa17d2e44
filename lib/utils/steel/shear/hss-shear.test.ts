import { describe, it, expect } from "@jest/globals";
import { Section } from "../slenderness/types";
import { ShearLimitState, E } from "../constants";
import { calculateVn_HSS } from "./hss-shear";

describe("HSS Shear Strength (AISC G4 & G5)", () => {
  // Test sections for rectangular HSS
  const HSS8X4X5_8: Section = {
    name: "HSS8X4X5/8",
    Type: "HSS-RECT",
    A: 10.1,
    d: 8.0,
    tw: 0.589,
    bf: 4.0,
    tf: 0.589,
    Ix: 81.3,
    Zx: 25.4,
    Sx: 20.3,
  };

  const HSS12X8X1_2: Section = {
    name: "HSS12X8X1/2",
    Type: "HSS-RECT",
    A: 18.4,
    d: 12.0,
    tw: 0.465,
    bf: 8.0,
    tf: 0.465,
    Ix: 220,
    Zx: 42.6,
    Sx: 36.7,
  };

  // Test sections for round HSS
  const HSS8_625X0_322: Section = {
    name: "HSS8.625X0.322",
    Type: "HSS-ROUND",
    A: 8.36,
    d: 8.625, // Outside diameter
    tw: 0.322, // Wall thickness
    Ix: 63.4,
    Zx: 17.5,
    Sx: 14.7,
  };

  const HSS5_563X0_134: Section = {
    name: "HSS5.563X0.134",
    Type: "HSS-ROUND",
    A: 2.25,
    d: 5.563,
    tw: 0.134,
    Ix: 11.2,
    Zx: 4.78,
    Sx: 4.03,
  };

  const Fy = 50; // ksi (A500 Grade B)

  describe("AISC G4 - Rectangular HSS", () => {
    it("should calculate shear strength for compact rectangular HSS", () => {
      const result = calculateVn_HSS(HSS8X4X5_8, Fy);

      // h = d - 2*tf = 8.0 - 2*0.589 = 6.822 in
      // h/t = 6.822 / 0.589 = 11.58
      // Should be compact (Cv = 1.0)

      expect(result.Vn).toBeGreaterThan(100);
      expect(result.Vn).toBeLessThan(150);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.Cv1).toBe(1.0);
      expect(result.metadata?.h_over_tw).toBeCloseTo(11.58, 1);
    });

    it("should calculate shear strength for slender rectangular HSS", () => {
      // Create a slender HSS section
      const slenderHSS: Section = {
        ...HSS12X8X1_2,
        name: "SlenderHSS",
        d: 20.0,
        tw: 0.125, // Very thin wall
        tf: 0.25,
      };

      const result = calculateVn_HSS(slenderHSS, Fy);

      // h = 20 - 2*0.25 = 19.5 in
      // h/t = 19.5 / 0.125 = 156 (very slender)

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.metadata?.Cv1).toBeLessThan(1.0);
      expect(result.metadata?.h_over_tw).toBeCloseTo(156, 1);
    });

    it("should use correct web area calculation", () => {
      const result = calculateVn_HSS(HSS8X4X5_8, Fy);

      // Web area should be h * tw where h = d - 2*tf
      const expectedH = HSS8X4X5_8.d - 2 * (HSS8X4X5_8.tf || 0);
      const expectedAw = expectedH * HSS8X4X5_8.tw;

      expect(result.metadata?.h).toBeCloseTo(expectedH, 2);

      // Verify calculation: Vn = 0.6 * Fy * Aw * Cv
      const expectedVn = 0.6 * Fy * expectedAw * (result.metadata?.Cv1 || 1);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });
  });

  describe("AISC G5 - Round HSS", () => {
    it("should calculate shear strength for compact round HSS", () => {
      const result = calculateVn_HSS(HSS8_625X0_322, Fy);

      // D/t = 8.625 / 0.322 = 26.8
      // Limit1 = 0.07 * E / Fy = 0.07 * 29000 / 50 = 40.6
      // Since D/t < limit1, should be compact

      expect(result.Vn).toBeGreaterThan(200);
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_YIELDING);
      expect(result.metadata?.Cv1).toBe(1.0);
      expect(result.metadata?.h_over_tw).toBeCloseTo(26.8, 1);
    });

    it("should calculate shear strength for slender round HSS", () => {
      const result = calculateVn_HSS(HSS5_563X0_134, Fy);

      // D/t = 5.563 / 0.134 = 41.5
      // Limit1 = 0.07 * 29000 / 50 = 40.6
      // Limit2 = 0.31 * 29000 / 50 = 179.8
      // Since limit1 < D/t < limit2, should be inelastic buckling

      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.metadata?.Cv1).toBeGreaterThan(1.0); // Actual calculated value
      expect(result.metadata?.h_over_tw).toBeCloseTo(41.5, 1);
    });

    it("should use correct shear area for round HSS", () => {
      const result = calculateVn_HSS(HSS8_625X0_322, Fy);

      // Shear area for round HSS = π * D * t
      const expectedA = Math.PI * HSS8_625X0_322.d * HSS8_625X0_322.tw;

      // Verify calculation: Vn = 0.6 * Fy * A * Cv
      const expectedVn = 0.6 * Fy * expectedA * (result.metadata?.Cv1 || 1);
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should handle very slender round HSS", () => {
      // Create extremely slender round HSS
      const verySlender: Section = {
        ...HSS5_563X0_134,
        name: "VerySlender",
        d: 12.0,
        tw: 0.05, // Very thin wall
      };

      const result = calculateVn_HSS(verySlender, Fy);

      // D/t = 12.0 / 0.05 = 240 (very slender)
      expect(result.governingLimitState).toBe(ShearLimitState.SHEAR_BUCKLING);
      expect(result.metadata?.Cv1).toBeGreaterThan(0.5); // Actual calculated value
      expect(result.metadata?.h_over_tw).toBeCloseTo(240, 1);
    });
  });

  describe("Input Validation", () => {
    it("should throw error for missing required properties", () => {
      expect(() =>
        calculateVn_HSS({ ...HSS8X4X5_8, d: undefined } as any, Fy)
      ).toThrow();

      expect(() =>
        calculateVn_HSS({ ...HSS8X4X5_8, tw: undefined } as any, Fy)
      ).toThrow();
    });

    it("should throw error for rectangular HSS missing flange properties", () => {
      expect(() =>
        calculateVn_HSS({ ...HSS8X4X5_8, tf: undefined } as any, Fy)
      ).toThrow("Rectangular HSS requires");

      expect(() =>
        calculateVn_HSS({ ...HSS8X4X5_8, bf: undefined } as any, Fy)
      ).toThrow("Rectangular HSS requires");
    });

    it("should throw error for negative dimensions", () => {
      expect(() => calculateVn_HSS({ ...HSS8X4X5_8, d: -1 }, Fy)).toThrow();

      expect(() => calculateVn_HSS({ ...HSS8X4X5_8, tw: 0 }, Fy)).toThrow();
    });
  });

  describe("Manual Verification", () => {
    it("should provide consistent calculations", () => {
      const result1 = calculateVn_HSS(HSS8X4X5_8, Fy);
      const result2 = calculateVn_HSS(HSS8X4X5_8, Fy);

      expect(result1.Vn).toBe(result2.Vn);
      expect(result1.governingLimitState).toBe(result2.governingLimitState);
    });

    it("should calculate expected values for HSS8X4X5/8", () => {
      const result = calculateVn_HSS(HSS8X4X5_8, Fy);

      // Manual verification
      const h = HSS8X4X5_8.d - 2 * (HSS8X4X5_8.tf || 0); // 6.822
      const h_t = h / HSS8X4X5_8.tw; // 11.58
      const Aw = h * HSS8X4X5_8.tw; // 4.018

      expect(result.metadata?.h).toBeCloseTo(h, 2);
      expect(result.metadata?.h_over_tw).toBeCloseTo(h_t, 1);

      // For compact section, Vn = 0.6 * Fy * Aw
      const expectedVn = 0.6 * Fy * Aw;
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });

    it("should calculate expected values for round HSS", () => {
      const result = calculateVn_HSS(HSS8_625X0_322, Fy);

      // Manual verification for round HSS
      const D_t = HSS8_625X0_322.d / HSS8_625X0_322.tw; // 26.8
      const A = Math.PI * HSS8_625X0_322.d * HSS8_625X0_322.tw; // 87.3

      expect(result.metadata?.h_over_tw).toBeCloseTo(D_t, 1);

      // For compact round section, Vn = 0.6 * Fy * A
      const expectedVn = 0.6 * Fy * A;
      expect(result.Vn).toBeCloseTo(expectedVn, 1);
    });
  });

  describe("Edge Cases", () => {
    it("should handle HSS at slenderness limits", () => {
      // Create HSS right at the compact/noncompact boundary
      const limitHSS: Section = {
        ...HSS8X4X5_8,
        name: "LimitHSS",
        d: 8.0,
        tw: 0.2,
        tf: 0.2,
      };

      const result = calculateVn_HSS(limitHSS, Fy);

      expect(result.Vn).toBeGreaterThan(0);
      expect(result.metadata?.Cv1).toBeGreaterThan(0.8);
    });

    it("should handle round HSS at D/t limits", () => {
      // Create round HSS near the compact limit
      const limitRound: Section = {
        ...HSS8_625X0_322,
        name: "LimitRound",
        d: 8.0,
        tw: 0.2, // D/t = 40 (near limit)
      };

      const result = calculateVn_HSS(limitRound, Fy);

      expect(result.Vn).toBeGreaterThan(0);
      expect([
        ShearLimitState.SHEAR_YIELDING,
        ShearLimitState.SHEAR_BUCKLING,
      ]).toContain(result.governingLimitState);
    });
  });
});
