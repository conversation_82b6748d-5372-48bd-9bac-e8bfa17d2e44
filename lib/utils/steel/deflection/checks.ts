/**
 * @file Main deflection checking functions.
 */

import { ERROR_MESSAGES } from "../constants";
import { Section } from "../slenderness/types";
import {
  DeflectionInputs,
  DeflectionCheckResult,
  SingleDeflectionResult,
} from "./types";
import {
  DEFAULT_DEFLECTION_LIMITS,
  DEFLECTION_RESULT_DIRECTIONS,
} from "./constants";
import { calculateSingleDeflectionCheck } from "./helpers";

/**
 * Performs deflection checks for both upward and downward deflections.
 *
 * This function evaluates the serviceability of a beam by checking if the
 * maximum deflections in both directions are within acceptable limits.
 * The function performs separate checks for upward and downward deflections
 * and determines the overall adequacy and controlling direction.
 *
 * @param inputs - Deflection check input parameters
 * @returns Complete deflection check results
 *
 * @throws {Error} If inputs are invalid or missing required parameters
 * @source Common engineering practice for serviceability checks
 *
 * @example
 * ```typescript
 * const inputs: DeflectionInputs = {
 *   section: myWSection,
 *   material: { Fy: 50, Fu: 65 },
 *   beamLength: 360, // 30 feet
 *   maxUpwardDeflection: -0.5, // 0.5" upward
 *   maxDownwardDeflection: 1.2, // 1.2" downward
 *   deflectionLimit: 240 // L/240
 * };
 *
 * const result = checkDeflection(inputs);
 * console.log(`Overall adequate: ${result.overallAdequate}`);
 * console.log(`Controlling direction: ${result.controllingDirection}`);
 * ```
 */
export function checkDeflection(
  inputs: DeflectionInputs
): DeflectionCheckResult {
  // Validate inputs
  if (!inputs) {
    throw new Error(ERROR_MESSAGES.DEFLECTION_INPUTS_REQUIRED);
  }

  const {
    section,
    material,
    beamLength,
    maxUpwardDeflection,
    maxDownwardDeflection,
    deflectionLimit = DEFAULT_DEFLECTION_LIMITS.TOTAL,
  } = inputs;

  // Validate required parameters
  if (!section) {
    throw new Error(ERROR_MESSAGES.SECTION_REQUIRED_DEFLECTION);
  }
  if (!material) {
    throw new Error(ERROR_MESSAGES.MATERIAL_REQUIRED_DEFLECTION);
  }
  if (typeof beamLength !== "number" || beamLength <= 0) {
    throw new Error(ERROR_MESSAGES.BEAM_LENGTH_POSITIVE);
  }
  if (typeof maxUpwardDeflection !== "number") {
    throw new Error(ERROR_MESSAGES.MAX_UPWARD_DEFLECTION_NUMBER);
  }
  if (typeof maxDownwardDeflection !== "number") {
    throw new Error(ERROR_MESSAGES.MAX_DOWNWARD_DEFLECTION_NUMBER);
  }
  if (typeof deflectionLimit !== "number" || deflectionLimit <= 0) {
    throw new Error(ERROR_MESSAGES.DEFLECTION_LIMIT_POSITIVE);
  }

  // Perform deflection checks for both directions
  const upwardCheck = calculateSingleDeflectionCheck(
    maxUpwardDeflection,
    beamLength,
    deflectionLimit
  );

  const downwardCheck = calculateSingleDeflectionCheck(
    maxDownwardDeflection,
    beamLength,
    deflectionLimit
  );

  // Determine overall adequacy and controlling direction
  const overallAdequate = upwardCheck.adequate && downwardCheck.adequate;

  let controllingDirection: "upward" | "downward" | "both";
  if (!upwardCheck.adequate && !downwardCheck.adequate) {
    controllingDirection = DEFLECTION_RESULT_DIRECTIONS.BOTH;
  } else if (!upwardCheck.adequate) {
    controllingDirection = DEFLECTION_RESULT_DIRECTIONS.UPWARD;
  } else if (!downwardCheck.adequate) {
    controllingDirection = DEFLECTION_RESULT_DIRECTIONS.DOWNWARD;
  } else {
    // Both are adequate, determine which has higher ratio
    controllingDirection =
      upwardCheck.deflectionRatio >= downwardCheck.deflectionRatio
        ? DEFLECTION_RESULT_DIRECTIONS.UPWARD
        : DEFLECTION_RESULT_DIRECTIONS.DOWNWARD;
  }

  return {
    upward: upwardCheck,
    downward: downwardCheck,
    overallAdequate,
    controllingDirection,
    deflectionLimit,
    beamLength,
  };
}

/**
 * Simplified deflection check function for quick analysis.
 *
 * This function provides a streamlined interface for deflection checks
 * when section and material properties are not needed or available.
 * It focuses purely on the geometric deflection check.
 *
 * @param beamLength - Beam length in inches
 * @param maxUpwardDeflection - Maximum upward deflection in inches
 * @param maxDownwardDeflection - Maximum downward deflection in inches
 * @param deflectionLimit - L/deflectionLimit (defaults to 240)
 * @returns Complete deflection check results
 *
 * @example
 * ```typescript
 * // Quick check for a 30-foot beam with 0.5" up and 1.2" down deflections
 * const result = performSimpleDeflectionCheck(360, -0.5, 1.2, 240);
 *
 * if (result.overallAdequate) {
 *   console.log('Deflections are adequate');
 * } else {
 *   console.log(`Deflection check fails in ${result.controllingDirection} direction`);
 * }
 * ```
 */
export function performSimpleDeflectionCheck(
  beamLength: number,
  maxUpwardDeflection: number,
  maxDownwardDeflection: number,
  deflectionLimit: number = DEFAULT_DEFLECTION_LIMITS.TOTAL
): DeflectionCheckResult {
  // Create minimal inputs for the main function
  const inputs: DeflectionInputs = {
    section: { name: "GENERIC", Type: "W" } as Section, // Minimal section (not used in deflection calc)
    material: { Fy: 50, Fu: 65 }, // Minimal material (not used in deflection calc)
    beamLength,
    maxUpwardDeflection,
    maxDownwardDeflection,
    deflectionLimit,
  };

  return checkDeflection(inputs);
}
