/**
 * @file Main entry point for steel deflection calculations.
 * Provides access to all deflection checking functions and utilities.
 */

// Re-export types and interfaces
export type {
  DeflectionInputs,
  SingleDeflectionResult,
  DeflectionCheckResult,
  BeamApplication,
} from "./types";

// Re-export constants
export {
  DEFAULT_DEFLECTION_LIMITS,
  DEFLECTION_RESULT_DIRECTIONS,
  BEAM_APPLICATIONS,
  ADEQUACY_THRESHOLD,
} from "./constants";

// Re-export main deflection check functions
export { checkDeflection, performSimpleDeflectionCheck } from "./checks";

// Re-export helper functions
export {
  calculateSingleDeflectionCheck,
  getRecommendedDeflectionLimit,
} from "./helpers";
