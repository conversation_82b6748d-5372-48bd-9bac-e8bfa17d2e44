/**
 * @file Constants for deflection calculations.
 */

import {
  DEFLECTION_CONSTANTS,
  DEFLECTION_DIRECTIONS,
  DEFLECTION_APPLICATIONS,
} from "../constants";

/**
 * Default deflection limits (L/limit ratios) for different applications.
 *
 * These limits are based on common engineering practice and building codes
 * such as IBC and AISC Design Guides.
 *
 * @source IBC, AISC Design Guide, and common engineering practice
 *
 * @example
 * ```typescript
 * // Use L/240 for general applications
 * const limit = DEFAULT_DEFLECTION_LIMITS.TOTAL; // 240
 *
 * // Use L/360 for floors with non-structural elements
 * const floorLimit = DEFAULT_DEFLECTION_LIMITS.FLOORS; // 360
 * ```
 */
export const DEFAULT_DEFLECTION_LIMITS = {
  /** Total load deflection for general applications (L/240) */
  TOTAL: DEFLECTION_CONSTANTS.DEFLECTION_LIMITS.GENERAL,
  /** Live load deflection limit (L/360) */
  LIVE: DEFLECTION_CONSTANTS.DEFLECTION_LIMITS.LIVE_LOAD,
  /** Live load deflection for floors (L/360) */
  FLOORS: DEFLECTION_CONSTANTS.DEFLECTION_LIMITS.FLOOR,
  /** Total load deflection for roofs (L/180) */
  ROOFS: DEFLECTION_CONSTANTS.DEFLECTION_LIMITS.ROOF,
  /** Cantilever beam deflection limit (L/180) */
  CANTILEVER: DEFLECTION_CONSTANTS.DEFLECTION_LIMITS.CANTILEVER,
} as const;

/**
 * Re-export deflection directions for use in results.
 */
export const DEFLECTION_RESULT_DIRECTIONS = {
  UPWARD: DEFLECTION_DIRECTIONS.UPWARD,
  DOWNWARD: DEFLECTION_DIRECTIONS.DOWNWARD,
  BOTH: DEFLECTION_DIRECTIONS.BOTH,
} as const;

/**
 * Re-export application types for convenience.
 */
export const BEAM_APPLICATIONS = {
  FLOOR: DEFLECTION_APPLICATIONS.FLOOR,
  ROOF: DEFLECTION_APPLICATIONS.ROOF,
  CANTILEVER: DEFLECTION_APPLICATIONS.CANTILEVER,
  GENERAL: DEFLECTION_APPLICATIONS.GENERAL,
} as const;

/**
 * Adequacy threshold for deflection checks.
 */
export const ADEQUACY_THRESHOLD = DEFLECTION_CONSTANTS.ADEQUACY_THRESHOLD;
