/**
 * @file Types and interfaces for deflection calculations.
 */

import { Section } from "../slenderness/types";
import { Material } from "../constants";

/**
 * Interface for deflection check inputs.
 * 
 * @example
 * ```typescript
 * const inputs: DeflectionInputs = {
 *   section: mySection,
 *   material: { Fy: 50, Fu: 65 },
 *   beamLength: 360, // 30 feet in inches
 *   maxUpwardDeflection: -0.5, // 0.5" upward
 *   maxDownwardDeflection: 1.2, // 1.2" downward
 *   deflectionLimit: 240 // L/240
 * };
 * ```
 */
export interface DeflectionInputs {
  section: Section;              // Cross-section properties
  material: Material;            // Material properties (only E is used)
  beamLength: number;           // Beam length in inches
  maxUpwardDeflection: number;  // Maximum upward deflection in inches (negative value)
  maxDownwardDeflection: number; // Maximum downward deflection in inches (positive value)
  deflectionLimit?: number;     // L/deflectionLimit (defaults to 240)
}

/**
 * Interface for individual deflection check result.
 * 
 * @example
 * ```typescript
 * const result: SingleDeflectionResult = {
 *   actualDeflection: 1.2,
 *   allowableDeflection: 1.5,
 *   deflectionRatio: 0.8,
 *   lOverDelta: 300,
 *   adequate: true
 * };
 * ```
 */
export interface SingleDeflectionResult {
  actualDeflection: number;     // Actual deflection value in inches
  allowableDeflection: number;  // Allowable deflection in inches
  deflectionRatio: number;      // Actual/allowable ratio
  lOverDelta: number;          // L/Δ ratio (beam length / deflection)
  adequate: boolean;           // True if deflection is within limits
}

/**
 * Interface for complete deflection check results.
 * 
 * @example
 * ```typescript
 * const checkResult: DeflectionCheckResult = {
 *   upward: { actualDeflection: -0.5, allowableDeflection: 1.5, deflectionRatio: 0.33, lOverDelta: 720, adequate: true },
 *   downward: { actualDeflection: 1.2, allowableDeflection: 1.5, deflectionRatio: 0.8, lOverDelta: 300, adequate: true },
 *   overallAdequate: true,
 *   controllingDirection: "downward",
 *   deflectionLimit: 240,
 *   beamLength: 360
 * };
 * ```
 */
export interface DeflectionCheckResult {
  upward: SingleDeflectionResult;      // Upward deflection check
  downward: SingleDeflectionResult;    // Downward deflection check
  overallAdequate: boolean;           // True if both directions are adequate
  controllingDirection: "upward" | "downward" | "both"; // Which direction controls
  deflectionLimit: number;            // Applied deflection limit (L/limit)
  beamLength: number;                 // Beam length used in calculation
}

/**
 * Type for beam application categories.
 */
export type BeamApplication = "floor" | "roof" | "cantilever" | "general"; 