/**
 * @file Helper functions for deflection calculations.
 */

import { ERROR_MESSAGES } from "../constants";
import { SingleDeflectionResult, BeamApplication } from "./types";
import { DEFAULT_DEFLECTION_LIMITS, ADEQUACY_THRESHOLD, BEAM_APPLICATIONS } from "./constants";

/**
 * Calculates a single deflection check result.
 * 
 * This function performs the deflection adequacy check for a single direction
 * by comparing the actual deflection against the allowable deflection based
 * on the specified limit ratio.
 * 
 * @param actualDeflection - Actual deflection in inches (signed)
 * @param beamLength - Beam length in inches
 * @param deflectionLimit - L/deflectionLimit (e.g., 240 for L/240)
 * @returns Single deflection check result
 * 
 * @throws {Error} If beam length or deflection limit are not positive
 * 
 * @example
 * ```typescript
 * // Check a 1.2" downward deflection on a 30-foot beam with L/240 limit
 * const result = calculateSingleDeflectionCheck(1.2, 360, 240);
 * console.log(`Adequate: ${result.adequate}, Ratio: ${result.deflectionRatio.toFixed(2)}`);
 * ```
 */
export function calculateSingleDeflectionCheck(
  actualDeflection: number,
  beamLength: number,
  deflectionLimit: number
): SingleDeflectionResult {
  // Validate inputs
  if (beamLength <= 0) {
    throw new Error(ERROR_MESSAGES.BEAM_LENGTH_POSITIVE_GENERAL);
  }
  if (deflectionLimit <= 0) {
    throw new Error(ERROR_MESSAGES.DEFLECTION_LIMIT_POSITIVE_GENERAL);
  }

  const allowableDeflection = beamLength / deflectionLimit;
  const absActualDeflection = Math.abs(actualDeflection);
  const deflectionRatio = absActualDeflection / allowableDeflection;
  const lOverDelta = absActualDeflection > 0 ? beamLength / absActualDeflection : Infinity;
  const adequate = deflectionRatio <= ADEQUACY_THRESHOLD;

  return {
    actualDeflection,
    allowableDeflection,
    deflectionRatio,
    lOverDelta,
    adequate,
  };
}

/**
 * Get recommended deflection limits based on beam application.
 * 
 * This function returns the appropriate deflection limit denominator
 * for different structural applications based on common engineering practice.
 * 
 * @param application - Type of beam application
 * @returns Recommended deflection limit denominator
 * 
 * @source IBC, AISC Design Guide, and common engineering practice
 * 
 * @example
 * ```typescript
 * // Get limit for floor beam
 * const floorLimit = getRecommendedDeflectionLimit('floor'); // Returns 360
 * 
 * // Get limit for roof beam
 * const roofLimit = getRecommendedDeflectionLimit('roof'); // Returns 180
 * 
 * // Get limit for cantilever
 * const cantileverLimit = getRecommendedDeflectionLimit('cantilever'); // Returns 180
 * ```
 */
export function getRecommendedDeflectionLimit(application: BeamApplication): number {
  switch (application) {
    case BEAM_APPLICATIONS.FLOOR:
      return DEFAULT_DEFLECTION_LIMITS.FLOORS;
    case BEAM_APPLICATIONS.ROOF:
      return DEFAULT_DEFLECTION_LIMITS.ROOFS;
    case BEAM_APPLICATIONS.CANTILEVER:
      return DEFAULT_DEFLECTION_LIMITS.CANTILEVER;
    case BEAM_APPLICATIONS.GENERAL:
    default:
      return DEFAULT_DEFLECTION_LIMITS.TOTAL;
  }
} 