import { describe, it, expect } from "@jest/globals";
import { DesignMethod, LimitStateType, SectionType } from "./constants";
import {
  getResistanceFactor,
  calculateAvailableStrength,
  calculateDCR,
  performDesignCheck,
} from "./analysis-utils";

describe("Steel Analysis Utilities", () => {
  describe("getResistanceFactor", () => {
    describe("Standard Cases - LRFD", () => {
      it("should return 0.90 for flexure with W-shape and LRFD", () => {
        const factor = getResistanceFactor(
          LimitStateType.FLEXURE,
          SectionType.W,
          DesignMethod.LRFD
        );
        expect(factor).toBe(0.9);
      });

      it("should return 1.00 for shear yielding with W-shape and LRFD", () => {
        const factor = getResistanceFactor(
          LimitStateType.SHEAR_YIELDING,
          SectionType.W,
          DesignMethod.LRFD
        );
        expect(factor).toBe(1.0);
      });

      it("should return 0.90 for shear buckling with W-shape and LRFD", () => {
        const factor = getResistanceFactor(
          LimitStateType.SHEAR_BUCKLING,
          SectionType.W,
          DesignMethod.LRFD
        );
        expect(factor).toBe(0.9);
      });

      it("should return 0.90 for flexure with HSS-RECT and LRFD", () => {
        const factor = getResistanceFactor(
          LimitStateType.FLEXURE,
          SectionType.HSS_RECT,
          DesignMethod.LRFD
        );
        expect(factor).toBe(0.9);
      });
    });

    describe("Standard Cases - ASD", () => {
      it("should return 1.67 for flexure with W-shape and ASD", () => {
        const factor = getResistanceFactor(
          LimitStateType.FLEXURE,
          SectionType.W,
          DesignMethod.ASD
        );
        expect(factor).toBe(1.67);
      });

      it("should return 1.67 for shear yielding with W-shape and ASD", () => {
        const factor = getResistanceFactor(
          LimitStateType.SHEAR_YIELDING,
          SectionType.W,
          DesignMethod.ASD
        );
        expect(factor).toBe(1.67);
      });

      it("should return 1.67 for shear buckling with W-shape and ASD", () => {
        const factor = getResistanceFactor(
          LimitStateType.SHEAR_BUCKLING,
          SectionType.W,
          DesignMethod.ASD
        );
        expect(factor).toBe(1.67);
      });

      it("should return 1.67 for shear yielding with HSS-RECT and ASD", () => {
        const factor = getResistanceFactor(
          LimitStateType.SHEAR_YIELDING,
          SectionType.HSS_RECT,
          DesignMethod.ASD
        );
        expect(factor).toBe(1.67);
      });
    });

    describe("All Section Types", () => {
      const sectionTypes = [
        SectionType.W,
        SectionType.I,
        SectionType.S,
        SectionType.M,
        SectionType.C,
        SectionType.MC,
        SectionType.HSS_RECT,
        SectionType.HSS_ROUND,
      ];

      it("should return consistent flexural factors for all section types", () => {
        sectionTypes.forEach((sectionType) => {
          const lrfdFactor = getResistanceFactor(
            LimitStateType.FLEXURE,
            sectionType,
            DesignMethod.LRFD
          );
          const asdFactor = getResistanceFactor(
            LimitStateType.FLEXURE,
            sectionType,
            DesignMethod.ASD
          );

          expect(lrfdFactor).toBe(0.9);
          expect(asdFactor).toBe(1.67);
        });
      });

      it("should return consistent shear yielding factors for all section types", () => {
        sectionTypes.forEach((sectionType) => {
          const lrfdFactor = getResistanceFactor(
            LimitStateType.SHEAR_YIELDING,
            sectionType,
            DesignMethod.LRFD
          );
          const asdFactor = getResistanceFactor(
            LimitStateType.SHEAR_YIELDING,
            sectionType,
            DesignMethod.ASD
          );

          expect(lrfdFactor).toBe(1.0);
          expect(asdFactor).toBe(1.67);
        });
      });
    });

    describe("String Input Support", () => {
      it("should accept string inputs for all parameters", () => {
        const factor = getResistanceFactor("flexure", "W", "LRFD");
        expect(factor).toBe(0.9);
      });

      it("should accept mixed string and enum inputs", () => {
        const factor = getResistanceFactor(
          LimitStateType.FLEXURE,
          "W",
          DesignMethod.LRFD
        );
        expect(factor).toBe(0.9);
      });
    });

    describe("Invalid Inputs", () => {
      it("should throw error for invalid design method", () => {
        expect(() =>
          getResistanceFactor(
            LimitStateType.FLEXURE,
            SectionType.W,
            "INVALID" as any
          )
        ).toThrow("Invalid design method: 'INVALID'. Must be 'LRFD' or 'ASD'.");
      });

      it("should throw error for invalid limit state type", () => {
        expect(() =>
          getResistanceFactor(
            "invalid_limit_state" as any,
            SectionType.W,
            DesignMethod.LRFD
          )
        ).toThrow(
          "Invalid limit state type: 'invalid_limit_state'. Must be 'flexure', 'shear_yielding', or 'shear_buckling'."
        );
      });
    });
  });

  describe("calculateAvailableStrength", () => {
    describe("Standard Cases - LRFD", () => {
      it("should multiply nominal strength by resistance factor for LRFD", () => {
        const result = calculateAvailableStrength(100, 0.9, DesignMethod.LRFD);
        expect(result).toBe(90);
      });

      it("should handle φ = 1.0 for LRFD", () => {
        const result = calculateAvailableStrength(100, 1.0, DesignMethod.LRFD);
        expect(result).toBe(100);
      });
    });

    describe("Standard Cases - ASD", () => {
      it("should divide nominal strength by safety factor for ASD", () => {
        const result = calculateAvailableStrength(100, 1.67, DesignMethod.ASD);
        expect(result).toBeCloseTo(59.88, 2);
      });

      it("should handle Ω = 1.0 for ASD (theoretical case)", () => {
        const result = calculateAvailableStrength(100, 1.0, DesignMethod.ASD);
        expect(result).toBe(100);
      });
    });

    describe("Edge Cases", () => {
      it("should return 0 when nominal strength is 0", () => {
        expect(calculateAvailableStrength(0, 0.9, DesignMethod.LRFD)).toBe(0);
        expect(calculateAvailableStrength(0, 1.67, DesignMethod.ASD)).toBe(0);
      });

      it("should return 0 when resistance factor is 0 for LRFD", () => {
        const result = calculateAvailableStrength(100, 0, DesignMethod.LRFD);
        expect(result).toBe(0);
      });

      it("should throw error when safety factor is 0 for ASD", () => {
        expect(() =>
          calculateAvailableStrength(100, 0, DesignMethod.ASD)
        ).toThrow("Safety factor (Ω) cannot be zero for ASD calculations.");
      });
    });

    describe("String Input Support", () => {
      it("should accept string input for design method", () => {
        const result = calculateAvailableStrength(100, 0.9, "LRFD");
        expect(result).toBe(90);
      });
    });

    describe("Invalid Inputs", () => {
      it("should throw error for negative nominal strength", () => {
        expect(() =>
          calculateAvailableStrength(-10, 0.9, DesignMethod.LRFD)
        ).toThrow("Nominal strength must be non-negative.");
      });

      it("should throw error for negative factor", () => {
        expect(() =>
          calculateAvailableStrength(100, -0.9, DesignMethod.LRFD)
        ).toThrow("Resistance/safety factor must be non-negative.");
      });

      it("should throw error for invalid design method", () => {
        expect(() =>
          calculateAvailableStrength(100, 0.9, "INVALID" as any)
        ).toThrow("Invalid design method: 'INVALID'. Must be 'LRFD' or 'ASD'.");
      });
    });
  });

  describe("calculateDCR", () => {
    describe("Standard Cases", () => {
      it("should return 0.5 for demand=50, capacity=100", () => {
        const dcr = calculateDCR(50, 100);
        expect(dcr).toBe(0.5);
      });

      it("should return 1.0 for demand=100, capacity=100", () => {
        const dcr = calculateDCR(100, 100);
        expect(dcr).toBe(1.0);
      });

      it("should return 2.0 for demand=200, capacity=100", () => {
        const dcr = calculateDCR(200, 100);
        expect(dcr).toBe(2.0);
      });
    });

    describe("Edge Cases", () => {
      it("should return 0.0 for demand=0, capacity=100", () => {
        const dcr = calculateDCR(0, 100);
        expect(dcr).toBe(0.0);
      });

      it("should return Infinity for demand=100, capacity=0", () => {
        const dcr = calculateDCR(100, 0);
        expect(dcr).toBe(Infinity);
      });

      it("should return 0 for demand=0, capacity=0", () => {
        const dcr = calculateDCR(0, 0);
        expect(dcr).toBe(0);
      });
    });

    describe("Invalid Inputs", () => {
      it("should throw error for negative demand", () => {
        expect(() => calculateDCR(-10, 100)).toThrow(
          "Demand must be non-negative."
        );
      });

      it("should throw error for negative capacity", () => {
        expect(() => calculateDCR(50, -100)).toThrow(
          "Capacity must be non-negative."
        );
      });
    });
  });

  describe("performDesignCheck", () => {
    describe("Standard Cases", () => {
      it("should perform complete LRFD flexural design check", () => {
        const result = performDesignCheck(
          1000, // nominal strength
          800, // demand
          LimitStateType.FLEXURE,
          SectionType.W,
          DesignMethod.LRFD
        );

        expect(result.nominalStrength).toBe(1000);
        expect(result.resistanceFactor).toBe(0.9);
        expect(result.availableStrength).toBe(900); // 1000 * 0.90
        expect(result.demand).toBe(800);
        expect(result.dcr).toBeCloseTo(0.889, 3); // 800 / 900
        expect(result.adequate).toBe(true); // DCR < 1.0
        expect(result.designMethod).toBe(DesignMethod.LRFD);
        expect(result.limitStateType).toBe(LimitStateType.FLEXURE);
      });

      it("should perform complete ASD shear design check", () => {
        const result = performDesignCheck(
          200, // nominal strength
          150, // demand
          LimitStateType.SHEAR_YIELDING,
          SectionType.HSS_RECT,
          DesignMethod.ASD
        );

        expect(result.nominalStrength).toBe(200);
        expect(result.resistanceFactor).toBe(1.67);
        expect(result.availableStrength).toBeCloseTo(119.76, 2); // 200 / 1.67
        expect(result.demand).toBe(150);
        expect(result.dcr).toBeCloseTo(1.253, 3); // 150 / 119.76
        expect(result.adequate).toBe(false); // DCR > 1.0
        expect(result.designMethod).toBe(DesignMethod.ASD);
        expect(result.limitStateType).toBe(LimitStateType.SHEAR_YIELDING);
      });

      it("should identify adequate design when DCR = 1.0", () => {
        const result = performDesignCheck(
          100, // nominal strength
          90, // demand
          LimitStateType.FLEXURE,
          SectionType.W,
          DesignMethod.LRFD
        );

        // Available = 100 * 0.90 = 90, DCR = 90/90 = 1.0
        expect(result.dcr).toBe(1.0);
        expect(result.adequate).toBe(true);
      });

      it("should identify inadequate design when DCR > 1.0", () => {
        const result = performDesignCheck(
          100, // nominal strength
          95, // demand
          LimitStateType.FLEXURE,
          SectionType.W,
          DesignMethod.LRFD
        );

        // Available = 100 * 0.90 = 90, DCR = 95/90 = 1.056
        expect(result.dcr).toBeCloseTo(1.056, 3);
        expect(result.adequate).toBe(false);
      });
    });

    describe("String Input Support", () => {
      it("should accept string inputs for all enum parameters", () => {
        const result = performDesignCheck(1000, 800, "flexure", "W", "LRFD");

        expect(result.nominalStrength).toBe(1000);
        expect(result.resistanceFactor).toBe(0.9);
        expect(result.adequate).toBe(true);
      });
    });
  });

  describe("Integration Tests", () => {
    it("should integrate with actual flexural strength calculations", () => {
      // Simulate a realistic flexural design check
      const nominalMoment = 5050; // kip-in (Fy * Zx for W18x50)
      const appliedMoment = 4000; // kip-in

      const result = performDesignCheck(
        nominalMoment,
        appliedMoment,
        LimitStateType.FLEXURE,
        SectionType.W,
        DesignMethod.LRFD
      );

      const expectedAvailable = nominalMoment * 0.9; // 4545 kip-in
      const expectedDCR = appliedMoment / expectedAvailable; // ~0.88

      expect(result.availableStrength).toBe(expectedAvailable);
      expect(result.dcr).toBeCloseTo(expectedDCR, 2);
      expect(result.adequate).toBe(true);
    });

    it("should integrate with actual shear strength calculations", () => {
      // Simulate a realistic shear design check
      const nominalShear = 191.7; // kips (0.6 * Fy * Aw for W18x50)
      const appliedShear = 150; // kips

      const result = performDesignCheck(
        nominalShear,
        appliedShear,
        LimitStateType.SHEAR_YIELDING,
        SectionType.W,
        DesignMethod.LRFD
      );

      const expectedAvailable = nominalShear * 1.0; // 191.7 kips (φ = 1.0 for shear yielding)
      const expectedDCR = appliedShear / expectedAvailable; // ~0.78

      expect(result.availableStrength).toBe(expectedAvailable);
      expect(result.dcr).toBeCloseTo(expectedDCR, 2);
      expect(result.adequate).toBe(true);
    });
  });
});
