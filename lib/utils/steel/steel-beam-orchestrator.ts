/**
 * @file Steel Beam Design Check Orchestrator
 * This file provides comprehensive design check functions that orchestrate
 * flexural strength, shear strength, member stability bracing, interaction checks,
 * fire design, and optional deflection checks.
 * 
 * @version 3.0.0 - Enhanced with AISC 360-22 Appendix 6 Bracing, Chapter H Interaction, and Appendix 4 Fire Design
 * @source AISC 360-22 Steel Construction Manual
 */

import { Section } from "./slenderness/types";
import {
  DesignMethod,
  Material,
  FlexuralLimitState,
  ShearLimitState,
  LimitStateType,
  ERROR_MESSAGES,
  FLEXURAL_CONSTANTS,
  DEFLECTION_CONSTANTS
} from "./constants";
import { calculateNominalFlexuralStrength } from "./flexural/index";
import { calculateNominalShearStrength } from "./shear/index";
import { checkDeflection, DeflectionCheckResult, DEFAULT_DEFLECTION_LIMITS } from "./deflection/index";
import {
  getResistanceFactor,
  calculateAvailableStrength,
  calculateDCR,
} from "./analysis-utils";
import {
  StiffnessAdjustmentParams,
  calculateStiffnessAdjustments,
  applyStiffnessAdjustments,
} from "./stiffness-adjustment-utils";

// Import bracing functionality
import {
  BracingType,
  BracingDirection,
  BracedMemberType,
  BracingDesignResult,
  ColumnBracingInput,
  BeamBracingInput,
  BeamColumnBracingInput,
  designColumnBracing,
  designBeamBracing,
  designBeamColumnBracing,
} from "./bracing";

// Import interaction functionality
import {
  AppliedLoads,
  AvailableCapacities,
  H1_3_Parameters,
  InteractionResult,
  InteractionLimitState,
  calculateH1_Interaction,
  calculateH2_StressInteraction,
  AppliedStresses,
  AvailableStresses,
} from "./interaction";

/**
 * Interface for applied demand forces on a beam
 */
export interface DemandForces {
  Mu: number; // Required flexural strength (moment) in kip-in
  Vu: number; // Required shear strength in kips
  Pu?: number; // Required axial strength in kips (optional, for stiffness adjustments and interaction)
}

/**
 * Interface for lateral-torsional buckling parameters
 */
export interface LTBParameters {
  Lb: number; // Unbraced length in inches
  Cb?: number; // Lateral-torsional buckling modification factor (defaults to 1.0)
}

/**
 * Interface for deflection check parameters
 */
export interface DeflectionParameters {
  beamLength: number;           // Beam length in inches
  maxUpwardDeflection: number;  // Maximum upward deflection in inches (negative value)
  maxDownwardDeflection: number; // Maximum downward deflection in inches (positive value)
  deflectionLimit?: number;     // L/deflectionLimit (defaults to 240)
}

/**
 * Interface for member stability bracing parameters
 */
export interface BracingParameters {
  enabled: boolean; // Whether to perform bracing calculations
  columnBracing?: ColumnBracingInput; // Column bracing requirements
  beamBracing?: BeamBracingInput; // Beam lateral/torsional bracing requirements
  beamColumnBracing?: BeamColumnBracingInput; // Combined beam-column bracing
}

/**
 * Interface for interaction check parameters
 */
export interface InteractionParameters {
  enabled: boolean; // Whether to perform interaction checks
  h1Parameters?: H1_3_Parameters; // H1.3 out-of-plane buckling parameters
  includeH2?: boolean; // Whether to include H2 stress interaction (default: false)
  stressAnalysis?: boolean; // Whether to perform stress-based analysis (default: false)
}

/**
 * Interface for simplified fire design parameters
 */
export interface FireParameters {
  enabled: boolean; // Whether to perform fire design checks
  temperature?: number; // Fire temperature in °F (optional)
  exposureTime?: number; // Fire exposure time in hours (optional)
}

/**
 * Interface summarizing stiffness adjustment results
 */
export interface StiffnessAdjustmentSummary {
  applied: boolean; // Whether stiffness adjustments were applied
  baseReductionFactor: number; // Base 0.80 factor
  tauB: number; // Additional flexural stiffness reduction parameter
  totalFlexuralReduction: number; // Combined reduction factor
  adjustedSection?: {
    IxAdjusted: number; // Adjusted strong-axis moment of inertia
    IyAdjusted: number; // Adjusted weak-axis moment of inertia
  };
}

/**
 * Interface for beam analysis inputs
 */
export interface BeamAnalysisInputs {
  section: Section; // Cross-section properties
  material: Material; // Material properties (Fy, Fu)
  ltbParameters: LTBParameters; // Lateral-torsional buckling parameters
  demandForces: DemandForces; // Applied demand forces
  designMethod: DesignMethod; // LRFD or ASD
  applyStiffnessAdjustments?: boolean; // Whether to apply AISC 360-22 Section C2.3 adjustments (defaults to false)
  nominalAxialStrength?: number; // Pn for stiffness adjustments (required if applyStiffnessAdjustments is true)
  deflectionParameters?: DeflectionParameters; // Optional deflection check parameters
  bracingParameters?: BracingParameters; // Optional member stability bracing parameters
  interactionParameters?: InteractionParameters; // Optional interaction check parameters
  fireParameters?: FireParameters; // Optional fire design parameters
}

/**
 * Interface for flexural design check results
 */
export interface FlexuralDesignResult {
  nominalStrength: number; // Mn in kip-in
  governingLimitState: FlexuralLimitState;
  resistanceFactor: number; // φ for LRFD or Ω for ASD
  availableStrength: number; // Available moment capacity in kip-in
  demand: number; // Applied moment Mu in kip-in
  dcr: number; // Demand-to-capacity ratio
  adequate: boolean; // True if DCR ≤ 1.0
}

/**
 * Interface for shear design check results
 */
export interface ShearDesignResult {
  nominalStrength: number; // Vn in kips
  governingLimitState: ShearLimitState;
  resistanceFactor: number; // φ for LRFD or Ω for ASD
  availableStrength: number; // Available shear capacity in kips
  demand: number; // Applied shear Vu in kips
  dcr: number; // Demand-to-capacity ratio
  adequate: boolean; // True if DCR ≤ 1.0
}

/**
 * Interface for member stability bracing results
 */
export interface BracingAnalysisResult {
  performed: boolean; // Whether bracing analysis was performed
  columnBracing?: BracingDesignResult; // Column bracing results
  beamBracing?: BracingDesignResult; // Beam bracing results
  beamColumnBracing?: BracingDesignResult; // Combined beam-column bracing results
  recommendations?: string[]; // Design recommendations
}

/**
 * Interface for interaction analysis results
 */
export interface InteractionAnalysisResult {
  performed: boolean; // Whether interaction analysis was performed
  h1Result?: InteractionResult; // H1 in-plane and out-of-plane interaction results
  h2Result?: InteractionResult; // H2 stress interaction results (if applicable)
  governingRatio: number; // Governing interaction ratio
  governingLimitState?: InteractionLimitState; // Governing limit state
  adequate: boolean; // True if all interaction ratios ≤ 1.0
  recommendations?: string[]; // Design recommendations
}

/**
 * Interface for fire design analysis results
 */
export interface FireAnalysisResult {
  performed: boolean; // Whether fire design analysis was performed
  temperature?: number; // Analysis temperature in °F
  adequate: boolean; // True if fire design requirements are satisfied
  recommendations?: string[]; // Fire design recommendations
}

/**
 * Interface for complete beam design check results
 */
export interface DesignCheckResults {
  flexure: FlexuralDesignResult;
  shear: ShearDesignResult;
  stiffnessAdjustments: StiffnessAdjustmentSummary; // Stiffness adjustment details
  deflection?: DeflectionCheckResult; // Optional deflection check results
  bracing: BracingAnalysisResult; // Member stability bracing results
  interaction: InteractionAnalysisResult; // Interaction analysis results
  fireDesign: FireAnalysisResult; // Fire design analysis results
  overallAdequate: boolean; // True if all checks are adequate
  designMethod: DesignMethod;
  sectionName: string;
  materialGrade: string;
  summary: {
    criticalChecks: string[]; // List of critical design checks
    governingRatio: number; // Overall governing DCR or interaction ratio
    governingCheck: string; // Description of governing check
    designMargin: number; // Design margin (1.0 - governingRatio)
  };
}

/**
 * Maps flexural limit states to general limit state types for resistance factor lookup
 */
function mapFlexuralLimitStateToLimitStateType(
  limitState: FlexuralLimitState
): LimitStateType {
  // All flexural limit states use the same resistance factors
  return LimitStateType.FLEXURE;
}

/**
 * Maps shear limit states to general limit state types for resistance factor lookup
 */
function mapShearLimitStateToLimitStateType(
  limitState: ShearLimitState
): LimitStateType {
  switch (limitState) {
    case ShearLimitState.SHEAR_YIELDING:
      return LimitStateType.SHEAR_YIELDING;
    case ShearLimitState.SHEAR_BUCKLING:
      return LimitStateType.SHEAR_BUCKLING;
    default:
      return LimitStateType.SHEAR_YIELDING; // Default fallback
  }
}

/**
 * Performs member stability bracing analysis
 */
function performBracingAnalysis(
  section: Section,
  bracingParams: BracingParameters,
  demandForces: DemandForces,
  designMethod: DesignMethod
): BracingAnalysisResult {
  if (!bracingParams.enabled) {
    return { performed: false };
  }

  const results: BracingAnalysisResult = {
    performed: true,
    recommendations: [],
  };

  try {
    // Column bracing analysis
    if (bracingParams.columnBracing) {
      results.columnBracing = designColumnBracing(bracingParams.columnBracing);
      results.recommendations!.push(
        `Column bracing: ${results.columnBracing.strength.description}`
      );
    }

    // Beam bracing analysis
    if (bracingParams.beamBracing) {
      results.beamBracing = designBeamBracing(bracingParams.beamBracing);
      results.recommendations!.push(
        `Beam bracing: ${results.beamBracing.strength.description}`
      );
    }

    // Beam-column bracing analysis
    if (bracingParams.beamColumnBracing) {
      results.beamColumnBracing = designBeamColumnBracing(bracingParams.beamColumnBracing);
      results.recommendations!.push(
        `Beam-column bracing: ${results.beamColumnBracing.strength.description}`
      );
    }

    return results;
  } catch (error) {
    results.recommendations!.push(`Bracing analysis error: ${error}`);
    return results;
  }
}

/**
 * Performs interaction analysis (H1, H2, etc.)
 */
function performInteractionAnalysis(
  section: Section,
  material: Material,
  demandForces: DemandForces,
  flexuralResult: FlexuralDesignResult,
  shearResult: ShearDesignResult,
  interactionParams: InteractionParameters,
  designMethod: DesignMethod
): InteractionAnalysisResult {
  if (!interactionParams.enabled || !demandForces.Pu) {
    return { 
      performed: false, 
      governingRatio: Math.max(flexuralResult.dcr, shearResult.dcr),
      adequate: true 
    };
  }

  const results: InteractionAnalysisResult = {
    performed: true,
    governingRatio: 0,
    adequate: true,
    recommendations: [],
  };

  try {
    // Prepare loads and capacities for interaction analysis
    const appliedLoads: AppliedLoads = {
      Pr: demandForces.Pu,
      Mrx: demandForces.Mu,
      Mry: 0, // Assume no minor-axis moment for beam analysis
    };

    // Calculate available capacities (approximate from existing results)
    const availableCapacities: AvailableCapacities = {
      Pc: demandForces.Pu * 10, // Placeholder - should be calculated from column analysis
      Mcx: flexuralResult.availableStrength,
      Mcy: flexuralResult.availableStrength * 0.1, // Approximate minor-axis capacity
    };

    // H1 Interaction Analysis
    results.h1Result = calculateH1_Interaction(
      appliedLoads,
      availableCapacities,
      interactionParams.h1Parameters
    );

    results.governingRatio = Math.max(results.governingRatio, results.h1Result.governingRatio);
    results.governingLimitState = results.h1Result.governingLimitState;

    // H2 Stress Interaction Analysis (if requested)
    if (interactionParams.includeH2 && interactionParams.stressAnalysis && section.Sx) {
      const appliedStresses: AppliedStresses = {
        fra: demandForces.Pu / section.A, // Required axial stress
        frbw: demandForces.Mu / section.Sx, // Required major-axis bending stress
        frbz: 0, // Required minor-axis bending stress
      };

      const availableStresses: AvailableStresses = {
        Fca: material.Fy * 0.6, // Approximate allowable axial stress
        Fcbw: material.Fy * 0.75, // Approximate allowable bending stress
        Fcbz: material.Fy * 0.75, // Approximate allowable bending stress
      };

      results.h2Result = calculateH2_StressInteraction(appliedStresses, availableStresses);
      results.governingRatio = Math.max(results.governingRatio, results.h2Result.governingRatio);
    }

    results.adequate = results.governingRatio <= 1.0;

    // Add recommendations
    if (!results.adequate) {
      results.recommendations!.push("Interaction ratio exceeds 1.0 - consider larger section");
    }
    if (results.h1Result && results.h1Result.metadata?.usedH1_3) {
      results.recommendations!.push("H1.3 out-of-plane buckling governs - consider lateral bracing");
    }

    return results;
  } catch (error) {
    results.recommendations!.push(`Interaction analysis error: ${error}`);
    results.adequate = false;
    return results;
  }
}

/**
 * Performs simplified fire design analysis
 */
function performFireAnalysis(
  section: Section,
  material: Material,
  fireParams: FireParameters,
  designMethod: DesignMethod
): FireAnalysisResult {
  if (!fireParams.enabled) {
    return { performed: false, adequate: true };
  }

  const results: FireAnalysisResult = {
    performed: true,
    adequate: true,
    recommendations: [],
    temperature: fireParams.temperature || 1000, // Default fire temperature
  };

  try {
    // Simplified fire analysis - check if temperature exceeds material limits
    const criticalTemp = material.Fy > 50 ? 1000 : 900; // Simplified threshold

    if (results.temperature! > criticalTemp) {
      results.adequate = false;
      results.recommendations!.push(`Temperature ${results.temperature}°F exceeds critical temperature ${criticalTemp}°F`);
      results.recommendations!.push("Consider fire protection or high-temperature materials");
    } else {
      results.recommendations!.push(`Temperature ${results.temperature}°F is within acceptable limits`);
    }

    return results;
  } catch (error) {
    results.recommendations!.push(`Fire design analysis error: ${error}`);
    results.adequate = false;
    return results;
  }
}

/**
 * Main orchestration function for comprehensive beam design checks.
 * Handles flexural strength, shear strength, member stability bracing, interaction checks,
 * fire design, optional stiffness adjustments, and optional deflection checks.
 * @param {BeamAnalysisInputs} beamAnalysisInputs All required inputs for beam analysis
 * @returns {DesignCheckResults} Comprehensive beam design check results
 * @throws {Error} if inputs are invalid or missing
 */
export function getDesignCheckResults(
  beamAnalysisInputs: BeamAnalysisInputs
): DesignCheckResults {
  // Validate inputs
  if (!beamAnalysisInputs) {
    throw new Error(ERROR_MESSAGES.BEAM_ANALYSIS_INPUTS_REQUIRED);
  }

  const {
    section,
    material,
    ltbParameters,
    demandForces,
    designMethod,
    applyStiffnessAdjustments: shouldApplyStiffnessAdjustments = false,
    nominalAxialStrength,
    deflectionParameters,
    bracingParameters,
    interactionParameters,
    fireParameters,
  } = beamAnalysisInputs;

  // Validate required inputs
  if (!section) {
    throw new Error(ERROR_MESSAGES.SECTION_REQUIRED);
  }
  if (!material) {
    throw new Error(ERROR_MESSAGES.MATERIAL_REQUIRED);
  }
  if (!ltbParameters) {
    throw new Error(ERROR_MESSAGES.LTB_PARAMETERS_REQUIRED);
  }
  if (!demandForces) {
    throw new Error(ERROR_MESSAGES.DEMAND_FORCES_REQUIRED);
  }
  if (!designMethod) {
    throw new Error(ERROR_MESSAGES.DESIGN_METHOD_REQUIRED);
  }

  // Validate stiffness adjustment inputs
  if (shouldApplyStiffnessAdjustments) {
    if (typeof nominalAxialStrength !== "number" || nominalAxialStrength <= 0) {
      throw new Error(ERROR_MESSAGES.NOMINAL_AXIAL_STRENGTH_REQUIRED);
    }
    if (typeof demandForces.Pu !== "number" || demandForces.Pu < 0) {
      throw new Error(ERROR_MESSAGES.PU_NON_NEGATIVE_REQUIRED);
    }
  }

  // Validate demand forces
  if (typeof demandForces.Mu !== "number" || demandForces.Mu < 0) {
    throw new Error(ERROR_MESSAGES.MU_NON_NEGATIVE);
  }
  if (typeof demandForces.Vu !== "number" || demandForces.Vu < 0) {
    throw new Error(ERROR_MESSAGES.VU_NON_NEGATIVE);
  }

  // Validate LTB parameters
  if (typeof ltbParameters.Lb !== "number" || ltbParameters.Lb < 0) {
    throw new Error(ERROR_MESSAGES.LB_NON_NEGATIVE);
  }

  const Cb = ltbParameters.Cb ?? FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR; // Default Cb to 1.0 if not provided

  // Step 1: Calculate stiffness adjustments if requested
  let stiffnessAdjustmentSummary: StiffnessAdjustmentSummary = {
    applied: false,
    baseReductionFactor: FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR,
    tauB: FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR,
    totalFlexuralReduction: FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR,
  };

  let effectiveSection = section; // Use original section by default

  if (shouldApplyStiffnessAdjustments && nominalAxialStrength && demandForces.Pu !== undefined) {
    const stiffnessParams: StiffnessAdjustmentParams = {
      Pr: demandForces.Pu,
      Pn: nominalAxialStrength,
      designMethod,
    };

    const adjustments = calculateStiffnessAdjustments(stiffnessParams);

    // Apply adjustments to moment of inertia if available
    if (section.Ix && section.Iy) {
      const adjustedMomentOfInertia = applyStiffnessAdjustments(
        section.Ix,
        section.Iy,
        stiffnessParams
      );

      // Create an adjusted section for calculations
      effectiveSection = {
        ...section,
        Ix: adjustedMomentOfInertia.IxAdjusted,
        Iy: adjustedMomentOfInertia.IyAdjusted,
      };

      stiffnessAdjustmentSummary = {
        applied: true,
        baseReductionFactor: adjustments.baseReductionFactor,
        tauB: adjustments.tauB,
        totalFlexuralReduction: adjustments.totalFlexuralReduction,
        adjustedSection: {
          IxAdjusted: adjustedMomentOfInertia.IxAdjusted,
          IyAdjusted: adjustedMomentOfInertia.IyAdjusted,
        },
      };
    } else {
      // If Ix and Iy are not available, still record the adjustment factors
      stiffnessAdjustmentSummary = {
        applied: true,
        baseReductionFactor: adjustments.baseReductionFactor,
        tauB: adjustments.tauB,
        totalFlexuralReduction: adjustments.totalFlexuralReduction,
      };
    }
  }

  // Step 2: Calculate nominal flexural strength (using effective section)
  const flexuralResult = calculateNominalFlexuralStrength(
    effectiveSection,
    material,
    ltbParameters.Lb,
    Cb
  );

  // Step 3: Calculate nominal shear strength (using effective section)
  const shearResult = calculateNominalShearStrength(effectiveSection, material);

  // Step 4: Get resistance factors for flexure
  const flexuralLimitStateType = mapFlexuralLimitStateToLimitStateType(
    flexuralResult.governingLimitState
  );
  const flexuralResistanceFactor = getResistanceFactor(
    flexuralLimitStateType,
    section.Type,
    designMethod
  );

  // Step 5: Calculate available flexural strength
  const availableFlexuralStrength = calculateAvailableStrength(
    flexuralResult.Mn,
    flexuralResistanceFactor,
    designMethod
  );

  // Step 6: Calculate flexural DCR
  const flexuralDCR = calculateDCR(demandForces.Mu, availableFlexuralStrength);

  // Step 7: Get resistance factors for shear
  const shearLimitStateType = mapShearLimitStateToLimitStateType(
    shearResult.governingLimitState
  );
  const shearResistanceFactor = getResistanceFactor(
    shearLimitStateType,
    section.Type,
    designMethod
  );

  // Step 8: Calculate available shear strength
  const availableShearStrength = calculateAvailableStrength(
    shearResult.Vn,
    shearResistanceFactor,
    designMethod
  );

  // Step 9: Calculate shear DCR
  const shearDCR = calculateDCR(demandForces.Vu, availableShearStrength);

  // Step 10: Assemble flexural design results
  const flexure: FlexuralDesignResult = {
    nominalStrength: flexuralResult.Mn,
    governingLimitState: flexuralResult.governingLimitState,
    resistanceFactor: flexuralResistanceFactor,
    availableStrength: availableFlexuralStrength,
    demand: demandForces.Mu,
    dcr: flexuralDCR,
    adequate: flexuralDCR <= DEFLECTION_CONSTANTS.ADEQUACY_THRESHOLD,
  };

  // Step 11: Assemble shear design results
  const shear: ShearDesignResult = {
    nominalStrength: shearResult.Vn,
    governingLimitState: shearResult.governingLimitState,
    resistanceFactor: shearResistanceFactor,
    availableStrength: availableShearStrength,
    demand: demandForces.Vu,
    dcr: shearDCR,
    adequate: shearDCR <= DEFLECTION_CONSTANTS.ADEQUACY_THRESHOLD,
  };

  // Step 12: Perform deflection check if parameters are provided
  let deflectionResult: DeflectionCheckResult | undefined;
  if (deflectionParameters) {
    deflectionResult = checkDeflection({
      section: effectiveSection,
      material,
      beamLength: deflectionParameters.beamLength,
      maxUpwardDeflection: deflectionParameters.maxUpwardDeflection,
      maxDownwardDeflection: deflectionParameters.maxDownwardDeflection,
      deflectionLimit: deflectionParameters.deflectionLimit,
    });
  }

  // Step 13: Perform bracing analysis if parameters are provided
  const bracingResult = performBracingAnalysis(
    effectiveSection,
    bracingParameters || { enabled: false },
    demandForces,
    designMethod
  );

  // Step 14: Perform interaction analysis if parameters are provided
  const interactionResult = performInteractionAnalysis(
    effectiveSection,
    material,
    demandForces,
    flexure,
    shear,
    interactionParameters || { enabled: false },
    designMethod
  );

  // Step 15: Perform fire design analysis if parameters are provided
  const fireResult = performFireAnalysis(
    effectiveSection,
    material,
    fireParameters || { enabled: false },
    designMethod
  );

  // Step 16: Determine overall adequacy (including all new checks)
  const allChecksAdequate = [
    flexure.adequate,
    shear.adequate,
    deflectionResult ? deflectionResult.overallAdequate : true,
    bracingResult.performed ? true : true, // Bracing analysis doesn't have adequate property
    interactionResult.adequate,
    fireResult.adequate,
  ].every(Boolean);

  // Step 17: Calculate summary information
  const allRatios = [
    flexuralDCR,
    shearDCR,
    interactionResult.governingRatio,
  ].filter(ratio => ratio > 0);

  const governingRatio = Math.max(...allRatios);
  const criticalChecks: string[] = [];
  let governingCheck = "Flexure";

  if (flexuralDCR === governingRatio && flexuralDCR > 0.8) {
    criticalChecks.push("Flexural strength");
    governingCheck = "Flexural strength";
  }
  if (shearDCR === governingRatio && shearDCR > 0.8) {
    criticalChecks.push("Shear strength");
    governingCheck = "Shear strength";
  }
  if (interactionResult.governingRatio === governingRatio && interactionResult.governingRatio > 0.8) {
    criticalChecks.push("Member interaction");
    governingCheck = "Member interaction";
  }
  if (deflectionResult && !deflectionResult.overallAdequate) {
    criticalChecks.push("Deflection limits");
  }
  if (!fireResult.adequate) {
    criticalChecks.push("Fire design");
  }

  // Step 18: Create material grade string
  const materialGrade = `Fy = ${material.Fy} ksi, Fu = ${material.Fu} ksi`;

  // Step 19: Return comprehensive results
  return {
    flexure,
    shear,
    stiffnessAdjustments: stiffnessAdjustmentSummary,
    deflection: deflectionResult,
    bracing: bracingResult,
    interaction: interactionResult,
    fireDesign: fireResult,
    overallAdequate: allChecksAdequate,
    designMethod,
    sectionName: section.name,
    materialGrade,
    summary: {
      criticalChecks,
      governingRatio,
      governingCheck,
      designMargin: Math.max(0, 1.0 - governingRatio),
    },
  };
}

/**
 * Convenience function for LRFD design checks.
 * @param {Section} section Cross-section properties
 * @param {Material} material Material properties
 * @param {number} Lb Unbraced length in inches
 * @param {number} Mu Required flexural strength in kip-in
 * @param {number} Vu Required shear strength in kips
 * @param {number} Cb Lateral-torsional buckling modification factor (optional, defaults to 1.0)
 * @returns {DesignCheckResults} Complete LRFD design check results
 */
export function performLRFDDesignCheck(
  section: Section,
  material: Material,
  Lb: number,
  Mu: number,
  Vu: number,
  Cb: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR
): DesignCheckResults {
  const inputs: BeamAnalysisInputs = {
    section,
    material,
    ltbParameters: { Lb, Cb },
    demandForces: { Mu, Vu },
    designMethod: DesignMethod.LRFD,
  };
  return getDesignCheckResults(inputs);
}

/**
 * Convenience function for ASD design checks.
 * @param {Section} section Cross-section properties
 * @param {Material} material Material properties
 * @param {number} Lb Unbraced length in inches
 * @param {number} Ma Required flexural strength in kip-in
 * @param {number} Va Required shear strength in kips
 * @param {number} Cb Lateral-torsional buckling modification factor (optional, defaults to 1.0)
 * @returns {DesignCheckResults} Complete ASD design check results
 */
export function performASDDesignCheck(
  section: Section,
  material: Material,
  Lb: number,
  Ma: number,
  Va: number,
  Cb: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR
): DesignCheckResults {
  const inputs: BeamAnalysisInputs = {
    section,
    material,
    ltbParameters: { Lb, Cb },
    demandForces: { Mu: Ma, Vu: Va },
    designMethod: DesignMethod.ASD,
  };
  return getDesignCheckResults(inputs);
}

/**
 * Convenience function for LRFD design checks with deflection.
 * @param {Section} section Cross-section properties
 * @param {Material} material Material properties
 * @param {number} Lb Unbraced length in inches
 * @param {number} Mu Required flexural strength in kip-in
 * @param {number} Vu Required shear strength in kips
 * @param {number} beamLength Beam length in inches
 * @param {number} maxUpwardDeflection Maximum upward deflection in inches
 * @param {number} maxDownwardDeflection Maximum downward deflection in inches
 * @param {number} Cb Lateral-torsional buckling modification factor (optional, defaults to 1.0)
 * @param {number} deflectionLimit L/deflectionLimit (optional, defaults to 240)
 * @returns {DesignCheckResults} Complete LRFD design check results with deflection
 */
export function performLRFDDesignCheckWithDeflection(
  section: Section,
  material: Material,
  Lb: number,
  Mu: number,
  Vu: number,
  beamLength: number,
  maxUpwardDeflection: number,
  maxDownwardDeflection: number,
  Cb: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR,
  deflectionLimit: number = DEFAULT_DEFLECTION_LIMITS.TOTAL
): DesignCheckResults {
  const inputs: BeamAnalysisInputs = {
    section,
    material,
    ltbParameters: { Lb, Cb },
    demandForces: { Mu, Vu },
    designMethod: DesignMethod.LRFD,
    deflectionParameters: {
      beamLength,
      maxUpwardDeflection,
      maxDownwardDeflection,
      deflectionLimit,
    },
  };
  return getDesignCheckResults(inputs);
}

/**
 * Convenience function for ASD design checks with deflection.
 * @param {Section} section Cross-section properties
 * @param {Material} material Material properties
 * @param {number} Lb Unbraced length in inches
 * @param {number} Ma Required flexural strength in kip-in
 * @param {number} Va Required shear strength in kips
 * @param {number} beamLength Beam length in inches
 * @param {number} maxUpwardDeflection Maximum upward deflection in inches
 * @param {number} maxDownwardDeflection Maximum downward deflection in inches
 * @param {number} Cb Lateral-torsional buckling modification factor (optional, defaults to 1.0)
 * @param {number} deflectionLimit L/deflectionLimit (optional, defaults to 240)
 * @returns {DesignCheckResults} Complete ASD design check results with deflection
 */
export function performASDDesignCheckWithDeflection(
  section: Section,
  material: Material,
  Lb: number,
  Ma: number,
  Va: number,
  beamLength: number,
  maxUpwardDeflection: number,
  maxDownwardDeflection: number,
  Cb: number = FLEXURAL_CONSTANTS.INITIAL_PLASTIFICATION_FACTOR,
  deflectionLimit: number = DEFAULT_DEFLECTION_LIMITS.TOTAL
): DesignCheckResults {
  const inputs: BeamAnalysisInputs = {
    section,
    material,
    ltbParameters: { Lb, Cb },
    demandForces: { Mu: Ma, Vu: Va },
    designMethod: DesignMethod.ASD,
    deflectionParameters: {
      beamLength,
      maxUpwardDeflection,
      maxDownwardDeflection,
      deflectionLimit,
    },
  };
  return getDesignCheckResults(inputs);
}

/**
 * Advanced function for comprehensive beam-column analysis with all features enabled.
 * Includes member stability bracing, interaction checks, and fire design.
 * @param {BeamAnalysisInputs} inputs Complete beam analysis inputs with all optional parameters
 * @returns {DesignCheckResults} Complete comprehensive design check results
 */
export function performComprehensiveDesignCheck(
  inputs: BeamAnalysisInputs
): DesignCheckResults {
  return getDesignCheckResults(inputs);
}

/**
 * Convenience function to create bracing parameters for typical beam applications.
 * @param {number} Mr Required flexural strength in kip-in
 * @param {number} ho Distance between flange centroids in inches
 * @param {number} Lbr Unbraced length in inches
 * @param {DesignMethod} designMethod LRFD or ASD
 * @param {BracingType} bracingType Panel or Point bracing
 * @returns {BracingParameters} Configured bracing parameters
 */
export function createBeamBracingParameters(
  Mr: number,
  ho: number,
  Lbr: number,
  designMethod: DesignMethod,
  bracingType: BracingType = BracingType.PANEL
): BracingParameters {
  return {
    enabled: true,
    beamBracing: {
      Mr,
      ho,
      Lbr,
      designMethod,
      bracingType,
      bracingDirection: BracingDirection.LATERAL,
      Cd: 1.0, // Standard coefficient
    },
  };
}

/**
 * Convenience function to create interaction parameters for beam-column analysis.
 * @param {H1_3_Parameters} h1Parameters H1.3 out-of-plane buckling parameters
 * @param {boolean} includeH2 Whether to include H2 stress interaction
 * @returns {InteractionParameters} Configured interaction parameters
 */
export function createInteractionParameters(
  h1Parameters?: H1_3_Parameters,
  includeH2: boolean = false
): InteractionParameters {
  return {
    enabled: true,
    h1Parameters,
    includeH2,
    stressAnalysis: includeH2,
  };
}

/**
 * Convenience function to create fire design parameters.
 * @param {number} temperature Fire temperature in °F
 * @param {number} exposureTime Fire exposure time in hours
 * @returns {FireParameters} Configured fire parameters
 */
export function createFireParameters(temperature: number = 1000, exposureTime: number = 2): FireParameters {
  return {
    enabled: true,
    temperature,
    exposureTime,
  };
}
