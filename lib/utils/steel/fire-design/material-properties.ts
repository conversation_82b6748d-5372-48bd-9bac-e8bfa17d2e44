/**
 * @file Fire Design Material Properties
 * 
 * Material property calculations for AISC 360-22 Appendix 4
 * "Structural Design for Fire Conditions"
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4.3b, Table A-4.2.1
 */

import { SteelPropertiesAtTemperature, StressStrainParameters } from './types';

/**
 * Steel material property retention factors per AISC Table A-4.2.1
 * Temperature ranges from 68°F to 2200°F
 */
export const STEEL_RETENTION_FACTORS = [
  { temp: 68, kE: 1.00, kp: 1.00, ky: 1.00 },
  { temp: 200, kE: 1.00, kp: 1.00, ky: 1.00 },
  { temp: 400, kE: 0.90, kp: 0.80, ky: 1.00 },
  { temp: 600, kE: 0.78, kp: 0.58, ky: 1.00 },
  { temp: 750, kE: 0.70, kp: 0.42, ky: 1.00 },
  { temp: 800, kE: 0.67, kp: 0.40, ky: 0.94 },
  { temp: 1000, kE: 0.49, kp: 0.29, ky: 0.66 },
  { temp: 1200, kE: 0.22, kp: 0.13, ky: 0.35 },
  { temp: 1400, kE: 0.11, kp: 0.06, ky: 0.16 },
  { temp: 1600, kE: 0.07, kp: 0.04, ky: 0.07 },
  { temp: 1800, kE: 0.05, kp: 0.03, ky: 0.04 },
  { temp: 2000, kE: 0.02, kp: 0.01, ky: 0.02 },
  { temp: 2200, kE: 0.00, kp: 0.00, ky: 0.00 },
] as const;

/**
 * Strain constants for stress-strain relationships per AISC A4.3b
 */
export const STRAIN_CONSTANTS = {
  /** Engineering yield strain at elevated temperature, in/in */
  EPSILON_Y_T: 0.02,
  /** Ultimate strain at elevated temperature, in/in */
  EPSILON_U_T: 0.15,
} as const;

/**
 * Interpolates retention factors from AISC Table A-4.2.1 for a given temperature
 * 
 * @param temperature - Temperature in °F
 * @returns Interpolated retention factors
 * 
 * @source AISC 360-22, Table A-4.2.1
 */
export function interpolateRetentionFactors(temperature: number): {
  kE: number;
  kp: number;
  ky: number;
} {
  // Clamp temperature to table bounds
  if (temperature <= STEEL_RETENTION_FACTORS[0].temp) {
    const first = STEEL_RETENTION_FACTORS[0];
    return { kE: first.kE, kp: first.kp, ky: first.ky };
  }
  
  if (temperature >= STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length - 1].temp) {
    const last = STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length - 1];
    return { kE: last.kE, kp: last.kp, ky: last.ky };
  }
  
  // Find bounding temperatures
  let lowerIndex = 0;
  for (let i = 0; i < STEEL_RETENTION_FACTORS.length - 1; i++) {
    if (temperature >= STEEL_RETENTION_FACTORS[i].temp && 
        temperature <= STEEL_RETENTION_FACTORS[i + 1].temp) {
      lowerIndex = i;
      break;
    }
  }
  
  const lower = STEEL_RETENTION_FACTORS[lowerIndex];
  const upper = STEEL_RETENTION_FACTORS[lowerIndex + 1];
  
  // Linear interpolation
  const ratio = (temperature - lower.temp) / (upper.temp - lower.temp);
  
  return {
    kE: lower.kE + ratio * (upper.kE - lower.kE),
    kp: lower.kp + ratio * (upper.kp - lower.kp),
    ky: lower.ky + ratio * (upper.ky - lower.ky),
  };
}

/**
 * Calculates steel material properties at elevated temperature
 * per AISC A4.3b and Table A-4.2.1
 * 
 * @param temperature - Temperature in °F
 * @param Fy_ambient - Ambient yield strength, ksi
 * @param Fu_ambient - Ambient tensile strength, ksi
 * @param E_ambient - Ambient elastic modulus, ksi (defaults to 29000)
 * @returns Steel properties at elevated temperature
 * 
 * @source AISC 360-22, Appendix 4.3b, Table A-4.2.1
 */
export function calculateSteelPropertiesAtTemperature(
  temperature: number,
  Fy_ambient: number,
  Fu_ambient: number,
  E_ambient: number = 29000
): SteelPropertiesAtTemperature {
  if (Fy_ambient <= 0) {
    throw new Error("Ambient yield strength (Fy) must be positive");
  }
  if (Fu_ambient <= 0) {
    throw new Error("Ambient tensile strength (Fu) must be positive");
  }
  if (E_ambient <= 0) {
    throw new Error("Ambient elastic modulus (E) must be positive");
  }
  
  // Apply material limits per AISC A4.3b User Note
  if (Fy_ambient > 65) {
    throw new Error("AISC A4.3b applies only to steel with Fy ≤ 65 ksi");
  }
  
  const retentionFactors = interpolateRetentionFactors(temperature);
  
  // Calculate properties at temperature using retention factors
  const E_T = retentionFactors.kE * E_ambient;
  const Fp_T = retentionFactors.kp * Fy_ambient;
  const Fy_T = retentionFactors.ky * Fy_ambient;
  
  // For Fu(T): equal to Fy(T) for T > 750°F, otherwise Fu may be used
  const Fu_T = temperature > 750 ? Fy_T : Fu_ambient;
  
  return {
    temperature,
    kE: retentionFactors.kE,
    kp: retentionFactors.kp,
    ky: retentionFactors.ky,
    E_T,
    Fp_T,
    Fy_T,
    Fu_T,
  };
}

/**
 * Calculates stress-strain parameters for elevated temperature analysis
 * per AISC A4 Equations A-4-5, A-4-6, A-4-7
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @returns Stress-strain parameters for analysis
 * 
 * @source AISC 360-22, Appendix 4, Equations A-4-5, A-4-6, A-4-7
 */
export function calculateStressStrainParameters(
  steelProps: SteelPropertiesAtTemperature
): StressStrainParameters {
  const { E_T, Fp_T, Fy_T } = steelProps;
  
  // Engineering strain at proportional limit per AISC A4.3b
  const epsilon_p_T = Fp_T / E_T;
  
  // Engineering strain constants
  const epsilon_y_T = STRAIN_CONSTANTS.EPSILON_Y_T;
  const epsilon_u_T = STRAIN_CONSTANTS.EPSILON_U_T;
  
  // Calculate parameters per AISC Equations A-4-5, A-4-6, A-4-7
  // A-4-7: c = [Fy(T) - Fp(T)]² / {E(T)[εy(T) - εp(T)] - 2[Fy(T) - Fp(T)]}
  const numerator = Math.pow(Fy_T - Fp_T, 2);
  const denominator = E_T * (epsilon_y_T - epsilon_p_T) - 2 * (Fy_T - Fp_T);
  const c = numerator / denominator;
  
  // A-4-5: a² = [εy(T) - εp(T)][εy(T) - εp(T) + c/E(T)]
  const term1 = epsilon_y_T - epsilon_p_T;
  const term2 = epsilon_y_T - epsilon_p_T + c / E_T;
  const a_squared = term1 * term2;
  
  // A-4-6: b² = E(T)[εy(T) - εp(T)]c + c²
  const b_squared = E_T * (epsilon_y_T - epsilon_p_T) * c + Math.pow(c, 2);
  
  return {
    epsilon_T: 0, // Will be determined based on stress level
    epsilon_p_T,
    epsilon_y_T,
    epsilon_u_T,
    a_squared,
    b_squared,
    c,
  };
}

/**
 * Calculates engineering stress at elevated temperature per AISC A4 Equations A-4-2, A-4-3, A-4-4
 * 
 * @param strain - Engineering strain, in/in
 * @param steelProps - Steel properties at elevated temperature
 * @param parameters - Stress-strain parameters
 * @returns Engineering stress at elevated temperature, ksi
 * 
 * @source AISC 360-22, Appendix 4, Equations A-4-2, A-4-3, A-4-4
 */
export function calculateStressAtTemperature(
  strain: number,
  steelProps: SteelPropertiesAtTemperature,
  parameters: StressStrainParameters
): number {
  const { E_T, Fp_T, Fy_T } = steelProps;
  const { epsilon_p_T, epsilon_y_T, epsilon_u_T, a_squared, b_squared, c } = parameters;
  
  if (strain < 0) {
    throw new Error("Strain must be non-negative");
  }
  
  // (1) Elastic range: ε(T) ≤ εp(T)
  if (strain <= epsilon_p_T) {
    // AISC Eq. A-4-2: F(T) = E(T)ε(T)
    return E_T * strain;
  }
  
  // (2) Nonlinear range: εp(T) < ε(T) < εy(T)
  else if (strain < epsilon_y_T) {
    // AISC Eq. A-4-3: F(T) = Fp(T) - c + (b/a)√[a² - [εy(T) - ε(T)]²]
    const term = a_squared - Math.pow(epsilon_y_T - strain, 2);
    if (term < 0) {
      throw new Error("Invalid stress-strain parameters: negative square root term");
    }
    const b_over_a = Math.sqrt(b_squared) / Math.sqrt(a_squared);
    return Fp_T - c + b_over_a * Math.sqrt(term);
  }
  
  // (3) Plastic range: εy(T) ≤ ε(T) ≤ εu(T)
  else if (strain <= epsilon_u_T) {
    // AISC Eq. A-4-4: F(T) = Fy(T)
    return Fy_T;
  }
  
  // Beyond ultimate strain
  else {
    throw new Error(`Strain ${strain.toFixed(4)} exceeds ultimate strain ${epsilon_u_T.toFixed(4)}`);
  }
} 