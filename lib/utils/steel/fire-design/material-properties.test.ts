/**
 * @file Material Properties Tests
 * 
 * Tests for AISC 360-22 Appendix 4 material property calculations
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 */

import {
  interpolateRetentionFactors,
  calculateSteelPropertiesAtTemperature,
  calculateStressStrainParameters,
  calculateStressAtTemperature,
  STEEL_RETENTION_FACTORS,
  STRAIN_CONSTANTS,
} from './material-properties';

describe('Material Properties - Steel Retention Factors', () => {
  test('interpolateRetentionFactors - exact table values', () => {
    // Test exact values from table
    const factors68 = interpolateRetentionFactors(68);
    expect(factors68.kE).toBeCloseTo(1.00);
    expect(factors68.kp).toBeCloseTo(1.00);
    expect(factors68.ky).toBeCloseTo(1.00);
    
    const factors800 = interpolateRetentionFactors(800);
    expect(factors800.kE).toBeCloseTo(0.67);
    expect(factors800.kp).toBeCloseTo(0.40);
    expect(factors800.ky).toBeCloseTo(0.94);
    
    const factors2200 = interpolateRetentionFactors(2200);
    expect(factors2200.kE).toBeCloseTo(0.00);
    expect(factors2200.kp).toBeCloseTo(0.00);
    expect(factors2200.ky).toBeCloseTo(0.00);
  });

  test('interpolateRetentionFactors - interpolated values', () => {
    // Test interpolation between 600°F and 750°F
    const factors675 = interpolateRetentionFactors(675);
    // kE: 0.78 + (675-600)/(750-600) * (0.70-0.78) = 0.78 + 0.5 * (-0.08) = 0.74
    expect(factors675.kE).toBeCloseTo(0.74, 2);
    // kp: 0.58 + 0.5 * (0.42-0.58) = 0.58 + 0.5 * (-0.16) = 0.50
    expect(factors675.kp).toBeCloseTo(0.50, 2);
    // ky: 1.00 + 0.5 * (1.00-1.00) = 1.00
    expect(factors675.ky).toBeCloseTo(1.00, 2);
  });

  test('interpolateRetentionFactors - boundary conditions', () => {
    // Below minimum temperature
    const factorsLow = interpolateRetentionFactors(0);
    expect(factorsLow.kE).toBe(1.00);
    expect(factorsLow.kp).toBe(1.00);
    expect(factorsLow.ky).toBe(1.00);
    
    // Above maximum temperature
    const factorsHigh = interpolateRetentionFactors(3000);
    expect(factorsHigh.kE).toBe(0.00);
    expect(factorsHigh.kp).toBe(0.00);
    expect(factorsHigh.ky).toBe(0.00);
  });
});

describe('Material Properties - Steel Properties at Temperature', () => {
  test('calculateSteelPropertiesAtTemperature - ambient conditions', () => {
    const props = calculateSteelPropertiesAtTemperature(68, 50, 65);
    
    expect(props.temperature).toBe(68);
    expect(props.kE).toBeCloseTo(1.00);
    expect(props.kp).toBeCloseTo(1.00);
    expect(props.ky).toBeCloseTo(1.00);
    expect(props.E_T).toBeCloseTo(29000);
    expect(props.Fp_T).toBeCloseTo(50);
    expect(props.Fy_T).toBeCloseTo(50);
    expect(props.Fu_T).toBeCloseTo(65);
  });

  test('calculateSteelPropertiesAtTemperature - elevated temperature', () => {
    const props = calculateSteelPropertiesAtTemperature(1000, 50, 65);
    
    expect(props.temperature).toBe(1000);
    expect(props.kE).toBeCloseTo(0.49);
    expect(props.kp).toBeCloseTo(0.29);
    expect(props.ky).toBeCloseTo(0.66);
    expect(props.E_T).toBeCloseTo(0.49 * 29000);
    expect(props.Fp_T).toBeCloseTo(0.29 * 50);
    expect(props.Fy_T).toBeCloseTo(0.66 * 50);
    expect(props.Fu_T).toBeCloseTo(0.66 * 50); // Fu_T = Fy_T for T > 750°F
  });

  test('calculateSteelPropertiesAtTemperature - Fu(T) behavior', () => {
    // Below 750°F - Fu_T should equal Fu_ambient
    const propsLow = calculateSteelPropertiesAtTemperature(600, 50, 65);
    expect(propsLow.Fu_T).toBeCloseTo(65);
    
    // Above 750°F - Fu_T should equal Fy_T
    const propsHigh = calculateSteelPropertiesAtTemperature(800, 50, 65);
    expect(propsHigh.Fu_T).toBeCloseTo(propsHigh.Fy_T);
  });

  test('calculateSteelPropertiesAtTemperature - input validation', () => {
    expect(() => calculateSteelPropertiesAtTemperature(800, 0, 65)).toThrow('Ambient yield strength');
    expect(() => calculateSteelPropertiesAtTemperature(800, 50, 0)).toThrow('Ambient tensile strength');
    expect(() => calculateSteelPropertiesAtTemperature(800, 50, 65, 0)).toThrow('Ambient elastic modulus');
    expect(() => calculateSteelPropertiesAtTemperature(800, 70, 65)).toThrow('Fy ≤ 65 ksi');
  });
});

describe('Material Properties - Stress-Strain Parameters', () => {
  test('calculateStressStrainParameters - basic calculation', () => {
    const steelProps = calculateSteelPropertiesAtTemperature(800, 50, 65);
    const params = calculateStressStrainParameters(steelProps);
    
    expect(params.epsilon_p_T).toBeCloseTo(steelProps.Fp_T / steelProps.E_T);
    expect(params.epsilon_y_T).toBe(STRAIN_CONSTANTS.EPSILON_Y_T);
    expect(params.epsilon_u_T).toBe(STRAIN_CONSTANTS.EPSILON_U_T);
    expect(params.a_squared).toBeGreaterThan(0);
    expect(params.b_squared).toBeGreaterThan(0);
    expect(params.c).toBeGreaterThan(0);
  });

  test('calculateStressStrainParameters - parameter relationships', () => {
    const steelProps = calculateSteelPropertiesAtTemperature(1000, 50, 65);
    const params = calculateStressStrainParameters(steelProps);
    
    // Verify parameter calculations per AISC equations
    const { E_T, Fp_T, Fy_T } = steelProps;
    const { epsilon_p_T, epsilon_y_T, a_squared, b_squared, c } = params;
    
    // Check A-4-7: c = [Fy(T) - Fp(T)]² / {E(T)[εy(T) - εp(T)] - 2[Fy(T) - Fp(T)]}
    const numerator = Math.pow(Fy_T - Fp_T, 2);
    const denominator = E_T * (epsilon_y_T - epsilon_p_T) - 2 * (Fy_T - Fp_T);
    const expectedC = numerator / denominator;
    expect(c).toBeCloseTo(expectedC, 6);
    
    // Check A-4-5: a² = [εy(T) - εp(T)][εy(T) - εp(T) + c/E(T)]
    const term1 = epsilon_y_T - epsilon_p_T;
    const term2 = epsilon_y_T - epsilon_p_T + c / E_T;
    const expectedA2 = term1 * term2;
    expect(a_squared).toBeCloseTo(expectedA2, 6);
    
    // Check A-4-6: b² = E(T)[εy(T) - εp(T)]c + c²
    const expectedB2 = E_T * (epsilon_y_T - epsilon_p_T) * c + Math.pow(c, 2);
    expect(b_squared).toBeCloseTo(expectedB2, 6);
  });
});

describe('Material Properties - Stress at Temperature', () => {
  const steelProps = calculateSteelPropertiesAtTemperature(800, 50, 65);
  const params = calculateStressStrainParameters(steelProps);

  test('calculateStressAtTemperature - elastic range', () => {
    const strain = params.epsilon_p_T * 0.5; // Half of proportional limit strain
    const stress = calculateStressAtTemperature(strain, steelProps, params);
    
    // Should follow elastic relationship: F(T) = E(T) * ε(T)
    const expectedStress = steelProps.E_T * strain;
    expect(stress).toBeCloseTo(expectedStress);
  });

  test('calculateStressAtTemperature - plastic range', () => {
    const strain = params.epsilon_y_T + 0.001; // Slightly above yield strain
    const stress = calculateStressAtTemperature(strain, steelProps, params);
    
    // Should equal yield strength: F(T) = Fy(T)
    expect(stress).toBeCloseTo(steelProps.Fy_T);
  });

  test('calculateStressAtTemperature - nonlinear range', () => {
    const strain = (params.epsilon_p_T + params.epsilon_y_T) / 2; // Between proportional and yield
    const stress = calculateStressAtTemperature(strain, steelProps, params);
    
    // Should be between proportional limit and yield strength
    expect(stress).toBeGreaterThan(steelProps.Fp_T);
    expect(stress).toBeLessThan(steelProps.Fy_T);
  });

  test('calculateStressAtTemperature - input validation', () => {
    expect(() => calculateStressAtTemperature(-0.001, steelProps, params)).toThrow('Strain must be non-negative');
    expect(() => calculateStressAtTemperature(0.2, steelProps, params)).toThrow('exceeds ultimate strain');
  });

  test('calculateStressAtTemperature - stress progression', () => {
    // Test that stress increases monotonically with strain (except for plateau)
    const strains = [0, 0.001, 0.005, 0.01, 0.02, 0.03];
    const stresses = strains.map(strain => {
      try {
        return calculateStressAtTemperature(strain, steelProps, params);
      } catch {
        return NaN;
      }
    });
    
    for (let i = 1; i < stresses.length - 1; i++) {
      if (!isNaN(stresses[i]) && !isNaN(stresses[i-1])) {
        expect(stresses[i]).toBeGreaterThanOrEqual(stresses[i-1] * 0.99); // Allow for small numerical errors
      }
    }
  });
});

describe('Material Properties - Table Data Integrity', () => {
  test('STEEL_RETENTION_FACTORS - data consistency', () => {
    // Test that table is properly ordered by temperature
    for (let i = 1; i < STEEL_RETENTION_FACTORS.length; i++) {
      expect(STEEL_RETENTION_FACTORS[i].temp).toBeGreaterThan(STEEL_RETENTION_FACTORS[i-1].temp);
    }
    
    // Test that retention factors are non-increasing (properties degrade with temperature)
    for (let i = 1; i < STEEL_RETENTION_FACTORS.length; i++) {
      expect(STEEL_RETENTION_FACTORS[i].kE).toBeLessThanOrEqual(STEEL_RETENTION_FACTORS[i-1].kE);
      expect(STEEL_RETENTION_FACTORS[i].kp).toBeLessThanOrEqual(STEEL_RETENTION_FACTORS[i-1].kp);
      expect(STEEL_RETENTION_FACTORS[i].ky).toBeLessThanOrEqual(STEEL_RETENTION_FACTORS[i-1].ky);
    }
    
    // Test boundary values
    expect(STEEL_RETENTION_FACTORS[0].kE).toBe(1.00);
    expect(STEEL_RETENTION_FACTORS[0].kp).toBe(1.00);
    expect(STEEL_RETENTION_FACTORS[0].ky).toBe(1.00);
    
    expect(STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length-1].kE).toBe(0.00);
    expect(STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length-1].kp).toBe(0.00);
    expect(STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length-1].ky).toBe(0.00);
  });

  test('STRAIN_CONSTANTS - reasonable values', () => {
    expect(STRAIN_CONSTANTS.EPSILON_Y_T).toBe(0.02);
    expect(STRAIN_CONSTANTS.EPSILON_U_T).toBe(0.15);
    expect(STRAIN_CONSTANTS.EPSILON_U_T).toBeGreaterThan(STRAIN_CONSTANTS.EPSILON_Y_T);
  });
}); 