/**
 * @file Fire Design Types
 * 
 * Type definitions for AISC 360-22 Appendix 4 "Structural Design for Fire Conditions"
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4
 */

/**
 * Interface for fire scenario types per AISC A4.2.1
 */
export type FireScenarioType = 'localized' | 'post-flashover' | 'exterior' | 'active-protection';

/**
 * Interface for fire load combination parameters per AISC A4 Section 4
 */
export interface FireLoadCombination {
  /** Dead load, kips or kip-ft */
  D: number;
  /** Nominal forces and deformations due to design-basis fire, kips or kip-ft */
  AT: number;
  /** Live load, kips or kip-ft */
  L: number;
  /** Snow load, kips or kip-ft */
  S: number;
  /** Whether to use 0.9D or 1.2D factor */
  useMinimumDeadLoad: boolean;
}

/**
 * Interface for thermal expansion coefficients per AISC A4.3a
 */
export interface ThermalExpansionCoefficients {
  /** Coefficient for structural and reinforcing steels, /°F */
  steel: number;
  /** Coefficient for normal weight concrete, /°F */
  normalConcrete: number;
  /** Coefficient for lightweight concrete, /°F */
  lightweightConcrete: number;
}

/**
 * Interface for steel material properties at elevated temperature
 * per AISC A4.3b and Table A-4.2.1
 */
export interface SteelPropertiesAtTemperature {
  /** Temperature, °F */
  temperature: number;
  /** Elastic modulus retention factor kE = E(T)/E */
  kE: number;
  /** Proportional limit retention factor kp = Fp(T)/Fy */
  kp: number;
  /** Yield strength retention factor ky = Fy(T)/Fy */
  ky: number;
  /** Elastic modulus at temperature, ksi */
  E_T: number;
  /** Proportional limit at temperature, ksi */
  Fp_T: number;
  /** Yield strength at temperature, ksi */
  Fy_T: number;
  /** Tensile strength at temperature, ksi */
  Fu_T: number;
}

/**
 * Interface for stress-strain parameters per AISC A4 Equations A-4-5, A-4-6, A-4-7
 */
export interface StressStrainParameters {
  /** Engineering strain at elevated temperature, in/in */
  epsilon_T: number;
  /** Engineering strain at proportional limit, in/in */
  epsilon_p_T: number;
  /** Engineering yield strain at elevated temperature, in/in */
  epsilon_y_T: number;
  /** Ultimate strain at elevated temperature, in/in */
  epsilon_u_T: number;
  /** Parameter a² for nonlinear range calculation */
  a_squared: number;
  /** Parameter b² for nonlinear range calculation */
  b_squared: number;
  /** Parameter c for nonlinear range calculation */
  c: number;
}

/**
 * Interface for composite beam flexural retention factors per Table A-4.2.4
 */
export interface CompositeBeamRetentionFactor {
  /** Bottom flange temperature, °F */
  temperature: number;
  /** Retention factor kcb = Mn(T)/Mn */
  kcb: number;
}

/**
 * Interface for member geometry properties for fire design
 */
export interface FireDesignGeometry {
  /** Effective length of member, in. */
  Lc: number;
  /** Laterally unbraced length of member, in. */
  L: number;
  /** Radius of gyration, in. */
  r: number;
  /** Effective length factor (default 1.0 for gravity columns) */
  K?: number;
}

/**
 * Interface for column restraint conditions per AISC A-4-10
 */
export interface ColumnRestraintConditions {
  /** Number of cooler column stories (1 = both above and below, 2 = either above or below only) */
  n: number;
  /** Whether column provides resistance to lateral loads */
  providesLateralResistance: boolean;
}

/**
 * Interface for composite section properties
 */
export interface CompositeSection {
  /** Steel yield strength, ksi */
  Fy: number;
  /** Concrete compressive strength, ksi */
  fc_prime: number;
  /** Steel area, in² */
  As: number;
  /** Concrete area, in² */
  Ac: number;
  /** Steel section modulus, in³ */
  Sx: number;
  /** Moment of inertia of steel section, in⁴ */
  Ix: number;
  /** Section depth, in. */
  h: number;
}

/**
 * Interface for fire design validation results
 */
export interface FireDesignValidation {
  /** Whether the design is valid */
  isValid: boolean;
  /** Array of error messages */
  errors: string[];
  /** Array of warning messages */
  warnings: string[];
}

/**
 * Interface for comprehensive fire design analysis results
 */
export interface FireDesignAnalysis {
  /** Validation results */
  validation: FireDesignValidation;
  /** Material properties at elevated temperature */
  materialProperties: SteelPropertiesAtTemperature;
  /** Stress-strain parameters */
  stressStrainParams: StressStrainParameters;
  /** Required strength under fire conditions */
  requiredStrength?: number;
  /** Thermal expansion coefficient */
  thermalExpansionCoeff: number;
} 