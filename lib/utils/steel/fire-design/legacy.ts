/**
 * @file AISC Fire Design Module
 * 
 * Implements AISC 360-22 Appendix 4 "Structural Design for Fire Conditions"
 * including material property degradation at elevated temperatures, thermal
 * expansion, and fire load combinations.
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4
 */

import { DesignMethod } from "./constants";

/**
 * Interface for fire scenario types per AISC A4.2.1
 */
export type FireScenarioType = 'localized' | 'post-flashover' | 'exterior' | 'active-protection';

/**
 * Interface for fire load combination parameters per AISC A4 Section 4
 */
export interface FireLoadCombination {
  /** Dead load, kips or kip-ft */
  D: number;
  /** Nominal forces and deformations due to design-basis fire, kips or kip-ft */
  AT: number;
  /** Live load, kips or kip-ft */
  L: number;
  /** Snow load, kips or kip-ft */
  S: number;
  /** Whether to use 0.9D or 1.2D factor */
  useMinimumDeadLoad: boolean;
}

/**
 * Interface for thermal expansion coefficients per AISC A4.3a
 */
export interface ThermalExpansionCoefficients {
  /** Coefficient for structural and reinforcing steels, /°F */
  steel: number;
  /** Coefficient for normal weight concrete, /°F */
  normalConcrete: number;
  /** Coefficient for lightweight concrete, /°F */
  lightweightConcrete: number;
}

/**
 * Interface for steel material properties at elevated temperature
 * per AISC A4.3b and Table A-4.2.1
 */
export interface SteelPropertiesAtTemperature {
  /** Temperature, °F */
  temperature: number;
  /** Elastic modulus retention factor kE = E(T)/E */
  kE: number;
  /** Proportional limit retention factor kp = Fp(T)/Fy */
  kp: number;
  /** Yield strength retention factor ky = Fy(T)/Fy */
  ky: number;
  /** Elastic modulus at temperature, ksi */
  E_T: number;
  /** Proportional limit at temperature, ksi */
  Fp_T: number;
  /** Yield strength at temperature, ksi */
  Fy_T: number;
  /** Tensile strength at temperature, ksi */
  Fu_T: number;
}

/**
 * Interface for stress-strain parameters per AISC A4 Equations A-4-5, A-4-6, A-4-7
 */
export interface StressStrainParameters {
  /** Engineering strain at elevated temperature, in/in */
  epsilon_T: number;
  /** Engineering strain at proportional limit, in/in */
  epsilon_p_T: number;
  /** Engineering yield strain at elevated temperature, in/in */
  epsilon_y_T: number;
  /** Ultimate strain at elevated temperature, in/in */
  epsilon_u_T: number;
  /** Parameter a² for nonlinear range calculation */
  a_squared: number;
  /** Parameter b² for nonlinear range calculation */
  b_squared: number;
  /** Parameter c for nonlinear range calculation */
  c: number;
}

/**
 * Thermal expansion coefficients per AISC A4.3a
 * Valid for temperatures above 150°F (66°C)
 */
export const THERMAL_EXPANSION_COEFFICIENTS: ThermalExpansionCoefficients = {
  // For structural and reinforcing steels (T > 150°F)
  steel: 7.8e-6, // /°F (1.4e-5 /°C)
  
  // For normal weight concrete (T > 150°F)  
  normalConcrete: 10e-6, // /°F (1.8e-5 /°C)
  
  // For lightweight concrete (T > 150°F)
  lightweightConcrete: 4.4e-6, // /°F (7.9e-6 /°C)
};

/**
 * Steel material property retention factors per AISC Table A-4.2.1
 * Temperature ranges from 68°F to 2200°F
 */
export const STEEL_RETENTION_FACTORS = [
  { temp: 68, kE: 1.00, kp: 1.00, ky: 1.00 },
  { temp: 200, kE: 1.00, kp: 1.00, ky: 1.00 },
  { temp: 400, kE: 0.90, kp: 0.80, ky: 1.00 },
  { temp: 600, kE: 0.78, kp: 0.58, ky: 1.00 },
  { temp: 750, kE: 0.70, kp: 0.42, ky: 1.00 },
  { temp: 800, kE: 0.67, kp: 0.40, ky: 0.94 },
  { temp: 1000, kE: 0.49, kp: 0.29, ky: 0.66 },
  { temp: 1200, kE: 0.22, kp: 0.13, ky: 0.35 },
  { temp: 1400, kE: 0.11, kp: 0.06, ky: 0.16 },
  { temp: 1600, kE: 0.07, kp: 0.04, ky: 0.07 },
  { temp: 1800, kE: 0.05, kp: 0.03, ky: 0.04 },
  { temp: 2000, kE: 0.02, kp: 0.01, ky: 0.02 },
  { temp: 2200, kE: 0.00, kp: 0.00, ky: 0.00 },
] as const;

/**
 * Strain constants for stress-strain relationships per AISC A4.3b
 */
export const STRAIN_CONSTANTS = {
  /** Engineering yield strain at elevated temperature, in/in */
  EPSILON_Y_T: 0.02,
  /** Ultimate strain at elevated temperature, in/in */
  EPSILON_U_T: 0.15,
} as const;

/**
 * Calculates fire load combination per AISC A4 Section 4, Equation A-4-1
 * 
 * Load combination: (0.9 or 1.2)D + AT + 0.5L + 0.2S
 * 
 * @param loads - Fire load combination parameters
 * @returns Required strength under fire conditions
 * 
 * @source AISC 360-22, Appendix 4, Section 4, Equation A-4-1
 */
export function calculateFireLoadCombination(loads: FireLoadCombination): number {
  const deadLoadFactor = loads.useMinimumDeadLoad ? 0.9 : 1.2;
  
  const totalLoad = deadLoadFactor * loads.D + loads.AT + 0.5 * loads.L + 0.2 * loads.S;
  
  return totalLoad;
}

/**
 * Interpolates retention factors from AISC Table A-4.2.1 for a given temperature
 * 
 * @param temperature - Temperature in °F
 * @returns Interpolated retention factors
 * 
 * @source AISC 360-22, Table A-4.2.1
 */
export function interpolateRetentionFactors(temperature: number): {
  kE: number;
  kp: number;
  ky: number;
} {
  // Clamp temperature to table bounds
  if (temperature <= STEEL_RETENTION_FACTORS[0].temp) {
    const first = STEEL_RETENTION_FACTORS[0];
    return { kE: first.kE, kp: first.kp, ky: first.ky };
  }
  
  if (temperature >= STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length - 1].temp) {
    const last = STEEL_RETENTION_FACTORS[STEEL_RETENTION_FACTORS.length - 1];
    return { kE: last.kE, kp: last.kp, ky: last.ky };
  }
  
  // Find bounding temperatures
  let lowerIndex = 0;
  for (let i = 0; i < STEEL_RETENTION_FACTORS.length - 1; i++) {
    if (temperature >= STEEL_RETENTION_FACTORS[i].temp && 
        temperature <= STEEL_RETENTION_FACTORS[i + 1].temp) {
      lowerIndex = i;
      break;
    }
  }
  
  const lower = STEEL_RETENTION_FACTORS[lowerIndex];
  const upper = STEEL_RETENTION_FACTORS[lowerIndex + 1];
  
  // Linear interpolation
  const ratio = (temperature - lower.temp) / (upper.temp - lower.temp);
  
  return {
    kE: lower.kE + ratio * (upper.kE - lower.kE),
    kp: lower.kp + ratio * (upper.kp - lower.kp),
    ky: lower.ky + ratio * (upper.ky - lower.ky),
  };
}

/**
 * Calculates steel material properties at elevated temperature
 * per AISC A4.3b and Table A-4.2.1
 * 
 * @param temperature - Temperature in °F
 * @param Fy_ambient - Ambient yield strength, ksi
 * @param Fu_ambient - Ambient tensile strength, ksi
 * @param E_ambient - Ambient elastic modulus, ksi (defaults to 29000)
 * @returns Steel properties at elevated temperature
 * 
 * @source AISC 360-22, Appendix 4.3b, Table A-4.2.1
 */
export function calculateSteelPropertiesAtTemperature(
  temperature: number,
  Fy_ambient: number,
  Fu_ambient: number,
  E_ambient: number = 29000
): SteelPropertiesAtTemperature {
  if (Fy_ambient <= 0) {
    throw new Error("Ambient yield strength (Fy) must be positive");
  }
  if (Fu_ambient <= 0) {
    throw new Error("Ambient tensile strength (Fu) must be positive");
  }
  if (E_ambient <= 0) {
    throw new Error("Ambient elastic modulus (E) must be positive");
  }
  
  // Apply material limits per AISC A4.3b User Note
  if (Fy_ambient > 65) {
    throw new Error("AISC A4.3b applies only to steel with Fy ≤ 65 ksi");
  }
  
  const retentionFactors = interpolateRetentionFactors(temperature);
  
  // Calculate properties at temperature using retention factors
  const E_T = retentionFactors.kE * E_ambient;
  const Fp_T = retentionFactors.kp * Fy_ambient;
  const Fy_T = retentionFactors.ky * Fy_ambient;
  
  // For Fu(T): equal to Fy(T) for T > 750°F, otherwise Fu may be used
  const Fu_T = temperature > 750 ? Fy_T : Fu_ambient;
  
  return {
    temperature,
    kE: retentionFactors.kE,
    kp: retentionFactors.kp,
    ky: retentionFactors.ky,
    E_T,
    Fp_T,
    Fy_T,
    Fu_T,
  };
}

/**
 * Calculates thermal expansion/contraction for a given temperature change
 * per AISC A4.3a
 * 
 * @param initialLength - Initial length of member, inches
 * @param temperatureChange - Change in temperature from ambient, °F
 * @param materialType - Type of material
 * @returns Change in length due to thermal effects, inches
 * 
 * @source AISC 360-22, Appendix 4.3a
 */
export function calculateThermalExpansion(
  initialLength: number,
  temperatureChange: number,
  materialType: keyof ThermalExpansionCoefficients
): number {
  if (initialLength <= 0) {
    throw new Error("Initial length must be positive");
  }
  
  const coefficient = THERMAL_EXPANSION_COEFFICIENTS[materialType];
  const deltaLength = coefficient * initialLength * temperatureChange;
  
  return deltaLength;
}

/**
 * Calculates stress-strain parameters for elevated temperature analysis
 * per AISC A4 Equations A-4-5, A-4-6, A-4-7
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @returns Stress-strain parameters for analysis
 * 
 * @source AISC 360-22, Appendix 4, Equations A-4-5, A-4-6, A-4-7
 */
export function calculateStressStrainParameters(
  steelProps: SteelPropertiesAtTemperature
): StressStrainParameters {
  const { E_T, Fp_T, Fy_T } = steelProps;
  
  // Engineering strain at proportional limit per AISC A4.3b
  const epsilon_p_T = Fp_T / E_T;
  
  // Engineering strain constants
  const epsilon_y_T = STRAIN_CONSTANTS.EPSILON_Y_T;
  const epsilon_u_T = STRAIN_CONSTANTS.EPSILON_U_T;
  
  // Calculate parameters per AISC Equations A-4-5, A-4-6, A-4-7
  // A-4-7: c = [Fy(T) - Fp(T)]² / {E(T)[εy(T) - εp(T)] - 2[Fy(T) - Fp(T)]}
  const numerator = Math.pow(Fy_T - Fp_T, 2);
  const denominator = E_T * (epsilon_y_T - epsilon_p_T) - 2 * (Fy_T - Fp_T);
  const c = numerator / denominator;
  
  // A-4-5: a² = [εy(T) - εp(T)][εy(T) - εp(T) + c/E(T)]
  const term1 = epsilon_y_T - epsilon_p_T;
  const term2 = epsilon_y_T - epsilon_p_T + c / E_T;
  const a_squared = term1 * term2;
  
  // A-4-6: b² = E(T)[εy(T) - εp(T)]c + c²
  const b_squared = E_T * (epsilon_y_T - epsilon_p_T) * c + Math.pow(c, 2);
  
  return {
    epsilon_T: 0, // Will be determined based on stress level
    epsilon_p_T,
    epsilon_y_T,
    epsilon_u_T,
    a_squared,
    b_squared,
    c,
  };
}

/**
 * Calculates engineering stress at elevated temperature per AISC A4 Equations A-4-2, A-4-3, A-4-4
 * 
 * @param strain - Engineering strain, in/in
 * @param steelProps - Steel properties at elevated temperature
 * @param parameters - Stress-strain parameters
 * @returns Engineering stress at elevated temperature, ksi
 * 
 * @source AISC 360-22, Appendix 4, Equations A-4-2, A-4-3, A-4-4
 */
export function calculateStressAtTemperature(
  strain: number,
  steelProps: SteelPropertiesAtTemperature,
  parameters: StressStrainParameters
): number {
  const { E_T, Fp_T, Fy_T } = steelProps;
  const { epsilon_p_T, epsilon_y_T, epsilon_u_T, a_squared, b_squared, c } = parameters;
  
  if (strain < 0) {
    throw new Error("Strain must be non-negative");
  }
  
  // (1) Elastic range: ε(T) ≤ εp(T)
  if (strain <= epsilon_p_T) {
    // AISC Eq. A-4-2: F(T) = E(T)ε(T)
    return E_T * strain;
  }
  
  // (2) Nonlinear range: εp(T) < ε(T) < εy(T)
  else if (strain < epsilon_y_T) {
    // AISC Eq. A-4-3: F(T) = Fp(T) - c + (b/a)√[a² - [εy(T) - ε(T)]²]
    const term = a_squared - Math.pow(epsilon_y_T - strain, 2);
    if (term < 0) {
      throw new Error("Invalid stress-strain parameters: negative square root term");
    }
    const b_over_a = Math.sqrt(b_squared) / Math.sqrt(a_squared);
    return Fp_T - c + b_over_a * Math.sqrt(term);
  }
  
  // (3) Plastic range: εy(T) ≤ ε(T) ≤ εu(T)
  else if (strain <= epsilon_u_T) {
    // AISC Eq. A-4-4: F(T) = Fy(T)
    return Fy_T;
  }
  
  // Beyond ultimate strain
  else {
    throw new Error(`Strain ${strain.toFixed(4)} exceeds ultimate strain ${epsilon_u_T.toFixed(4)}`);
  }
}

/**
 * Validates fire design input parameters
 * 
 * @param temperature - Temperature in °F
 * @param Fy - Yield strength, ksi
 * @param designMethod - Design method (LRFD recommended for fire)
 * @returns Validation result with warnings
 * 
 * @source AISC 360-22, Appendix 4
 */
export function validateFireDesignInput(
  temperature: number,
  Fy: number,
  designMethod: DesignMethod
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Temperature validation
  if (temperature < 68) {
    errors.push("Temperature must be at least 68°F");
  }
  if (temperature > 2200) {
    warnings.push("Temperature exceeds table range (2200°F) - extrapolation used");
  }
  
  // Material validation per AISC A4.3b User Note
  if (Fy > 65) {
    errors.push("AISC A4.3b applies only to structural steel with Fy ≤ 65 ksi");
  }
  
  // Design method recommendation per AISC A4 Section 2
  if (designMethod !== DesignMethod.LRFD) {
    warnings.push("AISC A4 Section 2 requires LRFD method unless advanced analysis is performed per Section 4.2.4c");
  }
  
  // High temperature warnings
  if (temperature > 1000) {
    warnings.push("Severe strength degradation at temperatures above 1000°F");
  }
  
  if (temperature > 750) {
    warnings.push("Tensile strength Fu(T) = Fy(T) for temperatures above 750°F");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Provides comprehensive fire design analysis results
 * 
 * @param temperature - Temperature in °F
 * @param Fy_ambient - Ambient yield strength, ksi
 * @param Fu_ambient - Ambient tensile strength, ksi
 * @param designMethod - Design method
 * @param loadCombination - Fire load combination parameters
 * @returns Complete fire design analysis
 * 
 * @source AISC 360-22, Appendix 4
 */
export function performFireDesignAnalysis(
  temperature: number,
  Fy_ambient: number,
  Fu_ambient: number,
  designMethod: DesignMethod,
  loadCombination?: FireLoadCombination
): {
  validation: ReturnType<typeof validateFireDesignInput>;
  materialProperties: SteelPropertiesAtTemperature;
  stressStrainParams: StressStrainParameters;
  requiredStrength?: number;
  thermalExpansionCoeff: number;
} {
  const validation = validateFireDesignInput(temperature, Fy_ambient, designMethod);
  
  if (!validation.isValid) {
    throw new Error(`Fire design validation failed: ${validation.errors.join(', ')}`);
  }
  
  const materialProperties = calculateSteelPropertiesAtTemperature(
    temperature, 
    Fy_ambient, 
    Fu_ambient
  );
  
  const stressStrainParams = calculateStressStrainParameters(materialProperties);
  
  let requiredStrength: number | undefined;
  if (loadCombination) {
    requiredStrength = calculateFireLoadCombination(loadCombination);
  }
  
  return {
    validation,
    materialProperties,
    stressStrainParams,
    requiredStrength,
    thermalExpansionCoeff: THERMAL_EXPANSION_COEFFICIENTS.steel,
  };
} 