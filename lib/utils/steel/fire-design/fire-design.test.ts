/**
 * @file Fire Design Tests
 * 
 * Comprehensive test suite for AISC 360-22 Appendix 4 "Structural Design for Fire Conditions"
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 */

import { describe, it, expect } from "@jest/globals";
import {
  interpolateRetentionFactors,
  calculateSteelPropertiesAtTemperature,
  calculateStressStrainParameters,
  calculateStressAtTemperature,
  STEEL_RETENTION_FACTORS,
  STRAIN_CONSTANTS,
} from "./material-properties";
import { calculateFireLoadCombination } from "./load-combinations";
import { calculateThermalExpansion, THERMAL_EXPANSION_COEFFICIENTS } from "./thermal-expansion";
import { validateFireDesignInput, performFireDesignAnalysis } from "./validation";
import { FireLoadCombination, SteelPropertiesAtTemperature } from "./types";
import { DesignMethod } from "../constants";

describe("AISC Fire Design Module - Appendix 4", () => {
  describe("Fire Load Combinations (AISC A4 Section 4, Equation A-4-1)", () => {
    it("should calculate load combination with minimum dead load factor", () => {
      const loads: FireLoadCombination = {
        D: 100, // kips
        AT: 50, // kips
        L: 80, // kips
        S: 30, // kips
        useMinimumDeadLoad: true, // Use 0.9D
      };

      const result = calculateFireLoadCombination(loads);

      // (0.9)D + AT + 0.5L + 0.2S = 0.9(100) + 50 + 0.5(80) + 0.2(30)
      // = 90 + 50 + 40 + 6 = 186 kips
      expect(result).toBeCloseTo(186, 3);
    });

    it("should calculate load combination with maximum dead load factor", () => {
      const loads: FireLoadCombination = {
        D: 100, // kips
        AT: 50, // kips
        L: 80, // kips
        S: 30, // kips
        useMinimumDeadLoad: false, // Use 1.2D
      };

      const result = calculateFireLoadCombination(loads);

      // (1.2)D + AT + 0.5L + 0.2S = 1.2(100) + 50 + 0.5(80) + 0.2(30)
      // = 120 + 50 + 40 + 6 = 216 kips
      expect(result).toBeCloseTo(216, 3);
    });

    it("should handle zero fire load", () => {
      const loads: FireLoadCombination = {
        D: 100,
        AT: 0, // No fire load
        L: 80,
        S: 30,
        useMinimumDeadLoad: true,
      };

      const result = calculateFireLoadCombination(loads);

      // 0.9(100) + 0 + 0.5(80) + 0.2(30) = 90 + 0 + 40 + 6 = 136 kips
      expect(result).toBeCloseTo(136, 3);
    });

    it("should handle all zero loads except fire", () => {
      const loads: FireLoadCombination = {
        D: 0,
        AT: 75, // Only fire load
        L: 0,
        S: 0,
        useMinimumDeadLoad: true,
      };

      const result = calculateFireLoadCombination(loads);

      expect(result).toBeCloseTo(75, 3);
    });
  });

  describe("Retention Factor Interpolation (AISC Table A-4.2.1)", () => {
    it("should return exact values for table temperatures", () => {
      // Test exact table values
      const result600 = interpolateRetentionFactors(600);
      expect(result600.kE).toBeCloseTo(0.78, 3);
      expect(result600.kp).toBeCloseTo(0.58, 3);
      expect(result600.ky).toBeCloseTo(1.00, 3);

      const result1000 = interpolateRetentionFactors(1000);
      expect(result1000.kE).toBeCloseTo(0.49, 3);
      expect(result1000.kp).toBeCloseTo(0.29, 3);
      expect(result1000.ky).toBeCloseTo(0.66, 3);
    });

    it("should interpolate between table values correctly", () => {
      // Test interpolation between 600°F and 750°F
      const result = interpolateRetentionFactors(675); // Midpoint

      // 600°F: kE=0.78, kp=0.58, ky=1.00
      // 750°F: kE=0.70, kp=0.42, ky=1.00
      // Midpoint: kE=(0.78+0.70)/2=0.74, kp=(0.58+0.42)/2=0.50, ky=1.00

      expect(result.kE).toBeCloseTo(0.74, 3);
      expect(result.kp).toBeCloseTo(0.50, 3);
      expect(result.ky).toBeCloseTo(1.00, 3);
    });

    it("should handle temperatures below table range", () => {
      const result = interpolateRetentionFactors(50); // Below 68°F

      // Should return values for 68°F
      expect(result.kE).toBeCloseTo(1.00, 3);
      expect(result.kp).toBeCloseTo(1.00, 3);
      expect(result.ky).toBeCloseTo(1.00, 3);
    });

    it("should handle temperatures above table range", () => {
      const result = interpolateRetentionFactors(2500); // Above 2200°F

      // Should return values for 2200°F
      expect(result.kE).toBeCloseTo(0.00, 3);
      expect(result.kp).toBeCloseTo(0.00, 3);
      expect(result.ky).toBeCloseTo(0.00, 3);
    });

    it("should handle complex interpolation case", () => {
      // Test interpolation between 800°F and 1000°F for temperature 900°F
      const result = interpolateRetentionFactors(900);

      // 800°F: kE=0.67, kp=0.40, ky=0.94
      // 1000°F: kE=0.49, kp=0.29, ky=0.66
      // 900°F is midpoint: kE=(0.67+0.49)/2=0.58, kp=(0.40+0.29)/2=0.345, ky=(0.94+0.66)/2=0.80

      expect(result.kE).toBeCloseTo(0.58, 3);
      expect(result.kp).toBeCloseTo(0.345, 3);
      expect(result.ky).toBeCloseTo(0.80, 3);
    });
  });

  describe("Steel Properties at Elevated Temperature (AISC A4.3b)", () => {
    it("should calculate properties at ambient temperature", () => {
      const result = calculateSteelPropertiesAtTemperature(68, 50, 65);

      expect(result.temperature).toBe(68);
      expect(result.kE).toBeCloseTo(1.00, 3);
      expect(result.kp).toBeCloseTo(1.00, 3);
      expect(result.ky).toBeCloseTo(1.00, 3);
      expect(result.E_T).toBeCloseTo(29000, 1);
      expect(result.Fp_T).toBeCloseTo(50, 1); // kp * Fy = 1.0 * 50
      expect(result.Fy_T).toBeCloseTo(50, 1); // ky * Fy = 1.0 * 50
      expect(result.Fu_T).toBeCloseTo(65, 1); // T ≤ 750°F, so Fu_T = Fu_ambient
    });

    it("should calculate properties at moderate temperature (600°F)", () => {
      const result = calculateSteelPropertiesAtTemperature(600, 50, 65);

      expect(result.temperature).toBe(600);
      expect(result.kE).toBeCloseTo(0.78, 3);
      expect(result.kp).toBeCloseTo(0.58, 3);
      expect(result.ky).toBeCloseTo(1.00, 3);
      expect(result.E_T).toBeCloseTo(0.78 * 29000, 1); // 22620 ksi
      expect(result.Fp_T).toBeCloseTo(0.58 * 50, 1); // 29 ksi
      expect(result.Fy_T).toBeCloseTo(1.00 * 50, 1); // 50 ksi
      expect(result.Fu_T).toBeCloseTo(65, 1); // T ≤ 750°F, so Fu_T = Fu_ambient
    });

    it("should calculate properties at high temperature (1000°F)", () => {
      const result = calculateSteelPropertiesAtTemperature(1000, 50, 65);

      expect(result.temperature).toBe(1000);
      expect(result.kE).toBeCloseTo(0.49, 3);
      expect(result.kp).toBeCloseTo(0.29, 3);
      expect(result.ky).toBeCloseTo(0.66, 3);
      expect(result.E_T).toBeCloseTo(0.49 * 29000, 1); // 14210 ksi
      expect(result.Fp_T).toBeCloseTo(0.29 * 50, 1); // 14.5 ksi
      expect(result.Fy_T).toBeCloseTo(0.66 * 50, 1); // 33 ksi
      expect(result.Fu_T).toBeCloseTo(33, 1); // T > 750°F, so Fu_T = Fy_T
    });

    it("should handle Fu(T) = Fy(T) for temperatures above 750°F", () => {
      const result = calculateSteelPropertiesAtTemperature(800, 50, 65);

      // At 800°F, ky = 0.94, so Fy_T = 0.94 * 50 = 47 ksi
      expect(result.Fy_T).toBeCloseTo(47, 1);
      expect(result.Fu_T).toBeCloseTo(47, 1); // Fu_T = Fy_T for T > 750°F
    });

    it("should validate input parameters", () => {
      expect(() => calculateSteelPropertiesAtTemperature(600, -50, 65)).toThrow(
        "Ambient yield strength (Fy) must be positive"
      );
      expect(() => calculateSteelPropertiesAtTemperature(600, 50, -65)).toThrow(
        "Ambient tensile strength (Fu) must be positive"
      );
      expect(() => calculateSteelPropertiesAtTemperature(600, 50, 65, -29000)).toThrow(
        "Ambient elastic modulus (E) must be positive"
      );
    });

    it("should enforce material limits per AISC A4.3b User Note", () => {
      expect(() => calculateSteelPropertiesAtTemperature(600, 70, 80)).toThrow(
        "AISC A4.3b applies only to steel with Fy ≤ 65 ksi"
      );
    });

    it("should use custom elastic modulus", () => {
      const result = calculateSteelPropertiesAtTemperature(600, 50, 65, 30000);

      expect(result.E_T).toBeCloseTo(0.78 * 30000, 1); // 23400 ksi
    });
  });

  describe("Thermal Expansion (AISC A4.3a)", () => {
    it("should calculate steel thermal expansion", () => {
      const initialLength = 240; // 20 ft beam in inches
      const tempChange = 500; // °F temperature rise
      
      const expansion = calculateThermalExpansion(initialLength, tempChange, 'steel');

      // ΔL = α * L * ΔT = 7.8e-6 * 240 * 500 = 0.936 inches
      const expected = THERMAL_EXPANSION_COEFFICIENTS.steel * initialLength * tempChange;
      expect(expansion).toBeCloseTo(expected, 6);
      expect(expansion).toBeCloseTo(0.936, 3);
    });

    it("should calculate normal concrete thermal expansion", () => {
      const initialLength = 144; // 12 ft
      const tempChange = 300; // °F
      
      const expansion = calculateThermalExpansion(initialLength, tempChange, 'normalConcrete');

      // ΔL = α * L * ΔT = 10e-6 * 144 * 300 = 0.432 inches
      const expected = THERMAL_EXPANSION_COEFFICIENTS.normalConcrete * initialLength * tempChange;
      expect(expansion).toBeCloseTo(expected, 6);
      expect(expansion).toBeCloseTo(0.432, 3);
    });

    it("should calculate lightweight concrete thermal expansion", () => {
      const initialLength = 120; // 10 ft
      const tempChange = 400; // °F
      
      const expansion = calculateThermalExpansion(initialLength, tempChange, 'lightweightConcrete');

      // ΔL = α * L * ΔT = 4.4e-6 * 120 * 400 = 0.2112 inches
      const expected = THERMAL_EXPANSION_COEFFICIENTS.lightweightConcrete * initialLength * tempChange;
      expect(expansion).toBeCloseTo(expected, 6);
      expect(expansion).toBeCloseTo(0.2112, 4);
    });

    it("should handle thermal contraction (negative temperature change)", () => {
      const initialLength = 180;
      const tempChange = -200; // Temperature drop
      
      const expansion = calculateThermalExpansion(initialLength, tempChange, 'steel');

      // Should be negative (contraction)
      expect(expansion).toBeLessThan(0);
      expect(expansion).toBeCloseTo(-0.2808, 4);
    });

    it("should validate input parameters", () => {
      expect(() => calculateThermalExpansion(-100, 500, 'steel')).toThrow(
        "Initial length must be positive"
      );
    });

    it("should handle zero temperature change", () => {
      const expansion = calculateThermalExpansion(240, 0, 'steel');
      expect(expansion).toBe(0);
    });
  });

  describe("Stress-Strain Parameters (AISC A4 Equations A-4-5, A-4-6, A-4-7)", () => {
    it("should calculate parameters for moderate temperature", () => {
      const steelProps = calculateSteelPropertiesAtTemperature(600, 50, 65);
      const params = calculateStressStrainParameters(steelProps);

      // E_T = 22620 ksi, Fp_T = 29 ksi, Fy_T = 50 ksi
      // εp(T) = Fp(T)/E(T) = 29/22620 = 0.001283
      expect(params.epsilon_p_T).toBeCloseTo(29 / 22620, 6);
      expect(params.epsilon_y_T).toBe(STRAIN_CONSTANTS.EPSILON_Y_T);
      expect(params.epsilon_u_T).toBe(STRAIN_CONSTANTS.EPSILON_U_T);

      // Parameters should be positive and finite
      expect(params.a_squared).toBeGreaterThan(0);
      expect(params.b_squared).toBeGreaterThan(0);
      expect(params.c).toBeGreaterThan(0);
      expect(isFinite(params.a_squared)).toBe(true);
      expect(isFinite(params.b_squared)).toBe(true);
      expect(isFinite(params.c)).toBe(true);
    });

    it("should calculate parameters for high temperature", () => {
      const steelProps = calculateSteelPropertiesAtTemperature(1000, 50, 65);
      const params = calculateStressStrainParameters(steelProps);

      // E_T = 14210 ksi, Fp_T = 14.5 ksi, Fy_T = 33 ksi
      expect(params.epsilon_p_T).toBeCloseTo(14.5 / 14210, 6);

      // All parameters should be well-defined
      expect(params.a_squared).toBeGreaterThan(0);
      expect(params.b_squared).toBeGreaterThan(0);
      expect(params.c).toBeGreaterThan(0);
    });

    it("should handle ambient temperature case", () => {
      const steelProps = calculateSteelPropertiesAtTemperature(68, 50, 65);
      const params = calculateStressStrainParameters(steelProps);

      // At ambient temperature, Fp_T = Fy_T = 50 ksi
      // This creates a special case where Fy_T - Fp_T = 0
      expect(params.epsilon_p_T).toBeCloseTo(50 / 29000, 6);
      
      // When Fp_T = Fy_T, c should be 0
      expect(params.c).toBeCloseTo(0, 6);
    });
  });

  describe("Integration Tests", () => {
    it("should verify AISC Table A-4.2.1 values exactly", () => {
      // Test all table values to ensure exact compliance
      const tableTests = [
        { temp: 68, kE: 1.00, kp: 1.00, ky: 1.00 },
        { temp: 400, kE: 0.90, kp: 0.80, ky: 1.00 },
        { temp: 800, kE: 0.67, kp: 0.40, ky: 0.94 },
        { temp: 1200, kE: 0.22, kp: 0.13, ky: 0.35 },
        { temp: 2000, kE: 0.02, kp: 0.01, ky: 0.02 },
      ];

      tableTests.forEach(test => {
        const factors = interpolateRetentionFactors(test.temp);
        expect(factors.kE).toBeCloseTo(test.kE, 3);
        expect(factors.kp).toBeCloseTo(test.kp, 3);
        expect(factors.ky).toBeCloseTo(test.ky, 3);
      });
    });

    it("should verify thermal expansion coefficients match AISC A4.3a", () => {
      // Verify coefficients match specification exactly
      expect(THERMAL_EXPANSION_COEFFICIENTS.steel).toBeCloseTo(7.8e-6, 8);
      expect(THERMAL_EXPANSION_COEFFICIENTS.normalConcrete).toBeCloseTo(10e-6, 8);
      expect(THERMAL_EXPANSION_COEFFICIENTS.lightweightConcrete).toBeCloseTo(4.4e-6, 8);

      // Verify metric equivalents (from specification comments)
      expect(THERMAL_EXPANSION_COEFFICIENTS.steel * 1.8).toBeCloseTo(1.4e-5, 6); // /°C
      expect(THERMAL_EXPANSION_COEFFICIENTS.normalConcrete * 1.8).toBeCloseTo(1.8e-5, 6); // /°C
      expect(THERMAL_EXPANSION_COEFFICIENTS.lightweightConcrete * 1.8).toBeCloseTo(7.9e-6, 6); // /°C
    });

    it("should demonstrate complete fire design workflow", () => {
      // Scenario: W18x50 beam at 1000°F with fire loading
      const temperature = 1000; // °F
      const Fy_ambient = 50; // ksi (A992 steel)
      const Fu_ambient = 65; // ksi
      
      const loadCombination: FireLoadCombination = {
        D: 120, // Dead load, kips
        AT: 80, // Fire-induced forces, kips
        L: 100, // Live load, kips
        S: 30, // Snow load, kips
        useMinimumDeadLoad: false, // Use 1.2D for maximum case
      };

      // Step 1: Perform fire design analysis
      const analysis = performFireDesignAnalysis(
        temperature, 
        Fy_ambient, 
        Fu_ambient, 
        DesignMethod.LRFD, 
        loadCombination
      );

      // Step 2: Verify material property degradation
      expect(analysis.materialProperties.Fy_T).toBeCloseTo(33, 1); // 66% of ambient
      expect(analysis.materialProperties.E_T).toBeCloseTo(14210, 1); // 49% of ambient

      // Step 3: Verify load combination
      // 1.2(120) + 80 + 0.5(100) + 0.2(30) = 144 + 80 + 50 + 6 = 280 kips
      expect(analysis.requiredStrength).toBeCloseTo(280, 1);

      // Step 4: Calculate thermal expansion for 20 ft beam
      const beamLength = 20 * 12; // 240 inches
      const tempRise = temperature - 68; // Temperature rise from ambient
      const expansion = calculateThermalExpansion(beamLength, tempRise, 'steel');
      
      // ΔL = 7.8e-6 * 240 * 932 = 1.745 inches
      expect(expansion).toBeCloseTo(1.745, 3);
    });
  });
});
