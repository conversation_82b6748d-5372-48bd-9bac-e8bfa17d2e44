/**
 * @file Fire Design Equations
 * 
 * Design equation implementations for AISC 360-22 Appendix 4
 * "Structural Design for Fire Conditions" - Section 4d
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4.4d
 */

import { 
  SteelPropertiesAtTemperature, 
  FireDesignGeometry, 
  ColumnRestraintConditions,
  CompositeSection,
  CompositeBeamRetentionFactor
} from './types';

/**
 * Composite beam flexural retention factors per AISC Table A-4.2.4
 */
export const COMPOSITE_BEAM_RETENTION_FACTORS: CompositeBeamRetentionFactor[] = [
  { temperature: 68, kcb: 1.00 },
  { temperature: 300, kcb: 0.98 },
  { temperature: 600, kcb: 0.95 },
  { temperature: 800, kcb: 0.89 },
  { temperature: 1000, kcb: 0.71 },
  { temperature: 1200, kcb: 0.49 },
  { temperature: 1400, kcb: 0.26 },
  { temperature: 1600, kcb: 0.12 },
  { temperature: 1800, kcb: 0.05 },
  { temperature: 2000, kcb: 0.00 },
];

/**
 * Calculates nominal tensile strength at elevated temperature per AISC A4.4d(a)
 * 
 * Uses provisions of Chapter D with steel properties per Section 4.2.3b(a)
 * assuming uniform temperature over cross section.
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param Ag - Gross area of member, in²
 * @param An - Net area of member, in²
 * @returns Nominal tensile strength at temperature, kips
 * 
 * @source AISC 360-22, Appendix 4.4d(a)
 */
export function calculateTensileStrengthAtTemperature(
  steelProps: SteelPropertiesAtTemperature,
  Ag: number,
  An: number
): number {
  if (Ag <= 0) {
    throw new Error("Gross area must be positive");
  }
  if (An <= 0) {
    throw new Error("Net area must be positive");
  }
  if (An > Ag) {
    throw new Error("Net area cannot exceed gross area");
  }
  
  const { Fy_T, Fu_T } = steelProps;
  
  // Chapter D: Tn = min(Fy*Ag, Fu*An)
  const Tn_yielding = Fy_T * Ag;
  const Tn_rupture = Fu_T * An;
  
  return Math.min(Tn_yielding, Tn_rupture);
}

/**
 * Calculates nominal compressive strength for nonslender columns at elevated temperature
 * per AISC A4.4d(b), Equation A-4-9
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param geometry - Member geometry properties
 * @param restraints - Column restraint conditions (optional)
 * @returns Nominal compressive strength at temperature, kips
 * 
 * @source AISC 360-22, Appendix 4.4d(b), Equation A-4-9
 */
export function calculateCompressionStrengthAtTemperature(
  steelProps: SteelPropertiesAtTemperature,
  geometry: FireDesignGeometry,
  restraints?: ColumnRestraintConditions
): number {
  const { Fy_T, E_T } = steelProps;
  const { Lc, r, K = 1.0 } = geometry;
  
  if (Lc <= 0) {
    throw new Error("Effective length must be positive");
  }
  if (r <= 0) {
    throw new Error("Radius of gyration must be positive");
  }
  
  // Calculate effective length considering fire conditions
  let Lc_effective = Lc;
  if (restraints && !restraints.providesLateralResistance) {
    // Apply Equations A-4-10 or A-4-10M for gravity columns
    Lc_effective = calculateEffectiveLengthForGravityColumns(
      steelProps.temperature,
      Lc,
      r,
      restraints.n
    );
  }
  
  // Calculate Fe per Equation E3-4 with elevated temperature properties
  const Fe = Math.PI * Math.PI * E_T / Math.pow(Lc_effective / r, 2);
  
  // AISC Equation A-4-9: Fn(T) = [0.42 * sqrt(Fy(T)/Fe(T))] * Fy(T)
  const lambda = Math.sqrt(Fy_T / Fe);
  const Fn_T = 0.42 * lambda * Fy_T;
  
  return Fn_T;
}

/**
 * Calculates effective length modification for gravity columns per AISC A-4-10/A-4-10M
 * 
 * @param temperature - Steel temperature, °F
 * @param Lc - Original effective length, in.
 * @param r - Radius of gyration, in.
 * @param n - Restraint factor (1 = cooler columns above and below, 2 = either above or below only)
 * @returns Modified effective length, in.
 * 
 * @source AISC 360-22, Appendix 4.4d(b), Equations A-4-10, A-4-10M
 */
export function calculateEffectiveLengthForGravityColumns(
  temperature: number,
  Lc: number,
  r: number,
  n: number
): number {
  if (n !== 1 && n !== 2) {
    throw new Error("Restraint factor n must be 1 or 2");
  }
  
  // AISC Equation A-4-10 (Imperial): Lc(T)/r = [1 - (T-32)/(n*3600)] * Lc/r - 35/(n*3600) * (T-32) ≥ 0
  const term1 = (1 - (temperature - 32) / (n * 3600)) * (Lc / r);
  const term2 = 35 / (n * 3600) * (temperature - 32);
  const slenderness = Math.max(0, term1 - term2);
  
  return slenderness * r;
}

/**
 * Calculates nominal compressive strength for filled composite columns per AISC A-4-11
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param composite - Composite section properties
 * @returns Nominal compressive strength, kips
 * 
 * @source AISC 360-22, Appendix 4.4d(c), Equation A-4-11
 */
export function calculateCompositeColumnStrength(
  steelProps: SteelPropertiesAtTemperature,
  composite: CompositeSection
): number {
  const { Fy_T } = steelProps;
  
  // Calculate Pno(T) using Equations I2-9, I2-10, I2-11 at elevated temperature
  // This is a simplified implementation - full composite design requires more parameters
  const Pno_T = Fy_T * composite.As + 0.85 * composite.fc_prime * composite.Ac;
  
  // Calculate Pe(T) using Equation I2-4 at elevated temperature  
  const Pe_T = Math.PI * Math.PI * steelProps.E_T * composite.Ix / Math.pow(composite.h, 2);
  
  // AISC Equation A-4-11: Pn(T) = {0.54 * [Pno(T)/Pe(T)]^0.3} * Pno(T)
  const ratio = Math.pow(Pno_T / Pe_T, 0.3);
  const Pn_T = 0.54 * ratio * Pno_T;
  
  return Pn_T;
}

/**
 * Calculates nominal compressive strength for filled composite plate shear walls per AISC A-4-12
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param composite - Composite section properties
 * @returns Nominal compressive strength, kips
 * 
 * @source AISC 360-22, Appendix 4.4d(d), Equation A-4-12
 */
export function calculateCompositeShearWallStrength(
  steelProps: SteelPropertiesAtTemperature,
  composite: CompositeSection
): number {
  const { Fy_T } = steelProps;
  
  // Similar to A-4-11 but with different factor
  const Pno_T = Fy_T * composite.As + 0.85 * composite.fc_prime * composite.Ac;
  const Pe_T = Math.PI * Math.PI * steelProps.E_T * composite.Ix / Math.pow(composite.h, 2);
  
  // AISC Equation A-4-12: Pn(T) = {0.32 * [Pno(T)/Pe(T)]^0.3} * Pno(T)
  const ratio = Math.pow(Pno_T / Pe_T, 0.3);
  const Pn_T = 0.32 * ratio * Pno_T;
  
  return Pn_T;
}

/**
 * Calculates lateral-torsional buckling parameters at elevated temperature per AISC A-4-15, A-4-16
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param Jc - Torsional constant, in⁴
 * @param Lb - Unbraced length, in.
 * @param Sx - Section modulus, in³
 * @param ho - Distance between flange centroids, in.
 * @param rts - Effective radius of gyration, in.
 * @returns Object with Fcr(T) and Lr(T)
 * 
 * @source AISC 360-22, Appendix 4.4d(e), Equations A-4-15, A-4-16
 */
export function calculateLateralTorsionalBucklingParameters(
  steelProps: SteelPropertiesAtTemperature,
  Jc: number,
  Lb: number,
  Sx: number,
  ho: number,
  rts: number
): { Fcr_T: number; Lr_T: number } {
  const { E_T, Fy_T, temperature } = steelProps;
  
  // Calculate FL(T) per A-4-17: FL(T) = Fy(kp - 0.3ky)
  const kp = steelProps.kp;
  const ky = steelProps.ky;
  const FL_T = Fy_T * (kp - 0.3 * ky);
  
  // Calculate cx per A-4-19/A-4-19M
  const cx = temperature <= 2500 ? // Using °F, convert if needed
    0.53 + temperature / 450 :
    3.0;
  
  // AISC Equation A-4-15: Fcr(T) = (Cb*π²*E(T)/(Lb/rts)²) * sqrt[1 + 0.078*(Jc/Sx*ho)*(Lb/rts)²]
  const term1 = Math.PI * Math.PI * E_T / Math.pow(Lb / rts, 2);
  const term2 = Math.sqrt(1 + 0.078 * (Jc / (Sx * ho)) * Math.pow(Lb / rts, 2));
  const Fcr_T = 1.0 * term1 * term2; // Cb = 1.0 for uniform moment
  
  // AISC Equation A-4-16: Lr(T) = 1.95*rts * sqrt(E(T)/FL(T)) * sqrt[Jc/Sx*ho + sqrt((Jc/Sx*ho)² + 6.76*(FL(T)/E(T))²)]
  const term3 = Math.sqrt(E_T / FL_T);
  const term4 = Jc / (Sx * ho);
  const term5 = Math.sqrt(term4 * term4 + 6.76 * Math.pow(FL_T / E_T, 2));
  const Lr_T = 1.95 * rts * term3 * Math.sqrt(term4 + term5);
  
  return { Fcr_T, Lr_T };
}

/**
 * Calculates nominal flexural strength at elevated temperature per AISC A-4-13, A-4-14
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param Sx - Section modulus, in³
 * @param Zx - Plastic section modulus, in³
 * @param Lb - Unbraced length, in.
 * @param buckling - Lateral-torsional buckling parameters
 * @returns Nominal flexural strength, kip-in
 * 
 * @source AISC 360-22, Appendix 4.4d(e), Equations A-4-13, A-4-14
 */
export function calculateFlexuralStrengthAtTemperature(
  steelProps: SteelPropertiesAtTemperature,
  Sx: number,
  Zx: number,
  Lb: number,
  buckling: { Fcr_T: number; Lr_T: number }
): number {
  const { Fy_T } = steelProps;
  const { Fcr_T, Lr_T } = buckling;
  
  // Calculate Mp(T) per A-4-18
  const Mp_T = Fy_T * Zx;
  
  if (Lb <= Lr_T) {
    // AISC Equation A-4-13: When Lb ≤ Lr(T)
    const FL_T = Fy_T * (steelProps.kp - 0.3 * steelProps.ky); // From A-4-17
    const cx = 0.53 + steelProps.temperature / 450; // Simplified for imperial units
    
    const term1 = FL_T * Sx;
    const term2 = Mp_T - FL_T * Sx;
    const term3 = Math.pow(1 - Lb / Lr_T, cx);
    
    const Mn_T = term1 + term2 * term3;
    return Math.min(Mn_T, Mp_T);
  } else {
    // AISC Equation A-4-14: When Lb > Lr(T)
    const Mn_T = Fcr_T * Sx;
    return Math.min(Mn_T, Mp_T);
  }
}

/**
 * Interpolates composite beam retention factor from Table A-4.2.4
 * 
 * @param temperature - Bottom flange temperature, °F
 * @returns Retention factor kcb
 * 
 * @source AISC 360-22, Table A-4.2.4
 */
export function interpolateCompositeBeamRetentionFactor(temperature: number): number {
  const factors = COMPOSITE_BEAM_RETENTION_FACTORS;
  
  // Clamp to bounds
  if (temperature <= factors[0].temperature) {
    return factors[0].kcb;
  }
  if (temperature >= factors[factors.length - 1].temperature) {
    return factors[factors.length - 1].kcb;
  }
  
  // Find bounding temperatures
  for (let i = 0; i < factors.length - 1; i++) {
    if (temperature >= factors[i].temperature && temperature <= factors[i + 1].temperature) {
      const lower = factors[i];
      const upper = factors[i + 1];
      const ratio = (temperature - lower.temperature) / (upper.temperature - lower.temperature);
      return lower.kcb + ratio * (upper.kcb - lower.kcb);
    }
  }
  
  return factors[0].kcb; // Fallback
}

/**
 * Calculates nominal flexural strength for composite beams per AISC A-4-20
 * 
 * @param Mn_ambient - Nominal flexural strength at ambient temperature, kip-in
 * @param bottomFlangeTemperature - Bottom flange temperature, °F
 * @returns Nominal flexural strength at temperature, kip-in
 * 
 * @source AISC 360-22, Appendix 4.4d(f), Equation A-4-20
 */
export function calculateCompositeBeamFlexuralStrength(
  Mn_ambient: number,
  bottomFlangeTemperature: number
): number {
  if (Mn_ambient <= 0) {
    throw new Error("Ambient flexural strength must be positive");
  }
  
  const kcb = interpolateCompositeBeamRetentionFactor(bottomFlangeTemperature);
  
  // AISC Equation A-4-20: Mn(T) = kcb * Mn
  const Mn_T = kcb * Mn_ambient;
  
  return Mn_T;
}

/**
 * Calculates nominal shear strength at elevated temperature per AISC A4.4d(g)
 * 
 * Uses provisions of Chapter G with steel properties per Section 4.2.3b(a)
 * assuming uniform temperature over cross section.
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param Aw - Web area, in²
 * @param Cv - Web shear coefficient (from ambient temperature analysis)
 * @returns Nominal shear strength at temperature, kips
 * 
 * @source AISC 360-22, Appendix 4.4d(g)
 */
export function calculateShearStrengthAtTemperature(
  steelProps: SteelPropertiesAtTemperature,
  Aw: number,
  Cv: number = 1.0
): number {
  if (Aw <= 0) {
    throw new Error("Web area must be positive");
  }
  if (Cv < 0 || Cv > 1) {
    throw new Error("Web shear coefficient must be between 0 and 1");
  }
  
  const { Fy_T } = steelProps;
  
  // Chapter G: Vn = 0.6 * Fy * Aw * Cv
  const Vn_T = 0.6 * Fy_T * Aw * Cv;
  
  return Vn_T;
}

/**
 * Validates fire design equation inputs
 * 
 * @param steelProps - Steel properties at elevated temperature
 * @param memberType - Type of member ('tension' | 'compression' | 'flexure' | 'shear')
 * @returns Validation result
 * 
 * @source AISC 360-22, Appendix 4.4d
 */
export function validateFireDesignEquationInputs(
  steelProps: SteelPropertiesAtTemperature,
  memberType: 'tension' | 'compression' | 'flexure' | 'shear'
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Basic steel properties validation
  if (steelProps.Fy_T <= 0) {
    errors.push("Yield strength at temperature must be positive");
  }
  if (steelProps.E_T <= 0) {
    errors.push("Elastic modulus at temperature must be positive");
  }
  
  // Temperature range checks
  if (steelProps.temperature < 400) {
    warnings.push("AISC A4.4d simple methods assume T ≥ 400°F for temperature effects");
  }
  
  // Member-specific warnings
  if (memberType === 'compression' && steelProps.temperature > 1000) {
    warnings.push("High compression strength degradation at temperatures above 1000°F");
  }
  
  if (memberType === 'flexure' && steelProps.temperature > 1200) {
    warnings.push("Consider nonuniform heating effects for flexural members at high temperatures");
  }
  
  // Nonslender section assumption
  if (memberType === 'compression' || memberType === 'flexure') {
    warnings.push("AISC A4.4d simple method applies only to nonslender and/or compact sections");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
} 