/**
 * @file Fire Design Thermal Expansion
 * 
 * Thermal expansion calculations for AISC 360-22 Appendix 4
 * "Structural Design for Fire Conditions"
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4.3a
 */

import { ThermalExpansionCoefficients } from './types';

/**
 * Thermal expansion coefficients per AISC A4.3a
 * Valid for temperatures above 150°F (66°C)
 */
export const THERMAL_EXPANSION_COEFFICIENTS: ThermalExpansionCoefficients = {
  // For structural and reinforcing steels (T > 150°F)
  steel: 7.8e-6, // /°F (1.4e-5 /°C)
  
  // For normal weight concrete (T > 150°F)  
  normalConcrete: 10e-6, // /°F (1.8e-5 /°C)
  
  // For lightweight concrete (T > 150°F)
  lightweightConcrete: 4.4e-6, // /°F (7.9e-6 /°C)
};

/**
 * Calculates thermal expansion/contraction for a given temperature change
 * per AISC A4.3a
 * 
 * @param initialLength - Initial length of member, inches
 * @param temperatureChange - Change in temperature from ambient, °F
 * @param materialType - Type of material
 * @returns Change in length due to thermal effects, inches
 * 
 * @source AISC 360-22, Appendix 4.3a
 */
export function calculateThermalExpansion(
  initialLength: number,
  temperatureChange: number,
  materialType: keyof ThermalExpansionCoefficients
): number {
  if (initialLength <= 0) {
    throw new Error("Initial length must be positive");
  }
  
  const coefficient = THERMAL_EXPANSION_COEFFICIENTS[materialType];
  const deltaLength = coefficient * initialLength * temperatureChange;
  
  return deltaLength;
}

/**
 * Calculates thermal strain for a given temperature change
 * per AISC A4.3a
 * 
 * @param temperatureChange - Change in temperature from ambient, °F
 * @param materialType - Type of material
 * @returns Thermal strain, in/in
 * 
 * @source AISC 360-22, Appendix 4.3a
 */
export function calculateThermalStrain(
  temperatureChange: number,
  materialType: keyof ThermalExpansionCoefficients
): number {
  const coefficient = THERMAL_EXPANSION_COEFFICIENTS[materialType];
  const thermalStrain = coefficient * temperatureChange;
  
  return thermalStrain;
}

/**
 * Calculates thermal forces in restrained members
 * 
 * @param thermalStrain - Thermal strain, in/in
 * @param E - Elastic modulus, ksi
 * @param A - Cross-sectional area, in²
 * @param restraintFactor - Factor accounting for partial restraint (0 = free, 1 = fully restrained)
 * @returns Thermal force, kips
 * 
 * @source AISC 360-22, Appendix 4.3a (implied)
 */
export function calculateThermalForce(
  thermalStrain: number,
  E: number,
  A: number,
  restraintFactor: number = 1.0
): number {
  if (E <= 0) {
    throw new Error("Elastic modulus must be positive");
  }
  if (A <= 0) {
    throw new Error("Cross-sectional area must be positive");
  }
  if (restraintFactor < 0 || restraintFactor > 1) {
    throw new Error("Restraint factor must be between 0 and 1");
  }
  
  // Thermal force = thermal strain × elastic modulus × area × restraint factor
  const thermalForce = thermalStrain * E * A * restraintFactor;
  
  return thermalForce;
}

/**
 * Calculates thermal moment in restrained beams due to temperature gradient
 * 
 * @param temperatureGradient - Temperature difference across depth, °F
 * @param depth - Member depth, in.
 * @param E - Elastic modulus, ksi
 * @param I - Moment of inertia, in⁴
 * @param materialType - Type of material
 * @param restraintFactor - Factor accounting for partial restraint (0 = free, 1 = fully restrained)
 * @returns Thermal moment, kip-in
 * 
 * @source AISC 360-22, Appendix 4.3a (implied)
 */
export function calculateThermalMoment(
  temperatureGradient: number,
  depth: number,
  E: number,
  I: number,
  materialType: keyof ThermalExpansionCoefficients,
  restraintFactor: number = 1.0
): number {
  if (depth <= 0) {
    throw new Error("Member depth must be positive");
  }
  if (E <= 0) {
    throw new Error("Elastic modulus must be positive");
  }
  if (I <= 0) {
    throw new Error("Moment of inertia must be positive");
  }
  if (restraintFactor < 0 || restraintFactor > 1) {
    throw new Error("Restraint factor must be between 0 and 1");
  }
  
  const coefficient = THERMAL_EXPANSION_COEFFICIENTS[materialType];
  
  // Thermal curvature = (thermal expansion coefficient × temperature gradient) / depth
  const thermalCurvature = (coefficient * temperatureGradient) / depth;
  
  // Thermal moment = thermal curvature × elastic modulus × moment of inertia × restraint factor
  const thermalMoment = thermalCurvature * E * I * restraintFactor;
  
  return thermalMoment;
}

/**
 * Validates thermal expansion inputs
 * 
 * @param initialLength - Initial length, inches
 * @param temperatureChange - Temperature change, °F
 * @param materialType - Material type
 * @returns Validation result
 * 
 * @source AISC 360-22, Appendix 4.3a
 */
export function validateThermalExpansionInputs(
  initialLength: number,
  temperatureChange: number,
  materialType: keyof ThermalExpansionCoefficients
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Basic validation
  if (initialLength <= 0) {
    errors.push("Initial length must be positive");
  }
  
  if (!isFinite(temperatureChange)) {
    errors.push("Temperature change must be a finite number");
  }
  
  if (!Object.keys(THERMAL_EXPANSION_COEFFICIENTS).includes(materialType)) {
    errors.push(`Invalid material type: ${materialType}`);
  }
  
  // Temperature range warnings per AISC A4.3a
  if (Math.abs(temperatureChange) < 82) { // Less than 150°F - 68°F ambient
    warnings.push("AISC A4.3a thermal expansion coefficients are valid for temperatures above 150°F");
  }
  
  if (Math.abs(temperatureChange) > 1932) { // Greater than 2000°F - 68°F ambient
    warnings.push("Temperature change exceeds typical fire exposure range");
  }
  
  // Large deformation warning
  const thermalStrain = Math.abs(calculateThermalStrain(temperatureChange, materialType));
  if (thermalStrain > 0.01) { // 1% strain
    warnings.push("Large thermal strains may require nonlinear analysis");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
} 