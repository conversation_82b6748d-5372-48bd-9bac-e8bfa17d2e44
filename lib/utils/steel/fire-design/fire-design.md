# AISC Fire Design Module (Appendix 4)

## Overview

This module implements **AISC 360-22 Appendix 4 "Structural Design for Fire Conditions"** providing comprehensive functionality for analyzing steel structures under elevated temperature conditions. The implementation covers material property degradation, thermal expansion, fire load combinations, and complete stress-strain behavior per the AISC specification.

## Features

### ✅ Complete AISC Appendix 4 Implementation

- **Load Combinations** (Section 4, Equation A-4-1)
- **Material Properties at Elevated Temperature** (Section 3b, Table A-4.2.1)
- **Thermal Expansion** (Section 3a)
- **Stress-Strain Relationships** (Equations A-4-2, A-4-3, A-4-4)
- **Fire Scenario Analysis** (Section 4.2)

### 🔥 Fire Design Analysis

The module provides functions for:

1. **Fire Load Combinations**: Calculate required strength using `(0.9 or 1.2)D + AT + 0.5L + 0.2S`
2. **Material Degradation**: Interpolate retention factors from AISC Table A-4.2.1
3. **Thermal Effects**: Calculate thermal expansion/contraction for structural members
4. **Stress-Strain Analysis**: Complete nonlinear stress-strain relationships at elevated temperatures
5. **Design Validation**: Comprehensive input validation and design guidance

## API Reference

### Core Functions

#### `calculateFireLoadCombination(loads: FireLoadCombination): number`

Calculates required strength under fire conditions per AISC A4 Section 4, Equation A-4-1.

```typescript
const loads: FireLoadCombination = {
  D: 100,    // Dead load, kips
  AT: 75,    // Fire-induced forces, kips  
  L: 80,     // Live load, kips
  S: 30,     // Snow load, kips
  useMinimumDeadLoad: true  // Use 0.9D vs 1.2D
};

const requiredStrength = calculateFireLoadCombination(loads);
// Returns: 186 kips = 0.9(100) + 75 + 0.5(80) + 0.2(30)
```

#### `calculateSteelPropertiesAtTemperature(temperature, Fy_ambient, Fu_ambient, E_ambient?): SteelPropertiesAtTemperature`

Calculates steel material properties at elevated temperature using AISC Table A-4.2.1 retention factors.

```typescript
const props = calculateSteelPropertiesAtTemperature(1000, 50, 65);
// At 1000°F: 
// - E_T = 14,210 ksi (49% retention)
// - Fy_T = 33 ksi (66% retention)  
// - Fu_T = 33 ksi (equals Fy_T above 750°F)
```

#### `calculateThermalExpansion(initialLength, temperatureChange, materialType): number`

Calculates thermal expansion/contraction per AISC A4.3a coefficients.

```typescript
// 20-foot steel beam heated 900°F above ambient
const expansion = calculateThermalExpansion(240, 900, 'steel');
// Returns: 1.685 inches = 7.8e-6 × 240 × 900
```

#### `calculateStressAtTemperature(strain, steelProps, params): number`

Calculates engineering stress at elevated temperature using AISC A4 Equations A-4-2, A-4-3, A-4-4.

```typescript
const steelProps = calculateSteelPropertiesAtTemperature(800, 50, 65);
const params = calculateStressStrainParameters(steelProps);

// Elastic range (Equation A-4-2)
const elasticStress = calculateStressAtTemperature(0.001, steelProps, params);

// Plastic range (Equation A-4-4)  
const plasticStress = calculateStressAtTemperature(0.05, steelProps, params);
```

#### `performFireDesignAnalysis(temperature, Fy_ambient, Fu_ambient, designMethod, loadCombination?)`

Comprehensive fire design analysis with validation, material properties, stress-strain parameters, and load combinations.

```typescript
const analysis = performFireDesignAnalysis(
  1000,              // Temperature, °F
  50,                // Ambient Fy, ksi
  65,                // Ambient Fu, ksi
  DesignMethod.LRFD, // Design method
  loadCombination    // Optional load combination
);

// Returns complete analysis with:
// - Material property degradation
// - Stress-strain parameters  
// - Required strength calculation
// - Validation results and warnings
```

### Material Properties

#### Steel Retention Factors (AISC Table A-4.2.1)

The module includes the complete AISC Table A-4.2.1 with linear interpolation between values:

| Temperature | kE (E retention) | kp (Fp retention) | ky (Fy retention) |
|-------------|------------------|-------------------|-------------------|
| 68°F        | 1.00             | 1.00              | 1.00              |
| 400°F       | 0.90             | 0.80              | 1.00              |
| 600°F       | 0.78             | 0.58              | 1.00              |
| 800°F       | 0.67             | 0.40              | 0.94              |
| 1000°F      | 0.49             | 0.29              | 0.66              |
| 1200°F      | 0.22             | 0.13              | 0.35              |
| 2000°F      | 0.02             | 0.01              | 0.02              |

#### Thermal Expansion Coefficients (AISC A4.3a)

For temperatures above 150°F (66°C):

- **Steel**: 7.8 × 10⁻⁶ /°F (1.4 × 10⁻⁵ /°C)
- **Normal Concrete**: 10 × 10⁻⁶ /°F (1.8 × 10⁻⁵ /°C)  
- **Lightweight Concrete**: 4.4 × 10⁻⁶ /°F (7.9 × 10⁻⁶ /°C)

### Stress-Strain Behavior

The module implements the complete trilinear stress-strain model from AISC A4.3b:

1. **Elastic Range** (ε ≤ εp): `F(T) = E(T)ε(T)`
2. **Nonlinear Range** (εp < ε < εy): `F(T) = Fp(T) - c + (b/a)√[a² - [εy(T) - ε(T)]²]`
3. **Plastic Range** (εy ≤ ε ≤ εu): `F(T) = Fy(T)`

### Design Validation

The module includes comprehensive validation per AISC A4 requirements:

- **Material Limits**: Fy ≤ 65 ksi (AISC A4.3b User Note)
- **Design Method**: LRFD recommended (AISC A4 Section 2)
- **Temperature Ranges**: Full validation from 68°F to 2200°F+
- **High Temperature Warnings**: Severe degradation alerts above 1000°F

## Usage Examples

### Example 1: Basic Fire Analysis

```typescript
import { 
  calculateFireLoadCombination,
  calculateSteelPropertiesAtTemperature,
  calculateThermalExpansion 
} from '@/steel/fire-design';

// Fire load combination
const fireLoads = {
  D: 150,    // Dead load
  AT: 100,   // Fire load  
  L: 120,    // Live load
  S: 40,     // Snow load
  useMinimumDeadLoad: false
};

const requiredStrength = calculateFireLoadCombination(fireLoads);
// Result: 286 kips

// Material properties at 800°F
const materialProps = calculateSteelPropertiesAtTemperature(800, 50, 65);
// Fy_T = 47 ksi (94% retention)
// E_T = 19,430 ksi (67% retention)

// Thermal expansion for 30-foot beam
const expansion = calculateThermalExpansion(360, 732, 'steel');
// Result: 2.06 inches
```

### Example 2: Complete Fire Design Workflow

```typescript
import { 
  performFireDesignAnalysis,
  calculateStressAtTemperature,
  DesignMethod 
} from '@/steel/fire-design';

// Complete fire analysis for W21x44 beam
const analysis = performFireDesignAnalysis(
  900,               // 900°F fire temperature
  50,                // A992 steel Fy = 50 ksi
  65,                // A992 steel Fu = 65 ksi  
  DesignMethod.LRFD,
  {
    D: 200, AT: 150, L: 180, S: 50,
    useMinimumDeadLoad: false
  }
);

// Check material degradation
console.log(`Strength retention: ${(analysis.materialProperties.Fy_T / 50 * 100).toFixed(1)}%`);
console.log(`Stiffness retention: ${(analysis.materialProperties.E_T / 29000 * 100).toFixed(1)}%`);

// Check required strength vs capacity
console.log(`Required strength: ${analysis.requiredStrength} kips`);

// Analyze stress-strain behavior
const strainLevels = [0.001, 0.01, 0.02, 0.05];
strainLevels.forEach(strain => {
  const stress = calculateStressAtTemperature(
    strain, 
    analysis.materialProperties, 
    analysis.stressStrainParams
  );
  console.log(`At ε = ${strain}: σ = ${stress.toFixed(1)} ksi`);
});
```

### Example 3: Temperature Sensitivity Study

```typescript
import { calculateSteelPropertiesAtTemperature } from '@/steel/fire-design';

// Study strength degradation from ambient to 1200°F
const temperatures = [68, 200, 400, 600, 800, 1000, 1200];
const Fy_ambient = 50; // ksi

console.log('Temperature (°F) | Fy(T) (ksi) | Retention (%)');
console.log('----------------|-------------|-------------');

temperatures.forEach(temp => {
  const props = calculateSteelPropertiesAtTemperature(temp, Fy_ambient, 65);
  const retention = (props.Fy_T / Fy_ambient * 100);
  console.log(`${temp.toString().padStart(15)} | ${props.Fy_T.toFixed(1).padStart(11)} | ${retention.toFixed(1).padStart(11)}`);
});

// Output:
//             68 |        50.0 |       100.0
//            200 |        50.0 |       100.0  
//            400 |        50.0 |       100.0
//            600 |        50.0 |       100.0
//            800 |        47.0 |        94.0
//           1000 |        33.0 |        66.0
//           1200 |        17.5 |        35.0
```

## Testing

The module includes **28 comprehensive tests** covering:

- ✅ Fire load combinations (4 tests)
- ✅ Retention factor interpolation (5 tests)  
- ✅ Material properties calculation (6 tests)
- ✅ Thermal expansion (6 tests)
- ✅ Stress-strain parameters (3 tests)
- ✅ Integration testing (4 tests)

All tests verify exact compliance with AISC 360-22 Appendix 4 equations and table values.

```bash
npm test -- --testPathPattern=fire-design
# ✓ 28 tests passing
```

## Design Considerations

### When to Use Fire Design

Fire design per AISC Appendix 4 is required when:

- **Building codes mandate** fire-resistant design
- **Structural members** are exposed to fire conditions  
- **Performance-based design** is used instead of prescriptive fire ratings
- **Advanced analysis** is performed per Section 4.2.4c

### Material Limitations

- **Steel Grade**: Limited to Fy ≤ 65 ksi per AISC A4.3b User Note
- **Temperature Range**: Valid from 68°F to 2200°F with interpolation
- **Concrete**: Separate provisions apply (f'c ≤ 8 ksi)

### Design Method Requirements

- **LRFD Preferred**: AISC A4 Section 2 requires LRFD unless advanced analysis per 4.2.4c
- **Ambient Resistance Factors**: Use standard φ values with degraded material properties
- **Load Combinations**: Use fire-specific combinations with AT term

### Engineering Judgment

- **Heat Transfer Analysis**: Required to determine member temperatures (Section 4.2.2)
- **Fire Scenarios**: Localized, post-flashover, or exterior fires (Section 4.2.1)  
- **Thermal Expansion**: Consider restraint effects and secondary stresses
- **Connection Design**: May require special considerations for elevated temperatures

## Implementation Notes

### Compliance

This implementation provides **exact compliance** with:
- ✅ AISC 360-22 Appendix 4 equations and procedures
- ✅ Table A-4.2.1 retention factors with linear interpolation
- ✅ Thermal expansion coefficients per Section 3a
- ✅ Load combination equation A-4-1
- ✅ Stress-strain relationships per Equations A-4-2, A-4-3, A-4-4

### Production Ready

- **Comprehensive validation** of all input parameters
- **Detailed error messages** for invalid conditions
- **Performance warnings** for high temperatures
- **TypeScript interfaces** for type safety
- **Extensive documentation** and examples

### Future Enhancements

Potential areas for expansion:
- **Concrete fire design** (separate AISC provisions)
- **Connection fire analysis** (elevated temperature effects)
- **Advanced fire scenarios** (time-temperature curves)
- **Heat transfer integration** (FEA coupling)

---

## References

- AISC 360-22, Appendix 4 "Structural Design for Fire Conditions"
- AISC Design Guide 19, "Fire Resistance of Structural Steel Framing"
- ASTM E119, "Standard Test Methods for Fire Tests of Building Construction"
- ANSI/UL 263, "Standard for Fire Tests of Building Construction and Materials" 