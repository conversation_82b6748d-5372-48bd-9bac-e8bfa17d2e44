/**
 * @file Fire Design Load Combinations
 * 
 * Load combination calculations for AISC 360-22 Appendix 4
 * "Structural Design for Fire Conditions"
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4, Section 4
 */

import { FireLoadCombination } from './types';

/**
 * Calculates fire load combination per AISC A4 Section 4, Equation A-4-1
 * 
 * Load combination: (0.9 or 1.2)D + AT + 0.5L + 0.2S
 * 
 * @param loads - Fire load combination parameters
 * @returns Required strength under fire conditions
 * 
 * @source AISC 360-22, Appendix 4, Section 4, Equation A-4-1
 */
export function calculateFireLoadCombination(loads: FireLoadCombination): number {
  const deadLoadFactor = loads.useMinimumDeadLoad ? 0.9 : 1.2;
  
  const totalLoad = deadLoadFactor * loads.D + loads.AT + 0.5 * loads.L + 0.2 * loads.S;
  
  return totalLoad;
}

/**
 * Calculates multiple fire load combinations to find the controlling case
 * 
 * @param loads - Fire load combination parameters (excluding useMinimumDeadLoad flag)
 * @returns Object with both load combinations and the controlling value
 * 
 * @source AISC 360-22, Appendix 4, Section 4, Equation A-4-1
 */
export function calculateControllingFireLoadCombination(
  loads: Omit<FireLoadCombination, 'useMinimumDeadLoad'>
): {
  with0_9D: number;
  with1_2D: number;
  controlling: number;
  isMinimumDeadLoadControlling: boolean;
} {
  const with0_9D = calculateFireLoadCombination({ ...loads, useMinimumDeadLoad: true });
  const with1_2D = calculateFireLoadCombination({ ...loads, useMinimumDeadLoad: false });
  
  const controlling = Math.max(with0_9D, with1_2D);
  const isMinimumDeadLoadControlling = with0_9D >= with1_2D;
  
  return {
    with0_9D,
    with1_2D,
    controlling,
    isMinimumDeadLoadControlling,
  };
}

/**
 * Validates fire load combination inputs
 * 
 * @param loads - Fire load combination parameters
 * @returns Validation result
 * 
 * @source AISC 360-22, Appendix 4, Section 4
 */
export function validateFireLoadCombination(loads: FireLoadCombination): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Basic validation
  if (typeof loads.D !== 'number' || !isFinite(loads.D)) {
    errors.push("Dead load (D) must be a finite number");
  }
  
  if (typeof loads.AT !== 'number' || !isFinite(loads.AT)) {
    errors.push("Fire load (AT) must be a finite number");
  }
  
  if (typeof loads.L !== 'number' || !isFinite(loads.L)) {
    errors.push("Live load (L) must be a finite number");
  }
  
  if (typeof loads.S !== 'number' || !isFinite(loads.S)) {
    errors.push("Snow load (S) must be a finite number");
  }
  
  // Physical reasonableness checks
  if (loads.D < 0) {
    warnings.push("Negative dead load is unusual");
  }
  
  if (loads.AT < 0) {
    warnings.push("Negative fire load (AT) is unusual");
  }
  
  if (loads.L < 0) {
    warnings.push("Negative live load is unusual");
  }
  
  if (loads.S < 0) {
    warnings.push("Negative snow load is unusual");
  }
  
  // Fire load magnitude check
  if (Math.abs(loads.AT) > Math.abs(loads.D) + Math.abs(loads.L)) {
    warnings.push("Fire load (AT) is unusually large compared to other loads");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
} 