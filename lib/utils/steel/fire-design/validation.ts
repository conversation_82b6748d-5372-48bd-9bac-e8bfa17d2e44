/**
 * @file Fire Design Validation
 * 
 * Validation functions for AISC 360-22 Appendix 4
 * "Structural Design for Fire Conditions"
 * 
 * <AUTHOR> Engineering App
 * @version 1.0.0
 * @source AISC 360-22, Appendix 4
 */

import { DesignMethod } from '../constants';
import { 
  FireDesignValidation, 
  FireDesignAnalysis, 
  FireLoadCombination,
  SteelPropertiesAtTemperature,
  StressStrainParameters
} from './types';
import { calculateSteelPropertiesAtTemperature, calculateStressStrainParameters } from './material-properties';
import { calculateFireLoadCombination } from './load-combinations';
import { THERMAL_EXPANSION_COEFFICIENTS } from './thermal-expansion';

/**
 * Validates fire design input parameters
 * 
 * @param temperature - Temperature in °F
 * @param Fy - Yield strength, ksi
 * @param designMethod - Design method (LRFD recommended for fire)
 * @returns Validation result with warnings
 * 
 * @source AISC 360-22, Appendix 4
 */
export function validateFireDesignInput(
  temperature: number,
  Fy: number,
  designMethod: DesignMethod
): FireDesignValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Temperature validation
  if (temperature < 68) {
    errors.push("Temperature must be at least 68°F");
  }
  if (temperature > 2200) {
    warnings.push("Temperature exceeds table range (2200°F) - extrapolation used");
  }
  
  // Material validation per AISC A4.3b User Note
  if (Fy > 65) {
    errors.push("AISC A4.3b applies only to structural steel with Fy ≤ 65 ksi");
  }
  if (Fy <= 0) {
    errors.push("Yield strength must be positive");
  }
  
  // Design method recommendation per AISC A4 Section 2
  if (designMethod !== DesignMethod.LRFD) {
    warnings.push("AISC A4 Section 2 requires LRFD method unless advanced analysis is performed per Section 4.2.4c");
  }
  
  // High temperature warnings
  if (temperature > 1000) {
    warnings.push("Severe strength degradation at temperatures above 1000°F");
  }
  
  if (temperature > 750) {
    warnings.push("Tensile strength Fu(T) = Fy(T) for temperatures above 750°F");
  }
  
  // Simple methods temperature limit per AISC A4.4d
  if (temperature < 400) {
    warnings.push("Simple methods not intended for temperatures below 400°F (200°C)");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates member geometry for fire design
 * 
 * @param geometry - Member geometry properties
 * @param memberType - Type of member
 * @returns Validation result
 * 
 * @source AISC 360-22, Appendix 4
 */
export function validateFireDesignGeometry(
  geometry: { 
    length?: number; 
    area?: number; 
    sectionModulus?: number; 
    radiusOfGyration?: number;
  },
  memberType: 'tension' | 'compression' | 'flexure' | 'shear'
): FireDesignValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Basic geometry validation
  if (geometry.length !== undefined && geometry.length <= 0) {
    errors.push("Member length must be positive");
  }
  
  if (geometry.area !== undefined && geometry.area <= 0) {
    errors.push("Cross-sectional area must be positive");
  }
  
  if (geometry.sectionModulus !== undefined && geometry.sectionModulus <= 0) {
    errors.push("Section modulus must be positive");
  }
  
  if (geometry.radiusOfGyration !== undefined && geometry.radiusOfGyration <= 0) {
    errors.push("Radius of gyration must be positive");
  }
  
  // Member-specific validation
  if (memberType === 'compression' && geometry.radiusOfGyration !== undefined && geometry.length !== undefined) {
    const slenderness = geometry.length / geometry.radiusOfGyration;
    if (slenderness > 200) {
      warnings.push("High slenderness ratio - consider local buckling effects");
    }
  }
  
  if (memberType === 'flexure' && geometry.length !== undefined) {
    if (geometry.length > 240) { // 20 feet
      warnings.push("Long unbraced length - consider lateral-torsional buckling carefully");
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates fire exposure scenario
 * 
 * @param scenario - Fire exposure details
 * @returns Validation result
 * 
 * @source AISC 360-22, Appendix 4.2
 */
export function validateFireExposureScenario(scenario: {
  duration?: number;
  maxTemperature?: number;
  heatingRate?: number;
  exposure?: 'three-side' | 'four-side' | 'localized';
}): FireDesignValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Duration validation
  if (scenario.duration !== undefined) {
    if (scenario.duration <= 0) {
      errors.push("Fire duration must be positive");
    }
    if (scenario.duration > 240) { // 4 hours
      warnings.push("Extended fire duration - consider creep effects");
    }
  }
  
  // Temperature validation
  if (scenario.maxTemperature !== undefined) {
    if (scenario.maxTemperature < 68) {
      errors.push("Maximum temperature must be at least ambient (68°F)");
    }
    if (scenario.maxTemperature > 2000) {
      warnings.push("Extreme temperature - verify applicability of material models");
    }
  }
  
  // Heating rate validation
  if (scenario.heatingRate !== undefined) {
    if (scenario.heatingRate <= 0) {
      errors.push("Heating rate must be positive");
    }
    if (scenario.heatingRate > 100) { // °F/min
      warnings.push("Rapid heating rate - thermal gradients may be significant");
    }
  }
  
  // Exposure warnings
  if (scenario.exposure === 'four-side') {
    warnings.push("Four-sided exposure typically results in higher temperatures and faster heating");
  }
  
  if (scenario.exposure === 'localized') {
    warnings.push("Localized fire exposure may require advanced thermal analysis");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Provides comprehensive fire design analysis results
 * 
 * @param temperature - Temperature in °F
 * @param Fy_ambient - Ambient yield strength, ksi
 * @param Fu_ambient - Ambient tensile strength, ksi
 * @param designMethod - Design method
 * @param loadCombination - Fire load combination parameters
 * @returns Complete fire design analysis
 * 
 * @source AISC 360-22, Appendix 4
 */
export function performFireDesignAnalysis(
  temperature: number,
  Fy_ambient: number,
  Fu_ambient: number,
  designMethod: DesignMethod,
  loadCombination?: FireLoadCombination
): FireDesignAnalysis {
  const validation = validateFireDesignInput(temperature, Fy_ambient, designMethod);
  
  if (!validation.isValid) {
    throw new Error(`Fire design validation failed: ${validation.errors.join(', ')}`);
  }
  
  const materialProperties = calculateSteelPropertiesAtTemperature(
    temperature, 
    Fy_ambient, 
    Fu_ambient
  );
  
  const stressStrainParams = calculateStressStrainParameters(materialProperties);
  
  let requiredStrength: number | undefined;
  if (loadCombination) {
    requiredStrength = calculateFireLoadCombination(loadCombination);
  }
  
  return {
    validation,
    materialProperties,
    stressStrainParams,
    requiredStrength,
    thermalExpansionCoeff: THERMAL_EXPANSION_COEFFICIENTS.steel,
  };
}

/**
 * Comprehensive fire design check for multiple scenarios
 * 
 * @param scenarios - Array of fire scenarios to analyze
 * @param designMethod - Design method
 * @returns Analysis results for all scenarios
 * 
 * @source AISC 360-22, Appendix 4
 */
export function performMultiScenarioFireAnalysis(
  scenarios: Array<{
    name: string;
    temperature: number;
    Fy_ambient: number;
    Fu_ambient: number;
    loadCombination?: FireLoadCombination;
  }>,
  designMethod: DesignMethod
): Array<{
  scenarioName: string;
  analysis: FireDesignAnalysis;
  isControlling: boolean;
}> {
  const results = scenarios.map(scenario => {
    const analysis = performFireDesignAnalysis(
      scenario.temperature,
      scenario.Fy_ambient,
      scenario.Fu_ambient,
      designMethod,
      scenario.loadCombination
    );
    
    return {
      scenarioName: scenario.name,
      analysis,
      isControlling: false, // Will be determined below
    };
  });
  
  // Determine controlling scenario (highest required strength or lowest capacity)
  if (results.length > 1) {
    let controllingIndex = 0;
    let maxDemandCapacityRatio = 0;
    
    results.forEach((result, index) => {
      if (result.analysis.requiredStrength !== undefined) {
        // Use strength degradation as a proxy for demand/capacity ratio
        const strengthRatio = result.analysis.materialProperties.ky;
        const demandCapacityRatio = (result.analysis.requiredStrength || 0) / strengthRatio;
        
        if (demandCapacityRatio > maxDemandCapacityRatio) {
          maxDemandCapacityRatio = demandCapacityRatio;
          controllingIndex = index;
        }
      }
    });
    
    results[controllingIndex].isControlling = true;
  } else if (results.length === 1) {
    results[0].isControlling = true;
  }
  
  return results;
}

/**
 * Generates fire design summary report
 * 
 * @param analysis - Fire design analysis results
 * @param memberType - Type of member being analyzed
 * @returns Summary report object
 * 
 * @source AISC 360-22, Appendix 4
 */
export function generateFireDesignSummary(
  analysis: FireDesignAnalysis,
  memberType: 'tension' | 'compression' | 'flexure' | 'shear'
): {
  temperature: number;
  strengthRetention: {
    elastic: number;
    yield: number;
    proportional: number;
  };
  applicableEquations: string[];
  designGuidance: string[];
  criticalIssues: string[];
} {
  const { materialProperties, validation } = analysis;
  
  const applicableEquations: string[] = [];
  const designGuidance: string[] = [];
  const criticalIssues: string[] = [];
  
  // Determine applicable equations based on member type
  switch (memberType) {
    case 'tension':
      applicableEquations.push('Chapter D with A4.3b material properties');
      break;
    case 'compression':
      applicableEquations.push('A-4-9: Column buckling');
      if (materialProperties.temperature > 600) {
        applicableEquations.push('A-4-10: Effective length modification');
      }
      break;
    case 'flexure':
      applicableEquations.push('A-4-13, A-4-14: Lateral-torsional buckling');
      applicableEquations.push('A-4-15, A-4-16: Buckling parameters');
      applicableEquations.push('A-4-17: Yield strength limit');
      break;
    case 'shear':
      applicableEquations.push('Chapter G with A4.3b material properties');
      break;
  }
  
  // Generate design guidance
  if (materialProperties.ky < 0.8) {
    designGuidance.push('Significant yield strength degradation - consider alternative protection');
  }
  
  if (materialProperties.kE < 0.6) {
    designGuidance.push('Substantial stiffness reduction - check serviceability and stability');
  }
  
  if (materialProperties.temperature > 1000) {
    designGuidance.push('High temperature exposure - verify connection performance');
  }
  
  // Identify critical issues
  validation.errors.forEach(error => criticalIssues.push(error));
  
  if (materialProperties.ky < 0.5) {
    criticalIssues.push('Severe strength degradation - structural adequacy questionable');
  }
  
  return {
    temperature: materialProperties.temperature,
    strengthRetention: {
      elastic: materialProperties.kE,
      yield: materialProperties.ky,
      proportional: materialProperties.kp,
    },
    applicableEquations,
    designGuidance,
    criticalIssues,
  };
} 