import { BeamModel } from "@/lib/types/beam/beam-model";
import { Span } from "@/lib/types/span/span";
import { Support } from "@/lib/types/support/support";
import { Type } from "@/lib/types/load/load-type";
import { Load } from "@/lib/types/load/load";
import { LoadComboFactor } from "@/lib/types/load/load-combo-factor";
import type { BeamData } from "@/components/beam-analysis/beam-analysis";
import type { AnalysisResults, DataPoint } from "@/lib/types/analysis/analysis-results";
import { getUnitsBySystem } from "@/lib/types/units/unit-enum";

/**
 * Creates spans from a list of supports and beam properties.
 * Each span represents a section of the beam between two supports.
 * 
 * @param supports - Array of beam supports, must be sorted by position
 * @param E - Elastic modulus of the beam material
 * @param I - Moment of inertia of the beam cross-section
 * @returns Array of valid Span objects, filtered to remove any invalid spans
 */
export function createSpansFromSupports(
  supports: Support[],
  E: number,
  I: number
): Span[] {
  return supports.slice(1).map((support, index) => {
    const startSupport = supports[index];
    const endSupport = support;
    
    if (Math.abs(endSupport.position - startSupport.position) < 0.001) {
      return null;
    }
    
    const isFirstSpan = index === 0;
    const isLastSpan = index === supports.length - 2;
    const totalSpans = supports.length - 1;

    return Span.create(
      startSupport,
      endSupport,
      E,
      I,
      totalSpans,
      isFirstSpan,
      isLastSpan
    );
  }).filter((span): span is Span => span !== null);
}

/**
 * Checks if a load's position falls within a span's boundaries.
 * 
 * @param span - The span to check against
 * @param load - The load to check
 * @returns true if the load falls within the span's boundaries
 */
function isLoadWithinSpan(span: Span, load: Load): boolean {
  if (load.type === Type.POINT) {
    return span.contains(load.startPosition!);
  }
  
  if (load.type === Type.DISTRIBUTED) {
    const start = Math.max(span.start, load.startPosition!);
    const end = Math.min(span.end, load.endPosition!);
    return start < end;
  }
  
  return false;
}

/**
 * Calculates the magnitude of a distributed load at a specific position.
 * Uses linear interpolation between start and end magnitudes.
 * 
 * @param position - Position along the load to calculate magnitude
 * @param load - The distributed load
 * @returns Interpolated magnitude at the given position
 */
function calculateLoadMagnitude(position: number, load: Load): number {
  const totalLength = load.endPosition! - load.startPosition!;
  const ratio = (position - load.startPosition!) / totalLength;
  return load.startMagnitude! + ratio * (load.endMagnitude! - load.startMagnitude!);
}

/**
 * Creates a point load for a specific span.
 * 
 * @param span - The span to create the load for
 * @param load - The original point load
 * @returns A new Load object if valid, null otherwise
 */
function createSpanPointLoad(span: Span, load: Load): Load | null {
  if (!span.contains(load.startPosition!)) {
    return null;
  }

  return new Load({
    type: Type.POINT,
    loadType: load.loadType,
    label: load.label || "",
    startPosition: load.startPosition!,
    startMagnitude: load.startMagnitude!
  });
}

/**
 * Creates a distributed load for a specific span, handling partial coverage.
 * 
 * @param span - The span to create the load for
 * @param load - The original distributed load
 * @returns A new Load object if valid, null otherwise
 */
function createSpanDistributedLoad(span: Span, load: Load): Load | null {
  const start = Math.max(span.start, load.startPosition!);
  const end = Math.min(span.end, load.endPosition!);
  
  if (start >= end) {
    return null;
  }

  const startMag = calculateLoadMagnitude(start, load);
  const endMag = calculateLoadMagnitude(end, load);

  return new Load({
    type: Type.DISTRIBUTED,
    loadType: load.loadType,
    label: load.label || "",
    startPosition: start,
    endPosition: end,
    startMagnitude: startMag,
    endMagnitude: endMag
  });
}

/**
 * Adds a single load to a span, handling both point and distributed loads.
 * 
 * @param span - The span to add the load to
 * @param load - The load to add
 * @returns true if the load was successfully added
 */
function addLoadToSpan(span: Span, load: Load): boolean {
  if (!isLoadWithinSpan(span, load)) {
    return false;
  }

  const spanLoad = load.type === Type.POINT
    ? createSpanPointLoad(span, load)
    : createSpanDistributedLoad(span, load);

  if (spanLoad) {
    span.addLoad(spanLoad);
    return true;
  }

  return false;
}

/**
 * Adds loads from load groups to spans, handling distributed loads across span boundaries.
 * 
 * @param spans - Array of beam spans
 * @param loadGroups - Array of load groups to add
 * @param loadCombo - Optional load combination factor to apply
 */
export function addLoadsToSpans(
  spans: Span[], 
  loadGroups: BeamData["loadGroups"],
  loadCombo?: LoadComboFactor
): void {
  spans.forEach(span => {
    loadGroups.forEach(loadGroup => {
      // Apply load combination if provided
      const loads = loadCombo 
        ? loadCombo.applyTo(loadGroup.loads)[0]
        : loadGroup.loads;

      loads.forEach(load => {
        addLoadToSpan(span, load);
      });
    });
  });
}

/**
 * Generates analysis points for shear, moment, and deflection.
 * 
 * @param model - Analyzed beam model
 * @param beamLength - Total length of beam
 * @param numPoints - Number of points to generate (default: 200)
 * @returns Analysis results containing arrays of data points
 */
export function generateAnalysisPoints(
  model: BeamModel,
  beamLength: number,
  numPoints: number = 200
): AnalysisResults {
  const dx = beamLength / numPoints;
  const results: AnalysisResults = {
    shear: [],
    moment: [],
    deflection: []
  };

  for (let i = 0; i <= numPoints; i++) {
    const x = i * dx;
    try {
      results.shear.push({ x, value: model.shearAt(x) });
      results.moment.push({ x, value: model.momentAt(x) });
      results.deflection.push({ x, value: model.deflectionAt(x) });
    } catch (error) {
      continue; // Skip points outside spans
    }
  }

  return results;
}

/**
 * Gets the appropriate axis label for a chart type.
 */
export function getAxisLabel(type: string, units: ReturnType<typeof getUnitsBySystem>): string {
  switch (type) {
    case "shear":
      return `Shear Force (${units.force})`;
    case "moment":
      return `Bending Moment (${units.moment})`;
    case "deflection":
      return `Deflection (${units.deflection})`;
    default:
      return "";
  }
}

/**
 * Formats a value for display based on its type and magnitude.
 */
export function formatValue(value: number, type: string): string {
  if (type === "deflection" || Math.abs(value) < 0.01) {
    return value.toExponential(3);
  }
  return value.toFixed(2);
}

/**
 * Calculates chart domain with padding.
 */
export function calculateDomain(data: DataPoint[]): [number, number] {
  if (!data.length) return [0, 1];
  
  const values = data.map(d => d.value);
  const min = Math.min(...values);
  const max = Math.max(...values);
  
  const padding = Math.max(Math.abs(max - min) * 0.1, Math.abs(min) * 0.1, Math.abs(max) * 0.1);
  return [min - padding, max + padding];
}

/**
 * Formats tick values for chart axes.
 */
export function formatTick(value: number): string {
  if (Math.abs(value) < 0.01 && value !== 0) {
    return value.toExponential(1);
  }
  return value.toFixed(2);
}