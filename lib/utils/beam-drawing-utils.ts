import { CanvasRenderingContext2D } from "canvas";
import { Support } from "@/lib/types/support/support";

interface BeamDrawingConfig {
  ctx: CanvasRenderingContext2D;
  canvas: HTMLCanvasElement;
  margin: number;
  centerY: number;
  beamLength: number;
  scale: number;
  units: {
    length: string;
  };
}

export function drawBeamLine(config: BeamDrawingConfig) {
  const { ctx, canvas, margin, centerY } = config;
  
  ctx.beginPath();
  ctx.moveTo(margin, centerY);
  ctx.lineTo(canvas.width - margin, centerY);
  ctx.strokeStyle = "#000";
  ctx.lineWidth = 7;
  ctx.stroke();
}

export function drawSpanRuler(config: BeamDrawingConfig, supports: Support[]) {
  const { ctx, margin, centerY, scale } = config;
  const rulerY = centerY + 20; // Position between beam and main ruler
  
  // Sort supports by position
  const sortedSupports = [...supports].sort((a, b) => a.position - b.position);
  
  // Draw span measurements between supports
  for (let i = 0; i < sortedSupports.length - 1; i++) {
    const start = sortedSupports[i].position;
    const end = sortedSupports[i + 1].position;
    const spanLength = end - start;
    
    const startX = margin + start * scale;
    const endX = margin + end * scale;
    const midX = (startX + endX) / 2;
    
    // Draw span ruler line
    ctx.beginPath();
    ctx.moveTo(startX, rulerY - 5);
    ctx.lineTo(startX, rulerY + 5);
    ctx.moveTo(startX, rulerY);
    ctx.lineTo(endX, rulerY);
    ctx.moveTo(endX, rulerY - 5);
    ctx.lineTo(endX, rulerY + 5);
    ctx.strokeStyle = "#666";
    ctx.lineWidth = 1;
    ctx.stroke();
    
    // Draw span length label
    ctx.font = "12px Arial";
    ctx.fillStyle = "#666";
    ctx.textAlign = "center";
    ctx.fillText(`${spanLength.toFixed(2)}`, midX, rulerY - 5);
  }
}

export function drawRulerLine(config: BeamDrawingConfig) {
  const { ctx, canvas, margin, centerY } = config;
  
  ctx.beginPath();
  ctx.moveTo(margin, centerY + 60);
  ctx.lineTo(canvas.width - margin, centerY + 60);
  ctx.strokeStyle = "#666";
  ctx.lineWidth = 1;
  ctx.stroke();
}

export function drawMajorTick(
  ctx: CanvasRenderingContext2D,
  x: number,
  centerY: number,
  value: number
) {
  // Draw tick mark
  ctx.beginPath();
  ctx.moveTo(x, centerY + 55);
  ctx.lineTo(x, centerY + 65);
  ctx.strokeStyle = "#666";
  ctx.lineWidth = 1;
  ctx.stroke();

  // Draw label
  ctx.font = "12px Arial";
  ctx.fillStyle = "#666";
  ctx.textAlign = "center";
  ctx.fillText(value.toFixed(1), x, centerY + 80);
}

export function drawMinorTicks(
  ctx: CanvasRenderingContext2D,
  startX: number,
  spacing: number,
  centerY: number
) {
  for (let j = 1; j < 5; j++) {
    const minorX = startX + (spacing * j) / 5;
    const tickHeight = j === 2 ? 7 : 5;

    ctx.beginPath();
    ctx.moveTo(minorX, centerY + 60 - tickHeight/2);
    ctx.lineTo(minorX, centerY + 60 + tickHeight/2);
    ctx.strokeStyle = "#999";
    ctx.lineWidth = 0.5;
    ctx.stroke();
  }
}

export function drawRulerTicks(config: BeamDrawingConfig) {
  const { ctx, margin, centerY, beamLength, scale } = config;
  
  const numTicks = 5;
  const tickSpacing = beamLength / numTicks;
  const pixelSpacing = (scale * beamLength) / numTicks;

  for (let i = 0; i <= numTicks; i++) {
    const x = margin + i * pixelSpacing;
    const value = i * tickSpacing;

    // Draw major tick and label
    drawMajorTick(ctx, x, centerY, value);

    // Draw minor ticks between major ticks
    if (i < numTicks) {
      drawMinorTicks(ctx, x, pixelSpacing, centerY);
    }
  }
}

export function clearCanvas(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement) {
  ctx.clearRect(0, 0, canvas.width, canvas.height);
}

export function initializeBeamDrawing(
  canvas: HTMLCanvasElement,
  beamLength: number
): BeamDrawingConfig | null {
  const ctx = canvas.getContext("2d");
  if (!ctx) return null;

  const margin = 50;
  const scale = (canvas.width - 2 * margin) / beamLength;
  const centerY = canvas.height / 2;

  return {
    ctx,
    canvas,
    margin,
    centerY,
    beamLength,
    scale,
    units: {
      length: "m" // Default unit, can be made configurable
    }
  };
}