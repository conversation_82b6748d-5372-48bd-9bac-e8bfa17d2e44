import { LoadType, Type } from "@/lib/types/load/load-type";
import { Load } from "@/lib/types/load/load";
import { LoadGroup } from "@/lib/types/load/load-group"

export const doOverlap = (g1: LoadGroup, g2: LoadGroup): boolean => {
  // Ensure start and end positions are valid, especially for point loads where end might be undefined
  const g1Start = g1.startPosition;
  const g1End = g1.type === Type.POINT ? g1Start : (g1.endPosition ?? g1Start);
  const g2Start = g2.startPosition;
  const g2End = g2.type === Type.POINT ? g2Start : (g2.endPosition ?? g2Start);

  // Add a small tolerance for floating point comparisons
  const tolerance = 1e-6;

  if (g1.type === Type.POINT && g2.type === Type.POINT) {
    return Math.abs(g1Start - g2Start) < tolerance;
  }

  // Check for overlap between a point load and a distributed load
  if (g1.type === Type.POINT && g2.type === Type.DISTRIBUTED) {
    return g1Start >= g2Start - tolerance && g1Start <= g2End + tolerance;
  }
  if (g1.type === Type.DISTRIBUTED && g2.type === Type.POINT) {
    return g2Start >= g1Start - tolerance && g2Start <= g1End + tolerance;
  }

  // Check for overlap between two distributed loads
  if (g1.type === Type.DISTRIBUTED && g2.type === Type.DISTRIBUTED) {
    // True if they are not separated
    return !(g1End <= g2Start + tolerance || g2End <= g1Start + tolerance);
  }

  return false; // Should not happen with defined types
};

export const assignLevels = (groups: LoadGroup[]): LoadGroup[] => {
  if (!groups || groups.length === 0) return [];

  // 1. Sort groups: This is crucial for desired stacking.
  //    - Primary: Start position (ascending).
  //    - Secondary: Prioritize point loads over distributed loads.
  //    - Tertiary (for distributed loads): Shorter loads (smaller end - start) before longer ones.
  //    - Quaternary (for point loads or same-length distributed): Higher magnitude loads first (processed earlier, potentially taking lower levels).
  const sortedGroups = [...groups].sort((a, b) => {
    // Primary: Start position
    if (Math.abs(a.startPosition - b.startPosition) > 1e-6) {
      return a.startPosition - b.startPosition;
    }

    // Secondary: Type (Point loads first)
    if (a.type === Type.POINT && b.type === Type.DISTRIBUTED) return -1;
    if (a.type === Type.DISTRIBUTED && b.type === Type.POINT) return 1;

    // Tertiary: Length (for distributed loads, shorter first)
    if (a.type === Type.DISTRIBUTED && b.type === Type.DISTRIBUTED) {
      const aLength = (a.endPosition ?? a.startPosition) - a.startPosition;
      const bLength = (b.endPosition ?? b.startPosition) - b.startPosition;
      if (Math.abs(aLength - bLength) > 1e-6) {
        return aLength - bLength; // Shorter loads first
      }
    }

    // Quaternary: Magnitude (higher magnitude first - for more stable stacking if other criteria are equal)
    // This means loads with higher magnitude are processed earlier, potentially taking lower levels.
    // Loads that should appear visually on top (like the user's point load) might need different handling
    // if this magnitude sort conflicts. For now, let's try this.
    // Consider reversing this if point loads need to be pushed higher regardless of magnitude relative to other point loads.
    return (b.getMaxMagnitude?.() ?? 0) - (a.getMaxMagnitude?.() ?? 0);
  });

  const placedGroups: LoadGroup[] = [];

  for (const currentGroup of sortedGroups) {
    let targetLevel = 0;
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const overlapsOnThisLevel = placedGroups.some(pg => 
        pg.level === targetLevel && doOverlap(pg, currentGroup)
      );

      if (!overlapsOnThisLevel) {
        currentGroup.level = targetLevel;
        placedGroups.push(currentGroup); // Add to placed AFTER finding its level
        break; // Found a level for currentGroup
      } else {
        targetLevel++; // Try next level
      }
    }
  }
  // The `placedGroups` array now contains all groups with their levels assigned.
  // However, the original sort order might be preferred by the calling function (BeamVisualization) for drawing iteration.
  // So, we return the original `groups` array, but with the `level` property mutated on each object.
  // Ensure the original `groups` array elements are the same objects that were sorted and had levels assigned.
  return groups; 
};

// The findOverlappingDistributedLoad function seems specific and might not be needed
// if the general assignLevels and drawing logic correctly use the levels.
// I will comment it out for now, as it might have been part of a previous leveling attempt.
/*
export const findOverlappingDistributedLoad = (x: number, currentGroup: LoadGroup, groups: LoadGroup[]): number | null => {
  let maxHeight = 0;
  
  for (const group of groups) {
    if (group === currentGroup) continue;
    if (group.type === Type.DISTRIBUTED) {
      if (x >= group.startPosition && x <= group.endPosition!) {
        const progress = (x - group.startPosition) / (group.endPosition! - group.startPosition);
        const startHeight = Math.abs(group.getMaxMagnitude());
        const endHeight = Math.abs(group.getMaxMagnitude()); // Assuming uniform for this specific old function
        const height = startHeight + (endHeight - startHeight) * progress;
        maxHeight = Math.max(maxHeight, height);
      }
    }
  }
  
  return maxHeight > 0 ? maxHeight : null;
};
*/