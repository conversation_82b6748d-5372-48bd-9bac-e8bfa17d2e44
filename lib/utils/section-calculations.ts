export interface RectangularSectionProperties {
  area: number;
  Ix: number; // Moment of inertia about x-axis (strong axis)
  Iy: number; // Moment of inertia about y-axis (weak axis)
  Sx: number; // Section modulus about x-axis (strong axis)
  Sy: number; // Section modulus about y-axis (weak axis)
  rx: number; // Radius of gyration about x-axis
  ry: number; // Radius of gyration about y-axis
}

/**
 * Calculates section properties for a rectangular section.
 * @param width The width of the rectangle.
 * @param depth The depth of the rectangle.
 * @returns An object containing area, Ix, Iy, Sx, Sy, rx, ry, or null if width/depth are invalid.
 */
export function calculateRectangularSectionProps(
  width: number,
  depth: number
): RectangularSectionProperties | null {
  if (isNaN(width) || width <= 0 || isNaN(depth) || depth <= 0) {
    return null;
  }

  const area = width * depth;
  const Ix = (width * Math.pow(depth, 3)) / 12;
  const Iy = (depth * Math.pow(width, 3)) / 12;
  const Sx = Ix / (depth / 2);
  const Sy = Iy / (width / 2);
  const rx = area > 0 ? Math.sqrt(Ix / area) : 0;
  const ry = area > 0 ? Math.sqrt(Iy / area) : 0;

  return {
    area,
    Ix,
    Iy,
    Sx,
    Sy,
    rx,
    ry,
  };
} 