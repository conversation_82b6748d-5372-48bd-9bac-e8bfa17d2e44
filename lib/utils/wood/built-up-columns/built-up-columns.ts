/**
 * NDS 15.3 Built-Up Columns
 * 
 * This module implements the built-up column design provisions from NDS Section 15.3
 * including general provisions, column stability factor calculations, and nailed
 * built-up column requirements.
 * 
 * Based on NDS 2018 Section 15.3
 */

/**
 * Connection types for built-up columns
 */
export const BUILT_UP_CONNECTION_TYPES = {
  NAILED: 'nailed',
  BOLTED: 'bolted',
  MECHANICALLY_LAMINATED: 'mechanically_laminated',
} as const;

export type BuiltUpConnectionType = typeof BUILT_UP_CONNECTION_TYPES[keyof typeof BUILT_UP_CONNECTION_TYPES];

/**
 * Column stability factor constants for built-up columns
 */
export const BUILT_UP_COLUMN_STABILITY_CONSTANTS = {
  // Kr values for different connection methods (NDS 15.3.2.4)
  KR_NAILED_le2_d2: 0.6,  // For built-up columns where le2/d2 is used and nailed per 15.3.3
  KR_BOLTED_le2_d2: 0.75, // For built-up columns where le2/d2 is used and bolted per 15.3.4
  KR_NAILED_le3_d1: 1.0,  // For built-up columns where le3/d1 is used and nailed per 15.3.3
  KR_BOLTED_le3_d1: 1.0,  // For built-up columns where le3/d1 is used and bolted per 15.3.4
  
  // Buckling stiffness coefficient
  BUCKLING_COEFFICIENT: 0.822,
  
  // Factor c for moisture conditions
  C_SAWN_LUMBER: 0.8,
  C_GLULAM_STRUCTURAL_COMPOSITE: 0.9,
} as const;

/**
 * Nailed built-up column requirements (NDS 15.3.3)
 */
export const NAILED_BUILT_UP_REQUIREMENTS = {
  // Nail spacing requirements
  END_DISTANCE_MIN: 18, // Minimum nail end distance (nail diameters)
  SPACING_ADJACENT_NAILS_MAX: 6, // Maximum spacing between adjacent nails in a row (min thickness)
  SPACING_BETWEEN_ROWS_MAX: 20, // Maximum spacing between rows of nails (nail diameters)
  EDGE_DISTANCE_MAX: 20, // Maximum edge distance (nail diameters)
  LONGITUDINAL_ROWS_MIN: 2, // Minimum number of longitudinal rows for proper connection
  
  // Nail size and penetration requirements
  MIN_PENETRATION_RATIO: 3/4, // Minimum penetration into outermost lamination
  STAGGER_REQUIREMENT: true, // Adjacent nails must be staggered when only one row used
} as const;

/**
 * Built-up column lamination requirements (NDS 15.3.1)
 */
export const BUILT_UP_LAMINATION_REQUIREMENTS = {
  MIN_LAMINATIONS: 2,
  MAX_LAMINATIONS: 5,
  MIN_THICKNESS: 1.5, // inches (1-1/2")
  MIN_DEPTH_CONSISTENCY: true, // All laminations must have same depth
  FACE_CONTACT_REQUIRED: true, // Adjacent lamination faces must be in contact
  FULL_LENGTH_REQUIRED: true, // All laminations must be full column length
} as const;

/**
 * Input parameters for built-up column design
 */
export interface BuiltUpColumnInput {
  /** Number of laminations */
  numberOfLaminations: number;
  /** Thickness of individual lamination (inches) */
  laminationThickness: number;
  /** Depth (face width) of individual lamination (inches) */
  laminationDepth: number;
  /** Actual column length (inches) */
  columnLength: number;
  /** Connection type */
  connectionType: BuiltUpConnectionType;
  /** Reference compression design value parallel to grain (psi) */
  fcStar: number;
  /** Modulus of elasticity for column buckling (psi) */
  eMin: number;
  /** Material type for c factor */
  materialType: 'sawn_lumber' | 'glulam_structural_composite';
  /** Nail specifications (if nailed connection) */
  nailSpecs?: {
    diameter: number; // inches
    spacing: number; // inches on center
    penetration: number; // inches
    numberOfRows: number;
  };
}

/**
 * Validation result for built-up column input
 */
export interface BuiltUpColumnValidation {
  /** Whether the input is valid */
  isValid: boolean;
  /** Validation error messages */
  errors: string[];
  /** Validation warnings */
  warnings: string[];
}

/**
 * Detailed calculation results for built-up column analysis
 */
export interface BuiltUpColumnCalculation {
  /** Slenderness ratio le2/d2 for individual lamination */
  slendernessRatioIndividual: number;
  /** Slenderness ratio le3/d1 for built-up column */
  slendernessRatioBuiltUp: number;
  /** Effective length factor (Kr) */
  effectiveLengthFactor: number;
  /** Critical buckling design value (FcE) in psi */
  criticalBucklingValue: number;
  /** Total cross-sectional area (sq. inches) */
  totalArea: number;
  /** Section modulus (in³) */
  sectionModulus: number;
  /** Intermediate calculation values */
  intermediateValues: {
    /** Factor c for moisture/material conditions */
    cFactor: number;
    /** Column stability factor numerator */
    numerator: number;
    /** Column stability factor denominator */
    denominator: number;
  };
}

/**
 * Result of built-up column analysis
 */
export interface BuiltUpColumnResult {
  /** Column stability factor (Cp) */
  columnStabilityFactor: number;
  /** Adjusted compression design value (Fc') in psi */
  adjustedCompressionValue: number;
  /** Maximum allowable axial load (lbs) */
  allowableAxialLoad: number;
  /** Detailed calculation breakdown */
  calculation: BuiltUpColumnCalculation;
  /** Connection compliance check */
  connectionCompliance: {
    compliant: boolean;
    violations: string[];
    recommendations: string[];
  };
  /** Analysis metadata */
  analysis: {
    connectionType: BuiltUpConnectionType;
    materialType: string;
    numberOfLaminations: number;
    governingSlendernessRatio: number;
  };
}

/**
 * Validates built-up column input parameters
 * 
 * @param input - Built-up column input parameters
 * @returns Validation result with errors and warnings
 */
export function validateBuiltUpColumnInput(input: BuiltUpColumnInput): BuiltUpColumnValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate lamination requirements (NDS 15.3.1)
  if (input.numberOfLaminations < BUILT_UP_LAMINATION_REQUIREMENTS.MIN_LAMINATIONS) {
    errors.push(`Minimum ${BUILT_UP_LAMINATION_REQUIREMENTS.MIN_LAMINATIONS} laminations required`);
  }

  if (input.numberOfLaminations > BUILT_UP_LAMINATION_REQUIREMENTS.MAX_LAMINATIONS) {
    errors.push(`Maximum ${BUILT_UP_LAMINATION_REQUIREMENTS.MAX_LAMINATIONS} laminations allowed`);
  }

  if (input.laminationThickness < BUILT_UP_LAMINATION_REQUIREMENTS.MIN_THICKNESS) {
    errors.push(`Minimum lamination thickness is ${BUILT_UP_LAMINATION_REQUIREMENTS.MIN_THICKNESS} inches`);
  }

  // Validate dimensions
  if (input.laminationDepth <= 0) {
    errors.push('Lamination depth must be greater than 0');
  }

  if (input.columnLength <= 0) {
    errors.push('Column length must be greater than 0');
  }

  // Validate slenderness ratios (NDS 15.3.2.3)
  const individualSlenderness = input.columnLength / input.laminationDepth;
  const builtUpSlenderness = input.columnLength / (input.numberOfLaminations * input.laminationThickness);

  if (individualSlenderness > 50) {
    errors.push(`Individual lamination slenderness ratio (le2/d2 = ${individualSlenderness.toFixed(1)}) exceeds maximum of 50`);
  }

  if (builtUpSlenderness > 75) {
    warnings.push(`Built-up column slenderness ratio (le3/d1 = ${builtUpSlenderness.toFixed(1)}) exceeds recommended maximum of 75 during construction`);
  }

  // Validate material properties
  if (input.fcStar <= 0) {
    errors.push('Reference compression design value must be greater than 0');
  }

  if (input.eMin <= 0) {
    errors.push('Modulus of elasticity must be greater than 0');
  }

  // Validate connection type
  if (!Object.values(BUILT_UP_CONNECTION_TYPES).includes(input.connectionType)) {
    errors.push(`Invalid connection type: ${input.connectionType}`);
  }

  // Validate nail specifications if nailed connection
  if (input.connectionType === BUILT_UP_CONNECTION_TYPES.NAILED && input.nailSpecs) {
    const nail = input.nailSpecs;
    
    if (nail.diameter <= 0) {
      errors.push('Nail diameter must be greater than 0');
    }

    if (nail.spacing <= 0) {
      errors.push('Nail spacing must be greater than 0');
    }

    if (nail.penetration < nail.diameter * NAILED_BUILT_UP_REQUIREMENTS.MIN_PENETRATION_RATIO) {
      warnings.push(`Nail penetration should be at least ${NAILED_BUILT_UP_REQUIREMENTS.MIN_PENETRATION_RATIO * 100}% of diameter`);
    }

    if (nail.numberOfRows < NAILED_BUILT_UP_REQUIREMENTS.LONGITUDINAL_ROWS_MIN) {
      warnings.push(`Minimum ${NAILED_BUILT_UP_REQUIREMENTS.LONGITUDINAL_ROWS_MIN} longitudinal rows of nails recommended`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Determines the effective length factor (Kr) based on connection method and slenderness ratios
 * 
 * @param connectionType - Type of connection (nailed, bolted, etc.)
 * @param useIndividualSlenderness - Whether to use individual lamination slenderness ratio
 * @returns Effective length factor
 */
export function determineEffectiveLengthFactor(
  connectionType: BuiltUpConnectionType,
  useIndividualSlenderness: boolean
): number {
  if (useIndividualSlenderness) {
    // Using le2/d2 approach
    switch (connectionType) {
      case BUILT_UP_CONNECTION_TYPES.NAILED:
        return BUILT_UP_COLUMN_STABILITY_CONSTANTS.KR_NAILED_le2_d2;
      case BUILT_UP_CONNECTION_TYPES.BOLTED:
        return BUILT_UP_COLUMN_STABILITY_CONSTANTS.KR_BOLTED_le2_d2;
      default:
        return BUILT_UP_COLUMN_STABILITY_CONSTANTS.KR_NAILED_le2_d2;
    }
  } else {
    // Using le3/d1 approach
    return BUILT_UP_COLUMN_STABILITY_CONSTANTS.KR_NAILED_le3_d1; // Same for both nailed and bolted
  }
}

/**
 * Calculates the critical buckling design value (FcE) for built-up columns
 * 
 * @param eMin - Minimum modulus of elasticity (psi)
 * @param effectiveLength - Effective column length (inches)
 * @param applicableDimension - Applicable dimension for slenderness calculation (inches)
 * @returns Critical buckling design value in psi
 */
export function calculateBuiltUpCriticalBuckling(
  eMin: number,
  effectiveLength: number,
  applicableDimension: number
): number {
  // FcE = 0.822 * E'min / (le/d)²
  const slendernessRatio = effectiveLength / applicableDimension;
  return BUILT_UP_COLUMN_STABILITY_CONSTANTS.BUCKLING_COEFFICIENT * eMin / (slendernessRatio ** 2);
}

/**
 * Calculates the column stability factor (Cp) for built-up columns
 * 
 * @param fcStar - Reference compression design value parallel to grain (psi)
 * @param fcE - Critical buckling design value (psi)
 * @param materialType - Material type for c factor determination
 * @returns Column stability factor and calculation details
 */
export function calculateBuiltUpColumnStabilityFactor(
  fcStar: number,
  fcE: number,
  materialType: 'sawn_lumber' | 'glulam_structural_composite'
): { cp: number; calculation: any } {
  // Determine c factor based on material type
  const c = materialType === 'sawn_lumber' 
    ? BUILT_UP_COLUMN_STABILITY_CONSTANTS.C_SAWN_LUMBER
    : BUILT_UP_COLUMN_STABILITY_CONSTANTS.C_GLULAM_STRUCTURAL_COMPOSITE;

  // Calculate intermediate values using NDS Equation 15.3-1
  const fcEOverFcStar = fcE / fcStar;
  const term1 = 1 + fcEOverFcStar;
  const numeratorTerm = term1 / (2 * c);
  const underSqrt = (term1 / (2 * c)) ** 2 - (fcEOverFcStar / c);
  
  if (underSqrt < 0) {
    throw new Error('Invalid calculation: negative value under square root in column stability formula');
  }
  
  const denominatorTerm = Math.sqrt(underSqrt);
  
  // Cp = (1 + FcE/Fc*) / (2c) - sqrt[((1 + FcE/Fc*) / (2c))² - (FcE/Fc*) / c]
  const cp = numeratorTerm - denominatorTerm;

  return {
    cp: Math.min(cp, 1.0), // Cp cannot exceed 1.0
    calculation: {
      cFactor: c,
      fcEOverFcStar,
      numerator: numeratorTerm,
      denominator: denominatorTerm,
      term1,
      underSqrt,
    },
  };
}

/**
 * Checks nailed built-up column connection compliance
 * 
 * @param input - Built-up column input with nail specifications
 * @returns Compliance check results
 */
export function checkNailedConnectionCompliance(input: BuiltUpColumnInput): {
  compliant: boolean;
  violations: string[];
  recommendations: string[];
} {
  const violations: string[] = [];
  const recommendations: string[] = [];

  if (input.connectionType !== BUILT_UP_CONNECTION_TYPES.NAILED || !input.nailSpecs) {
    return { compliant: true, violations, recommendations };
  }

  const nail = input.nailSpecs;

  // Check end distance (NDS 15.3.3(c))
  const minEndDistance = nail.diameter * NAILED_BUILT_UP_REQUIREMENTS.END_DISTANCE_MIN;
  if (nail.spacing < minEndDistance) {
    violations.push(`End distance must be ≥ ${NAILED_BUILT_UP_REQUIREMENTS.END_DISTANCE_MIN}D = ${minEndDistance.toFixed(2)}" for nail diameter ${nail.diameter}"`);
  }

  // Check spacing between adjacent nails (NDS 15.3.3(d))
  const maxAdjacentSpacing = input.laminationThickness * NAILED_BUILT_UP_REQUIREMENTS.SPACING_ADJACENT_NAILS_MAX;
  if (nail.spacing > maxAdjacentSpacing) {
    violations.push(`Spacing between adjacent nails cannot exceed ${NAILED_BUILT_UP_REQUIREMENTS.SPACING_ADJACENT_NAILS_MAX}tmin = ${maxAdjacentSpacing.toFixed(2)}"`);
  }

  // Check spacing between rows (NDS 15.3.3(e))
  const maxRowSpacing = nail.diameter * NAILED_BUILT_UP_REQUIREMENTS.SPACING_BETWEEN_ROWS_MAX;
  if (nail.spacing > maxRowSpacing) {
    violations.push(`Spacing between rows cannot exceed ${NAILED_BUILT_UP_REQUIREMENTS.SPACING_BETWEEN_ROWS_MAX}D = ${maxRowSpacing.toFixed(2)}"`);
  }

  // Check minimum penetration
  const minPenetration = nail.diameter * NAILED_BUILT_UP_REQUIREMENTS.MIN_PENETRATION_RATIO;
  if (nail.penetration < minPenetration) {
    violations.push(`Nail penetration must be ≥ ${NAILED_BUILT_UP_REQUIREMENTS.MIN_PENETRATION_RATIO * 100}% of diameter = ${minPenetration.toFixed(2)}"`);
  }

  // Recommendations
  if (nail.numberOfRows === 1) {
    recommendations.push('Consider using staggered nails when only one longitudinal row is used');
  }

  if (nail.numberOfRows < NAILED_BUILT_UP_REQUIREMENTS.LONGITUDINAL_ROWS_MIN) {
    recommendations.push(`Consider using at least ${NAILED_BUILT_UP_REQUIREMENTS.LONGITUDINAL_ROWS_MIN} longitudinal rows for better load distribution`);
  }

  return {
    compliant: violations.length === 0,
    violations,
    recommendations,
  };
}

/**
 * Performs complete built-up column analysis
 * 
 * @param input - Built-up column input parameters
 * @returns Complete built-up column analysis results
 * 
 * @example
 * ```typescript
 * const input: BuiltUpColumnInput = {
 *   numberOfLaminations: 3,
 *   laminationThickness: 1.5, // 2x nominal
 *   laminationDepth: 5.5, // 2x6 actual
 *   columnLength: 120, // 10 feet
 *   connectionType: BUILT_UP_CONNECTION_TYPES.NAILED,
 *   fcStar: 1350, // psi
 *   eMin: 1200000, // psi
 *   materialType: 'sawn_lumber',
 *   nailSpecs: {
 *     diameter: 0.131, // 10d nail
 *     spacing: 6, // inches
 *     penetration: 2.0,
 *     numberOfRows: 2
 *   }
 * };
 * 
 * const result = calculateBuiltUpColumn(input);
 * console.log(`Column stability factor: ${result.columnStabilityFactor}`);
 * console.log(`Allowable axial load: ${result.allowableAxialLoad} lbs`);
 * ```
 */
export function calculateBuiltUpColumn(input: BuiltUpColumnInput): BuiltUpColumnResult {
  // Validate input
  const validation = validateBuiltUpColumnInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input: ${validation.errors.join(', ')}`);
  }

  // Calculate dimensions and properties
  const totalThickness = input.numberOfLaminations * input.laminationThickness;
  const totalArea = totalThickness * input.laminationDepth;
  const sectionModulus = (totalThickness * input.laminationDepth ** 2) / 6;

  // Calculate slenderness ratios
  const slendernessRatioIndividual = input.columnLength / input.laminationDepth; // le2/d2
  const slendernessRatioBuiltUp = input.columnLength / totalThickness; // le3/d1

  // Determine which approach to use (NDS 15.3.2.1)
  // Use individual lamination approach if le2/d2 ≤ 50, otherwise use built-up approach
  const useIndividualApproach = slendernessRatioIndividual <= 50;
  const governingSlendernessRatio = useIndividualApproach ? slendernessRatioIndividual : slendernessRatioBuiltUp;
  const applicableDimension = useIndividualApproach ? input.laminationDepth : totalThickness;

  // Determine effective length factor
  const effectiveLengthFactor = determineEffectiveLengthFactor(input.connectionType, useIndividualApproach);
  
  // Calculate effective length
  const effectiveLength = effectiveLengthFactor * input.columnLength;

  // Calculate critical buckling design value
  const criticalBucklingValue = calculateBuiltUpCriticalBuckling(
    input.eMin,
    effectiveLength,
    applicableDimension
  );

  // Calculate column stability factor
  const stabilityResult = calculateBuiltUpColumnStabilityFactor(
    input.fcStar,
    criticalBucklingValue,
    input.materialType
  );

  // Calculate adjusted compression value and allowable load
  const adjustedCompressionValue = input.fcStar * stabilityResult.cp;
  const allowableAxialLoad = adjustedCompressionValue * totalArea;

  // Check connection compliance
  const connectionCompliance = checkNailedConnectionCompliance(input);

  const calculation: BuiltUpColumnCalculation = {
    slendernessRatioIndividual,
    slendernessRatioBuiltUp,
    effectiveLengthFactor,
    criticalBucklingValue,
    totalArea,
    sectionModulus,
    intermediateValues: stabilityResult.calculation,
  };

  return {
    columnStabilityFactor: stabilityResult.cp,
    adjustedCompressionValue,
    allowableAxialLoad,
    calculation,
    connectionCompliance,
    analysis: {
      connectionType: input.connectionType,
      materialType: input.materialType,
      numberOfLaminations: input.numberOfLaminations,
      governingSlendernessRatio,
    },
  };
}

/**
 * Optimizes built-up column design by comparing different lamination configurations
 * 
 * @param baseInput - Base built-up column parameters
 * @param variations - Array of parameter variations to analyze
 * @returns Optimization results with best configuration
 */
export function optimizeBuiltUpColumnDesign(
  baseInput: BuiltUpColumnInput,
  variations: Partial<BuiltUpColumnInput>[]
): {
  best: BuiltUpColumnResult;
  configurations: Array<{ input: BuiltUpColumnInput; result: BuiltUpColumnResult; efficiency: number }>;
} {
  const configurations = variations.map(variation => {
    const input = { ...baseInput, ...variation };
    const result = calculateBuiltUpColumn(input);
    // Efficiency could be based on load capacity per unit weight/cost
    const efficiency = result.allowableAxialLoad / result.calculation.totalArea; // Load per unit area
    
    return { input, result, efficiency };
  });

  // Sort by efficiency (highest load capacity per unit area)
  configurations.sort((a, b) => b.efficiency - a.efficiency);

  return {
    best: configurations[0].result,
    configurations,
  };
}

/**
 * Provides design recommendations for built-up column optimization
 * 
 * @param input - Built-up column input parameters
 * @returns Design recommendations and alternatives
 */
export function getBuiltUpColumnDesignRecommendations(input: BuiltUpColumnInput): {
  currentPerformance: BuiltUpColumnResult;
  recommendations: string[];
  alternatives: Array<{ description: string; input: BuiltUpColumnInput; improvement: string }>;
} {
  const current = calculateBuiltUpColumn(input);
  const recommendations: string[] = [];
  const alternatives: Array<{ description: string; input: BuiltUpColumnInput; improvement: string }> = [];

  // Analyze current performance
  if (current.columnStabilityFactor < 0.5) {
    recommendations.push('Column stability factor is low - consider reducing slenderness ratio');
  }

  if (current.calculation.slendernessRatioIndividual > 40) {
    recommendations.push('Consider increasing lamination depth to reduce slenderness');
    alternatives.push({
      description: 'Increase lamination depth by 1.5"',
      input: { ...input, laminationDepth: input.laminationDepth + 1.5 },
      improvement: 'Reduced slenderness ratio',
    });
  }

  if (input.numberOfLaminations < 4 && current.allowableAxialLoad < 5000) {
    recommendations.push('Consider adding laminations to increase capacity');
    alternatives.push({
      description: 'Add one lamination',
      input: { ...input, numberOfLaminations: input.numberOfLaminations + 1 },
      improvement: 'Increased cross-sectional area',
    });
  }

  if (input.connectionType === BUILT_UP_CONNECTION_TYPES.NAILED) {
    recommendations.push('Consider bolted connections for higher capacity applications');
    alternatives.push({
      description: 'Use bolted connections',
      input: { ...input, connectionType: BUILT_UP_CONNECTION_TYPES.BOLTED },
      improvement: 'Higher effective length factor',
    });
  }

  return {
    currentPerformance: current,
    recommendations,
    alternatives,
  };
} 