/**
 * NDS 15.2 Spaced Columns
 * 
 * This module implements the spaced column design provisions from NDS Section 15.2
 * including spacer and end block provisions, and column stability factor calculations.
 * 
 * Based on NDS 2018 Section 15.2
 */

/**
 * Species groups for end spacer block constant calculations
 */
export const SPACED_COLUMN_SPECIES_GROUPS = {
  A: 'A',
  B: 'B', 
  C: 'C',
  D: 'D',
} as const;

export type SpacedColumnSpeciesGroup = typeof SPACED_COLUMN_SPECIES_GROUPS[keyof typeof SPACED_COLUMN_SPECIES_GROUPS];

/**
 * End fixity conditions for spaced columns
 */
export const END_FIXITY_CONDITIONS = {
  CONDITION_A: 'condition_a', // End distance ≤ ℓ1/20
  CONDITION_B: 'condition_b', // ℓ1/20 < end distance ≤ ℓ1/10
} as const;

export type EndFixityCondition = typeof END_FIXITY_CONDITIONS[keyof typeof END_FIXITY_CONDITIONS];

/**
 * End spacer block constants (Ks) from NDS 15.2.2.5
 */
export const END_SPACER_BLOCK_CONSTANTS = {
  [SPACED_COLUMN_SPECIES_GROUPS.A]: {
    formula: '9.55 * ((ℓ₁/d₁) - 11)',
    maxValue: 468,
    coefficient: 9.55,
    offset: 11,
  },
  [SPACED_COLUMN_SPECIES_GROUPS.B]: {
    formula: '8.14 * ((ℓ₁/d₁) - 11)',
    maxValue: 399,
    coefficient: 8.14,
    offset: 11,
  },
  [SPACED_COLUMN_SPECIES_GROUPS.C]: {
    formula: '6.73 * ((ℓ₁/d₁) - 11)',
    maxValue: 330,
    coefficient: 6.73,
    offset: 11,
  },
  [SPACED_COLUMN_SPECIES_GROUPS.D]: {
    formula: '5.32 * ((ℓ₁/d₁) - 11)',
    maxValue: 261,
    coefficient: 5.32,
    offset: 11,
  },
} as const;

/**
 * Column stability factor constants for spaced columns
 */
export const SPACED_COLUMN_STABILITY_CONSTANTS = {
  // Effective length factor coefficients (Ke) for different fixity conditions
  KE_CONDITION_A: 2.5,  // Fixity condition "a"
  KE_CONDITION_B: 3.0,  // Fixity condition "b"
  
  // Buckling stiffness coefficient
  BUCKLING_COEFFICIENT: 0.822,
  
  // Factor c for moisture conditions
  C_SAWN_LUMBER: 0.8,
  C_GLULAM_STRUCTURAL_COMPOSITE: 0.9,
} as const;

/**
 * Input parameters for spaced column design
 */
export interface SpacedColumnInput {
  /** Species group (A, B, C, or D) */
  speciesGroup: SpacedColumnSpeciesGroup;
  /** Distance between points of lateral support in planes 1 and 2 (inches) */
  lateralSupportDistance: number;
  /** Cross-sectional dimension of individual member in plane of lateral support (inches) */
  crossSectionalDimension: number;
  /** Distance from center of spacer block to centroid of group of connectors (inches) */
  spacerBlockDistance: number;
  /** Actual column length (inches) */
  columnLength: number;
  /** End fixity condition */
  endFixityCondition: EndFixityCondition;
  /** Reference compression design value parallel to grain (psi) */
  fcStar: number;
  /** Modulus of elasticity for column buckling (psi) */
  eMin: number;
  /** Material type for c factor */
  materialType: 'sawn_lumber' | 'glulam_structural_composite';
}

/**
 * Validation result for spaced column input
 */
export interface SpacedColumnValidation {
  /** Whether the input is valid */
  isValid: boolean;
  /** Validation error messages */
  errors: string[];
  /** Validation warnings */
  warnings: string[];
}

/**
 * Detailed calculation results for spaced column analysis
 */
export interface SpacedColumnCalculation {
  /** End spacer block constant (Ks) */
  endSpacerBlockConstant: number;
  /** Slenderness ratio (ℓ₁/d₁) */
  slendernessRatio: number;
  /** Effective length factor (Ke) */
  effectiveLengthFactor: number;
  /** Effective column length (le) in inches */
  effectiveLength: number;
  /** Critical buckling design value (FcE) in psi */
  criticalBucklingValue: number;
  /** Intermediate calculation values */
  intermediateValues: {
    /** Factor c for moisture/material conditions */
    cFactor: number;
    /** Column stability factor numerator */
    numerator: number;
    /** Column stability factor denominator */
    denominator: number;
  };
}

/**
 * Result of spaced column analysis
 */
export interface SpacedColumnResult {
  /** Column stability factor (Cp) */
  columnStabilityFactor: number;
  /** Adjusted compression design value (Fc') in psi */
  adjustedCompressionValue: number;
  /** End spacer block constant used */
  endSpacerBlockConstant: number;
  /** Detailed calculation breakdown */
  calculation: SpacedColumnCalculation;
  /** Analysis metadata */
  analysis: {
    speciesGroup: SpacedColumnSpeciesGroup;
    endFixityCondition: EndFixityCondition;
    materialType: string;
    slendernessRatio: number;
  };
}

/**
 * Validates spaced column input parameters
 * 
 * @param input - Spaced column input parameters
 * @returns Validation result with errors and warnings
 */
export function validateSpacedColumnInput(input: SpacedColumnInput): SpacedColumnValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate species group
  if (!Object.values(SPACED_COLUMN_SPECIES_GROUPS).includes(input.speciesGroup)) {
    errors.push(`Invalid species group: ${input.speciesGroup}`);
  }

  // Validate dimensions
  if (input.lateralSupportDistance <= 0) {
    errors.push('Lateral support distance must be greater than 0');
  }

  if (input.crossSectionalDimension <= 0) {
    errors.push('Cross-sectional dimension must be greater than 0');
  }

  if (input.spacerBlockDistance <= 0) {
    errors.push('Spacer block distance must be greater than 0');
  }

  if (input.columnLength <= 0) {
    errors.push('Column length must be greater than 0');
  }

  // Validate slenderness ratio (NDS 15.2.3.2)
  const slendernessRatio = input.lateralSupportDistance / input.crossSectionalDimension;
  if (slendernessRatio > 80) {
    errors.push(`Slenderness ratio (ℓ₁/d₁ = ${slendernessRatio.toFixed(1)}) exceeds maximum of 80`);
  }

  if (slendernessRatio > 50) {
    warnings.push(`High slenderness ratio (ℓ₁/d₁ = ${slendernessRatio.toFixed(1)}) may require special consideration`);
  }

  // Validate material properties
  if (input.fcStar <= 0) {
    errors.push('Reference compression design value must be greater than 0');
  }

  if (input.eMin <= 0) {
    errors.push('Modulus of elasticity must be greater than 0');
  }

  // Validate end fixity condition
  if (!Object.values(END_FIXITY_CONDITIONS).includes(input.endFixityCondition)) {
    errors.push(`Invalid end fixity condition: ${input.endFixityCondition}`);
  }

  // Validate material type
  if (!['sawn_lumber', 'glulam_structural_composite'].includes(input.materialType)) {
    errors.push(`Invalid material type: ${input.materialType}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Calculates the end spacer block constant (Ks)
 * 
 * @param speciesGroup - Species group (A, B, C, or D)
 * @param slendernessRatio - Slenderness ratio (ℓ₁/d₁)
 * @returns End spacer block constant
 */
export function calculateEndSpacerBlockConstant(
  speciesGroup: SpacedColumnSpeciesGroup,
  slendernessRatio: number
): number {
  const constants = END_SPACER_BLOCK_CONSTANTS[speciesGroup];
  
  // Ks = coefficient * ((ℓ₁/d₁) - offset)
  const ks = constants.coefficient * (slendernessRatio - constants.offset);
  
  // Apply maximum value limit
  return Math.min(ks, constants.maxValue);
}

/**
 * Calculates the effective length factor (Ke) based on end fixity condition
 * 
 * @param endFixityCondition - End fixity condition (a or b)
 * @returns Effective length factor
 */
export function calculateEffectiveLengthFactor(endFixityCondition: EndFixityCondition): number {
  switch (endFixityCondition) {
    case END_FIXITY_CONDITIONS.CONDITION_A:
      return SPACED_COLUMN_STABILITY_CONSTANTS.KE_CONDITION_A;
    case END_FIXITY_CONDITIONS.CONDITION_B:
      return SPACED_COLUMN_STABILITY_CONSTANTS.KE_CONDITION_B;
    default:
      throw new Error(`Invalid end fixity condition: ${endFixityCondition}`);
  }
}

/**
 * Calculates the critical buckling design value (FcE)
 * 
 * @param eMin - Minimum modulus of elasticity (psi)
 * @param effectiveLength - Effective column length (inches)
 * @param spacerBlockDistance - Distance from spacer block center to connector centroid (inches)
 * @returns Critical buckling design value in psi
 */
export function calculateCriticalBucklingValue(
  eMin: number,
  effectiveLength: number,
  spacerBlockDistance: number
): number {
  // FcE = 0.822 * E'min / (le/d)²
  const slendernessRatioEffective = effectiveLength / spacerBlockDistance;
  return SPACED_COLUMN_STABILITY_CONSTANTS.BUCKLING_COEFFICIENT * eMin / (slendernessRatioEffective ** 2);
}

/**
 * Calculates the column stability factor (Cp) for spaced columns
 * 
 * @param fcStar - Reference compression design value parallel to grain (psi)
 * @param fcE - Critical buckling design value (psi)
 * @param materialType - Material type for c factor determination
 * @returns Column stability factor
 */
export function calculateColumnStabilityFactor(
  fcStar: number,
  fcE: number,
  materialType: 'sawn_lumber' | 'glulam_structural_composite'
): { cp: number; calculation: any } {
  // Determine c factor based on material type
  const c = materialType === 'sawn_lumber' 
    ? SPACED_COLUMN_STABILITY_CONSTANTS.C_SAWN_LUMBER
    : SPACED_COLUMN_STABILITY_CONSTANTS.C_GLULAM_STRUCTURAL_COMPOSITE;

  // Calculate intermediate values
  const fcEOverFcStar = fcE / fcStar;
  const term1 = 1 + fcEOverFcStar;
  const term2 = Math.sqrt((1 + fcEOverFcStar) ** 2 - (fcEOverFcStar / c));
  
  // Cp = (1 + FcE/Fc*) / (2c) - sqrt[((1 + FcE/Fc*) / (2c))² - (FcE/Fc*) / c]
  const numerator = term1 / (2 * c);
  const denominator = term2 / (2 * c);
  const cp = numerator - denominator;

  return {
    cp: Math.min(cp, 1.0), // Cp cannot exceed 1.0
    calculation: {
      cFactor: c,
      fcEOverFcStar,
      numerator,
      denominator,
      term1,
      term2,
    },
  };
}

/**
 * Performs complete spaced column analysis
 * 
 * @param input - Spaced column input parameters
 * @returns Complete spaced column analysis results
 * 
 * @example
 * ```typescript
 * const input: SpacedColumnInput = {
 *   speciesGroup: SPACED_COLUMN_SPECIES_GROUPS.B,
 *   lateralSupportDistance: 144, // 12 feet
 *   crossSectionalDimension: 3.5, // 2x4 actual
 *   spacerBlockDistance: 2.0,
 *   columnLength: 144,
 *   endFixityCondition: END_FIXITY_CONDITIONS.CONDITION_A,
 *   fcStar: 1500, // psi
 *   eMin: 1300000, // psi
 *   materialType: 'sawn_lumber'
 * };
 * 
 * const result = calculateSpacedColumn(input);
 * console.log(`Column stability factor: ${result.columnStabilityFactor}`);
 * console.log(`Adjusted compression value: ${result.adjustedCompressionValue} psi`);
 * ```
 */
export function calculateSpacedColumn(input: SpacedColumnInput): SpacedColumnResult {
  // Validate input
  const validation = validateSpacedColumnInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input: ${validation.errors.join(', ')}`);
  }

  // Calculate slenderness ratio
  const slendernessRatio = input.lateralSupportDistance / input.crossSectionalDimension;

  // Calculate end spacer block constant
  const endSpacerBlockConstant = calculateEndSpacerBlockConstant(input.speciesGroup, slendernessRatio);

  // Calculate effective length factor
  const effectiveLengthFactor = calculateEffectiveLengthFactor(input.endFixityCondition);

  // Calculate effective length
  const effectiveLength = effectiveLengthFactor * input.columnLength;

  // Calculate critical buckling design value
  const criticalBucklingValue = calculateCriticalBucklingValue(
    input.eMin,
    effectiveLength,
    input.spacerBlockDistance
  );

  // Calculate column stability factor
  const stabilityResult = calculateColumnStabilityFactor(
    input.fcStar,
    criticalBucklingValue,
    input.materialType
  );

  // Calculate adjusted compression value
  const adjustedCompressionValue = input.fcStar * stabilityResult.cp;

  const calculation: SpacedColumnCalculation = {
    endSpacerBlockConstant,
    slendernessRatio,
    effectiveLengthFactor,
    effectiveLength,
    criticalBucklingValue,
    intermediateValues: stabilityResult.calculation,
  };

  return {
    columnStabilityFactor: stabilityResult.cp,
    adjustedCompressionValue,
    endSpacerBlockConstant,
    calculation,
    analysis: {
      speciesGroup: input.speciesGroup,
      endFixityCondition: input.endFixityCondition,
      materialType: input.materialType,
      slendernessRatio,
    },
  };
}

/**
 * Optimizes spaced column design by comparing different configurations
 * 
 * @param baseInput - Base spaced column parameters
 * @param variations - Array of parameter variations to analyze
 * @returns Optimization results with best configuration
 */
export function optimizeSpacedColumnDesign(
  baseInput: SpacedColumnInput,
  variations: Partial<SpacedColumnInput>[]
): {
  best: SpacedColumnResult;
  configurations: Array<{ input: SpacedColumnInput; result: SpacedColumnResult; efficiency: number }>;
} {
  const configurations = variations.map(variation => {
    const input = { ...baseInput, ...variation };
    const result = calculateSpacedColumn(input);
    const efficiency = result.adjustedCompressionValue; // Higher is better
    
    return { input, result, efficiency };
  });

  // Sort by efficiency (highest adjusted compression value)
  configurations.sort((a, b) => b.efficiency - a.efficiency);

  return {
    best: configurations[0].result,
    configurations,
  };
}

/**
 * Checks compliance with NDS provisions for spaced columns
 * 
 * @param input - Spaced column input parameters
 * @returns Compliance check results
 */
export function checkSpacedColumnCompliance(input: SpacedColumnInput): {
  compliant: boolean;
  violations: string[];
  recommendations: string[];
} {
  const violations: string[] = [];
  const recommendations: string[] = [];

  // Check slenderness ratio limit (NDS 15.2.3.2)
  const slendernessRatio = input.lateralSupportDistance / input.crossSectionalDimension;
  if (slendernessRatio > 80) {
    violations.push(`Slenderness ratio ℓ₁/d₁ = ${slendernessRatio.toFixed(1)} exceeds maximum of 80`);
  }

  // Check individual member slenderness (ℓ₂/d₂ ≤ 50)
  // Note: This would require additional input parameters for ℓ₂ and d₂
  
  // Check spacer block spacing requirements (ℓ₃/d₁ ≤ 40)
  // Note: This would require additional input parameters for ℓ₃

  // Recommendations for efficiency
  if (slendernessRatio > 40) {
    recommendations.push('Consider reducing member spacing or increasing cross-sectional dimension');
  }

  if (input.endFixityCondition === END_FIXITY_CONDITIONS.CONDITION_B) {
    recommendations.push('Consider improving end fixity (Condition A) for better stability factor');
  }

  return {
    compliant: violations.length === 0,
    violations,
    recommendations,
  };
} 