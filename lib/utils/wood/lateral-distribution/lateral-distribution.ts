/**
 * NDS 15.1 Lateral Distribution of a Concentrated Load
 * 
 * This module implements the lateral distribution provisions from NDS Section 15.1
 * for distributing concentrated loads to adjacent parallel beams through wood
 * or concrete-slab floors.
 * 
 * Based on NDS 2018 Section 15.1
 */

/**
 * Floor construction types that affect lateral distribution factors
 */
export const FLOOR_CONSTRUCTION_TYPES = {
  PLANK_2_INCH: '2_inch_plank',
  NAIL_LAMINATED_4_INCH: '4_inch_nail_laminated', 
  NAIL_LAMINATED_6_INCH: '6_inch_nail_laminated',
  CONCRETE_STRUCTURALLY_DESIGNED: 'concrete_structurally_designed',
} as const;

export type FloorConstructionType = typeof FLOOR_CONSTRUCTION_TYPES[keyof typeof FLOOR_CONSTRUCTION_TYPES];

/**
 * Load application positions for lateral distribution calculations
 */
export const LOAD_APPLICATION_POSITIONS = {
  CENTER_OF_SPAN: 'center_of_span',
  QUARTER_POINT: 'quarter_point',
} as const;

export type LoadApplicationPosition = typeof LOAD_APPLICATION_POSITIONS[keyof typeof LOAD_APPLICATION_POSITIONS];

/**
 * Lateral Distribution Factors for Moment from NDS Table 15.1.1
 * Load on Critical Beam (for one traffic lane)
 */
export const LATERAL_DISTRIBUTION_FACTORS_MOMENT = {
  [FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH]: {
    factor: 'S/4.0',
    description: '2" plank',
  },
  [FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH]: {
    factor: 'S/4.5',
    description: '4" nail laminated',
  },
  [FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_6_INCH]: {
    factor: 'S/5.0', 
    description: '6" nail laminated',
  },
  [FLOOR_CONSTRUCTION_TYPES.CONCRETE_STRUCTURALLY_DESIGNED]: {
    factor: 'S/6.0',
    description: 'Concrete, structurally designed',
  },
} as const;

/**
 * Lateral Distribution in Terms of Proportion of Total Load from NDS Table 15.1.2
 */
export const LATERAL_DISTRIBUTION_PROPORTIONS = {
  [LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN]: {
    1.00: { centerBeam: 1.00, sideBeams: 0 },
    0.90: { centerBeam: 0.90, sideBeams: 0.10 },
    0.80: { centerBeam: 0.80, sideBeams: 0.20 },
    0.70: { centerBeam: 0.70, sideBeams: 0.30 },
    0.60: { centerBeam: 0.60, sideBeams: 0.40 },
    0.50: { centerBeam: 0.50, sideBeams: 0.50 },
    0.40: { centerBeam: 0.40, sideBeams: 0.60 },
    0.33: { centerBeam: 0.33, sideBeams: 0.67 },
  },
  [LOAD_APPLICATION_POSITIONS.QUARTER_POINT]: {
    1.00: { centerBeam: 1.00, sideBeams: 0 },
    0.94: { centerBeam: 0.94, sideBeams: 0.06 },
    0.87: { centerBeam: 0.87, sideBeams: 0.13 },
    0.79: { centerBeam: 0.79, sideBeams: 0.21 },
    0.69: { centerBeam: 0.69, sideBeams: 0.31 },
    0.58: { centerBeam: 0.58, sideBeams: 0.42 },
    0.44: { centerBeam: 0.44, sideBeams: 0.56 },
    0.33: { centerBeam: 0.33, sideBeams: 0.67 },
  },
} as const;

/**
 * Input parameters for lateral distribution factor calculation
 */
export interface LateralDistributionInput {
  /** Floor construction type */
  floorType: FloorConstructionType;
  /** Average spacing of beams in feet */
  beamSpacing: number;
  /** Total concentrated load in pounds */
  totalLoad: number;
  /** Position where load is applied */
  loadPosition: LoadApplicationPosition;
  /** Proportion factor for center beam (0.33 to 1.00) */
  centerBeamProportion?: number;
}

/**
 * Validation result for lateral distribution input
 */
export interface LateralDistributionValidation {
  /** Whether the input is valid */
  isValid: boolean;
  /** Validation error messages */
  errors: string[];
  /** Validation warnings */
  warnings: string[];
}

/**
 * Result of lateral distribution calculation
 */
export interface LateralDistributionResult {
  /** Load on the center (critical) beam in pounds */
  centerBeamLoad: number;
  /** Load distributed to each side beam in pounds */
  sideBeamLoad: number;
  /** Total number of side beams receiving load */
  numberOfSideBeams: number;
  /** Distribution factor used */
  distributionFactor: string;
  /** Proportion factors used */
  proportions: {
    centerBeam: number;
    sideBeams: number;
  };
  /** Analysis metadata */
  analysis: {
    floorType: FloorConstructionType;
    loadPosition: LoadApplicationPosition;
    beamSpacing: number;
    totalLoad: number;
  };
}

/**
 * Validates lateral distribution input parameters
 * 
 * @param input - Lateral distribution input parameters
 * @returns Validation result with errors and warnings
 */
export function validateLateralDistributionInput(input: LateralDistributionInput): LateralDistributionValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate floor type
  if (!Object.values(FLOOR_CONSTRUCTION_TYPES).includes(input.floorType)) {
    errors.push(`Invalid floor construction type: ${input.floorType}`);
  }

  // Validate beam spacing
  if (input.beamSpacing <= 0) {
    errors.push('Beam spacing must be greater than 0');
  }

  if (input.beamSpacing > 50) {
    warnings.push('Beam spacing exceeds typical range (>50 ft). Verify load distribution assumptions.');
  }

  // Validate total load
  if (input.totalLoad <= 0) {
    errors.push('Total load must be greater than 0');
  }

  // Validate load position
  if (!Object.values(LOAD_APPLICATION_POSITIONS).includes(input.loadPosition)) {
    errors.push(`Invalid load application position: ${input.loadPosition}`);
  }

  // Validate center beam proportion if provided
  if (input.centerBeamProportion !== undefined) {
    if (input.centerBeamProportion < 0.33 || input.centerBeamProportion > 1.0) {
      errors.push('Center beam proportion must be between 0.33 and 1.0');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Calculates the lateral distribution factor for moment
 * 
 * @param floorType - Type of floor construction
 * @param beamSpacing - Average spacing of beams in feet
 * @returns Distribution factor value
 */
export function calculateDistributionFactor(
  floorType: FloorConstructionType,
  beamSpacing: number
): number {
  const factorData = LATERAL_DISTRIBUTION_FACTORS_MOMENT[floorType];
  
  // Extract denominator from factor string (e.g., "S/4.0" -> 4.0)
  const denominator = parseFloat(factorData.factor.split('/')[1]);
  
  return beamSpacing / denominator;
}

/**
 * Gets the load proportion factors based on center beam proportion
 * 
 * @param loadPosition - Position where load is applied
 * @param centerBeamProportion - Desired proportion for center beam (0.33 to 1.0)
 * @returns Proportion factors for center beam and side beams
 */
export function getLoadProportions(
  loadPosition: LoadApplicationPosition,
  centerBeamProportion: number
): { centerBeam: number; sideBeams: number } {
  const proportionsTable = LATERAL_DISTRIBUTION_PROPORTIONS[loadPosition];
  
  // Find the closest match in the table
  const availableProportions = Object.keys(proportionsTable).map(Number).sort((a, b) => b - a);
  
  let closestProportion = availableProportions[0];
  let minDifference = Math.abs(centerBeamProportion - closestProportion);
  
  for (const proportion of availableProportions) {
    const difference = Math.abs(centerBeamProportion - proportion);
    if (difference < minDifference) {
      minDifference = difference;
      closestProportion = proportion;
    }
  }
  
  return proportionsTable[closestProportion as keyof typeof proportionsTable];
}

/**
 * Calculates lateral load distribution for concentrated loads
 * 
 * @param input - Lateral distribution input parameters
 * @returns Lateral distribution calculation results
 * 
 * @example
 * ```typescript
 * const input: LateralDistributionInput = {
 *   floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH,
 *   beamSpacing: 16, // feet
 *   totalLoad: 10000, // pounds
 *   loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
 *   centerBeamProportion: 0.70
 * };
 * 
 * const result = calculateLateralDistribution(input);
 * console.log(`Center beam load: ${result.centerBeamLoad} lbs`);
 * console.log(`Each side beam load: ${result.sideBeamLoad} lbs`);
 * ```
 */
export function calculateLateralDistribution(input: LateralDistributionInput): LateralDistributionResult {
  // Validate input
  const validation = validateLateralDistributionInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input: ${validation.errors.join(', ')}`);
  }

  // Use provided proportion or default to maximum distribution (1.0)
  const centerBeamProportion = input.centerBeamProportion ?? 1.0;
  
  // Get load proportions
  const proportions = getLoadProportions(input.loadPosition, centerBeamProportion);
  
  // Calculate loads
  const centerBeamLoad = input.totalLoad * proportions.centerBeam;
  const totalSideBeamsLoad = input.totalLoad * proportions.sideBeams;
  
  // Determine number of side beams (typically 2 for standard configuration)
  const numberOfSideBeams = proportions.sideBeams > 0 ? 2 : 0;
  const sideBeamLoad = numberOfSideBeams > 0 ? totalSideBeamsLoad / numberOfSideBeams : 0;
  
  // Get distribution factor
  const distributionFactor = LATERAL_DISTRIBUTION_FACTORS_MOMENT[input.floorType].factor;

  return {
    centerBeamLoad,
    sideBeamLoad,
    numberOfSideBeams,
    distributionFactor,
    proportions,
    analysis: {
      floorType: input.floorType,
      loadPosition: input.loadPosition,
      beamSpacing: input.beamSpacing,
      totalLoad: input.totalLoad,
    },
  };
}

/**
 * Applies lateral distribution for shear calculations
 * Note: NDS 15.1.2 states that for shear, loads at or near quarter point
 * should use the corresponding values from the last two columns of Table 15.1.2
 * 
 * @param input - Lateral distribution input parameters
 * @returns Lateral distribution results for shear design
 */
export function calculateLateralDistributionForShear(input: LateralDistributionInput): LateralDistributionResult {
  // For shear calculations, if load is at center, use quarter point distribution
  const shearInput: LateralDistributionInput = {
    ...input,
    loadPosition: input.loadPosition === LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN 
      ? LOAD_APPLICATION_POSITIONS.QUARTER_POINT 
      : input.loadPosition,
  };

  return calculateLateralDistribution(shearInput);
}

/**
 * Comprehensive lateral distribution analysis
 * 
 * @param input - Lateral distribution input parameters
 * @returns Complete analysis for both moment and shear
 */
export function performLateralDistributionAnalysis(input: LateralDistributionInput) {
  const validation = validateLateralDistributionInput(input);
  
  const momentDistribution = calculateLateralDistribution(input);
  const shearDistribution = calculateLateralDistributionForShear(input);

  return {
    validation,
    momentDistribution,
    shearDistribution,
    recommendations: {
      floorConstruction: LATERAL_DISTRIBUTION_FACTORS_MOMENT[input.floorType].description,
      criticalDesignCase: momentDistribution.centerBeamLoad > shearDistribution.centerBeamLoad 
        ? 'moment' : 'shear',
    },
  };
} 