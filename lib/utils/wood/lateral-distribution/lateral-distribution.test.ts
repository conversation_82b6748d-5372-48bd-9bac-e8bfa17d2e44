/**
 * Tests for NDS 15.1 Lateral Distribution of a Concentrated Load
 */

import {
  FLOOR_CONSTRUCTION_TYPES,
  LOAD_APPLICATION_POSITIONS,
  validateLateralDistributionInput,
  calculateDistributionFactor,
  getLoadProportions,
  calculateLateralDistribution,
  calculateLateralDistributionForShear,
  performLateralDistributionAnalysis,
  type LateralDistributionInput,
} from './lateral-distribution';

describe('NDS 15.1 Lateral Distribution of Concentrated Load', () => {
  describe('validateLateralDistributionInput', () => {
    it('should validate correct input', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH,
        beamSpacing: 16,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
      };

      const result = validateLateralDistributionInput(input);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid floor type', () => {
      const input: LateralDistributionInput = {
        floorType: 'invalid_type' as any,
        beamSpacing: 16,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
      };

      const result = validateLateralDistributionInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid floor construction type: invalid_type');
    });

    it('should reject negative beam spacing', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: -5,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
      };

      const result = validateLateralDistributionInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Beam spacing must be greater than 0');
    });

    it('should reject invalid center beam proportion', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: 16,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 1.5,
      };

      const result = validateLateralDistributionInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Center beam proportion must be between 0.33 and 1.0');
    });

    it('should warn about excessive beam spacing', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: 60,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
      };

      const result = validateLateralDistributionInput(input);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Beam spacing exceeds typical range (>50 ft). Verify load distribution assumptions.');
    });
  });

  describe('calculateDistributionFactor', () => {
    it('should calculate distribution factor for 2" plank', () => {
      const factor = calculateDistributionFactor(FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH, 16);
      expect(factor).toBe(16 / 4.0); // S/4.0
      expect(factor).toBe(4.0);
    });

    it('should calculate distribution factor for 4" nail laminated', () => {
      const factor = calculateDistributionFactor(FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH, 20);
      expect(factor).toBe(20 / 4.5); // S/4.5
      expect(factor).toBeCloseTo(4.44);
    });

    it('should calculate distribution factor for 6" nail laminated', () => {
      const factor = calculateDistributionFactor(FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_6_INCH, 24);
      expect(factor).toBe(24 / 5.0); // S/5.0
      expect(factor).toBe(4.8);
    });

    it('should calculate distribution factor for concrete', () => {
      const factor = calculateDistributionFactor(FLOOR_CONSTRUCTION_TYPES.CONCRETE_STRUCTURALLY_DESIGNED, 18);
      expect(factor).toBe(18 / 6.0); // S/6.0
      expect(factor).toBe(3.0);
    });
  });

  describe('getLoadProportions', () => {
    it('should return exact proportion for center of span', () => {
      const proportions = getLoadProportions(LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN, 0.70);
      expect(proportions.centerBeam).toBe(0.70);
      expect(proportions.sideBeams).toBe(0.30);
    });

    it('should return exact proportion for quarter point', () => {
      const proportions = getLoadProportions(LOAD_APPLICATION_POSITIONS.QUARTER_POINT, 0.79);
      expect(proportions.centerBeam).toBe(0.79);
      expect(proportions.sideBeams).toBe(0.21);
    });

    it('should find closest proportion when exact match not available', () => {
      const proportions = getLoadProportions(LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN, 0.75);
      // Should find closest match (0.70 or 0.80)
      expect([0.70, 0.80]).toContain(proportions.centerBeam);
    });

    it('should handle minimum proportion', () => {
      const proportions = getLoadProportions(LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN, 0.33);
      expect(proportions.centerBeam).toBe(0.33);
      expect(proportions.sideBeams).toBe(0.67);
    });

    it('should handle maximum proportion', () => {
      const proportions = getLoadProportions(LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN, 1.0);
      expect(proportions.centerBeam).toBe(1.00);
      expect(proportions.sideBeams).toBe(0);
    });
  });

  describe('calculateLateralDistribution', () => {
    it('should calculate distribution with full load on center beam', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH,
        beamSpacing: 16,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 1.0,
      };

      const result = calculateLateralDistribution(input);
      
      expect(result.centerBeamLoad).toBe(10000);
      expect(result.sideBeamLoad).toBe(0);
      expect(result.numberOfSideBeams).toBe(0);
      expect(result.proportions.centerBeam).toBe(1.0);
      expect(result.proportions.sideBeams).toBe(0);
      expect(result.distributionFactor).toBe('S/4.5');
    });

         it('should calculate distribution with load shared between beams', () => {
       const input: LateralDistributionInput = {
         floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: 12,
        totalLoad: 8000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 0.70,
      };

      const result = calculateLateralDistribution(input);
      
      expect(result.centerBeamLoad).toBe(5600); // 8000 * 0.70
      expect(result.sideBeamLoad).toBe(1200); // (8000 * 0.30) / 2
      expect(result.numberOfSideBeams).toBe(2);
      expect(result.proportions.centerBeam).toBe(0.70);
      expect(result.proportions.sideBeams).toBe(0.30);
    });

    it('should calculate quarter point distribution', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.CONCRETE_STRUCTURALLY_DESIGNED,
        beamSpacing: 20,
        totalLoad: 15000,
        loadPosition: LOAD_APPLICATION_POSITIONS.QUARTER_POINT,
        centerBeamProportion: 0.87,
      };

      const result = calculateLateralDistribution(input);
      
      expect(result.centerBeamLoad).toBe(13050); // 15000 * 0.87
      expect(result.sideBeamLoad).toBe(975); // (15000 * 0.13) / 2
      expect(result.numberOfSideBeams).toBe(2);
    });

    it('should use default proportion when not specified', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_6_INCH,
        beamSpacing: 24,
        totalLoad: 12000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
      };

      const result = calculateLateralDistribution(input);
      
      // Should default to 1.0 proportion (full load on center beam)
      expect(result.centerBeamLoad).toBe(12000);
      expect(result.sideBeamLoad).toBe(0);
      expect(result.proportions.centerBeam).toBe(1.0);
    });

    it('should throw error for invalid input', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: -10,
        totalLoad: 5000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
      };

      expect(() => calculateLateralDistribution(input)).toThrow('Invalid input');
    });
  });

  describe('calculateLateralDistributionForShear', () => {
    it('should use quarter point distribution for center span loads', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH,
        beamSpacing: 16,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 0.90,
      };

      const result = calculateLateralDistributionForShear(input);
      
      // Should use quarter point proportions instead of center span
      // For 0.90 center proportion at quarter point: closest is 0.87
      expect(result.proportions.centerBeam).toBe(0.87);
      expect(result.proportions.sideBeams).toBe(0.13);
      expect(result.centerBeamLoad).toBe(8700); // 10000 * 0.87
    });

    it('should preserve quarter point loads as-is', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: 12,
        totalLoad: 8000,
        loadPosition: LOAD_APPLICATION_POSITIONS.QUARTER_POINT,
        centerBeamProportion: 0.79,
      };

      const result = calculateLateralDistributionForShear(input);
      
      // Should remain quarter point distribution
      expect(result.proportions.centerBeam).toBe(0.79);
      expect(result.proportions.sideBeams).toBe(0.21);
    });
  });

  describe('performLateralDistributionAnalysis', () => {
    it('should provide complete analysis', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_4_INCH,
        beamSpacing: 16,
        totalLoad: 10000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 0.80,
      };

      const analysis = performLateralDistributionAnalysis(input);
      
      expect(analysis.validation.isValid).toBe(true);
      expect(analysis.momentDistribution).toBeDefined();
      expect(analysis.shearDistribution).toBeDefined();
      expect(analysis.recommendations).toBeDefined();
      expect(analysis.recommendations.floorConstruction).toBe('4" nail laminated');
    });

    it('should identify critical design case', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.CONCRETE_STRUCTURALLY_DESIGNED,
        beamSpacing: 20,
        totalLoad: 15000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 0.70,
      };

      const analysis = performLateralDistributionAnalysis(input);
      
      // Should identify whether moment or shear governs based on center beam loads
      expect(['moment', 'shear']).toContain(analysis.recommendations.criticalDesignCase);
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle minimum proportion (0.33)', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.PLANK_2_INCH,
        beamSpacing: 16,
        totalLoad: 6000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 0.33,
      };

      const result = calculateLateralDistribution(input);
      
      expect(result.centerBeamLoad).toBe(1980); // 6000 * 0.33
      expect(result.sideBeamLoad).toBeCloseTo(2010, 0); // (6000 * 0.67) / 2
      expect(result.proportions.centerBeam).toBe(0.33);
      expect(result.proportions.sideBeams).toBe(0.67);
    });

    it('should handle small loads', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.NAIL_LAMINATED_6_INCH,
        beamSpacing: 12,
        totalLoad: 100,
        loadPosition: LOAD_APPLICATION_POSITIONS.QUARTER_POINT,
        centerBeamProportion: 0.58,
      };

      const result = calculateLateralDistribution(input);
      
      expect(result.centerBeamLoad).toBeCloseTo(58, 0); // 100 * 0.58
      expect(result.sideBeamLoad).toBe(21); // (100 * 0.42) / 2
    });

    it('should handle large beam spacing', () => {
      const input: LateralDistributionInput = {
        floorType: FLOOR_CONSTRUCTION_TYPES.CONCRETE_STRUCTURALLY_DESIGNED,
        beamSpacing: 40,
        totalLoad: 20000,
        loadPosition: LOAD_APPLICATION_POSITIONS.CENTER_OF_SPAN,
        centerBeamProportion: 0.50,
      };

      const result = calculateLateralDistribution(input);
      
      expect(result.centerBeamLoad).toBe(10000); // 20000 * 0.50
      expect(result.sideBeamLoad).toBe(5000); // (20000 * 0.50) / 2
      expect(result.distributionFactor).toBe('S/6.0');
    });
  });
}); 