/**
 * Structural Glued Laminated Timber (Glulam) Beam Analysis
 *
 * This module provides comprehensive beam analysis for glulam members following
 * NDS 2018 Section 5 standards and Table 5.3.1 adjustment factor applicability rules.
 *
 * Key differences from sawn lumber:
 * - Directional design values (x-x strong axis, y-y weak axis)
 * - Positive and negative bending values (Fbx+, Fbx-)
 * - Additional adjustment factors: Volume Factor (CV), Curvature Factor (Cc),
 *   Stress Interaction Factor (CI), Shear Reduction Factor (Cvr)
 * - Different reference design value structure
 *
 * @fileoverview Glulam beam analysis calculations per NDS Section 5
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 * @since 2024
 *
 * @references
 * - NDS 2018 Section 5.2: Reference Design Values for Structural Glued Laminated Timber
 * - NDS 2018 Section 5.3: Adjustment of Reference Design Values
 * - NDS 2018 Table 5.3.1: Applicability of Adjustment Factors for Structural Glued Laminated Timber
 * - NDS 2018 Section 5.3.6: Volume Factor
 * - NDS 2018 Section 5.3.8: Curvature Factor
 * - NDS 2018 Section 5.3.9: Stress Interaction Factor
 * - NDS 2018 Section 5.3.10: Shear Reduction Factor
 */

import { LoadType } from "../../../types/load/load-type";

// Import adjustment factor functions
import {
  getLoadDurationFactorByLoadType,
  getWetServiceFactor,
  getTemperatureFactor,
  getIncisingFactorCi,
  getSizeFactor,
  getFlatUseFactorCfu,
  getRepetitiveMemberFactor,
  getBeamStabilityFactorCL,
  getBearingAreaFactor,
  getLrfdFormatConversionFactorKF,
  getLrfdResistanceFactorPhi,
  getLrfdTimeEffectFactorLambda,
  getVolumeFactor,
  getCurvatureFactor,
  getStressInteractionFactor,
  getShearReductionFactor,
  type WetServiceFactorInput,
  type TemperatureFactorInput,
  type SizeFactorInput,
  type FlatUseFactorInput,
  type RepetitiveMemberFactorInput,
  type VolumeFactorInput,
  type BearingAreaFactorInput,
  type CurvatureFactorInput,
  type StressInteractionFactorInput,
  type ShearReductionFactorInput,
  WET_SERVICE_LUMBER_CATEGORIES,
  VOLUME_FACTOR_PRODUCTS,
  CURVATURE_SPECIES_GROUPS,
  CURVATURE_MEMBER_TYPES,
  STRESS_INTERACTION_MEMBER_TYPES,
  SHEAR_REDUCTION_CONDITIONS,
  REPETITIVE_MEMBER_TYPES,
  REPETITIVE_MEMBER_LUMBER_CATEGORIES,
} from "../adjustment-factors";

// Import constants and types
import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  MOISTURE_CONDITIONS,
  GLULAM_CURVATURE_CONSTANTS,
  GLULAM_STRESS_INTERACTION_CONSTANTS,
  GLULAM_SHEAR_REDUCTION_CONSTANTS,
  GLULAM_MEMBER_TYPES,
  GLULAM_LOADING_TYPES,
  GLULAM_TAPER_LOCATIONS,
  MATHEMATICAL_CONSTANTS,
  ADJUSTMENT_FACTOR_CONSTANTS,
  // Import shared beam analysis constants
  DESIGN_METHODS,
  DesignMethod,
  GLULAM_AXIS,
  GlulamAxis,
  BENDING_STRESS_TYPES,
  BendingStressType,
  LAYUP_TYPES,
  LayupType,
  DEFAULT_DEFLECTION_LIMITS,
  SECTION_PROPERTY_CONSTANTS,
  BEAM_ANALYSIS_LIMITS,
  ANALYSIS_STATUS,
  CONTROLLING_CRITERIA,
  type GlulamMemberType,
  type GlulamLoadingType,
  type GlulamTaperLocation,
} from "../constants";

// Glulam-specific constants
const GLULAM_CONSTANTS = {
  // Unit conversion
  INCHES_TO_FEET: 12,

  // Thresholds
  MOISTURE_CONTENT_THRESHOLD: 19, // % - threshold for wet service conditions
  TEMPERATURE_THRESHOLD: 100, // °F - threshold for temperature factor application

  // Default values for calculations
  STRAIGHT_MEMBER_RADIUS: 1000000, // Very large radius for straight members (in)
  DEFAULT_STRESS_VALUE: 1000, // Default stress value for non-tapered members (psi)
  DEFAULT_LAMINATION_THICKNESS: 1, // Default lamination thickness for straight members (in)

  // Repetitive member defaults
  DEFAULT_REPETITIVE_SPACING: 16, // inches on center
  DEFAULT_REPETITIVE_COUNT: 3, // number of members

  // Simplified constants for lumber grades and species
  SIMPLIFIED_LUMBER_GRADE: "SELECT_STRUCTURAL",
  SIMPLIFIED_SPECIES_GROUP: "other_species",
  SIMPLIFIED_LOAD_DISTRIBUTING_ELEMENT: "flooring",
} as const;

// Sample glulam member constants
const SAMPLE_GLULAM_CONSTANTS = {
  // Dimensions
  LENGTH: 240, // inches (20 ft)
  WIDTH: 5.125, // inches
  DEPTH: 18, // inches
  UNBRACED_LENGTH: 240, // inches

  // Material properties
  SPECIES: "Douglas Fir-Larch",
  GRADE: "24F-V4",
  LAYUP_GRADE: "V4",
  COMBINATION: "DF-L",

  // Service conditions
  MOISTURE_CONTENT: 12, // %
  TEMPERATURE: 70, // °F

  // Analysis results
  MOMENT: 800000, // in-lb
  SHEAR: 6000, // lb
  DEFLECTION: 0.9, // in

  // Design parameters
  DEFLECTION_LIMIT: 240, // L/240
} as const;

// Sample reference design values for Douglas Fir-Larch 24F-V4
const SAMPLE_REFERENCE_DESIGN_VALUES = {
  Fbx_pos: 2400, // psi
  Fbx_neg: 1950, // psi
  Fby: 1450, // psi
  Ft: 1150, // psi
  Fvx: 265, // psi
  Fvy: 265, // psi
  Fc_perp_x: 650, // psi
  Fc_perp_y: 650, // psi
  Fc: 1600, // psi
  Ex: 1800000, // psi
  Ex_min: 930000, // psi
  Ey: 1600000, // psi
  Ey_min: 850000, // psi
  G: 90000, // psi
} as const;

// Documentation constants for commonly used descriptions
const GLULAM_DOCUMENTATION = {
  ORIENTATION_DESCRIPTIONS: {
    X_AXIS_STRONG: "Load on narrow face, thickness is width",
    X_AXIS_DEPTH: "Load on narrow face, depth is depth",
    X_AXIS_WIDTH: "Load on narrow face, width is width",
    Y_AXIS_WEAK: "Load on wide face, thickness is depth",
    Y_AXIS_DEPTH: "Load on wide face, depth is width",
    Y_AXIS_WIDTH: "Load on wide face, width is depth",
  },
  AXIS_DESCRIPTIONS: {
    X_AXIS: "strong axis",
    Y_AXIS: "weak axis",
  },
  LOAD_DESCRIPTIONS: {
    NARROW_FACE: "load applied to narrow face",
    WIDE_FACE: "load applied to wide face",
  },
} as const;

// All enums (DesignMethod, GlulamAxis, BendingStressType, LayupType) are now imported from constants.ts

/**
 * Reference design values for structural glued laminated timber
 * @reference NDS 5.2.1 Reference Design Values
 */
export interface GlulamReferenceDesignValues {
  // Bending stresses (NDS 5.2.4)
  Fbx_pos: number; // Positive bending stress about x-x axis (psi)
  Fbx_neg: number; // Negative bending stress about x-x axis (psi)
  Fby: number; // Bending stress about y-y axis (psi)

  // Tension parallel to grain (NDS 5.2.1)
  Ft: number; // Tensile stress parallel to grain (psi)

  // Shear parallel to grain (NDS 5.2.6)
  Fvx: number; // Shear stress parallel to grain in x-direction (psi)
  Fvy: number; // Shear stress parallel to grain in y-direction (psi)

  // Compression perpendicular to grain (NDS 5.2.5)
  Fc_perp_x: number; // Compression perpendicular to grain, x-face (psi)
  Fc_perp_y: number; // Compression perpendicular to grain, y-face (psi)

  // Compression parallel to grain (NDS 5.2.1)
  Fc: number; // Compressive stress parallel to grain (psi)

  // Modulus of elasticity (NDS 5.2.7)
  Ex: number; // Modulus of elasticity about x-x axis (psi)
  Ex_min: number; // Minimum modulus of elasticity about x-x axis (psi)
  Ey: number; // Modulus of elasticity about y-y axis (psi)
  Ey_min: number; // Minimum modulus of elasticity about y-y axis (psi)

  // Radial stresses (curved members) (NDS 5.2.8, 5.2.9)
  Frt?: number; // Radial tension (psi) - optional for straight members
  Frc?: number; // Radial compression (psi) - optional for straight members

  // Shear modulus
  G: number; // Shear modulus (psi)
}

/**
 * Adjusted design values after applying all factors
 */
export interface GlulamAdjustedDesignValues {
  Fbx_pos_prime: number; // F'bx+
  Fbx_neg_prime: number; // F'bx-
  Fby_prime: number; // F'by
  Ft_prime: number; // F't
  Fvx_prime: number; // F'vx
  Fvy_prime: number; // F'vy
  Fc_perp_x_prime: number; // F'c⊥x
  Fc_perp_y_prime: number; // F'c⊥y
  Fc_prime: number; // F'c
  Ex_prime: number; // E'x
  Ex_min_prime: number; // E'x min
  Ey_prime: number; // E'y
  Ey_min_prime: number; // E'y min
  Frt_prime?: number; // F'rt (if applicable)
  Frc_prime?: number; // F'rc (if applicable)
}

/**
 * Member geometry and properties for glulam
 */
export interface GlulamMemberGeometry {
  length: number; // Member length (in)
  width: number; // Cross-section width (in) - breadth (b)
  depth: number; // Cross-section depth (in) - height (d)
  unbracedLength: number; // Unbraced length for lateral stability (in)

  // Curved member properties (optional)
  radiusOfCurvature?: number; // Radius of curvature for curved members (in)
  laminationThickness?: number; // Thickness of individual laminations (in)

  // Taper properties (optional)
  taperAngle?: number; // Taper angle in degrees (for tapered members)
  taperLocation?: GlulamTaperLocation; // Which face is tapered
}

/**
 * Service conditions affecting the glulam member
 */
export interface GlulamServiceConditions {
  moistureContent: number; // Moisture content (%)
  temperature: number; // Service temperature (°F)
  loadDuration: LoadType; // Load duration category
  isFlatUse: boolean; // Flat use orientation (load applied to narrow face)
  isRepetitiveMember: boolean; // Repetitive member application
  isIncised: boolean; // Whether member is incised

  // Repetitive member parameters (when isRepetitiveMember is true)
  repetitiveMemberSpacing?: number; // Spacing on center (in) - defaults to 16" if not provided
  repetitiveMemberCount?: number; // Number of members - defaults to 3 if not provided

  // Glulam-specific conditions
  layupType: LayupType; // Balanced or unbalanced layup
  primaryAxis: GlulamAxis; // Primary bending axis (x or y)
}

/**
 * Material properties including species and grade for glulam
 */
export interface GlulamMaterialProperties {
  species: string; // e.g., "Douglas Fir-Larch", "Southern Pine"
  grade: string; // e.g., "24F-V4", "20F-E1"
  layupGrade: string; // Layup grade designation
  combination: string; // Species combination
  referenceDesignValues: GlulamReferenceDesignValues;
}

/**
 * Calculated adjustment factors per NDS Table 5.3.1
 * @reference NDS Table 5.3.1 Applicability of Adjustment Factors
 */
export interface GlulamAdjustmentFactors {
  // Standard factors
  CD: number; // Load duration factor (ASD only)
  CM: number; // Wet service factor
  Ct: number; // Temperature factor
  CL: number; // Beam stability factor
  Cfu: number; // Flat use factor
  Cb: number; // Bearing area factor

  // Glulam-specific factors
  CV: number; // Volume factor (NDS 5.3.6)
  Cc: number; // Curvature factor (NDS 5.3.8)
  CI: number; // Stress interaction factor (NDS 5.3.9)
  Cvr: number; // Shear reduction factor (NDS 5.3.10)
  CP: number; // Column stability factor (for compression)

  // LRFD factors
  KF: number; // Format conversion factor
  phi: number; // Resistance factor
  lambda: number; // Time effect factor

  // Repetitive Member Factor (Cr) - applies to bending design values for repetitive members
  Cr: number;

  // Incising Factor (Ci) - applies to most design values when member is incised
  Ci: number;
}

/**
 * Input structural analysis results (calculated externally)
 */
export interface InputAnalysisResults {
  maxMoment: number; // **IN-LB** - Maximum moment from structural analysis
  maxShear: number; // **LB** - Maximum shear from structural analysis
  maxDeflection: number; // **IN** - Maximum deflection from structural analysis
  bendingStressType?: BendingStressType; // Type of bending stress (positive/negative)
}

/**
 * Analysis results with calculated stress ratios
 */
export interface GlulamAnalysisResults extends InputAnalysisResults {
  maxBendingStress: number; // **PSI** - Calculated bending stress
  maxShearStress: number; // **PSI** - Calculated shear stress
  bendingStressRatio: number; // Actual/allowable bending stress ratio
  shearStressRatio: number; // Actual/allowable shear stress ratio
  deflectionRatio: number; // Actual/allowable deflection ratio
}

/**
 * Complete input interface for glulam beam analysis
 */
export interface GlulamBeamAnalysisInputs {
  member: GlulamMemberGeometry;
  material: GlulamMaterialProperties;
  serviceConditions: GlulamServiceConditions;
  analysisResults: InputAnalysisResults; // Pre-calculated structural results
  designMethod: DesignMethod;
  deflectionLimit: number; // L/deflectionLimit (e.g., 240 for L/240)
  includeSelfWeight?: boolean; // Optional flag for documentation
}

/**
 * Complete glulam beam analysis results
 */
export interface GlulamBeamAnalysisResults {
  // Input summary
  inputs: GlulamBeamAnalysisInputs;

  // Adjusted design values
  adjustedDesignValues: GlulamAdjustedDesignValues;

  // Design-value-specific adjustment factors
  designValueSpecificFactors: {
    Fbx_pos: Partial<GlulamAdjustmentFactors>;
    Fbx_neg: Partial<GlulamAdjustmentFactors>;
    Fby: Partial<GlulamAdjustmentFactors>;
    Ft: Partial<GlulamAdjustmentFactors>;
    Fvx: Partial<GlulamAdjustmentFactors>;
    Fvy: Partial<GlulamAdjustmentFactors>;
    Fc_perp_x: Partial<GlulamAdjustmentFactors>;
    Fc_perp_y: Partial<GlulamAdjustmentFactors>;
    Fc: Partial<GlulamAdjustmentFactors>;
    Ex: Partial<GlulamAdjustmentFactors>;
    Ex_min: Partial<GlulamAdjustmentFactors>;
    Ey: Partial<GlulamAdjustmentFactors>;
    Ey_min: Partial<GlulamAdjustmentFactors>;
    Frt?: Partial<GlulamAdjustmentFactors>;
    Frc?: Partial<GlulamAdjustmentFactors>;
  };

  // Structural analysis results
  analysisResults: GlulamAnalysisResults;

  // Design verification
  isDesignAcceptable: boolean;
  controllingCriteria: string;

  // Detailed checks
  bendingCheck: {
    allowableStress: number;
    actualStress: number;
    ratio: number;
    passes: boolean;
    stressType: BendingStressType;
  };

  shearCheck: {
    allowableStress: number;
    actualStress: number;
    ratio: number;
    passes: boolean;
  };

  deflectionCheck: {
    allowableDeflection: number;
    actualDeflection: number;
    ratio: number;
    passes: boolean;
  };
}

/**
 * Result of glulam adjusted design values calculation with design-value-specific factors
 */
export interface GlulamAdjustedDesignValuesResult {
  adjustedDesignValues: GlulamAdjustedDesignValues;
  designValueSpecificFactors: {
    Fbx_pos: Partial<GlulamAdjustmentFactors>;
    Fbx_neg: Partial<GlulamAdjustmentFactors>;
    Fby: Partial<GlulamAdjustmentFactors>;
    Ft: Partial<GlulamAdjustmentFactors>;
    Fvx: Partial<GlulamAdjustmentFactors>;
    Fvy: Partial<GlulamAdjustmentFactors>;
    Fc_perp_x: Partial<GlulamAdjustmentFactors>;
    Fc_perp_y: Partial<GlulamAdjustmentFactors>;
    Fc: Partial<GlulamAdjustmentFactors>;
    Ex: Partial<GlulamAdjustmentFactors>;
    Ex_min: Partial<GlulamAdjustmentFactors>;
    Ey: Partial<GlulamAdjustmentFactors>;
    Ey_min: Partial<GlulamAdjustmentFactors>;
    Frt?: Partial<GlulamAdjustmentFactors>;
    Frc?: Partial<GlulamAdjustmentFactors>;
  };
}

/**
 * Calculate all applicable adjustment factors for glulam based on NDS Table 5.3.1
 * @reference NDS Table 5.3.1 Applicability of Adjustment Factors
 */
export function calculateGlulamAdjustmentFactors(
  material: GlulamMaterialProperties,
  member: GlulamMemberGeometry,
  serviceConditions: GlulamServiceConditions,
  designMethod: DesignMethod
): GlulamAdjustmentFactors {
  // Initialize factors to 1.0 (no adjustment)
  const factors: GlulamAdjustmentFactors = {
    CD: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    CM: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    Ct: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    CL: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    Cfu: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    Cb: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    CV: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    Cc: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    CI: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    Cvr: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    CP: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    KF: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    phi: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    lambda: ADJUSTMENT_FACTOR_CONSTANTS.NO_ADJUSTMENT,
    Cr: 1.0,
    Ci: 1.0,
  };

  // Calculate oriented dimensions based on primary axis for glulam
  // When primary axis is X (strong axis), load is applied to narrow face: width=member.width, depth=member.depth
  // When primary axis is Y (weak axis), load is applied to wide face: width=member.depth, depth=member.width
  const orientedWidth =
    serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
      ? member.width // Load on narrow face, width is width
      : member.depth; // Load on wide face, width is depth
  const orientedDepth =
    serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
      ? member.depth // Load on narrow face, depth is depth
      : member.width; // Load on wide face, depth is width

  // Load Duration Factor (CD) - applies to all stress design values except E and Emin
  if (designMethod === DESIGN_METHODS.ASD) {
    factors.CD = getLoadDurationFactorByLoadType(
      serviceConditions.loadDuration
    );
  }

  // Wet Service Factor (CM) - applies when moisture content > 19%
  if (
    serviceConditions.moistureContent >
    GLULAM_CONSTANTS.MOISTURE_CONTENT_THRESHOLD
  ) {
    const wetServiceInput: WetServiceFactorInput = {
      moistureContent: serviceConditions.moistureContent,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER,
      speciesGroup: GLULAM_CONSTANTS.SIMPLIFIED_SPECIES_GROUP as any, // Simplified for now
    };
    factors.CM = getWetServiceFactor(wetServiceInput);
  }

  // Temperature Factor (Ct) - applies when temperature > 100°F
  if (serviceConditions.temperature > GLULAM_CONSTANTS.TEMPERATURE_THRESHOLD) {
    const tempInput: TemperatureFactorInput = {
      temperature: serviceConditions.temperature,
      moistureCondition:
        serviceConditions.moistureContent >
        GLULAM_CONSTANTS.MOISTURE_CONTENT_THRESHOLD
          ? MOISTURE_CONDITIONS.WET
          : MOISTURE_CONDITIONS.DRY,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    factors.Ct = getTemperatureFactor(tempInput);
  }

  // Flat Use Factor (Cfu) - applies to bending when loaded on narrow face
  if (serviceConditions.isFlatUse) {
    const flatUseInput: FlatUseFactorInput = {
      width: orientedWidth,
      depth: orientedDepth,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: GLULAM_CONSTANTS.SIMPLIFIED_LUMBER_GRADE as any, // Simplified
      isLoadOnWideFace: false, // Flat use means load on narrow face
    };
    factors.Cfu = getFlatUseFactorCfu(flatUseInput);
  }

  // Volume Factor (CV) - applies to bending design values for glulam
  const volumeInput: VolumeFactorInput = {
    length: member.length / GLULAM_CONSTANTS.INCHES_TO_FEET, // Convert to feet
    width: orientedWidth,
    depth: orientedDepth,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
  };
  factors.CV = getVolumeFactor(volumeInput);

  // Repetitive Member Factor (Cr) - applies to bending design values for repetitive members
  if (serviceConditions.isRepetitiveMember) {
    // For glulam, the "thickness" for repetitive member factor depends on the loading orientation
    // When primary axis is X (strong axis), load is applied to narrow face, so thickness = width (b)
    // When primary axis is Y (weak axis), load is applied to wide face, so thickness = depth (d)
    const thickness =
      serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
        ? member.width // Load on narrow face, thickness is width
        : member.depth; // Load on wide face, thickness is depth

    const repInput: RepetitiveMemberFactorInput = {
      thickness: thickness,
      memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
      spacingOnCenter:
        serviceConditions.repetitiveMemberSpacing ||
        GLULAM_CONSTANTS.DEFAULT_REPETITIVE_SPACING, // Use user input or default
      numberOfMembers:
        serviceConditions.repetitiveMemberCount ||
        GLULAM_CONSTANTS.DEFAULT_REPETITIVE_COUNT, // Use user input or default
      loadDistributingElements: [
        GLULAM_CONSTANTS.SIMPLIFIED_LOAD_DISTRIBUTING_ELEMENT as any,
      ],
      lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER, // Closest category for glulam
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    factors.Cr = getRepetitiveMemberFactor(repInput);
  }

  // Incising Factor (Ci) - applies to most design values when member is incised
  if (serviceConditions.isIncised) {
    // Use the proper incising factor function for bending (most common for glulam)
    // Note: Incising is less common for glulam, but included for completeness
    factors.Ci = getIncisingFactorCi(DESIGN_VALUE_TYPES.BENDING);
  }

  // Curvature Factor (Cc) - applies to curved glulam members
  if (member.radiusOfCurvature && member.laminationThickness) {
    factors.Cc = getCurvatureFactor({
      radiusOfCurvature: member.radiusOfCurvature,
      laminationThickness: member.laminationThickness,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      memberType: CURVATURE_MEMBER_TYPES.CURVED,
    });
  } else {
    // For straight members without curvature parameters
    factors.Cc = getCurvatureFactor({
      radiusOfCurvature: GLULAM_CONSTANTS.STRAIGHT_MEMBER_RADIUS,
      laminationThickness: GLULAM_CONSTANTS.DEFAULT_LAMINATION_THICKNESS,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      memberType: CURVATURE_MEMBER_TYPES.STRAIGHT,
    });
  }

  // Stress Interaction Factor (CI) - applies to tapered glulam members
  if (member.taperAngle && member.taperLocation) {
    factors.CI = getStressInteractionFactor({
      taperAngle: member.taperAngle,
      taperLocation: member.taperLocation,
      tensileStress: material.referenceDesignValues.Ft,
      compressiveStress: material.referenceDesignValues.Fc,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      memberType:
        member.taperLocation === GLULAM_TAPER_LOCATIONS.COMPRESSION
          ? STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION
          : STRESS_INTERACTION_MEMBER_TYPES.TAPERED_TENSION,
    });
  } else {
    // For non-tapered members
    factors.CI = getStressInteractionFactor({
      taperAngle: 0,
      taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
      tensileStress: GLULAM_CONSTANTS.DEFAULT_STRESS_VALUE,
      compressiveStress: GLULAM_CONSTANTS.DEFAULT_STRESS_VALUE,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    });
  }

  // Shear Reduction Factor (Cvr) - applies under specific conditions
  const memberType = member.taperAngle
    ? GLULAM_MEMBER_TYPES.NON_PRISMATIC
    : GLULAM_MEMBER_TYPES.PRISMATIC;
  factors.Cvr = getShearReductionFactor({
    memberType,
    loadingType: GLULAM_LOADING_TYPES.NORMAL, // Simplified - would need more input for impact/cyclic
    designValueType: DESIGN_VALUE_TYPES.SHEAR,
    conditions:
      memberType === GLULAM_MEMBER_TYPES.NON_PRISMATIC
        ? [SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC]
        : undefined,
  });

  // Beam Stability Factor (CL) - applies to bending
  // Calculate adjusted Fbx* (all factors except CL, CV, Cfu, Cc, CI)
  const primaryBending =
    serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
      ? material.referenceDesignValues.Fbx_pos
      : material.referenceDesignValues.Fby;

  const FbStar = primaryBending * factors.CD * factors.CM * factors.Ct;

  const primaryE_min =
    serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
      ? material.referenceDesignValues.Ex_min
      : material.referenceDesignValues.Ey_min;

  factors.CL = getBeamStabilityFactorCL(
    member.unbracedLength,
    orientedDepth,
    orientedWidth,
    primaryE_min * factors.CM * factors.Ct,
    FbStar
  );

  // LRFD Format Conversion and Resistance Factors
  if (designMethod === DESIGN_METHODS.LRFD) {
    factors.KF = getLrfdFormatConversionFactorKF(DESIGN_VALUE_TYPES.BENDING);
    factors.phi = getLrfdResistanceFactorPhi(DESIGN_VALUE_TYPES.BENDING);
    factors.lambda = ADJUSTMENT_FACTOR_CONSTANTS.DEFAULT_LRFD_TIME_EFFECT; // Default time effect factor
  }

  return factors;
}

/**
 * Apply adjustment factors to glulam reference design values per NDS Table 5.3.1
 * @reference NDS Table 5.3.1 Applicability of Adjustment Factors
 */
export function calculateGlulamAdjustedDesignValues(
  referenceValues: GlulamReferenceDesignValues,
  designMethod: DesignMethod,
  material?: GlulamMaterialProperties,
  member?: GlulamMemberGeometry,
  serviceConditions?: GlulamServiceConditions
): GlulamAdjustedDesignValuesResult {
  // If detailed inputs are not provided, throw error since we need them for proper calculations
  if (!material || !member || !serviceConditions) {
    throw new Error(
      "Material, member, and service conditions are required for glulam design-value-specific calculations"
    );
  }

  // Calculate design-value-specific factors according to NDS Table 5.3.1
  const fbxPosFactors = calculateGlulamDesignValueSpecificFactors(
    "Fbx_pos",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fbxNegFactors = calculateGlulamDesignValueSpecificFactors(
    "Fbx_neg",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fbyFactors = calculateGlulamDesignValueSpecificFactors(
    "Fby",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const ftFactors = calculateGlulamDesignValueSpecificFactors(
    "Ft",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fvxFactors = calculateGlulamDesignValueSpecificFactors(
    "Fvx",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fvyFactors = calculateGlulamDesignValueSpecificFactors(
    "Fvy",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fcPerpXFactors = calculateGlulamDesignValueSpecificFactors(
    "Fc_perp_x",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fcPerpYFactors = calculateGlulamDesignValueSpecificFactors(
    "Fc_perp_y",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const fcFactors = calculateGlulamDesignValueSpecificFactors(
    "Fc",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const exFactors = calculateGlulamDesignValueSpecificFactors(
    "Ex",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const exMinFactors = calculateGlulamDesignValueSpecificFactors(
    "Ex_min",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const eyFactors = calculateGlulamDesignValueSpecificFactors(
    "Ey",
    material,
    member,
    serviceConditions,
    designMethod
  );
  const eyMinFactors = calculateGlulamDesignValueSpecificFactors(
    "Ey_min",
    material,
    member,
    serviceConditions,
    designMethod
  );

  // Optional radial stress factors
  const frtFactors = referenceValues.Frt
    ? calculateGlulamDesignValueSpecificFactors(
        "Frt",
        material,
        member,
        serviceConditions,
        designMethod
      )
    : undefined;
  const frcFactors = referenceValues.Frc
    ? calculateGlulamDesignValueSpecificFactors(
        "Frc",
        material,
        member,
        serviceConditions,
        designMethod
      )
    : undefined;

  let adjustedDesignValues: GlulamAdjustedDesignValues;

  if (designMethod === DESIGN_METHODS.ASD) {
    // ASD Method: F' = F × (applicable adjustment factors from Table 5.3.1)
    adjustedDesignValues = {
      // Bending: Fb = Fb × CD × CM × Ct × CL × CV × Cfu × Cc × CI
      Fbx_pos_prime:
        referenceValues.Fbx_pos *
        (fbxPosFactors.CD || 1.0) *
        (fbxPosFactors.CM || 1.0) *
        (fbxPosFactors.Ct || 1.0) *
        (fbxPosFactors.CL || 1.0) *
        (fbxPosFactors.CV || 1.0) *
        (fbxPosFactors.Cfu || 1.0) *
        (fbxPosFactors.Cc || 1.0) *
        (fbxPosFactors.CI || 1.0),

      Fbx_neg_prime:
        referenceValues.Fbx_neg *
        (fbxNegFactors.CD || 1.0) *
        (fbxNegFactors.CM || 1.0) *
        (fbxNegFactors.Ct || 1.0) *
        (fbxNegFactors.CL || 1.0) *
        (fbxNegFactors.CV || 1.0) *
        (fbxNegFactors.Cfu || 1.0) *
        (fbxNegFactors.Cc || 1.0) *
        (fbxNegFactors.CI || 1.0),

      Fby_prime:
        referenceValues.Fby *
        (fbyFactors.CD || 1.0) *
        (fbyFactors.CM || 1.0) *
        (fbyFactors.Ct || 1.0) *
        (fbyFactors.CL || 1.0) *
        (fbyFactors.CV || 1.0) *
        (fbyFactors.Cfu || 1.0) *
        (fbyFactors.Cc || 1.0) *
        (fbyFactors.CI || 1.0),

      // Tension: Ft = Ft × CD × CM × Ct
      Ft_prime:
        referenceValues.Ft *
        (ftFactors.CD || 1.0) *
        (ftFactors.CM || 1.0) *
        (ftFactors.Ct || 1.0),

      // Shear: Fv = Fv × CD × CM × Ct × Cvr
      Fvx_prime:
        referenceValues.Fvx *
        (fvxFactors.CD || 1.0) *
        (fvxFactors.CM || 1.0) *
        (fvxFactors.Ct || 1.0) *
        (fvxFactors.Cvr || 1.0),

      Fvy_prime:
        referenceValues.Fvy *
        (fvyFactors.CD || 1.0) *
        (fvyFactors.CM || 1.0) *
        (fvyFactors.Ct || 1.0) *
        (fvyFactors.Cvr || 1.0),

      // Compression perpendicular: Fc⊥ = Fc⊥ × CM × Ct × Cb
      Fc_perp_x_prime:
        referenceValues.Fc_perp_x *
        (fcPerpXFactors.CM || 1.0) *
        (fcPerpXFactors.Ct || 1.0) *
        (fcPerpXFactors.Cb || 1.0),

      Fc_perp_y_prime:
        referenceValues.Fc_perp_y *
        (fcPerpYFactors.CM || 1.0) *
        (fcPerpYFactors.Ct || 1.0) *
        (fcPerpYFactors.Cb || 1.0),

      // Compression parallel: Fc = Fc × CD × CM × Ct × CP
      Fc_prime:
        referenceValues.Fc *
        (fcFactors.CD || 1.0) *
        (fcFactors.CM || 1.0) *
        (fcFactors.Ct || 1.0) *
        (fcFactors.CP || 1.0),

      // Modulus of elasticity: E = E × CM × Ct (no CD)
      Ex_prime:
        referenceValues.Ex * (exFactors.CM || 1.0) * (exFactors.Ct || 1.0),

      Ex_min_prime:
        referenceValues.Ex_min *
        (exMinFactors.CM || 1.0) *
        (exMinFactors.Ct || 1.0),

      Ey_prime:
        referenceValues.Ey * (eyFactors.CM || 1.0) * (eyFactors.Ct || 1.0),

      Ey_min_prime:
        referenceValues.Ey_min *
        (eyMinFactors.CM || 1.0) *
        (eyMinFactors.Ct || 1.0),

      // Radial stresses (if applicable): Frt = Frt × CD × CM² × Ct²
      Frt_prime:
        referenceValues.Frt && frtFactors
          ? referenceValues.Frt *
            (frtFactors.CD || 1.0) *
            Math.pow(frtFactors.CM || 1.0, 2) *
            Math.pow(frtFactors.Ct || 1.0, 2)
          : undefined,

      Frc_prime:
        referenceValues.Frc && frcFactors
          ? referenceValues.Frc *
            (frcFactors.CM || 1.0) *
            (frcFactors.Ct || 1.0)
          : undefined,
    };
  } else {
    // LRFD Method: F'n = F × λ × (other factors) and Rn = F'n × KF × φ
    adjustedDesignValues = {
      // Bending with LRFD factors
      Fbx_pos_prime:
        referenceValues.Fbx_pos *
        (fbxPosFactors.lambda || 1.0) *
        (fbxPosFactors.CM || 1.0) *
        (fbxPosFactors.Ct || 1.0) *
        (fbxPosFactors.CL || 1.0) *
        (fbxPosFactors.CV || 1.0) *
        (fbxPosFactors.Cfu || 1.0) *
        (fbxPosFactors.Cc || 1.0) *
        (fbxPosFactors.CI || 1.0) *
        (fbxPosFactors.KF || 1.0) *
        (fbxPosFactors.phi || 1.0),

      Fbx_neg_prime:
        referenceValues.Fbx_neg *
        (fbxNegFactors.lambda || 1.0) *
        (fbxNegFactors.CM || 1.0) *
        (fbxNegFactors.Ct || 1.0) *
        (fbxNegFactors.CL || 1.0) *
        (fbxNegFactors.CV || 1.0) *
        (fbxNegFactors.Cfu || 1.0) *
        (fbxNegFactors.Cc || 1.0) *
        (fbxNegFactors.CI || 1.0) *
        (fbxNegFactors.KF || 1.0) *
        (fbxNegFactors.phi || 1.0),

      Fby_prime:
        referenceValues.Fby *
        (fbyFactors.lambda || 1.0) *
        (fbyFactors.CM || 1.0) *
        (fbyFactors.Ct || 1.0) *
        (fbyFactors.CL || 1.0) *
        (fbyFactors.CV || 1.0) *
        (fbyFactors.Cfu || 1.0) *
        (fbyFactors.Cc || 1.0) *
        (fbyFactors.CI || 1.0) *
        (fbyFactors.KF || 1.0) *
        (fbyFactors.phi || 1.0),

      // Tension with LRFD factors
      Ft_prime:
        referenceValues.Ft *
        (ftFactors.lambda || 1.0) *
        (ftFactors.CM || 1.0) *
        (ftFactors.Ct || 1.0) *
        (ftFactors.KF || 1.0) *
        (ftFactors.phi || 1.0),

      // Shear with LRFD factors
      Fvx_prime:
        referenceValues.Fvx *
        (fvxFactors.lambda || 1.0) *
        (fvxFactors.CM || 1.0) *
        (fvxFactors.Ct || 1.0) *
        (fvxFactors.Cvr || 1.0) *
        (fvxFactors.KF || 1.0) *
        (fvxFactors.phi || 1.0),

      Fvy_prime:
        referenceValues.Fvy *
        (fvyFactors.lambda || 1.0) *
        (fvyFactors.CM || 1.0) *
        (fvyFactors.Ct || 1.0) *
        (fvyFactors.Cvr || 1.0) *
        (fvyFactors.KF || 1.0) *
        (fvyFactors.phi || 1.0),

      // Compression perpendicular (no λ factor)
      Fc_perp_x_prime:
        referenceValues.Fc_perp_x *
        (fcPerpXFactors.CM || 1.0) *
        (fcPerpXFactors.Ct || 1.0) *
        (fcPerpXFactors.Cb || 1.0),

      Fc_perp_y_prime:
        referenceValues.Fc_perp_y *
        (fcPerpYFactors.CM || 1.0) *
        (fcPerpYFactors.Ct || 1.0) *
        (fcPerpYFactors.Cb || 1.0),

      // Compression parallel with LRFD factors
      Fc_prime:
        referenceValues.Fc *
        (fcFactors.lambda || 1.0) *
        (fcFactors.CM || 1.0) *
        (fcFactors.Ct || 1.0) *
        (fcFactors.CP || 1.0) *
        (fcFactors.KF || 1.0) *
        (fcFactors.phi || 1.0),

      // Modulus of elasticity (no λ, KF, or φ factors)
      Ex_prime:
        referenceValues.Ex * (exFactors.CM || 1.0) * (exFactors.Ct || 1.0),

      Ex_min_prime:
        referenceValues.Ex_min *
        (exMinFactors.CM || 1.0) *
        (exMinFactors.Ct || 1.0),

      Ey_prime:
        referenceValues.Ey * (eyFactors.CM || 1.0) * (eyFactors.Ct || 1.0),

      Ey_min_prime:
        referenceValues.Ey_min *
        (eyMinFactors.CM || 1.0) *
        (eyMinFactors.Ct || 1.0),

      // Radial stresses with LRFD factors
      Frt_prime:
        referenceValues.Frt && frtFactors
          ? referenceValues.Frt *
            (frtFactors.lambda || 1.0) *
            Math.pow(frtFactors.CM || 1.0, 2) *
            Math.pow(frtFactors.Ct || 1.0, 2) *
            (frtFactors.KF || 1.0) *
            (frtFactors.phi || 1.0)
          : undefined,

      Frc_prime:
        referenceValues.Frc && frcFactors
          ? referenceValues.Frc *
            (frcFactors.CM || 1.0) *
            (frcFactors.Ct || 1.0)
          : undefined,
    };
  }

  return {
    adjustedDesignValues,
    designValueSpecificFactors: {
      Fbx_pos: fbxPosFactors,
      Fbx_neg: fbxNegFactors,
      Fby: fbyFactors,
      Ft: ftFactors,
      Fvx: fvxFactors,
      Fvy: fvyFactors,
      Fc_perp_x: fcPerpXFactors,
      Fc_perp_y: fcPerpYFactors,
      Fc: fcFactors,
      Ex: exFactors,
      Ex_min: exMinFactors,
      Ey: eyFactors,
      Ey_min: eyMinFactors,
      Frt: frtFactors,
      Frc: frcFactors,
    },
  };
}

/**
 * Calculate actual stresses from analysis results for glulam
 */
export function calculateGlulamStresses(
  member: GlulamMemberGeometry,
  analysisResults: InputAnalysisResults,
  primaryAxis: GlulamAxis
): { bendingStress: number; shearStress: number } {
  // Calculate section properties based on primary axis
  let sectionModulus: number;
  let area: number;

  if (primaryAxis === GLULAM_AXIS.X_AXIS) {
    // Strong axis bending (load applied to narrow face)
    sectionModulus =
      (member.width * Math.pow(member.depth, 2)) /
      MATHEMATICAL_CONSTANTS.RECTANGULAR_SECTION_MODULUS_FACTOR;
  } else {
    // Weak axis bending (load applied to wide face)
    sectionModulus =
      (member.depth * Math.pow(member.width, 2)) /
      MATHEMATICAL_CONSTANTS.RECTANGULAR_SECTION_MODULUS_FACTOR;
  }

  area = member.width * member.depth;

  const bendingStress = analysisResults.maxMoment / sectionModulus;
  const shearStress =
    (MATHEMATICAL_CONSTANTS.RECTANGULAR_SHEAR_STRESS_FACTOR *
      analysisResults.maxShear) /
    area; // Rectangular section

  return { bendingStress, shearStress };
}

/**
 * Check bending stress against allowable value for glulam
 */
export function checkGlulamBendingStress(
  actualStress: number,
  adjustedValues: GlulamAdjustedDesignValues,
  primaryAxis: GlulamAxis,
  bendingStressType: BendingStressType = BENDING_STRESS_TYPES.POSITIVE
): {
  allowableStress: number;
  actualStress: number;
  ratio: number;
  passes: boolean;
  stressType: BendingStressType;
} {
  // Select appropriate allowable stress based on axis and stress type
  let allowableStress: number;

  if (primaryAxis === GLULAM_AXIS.X_AXIS) {
    allowableStress =
      bendingStressType === BENDING_STRESS_TYPES.POSITIVE
        ? adjustedValues.Fbx_pos_prime
        : adjustedValues.Fbx_neg_prime;
  } else {
    allowableStress = adjustedValues.Fby_prime;
  }

  const ratio = actualStress / allowableStress;

  return {
    allowableStress,
    actualStress,
    ratio,
    passes: ratio <= ADJUSTMENT_FACTOR_CONSTANTS.MAX_STRESS_RATIO,
    stressType: bendingStressType,
  };
}

/**
 * Check shear stress against allowable value for glulam
 */
export function checkGlulamShearStress(
  actualStress: number,
  adjustedValues: GlulamAdjustedDesignValues,
  primaryAxis: GlulamAxis
): {
  allowableStress: number;
  actualStress: number;
  ratio: number;
  passes: boolean;
} {
  // Select appropriate allowable shear stress based on primary axis
  const allowableStress =
    primaryAxis === GLULAM_AXIS.X_AXIS
      ? adjustedValues.Fvx_prime
      : adjustedValues.Fvy_prime;

  const ratio = actualStress / allowableStress;

  return {
    allowableStress,
    actualStress,
    ratio,
    passes: ratio <= ADJUSTMENT_FACTOR_CONSTANTS.MAX_STRESS_RATIO,
  };
}

/**
 * Check deflection against allowable limit for glulam
 */
export function checkGlulamDeflection(
  actualDeflection: number,
  beamLength: number,
  deflectionLimit: number
): {
  allowableDeflection: number;
  actualDeflection: number;
  ratio: number;
  passes: boolean;
} {
  const allowableDeflection = beamLength / deflectionLimit;
  const ratio = actualDeflection / allowableDeflection;

  return {
    allowableDeflection,
    actualDeflection,
    ratio,
    passes: ratio <= ADJUSTMENT_FACTOR_CONSTANTS.MAX_STRESS_RATIO,
  };
}

/**
 * Main analysis function for glulam beams
 * @reference NDS Section 5 - Structural Glued Laminated Timber
 */
export function analyzeGlulamBeam(
  inputs: GlulamBeamAnalysisInputs
): GlulamBeamAnalysisResults {
  // Calculate adjusted design values using design-value-specific factors
  const adjustedDesignValuesResult = calculateGlulamAdjustedDesignValues(
    inputs.material.referenceDesignValues,
    inputs.designMethod,
    inputs.material,
    inputs.member,
    inputs.serviceConditions
  );

  // Calculate actual stresses from provided analysis results
  const stresses = calculateGlulamStresses(
    inputs.member,
    inputs.analysisResults,
    inputs.serviceConditions.primaryAxis
  );

  // Determine bending stress type from analysis results or default
  const bendingStressType =
    inputs.analysisResults.bendingStressType || BENDING_STRESS_TYPES.POSITIVE;

  // Perform design checks
  const bendingCheck = checkGlulamBendingStress(
    stresses.bendingStress,
    adjustedDesignValuesResult.adjustedDesignValues,
    inputs.serviceConditions.primaryAxis,
    bendingStressType
  );

  const shearCheck = checkGlulamShearStress(
    stresses.shearStress,
    adjustedDesignValuesResult.adjustedDesignValues,
    inputs.serviceConditions.primaryAxis
  );

  const deflectionCheck = checkGlulamDeflection(
    inputs.analysisResults.maxDeflection,
    inputs.member.length,
    inputs.deflectionLimit
  );

  // Create complete analysis results
  const analysisResults: GlulamAnalysisResults = {
    maxMoment: inputs.analysisResults.maxMoment,
    maxShear: inputs.analysisResults.maxShear,
    maxDeflection: inputs.analysisResults.maxDeflection,
    bendingStressType: bendingStressType,
    maxBendingStress: stresses.bendingStress,
    maxShearStress: stresses.shearStress,
    bendingStressRatio: bendingCheck.ratio,
    shearStressRatio: shearCheck.ratio,
    deflectionRatio: deflectionCheck.ratio,
  };

  // Determine overall design adequacy
  const isDesignAcceptable =
    bendingCheck.passes && shearCheck.passes && deflectionCheck.passes;

  // Determine controlling criteria
  let controllingCriteria = "Design is acceptable";
  if (!isDesignAcceptable) {
    const maxRatio = Math.max(
      bendingCheck.ratio,
      shearCheck.ratio,
      deflectionCheck.ratio
    );

    if (maxRatio === bendingCheck.ratio) {
      controllingCriteria = "Bending stress controls design";
    } else if (maxRatio === shearCheck.ratio) {
      controllingCriteria = "Shear stress controls design";
    } else {
      controllingCriteria = "Deflection controls design";
    }
  }

  return {
    inputs,
    adjustedDesignValues: adjustedDesignValuesResult.adjustedDesignValues,
    designValueSpecificFactors:
      adjustedDesignValuesResult.designValueSpecificFactors,
    analysisResults,
    bendingCheck,
    shearCheck,
    deflectionCheck,
    isDesignAcceptable,
    controllingCriteria,
  };
}

/**
 * Create sample glulam analysis inputs for testing and demonstration
 */
export function createSampleGlulamAnalysisInputs(): GlulamBeamAnalysisInputs {
  return {
    member: {
      length: SAMPLE_GLULAM_CONSTANTS.LENGTH,
      width: SAMPLE_GLULAM_CONSTANTS.WIDTH,
      depth: SAMPLE_GLULAM_CONSTANTS.DEPTH,
      unbracedLength: SAMPLE_GLULAM_CONSTANTS.UNBRACED_LENGTH,
    },
    material: {
      species: SAMPLE_GLULAM_CONSTANTS.SPECIES,
      grade: SAMPLE_GLULAM_CONSTANTS.GRADE,
      layupGrade: SAMPLE_GLULAM_CONSTANTS.LAYUP_GRADE,
      combination: SAMPLE_GLULAM_CONSTANTS.COMBINATION,
      referenceDesignValues: SAMPLE_REFERENCE_DESIGN_VALUES,
    },
    serviceConditions: {
      moistureContent: SAMPLE_GLULAM_CONSTANTS.MOISTURE_CONTENT,
      temperature: SAMPLE_GLULAM_CONSTANTS.TEMPERATURE,
      loadDuration: LoadType.LIVE,
      isFlatUse: false,
      isRepetitiveMember: false,
      isIncised: false,
      layupType: LAYUP_TYPES.UNBALANCED,
      primaryAxis: GLULAM_AXIS.X_AXIS,
    },
    analysisResults: {
      maxMoment: SAMPLE_GLULAM_CONSTANTS.MOMENT,
      maxShear: SAMPLE_GLULAM_CONSTANTS.SHEAR,
      maxDeflection: SAMPLE_GLULAM_CONSTANTS.DEFLECTION,
      bendingStressType: BENDING_STRESS_TYPES.POSITIVE,
    },
    designMethod: DESIGN_METHODS.ASD,
    deflectionLimit: SAMPLE_GLULAM_CONSTANTS.DEFLECTION_LIMIT,
    includeSelfWeight: true,
  };
}

// Re-export types and enums for external use
export type {
  DesignMethod,
  GlulamAxis,
  BendingStressType,
  LayupType,
} from "../constants";

/**
 * Calculate adjustment factors specific to a glulam design value type
 * @reference NDS Table 5.3.1 Applicability of Adjustment Factors for Glulam
 */
function calculateGlulamDesignValueSpecificFactors(
  designValueType: string, // Using string for glulam-specific design values
  material: GlulamMaterialProperties,
  member: GlulamMemberGeometry,
  serviceConditions: GlulamServiceConditions,
  designMethod: DesignMethod
): Partial<GlulamAdjustmentFactors> {
  const factors: Partial<GlulamAdjustmentFactors> = {};

  // Calculate oriented dimensions based on primary axis for glulam
  // When primary axis is X (strong axis), load is applied to narrow face: width=member.width, depth=member.depth
  // When primary axis is Y (weak axis), load is applied to wide face: width=member.depth, depth=member.width
  const orientedWidth =
    serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
      ? member.width // Load on narrow face, width is width
      : member.depth; // Load on wide face, width is depth
  const orientedDepth =
    serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
      ? member.depth // Load on narrow face, depth is depth
      : member.width; // Load on wide face, depth is width

  // Load Duration Factor (CD) - ASD only, applies to stress values except E and Emin
  if (designMethod === DESIGN_METHODS.ASD) {
    if (!["Ex", "Ex_min", "Ey", "Ey_min"].includes(designValueType)) {
      factors.CD = getLoadDurationFactorByLoadType(
        serviceConditions.loadDuration
      );
    }
  }

  // Time Effect Factor (lambda) - LRFD only, applies to stress values except E and Emin
  if (designMethod === DESIGN_METHODS.LRFD) {
    if (!["Ex", "Ex_min", "Ey", "Ey_min"].includes(designValueType)) {
      factors.lambda = ADJUSTMENT_FACTOR_CONSTANTS.DEFAULT_LRFD_TIME_EFFECT;
    }
  }

  // Wet Service Factor (CM) - applies to all design values, but value depends on type
  if (
    serviceConditions.moistureContent >
    GLULAM_CONSTANTS.MOISTURE_CONTENT_THRESHOLD
  ) {
    const wetServiceInput: WetServiceFactorInput = {
      moistureContent: serviceConditions.moistureContent,
      designValueType: DESIGN_VALUE_TYPES.BENDING, // Simplified for glulam
      lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER,
      speciesGroup: GLULAM_CONSTANTS.SIMPLIFIED_SPECIES_GROUP as any,
    };
    factors.CM = getWetServiceFactor(wetServiceInput);
  } else {
    factors.CM = 1.0;
  }

  // Temperature Factor (Ct) - applies to all design values
  if (serviceConditions.temperature > GLULAM_CONSTANTS.TEMPERATURE_THRESHOLD) {
    const tempInput: TemperatureFactorInput = {
      temperature: serviceConditions.temperature,
      moistureCondition:
        serviceConditions.moistureContent >
        GLULAM_CONSTANTS.MOISTURE_CONTENT_THRESHOLD
          ? MOISTURE_CONDITIONS.WET
          : MOISTURE_CONDITIONS.DRY,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    factors.Ct = getTemperatureFactor(tempInput);
  } else {
    factors.Ct = 1.0;
  }

  // Flat Use Factor (Cfu) - applies to bending design values only
  if (
    ["Fbx_pos", "Fbx_neg", "Fby"].includes(designValueType) &&
    serviceConditions.isFlatUse
  ) {
    const flatUseInput: FlatUseFactorInput = {
      width: orientedWidth,
      depth: orientedDepth,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: GLULAM_CONSTANTS.SIMPLIFIED_LUMBER_GRADE as any, // Simplified
      isLoadOnWideFace: false,
    };
    factors.Cfu = getFlatUseFactorCfu(flatUseInput);
  } else {
    factors.Cfu = 1.0;
  }

  // Volume Factor (CV) - applies to bending design values only
  if (["Fbx_pos", "Fbx_neg", "Fby"].includes(designValueType)) {
    const volumeInput: VolumeFactorInput = {
      length: member.length / GLULAM_CONSTANTS.INCHES_TO_FEET, // Convert to feet
      width: orientedWidth,
      depth: orientedDepth,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    factors.CV = getVolumeFactor(volumeInput);
  } else {
    factors.CV = 1.0;
  }

  // Curvature Factor (Cc) - applies to bending design values
  if (["Fbx_pos", "Fbx_neg", "Fby"].includes(designValueType)) {
    if (member.radiusOfCurvature && member.laminationThickness) {
      factors.Cc = getCurvatureFactor({
        radiusOfCurvature: member.radiusOfCurvature,
        laminationThickness: member.laminationThickness,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
        memberType: CURVATURE_MEMBER_TYPES.CURVED,
      });
    } else {
      factors.Cc = getCurvatureFactor({
        radiusOfCurvature: GLULAM_CONSTANTS.STRAIGHT_MEMBER_RADIUS,
        laminationThickness: GLULAM_CONSTANTS.DEFAULT_LAMINATION_THICKNESS,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        memberType: CURVATURE_MEMBER_TYPES.STRAIGHT,
      });
    }
  } else {
    factors.Cc = 1.0;
  }

  // Stress Interaction Factor (CI) - applies to bending design values for tapered members
  if (["Fbx_pos", "Fbx_neg", "Fby"].includes(designValueType)) {
    if (member.taperAngle && member.taperLocation) {
      factors.CI = getStressInteractionFactor({
        taperAngle: member.taperAngle,
        taperLocation: member.taperLocation,
        tensileStress: material.referenceDesignValues.Ft,
        compressiveStress: material.referenceDesignValues.Fc,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        memberType:
          member.taperLocation === GLULAM_TAPER_LOCATIONS.COMPRESSION
            ? STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION
            : STRESS_INTERACTION_MEMBER_TYPES.TAPERED_TENSION,
      });
    } else {
      factors.CI = getStressInteractionFactor({
        taperAngle: 0,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: GLULAM_CONSTANTS.DEFAULT_STRESS_VALUE,
        compressiveStress: GLULAM_CONSTANTS.DEFAULT_STRESS_VALUE,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      });
    }
  } else {
    factors.CI = 1.0;
  }

  // Shear Reduction Factor (Cvr) - applies to shear design values only
  if (["Fvx", "Fvy"].includes(designValueType)) {
    const memberType = member.taperAngle
      ? GLULAM_MEMBER_TYPES.NON_PRISMATIC
      : GLULAM_MEMBER_TYPES.PRISMATIC;
    factors.Cvr = getShearReductionFactor({
      memberType,
      loadingType: GLULAM_LOADING_TYPES.NORMAL,
      designValueType: DESIGN_VALUE_TYPES.SHEAR,
      conditions:
        memberType === GLULAM_MEMBER_TYPES.NON_PRISMATIC
          ? [SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC]
          : undefined,
    });
  } else {
    factors.Cvr = 1.0;
  }

  // Beam Stability Factor (CL) - applies to bending design values only
  if (["Fbx_pos", "Fbx_neg", "Fby"].includes(designValueType)) {
    // Calculate adjusted Fb* (all factors except CL, CV, Cfu, Cc, CI)
    const primaryBending =
      serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
        ? designValueType === "Fbx_pos"
          ? material.referenceDesignValues.Fbx_pos
          : material.referenceDesignValues.Fbx_neg
        : material.referenceDesignValues.Fby;

    const FbStar =
      primaryBending *
      (factors.CD || factors.lambda || 1.0) *
      (factors.CM || 1.0) *
      (factors.Ct || 1.0);

    const primaryE_min =
      serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
        ? material.referenceDesignValues.Ex_min
        : material.referenceDesignValues.Ey_min;

    factors.CL = getBeamStabilityFactorCL(
      member.unbracedLength,
      orientedDepth,
      orientedWidth,
      primaryE_min * (factors.CM || 1.0) * (factors.Ct || 1.0),
      FbStar
    );
  } else {
    factors.CL = 1.0;
  }

  // Bearing Area Factor (Cb) - applies to compression perpendicular only
  if (["Fc_perp_x", "Fc_perp_y"].includes(designValueType)) {
    const bearingInput: BearingAreaFactorInput = {
      bearingLength: member.width,
    };
    factors.Cb = getBearingAreaFactor(bearingInput);
  } else {
    factors.Cb = 1.0;
  }

  // Column Stability Factor (CP) - applies to compression parallel only
  if (designValueType === "Fc") {
    factors.CP = 1.0; // For beam analysis, simplified
  } else {
    factors.CP = 1.0;
  }

  // LRFD Format Conversion and Resistance Factors
  if (designMethod === DESIGN_METHODS.LRFD) {
    if (!["Ex", "Ex_min", "Ey", "Ey_min"].includes(designValueType)) {
      factors.KF = getLrfdFormatConversionFactorKF(DESIGN_VALUE_TYPES.BENDING); // Simplified
      factors.phi = getLrfdResistanceFactorPhi(DESIGN_VALUE_TYPES.BENDING); // Simplified
    } else {
      factors.KF = 1.0;
      factors.phi = 1.0;
    }
  }

  // Repetitive Member Factor (Cr) - applies to bending design values only
  if (
    ["Fbx_pos", "Fbx_neg", "Fby"].includes(designValueType) &&
    serviceConditions.isRepetitiveMember
  ) {
    // For glulam, the "thickness" for repetitive member factor depends on the loading orientation
    // When primary axis is X (strong axis), load is applied to narrow face, so thickness = width (b)
    // When primary axis is Y (weak axis), load is applied to wide face, so thickness = depth (d)
    const thickness =
      serviceConditions.primaryAxis === GLULAM_AXIS.X_AXIS
        ? member.width // Load on narrow face, thickness is width
        : member.depth; // Load on wide face, thickness is depth

    const repInput: RepetitiveMemberFactorInput = {
      thickness: thickness,
      memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
      spacingOnCenter:
        serviceConditions.repetitiveMemberSpacing ||
        GLULAM_CONSTANTS.DEFAULT_REPETITIVE_SPACING, // Use user input or default
      numberOfMembers:
        serviceConditions.repetitiveMemberCount ||
        GLULAM_CONSTANTS.DEFAULT_REPETITIVE_COUNT, // Use user input or default
      loadDistributingElements: [
        GLULAM_CONSTANTS.SIMPLIFIED_LOAD_DISTRIBUTING_ELEMENT as any,
      ],
      lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER, // Closest category for glulam
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    factors.Cr = getRepetitiveMemberFactor(repInput);
  } else {
    factors.Cr = 1.0;
  }

  // Incising Factor (Ci) - applies to specific design values when member is incised
  if (serviceConditions.isIncised) {
    // Map glulam design value types to standard design value types for incising
    let designValueTypeForIncising: DesignValueType;

    switch (designValueType) {
      case "Fbx_pos":
      case "Fbx_neg":
      case "Fby":
        designValueTypeForIncising = DESIGN_VALUE_TYPES.BENDING;
        break;
      case "Ft":
        designValueTypeForIncising = DESIGN_VALUE_TYPES.TENSION_PARALLEL;
        break;
      case "Fvx":
      case "Fvy":
        designValueTypeForIncising = DESIGN_VALUE_TYPES.SHEAR;
        break;
      case "Fc_perp_x":
      case "Fc_perp_y":
        designValueTypeForIncising =
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR;
        break;
      case "Fc":
        designValueTypeForIncising = DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL;
        break;
      case "Ex":
      case "Ey":
        designValueTypeForIncising = DESIGN_VALUE_TYPES.E;
        break;
      case "Ex_min":
      case "Ey_min":
        designValueTypeForIncising = DESIGN_VALUE_TYPES.E_MIN;
        break;
      default:
        // For radial stresses or other types, use bending as default
        designValueTypeForIncising = DESIGN_VALUE_TYPES.BENDING;
        break;
    }

    factors.Ci = getIncisingFactorCi(designValueTypeForIncising);
  } else {
    factors.Ci = 1.0;
  }

  return factors;
}
