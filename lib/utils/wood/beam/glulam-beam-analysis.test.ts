/**
 * Test suite for Glulam Beam Analysis
 *
 * @fileoverview Unit tests for structural glued laminated timber beam analysis
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  analyzeGlulamBeam,
  createSampleGlulamAnalysisInputs,
  calculateGlulamAdjustmentFactors,
  calculateGlulamAdjustedDesignValues,
  calculateGlulamStresses,
  checkGlulamBendingStress,
  checkGlulamShearStress,
  checkGlulamDeflection,
  type DesignMethod,
  type GlulamAxis,
  type BendingStressType,
  type LayupType,
  type GlulamBeamAnalysisInputs,
  type GlulamReferenceDesignValues,
  type GlulamMemberGeometry,
  type GlulamServiceConditions,
  type GlulamMaterialProperties,
} from "./glulam-beam-analysis";

import {
  DESIGN_METHODS,
  GLULAM_AXIS,
  BENDING_STRESS_TYPES,
  LAYUP_TYPES,
} from "../constants";

import { LoadType } from "../../../types/load/load-type";

describe("Glulam Beam Analysis", () => {
  describe("createSampleGlulamAnalysisInputs", () => {
    test("should create valid sample inputs", () => {
      const inputs = createSampleGlulamAnalysisInputs();

      expect(inputs.member.length).toBe(240);
      expect(inputs.member.width).toBe(5.125);
      expect(inputs.member.depth).toBe(18);
      expect(inputs.material.grade).toBe("24F-V4");
      expect(inputs.serviceConditions.primaryAxis).toBe(GLULAM_AXIS.X_AXIS);
      expect(inputs.designMethod).toBe(DESIGN_METHODS.ASD);
      expect(inputs.deflectionLimit).toBe(240);
    });
  });

  describe("calculateGlulamStresses", () => {
    const member: GlulamMemberGeometry = {
      length: 240,
      width: 5.125,
      depth: 18,
      unbracedLength: 240,
    };

    const analysisResults = {
      maxMoment: 800000, // in-lb
      maxShear: 6000, // lb
      maxDeflection: 0.9, // in
    };

    test("should calculate stresses for x-axis bending", () => {
      const stresses = calculateGlulamStresses(
        member,
        analysisResults,
        GLULAM_AXIS.X_AXIS
      );

      // Strong axis: S = b*d²/6 = 5.125*18²/6 = 277.875 in³
      const expectedSectionModulus = (5.125 * Math.pow(18, 2)) / 6;
      const expectedBendingStress = 800000 / expectedSectionModulus;
      const area = 5.125 * 18;
      const expectedShearStress = (1.5 * 6000) / area;

      expect(stresses.bendingStress).toBeCloseTo(expectedBendingStress, 1);
      expect(stresses.shearStress).toBeCloseTo(expectedShearStress, 1);
    });

    test("should calculate stresses for y-axis bending", () => {
      const stresses = calculateGlulamStresses(
        member,
        analysisResults,
        GLULAM_AXIS.Y_AXIS
      );

      // Weak axis: S = d*b²/6 = 18*5.125²/6 = 78.52 in³
      const expectedSectionModulus = (18 * Math.pow(5.125, 2)) / 6;
      const expectedBendingStress = 800000 / expectedSectionModulus;
      const area = 5.125 * 18;
      const expectedShearStress = (1.5 * 6000) / area;

      expect(stresses.bendingStress).toBeCloseTo(expectedBendingStress, 1);
      expect(stresses.shearStress).toBeCloseTo(expectedShearStress, 1);
    });
  });

  describe("checkGlulamBendingStress", () => {
    const adjustedValues = {
      Fbx_pos_prime: 2000,
      Fbx_neg_prime: 1800,
      Fby_prime: 1200,
      Ft_prime: 1000,
      Fvx_prime: 250,
      Fvy_prime: 250,
      Fc_perp_x_prime: 600,
      Fc_perp_y_prime: 600,
      Fc_prime: 1400,
      Ex_prime: 1700000,
      Ex_min_prime: 900000,
      Ey_prime: 1500000,
      Ey_min_prime: 800000,
    };

    test("should check positive bending stress on x-axis", () => {
      const result = checkGlulamBendingStress(
        1500, // actual stress
        adjustedValues,
        GLULAM_AXIS.X_AXIS,
        BENDING_STRESS_TYPES.POSITIVE
      );

      expect(result.allowableStress).toBe(2000);
      expect(result.actualStress).toBe(1500);
      expect(result.ratio).toBeCloseTo(0.75, 2);
      expect(result.passes).toBe(true);
      expect(result.stressType).toBe(BENDING_STRESS_TYPES.POSITIVE);
    });

    test("should check negative bending stress on x-axis", () => {
      const result = checkGlulamBendingStress(
        1500,
        adjustedValues,
        GLULAM_AXIS.X_AXIS,
        BENDING_STRESS_TYPES.NEGATIVE
      );

      expect(result.allowableStress).toBe(1800);
      expect(result.actualStress).toBe(1500);
      expect(result.ratio).toBeCloseTo(0.833, 2);
      expect(result.passes).toBe(true);
      expect(result.stressType).toBe(BENDING_STRESS_TYPES.NEGATIVE);
    });

    test("should check bending stress on y-axis", () => {
      const result = checkGlulamBendingStress(
        1000,
        adjustedValues,
        GLULAM_AXIS.Y_AXIS
      );

      expect(result.allowableStress).toBe(1200);
      expect(result.ratio).toBeCloseTo(0.833, 2);
      expect(result.passes).toBe(true);
    });

    test("should fail when stress ratio exceeds 1.0", () => {
      const result = checkGlulamBendingStress(
        2500, // exceeds allowable
        adjustedValues,
        GLULAM_AXIS.X_AXIS,
        BENDING_STRESS_TYPES.POSITIVE
      );

      expect(result.ratio).toBeGreaterThan(1.0);
      expect(result.passes).toBe(false);
    });
  });

  describe("checkGlulamShearStress", () => {
    const adjustedValues = {
      Fbx_pos_prime: 2000,
      Fbx_neg_prime: 1800,
      Fby_prime: 1200,
      Ft_prime: 1000,
      Fvx_prime: 250,
      Fvy_prime: 250,
      Fc_perp_x_prime: 600,
      Fc_perp_y_prime: 600,
      Fc_prime: 1400,
      Ex_prime: 1700000,
      Ex_min_prime: 900000,
      Ey_prime: 1500000,
      Ey_min_prime: 800000,
    };

    test("should check shear stress for x-axis", () => {
      const result = checkGlulamShearStress(
        150, // actual shear stress
        adjustedValues,
        GLULAM_AXIS.X_AXIS
      );

      expect(result.allowableStress).toBe(250);
      expect(result.actualStress).toBe(150);
      expect(result.ratio).toBeCloseTo(0.6, 2);
      expect(result.passes).toBe(true);
    });

    test("should check shear stress for y-axis", () => {
      const result = checkGlulamShearStress(
        200,
        adjustedValues,
        GLULAM_AXIS.Y_AXIS
      );

      expect(result.allowableStress).toBe(250);
      expect(result.ratio).toBeCloseTo(0.8, 2);
      expect(result.passes).toBe(true);
    });

    test("should fail when shear stress exceeds allowable", () => {
      const result = checkGlulamShearStress(
        300, // exceeds allowable
        adjustedValues,
        GLULAM_AXIS.X_AXIS
      );

      expect(result.ratio).toBeGreaterThan(1.0);
      expect(result.passes).toBe(false);
    });
  });

  describe("checkGlulamDeflection", () => {
    test("should check deflection against limit", () => {
      const result = checkGlulamDeflection(
        0.8, // actual deflection
        240, // beam length
        240 // deflection limit (L/240)
      );

      const expectedAllowable = 240 / 240; // 1.0 inch
      expect(result.allowableDeflection).toBe(expectedAllowable);
      expect(result.actualDeflection).toBe(0.8);
      expect(result.ratio).toBeCloseTo(0.8, 2);
      expect(result.passes).toBe(true);
    });

    test("should fail when deflection exceeds limit", () => {
      const result = checkGlulamDeflection(
        1.5, // exceeds L/240 = 1.0
        240,
        240
      );

      expect(result.ratio).toBeGreaterThan(1.0);
      expect(result.passes).toBe(false);
    });
  });

  describe("calculateGlulamAdjustmentFactors", () => {
    const material: GlulamMaterialProperties = {
      species: "Douglas Fir-Larch",
      grade: "24F-V4",
      layupGrade: "V4",
      combination: "DF-L",
      referenceDesignValues: {
        Fbx_pos: 2400,
        Fbx_neg: 1950,
        Fby: 1450,
        Ft: 1150,
        Fvx: 265,
        Fvy: 265,
        Fc_perp_x: 650,
        Fc_perp_y: 650,
        Fc: 1600,
        Ex: 1800000,
        Ex_min: 930000,
        Ey: 1600000,
        Ey_min: 850000,
        G: 90000,
      },
    };

    const member: GlulamMemberGeometry = {
      length: 240,
      width: 5.125,
      depth: 18,
      unbracedLength: 240,
    };

    const serviceConditions: GlulamServiceConditions = {
      moistureContent: 12,
      temperature: 70,
      loadDuration: LoadType.LIVE,
      isFlatUse: false,
      layupType: LAYUP_TYPES.UNBALANCED,
      primaryAxis: GLULAM_AXIS.X_AXIS,
    };

    test("should calculate adjustment factors for ASD", () => {
      const factors = calculateGlulamAdjustmentFactors(
        material,
        member,
        serviceConditions,
        DESIGN_METHODS.ASD
      );

      expect(factors.CD).toBeGreaterThan(0);
      expect(factors.CM).toBe(1.0); // Dry conditions
      expect(factors.Ct).toBe(1.0); // Normal temperature
      expect(factors.CL).toBeGreaterThan(0);
      expect(factors.CV).toBeGreaterThan(0);
      expect(factors.Cc).toBe(1.0); // Straight member
      expect(factors.CI).toBe(1.0); // Non-tapered
      expect(factors.Cvr).toBe(1.0); // Prismatic, normal loading
      expect(factors.Cfu).toBe(1.0); // Not flat use
      expect(factors.KF).toBe(1.0); // ASD
      expect(factors.phi).toBe(1.0); // ASD
      expect(factors.lambda).toBe(1.0); // ASD
    });

    test("should calculate curvature factor for curved members", () => {
      const curvedMember: GlulamMemberGeometry = {
        length: 240,
        width: 5.125,
        depth: 18,
        unbracedLength: 240,
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
      };

      const factors = calculateGlulamAdjustmentFactors(
        material,
        curvedMember,
        serviceConditions,
        DESIGN_METHODS.ASD
      );

      // For curved member with R=200, t=1.5: Cc = 1 - (2000)(1.5/200)² = 0.8875
      expect(factors.Cc).toBeCloseTo(0.8875, 3);
    });

    test("should calculate stress interaction factor for tapered members", () => {
      const taperedMember: GlulamMemberGeometry = {
        length: 240,
        width: 5.125,
        depth: 18,
        unbracedLength: 240,
        taperAngle: 5,
        taperLocation: "compression" as any,
      };

      const factors = calculateGlulamAdjustmentFactors(
        material,
        taperedMember,
        serviceConditions,
        DESIGN_METHODS.ASD
      );

      // For tapered member, CI should be less than 1.0
      expect(factors.CI).toBeGreaterThan(0);
      expect(factors.CI).toBeLessThanOrEqual(1.0);
    });

    test("should calculate shear reduction factor for non-prismatic members", () => {
      const nonPrismaticMember: GlulamMemberGeometry = {
        length: 240,
        width: 5.125,
        depth: 18,
        unbracedLength: 240,
        taperAngle: 3, // This makes it non-prismatic
      };

      const factors = calculateGlulamAdjustmentFactors(
        material,
        nonPrismaticMember,
        serviceConditions,
        DESIGN_METHODS.ASD
      );

      // For non-prismatic member, Cvr should be 0.72
      expect(factors.Cvr).toBe(0.72);
    });

    test("should calculate adjustment factors for LRFD", () => {
      const factors = calculateGlulamAdjustmentFactors(
        material,
        member,
        serviceConditions,
        DESIGN_METHODS.LRFD
      );

      expect(factors.CD).toBe(1.0); // LRFD doesn't use CD
      expect(factors.KF).toBeGreaterThan(1.0); // LRFD format conversion
      expect(factors.phi).toBeGreaterThan(0); // LRFD resistance factor
      expect(factors.lambda).toBeGreaterThan(0); // LRFD time effect
    });

    test("should apply wet service factor when moisture > 19%", () => {
      const wetServiceConditions = {
        ...serviceConditions,
        moistureContent: 25,
      };

      const factors = calculateGlulamAdjustmentFactors(
        material,
        member,
        wetServiceConditions,
        DESIGN_METHODS.ASD
      );

      expect(factors.CM).toBeLessThan(1.0);
    });

    test("should apply temperature factor when temperature > 100°F", () => {
      const highTempConditions = {
        ...serviceConditions,
        temperature: 130,
      };

      const factors = calculateGlulamAdjustmentFactors(
        material,
        member,
        highTempConditions,
        DESIGN_METHODS.ASD
      );

      expect(factors.Ct).toBeLessThan(1.0);
    });
  });

  describe("calculateGlulamAdjustedDesignValues", () => {
    const referenceValues: GlulamReferenceDesignValues = {
      Fbx_pos: 2400,
      Fbx_neg: 1950,
      Fby: 1450,
      Ft: 1150,
      Fvx: 265,
      Fvy: 265,
      Fc_perp_x: 650,
      Fc_perp_y: 650,
      Fc: 1600,
      Ex: 1800000,
      Ex_min: 930000,
      Ey: 1600000,
      Ey_min: 850000,
      G: 90000,
    };

    test("should apply adjustment factors correctly for ASD", () => {
      // Create mock inputs required for the new signature
      const mockMaterial: GlulamMaterialProperties = {
        species: "Douglas Fir-Larch",
        grade: "24F-V4",
        layupGrade: "V4",
        combination: "DF-L",
        referenceDesignValues: referenceValues,
      };

      const mockMember: GlulamMemberGeometry = {
        length: 240,
        width: 5.125,
        depth: 18,
        unbracedLength: 240,
      };

      const mockServiceConditions: GlulamServiceConditions = {
        moistureContent: 25, // Wet service
        temperature: 120, // High temperature
        loadDuration: LoadType.LIVE,
        isFlatUse: false,
        layupType: LAYUP_TYPES.UNBALANCED,
        primaryAxis: GLULAM_AXIS.X_AXIS,
      };

      const result = calculateGlulamAdjustedDesignValues(
        referenceValues,
        DESIGN_METHODS.ASD,
        mockMaterial,
        mockMember,
        mockServiceConditions
      );

      // Check that we get both adjusted values and specific factors
      expect(result.adjustedDesignValues).toBeDefined();
      expect(result.designValueSpecificFactors).toBeDefined();
      expect(result.designValueSpecificFactors.Fbx_pos).toBeDefined();
      expect(result.designValueSpecificFactors.Ft).toBeDefined();
      expect(result.designValueSpecificFactors.Fvx).toBeDefined();
      expect(result.designValueSpecificFactors.Ex).toBeDefined();

      // Check that adjusted values are calculated and positive
      expect(result.adjustedDesignValues.Fbx_pos_prime).toBeGreaterThan(0);
      expect(result.adjustedDesignValues.Ft_prime).toBeGreaterThan(0);
      expect(result.adjustedDesignValues.Fvx_prime).toBeGreaterThan(0);
      expect(result.adjustedDesignValues.Ex_prime).toBeGreaterThan(0);

      // With wet service and high temperature, values should be reduced from reference
      expect(result.adjustedDesignValues.Fbx_pos_prime).toBeLessThan(referenceValues.Fbx_pos);
      expect(result.adjustedDesignValues.Ex_prime).toBeLessThan(referenceValues.Ex);
    });

    test("should apply LRFD factors correctly", () => {
      // Create mock inputs for LRFD method
      const mockMaterial: GlulamMaterialProperties = {
        species: "Douglas Fir-Larch",
        grade: "24F-V4",
        layupGrade: "V4",
        combination: "DF-L",
        referenceDesignValues: referenceValues,
      };

      const mockMember: GlulamMemberGeometry = {
        length: 240,
        width: 5.125,
        depth: 18,
        unbracedLength: 240,
      };

      const mockServiceConditions: GlulamServiceConditions = {
        moistureContent: 15, // Dry service
        temperature: 70, // Normal temperature
        loadDuration: LoadType.LIVE,
        isFlatUse: false,
        layupType: LAYUP_TYPES.UNBALANCED,
        primaryAxis: GLULAM_AXIS.X_AXIS,
      };

      const lrfdResult = calculateGlulamAdjustedDesignValues(
        referenceValues,
        DESIGN_METHODS.LRFD,
        mockMaterial,
        mockMember,
        mockServiceConditions
      );

      const asdResult = calculateGlulamAdjustedDesignValues(
        referenceValues,
        DESIGN_METHODS.ASD,
        mockMaterial,
        mockMember,
        mockServiceConditions
      );

      // Check that we get both adjusted values and specific factors
      expect(lrfdResult.adjustedDesignValues).toBeDefined();
      expect(lrfdResult.designValueSpecificFactors).toBeDefined();

      // LRFD should produce different values than ASD
      expect(lrfdResult.adjustedDesignValues.Fbx_pos_prime).not.toBeCloseTo(asdResult.adjustedDesignValues.Fbx_pos_prime, 1);

      // LRFD should have lambda factor, ASD should have CD factor
      expect(lrfdResult.designValueSpecificFactors.Fbx_pos.lambda).toBeDefined();
      expect(asdResult.designValueSpecificFactors.Fbx_pos.CD).toBeDefined();

      // Both should have CM and Ct factors
      expect(lrfdResult.designValueSpecificFactors.Fbx_pos.CM).toBeDefined();
      expect(asdResult.designValueSpecificFactors.Fbx_pos.CM).toBeDefined();
    });
  });

  describe("analyzeGlulamBeam - Integration Test", () => {
    test("should perform complete beam analysis", () => {
      const inputs = createSampleGlulamAnalysisInputs();
      const results = analyzeGlulamBeam(inputs);

      // Check that all results are present
      expect(results.inputs).toBeDefined();
      expect(results.adjustedDesignValues).toBeDefined();
      expect(results.designValueSpecificFactors).toBeDefined();
      expect(results.analysisResults).toBeDefined();
      expect(results.bendingCheck).toBeDefined();
      expect(results.shearCheck).toBeDefined();
      expect(results.deflectionCheck).toBeDefined();
      expect(results.isDesignAcceptable).toBeDefined();
      expect(results.controllingCriteria).toBeDefined();

      // Check that design-value-specific factors are present
      expect(results.designValueSpecificFactors.Fbx_pos).toBeDefined();
      expect(results.designValueSpecificFactors.Ft).toBeDefined();
      expect(results.designValueSpecificFactors.Ex).toBeDefined();

      // Check that stress calculations were performed
      expect(results.analysisResults.maxBendingStress).toBeGreaterThan(0);
      expect(results.analysisResults.maxShearStress).toBeGreaterThan(0);

      // Check that design checks have ratios
      expect(results.bendingCheck.ratio).toBeGreaterThan(0);
      expect(results.shearCheck.ratio).toBeGreaterThan(0);
      expect(results.deflectionCheck.ratio).toBeGreaterThan(0);

      // Check that stress type is included
      expect(results.bendingCheck.stressType).toBe(BENDING_STRESS_TYPES.POSITIVE);
    });

    test("should identify controlling criteria when design fails", () => {
      const inputs = createSampleGlulamAnalysisInputs();

      // Increase loads to cause failure
      inputs.analysisResults.maxMoment = 2000000; // Very high moment

      const results = analyzeGlulamBeam(inputs);

      expect(results.isDesignAcceptable).toBe(false);
      expect(results.controllingCriteria).toContain("Bending stress");
      expect(results.bendingCheck.passes).toBe(false);
    });

    test("should handle unbalanced layup with negative bending", () => {
      const inputs = createSampleGlulamAnalysisInputs();
      inputs.serviceConditions.layupType = LAYUP_TYPES.UNBALANCED;
      inputs.analysisResults.bendingStressType = BENDING_STRESS_TYPES.NEGATIVE;

      const results = analyzeGlulamBeam(inputs);

      expect(results.bendingCheck.stressType).toBe(BENDING_STRESS_TYPES.NEGATIVE);
      expect(results.bendingCheck.allowableStress).toBe(
        results.adjustedDesignValues.Fbx_neg_prime
      );
    });

    test("should handle y-axis bending", () => {
      const inputs = createSampleGlulamAnalysisInputs();
      inputs.serviceConditions.primaryAxis = GLULAM_AXIS.Y_AXIS;

      const results = analyzeGlulamBeam(inputs);

      expect(results.bendingCheck.allowableStress).toBe(
        results.adjustedDesignValues.Fby_prime
      );
      expect(results.shearCheck.allowableStress).toBe(
        results.adjustedDesignValues.Fvy_prime
      );
    });
  });

  describe("Error Handling", () => {
    test("should handle curved member with excessive curvature", () => {
      const inputs = createSampleGlulamAnalysisInputs();
      inputs.member.radiusOfCurvature = 50; // Small radius
      inputs.member.laminationThickness = 2.0; // Large thickness

      expect(() => {
        analyzeGlulamBeam(inputs);
      }).toThrow(/Curvature ratio.*exceeds maximum allowed/);
    });
  });

  describe("Edge Cases", () => {
    test("should handle very large deflection limit", () => {
      const inputs = createSampleGlulamAnalysisInputs();
      inputs.deflectionLimit = 50; // Very large limit (L/50 instead of L/240) - much more lenient

      const results = analyzeGlulamBeam(inputs);
      expect(results.deflectionCheck.passes).toBe(true);
    });

    test("should handle very small member dimensions", () => {
      const inputs = createSampleGlulamAnalysisInputs();
      inputs.member.width = 1.75; // Small width
      inputs.member.depth = 3.5; // Small depth

      const results = analyzeGlulamBeam(inputs);
      // Volume factor for glulam increases for smaller members, should be reflected in bending factors
      expect(results.designValueSpecificFactors.Fbx_pos.CV).toBeGreaterThanOrEqual(1.0); // Volume factor should be at least 1.0
    });
  });
});
