/**
 * Sawn Lumber Beam Analysis
 *
 * This module provides comprehensive beam analysis for sawn lumber members following
 * NDS 2018 standards and Table 4.3.1 adjustment factor applicability rules.
 */

import { LoadType } from "../../../types/load/load-type";

// Import adjustment factor functions
import {
  getLoadDurationFactorByLoadType,
  getWetServiceFactor,
  getTemperatureFactor,
  getIncisingFactorCi,
  getSizeFactor,
  getFlatUseFactorCfu,
  getRepetitiveMemberFactor,
  getBeamStabilityFactorCL,
  getBearingAreaFactor,
  getLrfdFormatConversionFactorKF,
  getLrfdResistanceFactorPhi,
  getLrfdTimeEffectFactorLambda,
  getVolumeFactor,
  type WetServiceFactorInput,
  type TemperatureFactorInput,
  type SizeFactorInput,
  type FlatUseFactorInput,
  type RepetitiveMemberFactorInput,
  type VolumeFactorInput,
  type BearingAreaFactorInput,
} from "../adjustment-factors";

// Import constants and types
import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  MOISTURE_CONDITIONS,
  REPETITIVE_MEMBER_TYPES,
  // Import shared design method constant
  DESIGN_METHODS,
  DesignMethod,
  DEFAULT_DEFLECTION_LIMITS,
  SECTION_PROPERTY_CONSTANTS,
  BEAM_ANALYSIS_LIMITS,
  LUMBER_CATEGORIES,
} from "../constants";

import { WET_SERVICE_LUMBER_CATEGORIES } from "../adjustment-factors/wet-service/wet-service";

import { SIZE_FACTOR_LUMBER_GRADES } from "../adjustment-factors/size-factor/size-factor";

import { FLAT_USE_LUMBER_GRADES } from "../adjustment-factors/flat-use-factor/flat-use-factor";

import { REPETITIVE_MEMBER_LUMBER_CATEGORIES } from "../constants";

/**
 * Reference design values for sawn lumber
 */
export interface ReferenceDesignValues {
  Fb: number; // Bending stress (psi)
  Ft: number; // Tensile stress parallel to grain (psi)
  Fv: number; // Shear stress parallel to grain (psi)
  Fc_perp: number; // Compressive stress perpendicular to grain (psi)
  Fc: number; // Compressive stress parallel to grain (psi)
  E: number; // Modulus of elasticity (psi)
  Emin: number; // Minimum modulus of elasticity (psi)
  G: number; // Shear modulus (psi)
}

/**
 * Adjusted design values after applying all factors
 */
export interface AdjustedDesignValues {
  Fb_prime: number; // F'b
  Ft_prime: number; // F't
  Fv_prime: number; // F'v
  Fc_perp_prime: number; // F'c⊥
  Fc_prime: number; // F'c
  E_prime: number; // E'
  Emin_prime: number; // E'min
}

/**
 * Member geometry and properties
 */
export interface MemberGeometry {
  length: number; // Member length (in)
  width: number; // Cross-section width (in)
  depth: number; // Cross-section depth (in)
  unbracedLength: number; // Unbraced length for lateral stability (in)
}

/**
 * Service conditions affecting the member
 */
export interface ServiceConditions {
  moistureContent: number; // Moisture content (%)
  temperature: number; // Service temperature (°F)
  loadDuration: LoadType; // Load duration category
  isIncised: boolean; // Whether member is incised
  isRepetitiveMember: boolean; // Repetitive member application
  isFlatUse: boolean; // Flat use orientation
}

/**
 * Material properties including species and grade
 */
export interface MaterialProperties {
  species: string;
  grade: string;
  lumberCategory: string; // e.g., "Dimension Lumber", "Beams and Stringers"
  referenceDesignValues: ReferenceDesignValues;
}

/**
 * Calculated adjustment factors per NDS Table 4.3.1
 */
export interface AdjustmentFactors {
  CD: number; // Load duration factor
  CM: number; // Wet service factor
  Ct: number; // Temperature factor
  CL: number; // Beam stability factor
  CF: number; // Size factor
  Cfu: number; // Flat use factor
  Ci: number; // Incising factor
  Cr: number; // Repetitive member factor
  CP: number; // Column stability factor (for compression)
  Cb: number; // Bearing area factor
  CT: number; // Buckling stiffness factor
  CV: number; // Volume factor
  // LRFD factors
  KF: number; // Format conversion factor
  phi: number; // Resistance factor
  lambda: number; // Time effect factor
}

/**
 * Input structural analysis results (calculated externally)
 */
export interface InputAnalysisResults {
  maxMoment: number; // **IN-LB** - Maximum moment from structural analysis
  maxShear: number; // **LB** - Maximum shear from structural analysis
  maxDeflection: number; // **IN** - Maximum deflection from structural analysis
}

/**
 * Analysis results with calculated stress ratios
 */
export interface AnalysisResults extends InputAnalysisResults {
  maxBendingStress: number; // **PSI** - Calculated bending stress
  maxShearStress: number; // **PSI** - Calculated shear stress
  bendingStressRatio: number; // Actual/allowable bending stress ratio
  shearStressRatio: number; // Actual/allowable shear stress ratio
  deflectionRatio: number; // Actual/allowable deflection ratio
}

/**
 * Complete input interface for sawn lumber beam analysis
 */
export interface SawnLumberBeamAnalysisInputs {
  member: MemberGeometry;
  material: MaterialProperties;
  serviceConditions: ServiceConditions;
  analysisResults: InputAnalysisResults; // Pre-calculated structural results
  designMethod: DesignMethod;
  deflectionLimit: number; // L/deflectionLimit (e.g., 240 for L/240)
  includeSelfWeight?: boolean; // Optional flag for documentation (not used in calculations)
}

/**
 * Complete beam analysis results
 */
export interface SawnLumberBeamAnalysisResults {
  // Input summary
  inputs: SawnLumberBeamAnalysisInputs;

  // Calculated adjustment factors (legacy - for backwards compatibility)
  adjustmentFactors: AdjustmentFactors;

  // Adjusted design values
  adjustedDesignValues: AdjustedDesignValues;

  // Design-value-specific adjustment factors
  designValueSpecificFactors: {
    Fb: Partial<AdjustmentFactors>;
    Ft: Partial<AdjustmentFactors>;
    Fv: Partial<AdjustmentFactors>;
    Fc_perp: Partial<AdjustmentFactors>;
    Fc: Partial<AdjustmentFactors>;
    E: Partial<AdjustmentFactors>;
    Emin: Partial<AdjustmentFactors>;
  };

  // Structural analysis results
  analysisResults: AnalysisResults;

  // Design verification
  isDesignAcceptable: boolean;
  controllingCriteria: string;

  // Detailed checks
  bendingCheck: {
    allowableStress: number;
    actualStress: number;
    ratio: number;
    passes: boolean;
  };

  shearCheck: {
    allowableStress: number;
    actualStress: number;
    ratio: number;
    passes: boolean;
  };

  deflectionCheck: {
    allowableDeflection: number;
    actualDeflection: number;
    ratio: number;
    passes: boolean;
  };
}

/**
 * Calculate adjustment factors specific to a design value type
 */
function calculateDesignValueSpecificFactors(
  designValueType: DesignValueType,
  material: MaterialProperties,
  member: MemberGeometry,
  serviceConditions: ServiceConditions,
  designMethod: DesignMethod
): Partial<AdjustmentFactors> {
  const factors: Partial<AdjustmentFactors> = {};

  // Load Duration Factor (CD) - ASD only, applies to Fb, Ft, Fv, Fc
  if (designMethod === DESIGN_METHODS.ASD) {
    if (([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, 
         DESIGN_VALUE_TYPES.SHEAR, DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL] as string[]).includes(designValueType)) {
      factors.CD = getLoadDurationFactorByLoadType(serviceConditions.loadDuration);
    }
  }

  // Time Effect Factor (lambda) - LRFD only, applies to Fb, Ft, Fv, Fc
  if (designMethod === DESIGN_METHODS.LRFD) {
    if (([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, 
         DESIGN_VALUE_TYPES.SHEAR, DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL] as string[]).includes(designValueType)) {
      factors.lambda = 0.8; // Default time effect factor - would need proper load combination mapping
    }
  }

  // Wet Service Factor (CM) - applies to all design values, but value depends on type
  if (serviceConditions.moistureContent > 19) {
    const wetServiceInput: WetServiceFactorInput = {
      moistureContent: serviceConditions.moistureContent,
      designValueType: designValueType,
      lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      speciesGroup: "other_species" as any,
    };
    factors.CM = getWetServiceFactor(wetServiceInput);
  } else {
    factors.CM = 1.0;
  }

  // Temperature Factor (Ct) - applies to all design values
  if (serviceConditions.temperature > 100) {
    const tempInput: TemperatureFactorInput = {
      temperature: serviceConditions.temperature,
      moistureCondition: serviceConditions.moistureContent > 19 
        ? MOISTURE_CONDITIONS.WET 
        : MOISTURE_CONDITIONS.DRY,
      designValueType: designValueType,
    };
    factors.Ct = getTemperatureFactor(tempInput);
  } else {
    factors.Ct = 1.0;
  }

  // Size Factor (CF) - applies to Fb, Ft, Fc
  if (([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, 
       DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL] as string[]).includes(designValueType)) {
    const sizeInput: SizeFactorInput = {
      width: member.width,
      depth: member.depth,
      designValueType: designValueType as "Fb" | "Ft" | "Fc",
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.NO_1,
    };
    factors.CF = getSizeFactor(sizeInput);
  }

  // Flat Use Factor (Cfu) - applies to Fb only
  if (designValueType === DESIGN_VALUE_TYPES.BENDING && serviceConditions.isFlatUse) {
    const flatUseInput: FlatUseFactorInput = {
      width: member.width,
      depth: member.depth,
      designValueType: designValueType as "Fb",
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    factors.Cfu = getFlatUseFactorCfu(flatUseInput);
  } else {
    factors.Cfu = 1.0;
  }

  // Incising Factor (Ci) - applies to Fb, Ft, Fv, Fc, Fc_perp, E, Emin
  if ([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, DESIGN_VALUE_TYPES.SHEAR,
       DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
       DESIGN_VALUE_TYPES.E, DESIGN_VALUE_TYPES.E_MIN].includes(designValueType)) {
    if (serviceConditions.isIncised) {
      // Only call getIncisingFactorCi for supported design value types
      if (([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, 
           DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, DESIGN_VALUE_TYPES.SHEAR] as string[]).includes(designValueType)) {
        factors.Ci = getIncisingFactorCi(designValueType as "Fb" | "Ft" | "Fc" | "Fv");
      } else {
        // For E and E_MIN, use the modulus of elasticity factor
        factors.Ci = 0.95; // Standard incising factor for modulus values
      }
    } else {
      factors.Ci = 1.0;
    }
  }

  // Repetitive Member Factor (Cr) - applies to Fb only
  if (designValueType === DESIGN_VALUE_TYPES.BENDING && serviceConditions.isRepetitiveMember) {
    const repInput: RepetitiveMemberFactorInput = {
      thickness: member.width,
      memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
      spacingOnCenter: 16,
      numberOfMembers: 3,
      loadDistributingElements: ["flooring" as any],
      lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      designValueType: designValueType as "Fb",
    };
    factors.Cr = getRepetitiveMemberFactor(repInput);
  } else {
    factors.Cr = 1.0;
  }

  // Column Stability Factor (CP) - applies to Fc only
  if (designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL) {
    // For beam analysis, CP is typically 1.0 unless column buckling is considered
    factors.CP = 1.0;
  }

  // Bearing Area Factor (Cb) - applies to Fc_perp only
  if (designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR) {
    const bearingInput: BearingAreaFactorInput = {
      bearingLength: member.width, // Simplified assumption
    };
    factors.Cb = getBearingAreaFactor(bearingInput);
  }

  // Buckling Stiffness Factor (CT) - applies to Emin only
  if (designValueType === DESIGN_VALUE_TYPES.E_MIN) {
    factors.CT = 1.0; // Default value, would need specific calculation
  }

  // Beam Stability Factor (CL) - applies to Fb only
  if (designValueType === DESIGN_VALUE_TYPES.BENDING) {
    // Calculate Fb* (all factors except CL)
    const FbStar = material.referenceDesignValues.Fb *
      (factors.CD || factors.lambda || 1.0) *
      (factors.CM || 1.0) *
      (factors.Ct || 1.0) *
      (factors.CF || 1.0) *
      (factors.Ci || 1.0) *
      (factors.Cr || 1.0);

    // Calculate E'min for CL calculation
    const EminFactors = calculateDesignValueSpecificFactors(
      DESIGN_VALUE_TYPES.E_MIN,
      material, member, serviceConditions, designMethod
    );
    const EminPrime = material.referenceDesignValues.Emin *
      (EminFactors.CM || 1.0) *
      (EminFactors.Ct || 1.0) *
      (EminFactors.Ci || 1.0) *
      (EminFactors.CT || 1.0);

    factors.CL = getBeamStabilityFactorCL(
      member.unbracedLength,
      member.depth,
      member.width,
      EminPrime,
      FbStar
    );
  }

  // LRFD Format Conversion Factor (KF) - different values for each design value
  if (designMethod === DESIGN_METHODS.LRFD) {
    // Only call for supported design value types
    if (([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, 
         DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, DESIGN_VALUE_TYPES.SHEAR] as string[]).includes(designValueType)) {
      factors.KF = getLrfdFormatConversionFactorKF(designValueType as "Fb" | "Ft" | "Fc" | "Fv");
    } else if (designValueType === DESIGN_VALUE_TYPES.E_MIN) {
      factors.KF = 1.76; // From NDS Table N1
    } else if (designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR) {
      factors.KF = 1.67; // From NDS Table N1
    } else {
      factors.KF = 1.0; // Default for E
    }
  }

  // LRFD Resistance Factor (phi) - different values for each design value
  if (designMethod === DESIGN_METHODS.LRFD) {
    // Only call for supported design value types
    if (([DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.TENSION_PARALLEL, 
         DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, DESIGN_VALUE_TYPES.SHEAR] as string[]).includes(designValueType)) {
      factors.phi = getLrfdResistanceFactorPhi(designValueType as "Fb" | "Ft" | "Fc" | "Fv");
    } else if (designValueType === DESIGN_VALUE_TYPES.E_MIN) {
      factors.phi = 0.85; // From NDS Table N2
    } else if (designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR) {
      factors.phi = 0.90; // From NDS Table N2
    } else {
      factors.phi = 1.0; // Default for E
    }
  }

  return factors;
}

/**
 * Calculate all applicable adjustment factors based on NDS Table 4.3.1
 */
export function calculateAdjustmentFactors(
  material: MaterialProperties,
  member: MemberGeometry,
  serviceConditions: ServiceConditions,
  designMethod: DesignMethod
): AdjustmentFactors {
  // Calculate factors for bending (most comprehensive set)
  const bendingFactors = calculateDesignValueSpecificFactors(
    DESIGN_VALUE_TYPES.BENDING,
    material, member, serviceConditions, designMethod
  );

  // Return a representative set of factors (mainly for backwards compatibility)
  // In practice, each design value should use its own specific factors
  return {
    CD: bendingFactors.CD || 1.0,
    CM: bendingFactors.CM || 1.0,
    Ct: bendingFactors.Ct || 1.0,
    CL: bendingFactors.CL || 1.0,
    CF: bendingFactors.CF || 1.0,
    Cfu: bendingFactors.Cfu || 1.0,
    Ci: bendingFactors.Ci || 1.0,
    Cr: bendingFactors.Cr || 1.0,
    CP: 1.0,
    Cb: 1.0,
    CT: 1.0,
    CV: 1.0,
    KF: bendingFactors.KF || 1.0,
    phi: bendingFactors.phi || 1.0,
    lambda: bendingFactors.lambda || 1.0,
  };
}

/**
 * Result of adjusted design values calculation with design-value-specific factors
 */
export interface AdjustedDesignValuesResult {
  adjustedDesignValues: AdjustedDesignValues;
  designValueSpecificFactors: {
    Fb: Partial<AdjustmentFactors>;
    Ft: Partial<AdjustmentFactors>;
    Fv: Partial<AdjustmentFactors>;
    Fc_perp: Partial<AdjustmentFactors>;
    Fc: Partial<AdjustmentFactors>;
    E: Partial<AdjustmentFactors>;
    Emin: Partial<AdjustmentFactors>;
  };
}

/**
 * Apply design-value-specific adjustment factors per NDS Table 4.3.1
 */
export function calculateAdjustedDesignValues(
  referenceValues: ReferenceDesignValues,
  designMethod: DesignMethod,
  material?: MaterialProperties,
  member?: MemberGeometry,
  serviceConditions?: ServiceConditions
): AdjustedDesignValuesResult {
  // If detailed inputs are not provided, return empty result
  if (!material || !member || !serviceConditions) {
    throw new Error("Material, member, and service conditions are required for design-value-specific calculations");
  }

  // Calculate design-value-specific factors according to NDS Table 4.3.1
  const fbFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.BENDING, material, member, serviceConditions, designMethod);
  const ftFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.TENSION_PARALLEL, material, member, serviceConditions, designMethod);
  const fvFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.SHEAR, material, member, serviceConditions, designMethod);
  const fcFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, material, member, serviceConditions, designMethod);
  const fcPerpFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR, material, member, serviceConditions, designMethod);
  const eFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.E, material, member, serviceConditions, designMethod);
  const eminFactors = calculateDesignValueSpecificFactors(DESIGN_VALUE_TYPES.E_MIN, material, member, serviceConditions, designMethod);

  let adjustedDesignValues: AdjustedDesignValues;

  if (designMethod === DESIGN_METHODS.ASD) {
    // ASD Method: F' = F × (applicable adjustment factors from Table 4.3.1)
    adjustedDesignValues = {
      // Fb' = Fb × CD × CM × Ct × CL × CF × Cfu × Ci × Cr
      Fb_prime: referenceValues.Fb * 
        (fbFactors.CD || 1.0) * 
        (fbFactors.CM || 1.0) * 
        (fbFactors.Ct || 1.0) * 
        (fbFactors.CL || 1.0) * 
        (fbFactors.CF || 1.0) * 
        (fbFactors.Cfu || 1.0) * 
        (fbFactors.Ci || 1.0) * 
        (fbFactors.Cr || 1.0),

      // Ft' = Ft × CD × CM × Ct × CF × Ci
      Ft_prime: referenceValues.Ft * 
        (ftFactors.CD || 1.0) * 
        (ftFactors.CM || 1.0) * 
        (ftFactors.Ct || 1.0) * 
        (ftFactors.CF || 1.0) * 
        (ftFactors.Ci || 1.0),

      // Fv' = Fv × CD × CM × Ct × Ci
      Fv_prime: referenceValues.Fv * 
        (fvFactors.CD || 1.0) * 
        (fvFactors.CM || 1.0) * 
        (fvFactors.Ct || 1.0) * 
        (fvFactors.Ci || 1.0),

      // Fc_perp' = Fc_perp × CM × Ct × Ci × Cb
      Fc_perp_prime: referenceValues.Fc_perp * 
        (fcPerpFactors.CM || 1.0) * 
        (fcPerpFactors.Ct || 1.0) * 
        (fcPerpFactors.Ci || 1.0) * 
        (fcPerpFactors.Cb || 1.0),

      // Fc' = Fc × CD × CM × Ct × CF × Ci × CP
      Fc_prime: referenceValues.Fc * 
        (fcFactors.CD || 1.0) * 
        (fcFactors.CM || 1.0) * 
        (fcFactors.Ct || 1.0) * 
        (fcFactors.CF || 1.0) * 
        (fcFactors.Ci || 1.0) * 
        (fcFactors.CP || 1.0),

      // E' = E × CM × Ct × Ci (no CD factor for E)
      E_prime: referenceValues.E * 
        (eFactors.CM || 1.0) * 
        (eFactors.Ct || 1.0) * 
        (eFactors.Ci || 1.0),

      // Emin' = Emin × CM × Ct × Ci × CT
      Emin_prime: referenceValues.Emin * 
        (eminFactors.CM || 1.0) * 
        (eminFactors.Ct || 1.0) * 
        (eminFactors.Ci || 1.0) * 
        (eminFactors.CT || 1.0),
    };
  } else {
    // LRFD Method: F'n = F × λ × (other factors) × KF × φ (where applicable)
    adjustedDesignValues = {
      // Fb' = Fb × λ × CM × Ct × CL × CF × Cfu × Ci × Cr × KF × φ
      Fb_prime: referenceValues.Fb * 
        (fbFactors.lambda || 1.0) * 
        (fbFactors.CM || 1.0) * 
        (fbFactors.Ct || 1.0) * 
        (fbFactors.CL || 1.0) * 
        (fbFactors.CF || 1.0) * 
        (fbFactors.Cfu || 1.0) * 
        (fbFactors.Ci || 1.0) * 
        (fbFactors.Cr || 1.0) * 
        (fbFactors.KF || 1.0) * 
        (fbFactors.phi || 1.0),

      // Ft' = Ft × λ × CM × Ct × CF × Ci × KF × φ
      Ft_prime: referenceValues.Ft * 
        (ftFactors.lambda || 1.0) * 
        (ftFactors.CM || 1.0) * 
        (ftFactors.Ct || 1.0) * 
        (ftFactors.CF || 1.0) * 
        (ftFactors.Ci || 1.0) * 
        (ftFactors.KF || 1.0) * 
        (ftFactors.phi || 1.0),

      // Fv' = Fv × λ × CM × Ct × Ci × KF × φ
      Fv_prime: referenceValues.Fv * 
        (fvFactors.lambda || 1.0) * 
        (fvFactors.CM || 1.0) * 
        (fvFactors.Ct || 1.0) * 
        (fvFactors.Ci || 1.0) * 
        (fvFactors.KF || 1.0) * 
        (fvFactors.phi || 1.0),

      // Fc_perp' = Fc_perp × CM × Ct × Ci × Cb × KF × φ (no λ factor)
      Fc_perp_prime: referenceValues.Fc_perp * 
        (fcPerpFactors.CM || 1.0) * 
        (fcPerpFactors.Ct || 1.0) * 
        (fcPerpFactors.Ci || 1.0) * 
        (fcPerpFactors.Cb || 1.0) * 
        (fcPerpFactors.KF || 1.0) * 
        (fcPerpFactors.phi || 1.0),

      // Fc' = Fc × λ × CM × Ct × CF × Ci × CP × KF × φ
      Fc_prime: referenceValues.Fc * 
        (fcFactors.lambda || 1.0) * 
        (fcFactors.CM || 1.0) * 
        (fcFactors.Ct || 1.0) * 
        (fcFactors.CF || 1.0) * 
        (fcFactors.Ci || 1.0) * 
        (fcFactors.CP || 1.0) * 
        (fcFactors.KF || 1.0) * 
        (fcFactors.phi || 1.0),

      // E' = E × CM × Ct × Ci (no λ, KF, or φ factors)
      E_prime: referenceValues.E * 
        (eFactors.CM || 1.0) * 
        (eFactors.Ct || 1.0) * 
        (eFactors.Ci || 1.0),

      // Emin' = Emin × CM × Ct × Ci × CT × KF × φ (no λ factor)
      Emin_prime: referenceValues.Emin * 
        (eminFactors.CM || 1.0) * 
        (eminFactors.Ct || 1.0) * 
        (eminFactors.Ci || 1.0) * 
        (eminFactors.CT || 1.0) * 
        (eminFactors.KF || 1.0) * 
        (eminFactors.phi || 1.0),
    };
  }

  return {
    adjustedDesignValues,
    designValueSpecificFactors: {
      Fb: fbFactors,
      Ft: ftFactors,
      Fv: fvFactors,
      Fc_perp: fcPerpFactors,
      Fc: fcFactors,
      E: eFactors,
      Emin: eminFactors,
    },
  };
}

/**
 * Calculate actual stresses from analysis results
 */
export function calculateStresses(
  member: MemberGeometry,
  analysisResults: InputAnalysisResults
): { bendingStress: number; shearStress: number } {
  const sectionModulus = (member.width * Math.pow(member.depth, 2)) / 6;
  const area = member.width * member.depth;

  const bendingStress = analysisResults.maxMoment / sectionModulus;
  const shearStress = (1.5 * analysisResults.maxShear) / area; // Rectangular section

  return { bendingStress, shearStress };
}

/**
 * Check bending stress against allowable value
 */
export function checkBendingStress(
  actualStress: number,
  allowableStress: number
): {
  allowableStress: number;
  actualStress: number;
  ratio: number;
  passes: boolean;
} {
  const ratio = actualStress / allowableStress;
  return {
    allowableStress,
    actualStress,
    ratio,
    passes: ratio <= 1.0,
  };
}

/**
 * Check shear stress against allowable value
 */
export function checkShearStress(
  actualStress: number,
  allowableStress: number
): {
  allowableStress: number;
  actualStress: number;
  ratio: number;
  passes: boolean;
} {
  const ratio = actualStress / allowableStress;
  return {
    allowableStress,
    actualStress,
    ratio,
    passes: ratio <= 1.0,
  };
}

/**
 * Check deflection against allowable limit
 */
export function checkDeflection(
  actualDeflection: number,
  beamLength: number,
  deflectionLimit: number
): {
  allowableDeflection: number;
  actualDeflection: number;
  ratio: number;
  passes: boolean;
} {
  const allowableDeflection = beamLength / deflectionLimit;
  const ratio = actualDeflection / allowableDeflection;
  return {
    allowableDeflection,
    actualDeflection,
    ratio,
    passes: ratio <= 1.0,
  };
}

/**
 * Main analysis function for sawn lumber beams
 */
export function analyzeSawnLumberBeam(
  inputs: SawnLumberBeamAnalysisInputs
): SawnLumberBeamAnalysisResults {
  // Calculate adjustment factors
  const adjustmentFactors = calculateAdjustmentFactors(
    inputs.material,
    inputs.member,
    inputs.serviceConditions,
    inputs.designMethod
  );

  // Calculate adjusted design values using the new approach with design-value-specific factors
  const adjustedDesignValuesResult = calculateAdjustedDesignValues(
    inputs.material.referenceDesignValues,
    inputs.designMethod,
    inputs.material,
    inputs.member,
    inputs.serviceConditions
  );

  // Calculate actual stresses from provided analysis results
  const stresses = calculateStresses(inputs.member, inputs.analysisResults);

  // Perform design checks
  const bendingCheck = checkBendingStress(
    stresses.bendingStress,
    adjustedDesignValuesResult.adjustedDesignValues.Fb_prime
  );

  const shearCheck = checkShearStress(
    stresses.shearStress,
    adjustedDesignValuesResult.adjustedDesignValues.Fv_prime
  );

  const deflectionCheck = checkDeflection(
    inputs.analysisResults.maxDeflection,
    inputs.member.length,
    inputs.deflectionLimit
  );

  // Create complete analysis results
  const analysisResults: AnalysisResults = {
    maxMoment: inputs.analysisResults.maxMoment,
    maxShear: inputs.analysisResults.maxShear,
    maxDeflection: inputs.analysisResults.maxDeflection,
    maxBendingStress: stresses.bendingStress,
    maxShearStress: stresses.shearStress,
    bendingStressRatio: bendingCheck.ratio,
    shearStressRatio: shearCheck.ratio,
    deflectionRatio: deflectionCheck.ratio,
  };

  // Determine overall design adequacy
  const isDesignAcceptable =
    bendingCheck.passes && shearCheck.passes && deflectionCheck.passes;

  // Determine controlling criteria
  let controllingCriteria = "Design is acceptable";
  if (!isDesignAcceptable) {
    const maxRatio = Math.max(
      bendingCheck.ratio,
      shearCheck.ratio,
      deflectionCheck.ratio
    );

    if (maxRatio === bendingCheck.ratio) {
      controllingCriteria = "Bending stress";
    } else if (maxRatio === shearCheck.ratio) {
      controllingCriteria = "Shear stress";
    } else {
      controllingCriteria = "Deflection";
    }
  }

  return {
    inputs,
    adjustmentFactors,
    adjustedDesignValues: adjustedDesignValuesResult.adjustedDesignValues,
    designValueSpecificFactors: adjustedDesignValuesResult.designValueSpecificFactors,
    analysisResults,
    bendingCheck,
    shearCheck,
    deflectionCheck,
    isDesignAcceptable,
    controllingCriteria,
  };
}

/**
 * Create sample analysis inputs for testing and demonstration
 */
export function createSampleAnalysisInputs(): SawnLumberBeamAnalysisInputs {
  return {
    member: {
      length: 240, // 20 ft
      width: 3.5, // 2x12 actual width
      depth: 11.25, // 2x12 actual depth
      unbracedLength: 240,
    },
    material: {
      species: "Douglas Fir-Larch",
      grade: "No. 1",
      lumberCategory: "Dimension Lumber",
      referenceDesignValues: {
        Fb: 1000,
        Ft: 675,
        Fv: 180,
        Fc_perp: 625,
        Fc: 1350,
        E: 1600000,
        Emin: 580000,
        G: 90000,
      },
    },
    serviceConditions: {
      moistureContent: 15,
      temperature: 70,
      loadDuration: LoadType.LIVE,
      isIncised: false,
      isRepetitiveMember: true,
      isFlatUse: false,
    },
    analysisResults: {
      maxMoment: 500000, // 500,000 in-lb
      maxShear: 5000, // 5,000 lb
      maxDeflection: 0.8, // 0.8 in
    },
    designMethod: DESIGN_METHODS.ASD,
    deflectionLimit: 240,
    includeSelfWeight: true,
  };
}

// Re-export DesignMethod for external use
export type { DesignMethod } from "../constants";
