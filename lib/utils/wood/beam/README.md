# Wood Beam Analysis Modules

This directory contains comprehensive beam analysis modules for wood structural design following NDS 2018 standards.

## Overview

The beam analysis modules provide:
- **Sawn Lumber Beam Analysis** - Traditional dimensional lumber beam design
- **Glulam Beam Analysis** - Structural glued laminated timber beam design  
- Comprehensive adjustment factor calculations per NDS standards
- Both ASD (Allowable Stress Design) and LRFD (Load and Resistance Factor Design) methods
- Modular, testable functions with clear documentation

## Modules

### 1. Sawn Lumber Beam Analysis (`sawn-lumber-beam-analysis.ts`)

Analyzes sawn lumber beams per NDS Section 4 and Table 4.3.1.

**Key Features:**
- Standard adjustment factors: CD, CM, Ct, CL, CF, Cfu, Ci, Cr, CP, Cb
- Size factor calculations for dimension lumber
- Beam stability factor for lateral-torsional buckling
- Repetitive member factor applications

**Example Usage:**
```typescript
import { analyzeSawnLumberBeam, createSampleAnalysisInputs } from './sawn-lumber-beam-analysis';

// Create sample inputs or customize
const inputs = createSampleAnalysisInputs();
inputs.member.depth = 11.25; // 2x12
inputs.analysisResults.maxMoment = 400000; // 400 k-in

// Perform analysis
const results = analyzeSawnLumberBeam(inputs);

console.log(`Design Acceptable: ${results.isDesignAcceptable}`);
console.log(`Controlling Criteria: ${results.controllingCriteria}`);
console.log(`Bending Stress Ratio: ${results.bendingCheck.ratio.toFixed(2)}`);
```

### 2. Glulam Beam Analysis (`glulam-beam-analysis.ts`)

Analyzes structural glued laminated timber beams per NDS Section 5 and Table 5.3.1.

**Key Features:**
- Directional design values (x-x strong axis, y-y weak axis)
- Positive and negative bending values (Fbx+, Fbx-)  
- Glulam-specific factors: Volume Factor (CV), Curvature Factor (Cc), Stress Interaction Factor (CI), Shear Reduction Factor (Cvr)
- Curved and tapered member support
- Balanced and unbalanced layup configurations

**Example Usage:**
```typescript
import { 
  analyzeGlulamBeam, 
  createSampleGlulamAnalysisInputs, 
  GlulamAxis, 
  BendingStressType, 
  LayupType 
} from './glulam-beam-analysis';

// Create and customize inputs
const inputs = createSampleGlulamAnalysisInputs();
inputs.member.width = 5.125;
inputs.member.depth = 21; // 5.125" x 21"
inputs.serviceConditions.primaryAxis = GlulamAxis.X_AXIS;
inputs.serviceConditions.layupType = LayupType.UNBALANCED;
inputs.analysisResults.bendingStressType = BendingStressType.POSITIVE;

// Perform analysis  
const results = analyzeGlulamBeam(inputs);

console.log(`Volume Factor: ${results.adjustmentFactors.CV.toFixed(3)}`);
console.log(`Bending Check (${results.bendingCheck.stressType}): ${results.bendingCheck.ratio.toFixed(2)}`);
```

## Core Concepts

### Design Values

Both modules work with material-specific reference design values that are adjusted using various factors:

**Sawn Lumber:**
- Fb, Ft, Fv, Fc⊥, Fc, E, Emin - Single values per grade

**Glulam:**
- Fbx+, Fbx-, Fby - Directional bending values
- Fvx, Fvy - Directional shear values  
- Fc⊥x, Fc⊥y - Directional compression perpendicular values
- Ex, Ex_min, Ey, Ey_min - Directional modulus values

### Adjustment Factors

All factors are calculated automatically based on service conditions and member properties:

| Factor | Sawn Lumber | Glulam | Purpose |
|--------|-------------|---------|---------|
| CD | ✓ | ✓ | Load duration (ASD only) |
| CM | ✓ | ✓ | Wet service conditions |
| Ct | ✓ | ✓ | Temperature effects |
| CL | ✓ | ✓ | Beam stability (lateral buckling) |
| CF | ✓ | - | Size factor (sawn lumber) |
| Cfu | ✓ | ✓ | Flat use orientation |
| Ci | ✓ | - | Incising (sawn lumber) |
| Cr | ✓ | - | Repetitive member (sawn lumber) |
| CV | - | ✓ | Volume factor (glulam) |
| Cc | - | ✓ | Curvature (curved glulam) |
| CI | - | ✓ | Stress interaction (tapered glulam) |
| Cvr | - | ✓ | Shear reduction (glulam) |

### Analysis Process

1. **Input Validation** - Check geometry, materials, service conditions
2. **Adjustment Factor Calculation** - Apply all applicable NDS factors
3. **Adjusted Design Values** - Calculate F', E' values
4. **Stress Calculation** - Convert moments/shears to actual stresses
5. **Design Checks** - Compare actual vs. allowable stresses
6. **Results** - Determine adequacy and controlling criteria

### Design Methods

Both **ASD** and **LRFD** methods are supported:

**ASD (Allowable Stress Design):**
- Uses load duration factor (CD)
- F' = F × (adjustment factors)
- Check: actual stress ≤ F'

**LRFD (Load and Resistance Factor Design):**
- Uses time effect factor (λ) instead of CD
- Rn = F'n × KF × φ  
- Check: factored load effects ≤ Rn

## Input Structure

### Member Geometry
```typescript
interface MemberGeometry {
  length: number;        // Overall length (in)
  width: number;         // Cross-section width (in) 
  depth: number;         // Cross-section depth (in)
  unbracedLength: number; // Unbraced length for stability (in)
  
  // Glulam-specific (optional)
  radiusOfCurvature?: number;    // For curved members (in)
  laminationThickness?: number;  // Lamination thickness (in)
  taperAngle?: number;          // Taper angle (degrees)
}
```

### Service Conditions
```typescript
interface ServiceConditions {
  moistureContent: number;  // Moisture content (%)
  temperature: number;      // Service temperature (°F)
  loadDuration: LoadType;   // DEAD, LIVE, SNOW, WIND, etc.
  
  // Member-specific conditions
  isIncised: boolean;           // Sawn lumber only
  isRepetitiveMember: boolean;  // Sawn lumber only
  isFlatUse: boolean;          // Both
  
  // Glulam-specific
  layupType: LayupType;        // BALANCED, UNBALANCED
  primaryAxis: GlulamAxis;     // X_AXIS, Y_AXIS
}
```

### Analysis Results
```typescript
interface InputAnalysisResults {
  maxMoment: number;           // Maximum moment (in-lb)
  maxShear: number;            // Maximum shear (lb)
  maxDeflection: number;       // Maximum deflection (in)
  bendingStressType?: BendingStressType; // Glulam only
}
```

## Advanced Features

### Curved Glulam Members

For curved glulam beams, specify radius and lamination thickness:

```typescript
inputs.member.radiusOfCurvature = 120; // inches
inputs.member.laminationThickness = 1.5; // inches

// Curvature factor Cc is calculated automatically
// Cc = 1 - (2000)(t/R)² per NDS 5.3.8
```

### Tapered Glulam Members

For tapered glulam beams, specify taper details:

```typescript
inputs.member.taperAngle = 5; // degrees
inputs.member.taperLocation = 'compression'; // or 'tension'

// Stress interaction factor CI is calculated automatically
// per NDS 5.3.9
```

### Load Duration Categories

Support for all NDS load duration categories:

```typescript
import { LoadType } from '../../../types/load/load-type';

// Available load types:
LoadType.DEAD      // CD = 0.90
LoadType.LIVE      // CD = 1.00  
LoadType.SNOW      // CD = 1.15
LoadType.WIND      // CD = 1.60
LoadType.SEISMIC   // CD = 1.60
```

## Testing

Both modules include comprehensive test suites:

```bash
# Run sawn lumber tests
npm test sawn-lumber-beam-analysis.test.ts

# Run glulam tests  
npm test glulam-beam-analysis.test.ts
```

Tests cover:
- Individual function testing
- Integration testing
- Edge cases and error handling
- NDS calculation verification
- Both ASD and LRFD methods

## Error Handling

The modules include robust error handling for:

- **Invalid geometry** - Negative dimensions, excessive slenderness
- **Excessive curvature** - t/R ratios beyond NDS limits
- **Invalid service conditions** - Unrealistic temperatures, moisture content
- **Missing required parameters** - Incomplete input validation

Example error handling:
```typescript
try {
  const results = analyzeGlulamBeam(inputs);
} catch (error) {
  if (error.message.includes('Curvature ratio')) {
    console.error('Curved member design exceeds NDS limits');
  }
}
```

## Integration with Adjustment Factors

Both modules leverage the comprehensive adjustment factors system:

```typescript
import {
  getLoadDurationFactorByLoadType,
  getWetServiceFactor,
  getTemperatureFactor,
  getBeamStabilityFactorCL,
  getVolumeFactor, // Glulam only
  // ... other factors
} from '../adjustment-factors';
```

## API Documentation

### Key Functions

**Sawn Lumber:**
- `analyzeSawnLumberBeam(inputs)` - Main analysis function
- `calculateAdjustmentFactors()` - Calculate all applicable factors
- `calculateAdjustedDesignValues()` - Apply factors to reference values
- `createSampleAnalysisInputs()` - Generate example inputs

**Glulam:**
- `analyzeGlulamBeam(inputs)` - Main analysis function
- `calculateGlulamAdjustmentFactors()` - Calculate all applicable factors  
- `calculateGlulamAdjustedDesignValues()` - Apply factors to reference values
- `getCurvatureFactor()` - Calculate curvature factor for curved members
- `getStressInteractionFactor()` - Calculate stress interaction for tapered members
- `createSampleGlulamAnalysisInputs()` - Generate example inputs

### Return Values

All analysis functions return comprehensive results including:
- Input summary
- Calculated adjustment factors
- Adjusted design values  
- Stress calculations and ratios
- Pass/fail status for each check
- Overall design adequacy
- Controlling criteria identification

## References

- **NDS 2018** - National Design Specification for Wood Construction
- **NDS Section 4** - Sawn Lumber Design
- **NDS Section 5** - Structural Glued Laminated Timber Design
- **NDS Table 4.3.1** - Adjustment Factors for Sawn Lumber
- **NDS Table 5.3.1** - Adjustment Factors for Glulam
- **ANSI/AWC NDS-2018** - National Design Specification for Wood Construction

## Contributing

When adding new features or modifications:

1. Follow existing code patterns and documentation style
2. Add comprehensive unit tests for new functions
3. Update this README for any new capabilities
4. Ensure NDS reference citations are included
5. Test both ASD and LRFD methods where applicable
