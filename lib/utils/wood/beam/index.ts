/**
 * @beam - Wood Beam Analysis Module
 * 
 * This module provides comprehensive structural analysis capabilities for wood beams,
 * supporting both sawn lumber and glued laminated timber (glulam) members according
 * to NDS (National Design Specification) standards.
 * 
 * Features:
 * - Sawn lumber beam analysis (NDS Section 4)
 * - Glulam beam analysis (NDS Section 5)
 * - Adjustment factor calculations
 * - Design verification (bending, shear, deflection)
 * - Both ASD and LRFD design methods
 * 
 * @fileoverview Main module interface for wood beam analysis
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

// =============================================================================
// SAWN LUMBER BEAM ANALYSIS
// =============================================================================

// Main analysis functions
export {
  analyzeSawnLumberBeam,
  createSampleAnalysisInputs,
} from './sawn-lumber-beam-analysis';

// Individual calculation functions
export {
  calculateAdjustmentFactors,
  calculateAdjustedDesignValues,
  calculateStresses,
  checkBendingStress,
  checkShearStress,
  checkDeflection,
} from './sawn-lumber-beam-analysis';

// Sawn lumber types and interfaces
export type {
  SawnLumberBeamAnalysisInputs,
  SawnLumberBeamAnalysisResults,
  ReferenceDesignValues,
  MemberGeometry,
  ServiceConditions,
  MaterialProperties,
  AdjustmentFactors,
  AdjustedDesignValues,
  InputAnalysisResults,
  AnalysisResults,
} from './sawn-lumber-beam-analysis';

// =============================================================================
// GLULAM BEAM ANALYSIS
// =============================================================================

// Main analysis functions
export {
  analyzeGlulamBeam,
  createSampleGlulamAnalysisInputs,
} from './glulam-beam-analysis';

// Individual calculation functions
export {
  calculateGlulamAdjustmentFactors,
  calculateGlulamAdjustedDesignValues,
  calculateGlulamStresses,
  checkGlulamBendingStress,
  checkGlulamShearStress,
  checkGlulamDeflection,
} from './glulam-beam-analysis';

// Glulam types and interfaces
export type {
  GlulamBeamAnalysisInputs,
  GlulamBeamAnalysisResults,
  GlulamReferenceDesignValues,
  GlulamMemberGeometry,
  GlulamServiceConditions,
  GlulamMaterialProperties,
  GlulamAdjustmentFactors,
  GlulamAdjustedDesignValues,
  GlulamAnalysisResults,
} from './glulam-beam-analysis';

// =============================================================================
// SHARED TYPES AND CONSTANTS
// =============================================================================

// Re-export shared design method and beam analysis types
export type {
  DesignMethod,
  GlulamAxis,
  BendingStressType,
  LayupType,
} from './glulam-beam-analysis';

// =============================================================================
// MODULE UTILITIES
// =============================================================================

/**
 * Available wood beam analysis types
 */
export const BEAM_ANALYSIS_TYPES = {
  SAWN_LUMBER: 'sawn_lumber',
  GLULAM: 'glulam',
} as const;

export type BeamAnalysisType = typeof BEAM_ANALYSIS_TYPES[keyof typeof BEAM_ANALYSIS_TYPES];

/**
 * Module version and metadata
 */
export const BEAM_MODULE_INFO = {
  version: '1.0.0',
  standards: ['NDS 2018'],
  supportedMaterials: ['Sawn Lumber', 'Glued Laminated Timber (Glulam)'],
  designMethods: ['ASD', 'LRFD'],
  capabilities: [
    'Bending stress analysis',
    'Shear stress analysis', 
    'Deflection analysis',
    'Adjustment factor calculations',
    'Design verification',
  ],
} as const; 