/**
 * Notch Adjustment Factors and Validation
 * Based on NDS 3.2.3, 4.4.3, 5.4.5, and 8.4.1
 *
 * This module handles notch limitations and shear strength adjustments for
 * notched wood bending members across different wood product types.
 *
 * Key NDS Sections:
 * - NDS 3.2.3: General notch provisions and stiffness considerations
 * - NDS 3.4.3: Shear strength adjustment for notched members
 * - NDS 4.4.3: Sawn lumber notch limitations
 * - NDS 5.4.5: Structural glued laminated timber notch limitations
 * - NDS 8.4.1: Structural composite bending member notch limitations
 */

import {
  // Wood product and notch types
  NotchWoodProductType,
  NOTCH_WOOD_PRODUCT_TYPES,
  NotchLocationType,
  NOTCH_LOCATION_TYPES,
  NotchFaceType,
  NOTCH_FACE_TYPES,
  NotchSpanRegion,
  NOTCH_SPAN_REGIONS,
  NotchCrossSectionShape,
  NOTCH_CROSS_SECTION_SHAPES,

  // Notch limitations by product type
  SAWN_LUMBER_NOTCH_LIMITS,
  GLULAM_NOTCH_LIMITS,
  STRUCTURAL_COMPOSITE_NOTCH_LIMITS,

  // Stiffness and shear calculation constants
  NOTCH_STIFFNESS_THRESHOLDS,
  NOTCH_SHEAR_COEFFICIENTS,
  NOTCH_STRESS_CONCENTRATION,

  // Reference information
  NOTCH_STRENGTH_REFERENCES,
  GENERAL_NOTCH_PROHIBITIONS,
} from "../../constants";

/**
 * Interface for basic notch geometry parameters
 */
export interface NotchGeometry {
  /** Notch depth in inches */
  depth: number;
  /** Notch length along member in inches */
  length: number;
  /** Distance from member end to notch centerline in inches */
  distanceFromEnd: number;
}

/**
 * Interface for member geometry parameters
 */
export interface MemberGeometry {
  /** Member depth (height) in inches */
  depth: number;
  /** Member width in inches */
  width: number;
  /** Member span length in inches */
  spanLength: number;
  /** Cross-sectional area for circular sections in square inches */
  crossSectionalArea?: number;
}

/**
 * Interface for connection geometry (if applicable)
 */
export interface ConnectionGeometry {
  /** Distance from unloaded edge to nearest connection edge in inches */
  distanceToConnection: number;
  /** Type of connection (for determining effective depth calculation) */
  connectionType: "split_ring" | "shear_plate" | "bolt" | "lag_screw";
}

/**
 * Interface for notch validation input parameters
 */
export interface NotchValidationInput {
  /** Wood product type */
  woodProductType: NotchWoodProductType;
  /** Notch location type */
  notchLocationType: NotchLocationType;
  /** Face where notch is located */
  notchFace: NotchFaceType;
  /** Notch geometry */
  notchGeometry: NotchGeometry;
  /** Member geometry */
  memberGeometry: MemberGeometry;
  /** Member thickness for sawn lumber (nominal thickness in inches) */
  memberThickness?: number;
  /** Whether the notch has a gradual taper (reduces stress concentration) */
  hasGradualTaper?: boolean;
  /** Connection geometry (if applicable for shear calculations) */
  connectionGeometry?: ConnectionGeometry;
}

/**
 * Interface for shear strength adjustment input parameters
 */
export interface NotchShearAdjustmentInput {
  /** Cross-section shape */
  crossSectionShape: NotchCrossSectionShape;
  /** Design shear value (Fv') in psi */
  designShearValue: number;
  /** Member geometry */
  memberGeometry: MemberGeometry;
  /** Notch geometry */
  notchGeometry: NotchGeometry;
  /** Connection geometry (if applicable) */
  connectionGeometry?: ConnectionGeometry;
}

/**
 * Interface for notch validation result
 */
export interface NotchValidationResult {
  /** Whether the notch configuration is permitted */
  isPermitted: boolean;
  /** Array of validation error messages */
  errors: string[];
  /** Array of validation warning messages */
  warnings: string[];
  /** Array of design recommendations */
  recommendations: string[];
  /** Maximum permitted notch depth in inches */
  maxPermittedDepth?: number;
  /** Maximum permitted notch depth ratio */
  maxPermittedDepthRatio?: number;
}

/**
 * Interface for stiffness impact analysis
 */
export interface StiffnessImpactAnalysis {
  /** Notch depth ratio (notch depth / member depth) */
  depthRatio: number;
  /** Notch length ratio (notch length / member depth) */
  lengthRatio: number;
  /** Whether stiffness impact is negligible per NDS ******* */
  isStiffnessImpactNegligible: boolean;
  /** Stiffness reduction estimate (qualitative) */
  stiffnessImpact: "negligible" | "moderate" | "significant";
}

/**
 * Interface for shear strength adjustment result
 */
export interface ShearStrengthAdjustmentResult {
  /** Original design shear value in psi */
  originalDesignShearValue: number;
  /** Adjusted design shear value for notched member in psi */
  adjustedDesignShearValue: number;
  /** Depth of member remaining at notch in inches */
  remainingDepth: number;
  /** Effective depth for connections in inches (if applicable) */
  effectiveDepth?: number;
  /** Shear adjustment coefficient applied */
  shearAdjustmentCoefficient: number;
}

/**
 * Interface for comprehensive notch analysis result
 */
export interface NotchAnalysisResult {
  /** Input parameters used for analysis */
  input: NotchValidationInput;
  /** Notch validation results */
  validation: NotchValidationResult;
  /** Stiffness impact analysis */
  stiffnessImpact: StiffnessImpactAnalysis;
  /** Shear strength adjustment (if applicable) */
  shearAdjustment?: ShearStrengthAdjustmentResult;
  /** Span region classification */
  spanRegion: NotchSpanRegion;
  /** NDS section references for this configuration */
  ndsReferences: string[];
}

/**
 * Validates notch configuration according to NDS requirements
 *
 * @param input - Notch validation input parameters
 * @returns Validation result with errors, warnings, and recommendations
 *
 * @example
 * ```typescript
 * const validation = validateNotchConfiguration({
 *   woodProductType: 'sawn_lumber',
 *   notchLocationType: 'end_notch',
 *   notchFace: 'compression',
 *   notchGeometry: { depth: 1.5, length: 3.0, distanceFromEnd: 0 },
 *   memberGeometry: { depth: 9.25, width: 1.5, spanLength: 144 },
 *   memberThickness: 2.0
 * });
 * ```
 */
export function validateNotchConfiguration(
  input: NotchValidationInput
): NotchValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Validate input parameters
  const inputValidation = validateNotchInput(input);
  if (!inputValidation.isValid) {
    return {
      isPermitted: false,
      errors: inputValidation.errors,
      warnings: inputValidation.warnings,
      recommendations,
    };
  }

  // Add warnings from input validation even if validation passed
  warnings.push(...inputValidation.warnings);

  // Calculate notch ratios
  const depthRatio = input.notchGeometry.depth / input.memberGeometry.depth;
  const spanRegion = determineSpanRegion(
    input.notchGeometry.distanceFromEnd,
    input.memberGeometry.spanLength
  );

  // Get applicable limits based on wood product type
  const limits = getNotchLimits(
    input.woodProductType,
    input.notchLocationType,
    input.notchFace
  );

  // Check general prohibitions
  if (limits.generalProhibition && !limits.exceptions) {
    errors.push(
      `${input.notchFace} side notches are generally prohibited for ${input.woodProductType} members`
    );
  }

  // Validate notch depth
  if (limits.maxDepthRatio && depthRatio > limits.maxDepthRatio) {
    errors.push(
      `Notch depth ratio ${depthRatio.toFixed(
        3
      )} exceeds maximum permitted ratio of ${limits.maxDepthRatio.toFixed(
        3
      )} (NDS ${limits.referenceSection})`
    );
  }

  if (
    limits.maxDepthAbsolute &&
    input.notchGeometry.depth > limits.maxDepthAbsolute
  ) {
    errors.push(
      `Notch depth ${input.notchGeometry.depth}" exceeds maximum permitted depth of ${limits.maxDepthAbsolute}" (NDS ${limits.referenceSection})`
    );
  }

  // Check sawn lumber specific requirements
  if (input.woodProductType === NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER) {
    const sawLumberValidation = validateSawnLumberNotch(input, spanRegion);
    errors.push(...sawLumberValidation.errors);
    warnings.push(...sawLumberValidation.warnings);
  }

  // Check glulam specific requirements
  if (input.woodProductType === NOTCH_WOOD_PRODUCT_TYPES.GLULAM) {
    const gulamValidation = validateGlulamNotch(input, spanRegion);
    errors.push(...gulamValidation.errors);
    warnings.push(...gulamValidation.warnings);
  }

  // Check structural composite specific requirements
  if (input.woodProductType === NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE) {
    const compositeValidation = validateStructuralCompositeNotch(
      input,
      spanRegion
    );
    errors.push(...compositeValidation.errors);
    warnings.push(...compositeValidation.warnings);
  }

  // Add design recommendations
  if (input.hasGradualTaper !== true) {
    recommendations.push(
      "Consider using a gradual taper cut instead of a square notch to reduce stress concentrations (NDS 3.2.3.1)"
    );
  }

  if (depthRatio > NOTCH_STIFFNESS_THRESHOLDS.NEGLIGIBLE_IMPACT.DEPTH_RATIO) {
    recommendations.push(
      "Notch depth may have significant impact on member stiffness - consider structural analysis"
    );
  }

  return {
    isPermitted: errors.length === 0,
    errors,
    warnings,
    recommendations,
    maxPermittedDepth: limits.maxDepthRatio
      ? limits.maxDepthRatio * input.memberGeometry.depth
      : undefined,
    maxPermittedDepthRatio: limits.maxDepthRatio,
  };
}

/**
 * Calculates adjusted design shear value for notched bending members
 * Based on NDS 3.4.3 equations
 *
 * @param input - Shear adjustment input parameters
 * @returns Adjusted shear strength and calculation details
 *
 * @example
 * ```typescript
 * const adjustment = calculateNotchShearAdjustment({
 *   crossSectionShape: 'rectangular',
 *   designShearValue: 180,
 *   memberGeometry: { depth: 9.25, width: 1.5, spanLength: 144 },
 *   notchGeometry: { depth: 1.5, length: 3.0, distanceFromEnd: 6.0 }
 * });
 * ```
 */
export function calculateNotchShearAdjustment(
  input: NotchShearAdjustmentInput
): ShearStrengthAdjustmentResult {
  const {
    memberGeometry,
    notchGeometry,
    designShearValue,
    connectionGeometry,
  } = input;

  // Calculate remaining depth at notch
  const remainingDepth = memberGeometry.depth - notchGeometry.depth;

  // Determine effective depth for connections (NDS *******)
  let effectiveDepth = remainingDepth;
  if (connectionGeometry) {
    effectiveDepth = Math.min(
      remainingDepth,
      connectionGeometry.distanceToConnection
    );
  }

  // Calculate adjusted design shear based on cross-section shape and connection distance
  let adjustedDesignShearValue: number;
  const coefficient = NOTCH_SHEAR_COEFFICIENTS.BASIC_COEFFICIENT;

  if (input.crossSectionShape === NOTCH_CROSS_SECTION_SHAPES.RECTANGULAR) {
    if (
      connectionGeometry &&
      notchGeometry.distanceFromEnd < 5 * memberGeometry.depth
    ) {
      // Connection less than 5 times depth from end (NDS Eq. 3.4-6)
      const depthRatio = effectiveDepth / memberGeometry.depth;
      adjustedDesignShearValue =
        coefficient *
        designShearValue *
        memberGeometry.width *
        remainingDepth *
        Math.pow(depthRatio, 2);
      adjustedDesignShearValue =
        adjustedDesignShearValue / (memberGeometry.width * remainingDepth); // Convert back to stress
    } else if (
      connectionGeometry &&
      notchGeometry.distanceFromEnd >= 5 * memberGeometry.depth
    ) {
      // Connection at least 5 times depth from end (NDS Eq. 3.4-7)
      adjustedDesignShearValue =
        (coefficient * designShearValue * effectiveDepth) /
        memberGeometry.depth;
    } else {
      // Basic rectangular notched member (NDS Eq. 3.4-3)
      const depthRatio = remainingDepth / memberGeometry.depth;
      adjustedDesignShearValue =
        coefficient * designShearValue * Math.pow(depthRatio, 2);
    }
  } else if (input.crossSectionShape === NOTCH_CROSS_SECTION_SHAPES.CIRCULAR) {
    // Circular cross-section (NDS Eq. 3.4-4)
    const depthRatio = remainingDepth / memberGeometry.depth;
    adjustedDesignShearValue =
      coefficient * designShearValue * Math.pow(depthRatio, 2);

    if (memberGeometry.crossSectionalArea) {
      // Account for circular cross-sectional area
      const notchedArea =
        memberGeometry.crossSectionalArea * Math.pow(depthRatio, 2);
      adjustedDesignShearValue =
        (coefficient * designShearValue * notchedArea) /
        memberGeometry.crossSectionalArea;
    }
  } else {
    // Other cross-sections - use engineering analysis (NDS 3.4.3(c))
    const depthRatio = remainingDepth / memberGeometry.depth;
    adjustedDesignShearValue =
      coefficient * designShearValue * Math.pow(depthRatio, 2);
  }

  return {
    originalDesignShearValue: designShearValue,
    adjustedDesignShearValue,
    remainingDepth,
    effectiveDepth: connectionGeometry ? effectiveDepth : undefined,
    shearAdjustmentCoefficient: coefficient,
  };
}

/**
 * Analyzes stiffness impact of notch on member
 * Based on NDS ******* thresholds
 *
 * @param notchGeometry - Notch geometry parameters
 * @param memberDepth - Member depth in inches
 * @returns Stiffness impact analysis
 *
 * @example
 * ```typescript
 * const stiffnessImpact = analyzeStiffnessImpact(
 *   { depth: 1.0, length: 2.0, distanceFromEnd: 6.0 },
 *   9.25
 * );
 * ```
 */
export function analyzeStiffnessImpact(
  notchGeometry: NotchGeometry,
  memberDepth: number
): StiffnessImpactAnalysis {
  const depthRatio = notchGeometry.depth / memberDepth;
  const lengthRatio = notchGeometry.length / memberDepth;

  const thresholds = NOTCH_STIFFNESS_THRESHOLDS.NEGLIGIBLE_IMPACT;
  const isStiffnessImpactNegligible =
    depthRatio <= thresholds.DEPTH_RATIO &&
    lengthRatio <= thresholds.LENGTH_RATIO;

  let stiffnessImpact: "negligible" | "moderate" | "significant";
  if (isStiffnessImpactNegligible) {
    stiffnessImpact = "negligible";
  } else if (depthRatio <= thresholds.DEPTH_RATIO * 1.5) {
    stiffnessImpact = "moderate";
  } else {
    stiffnessImpact = "significant";
  }

  return {
    depthRatio,
    lengthRatio,
    isStiffnessImpactNegligible,
    stiffnessImpact,
  };
}

/**
 * Performs comprehensive notch analysis including validation and adjustments
 *
 * @param input - Notch validation input parameters
 * @param shearInput - Optional shear adjustment parameters
 * @returns Comprehensive notch analysis result
 *
 * @example
 * ```typescript
 * const analysis = performNotchAnalysis({
 *   woodProductType: 'sawn_lumber',
 *   notchLocationType: 'interior_notch',
 *   notchFace: 'tension',
 *   notchGeometry: { depth: 1.0, length: 2.0, distanceFromEnd: 48.0 },
 *   memberGeometry: { depth: 9.25, width: 1.5, spanLength: 144 }
 * });
 * ```
 */
export function performNotchAnalysis(
  input: NotchValidationInput,
  shearInput?: Omit<
    NotchShearAdjustmentInput,
    "memberGeometry" | "notchGeometry"
  >
): NotchAnalysisResult {
  // Perform validation
  const validation = validateNotchConfiguration(input);

  // Analyze stiffness impact
  const stiffnessImpact = analyzeStiffnessImpact(
    input.notchGeometry,
    input.memberGeometry.depth
  );

  // Calculate shear adjustment if provided
  let shearAdjustment: ShearStrengthAdjustmentResult | undefined;
  if (shearInput) {
    const shearAdjustmentInput: NotchShearAdjustmentInput = {
      ...shearInput,
      memberGeometry: input.memberGeometry,
      notchGeometry: input.notchGeometry,
    };
    shearAdjustment = calculateNotchShearAdjustment(shearAdjustmentInput);
  }

  // Determine span region
  const spanRegion = determineSpanRegion(
    input.notchGeometry.distanceFromEnd,
    input.memberGeometry.spanLength
  );

  // Get applicable NDS references
  const ndsReferences = getApplicableNDSReferences(
    input.woodProductType,
    input.notchLocationType,
    input.notchFace
  );

  return {
    input,
    validation,
    stiffnessImpact,
    shearAdjustment,
    spanRegion,
    ndsReferences,
  };
}

/**
 * Validates basic input parameters for notch calculations
 *
 * @param input - Input parameters to validate
 * @returns Validation result
 *
 * @private
 */
function validateNotchInput(input: NotchValidationInput): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate wood product type
  if (
    !Object.values(NOTCH_WOOD_PRODUCT_TYPES).includes(input.woodProductType)
  ) {
    errors.push(`Invalid wood product type: ${input.woodProductType}`);
  }

  // Validate notch location type
  if (!Object.values(NOTCH_LOCATION_TYPES).includes(input.notchLocationType)) {
    errors.push(`Invalid notch location type: ${input.notchLocationType}`);
  }

  // Validate notch face
  if (!Object.values(NOTCH_FACE_TYPES).includes(input.notchFace)) {
    errors.push(`Invalid notch face: ${input.notchFace}`);
  }

  // Validate notch geometry
  if (
    !Number.isFinite(input.notchGeometry.depth) ||
    input.notchGeometry.depth <= 0
  ) {
    errors.push("Notch depth must be a positive number");
  }

  if (
    !Number.isFinite(input.notchGeometry.length) ||
    input.notchGeometry.length <= 0
  ) {
    errors.push("Notch length must be a positive number");
  }

  if (
    !Number.isFinite(input.notchGeometry.distanceFromEnd) ||
    input.notchGeometry.distanceFromEnd < 0
  ) {
    errors.push("Distance from end must be a non-negative number");
  }

  // Validate member geometry
  if (
    !Number.isFinite(input.memberGeometry.depth) ||
    input.memberGeometry.depth <= 0
  ) {
    errors.push("Member depth must be a positive number");
  }

  if (
    !Number.isFinite(input.memberGeometry.width) ||
    input.memberGeometry.width <= 0
  ) {
    errors.push("Member width must be a positive number");
  }

  if (
    !Number.isFinite(input.memberGeometry.spanLength) ||
    input.memberGeometry.spanLength <= 0
  ) {
    errors.push("Member span length must be a positive number");
  }

  // Check if notch depth exceeds member depth
  if (input.notchGeometry.depth >= input.memberGeometry.depth) {
    errors.push("Notch depth cannot equal or exceed member depth");
  }

  // Validate member thickness for sawn lumber
  if (input.woodProductType === NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER) {
    if (input.memberThickness === undefined) {
      warnings.push(
        "Member thickness not specified for sawn lumber - some validations may not apply"
      );
    } else if (
      !Number.isFinite(input.memberThickness) ||
      input.memberThickness <= 0
    ) {
      errors.push("Member thickness must be a positive number for sawn lumber");
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Gets notch limits for specific wood product, location, and face
 *
 * @param woodProductType - Wood product type
 * @param notchLocationType - Notch location type
 * @param notchFace - Notch face type
 * @returns Notch limits and restrictions
 *
 * @private
 */
function getNotchLimits(
  woodProductType: NotchWoodProductType,
  notchLocationType: NotchLocationType,
  notchFace: NotchFaceType
): {
  maxDepthRatio?: number;
  maxDepthAbsolute?: number;
  generalProhibition?: boolean;
  exceptions?: boolean;
  referenceSection?: string;
} {
  if (woodProductType === NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER) {
    if (notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH) {
      return {
        maxDepthRatio: SAWN_LUMBER_NOTCH_LIMITS.END_NOTCH.MAX_DEPTH_RATIO,
        referenceSection: NOTCH_STRENGTH_REFERENCES.SAWN_LUMBER,
      };
    } else {
      return {
        maxDepthRatio: SAWN_LUMBER_NOTCH_LIMITS.INTERIOR_NOTCH.MAX_DEPTH_RATIO,
        referenceSection: NOTCH_STRENGTH_REFERENCES.SAWN_LUMBER,
      };
    }
  }

  if (woodProductType === NOTCH_WOOD_PRODUCT_TYPES.GLULAM) {
    if (notchFace === NOTCH_FACE_TYPES.TENSION) {
      return {
        generalProhibition:
          GLULAM_NOTCH_LIMITS.TENSION_SIDE.GENERAL_PROHIBITION,
        exceptions:
          GLULAM_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION.ALLOWED &&
          notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH,
        maxDepthRatio:
          GLULAM_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION
            .MAX_DEPTH_RATIO,
        maxDepthAbsolute:
          GLULAM_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION
            .MAX_DEPTH_ABSOLUTE,
        referenceSection: NOTCH_STRENGTH_REFERENCES.GLULAM,
      };
    } else {
      return {
        generalProhibition:
          GLULAM_NOTCH_LIMITS.COMPRESSION_SIDE.GENERAL_PROHIBITION,
        exceptions:
          GLULAM_NOTCH_LIMITS.COMPRESSION_SIDE.END_NOTCH.ALLOWED &&
          notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH,
        maxDepthRatio:
          GLULAM_NOTCH_LIMITS.COMPRESSION_SIDE.END_NOTCH.MAX_DEPTH_RATIO,
        referenceSection: NOTCH_STRENGTH_REFERENCES.GLULAM,
      };
    }
  }

  if (woodProductType === NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE) {
    if (notchFace === NOTCH_FACE_TYPES.TENSION) {
      return {
        generalProhibition:
          STRUCTURAL_COMPOSITE_NOTCH_LIMITS.TENSION_SIDE.GENERAL_PROHIBITION,
        exceptions:
          STRUCTURAL_COMPOSITE_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION
            .ALLOWED && notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH,
        maxDepthRatio:
          STRUCTURAL_COMPOSITE_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION
            .MAX_DEPTH_RATIO,
        referenceSection: NOTCH_STRENGTH_REFERENCES.STRUCTURAL_COMPOSITE,
      };
    } else {
      return {
        generalProhibition:
          STRUCTURAL_COMPOSITE_NOTCH_LIMITS.COMPRESSION_SIDE
            .GENERAL_PROHIBITION,
        exceptions:
          STRUCTURAL_COMPOSITE_NOTCH_LIMITS.COMPRESSION_SIDE.END_NOTCH
            .ALLOWED && notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH,
        maxDepthRatio:
          STRUCTURAL_COMPOSITE_NOTCH_LIMITS.COMPRESSION_SIDE.END_NOTCH
            .MAX_DEPTH_RATIO,
        referenceSection: NOTCH_STRENGTH_REFERENCES.STRUCTURAL_COMPOSITE,
      };
    }
  }

  return {};
}

/**
 * Validates sawn lumber specific notch requirements
 *
 * @param input - Notch validation input
 * @param spanRegion - Span region where notch is located
 * @returns Validation errors and warnings specific to sawn lumber
 *
 * @private
 */
function validateSawnLumberNotch(
  input: NotchValidationInput,
  spanRegion: NotchSpanRegion
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check interior notch restrictions (NDS 4.4.3.2)
  if (input.notchLocationType === NOTCH_LOCATION_TYPES.INTERIOR_NOTCH) {
    // Interior notches only allowed in outer thirds of span
    if (spanRegion === NOTCH_SPAN_REGIONS.MIDDLE_THIRD) {
      errors.push(
        "Interior notches are only permitted in the outer thirds of the span (NDS 4.4.3.2)"
      );
    }

    // Check tension side restrictions for thick members
    if (input.notchFace === NOTCH_FACE_TYPES.TENSION && input.memberThickness) {
      if (
        input.memberThickness >=
        SAWN_LUMBER_NOTCH_LIMITS.INTERIOR_NOTCH.ACTUAL_THICKNESS_LIMIT
      ) {
        errors.push(
          `Interior notches on tension side not permitted for members ≥ ${SAWN_LUMBER_NOTCH_LIMITS.INTERIOR_NOTCH.ACTUAL_THICKNESS_LIMIT}" actual thickness (${SAWN_LUMBER_NOTCH_LIMITS.INTERIOR_NOTCH.NOMINAL_THICKNESS_LIMIT}" nominal) (NDS 4.4.3.2)`
        );
      }
    }
  }

  return { errors, warnings };
}

/**
 * Validates glulam specific notch requirements
 *
 * @param input - Notch validation input
 * @param spanRegion - Span region where notch is located
 * @returns Validation errors and warnings specific to glulam
 *
 * @private
 */
function validateGlulamNotch(
  input: NotchValidationInput,
  spanRegion: NotchSpanRegion
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check compression side end-notch restrictions (NDS 5.4.5.2)
  if (
    input.notchFace === NOTCH_FACE_TYPES.COMPRESSION &&
    input.notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH &&
    GLULAM_NOTCH_LIMITS.COMPRESSION_SIDE.END_NOTCH.MIDDLE_THIRD_RESTRICTION
  ) {
    if (spanRegion === NOTCH_SPAN_REGIONS.MIDDLE_THIRD) {
      errors.push(
        "Compression side end-notches shall not extend into the middle 1/3 of the span (NDS 5.4.5.2)"
      );
    }
  }

  // Check both faces restriction (NDS 5.4.5.3)
  if (GLULAM_NOTCH_LIMITS.BOTH_FACES_RESTRICTION) {
    warnings.push(
      "Ensure notches are not located on both tension and compression faces at the same cross-section (NDS 5.4.5.3)"
    );
  }

  // Recommend taper cut for compression side notches
  if (
    input.notchFace === NOTCH_FACE_TYPES.COMPRESSION &&
    !input.hasGradualTaper
  ) {
    warnings.push(
      "Consider using a gradual taper cut on compression edge per NDS 5.4.5.2 exception"
    );
  }

  return { errors, warnings };
}

/**
 * Validates structural composite specific notch requirements
 *
 * @param input - Notch validation input
 * @param spanRegion - Span region where notch is located
 * @returns Validation errors and warnings specific to structural composite
 *
 * @private
 */
function validateStructuralCompositeNotch(
  input: NotchValidationInput,
  spanRegion: NotchSpanRegion
): { errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check compression side end-notch restrictions (NDS 8.4.1.1)
  if (
    input.notchFace === NOTCH_FACE_TYPES.COMPRESSION &&
    input.notchLocationType === NOTCH_LOCATION_TYPES.END_NOTCH &&
    STRUCTURAL_COMPOSITE_NOTCH_LIMITS.COMPRESSION_SIDE.END_NOTCH
      .MIDDLE_THIRD_RESTRICTION
  ) {
    if (spanRegion === NOTCH_SPAN_REGIONS.MIDDLE_THIRD) {
      errors.push(
        "Compression side end-notches shall not extend into the middle third of the span (NDS 8.4.1.1)"
      );
    }
  }

  return { errors, warnings };
}

/**
 * Determines which span region contains the notch
 *
 * @param distanceFromEnd - Distance from member end to notch centerline
 * @param spanLength - Total span length
 * @returns Span region classification
 *
 * @private
 */
function determineSpanRegion(
  distanceFromEnd: number,
  spanLength: number
): NotchSpanRegion {
  const oneThirdSpan = spanLength / 3;

  if (distanceFromEnd <= oneThirdSpan || distanceFromEnd >= 2 * oneThirdSpan) {
    return NOTCH_SPAN_REGIONS.OUTER_THIRD;
  } else {
    return NOTCH_SPAN_REGIONS.MIDDLE_THIRD;
  }
}

/**
 * Gets applicable NDS reference sections for given notch configuration
 *
 * @param woodProductType - Wood product type
 * @param notchLocationType - Notch location type
 * @param notchFace - Notch face type
 * @returns Array of applicable NDS section references
 *
 * @private
 */
function getApplicableNDSReferences(
  woodProductType: NotchWoodProductType,
  notchLocationType: NotchLocationType,
  notchFace: NotchFaceType
): string[] {
  const references: string[] = [
    NOTCH_STRENGTH_REFERENCES.GENERAL_BENDING, // Always applicable
    NOTCH_STRENGTH_REFERENCES.SHEAR_STRENGTH, // Always applicable for shear
  ];

  // Add product-specific references
  if (woodProductType === NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER) {
    references.push(NOTCH_STRENGTH_REFERENCES.SAWN_LUMBER);
  } else if (woodProductType === NOTCH_WOOD_PRODUCT_TYPES.GLULAM) {
    references.push(NOTCH_STRENGTH_REFERENCES.GLULAM);
  } else if (
    woodProductType === NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE
  ) {
    references.push(NOTCH_STRENGTH_REFERENCES.STRUCTURAL_COMPOSITE);
  }

  return references;
}
