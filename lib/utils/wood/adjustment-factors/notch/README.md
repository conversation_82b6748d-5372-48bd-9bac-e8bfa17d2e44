# Notch Adjustment Factors and Validation

This module provides comprehensive validation and adjustment calculations for notched wood bending members according to the National Design Specification (NDS) for Wood Construction.

## Key NDS Sections Covered

- **NDS 3.2.3**: General notch provisions and stiffness considerations
- **NDS 3.4.3**: Shear strength adjustment for notched members
- **NDS 4.4.3**: Sawn lumber notch limitations
- **NDS 5.4.5**: Structural glued laminated timber (glulam) notch limitations  
- **NDS 8.4.1**: Structural composite bending member notch limitations

## Wood Product Types Supported

1. **Sawn Lumber** (NDS 4.4.3)
   - End notches: ≤ 1/4 beam depth
   - Interior notches: ≤ 1/6 beam depth, outer thirds only
   - Special restrictions for thick members (≥3.5" actual thickness)

2. **Glued Laminated Timber (Glulam)** (NDS 5.4.5)
   - Tension side: Generally prohibited except end bearing (≤1/10 depth, ≤3")
   - Compression side: End notches only (≤2/5 depth, not in middle third)
   - No notches on both faces at same cross-section

3. **Structural Composite** (NDS 8.4.1)
   - Tension side: End bearing only (≤1/10 depth)
   - Compression side: End notches only (≤2/5 depth, not in middle third)

## Main Functions

### `validateNotchConfiguration(input: NotchValidationInput): NotchValidationResult`

Validates notch configuration according to NDS requirements.

```typescript
import { validateNotchConfiguration, NOTCH_WOOD_PRODUCT_TYPES, NOTCH_LOCATION_TYPES, NOTCH_FACE_TYPES } from './notch';

const validation = validateNotchConfiguration({
  woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
  notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
  notchFace: NOTCH_FACE_TYPES.COMPRESSION,
  notchGeometry: {
    depth: 2.0,        // inches
    length: 3.0,       // inches
    distanceFromEnd: 0 // inches
  },
  memberGeometry: {
    depth: 9.25,       // inches
    width: 1.5,        // inches
    spanLength: 144    // inches
  },
  memberThickness: 2.0 // nominal thickness for sawn lumber
});

console.log(validation.isPermitted); // true/false
console.log(validation.errors);      // validation errors
console.log(validation.warnings);    // validation warnings
console.log(validation.recommendations); // design recommendations
```

### `calculateNotchShearAdjustment(input: NotchShearAdjustmentInput): ShearStrengthAdjustmentResult`

Calculates adjusted design shear value for notched members based on NDS 3.4.3 equations.

```typescript
import { calculateNotchShearAdjustment, NOTCH_CROSS_SECTION_SHAPES } from './notch';

const adjustment = calculateNotchShearAdjustment({
  crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.RECTANGULAR,
  designShearValue: 180, // psi
  memberGeometry: {
    depth: 9.25,
    width: 1.5,
    spanLength: 144
  },
  notchGeometry: {
    depth: 1.5,
    length: 3.0,
    distanceFromEnd: 6.0
  }
});

console.log(adjustment.originalDesignShearValue);  // 180 psi
console.log(adjustment.adjustedDesignShearValue);  // reduced value
console.log(adjustment.remainingDepth);           // depth at notch
```

### `analyzeStiffnessImpact(notchGeometry: NotchGeometry, memberDepth: number): StiffnessImpactAnalysis`

Analyzes the impact of notch on member stiffness based on NDS ******* thresholds.

```typescript
import { analyzeStiffnessImpact } from './notch';

const stiffnessImpact = analyzeStiffnessImpact(
  {
    depth: 1.0,
    length: 2.0,
    distanceFromEnd: 6.0
  },
  9.25 // member depth
);

console.log(stiffnessImpact.isStiffnessImpactNegligible); // true/false
console.log(stiffnessImpact.stiffnessImpact);            // 'negligible' | 'moderate' | 'significant'
```

### `performNotchAnalysis(input: NotchValidationInput, shearInput?: ShearInput): NotchAnalysisResult`

Performs comprehensive analysis including validation, stiffness impact, and optional shear adjustment.

```typescript
import { performNotchAnalysis } from './notch';

const analysis = performNotchAnalysis(
  {
    woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
    notchLocationType: NOTCH_LOCATION_TYPES.INTERIOR_NOTCH,
    notchFace: NOTCH_FACE_TYPES.TENSION,
    notchGeometry: { depth: 1.0, length: 2.0, distanceFromEnd: 48.0 },
    memberGeometry: { depth: 9.25, width: 1.5, spanLength: 144 }
  },
  {
    crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.RECTANGULAR,
    designShearValue: 180
  }
);

console.log(analysis.validation);      // validation results
console.log(analysis.stiffnessImpact); // stiffness analysis
console.log(analysis.shearAdjustment);  // shear adjustment (if provided)
console.log(analysis.spanRegion);      // 'outer_third' | 'middle_third'
console.log(analysis.ndsReferences);   // applicable NDS sections
```

## Key Design Considerations

### Stress Concentrations
- Square notches create high stress concentrations
- Gradual taper cuts are recommended to reduce stress concentrations
- The module provides recommendations for improved notch design

### Stiffness Impact
Per NDS *******, stiffness impact is negligible when:
- Notch depth ≤ 1/6 beam depth
- Notch length ≤ 1/3 beam depth

### Shear Strength Reduction
Notched members have reduced shear capacity calculated using:
- Basic coefficient: 2/3
- Different equations for rectangular, circular, and other cross-sections
- Special considerations for connections near notches

### Span Location Restrictions
- **Interior notches**: Only permitted in outer thirds of span
- **End notches**: Located at member ends for bearing support
- **Middle third**: Generally prohibited for interior notches

## Examples by Wood Product Type

### Sawn Lumber Example
```typescript
// Valid 2x10 end notch for bearing
const sawLumberValidation = validateNotchConfiguration({
  woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
  notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
  notchFace: NOTCH_FACE_TYPES.COMPRESSION,
  notchGeometry: { depth: 2.3, length: 3.5, distanceFromEnd: 0 }, // Just under 1/4 depth limit
  memberGeometry: { depth: 9.25, width: 1.5, spanLength: 192 },
  memberThickness: 2.0
});
```

### Glulam Example
```typescript
// Valid glulam tension side end notch for bearing
const glulamValidation = validateNotchConfiguration({
  woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
  notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
  notchFace: NOTCH_FACE_TYPES.TENSION,
  notchGeometry: { depth: 0.9, length: 4.0, distanceFromEnd: 0 }, // Under 1/10 depth and 3" limits
  memberGeometry: { depth: 12.0, width: 3.125, spanLength: 240 }
});
```

### Structural Composite Example
```typescript
// Valid structural composite compression side end notch
const compositeValidation = validateNotchConfiguration({
  woodProductType: NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE,
  notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
  notchFace: NOTCH_FACE_TYPES.COMPRESSION,
  notchGeometry: { depth: 4.0, length: 3.5, distanceFromEnd: 0 }, // Under 2/5 depth limit
  memberGeometry: { depth: 11.875, width: 2.5, spanLength: 288 }
});
```

## Connection Geometry Considerations

For members with connections (bolts, lag screws, etc.), the effective depth calculation accounts for:
- Distance from unloaded edge to connection
- Connection type (affects stress distribution)
- Distance from member end (< 5d or ≥ 5d from end)

```typescript
const connectionGeometry = {
  distanceToConnection: 6.0,    // inches from unloaded edge
  connectionType: 'bolt' as const
};

const adjustmentWithConnection = calculateNotchShearAdjustment({
  crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.RECTANGULAR,
  designShearValue: 180,
  memberGeometry: { depth: 9.25, width: 1.5, spanLength: 144 },
  notchGeometry: { depth: 1.5, length: 3.0, distanceFromEnd: 20.0 },
  connectionGeometry
});
```

## Error Handling and Validation

The module provides comprehensive validation with:
- **Errors**: Violations of NDS requirements that make notch impermissible
- **Warnings**: Potential issues or missing information
- **Recommendations**: Design improvements for better performance

Common validation errors:
- Notch depth exceeding maximum ratios
- Interior notches in middle third of span
- Tension side notches on thick sawn lumber members
- Notches on both faces at same cross-section (glulam)

## TypeScript Types

All functions use strongly typed interfaces:
- `NotchValidationInput`: Complete input parameters
- `NotchValidationResult`: Validation outcome with errors/warnings
- `NotchAnalysisResult`: Comprehensive analysis results
- `StiffnessImpactAnalysis`: Stiffness impact evaluation
- `ShearStrengthAdjustmentResult`: Shear adjustment calculations

## References

- **NDS 2018**: National Design Specification for Wood Construction
- **NDS 3.2.3**: Bending Members - General (Notches)
- **NDS 3.4.3**: Shear - Notched Members
- **NDS 4.4.3**: Sawn Lumber - Notches
- **NDS 5.4.5**: Structural Glued Laminated Timber - Notches
- **NDS 8.4.1**: Structural Composite Lumber - Notches 