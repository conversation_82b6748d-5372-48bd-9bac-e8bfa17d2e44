/**
 * Unit tests for Notch Adjustment Factors and Validation
 * Tests all functions and edge cases for notch configurations
 */

import {
  validateNotchConfiguration,
  calculateNotchShearAdjustment,
  analyzeStiffnessImpact,
  performNotchAnalysis,
  type NotchValidationInput,
  type NotchShearAdjustmentInput,
  type NotchGeometry,
  type MemberGeometry,
  type ConnectionGeometry,
} from "./notch";

import {
  NOTCH_WOOD_PRODUCT_TYPES,
  NOTCH_LOCATION_TYPES,
  NOTCH_FACE_TYPES,
  NOTCH_CROSS_SECTION_SHAPES,
  SAWN_LUMBER_NOTCH_LIMITS,
  GLULAM_NOTCH_LIMITS,
  STRUCTURAL_COMPOSITE_NOTCH_LIMITS,
  NOTCH_STIFFNESS_THRESHOLDS,
  NOTCH_SHEAR_COEFFICIENTS,
} from "../../constants";

describe("Notch Validation", () => {
  // Common test data
  const basicMemberGeometry: MemberGeometry = {
    depth: 9.25,
    width: 1.5,
    spanLength: 144,
  };

  const basicNotchGeometry: NotchGeometry = {
    depth: 1.5,
    length: 3.0,
    distanceFromEnd: 0,
  };

  describe("validateNotchConfiguration", () => {
    describe("Input Validation", () => {
      it("should reject invalid wood product type", () => {
        const input: NotchValidationInput = {
          woodProductType: "invalid_type" as any,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: basicNotchGeometry,
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain(
          "Invalid wood product type: invalid_type"
        );
      });

      it("should reject invalid notch location type", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: "invalid_location" as any,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: basicNotchGeometry,
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain(
          "Invalid notch location type: invalid_location"
        );
      });

      it("should reject invalid notch face", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: "invalid_face" as any,
          notchGeometry: basicNotchGeometry,
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain("Invalid notch face: invalid_face");
      });

      it("should reject negative notch dimensions", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: -1,
            length: -1,
            distanceFromEnd: -1,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain(
          "Notch depth must be a positive number"
        );
        expect(result.errors).toContain(
          "Notch length must be a positive number"
        );
        expect(result.errors).toContain(
          "Distance from end must be a non-negative number"
        );
      });

      it("should reject notch depth exceeding member depth", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            ...basicNotchGeometry,
            depth: 10.0, // Exceeds member depth
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain(
          "Notch depth cannot equal or exceed member depth"
        );
      });
    });

    describe("Sawn Lumber Notches", () => {
      it("should permit valid end notches", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 2.0, // Within 1/4 of depth (9.25/4 = 2.31)
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
          memberThickness: 2.0,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.maxPermittedDepthRatio).toBe(
          SAWN_LUMBER_NOTCH_LIMITS.END_NOTCH.MAX_DEPTH_RATIO
        );
      });

      it("should reject end notches exceeding depth limit", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 3.0, // Exceeds 1/4 of depth (9.25/4 = 2.31)
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
          memberThickness: 2.0,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(
          result.errors.some((error) =>
            error.includes("exceeds maximum permitted ratio")
          )
        ).toBe(true);
      });

      it("should permit valid interior notches in outer thirds", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.INTERIOR_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 1.5, // Within 1/6 of depth (9.25/6 = 1.54)
            length: 3.0,
            distanceFromEnd: 24, // In outer third of 144" span
          },
          memberGeometry: basicMemberGeometry,
          memberThickness: 2.0,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it("should reject interior notches in middle third of span", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.INTERIOR_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 1.5,
            length: 3.0,
            distanceFromEnd: 72, // In middle third of 144" span
          },
          memberGeometry: basicMemberGeometry,
          memberThickness: 2.0,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain(
          "Interior notches are only permitted in the outer thirds of the span (NDS 4.4.3.2)"
        );
      });

      it("should reject tension side interior notches on thick members", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.INTERIOR_NOTCH,
          notchFace: NOTCH_FACE_TYPES.TENSION,
          notchGeometry: {
            depth: 1.5,
            length: 3.0,
            distanceFromEnd: 24,
          },
          memberGeometry: basicMemberGeometry,
          memberThickness: 4.0, // ≥ 3.5" actual thickness
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(
          result.errors.some((error) =>
            error.includes("Interior notches on tension side not permitted")
          )
        ).toBe(true);
      });
    });

    describe("Glulam Notches", () => {
      it("should permit end notches on tension side for bearing", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.TENSION,
          notchGeometry: {
            depth: 0.9, // Within 1/10 of depth (9.25/10 = 0.925) and ≤ 3"
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it("should reject tension side notches exceeding depth and absolute limits", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.TENSION,
          notchGeometry: {
            depth: 3.5, // Exceeds both 1/10 depth and 3" absolute
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(
          result.errors.some((error) =>
            error.includes("exceeds maximum permitted ratio")
          )
        ).toBe(true);
        expect(
          result.errors.some((error) =>
            error.includes("exceeds maximum permitted depth")
          )
        ).toBe(true);
      });

      it("should permit compression side end notches", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 3.5, // Within 2/5 of depth (9.25 * 2/5 = 3.7)
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it("should reject compression side end notches extending into middle third", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 3.0,
            length: 60.0, // Long notch extending into middle third
            distanceFromEnd: 60, // Notch centerline in middle third
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(false);
        expect(result.errors).toContain(
          "Compression side end-notches shall not extend into the middle 1/3 of the span (NDS 5.4.5.2)"
        );
      });
    });

    describe("Structural Composite Notches", () => {
      it("should permit tension side end notches for bearing", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.TENSION,
          notchGeometry: {
            depth: 0.9, // Within 1/10 of depth
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it("should permit compression side end notches", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 3.5, // Within 2/5 of depth
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.isPermitted).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    describe("Design Recommendations", () => {
      it("should recommend gradual taper for square notches", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: basicNotchGeometry,
          memberGeometry: basicMemberGeometry,
          hasGradualTaper: false,
        };

        const result = validateNotchConfiguration(input);

        expect(result.recommendations).toContain(
          "Consider using a gradual taper cut instead of a square notch to reduce stress concentrations (NDS 3.2.3.1)"
        );
      });

      it("should recommend structural analysis for deep notches", () => {
        const input: NotchValidationInput = {
          woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
          notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
          notchFace: NOTCH_FACE_TYPES.COMPRESSION,
          notchGeometry: {
            depth: 2.0, // > 1/6 threshold (9.25/6 = 1.54)
            length: 3.0,
            distanceFromEnd: 0,
          },
          memberGeometry: basicMemberGeometry,
        };

        const result = validateNotchConfiguration(input);

        expect(result.recommendations).toContain(
          "Notch depth may have significant impact on member stiffness - consider structural analysis"
        );
      });
    });
  });

  describe("calculateNotchShearAdjustment", () => {
    const baseShearInput: NotchShearAdjustmentInput = {
      crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.RECTANGULAR,
      designShearValue: 180,
      memberGeometry: basicMemberGeometry,
      notchGeometry: basicNotchGeometry,
    };

    it("should calculate basic rectangular notched member shear adjustment", () => {
      const result = calculateNotchShearAdjustment(baseShearInput);

      const expectedRemainingDepth =
        basicMemberGeometry.depth - basicNotchGeometry.depth;
      const expectedDepthRatio =
        expectedRemainingDepth / basicMemberGeometry.depth;
      const expectedAdjustedShear =
        NOTCH_SHEAR_COEFFICIENTS.BASIC_COEFFICIENT *
        180 *
        Math.pow(expectedDepthRatio, 2);

      expect(result.originalDesignShearValue).toBe(180);
      expect(result.remainingDepth).toBe(expectedRemainingDepth);
      expect(result.adjustedDesignShearValue).toBeCloseTo(
        expectedAdjustedShear,
        2
      );
      expect(result.shearAdjustmentCoefficient).toBe(
        NOTCH_SHEAR_COEFFICIENTS.BASIC_COEFFICIENT
      );
    });

    it("should calculate circular cross-section adjustment", () => {
      const circularInput: NotchShearAdjustmentInput = {
        ...baseShearInput,
        crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.CIRCULAR,
        memberGeometry: {
          ...basicMemberGeometry,
          crossSectionalArea: 67.2, // Area of 9.25" diameter circle
        },
      };

      const result = calculateNotchShearAdjustment(circularInput);

      expect(result.adjustedDesignShearValue).toBeLessThan(
        result.originalDesignShearValue
      );
      expect(result.shearAdjustmentCoefficient).toBe(
        NOTCH_SHEAR_COEFFICIENTS.BASIC_COEFFICIENT
      );
    });

    it("should handle connection geometry for close connections", () => {
      const connectionGeometry: ConnectionGeometry = {
        distanceToConnection: 6.0,
        connectionType: "bolt",
      };

      const connectionInput: NotchShearAdjustmentInput = {
        ...baseShearInput,
        notchGeometry: {
          ...basicNotchGeometry,
          distanceFromEnd: 20.0, // Less than 5 * depth (46.25)
        },
        connectionGeometry,
      };

      const result = calculateNotchShearAdjustment(connectionInput);

      expect(result.effectiveDepth).toBe(
        Math.min(
          basicMemberGeometry.depth - basicNotchGeometry.depth,
          connectionGeometry.distanceToConnection
        )
      );
    });

    it("should handle connection geometry for distant connections", () => {
      const connectionGeometry: ConnectionGeometry = {
        distanceToConnection: 6.0,
        connectionType: "bolt",
      };

      const connectionInput: NotchShearAdjustmentInput = {
        ...baseShearInput,
        notchGeometry: {
          ...basicNotchGeometry,
          distanceFromEnd: 60.0, // More than 5 * depth (46.25)
        },
        connectionGeometry,
      };

      const result = calculateNotchShearAdjustment(connectionInput);

      expect(result.effectiveDepth).toBe(
        connectionGeometry.distanceToConnection
      );
    });

    it("should handle other cross-section shapes", () => {
      const otherInput: NotchShearAdjustmentInput = {
        ...baseShearInput,
        crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.OTHER,
      };

      const result = calculateNotchShearAdjustment(otherInput);

      expect(result.adjustedDesignShearValue).toBeLessThan(
        result.originalDesignShearValue
      );
      expect(result.shearAdjustmentCoefficient).toBe(
        NOTCH_SHEAR_COEFFICIENTS.BASIC_COEFFICIENT
      );
    });
  });

  describe("analyzeStiffnessImpact", () => {
    it("should identify negligible stiffness impact", () => {
      const smallNotch: NotchGeometry = {
        depth: 1.0, // 1.0/9.25 = 0.108 < 1/6
        length: 2.0, // 2.0/9.25 = 0.216 < 1/3
        distanceFromEnd: 24,
      };

      const result = analyzeStiffnessImpact(
        smallNotch,
        basicMemberGeometry.depth
      );

      expect(result.isStiffnessImpactNegligible).toBe(true);
      expect(result.stiffnessImpact).toBe("negligible");
      expect(result.depthRatio).toBeCloseTo(1.0 / 9.25, 3);
      expect(result.lengthRatio).toBeCloseTo(2.0 / 9.25, 3);
    });

    it("should identify moderate stiffness impact", () => {
      const moderateNotch: NotchGeometry = {
        depth: 2.0, // 2.0/9.25 = 0.216 > 1/6 but ≤ 1.5 * 1/6
        length: 2.0,
        distanceFromEnd: 24,
      };

      const result = analyzeStiffnessImpact(
        moderateNotch,
        basicMemberGeometry.depth
      );

      expect(result.isStiffnessImpactNegligible).toBe(false);
      expect(result.stiffnessImpact).toBe("moderate");
    });

    it("should identify significant stiffness impact", () => {
      const largeNotch: NotchGeometry = {
        depth: 3.0, // 3.0/9.25 = 0.324 > 1.5 * 1/6
        length: 4.0,
        distanceFromEnd: 24,
      };

      const result = analyzeStiffnessImpact(
        largeNotch,
        basicMemberGeometry.depth
      );

      expect(result.isStiffnessImpactNegligible).toBe(false);
      expect(result.stiffnessImpact).toBe("significant");
    });
  });

  describe("performNotchAnalysis", () => {
    it("should perform comprehensive analysis without shear input", () => {
      const input: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: basicNotchGeometry,
        memberGeometry: basicMemberGeometry,
        memberThickness: 2.0,
      };

      const result = performNotchAnalysis(input);

      expect(result.input).toBe(input);
      expect(result.validation).toBeDefined();
      expect(result.stiffnessImpact).toBeDefined();
      expect(result.shearAdjustment).toBeUndefined();
      expect(result.spanRegion).toBeDefined();
      expect(result.ndsReferences).toContain("3.1.2");
      expect(result.ndsReferences).toContain("3.4.3");
      expect(result.ndsReferences).toContain("4.4.3");
    });

    it("should perform comprehensive analysis with shear input", () => {
      const input: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: basicNotchGeometry,
        memberGeometry: basicMemberGeometry,
        memberThickness: 2.0,
      };

      const shearInput = {
        crossSectionShape: NOTCH_CROSS_SECTION_SHAPES.RECTANGULAR,
        designShearValue: 180,
      };

      const result = performNotchAnalysis(input, shearInput);

      expect(result.shearAdjustment).toBeDefined();
      expect(result.shearAdjustment?.originalDesignShearValue).toBe(180);
    });

    it("should classify span regions correctly", () => {
      // Test outer third (near start)
      const nearStartInput: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.INTERIOR_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: {
          ...basicNotchGeometry,
          distanceFromEnd: 24, // 144/3 = 48, so 24 is in outer third
        },
        memberGeometry: basicMemberGeometry,
      };

      const nearStartResult = performNotchAnalysis(nearStartInput);
      expect(nearStartResult.spanRegion).toBe("outer_third");

      // Test middle third
      const middleInput: NotchValidationInput = {
        ...nearStartInput,
        notchGeometry: {
          ...basicNotchGeometry,
          distanceFromEnd: 72, // Middle of 144" span
        },
      };

      const middleResult = performNotchAnalysis(middleInput);
      expect(middleResult.spanRegion).toBe("middle_third");

      // Test outer third (near end)
      const nearEndInput: NotchValidationInput = {
        ...nearStartInput,
        notchGeometry: {
          ...basicNotchGeometry,
          distanceFromEnd: 120, // 144 - 24 = 120, in outer third
        },
      };

      const nearEndResult = performNotchAnalysis(nearEndInput);
      expect(nearEndResult.spanRegion).toBe("outer_third");
    });

    it("should include correct NDS references for different wood products", () => {
      const glulamInput: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: basicNotchGeometry,
        memberGeometry: basicMemberGeometry,
      };

      const glulamResult = performNotchAnalysis(glulamInput);

      expect(glulamResult.ndsReferences).toContain("3.1.2");
      expect(glulamResult.ndsReferences).toContain("3.4.3");
      expect(glulamResult.ndsReferences).toContain("5.4.5");

      const compositeInput: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.STRUCTURAL_COMPOSITE,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: basicNotchGeometry,
        memberGeometry: basicMemberGeometry,
      };

      const compositeResult = performNotchAnalysis(compositeInput);

      expect(compositeResult.ndsReferences).toContain("3.1.2");
      expect(compositeResult.ndsReferences).toContain("3.4.3");
      expect(compositeResult.ndsReferences).toContain("8.4.1");
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle zero distance from end", () => {
      const input: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: {
          depth: 1.0,
          length: 2.0,
          distanceFromEnd: 0,
        },
        memberGeometry: basicMemberGeometry,
      };

      const result = validateNotchConfiguration(input);
      expect(result.isPermitted).toBe(true);
    });

    it("should handle maximum permitted notch depths exactly", () => {
      // Test sawn lumber end notch at exact limit
      const sawLumberInput: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: {
          depth:
            basicMemberGeometry.depth *
            SAWN_LUMBER_NOTCH_LIMITS.END_NOTCH.MAX_DEPTH_RATIO,
          length: 3.0,
          distanceFromEnd: 0,
        },
        memberGeometry: basicMemberGeometry,
      };

      const sawLumberResult = validateNotchConfiguration(sawLumberInput);
      expect(sawLumberResult.isPermitted).toBe(true);

      // Test glulam tension side at exact limits
      const glulamInput: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.GLULAM,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.TENSION,
        notchGeometry: {
          depth: Math.min(
            basicMemberGeometry.depth *
              GLULAM_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION
                .MAX_DEPTH_RATIO,
            GLULAM_NOTCH_LIMITS.TENSION_SIDE.END_BEARING_EXCEPTION
              .MAX_DEPTH_ABSOLUTE
          ),
          length: 3.0,
          distanceFromEnd: 0,
        },
        memberGeometry: basicMemberGeometry,
      };

      const glulamResult = validateNotchConfiguration(glulamInput);
      expect(glulamResult.isPermitted).toBe(true);
    });

    it("should handle very small members", () => {
      const smallMember: MemberGeometry = {
        depth: 3.5,
        width: 1.5,
        spanLength: 48,
      };

      const input: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: {
          depth: 0.5,
          length: 1.0,
          distanceFromEnd: 0,
        },
        memberGeometry: smallMember,
      };

      const result = validateNotchConfiguration(input);
      expect(result.isPermitted).toBe(true);
    });

    it("should handle missing member thickness for sawn lumber", () => {
      const input: NotchValidationInput = {
        woodProductType: NOTCH_WOOD_PRODUCT_TYPES.SAWN_LUMBER,
        notchLocationType: NOTCH_LOCATION_TYPES.END_NOTCH,
        notchFace: NOTCH_FACE_TYPES.COMPRESSION,
        notchGeometry: basicNotchGeometry,
        memberGeometry: basicMemberGeometry,
        // memberThickness not provided
      };

      const result = validateNotchConfiguration(input);
      expect(result.warnings).toContain(
        "Member thickness not specified for sawn lumber - some validations may not apply"
      );
    });
  });
});
