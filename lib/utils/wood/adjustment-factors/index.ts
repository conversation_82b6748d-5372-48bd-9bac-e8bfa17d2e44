/**
 * Wood Adjustment Factors
 * Exports all adjustment factor functions for wood design
 */

// Load Duration Factors
export {
  getLoadDurationFactorCD,
  getLoadDurationFactorByLoadType,
  getLoadDurationCategoryByLoadType,
  isDesignValueExemptFromLoadDuration,
  validateLoadDurationFactor,
  applyLoadDurationFactor,
  applyLoadDurationFactorByLoadType,
  getMultipleLoadDurationFactors,
  getControllingLoadDurationFactor,
  getLoadDurationAnalysis,
} from './load-duration/load-duration';

// Incising Factors
export {
  getIncisingFactorCi,
  validateIncisingSpecifications,
  applyIncisingFactor,
  getMultipleIncisingFactors,
  getIncisingFactorByName,
} from './incising/incising';

// Temperature Factors
export {
  getTemperatureFactor,
  applyTemperatureFactor,
  validateTemperatureFactorInput,
  getMultipleTemperatureFactors,
  getTemperatureFactorAnalysis,
  isDesignValueAffectedByTemperature,
  getEffectiveMoistureCondition,
  type TemperatureFactorInput,
  type TemperatureFactorValidation,
  type TemperatureFactorAnalysis,
} from './temperature/temperature';

// Wet Service Factors
export {
  getWetServiceFactor,
  applyWetServiceFactor,
  validateWetServiceFactorInput,
  getMultipleWetServiceFactors,
  getWetServiceFactorAnalysis,
  isWetServiceRequired,
  getWetServiceMoistureThreshold,
  checkWetServiceSpecialConditions,
  isDesignValueAffectedByWetService,
  type WetServiceFactorInput,
  type WetServiceFactorValidation,
  type WetServiceFactorAnalysis,
  type WetServiceLumberCategory,
  type WetServiceSpeciesGroup,
  WET_SERVICE_LUMBER_CATEGORIES,
  WET_SERVICE_SPECIES_GROUPS,
  WET_SERVICE_MOISTURE_THRESHOLDS,
  SAWN_LUMBER_WET_SERVICE_FACTORS,
  GLULAM_WET_SERVICE_FACTORS,
  WET_SERVICE_SPECIAL_CONDITIONS,
} from './wet-service/wet-service';

// Repetitive Member Factor (Cr) exports
export {
  getRepetitiveMemberFactor,
  validateRepetitiveMemberFactorInput,
  getRepetitiveMemberFactorAnalysis,
  applyRepetitiveMemberFactor,
  isRepetitiveMemberFactorApplicable,
  getMultipleRepetitiveMemberFactors,
  isMemberTypeQualified,
  getQualifiedMemberTypes,
  getQualifiedLoadDistributingElements,
  type RepetitiveMemberFactorInput,
  type RepetitiveMemberFactorValidation,
  type RepetitiveMemberFactorAnalysis,
} from './repetitive-member/repetitive-member';

// Repetitive Member Factor Constants exports
export {
  REPETITIVE_MEMBER_FACTOR_CONSTANTS,
  REPETITIVE_MEMBER_TYPES,
  type RepetitiveMemberType,
  LOAD_DISTRIBUTING_ELEMENTS,
  type LoadDistributingElement,
  REPETITIVE_MEMBER_LUMBER_CATEGORIES,
  type RepetitiveMemberLumberCategory,
} from '../constants';

// Beam Stability Factors
export {
  getBeamStabilityFactorCL,
  getEffectiveLengthFactor,
  getBeamStabilityFactorCLDetailed,
} from './stability/stability';

// LRFD Factors
export {
  getLrfdFormatConversionFactorKF,
  getLrfdResistanceFactorPhi,
  getLrfdTimeEffectFactorLambda,
  calculateAdjustedLrfdDesignValue,
  getLrfdFactorsAnalysis,
  isDesignValueSupportedForLrfd,
  getAvailableTimeEffectFactors,
} from './lrfd/lrfd';

// Size Factor (CF) and Flat Use Factor (Cflu) exports
export {
  SIZE_FACTOR_LUMBER_GRADES,
  SIZE_FACTOR_DIMENSIONS,
  SIZE_FACTORS,
  FLAT_USE_FACTORS,
  SIZE_FACTOR_CONSTANTS,
  type SizeFactorInput,
  type SizeFactorValidation,
  type SizeFactorAnalysis,
  type SizeFactorLumberGrade,
  type SizeFactorDimension,
  validateSizeFactorInput,
  getDimensionCategory,
  getSizeFactor,
  getFlatUseFactor,
  applySizeFactor,
  getSizeFactorAnalysis,
  isDesignValueAffectedBySizeFactor,
} from './size-factor/size-factor';

// Flat Use Factor (Cfu) exports
export {
  FLAT_USE_LUMBER_GRADES,
  FLAT_USE_DIMENSIONS,
  FLAT_USE_FACTORS as FLAT_USE_FACTOR_VALUES,
  FLAT_USE_CONSTANTS,
  type FlatUseFactorInput,
  type FlatUseFactorValidation,
  type FlatUseFactorAnalysis,
  type FlatUseLumberGrade,
  type FlatUseDimension,
  validateFlatUseFactorInput,
  getFlatUseDimensionCategory,
  getFlatUseFactor as getFlatUseFactorCfu,
  applyFlatUseFactor,
  getFlatUseFactorAnalysis,
  getMultipleFlatUseFactors,
  isDesignValueAffectedByFlatUse,
  getOrientationOptimization,
} from './flat-use-factor/flat-use-factor';

// Volume Factor (CV) exports
export {
  VOLUME_FACTOR_PRODUCTS,
  VOLUME_FACTOR_PARAMETERS,
  VOLUME_FACTOR_CONSTANTS,
  type VolumeFactorInput,
  type VolumeFactorValidation,
  type VolumeFactorAnalysis,
  type VolumeFactorCalculation,
  type VolumeFactorProduct,
  validateVolumeFactorInput,
  calculateVolumeFactorDetails,
  getVolumeFactor,
  applyVolumeFactor,
  getVolumeFactorAnalysis,
  getMultipleVolumeFactors,
  isDesignValueAffectedByVolume,
  getVolumeFactorOptimization,
} from './volume-factor/volume-factor';

// Notch Validation and Adjustment exports
export {
  validateNotchConfiguration,
  calculateNotchShearAdjustment,
  analyzeStiffnessImpact,
  performNotchAnalysis,
  type NotchValidationInput,
  type NotchShearAdjustmentInput,
  type NotchValidationResult,
  type NotchAnalysisResult,
  type StiffnessImpactAnalysis,
  type ShearStrengthAdjustmentResult,
  type NotchGeometry,
  type MemberGeometry,
  type ConnectionGeometry,
} from './notch/notch';

// Notch Constants exports
export {
  NOTCH_WOOD_PRODUCT_TYPES,
  type NotchWoodProductType,
  NOTCH_LOCATION_TYPES,
  type NotchLocationType,
  NOTCH_FACE_TYPES,
  type NotchFaceType,
  NOTCH_SPAN_REGIONS,
  type NotchSpanRegion,
  NOTCH_CROSS_SECTION_SHAPES,
  type NotchCrossSectionShape,
  SAWN_LUMBER_NOTCH_LIMITS,
  GLULAM_NOTCH_LIMITS,
  STRUCTURAL_COMPOSITE_NOTCH_LIMITS,
  NOTCH_STIFFNESS_THRESHOLDS,
  NOTCH_SHEAR_COEFFICIENTS,
  NOTCH_STRESS_CONCENTRATION,
  NOTCH_STRENGTH_REFERENCES,
  GENERAL_NOTCH_PROHIBITIONS,
} from '../constants';

// Bearing Area Factor (Cb) exports
export {
  getBearingAreaFactor,
  applyBearingAreaFactor,
  calculateBearingAtAngle,
  getBearingAreaFactorAnalysis,
  getBearingAtAngleAnalysis,
  validateBearingAreaFactorInput,
  validateBearingAtAngleInput,
  getEffectiveBearingLength,
  calculateBearingAreaFactorFormula,
  getBearingAreaFactorFromTable,
  getMultipleBearingAreaFactors,
  isDesignValueAffectedByBearingAreaFactor,
  getBearingAreaFactorOptimization,
  type BearingAreaFactorInput,
  type BearingAtAngleInput,
  type BearingAreaFactorValidation,
  type BearingAreaFactorAnalysis,
  type BearingAtAngleAnalysis,
  type BearingConfigurationType,
} from './bearing-area-factor/bearing-area-factor';

// Bearing Area Factor Constants exports
export {
  BEARING_AREA_FACTORS_TABLE,
  BEARING_AREA_FACTOR_CONSTANTS,
  BEARING_CONFIGURATION_TYPES,
} from './bearing-area-factor/bearing-area-factor';

// Curvature Factor (Cc) exports - Glulam NDS 5.3.8
export {
  getCurvatureFactor,
  getCurvatureFactorForStraightMember,
  applyCurvatureFactor,
  validateCurvatureFactorInput,
  calculateCurvatureFactorDetails,
  getCurvatureFactorAnalysis,
  getMultipleCurvatureFactors,
  isDesignValueAffectedByCurvature,
  getCurvatureFactorOptimization,
  CURVATURE_SPECIES_GROUPS,
  CURVATURE_MEMBER_TYPES,
  CURVATURE_FACTOR_CONSTANTS,
  type CurvatureFactorInput,
  type CurvatureFactorValidation,
  type CurvatureFactorCalculation,
  type CurvatureFactorAnalysis,
  type CurvatureSpeciesGroup,
  type CurvatureMemberType,
} from './curvature-factor';

// Stress Interaction Factor (CI) exports - Glulam NDS 5.3.9
export {
  getStressInteractionFactor,
  getStressInteractionFactorForStraightMember,
  applyStressInteractionFactor,
  validateStressInteractionFactorInput,
  calculateStressInteractionFactorDetails,
  getStressInteractionFactorAnalysis,
  getMultipleStressInteractionFactors,
  isDesignValueAffectedByStressInteraction,
  getStressInteractionFactorOptimization,
  classifyTaperSeverity,
  determineMemberType,
  STRESS_INTERACTION_MEMBER_TYPES,
  TAPER_SEVERITY_LEVELS,
  STRESS_INTERACTION_FACTOR_CONSTANTS,
  type StressInteractionFactorInput,
  type StressInteractionFactorValidation,
  type StressInteractionFactorCalculation,
  type StressInteractionFactorAnalysis,
  type StressInteractionMemberType,
  type TaperSeverityLevel,
} from './stress-interaction-factor';

// Shear Reduction Factor (Cvr) exports - Glulam NDS 5.3.10
export {
  getShearReductionFactor,
  getShearReductionFactorForStandardConditions,
  applyShearReductionFactor,
  validateShearReductionFactorInput,
  calculateShearReductionFactorDetails,
  getShearReductionFactorAnalysis,
  getMultipleShearReductionFactors,
  isDesignValueAffectedByShearReduction,
  getShearReductionFactorOptimization,
  getStandardShearReductionFactorScenarios,
  determineActiveConditions,
  assessReductionSeverity,
  generateReductionReasons,
  SHEAR_REDUCTION_CONDITIONS,
  SHEAR_REDUCTION_SEVERITY,
  SHEAR_REDUCTION_FACTOR_CONSTANTS,
  type ShearReductionFactorInput,
  type ShearReductionFactorValidation,
  type ShearReductionFactorCalculation,
  type ShearReductionFactorAnalysis,
  type ShearReductionCondition,
  type ShearReductionSeverity,
} from './shear-reduction-factor';

 