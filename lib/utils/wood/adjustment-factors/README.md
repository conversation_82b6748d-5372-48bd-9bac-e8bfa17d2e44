# Wood Adjustment Factors

## Overview

This directory contains all wood design adjustment factor calculations according to the National Design Specification (NDS) for Wood Construction. Each adjustment factor is organized into its own module folder with comprehensive implementations, tests, and documentation.

## Module Organization

Each adjustment factor module is organized in its own folder containing:
- **Main implementation** (`module-name.ts`)
- **Comprehensive test suite** (`module-name.test.ts`)
- **Usage examples** (`module-name-examples.ts`) (where applicable)
- **Module documentation** (`README.md`)

## Available Modules

### Load Duration Factor (CD)
**Location**: `./load-duration/`
- Adjusts design values based on load duration in ASD
- Handles permanent, live, snow, wind, earthquake, and impact loads
- **Key Functions**: `getLoadDurationFactorCD()`, `getControllingLoadDurationFactor()`

### Incising Factor (Ci)
**Location**: `./incising/`
- Adjusts design values for incised pressure-treated lumber
- Accounts for preservative penetration enhancement
- **Key Functions**: `getIncisingFactorCi()`, `validateIncisingSpecifications()`

### Temperature Factor (Ct)
**Location**: `./temperature/`
- Adjusts design values for elevated temperature exposure
- Handles different moisture conditions and temperature ranges
- **Key Functions**: `getTemperatureFactor()`, `getTemperatureFactorAnalysis()`

### Wet Service Factor (CM)
**Location**: `./wet-service/`
- Adjusts design values for moisture content ≥ 19%
- Covers sawn lumber, glulam, and different species groups
- **Key Functions**: `getWetServiceFactor()`, `isWetServiceRequired()`

### Repetitive Member Factor (Cr)
**Location**: `./repetitive-member/`
- Applies to multiple members acting together with load distribution
- For dimension lumber spaced ≤ 24" o.c. with ≥ 3 members
- **Key Functions**: `getRepetitiveMemberFactor()`, `getRepetitiveMemberFactorAnalysis()`

### Size Factor (CF)
**Location**: `./size-factor/`
- Adjusts bending design values based on member size
- Covers dimension lumber, beams & stringers, posts & timbers
- **Key Functions**: `getSizeFactor()`, `getDimensionCategory()`

### Flat Use Factor (Cfu)
**Location**: `./flat-use-factor/`
- Adjusts bending values when lumber is loaded on wide face
- For dimension lumber in weak-axis bending
- **Key Functions**: `getFlatUseFactor()`, `getOrientationOptimization()`

### Volume Factor (CV)
**Location**: `./volume-factor/`
- Adjusts bending values for glulam and engineered lumber
- Accounts for volume effect on strength variability
- **Key Functions**: `getVolumeFactor()`, `calculateVolumeFactorDetails()`

### Beam Stability Factor (CL)
**Location**: `./stability/`
- Accounts for lateral-torsional buckling of bending members
- Critical for unbraced beams and deep members
- **Key Functions**: `getBeamStabilityFactorCL()`, `getEffectiveLengthFactor()`

### LRFD Factors
**Location**: `./lrfd/`
- Format conversion, resistance, and time effect factors for LRFD
- Converts ASD design values to LRFD design values
- **Key Functions**: `getLrfdFormatConversionFactorKF()`, `calculateAdjustedLrfdDesignValue()`

## Usage

### Individual Module Import
```typescript
// Import specific module
import { getLoadDurationFactorCD } from './load-duration/load-duration';
import { getWetServiceFactor } from './wet-service/wet-service';
import { getRepetitiveMemberFactor } from './repetitive-member/repetitive-member';
```

### Centralized Import
```typescript
// Import from main index (recommended)
import { 
  getLoadDurationFactorCD,
  getWetServiceFactor,
  getRepetitiveMemberFactor,
  // ... other functions
} from './adjustment-factors';
```

## Constants

All wood design constants are consolidated in:
- **Location**: `../constants.ts`
- **Contents**: NDS tables, factors, thresholds, and design value types
- **Usage**: Imported by all modules for consistency

## Design Philosophy

### Comprehensive Coverage
- Complete implementation of all NDS adjustment factors
- Detailed validation and error handling
- Extensive test coverage (467+ tests)

### Modular Architecture
- Each factor in its own module for maintainability
- Consistent API patterns across modules
- Clear separation of concerns

### Documentation-Driven
- Comprehensive README for each module
- Inline code documentation
- Usage examples and design considerations

### Standards Compliance
- Based on NDS 2018 specifications
- References to specific NDS sections and tables
- Validation against published design values

## Testing

Run all adjustment factor tests:
```bash
npm test -- --testPathPattern="adjustment-factors"
```

Run specific module tests:
```bash
npm test -- --testPathPattern="load-duration"
npm test -- --testPathPattern="wet-service"
# ... etc
```

## References

- **NDS 2018**: National Design Specification for Wood Construction
- **NDS Supplement**: Design Values for Wood Construction
- **ASCE 7**: Minimum Design Loads and Associated Criteria for Buildings
- **IBC**: International Building Code

## Contributing

When adding new adjustment factors or modifying existing ones:

1. **Follow the established module structure**
2. **Include comprehensive tests** (aim for >95% coverage)
3. **Document all functions** with JSDoc comments
4. **Update the main README** to include new modules
5. **Reference specific NDS sections** in documentation
6. **Validate against published examples** where available 