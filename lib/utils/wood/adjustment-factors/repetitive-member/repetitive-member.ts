/**
 * Repetitive Member Factor for Sawn Lumber Design Values
 * Based on NDS (National Design Specification) for Wood Construction - Section 4.3.9
 *
 * Reference: Tables 4A, 4B, 4C, 4D, 4E, 4F - Adjustment Factors
 * The repetitive member factor Cr = 1.15 applies to dimension lumber 2" to 4" thick
 * when used as joists, truss chords, rafters, studs, planks, decking, or similar members.
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  REPETITIVE_MEMBER_FACTOR_CONSTANTS,
  REPETITIVE_MEMBER_TYPES,
  RepetitiveMemberType,
  LOAD_DISTRIBUTING_ELEMENTS,
  LoadDistributingElement,
  REPETITIVE_MEMBER_LUMBER_CATEGORIES,
  RepetitiveMemberLumberCategory,
} from "../../constants";

/**
 * Input parameters for repetitive member factor calculation
 */
export interface RepetitiveMemberFactorInput {
  /** Lumber thickness in inches */
  thickness: number;
  /** Type of member (joists, rafters, studs, etc.) */
  memberType: RepetitiveMemberType;
  /** Spacing between members on center in inches */
  spacingOnCenter: number;
  /** Number of members in the assembly */
  numberOfMembers: number;
  /** Types of load distributing elements present */
  loadDistributingElements: LoadDistributingElement[];
  /** Lumber category (dimension lumber, decking, etc.) */
  lumberCategory: RepetitiveMemberLumberCategory;
  /** Design value type being adjusted */
  designValueType: DesignValueType;
}

/**
 * Validation result for repetitive member factor input
 */
export interface RepetitiveMemberFactorValidation {
  /** Whether the input is valid */
  isValid: boolean;
  /** Array of validation error messages */
  errors: string[];
  /** Array of warning messages */
  warnings: string[];
  /** Whether repetitive member factor applies */
  factorApplies: boolean;
}

/**
 * Analysis result for repetitive member factor calculation
 */
export interface RepetitiveMemberFactorAnalysis {
  /** The calculated repetitive member factor */
  factor: number;
  /** Whether the factor applies to the given conditions */
  factorApplies: boolean;
  /** Detailed validation results */
  validation: RepetitiveMemberFactorValidation;
  /** Explanation of why factor applies or doesn't apply */
  explanation: string;
  /** Requirements that are met */
  requirementsMet: string[];
  /** Requirements that are not met */
  requirementsNotMet: string[];
}

/**
 * Calculates the repetitive member factor, Cr, for dimension lumber
 *
 * Per NDS 4.3.9: Reference bending design values, Fb, in Tables 4A, 4B, 4C, and 4F
 * for dimension lumber 2" to 4" thick shall be multiplied by the repetitive member
 * factor, Cr = 1.15, where such members are used as joists, truss chords, rafters,
 * studs, planks, decking, or similar members which are in contact or spaced not more
 * than 24" on center, are not less than three in number and are joined by floor, roof,
 * or other load distributing elements adequate to support the design load.
 *
 * Note: Reference bending design values in Table 4E for visually graded Decking
 * have already been multiplied by Cr = 1.15.
 *
 * @param input - Input parameters for repetitive member factor calculation
 * @returns The repetitive member factor Cr (1.15 if applicable, 1.0 if not)
 */
export function getRepetitiveMemberFactor(
  input: RepetitiveMemberFactorInput
): number {
  const analysis = getRepetitiveMemberFactorAnalysis(input);
  return analysis.factor;
}

/**
 * Validates input parameters for repetitive member factor calculation
 *
 * @param input - Input parameters to validate
 * @returns Validation result with errors, warnings, and applicability
 */
export function validateRepetitiveMemberFactorInput(
  input: RepetitiveMemberFactorInput
): RepetitiveMemberFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate thickness
  if (input.thickness <= 0) {
    errors.push("Lumber thickness must be positive");
  }

  // Validate spacing
  if (input.spacingOnCenter <= 0) {
    errors.push("Spacing on center must be positive");
  }

  // Validate number of members
  if (input.numberOfMembers < 1) {
    errors.push("Number of members must be at least 1");
  }

  // Validate load distributing elements
  if (
    !input.loadDistributingElements ||
    input.loadDistributingElements.length === 0
  ) {
    errors.push("At least one load distributing element must be specified");
  }

  // Check if repetitive member factor applies
  let factorApplies = true;

  // Check thickness requirements (NDS 4.3.9)
  const thicknessInRange =
    input.thickness >= REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_THICKNESS &&
    input.thickness <= REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_THICKNESS;

  if (!thicknessInRange) {
    factorApplies = false;
    warnings.push(
      `Thickness ${input.thickness}" is outside the range ` +
        `${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_THICKNESS}" to ` +
        `${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_THICKNESS}" - repetitive member factor does not apply`
    );
  }

  // Check spacing requirements (NDS 4.3.9)
  if (
    input.spacingOnCenter >
    REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_SPACING_ON_CENTER
  ) {
    factorApplies = false;
    warnings.push(
      `Spacing ${input.spacingOnCenter}" exceeds maximum ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_SPACING_ON_CENTER}" - ` +
        "repetitive member factor does not apply"
    );
  }

  // Check minimum number of members (NDS 4.3.9)
  if (
    input.numberOfMembers <
    REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_NUMBER_OF_MEMBERS
  ) {
    factorApplies = false;
    warnings.push(
      `Number of members ${input.numberOfMembers} is less than minimum ` +
        `${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_NUMBER_OF_MEMBERS} - repetitive member factor does not apply`
    );
  }

  // Check if design value type is bending (Fb) - repetitive member factor only applies to bending
  if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
    factorApplies = false;
    warnings.push(
      `Repetitive member factor only applies to bending design values (Fb), not ${input.designValueType}`
    );
  }

  // Special case: Check if decking already has repetitive member factor applied (Table 4E)
  if (
    input.lumberCategory ===
    REPETITIVE_MEMBER_LUMBER_CATEGORIES.VISUALLY_GRADED_DECKING
  ) {
    factorApplies = false;
    warnings.push(
      "Visually graded decking design values (Table 4E) already include repetitive member factor Cr = 1.15"
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    factorApplies,
  };
}

/**
 * Performs comprehensive analysis of repetitive member factor applicability
 *
 * @param input - Input parameters for analysis
 * @returns Detailed analysis including factor value, applicability, and explanations
 */
export function getRepetitiveMemberFactorAnalysis(
  input: RepetitiveMemberFactorInput
): RepetitiveMemberFactorAnalysis {
  const validation = validateRepetitiveMemberFactorInput(input);

  const requirementsMet: string[] = [];
  const requirementsNotMet: string[] = [];

  // Check each requirement
  const thicknessInRange =
    input.thickness >= REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_THICKNESS &&
    input.thickness <= REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_THICKNESS;

  if (thicknessInRange) {
    requirementsMet.push(
      `Thickness ${input.thickness}" is within range ` +
        `${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_THICKNESS}" to ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_THICKNESS}"`
    );
  } else {
    requirementsNotMet.push(
      `Thickness ${input.thickness}" is outside required range ` +
        `${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_THICKNESS}" to ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_THICKNESS}"`
    );
  }

  if (
    input.spacingOnCenter <=
    REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_SPACING_ON_CENTER
  ) {
    requirementsMet.push(
      `Spacing ${input.spacingOnCenter}" ≤ ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_SPACING_ON_CENTER}" maximum`
    );
  } else {
    requirementsNotMet.push(
      `Spacing ${input.spacingOnCenter}" > ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MAX_SPACING_ON_CENTER}" maximum`
    );
  }

  if (
    input.numberOfMembers >=
    REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_NUMBER_OF_MEMBERS
  ) {
    requirementsMet.push(
      `Number of members ${input.numberOfMembers} ≥ ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_NUMBER_OF_MEMBERS} minimum`
    );
  } else {
    requirementsNotMet.push(
      `Number of members ${input.numberOfMembers} < ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.MIN_NUMBER_OF_MEMBERS} minimum`
    );
  }

  if (input.designValueType === DESIGN_VALUE_TYPES.BENDING) {
    requirementsMet.push("Design value type is bending (Fb)");
  } else {
    requirementsNotMet.push(
      `Design value type ${input.designValueType} is not bending (Fb)`
    );
  }

  if (
    input.loadDistributingElements &&
    input.loadDistributingElements.length > 0
  ) {
    requirementsMet.push(
      `Load distributing elements present: ${input.loadDistributingElements.join(
        ", "
      )}`
    );
  } else {
    requirementsNotMet.push("No load distributing elements specified");
  }

  // Determine factor and explanation
  let factor: number;
  let explanation: string;

  if (!validation.isValid) {
    factor = REPETITIVE_MEMBER_FACTOR_CONSTANTS.NO_FACTOR;
    explanation = `Input validation failed: ${validation.errors.join(", ")}`;
  } else if (!validation.factorApplies) {
    factor = REPETITIVE_MEMBER_FACTOR_CONSTANTS.NO_FACTOR;
    explanation = `Repetitive member factor does not apply: ${validation.warnings.join(
      ", "
    )}`;
  } else {
    factor = REPETITIVE_MEMBER_FACTOR_CONSTANTS.STANDARD_FACTOR;
    explanation = `Repetitive member factor Cr = ${REPETITIVE_MEMBER_FACTOR_CONSTANTS.STANDARD_FACTOR} applies - all requirements met`;
  }

  return {
    factor,
    factorApplies: validation.factorApplies && validation.isValid,
    validation,
    explanation,
    requirementsMet,
    requirementsNotMet,
  };
}

/**
 * Applies repetitive member factor to a design value
 *
 * @param designValue - The original design value
 * @param input - Input parameters for repetitive member factor calculation
 * @returns The adjusted design value with repetitive member factor applied
 * @throws Error if input validation fails
 */
export function applyRepetitiveMemberFactor(
  designValue: number,
  input: RepetitiveMemberFactorInput
): number {
  if (designValue <= 0) {
    throw new Error("Design value must be positive");
  }

  const validation = validateRepetitiveMemberFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input: ${validation.errors.join(", ")}`);
  }

  const factor = getRepetitiveMemberFactor(input);
  return designValue * factor;
}

/**
 * Checks if repetitive member factor applies to given conditions
 *
 * @param input - Input parameters to check
 * @returns True if repetitive member factor applies, false otherwise
 */
export function isRepetitiveMemberFactorApplicable(
  input: RepetitiveMemberFactorInput
): boolean {
  const validation = validateRepetitiveMemberFactorInput(input);
  return validation.isValid && validation.factorApplies;
}

/**
 * Gets repetitive member factor for multiple design values
 * Note: Repetitive member factor only applies to bending design values (Fb)
 *
 * @param input - Input parameters for repetitive member factor calculation
 * @param designValueTypes - Array of design value types to get factors for
 * @returns Object mapping design value types to their repetitive member factors
 */
export function getMultipleRepetitiveMemberFactors(
  input: RepetitiveMemberFactorInput,
  designValueTypes: DesignValueType[]
): Record<string, number> {
  const factors: Record<string, number> = {};

  designValueTypes.forEach((type) => {
    const modifiedInput = { ...input, designValueType: type };
    factors[type] = getRepetitiveMemberFactor(modifiedInput);
  });

  return factors;
}

/**
 * Convenience function to check if member type qualifies for repetitive member factor
 *
 * @param memberType - The type of member to check
 * @returns True if the member type qualifies, false otherwise
 */
export function isMemberTypeQualified(
  memberType: RepetitiveMemberType
): boolean {
  return Object.values(REPETITIVE_MEMBER_TYPES).includes(memberType);
}

/**
 * Gets all qualified member types for repetitive member factor
 *
 * @returns Array of all qualified member types
 */
export function getQualifiedMemberTypes(): RepetitiveMemberType[] {
  return Object.values(REPETITIVE_MEMBER_TYPES);
}

/**
 * Gets all qualified load distributing element types
 *
 * @returns Array of all qualified load distributing element types
 */
export function getQualifiedLoadDistributingElements(): LoadDistributingElement[] {
  return Object.values(LOAD_DISTRIBUTING_ELEMENTS);
}
