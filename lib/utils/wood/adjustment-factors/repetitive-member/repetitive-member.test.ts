/**
 * Unit tests for Sawn Lumber Repetitive Member Adjustment Factors
 * Based on NDS (National Design Specification) for Wood Construction - Section 4.3.9
 */

import {
  getRepetitiveMemberFactor,
  validateRepetitiveMemberFactorInput,
  getRepetitiveMemberFactorAnalysis,
  applyRepetitiveMemberFactor,
  isRepetitiveMemberFactorApplicable,
  getMultipleRepetitiveMemberFactors,
  isMemberTypeQualified,
  getQualifiedMemberTypes,
  getQualifiedLoadDistributingElements,
  RepetitiveMemberFactorInput,
} from "./repetitive-member";
import {
  DESIGN_VALUE_TYPES,
  REPETITIVE_MEMBER_FACTOR_CONSTANTS,
  REPETITIVE_MEMBER_TYPES,
  LOAD_DISTRIBUTING_ELEMENTS,
  REPETITIVE_MEMBER_LUMBER_CATEGORIES,
} from "../../constants";

describe("Repetitive Member Factor Calculations", () => {
  // Base valid input for testing
  const baseValidInput: RepetitiveMemberFactorInput = {
    thickness: 2.0,
    memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
    spacingOnCenter: 16,
    numberOfMembers: 5,
    loadDistributingElements: [LOAD_DISTRIBUTING_ELEMENTS.SUBFLOORING],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  describe("getRepetitiveMemberFactor", () => {
    describe("Valid Conditions - Factor Applies", () => {
      test('should return 1.15 for valid joists at 16" o.c.', () => {
        const result = getRepetitiveMemberFactor(baseValidInput);
        expect(result).toBe(REPETITIVE_MEMBER_FACTOR_CONSTANTS.STANDARD_FACTOR);
        expect(result).toBe(1.15);
      });

      test('should return 1.15 for 2" thick rafters at 24" o.c. maximum spacing', () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          memberType: REPETITIVE_MEMBER_TYPES.RAFTERS,
          spacingOnCenter: 24, // Maximum allowed spacing
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.15);
      });

      test('should return 1.15 for 4" thick studs with minimum 3 members', () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 4.0, // Maximum allowed thickness
          memberType: REPETITIVE_MEMBER_TYPES.STUDS,
          numberOfMembers: 3, // Minimum allowed number
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.15);
      });

      test("should return 1.15 for truss chords with multiple load distributing elements", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          memberType: REPETITIVE_MEMBER_TYPES.TRUSS_CHORDS,
          loadDistributingElements: [
            LOAD_DISTRIBUTING_ELEMENTS.SHEATHING,
            LOAD_DISTRIBUTING_ELEMENTS.NAIL_GLUING,
          ],
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.15);
      });

      test("should return 1.15 for planks with tongue and groove connection", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          memberType: REPETITIVE_MEMBER_TYPES.PLANKS,
          loadDistributingElements: [
            LOAD_DISTRIBUTING_ELEMENTS.TONGUE_AND_GROOVE,
          ],
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.15);
      });

      test("should return 1.15 for decking (dimension lumber category)", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          memberType: REPETITIVE_MEMBER_TYPES.DECKING,
          lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.15);
      });
    });

    describe("Invalid Conditions - Factor Does Not Apply", () => {
      test('should return 1.0 for thickness below minimum (1.5")', () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 1.5, // Below 2" minimum
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(REPETITIVE_MEMBER_FACTOR_CONSTANTS.NO_FACTOR);
        expect(result).toBe(1.0);
      });

      test('should return 1.0 for thickness above maximum (5")', () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 5.0, // Above 4" maximum
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.0);
      });

      test('should return 1.0 for spacing exceeding 24" o.c.', () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          spacingOnCenter: 30, // Exceeds 24" maximum
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.0);
      });

      test("should return 1.0 for fewer than 3 members", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          numberOfMembers: 2, // Below 3 minimum
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.0);
      });

      test("should return 1.0 for non-bending design values", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.0);
      });

      test("should return 1.0 for visually graded decking (already includes Cr)", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          memberType: REPETITIVE_MEMBER_TYPES.DECKING,
          lumberCategory:
            REPETITIVE_MEMBER_LUMBER_CATEGORIES.VISUALLY_GRADED_DECKING,
        };
        const result = getRepetitiveMemberFactor(input);
        expect(result).toBe(1.0);
      });
    });
  });

  describe("validateRepetitiveMemberFactorInput", () => {
    describe("Valid Input Validation", () => {
      test("should validate correct input with no errors or warnings", () => {
        const result = validateRepetitiveMemberFactorInput(baseValidInput);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.factorApplies).toBe(true);
      });

      test("should validate at boundary conditions", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 2.0, // Minimum
          spacingOnCenter: 24, // Maximum
          numberOfMembers: 3, // Minimum
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(true);
        expect(result.factorApplies).toBe(true);
      });
    });

    describe("Input Validation Errors", () => {
      test("should return error for negative thickness", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: -1,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain("Lumber thickness must be positive");
      });

      test("should return error for zero spacing", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          spacingOnCenter: 0,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain("Spacing on center must be positive");
      });

      test("should return error for zero members", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          numberOfMembers: 0,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain("Number of members must be at least 1");
      });

      test("should return error for empty load distributing elements", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          loadDistributingElements: [],
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(
          "At least one load distributing element must be specified"
        );
      });

      test("should return multiple errors for multiple invalid inputs", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: -1,
          spacingOnCenter: 0,
          numberOfMembers: 0,
          loadDistributingElements: [],
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveLength(4);
      });
    });

    describe("Factor Applicability Warnings", () => {
      test("should warn for thickness outside range", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 1.5,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(true);
        expect(result.factorApplies).toBe(false);
        expect(
          result.warnings.some((w) => w.includes("outside the range"))
        ).toBe(true);
      });

      test("should warn for excessive spacing", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          spacingOnCenter: 30,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(true);
        expect(result.factorApplies).toBe(false);
        expect(result.warnings.some((w) => w.includes("exceeds maximum"))).toBe(
          true
        );
      });

      test("should warn for insufficient number of members", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          numberOfMembers: 2,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(true);
        expect(result.factorApplies).toBe(false);
        expect(
          result.warnings.some((w) => w.includes("less than minimum"))
        ).toBe(true);
      });

      test("should warn for non-bending design value", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          designValueType: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(true);
        expect(result.factorApplies).toBe(false);
        expect(
          result.warnings.some((w) => w.includes("only applies to bending"))
        ).toBe(true);
      });

      test("should warn for visually graded decking", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          lumberCategory:
            REPETITIVE_MEMBER_LUMBER_CATEGORIES.VISUALLY_GRADED_DECKING,
        };
        const result = validateRepetitiveMemberFactorInput(input);
        expect(result.isValid).toBe(true);
        expect(result.factorApplies).toBe(false);
        expect(result.warnings.some((w) => w.includes("already include"))).toBe(
          true
        );
      });
    });
  });

  describe("getRepetitiveMemberFactorAnalysis", () => {
    describe("Successful Analysis", () => {
      test("should provide complete analysis for valid conditions", () => {
        const result = getRepetitiveMemberFactorAnalysis(baseValidInput);

        expect(result.factor).toBe(1.15);
        expect(result.factorApplies).toBe(true);
        expect(result.validation.isValid).toBe(true);
        expect(result.explanation).toContain("all requirements met");
        expect(result.requirementsMet.length).toBeGreaterThan(0);
        expect(result.requirementsNotMet).toHaveLength(0);
      });

      test("should list all requirements met for valid input", () => {
        const result = getRepetitiveMemberFactorAnalysis(baseValidInput);

        const metTexts = result.requirementsMet.join(" ");
        expect(metTexts).toContain("Thickness");
        expect(metTexts).toContain("Spacing");
        expect(metTexts).toContain("Number of members");
        expect(metTexts).toContain("bending");
        expect(metTexts).toContain("Load distributing elements");
      });
    });

    describe("Analysis of Invalid Conditions", () => {
      test("should provide analysis for conditions where factor does not apply", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 5.0, // Outside range
          spacingOnCenter: 30, // Too wide
        };
        const result = getRepetitiveMemberFactorAnalysis(input);

        expect(result.factor).toBe(1.0);
        expect(result.factorApplies).toBe(false);
        expect(result.explanation).toContain("does not apply");
        expect(result.requirementsNotMet.length).toBeGreaterThan(0);
      });

      test("should identify specific unmet requirements", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 6.0,
          numberOfMembers: 1,
        };
        const result = getRepetitiveMemberFactorAnalysis(input);

        const unmetTexts = result.requirementsNotMet.join(" ");
        expect(unmetTexts).toContain("outside required range");
        expect(unmetTexts).toContain("< 3 minimum");
      });
    });

    describe("Analysis of Input Validation Errors", () => {
      test("should handle invalid input gracefully", () => {
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: -1,
          spacingOnCenter: 0,
        };
        const result = getRepetitiveMemberFactorAnalysis(input);

        expect(result.factor).toBe(1.0);
        expect(result.factorApplies).toBe(false);
        expect(result.validation.isValid).toBe(false);
        expect(result.explanation).toContain("Input validation failed");
      });
    });
  });

  describe("applyRepetitiveMemberFactor", () => {
    describe("Valid Applications", () => {
      test("should apply factor to bending design value", () => {
        const originalValue = 1000; // psi
        const result = applyRepetitiveMemberFactor(
          originalValue,
          baseValidInput
        );

        expect(result).toBe(originalValue * 1.15);
        expect(result).toBe(1150);
      });

      test("should not change value when factor does not apply", () => {
        const originalValue = 1200;
        const input: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: 5.0, // Outside range
        };
        const result = applyRepetitiveMemberFactor(originalValue, input);

        expect(result).toBe(originalValue * 1.0);
        expect(result).toBe(1200);
      });

      test("should handle decimal values correctly", () => {
        const originalValue = 875.5;
        const result = applyRepetitiveMemberFactor(
          originalValue,
          baseValidInput
        );

        expect(result).toBeCloseTo(875.5 * 1.15, 2);
        expect(result).toBeCloseTo(1006.825, 2);
      });
    });

    describe("Invalid Applications", () => {
      test("should throw error for negative design value", () => {
        expect(() => {
          applyRepetitiveMemberFactor(-100, baseValidInput);
        }).toThrow("Design value must be positive");
      });

      test("should throw error for zero design value", () => {
        expect(() => {
          applyRepetitiveMemberFactor(0, baseValidInput);
        }).toThrow("Design value must be positive");
      });

      test("should throw error for invalid input parameters", () => {
        const invalidInput: RepetitiveMemberFactorInput = {
          ...baseValidInput,
          thickness: -1,
        };

        expect(() => {
          applyRepetitiveMemberFactor(1000, invalidInput);
        }).toThrow("Invalid input");
      });
    });
  });

  describe("isRepetitiveMemberFactorApplicable", () => {
    test("should return true for valid conditions", () => {
      const result = isRepetitiveMemberFactorApplicable(baseValidInput);
      expect(result).toBe(true);
    });

    test("should return false for thickness outside range", () => {
      const input: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        thickness: 1.5,
      };
      const result = isRepetitiveMemberFactorApplicable(input);
      expect(result).toBe(false);
    });

    test("should return false for excessive spacing", () => {
      const input: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        spacingOnCenter: 30,
      };
      const result = isRepetitiveMemberFactorApplicable(input);
      expect(result).toBe(false);
    });

    test("should return false for invalid input", () => {
      const input: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        thickness: -1,
      };
      const result = isRepetitiveMemberFactorApplicable(input);
      expect(result).toBe(false);
    });
  });

  describe("getMultipleRepetitiveMemberFactors", () => {
    test("should return factors for multiple design value types", () => {
      const designValueTypes = [
        DESIGN_VALUE_TYPES.BENDING,
        DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
      ];

      const result = getMultipleRepetitiveMemberFactors(
        baseValidInput,
        designValueTypes
      );

      expect(result[DESIGN_VALUE_TYPES.BENDING]).toBe(1.15); // Factor applies
      expect(result[DESIGN_VALUE_TYPES.TENSION_PARALLEL]).toBe(1.0); // Factor doesn't apply
      expect(result[DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]).toBe(1.0); // Factor doesn't apply
    });

    test("should handle empty design value types array", () => {
      const result = getMultipleRepetitiveMemberFactors(baseValidInput, []);
      expect(Object.keys(result)).toHaveLength(0);
    });
  });

  describe("Utility Functions", () => {
    describe("isMemberTypeQualified", () => {
      test("should return true for all qualified member types", () => {
        Object.values(REPETITIVE_MEMBER_TYPES).forEach((memberType) => {
          expect(isMemberTypeQualified(memberType)).toBe(true);
        });
      });

      test("should return false for invalid member type", () => {
        expect(isMemberTypeQualified("invalid_type" as any)).toBe(false);
      });
    });

    describe("getQualifiedMemberTypes", () => {
      test("should return all qualified member types", () => {
        const result = getQualifiedMemberTypes();
        const expectedTypes = Object.values(REPETITIVE_MEMBER_TYPES);

        expect(result).toEqual(expect.arrayContaining(expectedTypes));
        expect(result).toHaveLength(expectedTypes.length);
      });

      test("should include all expected member types", () => {
        const result = getQualifiedMemberTypes();

        expect(result).toContain(REPETITIVE_MEMBER_TYPES.JOISTS);
        expect(result).toContain(REPETITIVE_MEMBER_TYPES.TRUSS_CHORDS);
        expect(result).toContain(REPETITIVE_MEMBER_TYPES.RAFTERS);
        expect(result).toContain(REPETITIVE_MEMBER_TYPES.STUDS);
        expect(result).toContain(REPETITIVE_MEMBER_TYPES.PLANKS);
        expect(result).toContain(REPETITIVE_MEMBER_TYPES.DECKING);
        expect(result).toContain(REPETITIVE_MEMBER_TYPES.SIMILAR_MEMBERS);
      });
    });

    describe("getQualifiedLoadDistributingElements", () => {
      test("should return all qualified load distributing elements", () => {
        const result = getQualifiedLoadDistributingElements();
        const expectedElements = Object.values(LOAD_DISTRIBUTING_ELEMENTS);

        expect(result).toEqual(expect.arrayContaining(expectedElements));
        expect(result).toHaveLength(expectedElements.length);
      });

      test("should include all expected load distributing elements", () => {
        const result = getQualifiedLoadDistributingElements();

        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.SUBFLOORING);
        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.FLOORING);
        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.SHEATHING);
        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.COVERING_ELEMENTS);
        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.NAIL_GLUING);
        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.TONGUE_AND_GROOVE);
        expect(result).toContain(LOAD_DISTRIBUTING_ELEMENTS.THROUGH_NAILING);
      });
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    test("should handle exact boundary thickness values", () => {
      const input2inch: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        thickness: 2.0, // Exact minimum
      };
      expect(getRepetitiveMemberFactor(input2inch)).toBe(1.15);

      const input4inch: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        thickness: 4.0, // Exact maximum
      };
      expect(getRepetitiveMemberFactor(input4inch)).toBe(1.15);
    });

    test("should handle exact boundary spacing values", () => {
      const inputMaxSpacing: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        spacingOnCenter: 24, // Exact maximum
      };
      expect(getRepetitiveMemberFactor(inputMaxSpacing)).toBe(1.15);

      const inputExceedSpacing: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        spacingOnCenter: 24.1, // Just over maximum
      };
      expect(getRepetitiveMemberFactor(inputExceedSpacing)).toBe(1.0);
    });

    test("should handle exact minimum number of members", () => {
      const inputMinMembers: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        numberOfMembers: 3, // Exact minimum
      };
      expect(getRepetitiveMemberFactor(inputMinMembers)).toBe(1.15);

      const inputBelowMin: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        numberOfMembers: 2, // Below minimum
      };
      expect(getRepetitiveMemberFactor(inputBelowMin)).toBe(1.0);
    });

    test("should handle large number of members", () => {
      const input: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        numberOfMembers: 100,
      };
      expect(getRepetitiveMemberFactor(input)).toBe(1.15);
    });

    test("should handle very small positive spacing", () => {
      const input: RepetitiveMemberFactorInput = {
        ...baseValidInput,
        spacingOnCenter: 0.1, // Very small but positive
      };
      expect(getRepetitiveMemberFactor(input)).toBe(1.15);
    });
  });
});
