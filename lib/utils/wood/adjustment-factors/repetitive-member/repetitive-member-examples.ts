/**
 * Repetitive Member Factor Examples
 * Demonstrates practical usage of repetitive member factor calculations
 * Based on NDS (National Design Specification) for Wood Construction - Section 4.3.9
 */

import {
  getRepetitiveMemberFactor,
  getRepetitiveMemberFactorAnalysis,
  applyRepetitiveMemberFactor,
  RepetitiveMemberFactorInput,
} from "./repetitive-member";
import {
  DESIGN_VALUE_TYPES,
  REPETITIVE_MEMBER_TYPES,
  LOAD_DISTRIBUTING_ELEMENTS,
  REPETITIVE_MEMBER_LUMBER_CATEGORIES,
} from "../../constants";

/**
 * Example 1: Floor Joists
 * Standard residential floor framing with 2x10 joists at 16" o.c.
 */
export function example1_FloorJoists() {
  console.log('Example 1: Floor Joists (2x10 @ 16" o.c.)');

  const input: RepetitiveMemberFactorInput = {
    thickness: 2.0, // 2x lumber
    memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
    spacingOnCenter: 16, // 16" o.c.
    numberOfMembers: 8, // 8 joists in span
    loadDistributingElements: [
      LOAD_DISTRIBUTING_ELEMENTS.SUBFLOORING,
      LOAD_DISTRIBUTING_ELEMENTS.FLOORING,
    ],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 1200; // psi
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Example 2: Roof Rafters
 * Residential roof framing with 2x8 rafters at maximum 24" o.c.
 */
export function example2_RoofRafters() {
  console.log('Example 2: Roof Rafters (2x8 @ 24" o.c.)');

  const input: RepetitiveMemberFactorInput = {
    thickness: 2.0, // 2x lumber
    memberType: REPETITIVE_MEMBER_TYPES.RAFTERS,
    spacingOnCenter: 24, // Maximum allowed spacing
    numberOfMembers: 10, // 10 rafters
    loadDistributingElements: [LOAD_DISTRIBUTING_ELEMENTS.SHEATHING],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 875; // psi
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Example 3: Wall Studs
 * Standard wall framing with 2x6 studs at 16" o.c.
 */
export function example3_WallStuds() {
  console.log('Example 3: Wall Studs (2x6 @ 16" o.c.)');

  const input: RepetitiveMemberFactorInput = {
    thickness: 2.0, // 2x lumber
    memberType: REPETITIVE_MEMBER_TYPES.STUDS,
    spacingOnCenter: 16, // 16" o.c.
    numberOfMembers: 12, // 12 studs in wall section
    loadDistributingElements: [
      LOAD_DISTRIBUTING_ELEMENTS.SHEATHING,
      LOAD_DISTRIBUTING_ELEMENTS.COVERING_ELEMENTS,
    ],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 1050; // psi
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Example 4: Heavy Timber Decking
 * 4x lumber used as decking - maximum thickness for repetitive member factor
 */
export function example4_HeavyTimberDecking() {
  console.log('Example 4: Heavy Timber Decking (4x12 @ 20" o.c.)');

  const input: RepetitiveMemberFactorInput = {
    thickness: 4.0, // Maximum thickness for repetitive member factor
    memberType: REPETITIVE_MEMBER_TYPES.DECKING,
    spacingOnCenter: 20, // Within 24" limit
    numberOfMembers: 6, // 6 pieces in assembly
    loadDistributingElements: [LOAD_DISTRIBUTING_ELEMENTS.TONGUE_AND_GROOVE],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 1000; // psi
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Example 5: Non-Qualifying Case - Single Beam
 * Shows case where repetitive member factor does NOT apply
 */
export function example5_SingleBeam() {
  console.log("Example 5: Single Beam (4x12 - Factor Does NOT Apply)");

  const input: RepetitiveMemberFactorInput = {
    thickness: 4.0,
    memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
    spacingOnCenter: 16,
    numberOfMembers: 1, // Only 1 member - below minimum of 3
    loadDistributingElements: [LOAD_DISTRIBUTING_ELEMENTS.SUBFLOORING],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 1350; // psi
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log(
    `  Requirements not met: ${analysis.requirementsNotMet.join(", ")}`
  );
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Example 6: Non-Qualifying Case - Excessive Spacing
 * Shows case where spacing exceeds 24" limit
 */
export function example6_ExcessiveSpacing() {
  console.log(
    'Example 6: Excessive Spacing (30" o.c. - Factor Does NOT Apply)'
  );

  const input: RepetitiveMemberFactorInput = {
    thickness: 2.0,
    memberType: REPETITIVE_MEMBER_TYPES.JOISTS,
    spacingOnCenter: 30, // Exceeds 24" maximum
    numberOfMembers: 8,
    loadDistributingElements: [LOAD_DISTRIBUTING_ELEMENTS.SUBFLOORING],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.DIMENSION_LUMBER,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 1200; // psi
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log(
    `  Requirements not met: ${analysis.requirementsNotMet.join(", ")}`
  );
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Example 7: Visually Graded Decking
 * Shows special case where factor is already included (Table 4E)
 */
export function example7_VisuallyGradedDecking() {
  console.log("Example 7: Visually Graded Decking (Factor Already Included)");

  const input: RepetitiveMemberFactorInput = {
    thickness: 2.0,
    memberType: REPETITIVE_MEMBER_TYPES.DECKING,
    spacingOnCenter: 16,
    numberOfMembers: 10,
    loadDistributingElements: [LOAD_DISTRIBUTING_ELEMENTS.TONGUE_AND_GROOVE],
    lumberCategory: REPETITIVE_MEMBER_LUMBER_CATEGORIES.VISUALLY_GRADED_DECKING, // Special case
    designValueType: DESIGN_VALUE_TYPES.BENDING,
  };

  const analysis = getRepetitiveMemberFactorAnalysis(input);
  const originalFb = 1150; // psi (already includes Cr = 1.15)
  const adjustedFb = applyRepetitiveMemberFactor(originalFb, input);

  console.log(`  Original Fb: ${originalFb} psi (already includes Cr)`);
  console.log(`  Repetitive member factor (Cr): ${analysis.factor}`);
  console.log(`  Adjusted Fb: ${adjustedFb} psi`);
  console.log(`  Factor applies: ${analysis.factorApplies}`);
  console.log(`  Explanation: ${analysis.explanation}`);
  console.log("");

  return { input, analysis, originalFb, adjustedFb };
}

/**
 * Run all examples
 */
export function runAllExamples() {
  console.log("=".repeat(60));
  console.log("REPETITIVE MEMBER FACTOR EXAMPLES");
  console.log("Based on NDS Section 4.3.9");
  console.log("=".repeat(60));
  console.log("");

  const results = [
    example1_FloorJoists(),
    example2_RoofRafters(),
    example3_WallStuds(),
    example4_HeavyTimberDecking(),
    example5_SingleBeam(),
    example6_ExcessiveSpacing(),
    example7_VisuallyGradedDecking(),
  ];

  console.log("=".repeat(60));
  console.log("SUMMARY");
  console.log("=".repeat(60));

  results.forEach((result, index) => {
    const exampleNumber = index + 1;
    const increase = result.adjustedFb - result.originalFb;
    const percentIncrease = ((increase / result.originalFb) * 100).toFixed(1);

    console.log(
      `Example ${exampleNumber}: Cr = ${result.analysis.factor}, ` +
        `Fb increase = ${increase} psi (${percentIncrease}%)`
    );
  });

  return results;
}

// Export for use in other modules
export default {
  example1_FloorJoists,
  example2_RoofRafters,
  example3_WallStuds,
  example4_HeavyTimberDecking,
  example5_SingleBeam,
  example6_ExcessiveSpacing,
  example7_VisuallyGradedDecking,
  runAllExamples,
};
