# Repetitive Member Factor (Cr)

## Overview

This module implements the repetitive member factor (Cr) calculations according to the National Design Specification (NDS) for Wood Construction, Section 4.3.9.

The repetitive member factor accounts for the statistical reduction in variability when multiple members act together and share loads through load-distributing elements.

## Key Concepts

### When to Apply Repetitive Member Factor

The repetitive member factor Cr = 1.15 applies to bending design values (Fb) when ALL of the following conditions are met:

1. **Member Type**: Dimension lumber (2" to 4" thick) such as:
   - Joists
   - Truss chords
   - Rafters
   - Studs
   - Planks
   - Decking

2. **Spacing**: Members spaced not more than 24 inches on center

3. **Number**: At least 3 members acting together

4. **Load Distribution**: Connected by load-distributing elements such as:
   - Subflooring
   - Flooring
   - Sheathing
   - Other covering elements properly fastened

### Limitations

- Only applies to **bending design values (Fb)**
- Does not apply to **visually graded decking** (already includes Cr in published values)
- Member thickness must be between 2" and 4"

## API Reference

### Main Functions

#### `getRepetitiveMemberFactor(input)`

Calculates the repetitive member factor for a given configuration.

**Parameters:**
- `input.memberType`: Type of structural member
- `input.thickness`: Member thickness in inches
- `input.spacingOnCenter`: Spacing between members in inches
- `input.numberOfMembers`: Number of members acting together
- `input.hasLoadDistributingElements`: Whether load-distributing elements are present
- `input.lumberCategory`: Category of lumber (dimension lumber, decking, etc.)

**Returns:**
- `factor`: The repetitive member factor (1.0 or 1.15)
- `applicable`: Whether the factor applies
- `explanation`: Detailed explanation of the result

#### `validateRepetitiveMemberFactorInput(input)`

Validates input parameters and provides detailed validation results.

#### `getRepetitiveMemberFactorAnalysis(input)`

Provides comprehensive analysis including all requirements and their status.

### Usage Example

```typescript
import { getRepetitiveMemberFactor } from './repetitive-member';

const result = getRepetitiveMemberFactor({
  memberType: 'joists',
  thickness: 2.0,
  spacingOnCenter: 16,
  numberOfMembers: 8,
  hasLoadDistributingElements: true,
  lumberCategory: 'dimension_lumber'
});

console.log(result.factor); // 1.15
console.log(result.applicable); // true
```

## Files

- `repetitive-member.ts` - Main implementation
- `repetitive-member.test.ts` - Comprehensive test suite
- `repetitive-member-examples.ts` - Usage examples and scenarios
- `README.md` - This documentation

## References

- NDS 2018, Section 4.3.9 - Repetitive Member Factor, Cr
- NDS Table 4A - Adjustment Factors for Sawn Lumber 