/**
 * Bearing Area Factor (Cb) Calculations
 *
 * Implements NDS 3.10.3 Bearing at an Angle to Grain and NDS 3.10.4 Bearing Area Factor
 * for adjusting reference compression design values perpendicular to grain based on
 * bearing length and load angle.
 *
 * Based on:
 * - NDS 3.10.3: Bearing at an Angle to Grain (Equation 3.10-1)
 * - NDS 3.10.4: Bearing Area Factor, Cb (Equation 3.10-2)
 * - NDS Table 3.10.4: Bearing Area Factors for various bearing lengths
 * - NDS Figure 31: Bearing at an Angle to Grain illustration
 *
 * Key equations:
 * - Adjusted bearing design value: Fc' = (Fc * Fc⊥') / (Fc sin²θ + Fc⊥' cos²θ)
 * - Bearing area factor: Cb = (ℓb + 0.375) / ℓb
 *
 * @fileoverview Bearing area factor calculations for wood structural design
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import { DESIGN_VALUE_TYPES, DesignValueType } from "../../constants";

/**
 * Bearing area factors from NDS Table 3.10.4
 * Values for small areas such as plates and washers
 */
export const BEARING_AREA_FACTORS_TABLE = {
  // Bearing length (ℓb) in inches
  0.5: 1.75,
  1.0: 1.38,
  1.5: 1.25,
  2.0: 1.19,
  3.0: 1.13,
  4.0: 1.1,
  6.0: 1.0,
} as const;

/**
 * Bearing area factor constants from NDS 3.10.4
 */
export const BEARING_AREA_FACTOR_CONSTANTS = {
  // Bearing area factor formula constant (NDS Equation 3.10-2)
  BEARING_LENGTH_CONSTANT: 0.375, // inches

  // Minimum bearing area factor
  MIN_BEARING_AREA_FACTOR: 1.0,

  // Bearing length thresholds
  MIN_BEARING_LENGTH: 0.1, // Minimum practical bearing length, inches
  TABULATED_MAX_LENGTH: 6.0, // Maximum length in tabulated values, inches

  // Round bearing area specifications
  ROUND_BEARING_DIAMETER_EQUALS_LENGTH: true, // For washers, ℓb = diameter

  // Angle limitations
  MIN_ANGLE_DEGREES: 0, // Load parallel to grain
  MAX_ANGLE_DEGREES: 90, // Load perpendicular to grain

  // Default values
  DEFAULT_BEARING_AREA_FACTOR: 1.0,
  PARALLEL_TO_GRAIN_ANGLE: 0, // degrees
  PERPENDICULAR_TO_GRAIN_ANGLE: 90, // degrees
} as const;

/**
 * Bearing configuration types
 */
export const BEARING_CONFIGURATION_TYPES = {
  PLATE: "plate", // Bearing plates
  WASHER: "washer", // Round washers
  BOLT: "bolt", // Bolt bearing
  POST: "post", // Post on sill
  BEAM: "beam", // Beam on support
  GENERAL: "general", // General bearing condition
} as const;

export type BearingConfigurationType =
  (typeof BEARING_CONFIGURATION_TYPES)[keyof typeof BEARING_CONFIGURATION_TYPES];

/**
 * Input parameters for bearing area factor calculation
 */
export interface BearingAreaFactorInput {
  /** Bearing length measured parallel to grain, inches */
  bearingLength: number;

  /** Configuration type of bearing */
  configurationType?: BearingConfigurationType;

  /** Diameter for round bearing areas (washers), inches */
  diameter?: number;

  /** Whether to use tabulated values when available */
  useTabulated?: boolean;
}

/**
 * Input parameters for bearing at angle to grain calculation
 */
export interface BearingAtAngleInput {
  /** Bearing length measured parallel to grain, inches */
  bearingLength: number;

  /** Angle between direction of load and direction of grain, degrees */
  angleToGrain: number;

  /** Reference compression design value parallel to grain, psi */
  fcParallel: number;

  /** Adjusted compression design value perpendicular to grain, psi */
  fcPerpendicularAdjusted: number;

  /** Configuration type of bearing */
  configurationType?: BearingConfigurationType;

  /** Diameter for round bearing areas, inches */
  diameter?: number;

  /** Whether to use tabulated values when available */
  useTabulated?: boolean;
}

/**
 * Validation result for bearing area factor input
 */
export interface BearingAreaFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Analysis result for bearing area factor calculation
 */
export interface BearingAreaFactorAnalysis {
  input: BearingAreaFactorInput;
  bearingAreaFactor: number;
  calculationMethod: "formula" | "tabulated" | "minimum";
  effectiveBearingLength: number;
  validation: BearingAreaFactorValidation;
  recommendations: string[];
}

/**
 * Analysis result for bearing at angle to grain calculation
 */
export interface BearingAtAngleAnalysis {
  input: BearingAtAngleInput;
  adjustedBearingValue: number;
  bearingAreaFactor: number;
  bearingAreaFactorAnalysis: BearingAreaFactorAnalysis;
  angleInRadians: number;
  sinSquaredTheta: number;
  cosSquaredTheta: number;
  validation: BearingAreaFactorValidation;
  recommendations: string[];
}

/**
 * Validates bearing area factor input parameters
 *
 * @param input - Bearing area factor input parameters
 * @returns Validation result with errors and warnings
 *
 * @example
 * ```typescript
 * const validation = validateBearingAreaFactorInput({
 *   bearingLength: 2.0,
 *   configurationType: 'plate'
 * });
 * ```
 */
export function validateBearingAreaFactorInput(
  input: BearingAreaFactorInput
): BearingAreaFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate bearing length
  if (
    typeof input.bearingLength !== "number" ||
    !isFinite(input.bearingLength)
  ) {
    errors.push("Bearing length must be a finite number");
  } else if (input.bearingLength <= 0) {
    errors.push("Bearing length must be positive");
  } else if (
    input.bearingLength < BEARING_AREA_FACTOR_CONSTANTS.MIN_BEARING_LENGTH
  ) {
    warnings.push(
      `Bearing length (${input.bearingLength}") is very small - verify design assumptions`
    );
  }

  // Validate configuration type
  if (
    input.configurationType &&
    !Object.values(BEARING_CONFIGURATION_TYPES).includes(
      input.configurationType
    )
  ) {
    errors.push(
      `Invalid bearing configuration type: ${input.configurationType}`
    );
  }

  // Validate diameter for round bearing areas
  if (input.diameter !== undefined) {
    if (typeof input.diameter !== "number" || !isFinite(input.diameter)) {
      errors.push("Diameter must be a finite number");
    } else if (input.diameter <= 0) {
      errors.push("Diameter must be positive");
    } else if (input.configurationType === BEARING_CONFIGURATION_TYPES.WASHER) {
      // For washers, bearing length should equal diameter
      if (Math.abs(input.bearingLength - input.diameter) > 0.001) {
        warnings.push(
          "For round bearing areas (washers), bearing length should equal diameter"
        );
      }
    }
  }

  // Check if bearing length exceeds tabulated values
  if (
    input.bearingLength > BEARING_AREA_FACTOR_CONSTANTS.TABULATED_MAX_LENGTH
  ) {
    warnings.push(
      `Bearing length (${input.bearingLength}") exceeds tabulated values - using formula calculation`
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates bearing at angle input parameters
 *
 * @param input - Bearing at angle input parameters
 * @returns Validation result with errors and warnings
 */
export function validateBearingAtAngleInput(
  input: BearingAtAngleInput
): BearingAreaFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate bearing area factor input first
  const bearingAreaValidation = validateBearingAreaFactorInput({
    bearingLength: input.bearingLength,
    configurationType: input.configurationType,
    diameter: input.diameter,
    useTabulated: input.useTabulated,
  });

  errors.push(...bearingAreaValidation.errors);
  warnings.push(...bearingAreaValidation.warnings);

  // Validate angle to grain
  if (typeof input.angleToGrain !== "number" || !isFinite(input.angleToGrain)) {
    errors.push("Angle to grain must be a finite number");
  } else if (
    input.angleToGrain < BEARING_AREA_FACTOR_CONSTANTS.MIN_ANGLE_DEGREES ||
    input.angleToGrain > BEARING_AREA_FACTOR_CONSTANTS.MAX_ANGLE_DEGREES
  ) {
    errors.push(
      `Angle to grain must be between ${BEARING_AREA_FACTOR_CONSTANTS.MIN_ANGLE_DEGREES}° and ${BEARING_AREA_FACTOR_CONSTANTS.MAX_ANGLE_DEGREES}°`
    );
  }

  // Validate design values
  if (typeof input.fcParallel !== "number" || !isFinite(input.fcParallel)) {
    errors.push("Fc parallel must be a finite number");
  } else if (input.fcParallel <= 0) {
    errors.push("Fc parallel must be positive");
  }

  if (
    typeof input.fcPerpendicularAdjusted !== "number" ||
    !isFinite(input.fcPerpendicularAdjusted)
  ) {
    errors.push("Fc perpendicular adjusted must be a finite number");
  } else if (input.fcPerpendicularAdjusted <= 0) {
    errors.push("Fc perpendicular adjusted must be positive");
  }

  // Validate relationship between parallel and perpendicular values
  if (
    input.fcParallel > 0 &&
    input.fcPerpendicularAdjusted > 0 &&
    input.fcPerpendicularAdjusted > input.fcParallel
  ) {
    warnings.push(
      "Fc perpendicular is greater than Fc parallel - verify design values"
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Gets the effective bearing length for calculation
 * For round bearing areas (washers), uses diameter
 *
 * @param input - Bearing area factor input
 * @returns Effective bearing length in inches
 */
export function getEffectiveBearingLength(
  input: BearingAreaFactorInput
): number {
  if (
    input.configurationType === BEARING_CONFIGURATION_TYPES.WASHER &&
    input.diameter
  ) {
    return input.diameter;
  }
  return input.bearingLength;
}

/**
 * Calculates bearing area factor using the formula from NDS Equation 3.10-2
 *
 * @param bearingLength - Bearing length parallel to grain, inches
 * @returns Bearing area factor Cb
 *
 * @example
 * ```typescript
 * const cb = calculateBearingAreaFactorFormula(2.0);
 * // Returns: 1.1875
 * ```
 */
export function calculateBearingAreaFactorFormula(
  bearingLength: number
): number {
  if (bearingLength <= 0) {
    return BEARING_AREA_FACTOR_CONSTANTS.DEFAULT_BEARING_AREA_FACTOR;
  }

  // NDS Equation 3.10-2: Cb = (ℓb + 0.375) / ℓb
  const cb =
    (bearingLength + BEARING_AREA_FACTOR_CONSTANTS.BEARING_LENGTH_CONSTANT) /
    bearingLength;

  // Bearing area factor cannot be less than 1.0
  return Math.max(cb, BEARING_AREA_FACTOR_CONSTANTS.MIN_BEARING_AREA_FACTOR);
}

/**
 * Gets bearing area factor from tabulated values (NDS Table 3.10.4)
 * Uses linear interpolation for values between tabulated points
 *
 * @param bearingLength - Bearing length parallel to grain, inches
 * @returns Bearing area factor from table or null if outside range
 */
export function getBearingAreaFactorFromTable(
  bearingLength: number
): number | null {
  const tableKeys = Object.keys(BEARING_AREA_FACTORS_TABLE)
    .map(Number)
    .sort((a, b) => a - b);

  // Check if exact match exists
  if (bearingLength in BEARING_AREA_FACTORS_TABLE) {
    return BEARING_AREA_FACTORS_TABLE[
      bearingLength as keyof typeof BEARING_AREA_FACTORS_TABLE
    ];
  }

  // For values outside tabulated range, return null to allow fallback to formula
  if (
    bearingLength > Math.max(...tableKeys) ||
    bearingLength < Math.min(...tableKeys)
  ) {
    return null;
  }

  // Linear interpolation between tabulated values
  for (let i = 0; i < tableKeys.length - 1; i++) {
    const lowerKey = tableKeys[i];
    const upperKey = tableKeys[i + 1];

    if (bearingLength >= lowerKey && bearingLength <= upperKey) {
      const lowerValue =
        BEARING_AREA_FACTORS_TABLE[
          lowerKey as keyof typeof BEARING_AREA_FACTORS_TABLE
        ];
      const upperValue =
        BEARING_AREA_FACTORS_TABLE[
          upperKey as keyof typeof BEARING_AREA_FACTORS_TABLE
        ];

      const ratio = (bearingLength - lowerKey) / (upperKey - lowerKey);
      return lowerValue + ratio * (upperValue - lowerValue);
    }
  }

  return null;
}

/**
 * Calculates bearing area factor (Cb) based on NDS 3.10.4
 *
 * @param input - Bearing area factor input parameters
 * @returns Bearing area factor value
 *
 * @example
 * ```typescript
 * const cb = getBearingAreaFactor({
 *   bearingLength: 2.0,
 *   configurationType: 'plate'
 * });
 * ```
 */
export function getBearingAreaFactor(input: BearingAreaFactorInput): number {
  const effectiveLength = getEffectiveBearingLength(input);

  // Use tabulated values if explicitly requested and available
  if (input.useTabulated === true) {
    const tabulatedValue = getBearingAreaFactorFromTable(effectiveLength);
    if (tabulatedValue !== null) {
      return tabulatedValue;
    }
  }

  // Use formula calculation (default behavior)
  return calculateBearingAreaFactorFormula(effectiveLength);
}

/**
 * Applies bearing area factor to a reference compression perpendicular design value
 *
 * @param referenceValue - Reference Fc⊥ value, psi
 * @param input - Bearing area factor input parameters
 * @returns Adjusted design value, psi
 *
 * @example
 * ```typescript
 * const fcPerpAdjusted = applyBearingAreaFactor(625, {
 *   bearingLength: 1.5
 * });
 * ```
 */
export function applyBearingAreaFactor(
  referenceValue: number,
  input: BearingAreaFactorInput
): number {
  if (referenceValue <= 0) {
    throw new Error("Reference value must be positive");
  }

  const cb = getBearingAreaFactor(input);
  return referenceValue * cb;
}

/**
 * Calculates adjusted bearing design value at an angle to grain using NDS Equation 3.10-1
 *
 * @param input - Bearing at angle input parameters
 * @returns Adjusted bearing design value, psi
 *
 * @example
 * ```typescript
 * const fcPrime = calculateBearingAtAngle({
 *   bearingLength: 2.0,
 *   angleToGrain: 30,
 *   fcParallel: 1600,
 *   fcPerpendicularAdjusted: 780
 * });
 * ```
 */
export function calculateBearingAtAngle(input: BearingAtAngleInput): number {
  // Convert angle to radians
  const angleRad = (input.angleToGrain * Math.PI) / 180;

  // Calculate trigonometric values
  const sinSquaredTheta = Math.sin(angleRad) ** 2;
  const cosSquaredTheta = Math.cos(angleRad) ** 2;

  // NDS Equation 3.10-1: Fc' = (Fc * Fc⊥') / (Fc * sin²θ + Fc⊥' * cos²θ)
  const numerator = input.fcParallel * input.fcPerpendicularAdjusted;
  const denominator =
    input.fcParallel * sinSquaredTheta +
    input.fcPerpendicularAdjusted * cosSquaredTheta;

  if (denominator === 0) {
    throw new Error("Division by zero in bearing at angle calculation");
  }

  return numerator / denominator;
}

/**
 * Performs comprehensive bearing area factor analysis
 *
 * @param input - Bearing area factor input parameters
 * @returns Detailed analysis results
 */
export function getBearingAreaFactorAnalysis(
  input: BearingAreaFactorInput
): BearingAreaFactorAnalysis {
  const validation = validateBearingAreaFactorInput(input);
  const effectiveLength = getEffectiveBearingLength(input);
  const recommendations: string[] = [];

  // Determine calculation method - use same logic as getBearingAreaFactor
  let calculationMethod: "formula" | "tabulated" | "minimum";
  let bearingAreaFactor: number;

  if (input.useTabulated === true) {
    const tabulatedValue = getBearingAreaFactorFromTable(effectiveLength);
    if (tabulatedValue !== null) {
      bearingAreaFactor = tabulatedValue;
      calculationMethod = "tabulated";
    } else {
      bearingAreaFactor = calculateBearingAreaFactorFormula(effectiveLength);
      calculationMethod = "formula";
    }
  } else {
    bearingAreaFactor = calculateBearingAreaFactorFormula(effectiveLength);
    calculationMethod = "formula";
  }

  // Check for minimum factor
  if (
    bearingAreaFactor ===
      BEARING_AREA_FACTOR_CONSTANTS.MIN_BEARING_AREA_FACTOR &&
    effectiveLength >= BEARING_AREA_FACTOR_CONSTANTS.TABULATED_MAX_LENGTH
  ) {
    calculationMethod = "minimum";
  }

  // Generate recommendations
  if (effectiveLength < 1.0) {
    recommendations.push(
      "Consider larger bearing area for better load distribution"
    );
  }

  if (
    input.configurationType === BEARING_CONFIGURATION_TYPES.WASHER &&
    !input.diameter
  ) {
    recommendations.push(
      "Specify washer diameter for accurate bearing length calculation"
    );
  }

  if (bearingAreaFactor > 1.5) {
    recommendations.push(
      "High bearing area factor - verify that small bearing area is appropriate"
    );
  }

  return {
    input,
    bearingAreaFactor,
    calculationMethod,
    effectiveBearingLength: effectiveLength,
    validation,
    recommendations,
  };
}

/**
 * Performs comprehensive bearing at angle analysis
 *
 * @param input - Bearing at angle input parameters
 * @returns Detailed analysis results
 */
export function getBearingAtAngleAnalysis(
  input: BearingAtAngleInput
): BearingAtAngleAnalysis {
  const validation = validateBearingAtAngleInput(input);

  // Get bearing area factor analysis
  const bearingAreaFactorAnalysis = getBearingAreaFactorAnalysis({
    bearingLength: input.bearingLength,
    configurationType: input.configurationType,
    diameter: input.diameter,
    useTabulated: input.useTabulated,
  });

  const recommendations: string[] = [
    ...bearingAreaFactorAnalysis.recommendations,
  ];

  // Convert angle to radians and calculate trig values
  const angleRad = (input.angleToGrain * Math.PI) / 180;
  const sinSquaredTheta = Math.sin(angleRad) ** 2;
  const cosSquaredTheta = Math.cos(angleRad) ** 2;

  // Calculate adjusted bearing value
  let adjustedBearingValue = 0;
  if (validation.isValid) {
    adjustedBearingValue = calculateBearingAtAngle(input);
  }

  // Generate additional recommendations
  if (input.angleToGrain === 0) {
    recommendations.push("Load is parallel to grain - use Fc design values");
  } else if (input.angleToGrain === 90) {
    recommendations.push(
      "Load is perpendicular to grain - bearing area factor applies"
    );
  } else {
    recommendations.push(
      "Load at angle to grain - verify load path and bearing configuration"
    );
  }

  if (adjustedBearingValue > input.fcParallel && input.angleToGrain < 90) {
    recommendations.push(
      "Adjusted bearing value exceeds Fc parallel - verify calculation"
    );
  }

  return {
    input,
    adjustedBearingValue,
    bearingAreaFactor: bearingAreaFactorAnalysis.bearingAreaFactor,
    bearingAreaFactorAnalysis,
    angleInRadians: angleRad,
    sinSquaredTheta,
    cosSquaredTheta,
    validation,
    recommendations,
  };
}

/**
 * Calculates multiple bearing area factors for different bearing lengths
 *
 * @param bearingLengths - Array of bearing lengths to calculate
 * @param baseInput - Base input parameters (excluding bearing length)
 * @returns Array of bearing area factor results
 */
export function getMultipleBearingAreaFactors(
  bearingLengths: number[],
  baseInput: Omit<BearingAreaFactorInput, "bearingLength">
): BearingAreaFactorAnalysis[] {
  return bearingLengths.map((length) =>
    getBearingAreaFactorAnalysis({
      ...baseInput,
      bearingLength: length,
    })
  );
}

/**
 * Determines if bearing area factor applies to a design value type
 * Only applies to compression perpendicular to grain (Fc⊥)
 *
 * @param designValueType - Type of design value
 * @returns True if bearing area factor applies
 */
export function isDesignValueAffectedByBearingAreaFactor(
  designValueType: DesignValueType
): boolean {
  return designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR;
}

/**
 * Gets bearing area factor optimization recommendations
 *
 * @param targetFactor - Target bearing area factor
 * @param currentLength - Current bearing length
 * @returns Optimized bearing length and recommendations
 */
export function getBearingAreaFactorOptimization(
  targetFactor: number,
  currentLength: number
): {
  optimizedLength: number;
  achievedFactor: number;
  recommendations: string[];
} {
  const recommendations: string[] = [];

  if (targetFactor <= BEARING_AREA_FACTOR_CONSTANTS.MIN_BEARING_AREA_FACTOR) {
    return {
      optimizedLength: BEARING_AREA_FACTOR_CONSTANTS.TABULATED_MAX_LENGTH,
      achievedFactor: BEARING_AREA_FACTOR_CONSTANTS.MIN_BEARING_AREA_FACTOR,
      recommendations: [
        "Target factor is minimum - use bearing length ≥ 6 inches",
      ],
    };
  }

  // For very high target factors, return current length with warning
  if (targetFactor >= 10) {
    recommendations.push(
      "Target factor too high for practical bearing lengths"
    );
    return {
      optimizedLength: currentLength,
      achievedFactor: calculateBearingAreaFactorFormula(currentLength),
      recommendations,
    };
  }

  // Solve for required bearing length using formula: ℓb = 0.375 / (Cb - 1)
  const requiredLength =
    BEARING_AREA_FACTOR_CONSTANTS.BEARING_LENGTH_CONSTANT / (targetFactor - 1);

  // Check if achievable
  if (requiredLength <= 0) {
    recommendations.push(
      "Target factor too high for practical bearing lengths"
    );
    return {
      optimizedLength: currentLength,
      achievedFactor: calculateBearingAreaFactorFormula(currentLength),
      recommendations,
    };
  }

  const achievedFactor = calculateBearingAreaFactorFormula(requiredLength);

  if (requiredLength < 0.5) {
    recommendations.push(
      "Very small bearing length required - consider design alternatives"
    );
  }

  if (requiredLength > 6) {
    recommendations.push(
      "Large bearing length - bearing area factor approaches 1.0"
    );
  }

  return {
    optimizedLength: requiredLength,
    achievedFactor,
    recommendations,
  };
}
