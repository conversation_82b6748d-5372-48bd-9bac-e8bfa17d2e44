# Bearing Area Factor (Cb) Module

## Overview

This module implements **NDS 3.10.3 Bearing at an Angle to Grain** and **NDS 3.10.4 Bearing Area Factor** calculations for adjusting reference compression design values perpendicular to grain based on bearing length and load angle.

## Key Features

- **Bearing Area Factor (Cb)** calculation using NDS Equation 3.10-2
- **Bearing at Angle to Grain** calculation using NDS Equation 3.10-1
- Support for both tabulated values (NDS Table 3.10.4) and formula calculations
- Special handling for round bearing areas (washers)
- Comprehensive validation and analysis functions
- Optimization recommendations for bearing design

## NDS References

- **NDS 3.10.3**: Bearing at an Angle to Grain
- **NDS 3.10.4**: Bearing Area Factor, Cb
- **NDS Table 3.10.4**: Bearing Area Factors for various bearing lengths
- **NDS Figure 31**: Bearing at an Angle to Grain illustration
- **NDS Equation 3.10-1**: Fc' = (Fc × Fc⊥') / (Fc sin²θ + Fc⊥' cos²θ)
- **NDS Equation 3.10-2**: Cb = (ℓb + 0.375) / ℓb

## Key Equations

### Bearing Area Factor (NDS Equation 3.10-2)
```
Cb = (ℓb + 0.375) / ℓb
```
Where:
- `Cb` = bearing area factor
- `ℓb` = bearing length measured parallel to grain (inches)

### Bearing at Angle to Grain (NDS Equation 3.10-1)
```
Fc' = (Fc × Fc⊥') / (Fc sin²θ + Fc⊥' cos²θ)
```
Where:
- `Fc'` = adjusted bearing design value at angle θ
- `Fc` = reference compression design value parallel to grain
- `Fc⊥'` = adjusted compression design value perpendicular to grain
- `θ` = angle between direction of load and direction of grain

## Usage Examples

### Basic Bearing Area Factor Calculation

```typescript
import { getBearingAreaFactor, applyBearingAreaFactor } from './bearing-area-factor';

// Calculate bearing area factor for a 2" bearing plate
const input = {
  bearingLength: 2.0,
  configurationType: 'plate'
};

const cb = getBearingAreaFactor(input);
console.log(`Bearing area factor: ${cb}`); // 1.1875

// Apply to reference Fc⊥ value
const referenceFcPerp = 625; // psi
const adjustedFcPerp = applyBearingAreaFactor(referenceFcPerp, input);
console.log(`Adjusted Fc⊥: ${adjustedFcPerp} psi`); // 742.2 psi
```

### Using Tabulated Values

```typescript
import { getBearingAreaFactor } from './bearing-area-factor';

// Use NDS Table 3.10.4 values when available
const input = {
  bearingLength: 1.0,
  useTabulated: true
};

const cb = getBearingAreaFactor(input);
console.log(`Tabulated Cb: ${cb}`); // 1.38 (exact from table)
```

### Round Bearing Areas (Washers)

```typescript
import { getBearingAreaFactorAnalysis } from './bearing-area-factor';

// For round washers, bearing length equals diameter
const input = {
  bearingLength: 1.0,
  configurationType: 'washer',
  diameter: 1.0,
  useTabulated: true
};

const analysis = getBearingAreaFactorAnalysis(input);
console.log(`Washer Cb: ${analysis.bearingAreaFactor}`); // 1.38
console.log(`Method: ${analysis.calculationMethod}`); // 'tabulated'
```

### Bearing at Angle to Grain

```typescript
import { calculateBearingAtAngle, getBearingAtAngleAnalysis } from './bearing-area-factor';

// Load at 30° to grain
const input = {
  bearingLength: 2.0,
  angleToGrain: 30,
  fcParallel: 1600,          // psi
  fcPerpendicularAdjusted: 780, // psi (already includes Cb)
};

const fcPrime = calculateBearingAtAngle(input);
console.log(`Adjusted bearing value: ${fcPrime} psi`);

// Comprehensive analysis
const analysis = getBearingAtAngleAnalysis(input);
console.log(`Bearing area factor: ${analysis.bearingAreaFactor}`);
console.log(`Recommendations: ${analysis.recommendations.join(', ')}`);
```

### Multiple Bearing Lengths Comparison

```typescript
import { getMultipleBearingAreaFactors } from './bearing-area-factor';

// Compare different bearing lengths
const bearingLengths = [0.5, 1.0, 2.0, 4.0, 6.0];
const baseInput = {
  configurationType: 'plate',
  useTabulated: true
};

const results = getMultipleBearingAreaFactors(bearingLengths, baseInput);

results.forEach(result => {
  console.log(`Length: ${result.input.bearingLength}", Cb: ${result.bearingAreaFactor}`);
});
// Length: 0.5", Cb: 1.75
// Length: 1.0", Cb: 1.38
// Length: 2.0", Cb: 1.19
// Length: 4.0", Cb: 1.10
// Length: 6.0", Cb: 1.00
```

### Optimization for Target Factor

```typescript
import { getBearingAreaFactorOptimization } from './bearing-area-factor';

// Find bearing length needed for Cb = 1.25
const result = getBearingAreaFactorOptimization(1.25, 2.0);

console.log(`Required length: ${result.optimizedLength}" `); // 1.5"
console.log(`Achieved factor: ${result.achievedFactor}`); // 1.25
console.log(`Recommendations: ${result.recommendations.join(', ')}`);
```

## API Reference

### Constants

#### BEARING_AREA_FACTORS_TABLE
Tabulated values from NDS Table 3.10.4:
```typescript
{
  0.5: 1.75,
  1.0: 1.38,
  1.5: 1.25,
  2.0: 1.19,
  3.0: 1.13,
  4.0: 1.10,
  6.0: 1.00
}
```

#### BEARING_AREA_FACTOR_CONSTANTS
Key constants for calculations:
- `BEARING_LENGTH_CONSTANT`: 0.375 (inches)
- `MIN_BEARING_AREA_FACTOR`: 1.0
- `TABULATED_MAX_LENGTH`: 6.0 (inches)

#### BEARING_CONFIGURATION_TYPES
Supported bearing configurations:
- `PLATE`: Bearing plates
- `WASHER`: Round washers
- `BOLT`: Bolt bearing
- `POST`: Post on sill
- `BEAM`: Beam on support
- `GENERAL`: General bearing condition

### Core Functions

#### `getBearingAreaFactor(input: BearingAreaFactorInput): number`
Calculates bearing area factor using either tabulated values or formula.

#### `applyBearingAreaFactor(referenceValue: number, input: BearingAreaFactorInput): number`
Applies bearing area factor to a reference Fc⊥ value.

#### `calculateBearingAtAngle(input: BearingAtAngleInput): number`
Calculates adjusted bearing design value at an angle to grain using NDS Equation 3.10-1.

### Analysis Functions

#### `getBearingAreaFactorAnalysis(input: BearingAreaFactorInput): BearingAreaFactorAnalysis`
Provides comprehensive analysis including:
- Calculated bearing area factor
- Calculation method used
- Validation results
- Design recommendations

#### `getBearingAtAngleAnalysis(input: BearingAtAngleInput): BearingAtAngleAnalysis`
Provides comprehensive analysis for angled bearing including:
- Adjusted bearing value
- Trigonometric calculations
- Combined bearing area factor analysis
- Angle-specific recommendations

### Validation Functions

#### `validateBearingAreaFactorInput(input: BearingAreaFactorInput): BearingAreaFactorValidation`
Validates input parameters for bearing area factor calculations.

#### `validateBearingAtAngleInput(input: BearingAtAngleInput): BearingAreaFactorValidation`
Validates input parameters for bearing at angle calculations.

### Utility Functions

#### `getMultipleBearingAreaFactors(bearingLengths: number[], baseInput: Omit<BearingAreaFactorInput, 'bearingLength'>): BearingAreaFactorAnalysis[]`
Calculates bearing area factors for multiple bearing lengths.

#### `getBearingAreaFactorOptimization(targetFactor: number, currentLength: number): OptimizationResult`
Determines optimal bearing length to achieve a target bearing area factor.

#### `isDesignValueAffectedByBearingAreaFactor(designValueType: DesignValueType): boolean`
Returns true only for compression perpendicular to grain (Fc⊥).

## Input Types

### BearingAreaFactorInput
```typescript
{
  bearingLength: number;              // inches, parallel to grain
  configurationType?: string;         // 'plate', 'washer', etc.
  diameter?: number;                  // inches, for round bearing areas
  useTabulated?: boolean;             // use NDS table when available
}
```

### BearingAtAngleInput
```typescript
{
  bearingLength: number;              // inches, parallel to grain
  angleToGrain: number;               // degrees (0-90)
  fcParallel: number;                 // psi, reference Fc
  fcPerpendicularAdjusted: number;    // psi, adjusted Fc⊥'
  configurationType?: string;         // bearing configuration type
  diameter?: number;                  // inches, for round areas
  useTabulated?: boolean;             // use NDS table when available
}
```

## Design Recommendations

### Bearing Length Selection
- **Small bearing areas** (ℓb < 1"): High bearing area factors (Cb > 1.3) - consider larger bearing area
- **Medium bearing areas** (1" ≤ ℓb ≤ 6"): Use tabulated values for accuracy
- **Large bearing areas** (ℓb > 6"): Bearing area factor approaches 1.0

### Round Bearing Areas
- For washers and round plates, bearing length (ℓb) equals diameter
- Specify diameter explicitly for accurate calculations
- Consider load distribution patterns

### Angled Loading
- **0° (parallel to grain)**: Use Fc design values directly
- **90° (perpendicular to grain)**: Apply bearing area factor to Fc⊥
- **Intermediate angles**: Use NDS Equation 3.10-1 for interpolation

## Validation Rules

### Input Validation
- Bearing length must be positive and finite
- Angle to grain must be between 0° and 90°
- Design values (Fc, Fc⊥') must be positive
- Configuration type must be from supported list

### Design Checks
- Warns if bearing length is very small (< 0.1")
- Warns if bearing length exceeds tabulated values (> 6")
- Warns if Fc⊥ > Fc (unusual condition)
- Recommends washer diameter specification when missing

## Error Handling

The module includes comprehensive error handling:
- **Input validation**: Catches invalid parameters before calculation
- **Division by zero**: Protects against mathematical errors
- **Range checking**: Ensures values are within practical limits
- **Warning system**: Alerts to potentially problematic conditions

## Testing

The module includes extensive unit tests covering:
- All calculation functions
- Edge cases and boundary conditions
- Validation logic
- Integration scenarios
- Error conditions

Run tests with:
```bash
npm test bearing-area-factor.test.ts
```

## Performance Notes

- Tabulated value lookup: O(1) for exact matches, O(log n) for interpolation
- Formula calculation: O(1) constant time
- Validation: O(1) constant time
- Multiple bearing lengths: O(n) linear in number of lengths

## Related Modules

This module works with other adjustment factor modules:
- **Wet Service Factor**: May affect Fc⊥ before bearing area factor application
- **Temperature Factor**: May affect design values
- **Load Duration Factor**: Does not apply to Fc⊥ in deformation-controlled cases
- **Size Factor**: May affect reference design values 