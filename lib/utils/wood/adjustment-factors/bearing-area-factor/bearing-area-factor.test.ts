/**
 * Tests for Bearing Area Factor (Cb) Calculations
 *
 * @fileoverview Comprehensive test suite for bearing area factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import { describe, test, expect } from "@jest/globals";
import {
  // Constants
  BEARING_AREA_FACTORS_TABLE,
  BEARING_AREA_FACTOR_CONSTANTS,
  BEARING_CONFIGURATION_TYPES,

  // Types
  type BearingAreaFactorInput,
  type BearingAtAngleInput,
  type BearingAreaFactorValidation,
  type BearingAreaFactorAnalysis,
  type BearingAtAngleAnalysis,
  type BearingConfigurationType,

  // Functions
  validateBearingAreaFactorInput,
  validateBearingAtAngleInput,
  getEffectiveBearingLength,
  calculateBearingAreaFactorFormula,
  getBearingAreaFactorFromTable,
  getBearingAreaFactor,
  applyBearingAreaFactor,
  calculateBearingAtAngle,
  getBearingAreaFactorAnalysis,
  getBearingAtAngleAnalysis,
  getMultipleBearingAreaFactors,
  isDesignValueAffectedByBearingAreaFactor,
  getBearingAreaFactorOptimization,
} from "./bearing-area-factor";

import { DESIGN_VALUE_TYPES } from "../../constants";

describe("Bearing Area Factor Constants", () => {
  test("should have correct tabulated values from NDS Table 3.10.4", () => {
    expect(BEARING_AREA_FACTORS_TABLE[0.5]).toBe(1.75);
    expect(BEARING_AREA_FACTORS_TABLE[1.0]).toBe(1.38);
    expect(BEARING_AREA_FACTORS_TABLE[1.5]).toBe(1.25);
    expect(BEARING_AREA_FACTORS_TABLE[2.0]).toBe(1.19);
    expect(BEARING_AREA_FACTORS_TABLE[3.0]).toBe(1.13);
    expect(BEARING_AREA_FACTORS_TABLE[4.0]).toBe(1.1);
    expect(BEARING_AREA_FACTORS_TABLE[6.0]).toBe(1.0);
  });

  test("should have correct formula constant from NDS Equation 3.10-2", () => {
    expect(BEARING_AREA_FACTOR_CONSTANTS.BEARING_LENGTH_CONSTANT).toBe(0.375);
  });

  test("should have correct minimum bearing area factor", () => {
    expect(BEARING_AREA_FACTOR_CONSTANTS.MIN_BEARING_AREA_FACTOR).toBe(1.0);
  });

  test("should have correct angle limitations", () => {
    expect(BEARING_AREA_FACTOR_CONSTANTS.MIN_ANGLE_DEGREES).toBe(0);
    expect(BEARING_AREA_FACTOR_CONSTANTS.MAX_ANGLE_DEGREES).toBe(90);
  });

  test("should have correct bearing configuration types", () => {
    expect(BEARING_CONFIGURATION_TYPES.PLATE).toBe("plate");
    expect(BEARING_CONFIGURATION_TYPES.WASHER).toBe("washer");
    expect(BEARING_CONFIGURATION_TYPES.BOLT).toBe("bolt");
    expect(BEARING_CONFIGURATION_TYPES.POST).toBe("post");
    expect(BEARING_CONFIGURATION_TYPES.BEAM).toBe("beam");
    expect(BEARING_CONFIGURATION_TYPES.GENERAL).toBe("general");
  });
});

describe("validateBearingAreaFactorInput", () => {
  const validInput: BearingAreaFactorInput = {
    bearingLength: 2.0,
    configurationType: BEARING_CONFIGURATION_TYPES.PLATE,
  };

  test("should validate correct input", () => {
    const result = validateBearingAreaFactorInput(validInput);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test("should reject invalid bearing length values", () => {
    const invalidInputs = [
      { ...validInput, bearingLength: 0 },
      { ...validInput, bearingLength: -1 },
      { ...validInput, bearingLength: NaN },
      { ...validInput, bearingLength: Infinity },
      { ...validInput, bearingLength: -Infinity },
    ];

    invalidInputs.forEach((input) => {
      const result = validateBearingAreaFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test("should reject non-numeric bearing length", () => {
    const result = validateBearingAreaFactorInput({
      ...validInput,
      bearingLength: "invalid" as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Bearing length must be a finite number");
  });

  test("should warn about very small bearing lengths", () => {
    const result = validateBearingAreaFactorInput({
      ...validInput,
      bearingLength: 0.05,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings.length).toBeGreaterThan(0);
    expect(result.warnings[0]).toContain("very small");
  });

  test("should reject invalid configuration types", () => {
    const result = validateBearingAreaFactorInput({
      ...validInput,
      configurationType: "invalid" as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain(
      "Invalid bearing configuration type: invalid"
    );
  });

  test("should validate diameter for round bearing areas", () => {
    const validWasherInput = {
      ...validInput,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
      diameter: 2.0,
    };

    const result = validateBearingAreaFactorInput(validWasherInput);
    expect(result.isValid).toBe(true);
  });

  test("should reject invalid diameter values", () => {
    const invalidInputs = [
      { ...validInput, diameter: 0 },
      { ...validInput, diameter: -1 },
      { ...validInput, diameter: NaN },
      { ...validInput, diameter: Infinity },
    ];

    invalidInputs.forEach((input) => {
      const result = validateBearingAreaFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test("should warn when washer diameter does not match bearing length", () => {
    const result = validateBearingAreaFactorInput({
      ...validInput,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
      bearingLength: 2.0,
      diameter: 1.5,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain(
      "For round bearing areas (washers), bearing length should equal diameter"
    );
  });

  test("should warn about bearing lengths exceeding tabulated values", () => {
    const result = validateBearingAreaFactorInput({
      ...validInput,
      bearingLength: 10.0,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain(
      'Bearing length (10") exceeds tabulated values - using formula calculation'
    );
  });
});

describe("validateBearingAtAngleInput", () => {
  const validInput: BearingAtAngleInput = {
    bearingLength: 2.0,
    angleToGrain: 45,
    fcParallel: 1600,
    fcPerpendicularAdjusted: 780,
  };

  test("should validate correct input", () => {
    const result = validateBearingAtAngleInput(validInput);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test("should reject invalid angle values", () => {
    const invalidInputs = [
      { ...validInput, angleToGrain: -1 },
      { ...validInput, angleToGrain: 91 },
      { ...validInput, angleToGrain: NaN },
      { ...validInput, angleToGrain: Infinity },
    ];

    invalidInputs.forEach((input) => {
      const result = validateBearingAtAngleInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test("should reject invalid design values", () => {
    const invalidInputs = [
      { ...validInput, fcParallel: 0 },
      { ...validInput, fcParallel: -1600 },
      { ...validInput, fcParallel: NaN },
      { ...validInput, fcPerpendicularAdjusted: 0 },
      { ...validInput, fcPerpendicularAdjusted: -780 },
      { ...validInput, fcPerpendicularAdjusted: NaN },
    ];

    invalidInputs.forEach((input) => {
      const result = validateBearingAtAngleInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test("should warn when Fc perpendicular exceeds Fc parallel", () => {
    const result = validateBearingAtAngleInput({
      ...validInput,
      fcParallel: 1000,
      fcPerpendicularAdjusted: 1200,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain(
      "Fc perpendicular is greater than Fc parallel - verify design values"
    );
  });

  test("should accept boundary angle values", () => {
    const parallelResult = validateBearingAtAngleInput({
      ...validInput,
      angleToGrain: 0,
    });
    expect(parallelResult.isValid).toBe(true);

    const perpendicularResult = validateBearingAtAngleInput({
      ...validInput,
      angleToGrain: 90,
    });
    expect(perpendicularResult.isValid).toBe(true);
  });
});

describe("getEffectiveBearingLength", () => {
  test("should return bearing length for non-washer configurations", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      configurationType: BEARING_CONFIGURATION_TYPES.PLATE,
    };

    expect(getEffectiveBearingLength(input)).toBe(2.0);
  });

  test("should return diameter for washer configurations", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
      diameter: 1.5,
    };

    expect(getEffectiveBearingLength(input)).toBe(1.5);
  });

  test("should return bearing length for washer without diameter", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
    };

    expect(getEffectiveBearingLength(input)).toBe(2.0);
  });
});

describe("calculateBearingAreaFactorFormula", () => {
  test("should calculate using NDS Equation 3.10-2", () => {
    // Cb = (ℓb + 0.375) / ℓb
    expect(calculateBearingAreaFactorFormula(1.0)).toBeCloseTo(1.375, 3);
    expect(calculateBearingAreaFactorFormula(2.0)).toBeCloseTo(1.1875, 4);
    expect(calculateBearingAreaFactorFormula(4.0)).toBeCloseTo(1.09375, 5);
  });

  test("should return minimum factor for zero or negative lengths", () => {
    expect(calculateBearingAreaFactorFormula(0)).toBe(1.0);
    expect(calculateBearingAreaFactorFormula(-1)).toBe(1.0);
  });

  test("should approach 1.0 for large bearing lengths", () => {
    expect(calculateBearingAreaFactorFormula(100)).toBeCloseTo(1.00375, 5);
    expect(calculateBearingAreaFactorFormula(1000)).toBeCloseTo(1.000375, 6);
  });

  test("should match tabulated values within acceptable tolerance", () => {
    // Verify formula matches NDS tabulated values
    expect(calculateBearingAreaFactorFormula(0.5)).toBeCloseTo(1.75, 2);
    expect(calculateBearingAreaFactorFormula(1.0)).toBeCloseTo(1.375, 3);
    expect(calculateBearingAreaFactorFormula(2.0)).toBeCloseTo(1.1875, 4);
    expect(calculateBearingAreaFactorFormula(6.0)).toBeCloseTo(1.0625, 4);
  });

  test("should never return value less than 1.0", () => {
    const testLengths = [0.1, 0.5, 1.0, 2.0, 10.0, 100.0];
    testLengths.forEach((length) => {
      expect(calculateBearingAreaFactorFormula(length)).toBeGreaterThanOrEqual(
        1.0
      );
    });
  });
});

describe("getBearingAreaFactorFromTable", () => {
  test("should return exact tabulated values", () => {
    expect(getBearingAreaFactorFromTable(0.5)).toBe(1.75);
    expect(getBearingAreaFactorFromTable(1.0)).toBe(1.38);
    expect(getBearingAreaFactorFromTable(2.0)).toBe(1.19);
    expect(getBearingAreaFactorFromTable(6.0)).toBe(1.0);
  });

  test("should interpolate between tabulated values", () => {
    // Test interpolation between 1.0 (1.38) and 1.5 (1.25)
    const interpolated = getBearingAreaFactorFromTable(1.25);
    expect(interpolated).toBeCloseTo(1.315, 3); // Linear interpolation
  });

  test("should return null for values outside tabulated range", () => {
    expect(getBearingAreaFactorFromTable(10.0)).toBeNull();
    expect(getBearingAreaFactorFromTable(100.0)).toBeNull();
    expect(getBearingAreaFactorFromTable(0.1)).toBeNull();
    expect(getBearingAreaFactorFromTable(0.25)).toBeNull();
  });

  test("should handle edge case interpolations", () => {
    // Test interpolation at boundaries
    const result = getBearingAreaFactorFromTable(5.99);
    expect(result).toBeCloseTo(1.001, 3);
  });
});

describe("getBearingAreaFactor", () => {
  test("should use tabulated values when available and requested", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      useTabulated: true,
    };

    expect(getBearingAreaFactor(input)).toBe(1.19);
  });

  test("should use formula when tabulated values are disabled", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      useTabulated: false,
    };

    expect(getBearingAreaFactor(input)).toBeCloseTo(1.1875, 4);
  });

  test("should use formula for values outside tabulated range", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 10.0,
      useTabulated: true,
    };

    // For values outside tabulated range, should fall back to formula calculation
    expect(getBearingAreaFactor(input)).toBeCloseTo(1.0375, 4);
  });

  test("should handle washer configurations", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
      diameter: 1.0,
      useTabulated: true,
    };

    expect(getBearingAreaFactor(input)).toBe(1.38);
  });
});

describe("applyBearingAreaFactor", () => {
  test("should apply bearing area factor to reference value", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      useTabulated: false, // Force formula calculation for predictable result
    };

    const adjusted = applyBearingAreaFactor(625, input);
    expect(adjusted).toBeCloseTo(625 * 1.1875, 2);
  });

  test("should throw error for non-positive reference values", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
    };

    expect(() => applyBearingAreaFactor(0, input)).toThrow(
      "Reference value must be positive"
    );
    expect(() => applyBearingAreaFactor(-625, input)).toThrow(
      "Reference value must be positive"
    );
  });

  test("should handle tabulated values correctly", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 1.0,
      useTabulated: true,
    };

    const adjusted = applyBearingAreaFactor(625, input);
    expect(adjusted).toBe(625 * 1.38);
  });
});

describe("calculateBearingAtAngle", () => {
  test("should calculate using NDS Equation 3.10-1", () => {
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 45,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    // At 45°, sin²θ = cos²θ = 0.5
    // Fc' = (1600 * 800) / (1600 * 0.5 + 800 * 0.5) = 1280000 / 1200 = 1066.67
    const result = calculateBearingAtAngle(input);
    expect(result).toBeCloseTo(1066.67, 1);
  });

  test("should handle parallel to grain (0°)", () => {
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 0,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    // At 0°, sin²θ = 0, cos²θ = 1
    // Should approach Fc parallel
    const result = calculateBearingAtAngle(input);
    expect(result).toBeCloseTo(1600, 1);
  });

  test("should handle perpendicular to grain (90°)", () => {
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 90,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    // At 90°, sin²θ = 1, cos²θ = 0
    // Should approach Fc perpendicular adjusted
    const result = calculateBearingAtAngle(input);
    expect(result).toBeCloseTo(800, 1);
  });

  test("should handle various angles correctly", () => {
    const baseInput: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 30,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    // Test multiple angles
    const angles = [0, 15, 30, 45, 60, 75, 90];
    angles.forEach((angle) => {
      const input = { ...baseInput, angleToGrain: angle };
      const result = calculateBearingAtAngle(input);

      // Result should be between Fc perpendicular and Fc parallel
      expect(result).toBeGreaterThanOrEqual(Math.min(800, 1600));
      expect(result).toBeLessThanOrEqual(Math.max(800, 1600));
    });
  });

  test("should throw error for division by zero", () => {
    // This would be extremely rare in practice but test for robustness
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 45,
      fcParallel: 0,
      fcPerpendicularAdjusted: 0,
    };

    expect(() => calculateBearingAtAngle(input)).toThrow(
      "Division by zero in bearing at angle calculation"
    );
  });
});

describe("getBearingAreaFactorAnalysis", () => {
  test("should provide comprehensive analysis", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 2.0,
      configurationType: BEARING_CONFIGURATION_TYPES.PLATE,
      useTabulated: false, // Force formula calculation for predictable result
    };

    const analysis = getBearingAreaFactorAnalysis(input);

    expect(analysis.input).toEqual(input);
    expect(analysis.bearingAreaFactor).toBeCloseTo(1.1875, 4);
    expect(analysis.calculationMethod).toBe("formula");
    expect(analysis.effectiveBearingLength).toBe(2.0);
    expect(analysis.validation.isValid).toBe(true);
    expect(Array.isArray(analysis.recommendations)).toBe(true);
  });

  test("should use tabulated method when appropriate", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 1.0,
      useTabulated: true,
    };

    const analysis = getBearingAreaFactorAnalysis(input);
    expect(analysis.calculationMethod).toBe("tabulated");
    expect(analysis.bearingAreaFactor).toBe(1.38);
  });

  test("should provide recommendations for small bearing areas", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 0.5,
    };

    const analysis = getBearingAreaFactorAnalysis(input);
    expect(analysis.recommendations).toContain(
      "Consider larger bearing area for better load distribution"
    );
  });

  test("should recommend specifying washer diameter", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 1.0,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
    };

    const analysis = getBearingAreaFactorAnalysis(input);
    expect(analysis.recommendations).toContain(
      "Specify washer diameter for accurate bearing length calculation"
    );
  });

  test("should warn about high bearing area factors", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 0.2,
    };

    const analysis = getBearingAreaFactorAnalysis(input);
    expect(analysis.recommendations).toContain(
      "High bearing area factor - verify that small bearing area is appropriate"
    );
  });
});

describe("getBearingAtAngleAnalysis", () => {
  test("should provide comprehensive analysis", () => {
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 45,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
      useTabulated: false, // Force formula calculation for predictable result
    };

    const analysis = getBearingAtAngleAnalysis(input);

    expect(analysis.input).toEqual(input);
    expect(analysis.adjustedBearingValue).toBeCloseTo(1066.67, 1);
    expect(analysis.bearingAreaFactor).toBeCloseTo(1.1875, 4);
    expect(analysis.angleInRadians).toBeCloseTo(Math.PI / 4, 6);
    expect(analysis.sinSquaredTheta).toBeCloseTo(0.5, 6);
    expect(analysis.cosSquaredTheta).toBeCloseTo(0.5, 6);
    expect(analysis.validation.isValid).toBe(true);
    expect(Array.isArray(analysis.recommendations)).toBe(true);
  });

  test("should provide angle-specific recommendations", () => {
    const parallelInput: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 0,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    const parallelAnalysis = getBearingAtAngleAnalysis(parallelInput);
    expect(parallelAnalysis.recommendations).toContain(
      "Load is parallel to grain - use Fc design values"
    );

    const perpendicularInput: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 90,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    const perpendicularAnalysis = getBearingAtAngleAnalysis(perpendicularInput);
    expect(perpendicularAnalysis.recommendations).toContain(
      "Load is perpendicular to grain - bearing area factor applies"
    );

    const angleInput: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 30,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 800,
    };

    const angleAnalysis = getBearingAtAngleAnalysis(angleInput);
    expect(angleAnalysis.recommendations).toContain(
      "Load at angle to grain - verify load path and bearing configuration"
    );
  });

  test("should warn about potentially incorrect calculations", () => {
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 30,
      fcParallel: 1000,
      fcPerpendicularAdjusted: 800,
    };

    const analysis = getBearingAtAngleAnalysis(input);

    // If adjusted bearing value exceeds Fc parallel, it should warn
    if (analysis.adjustedBearingValue > input.fcParallel) {
      expect(analysis.recommendations).toContain(
        "Adjusted bearing value exceeds Fc parallel - verify calculation"
      );
    }
  });
});

describe("getMultipleBearingAreaFactors", () => {
  test("should calculate factors for multiple bearing lengths", () => {
    const bearingLengths = [0.5, 1.0, 2.0, 4.0];
    const baseInput = {
      configurationType: BEARING_CONFIGURATION_TYPES.PLATE,
      useTabulated: true, // Use tabulated values for this test
    };

    const results = getMultipleBearingAreaFactors(bearingLengths, baseInput);

    expect(results).toHaveLength(4);
    expect(results[0].bearingAreaFactor).toBe(1.75); // Exact tabulated value
    expect(results[1].bearingAreaFactor).toBe(1.38); // Exact tabulated value
    expect(results[2].bearingAreaFactor).toBe(1.19); // Exact tabulated value
    expect(results[3].bearingAreaFactor).toBe(1.1); // Exact tabulated value
  });

  test("should handle empty array", () => {
    const results = getMultipleBearingAreaFactors([], {});
    expect(results).toHaveLength(0);
  });

  test("should preserve base input parameters", () => {
    const bearingLengths = [1.0, 2.0];
    const baseInput = {
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
      diameter: 1.5,
      useTabulated: true,
    };

    const results = getMultipleBearingAreaFactors(bearingLengths, baseInput);

    results.forEach((result) => {
      expect(result.input.configurationType).toBe(
        BEARING_CONFIGURATION_TYPES.WASHER
      );
      expect(result.input.diameter).toBe(1.5);
      expect(result.input.useTabulated).toBe(true);
    });
  });
});

describe("isDesignValueAffectedByBearingAreaFactor", () => {
  test("should return true only for compression perpendicular", () => {
    expect(
      isDesignValueAffectedByBearingAreaFactor(
        DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR
      )
    ).toBe(true);
  });

  test("should return false for other design values", () => {
    const otherDesignValues = [
      DESIGN_VALUE_TYPES.BENDING,
      DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
      DESIGN_VALUE_TYPES.SHEAR,
      DESIGN_VALUE_TYPES.E,
      DESIGN_VALUE_TYPES.E_MIN,
    ];

    otherDesignValues.forEach((designValue) => {
      expect(isDesignValueAffectedByBearingAreaFactor(designValue)).toBe(false);
    });
  });
});

describe("getBearingAreaFactorOptimization", () => {
  test("should return minimum length for target factor of 1.0", () => {
    const result = getBearingAreaFactorOptimization(1.0, 2.0);

    expect(result.optimizedLength).toBe(6.0);
    expect(result.achievedFactor).toBe(1.0);
    expect(result.recommendations).toContain(
      "Target factor is minimum - use bearing length ≥ 6 inches"
    );
  });

  test("should calculate optimized length for realistic target factors", () => {
    const result = getBearingAreaFactorOptimization(1.25, 2.0);

    // ℓb = 0.375 / (1.25 - 1) = 0.375 / 0.25 = 1.5
    expect(result.optimizedLength).toBeCloseTo(1.5, 3);
    expect(result.achievedFactor).toBeCloseTo(1.25, 3);
  });

  test("should handle unrealistic target factors", () => {
    const result = getBearingAreaFactorOptimization(10.0, 2.0);

    expect(result.optimizedLength).toBe(2.0);
    expect(result.achievedFactor).toBeCloseTo(1.1875, 4);
    expect(result.recommendations).toContain(
      "Target factor too high for practical bearing lengths"
    );
  });

  test("should warn about very small bearing lengths", () => {
    const result = getBearingAreaFactorOptimization(2.0, 1.0);

    expect(result.recommendations).toContain(
      "Very small bearing length required - consider design alternatives"
    );
  });

  test("should warn about large bearing lengths", () => {
    const result = getBearingAreaFactorOptimization(1.05, 2.0);

    expect(result.recommendations).toContain(
      "Large bearing length - bearing area factor approaches 1.0"
    );
  });

  test("should solve the inverse formula correctly", () => {
    // Test with known values
    const targetFactor = 1.375; // Should require 1.0" bearing length
    const result = getBearingAreaFactorOptimization(targetFactor, 2.0);

    expect(result.optimizedLength).toBeCloseTo(1.0, 3);
    expect(result.achievedFactor).toBeCloseTo(1.375, 3);
  });
});

describe("Integration Tests", () => {
  test("should demonstrate typical bearing plate calculation", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 3.0,
      configurationType: BEARING_CONFIGURATION_TYPES.PLATE,
      useTabulated: true, // Use tabulated values
    };

    const analysis = getBearingAreaFactorAnalysis(input);
    const referenceFcPerp = 625; // psi
    const adjustedValue = applyBearingAreaFactor(referenceFcPerp, input);

    // For 3.0" bearing length, tabulated value is 1.13
    expect(analysis.bearingAreaFactor).toBe(1.13);
    expect(adjustedValue).toBeCloseTo(625 * 1.13, 2);
    expect(analysis.validation.isValid).toBe(true);
  });

  test("should demonstrate washer bearing calculation", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 1.0,
      configurationType: BEARING_CONFIGURATION_TYPES.WASHER,
      diameter: 1.0,
      useTabulated: true,
    };

    const analysis = getBearingAreaFactorAnalysis(input);

    expect(analysis.bearingAreaFactor).toBe(1.38);
    expect(analysis.calculationMethod).toBe("tabulated");
    expect(analysis.effectiveBearingLength).toBe(1.0);
  });

  test("should demonstrate bearing at angle calculation", () => {
    const input: BearingAtAngleInput = {
      bearingLength: 2.0,
      angleToGrain: 30,
      fcParallel: 1600,
      fcPerpendicularAdjusted: 780, // Already includes bearing area factor
    };

    const analysis = getBearingAtAngleAnalysis(input);

    expect(analysis.adjustedBearingValue).toBeGreaterThan(780);
    expect(analysis.adjustedBearingValue).toBeLessThan(1600);
    expect(analysis.validation.isValid).toBe(true);
  });

  test("should handle edge case with very small bearing area", () => {
    const input: BearingAreaFactorInput = {
      bearingLength: 0.25,
      useTabulated: false, // Use formula for small values
    };

    const analysis = getBearingAreaFactorAnalysis(input);

    // For 0.25" bearing length: Cb = (0.25 + 0.375) / 0.25 = 0.625 / 0.25 = 2.5
    expect(analysis.bearingAreaFactor).toBeCloseTo(2.5, 1);
    expect(analysis.validation.isValid).toBe(true);
    expect(analysis.recommendations.length).toBeGreaterThan(0);
  });

  test("should handle comparison between formula and tabulated values", () => {
    const bearingLength = 1.5;

    const formulaInput: BearingAreaFactorInput = {
      bearingLength,
      useTabulated: false,
    };

    const tabulatedInput: BearingAreaFactorInput = {
      bearingLength,
      useTabulated: true,
    };

    const formulaResult = getBearingAreaFactor(formulaInput);
    const tabulatedResult = getBearingAreaFactor(tabulatedInput);

    // Both should give reasonable results
    expect(formulaResult).toBeCloseTo(1.25, 2);
    expect(tabulatedResult).toBe(1.25); // Exact tabulated value
  });
});
