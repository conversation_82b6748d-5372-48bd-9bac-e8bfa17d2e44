/**
 * Flat Use Factor (Cfu) Calculations
 * 
 * Implements NDS 4.3.7 Flat Use Factor for adjusting bending design values
 * when loads are applied to the wide face of lumber members.
 * 
 * Based on:
 * - NDS Tables 4A-4F: Flat Use Factors for sawn lumber
 * - Flat use factor applies only to bending design value (Fb)
 * - Applied when load is perpendicular to the wide face of the member
 * 
 * @fileoverview Flat use factor calculations for wood structural design
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
} from "../../constants";

/**
 * Lumber grades for flat use factor determination
 */
export const FLAT_USE_LUMBER_GRADES = {
  // Visually graded lumber
  SELECT_STRUCTURAL: 'select_structural',
  NO_1_AND_BETTER: 'no_1_and_better',
  NO_1: 'no_1',
  NO_2: 'no_2',
  NO_3: 'no_3',
  
  // Machine stress rated lumber  
  STUD: 'stud',
  CONSTRUCTION: 'construction',
  STANDARD: 'standard',
  UTILITY: 'utility',
  
  // Machine evaluated lumber
  MSR: 'msr',
  MEL: 'mel',
} as const;

export type FlatUseLumberGrade = typeof FLAT_USE_LUMBER_GRADES[keyof typeof FLAT_USE_LUMBER_GRADES];

/**
 * Member dimensions for flat use factor calculations
 */
export const FLAT_USE_DIMENSIONS = {
  // Standard thickness categories
  THICKNESS_2_AND_3: '2_and_3_inch',    // 2" & 3" thick (nominal)
  THICKNESS_4: '4_inch',                 // 4" thick (nominal)
  THICKNESS_5_AND_THICKER: '5_and_thicker', // 5" and thicker (nominal)
  
  // Depth categories (width when oriented for flat use)
  DEPTH_2_3_4: '2_3_4_inch',            // 2", 3", & 4" deep
  DEPTH_5: '5_inch',                     // 5" deep
  DEPTH_6: '6_inch',                     // 6" deep
  DEPTH_8: '8_inch',                     // 8" deep
  DEPTH_10: '10_inch',                   // 10" deep
  DEPTH_12: '12_inch',                   // 12" deep
  DEPTH_14_AND_WIDER: '14_inch_and_wider', // 14" & wider
} as const;

export type FlatUseDimension = typeof FLAT_USE_DIMENSIONS[keyof typeof FLAT_USE_DIMENSIONS];

/**
 * Flat use factors from NDS Tables 4A-4F
 */
export const FLAT_USE_FACTORS = {
  // Standard flat use factors for visually graded lumber and higher grades
  STANDARD: {
    [FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3]: {
      [FLAT_USE_DIMENSIONS.DEPTH_2_3_4]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_5]: 1.1,
      [FLAT_USE_DIMENSIONS.DEPTH_6]: 1.15,
      [FLAT_USE_DIMENSIONS.DEPTH_8]: 1.15,
      [FLAT_USE_DIMENSIONS.DEPTH_10]: 1.2,
      [FLAT_USE_DIMENSIONS.DEPTH_12]: 1.2,
      [FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER]: 1.2,
    },
    [FLAT_USE_DIMENSIONS.THICKNESS_4]: {
      [FLAT_USE_DIMENSIONS.DEPTH_2_3_4]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_5]: 1.05,
      [FLAT_USE_DIMENSIONS.DEPTH_6]: 1.05,
      [FLAT_USE_DIMENSIONS.DEPTH_8]: 1.05,
      [FLAT_USE_DIMENSIONS.DEPTH_10]: 1.1,
      [FLAT_USE_DIMENSIONS.DEPTH_12]: 1.1,
      [FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER]: 1.1,
    },
    [FLAT_USE_DIMENSIONS.THICKNESS_5_AND_THICKER]: {
      [FLAT_USE_DIMENSIONS.DEPTH_2_3_4]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_5]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_6]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_8]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_10]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_12]: 1.0,
      [FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER]: 1.0,
    },
  },
  
  // Special factors for Construction, Standard, Utility grades
  CONSTRUCTION_STANDARD_UTILITY: {
    [FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3]: {
      [FLAT_USE_DIMENSIONS.DEPTH_2_3_4]: 0.4,
    },
    [FLAT_USE_DIMENSIONS.THICKNESS_4]: {
      [FLAT_USE_DIMENSIONS.DEPTH_2_3_4]: 0.6,
    },
    [FLAT_USE_DIMENSIONS.THICKNESS_5_AND_THICKER]: {
      [FLAT_USE_DIMENSIONS.DEPTH_2_3_4]: 1.0,
    },
  },
} as const;

/**
 * Flat use factor limitations and constants
 */
export const FLAT_USE_CONSTANTS = {
  // Maximum allowable dimensions for flat use factors
  MAX_DEPTH_INCHES: 14,          // 14" maximum depth for tabulated values
  MAX_THICKNESS_INCHES: 12,      // Practical maximum thickness for flat use
  
  // Minimum dimensions for flat use application
  MIN_DEPTH_INCHES: 2,           // Minimum depth for flat use considerations
  MIN_THICKNESS_INCHES: 1.5,     // Minimum thickness (actual dimension)
  
  // Default factors
  DEFAULT_FLAT_USE_FACTOR: 1.0,  // Default when flat use factor doesn't apply
  
  // Applicability thresholds
  ASPECT_RATIO_THRESHOLD: 2.0,   // Depth/thickness ratio for flat use considerations
} as const;

/**
 * Input parameters for flat use factor calculation
 */
export interface FlatUseFactorInput {
  /** Width (breadth) of member in inches */
  width: number;
  
  /** Depth (height) of member in inches */
  depth: number;
  
  /** Type of design value (must be Fb for flat use factor) */
  designValueType: DesignValueType;
  
  /** Lumber grade */
  lumberGrade: FlatUseLumberGrade;
  
  /** Whether load is applied to wide face (required for flat use factor) */
  isLoadOnWideFace: boolean;
}

/**
 * Validation result for flat use factor input
 */
export interface FlatUseFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Comprehensive flat use factor analysis result
 */
export interface FlatUseFactorAnalysis {
  input: FlatUseFactorInput;
  flatUseFactor: number;
  applicableDesignValue: boolean;
  dimensionCategory: string;
  gradeCategory: string;
  aspectRatio: number;
  isWideOrientation: boolean;
  validation: FlatUseFactorValidation;
}

/**
 * Validates flat use factor input parameters
 * 
 * @param input - Input parameters to validate
 * @returns Validation result with errors and warnings
 * 
 * @example
 * ```typescript
 * const input = {
 *   width: 1.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
 *   isLoadOnWideFace: true
 * };
 * const validation = validateFlatUseFactorInput(input);
 * ```
 */
export function validateFlatUseFactorInput(
  input: FlatUseFactorInput
): FlatUseFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate width
  if (typeof input.width !== 'number' || 
      isNaN(input.width) || 
      !isFinite(input.width)) {
    errors.push('Width must be a valid number');
  } else if (input.width <= 0) {
    errors.push('Width must be positive');
  } else if (input.width < FLAT_USE_CONSTANTS.MIN_THICKNESS_INCHES) {
    warnings.push(`Width is very small (${input.width}") - verify dimensions`);
  } else if (input.width > FLAT_USE_CONSTANTS.MAX_THICKNESS_INCHES) {
    warnings.push(`Width exceeds ${FLAT_USE_CONSTANTS.MAX_THICKNESS_INCHES}" - verify flat use applicability`);
  }
  
  // Validate depth
  if (typeof input.depth !== 'number' || 
      isNaN(input.depth) || 
      !isFinite(input.depth)) {
    errors.push('Depth must be a valid number');
  } else if (input.depth <= 0) {
    errors.push('Depth must be positive');
  } else if (input.depth < FLAT_USE_CONSTANTS.MIN_DEPTH_INCHES) {
    warnings.push(`Depth is very small (${input.depth}") - flat use factor may not be significant`);
  } else if (input.depth > FLAT_USE_CONSTANTS.MAX_DEPTH_INCHES) {
    warnings.push(`Depth exceeds ${FLAT_USE_CONSTANTS.MAX_DEPTH_INCHES}" - may require special analysis`);
  }
  
  // Validate design value type
  if (!Object.values(DESIGN_VALUE_TYPES).includes(input.designValueType)) {
    errors.push(`Invalid design value type: ${input.designValueType}`);
  } else if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
    warnings.push(`Flat use factor only applies to bending (Fb), not ${input.designValueType}`);
  }
  
  // Validate lumber grade
  if (!Object.values(FLAT_USE_LUMBER_GRADES).includes(input.lumberGrade)) {
    errors.push(`Invalid lumber grade: ${input.lumberGrade}`);
  }
  
  // Validate load orientation
  if (typeof input.isLoadOnWideFace !== 'boolean') {
    errors.push('isLoadOnWideFace must be specified as boolean');
  } else if (!input.isLoadOnWideFace) {
    warnings.push('Flat use factor only applies when load is on wide face');
  }
  
  // Check aspect ratio
  if (input.width > 0 && input.depth > 0) {
    const aspectRatio = input.depth / input.width;
    if (aspectRatio < FLAT_USE_CONSTANTS.ASPECT_RATIO_THRESHOLD) {
      warnings.push(`Low aspect ratio (${aspectRatio.toFixed(1)}) - flat use may not be beneficial`);
    }
    
    if (input.width > input.depth) {
      warnings.push('Width is greater than depth - verify member orientation for flat use');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Determines dimension category for flat use factor lookup
 * 
 * @param width - Member width (thickness when in flat orientation) in inches
 * @param depth - Member depth (width when in flat orientation) in inches
 * @returns Dimension category information
 */
export function getFlatUseDimensionCategory(
  width: number,
  depth: number
): { thickness: FlatUseDimension; depthCategory: FlatUseDimension } {
  // Determine thickness category (width when oriented for flat use)
  let thickness: FlatUseDimension;
  if (width <= 3.5) {
    thickness = FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3;
  } else if (width <= 4.5) {
    thickness = FLAT_USE_DIMENSIONS.THICKNESS_4;
  } else {
    thickness = FLAT_USE_DIMENSIONS.THICKNESS_5_AND_THICKER;
  }
  
  // Determine depth category (becomes the width when oriented for flat use)
  let depthCategory: FlatUseDimension;
  if (depth <= 4) {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_2_3_4;
  } else if (depth <= 5) {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_5;
  } else if (depth <= 6) {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_6;
  } else if (depth <= 8) {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_8;
  } else if (depth <= 10) {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_10;
  } else if (depth <= 12) {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_12;
  } else {
    depthCategory = FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER;
  }
  
  return { thickness, depthCategory };
}

/**
 * Gets flat use factor for specified lumber grade and dimensions
 * 
 * @param input - Input parameters for flat use factor calculation
 * @returns Flat use factor (Cfu)
 * @throws Error if input validation fails
 * 
 * @example
 * ```typescript
 * const input = {
 *   width: 1.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
 *   isLoadOnWideFace: true
 * };
 * const factor = getFlatUseFactor(input);
 * console.log(factor); // 1.2 for 2x12 on wide face
 * ```
 */
export function getFlatUseFactor(input: FlatUseFactorInput): number {
  const validation = validateFlatUseFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid flat use factor input: ${validation.errors.join(', ')}`);
  }
  
  // Flat use factor only applies when load is on wide face
  if (!input.isLoadOnWideFace) {
    return FLAT_USE_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
  }
  
  // Flat use factor only applies to bending
  if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
    return FLAT_USE_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
  }
  
  const { thickness, depthCategory } = getFlatUseDimensionCategory(input.width, input.depth);
  
  // Determine factor table based on grade
  let factorTable: any;
  
  const constructionGrades: FlatUseLumberGrade[] = [
    FLAT_USE_LUMBER_GRADES.CONSTRUCTION,
    FLAT_USE_LUMBER_GRADES.STANDARD,
    FLAT_USE_LUMBER_GRADES.UTILITY
  ];
  
  if (constructionGrades.includes(input.lumberGrade)) {
    factorTable = FLAT_USE_FACTORS.CONSTRUCTION_STANDARD_UTILITY;
  } else {
    factorTable = FLAT_USE_FACTORS.STANDARD;
  }
  
  return factorTable[thickness]?.[depthCategory] ?? FLAT_USE_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
}

/**
 * Applies flat use factor to a reference bending design value
 * 
 * @param referenceValue - Reference bending design value (Fb)
 * @param input - Input parameters for flat use factor calculation
 * @returns Adjusted bending design value
 * @throws Error if reference value is invalid or input validation fails
 * 
 * @example
 * ```typescript
 * const adjustedFb = applyFlatUseFactor(1000, {
 *   width: 1.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
 *   isLoadOnWideFace: true
 * });
 * console.log(adjustedFb); // 1200 (1000 × 1.2)
 * ```
 */
export function applyFlatUseFactor(
  referenceValue: number,
  input: FlatUseFactorInput
): number {
  if (typeof referenceValue !== 'number' || 
      isNaN(referenceValue) || 
      !isFinite(referenceValue) ||
      referenceValue < 0) {
    throw new Error('Reference value must be a non-negative finite number');
  }
  
  const flatUseFactor = getFlatUseFactor(input);
  return referenceValue * flatUseFactor;
}

/**
 * Provides comprehensive analysis of flat use factor calculation
 * 
 * @param input - Input parameters for analysis
 * @returns Detailed analysis including factor, categories, and validation
 * 
 * @example
 * ```typescript
 * const analysis = getFlatUseFactorAnalysis({
 *   width: 1.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
 *   isLoadOnWideFace: true
 * });
 * console.log(analysis.flatUseFactor); // 1.2
 * console.log(analysis.aspectRatio); // 7.5
 * ```
 */
export function getFlatUseFactorAnalysis(
  input: FlatUseFactorInput
): FlatUseFactorAnalysis {
  const validation = validateFlatUseFactorInput(input);
  
  if (!validation.isValid) {
    return {
      input,
      flatUseFactor: FLAT_USE_CONSTANTS.DEFAULT_FLAT_USE_FACTOR,
      applicableDesignValue: false,
      dimensionCategory: 'invalid',
      gradeCategory: 'invalid',
      aspectRatio: 0,
      isWideOrientation: false,
      validation,
    };
  }
  
  const flatUseFactor = getFlatUseFactor(input);
  const { thickness, depthCategory } = getFlatUseDimensionCategory(input.width, input.depth);
  
  // Calculate aspect ratio
  const aspectRatio = input.depth / input.width;
  
  // Determine if this is a wide orientation (suitable for flat use)
  const isWideOrientation = aspectRatio >= FLAT_USE_CONSTANTS.ASPECT_RATIO_THRESHOLD;
  
  // Check if design value is applicable
  const applicableDesignValue = input.designValueType === DESIGN_VALUE_TYPES.BENDING;
  
  // Determine grade category
  let gradeCategory: string;
  const constructionGrades: FlatUseLumberGrade[] = [
    FLAT_USE_LUMBER_GRADES.CONSTRUCTION,
    FLAT_USE_LUMBER_GRADES.STANDARD,
    FLAT_USE_LUMBER_GRADES.UTILITY
  ];
  
  if (constructionGrades.includes(input.lumberGrade)) {
    gradeCategory = 'construction_standard_utility';
  } else {
    gradeCategory = 'standard_grades';
  }
  
  return {
    input,
    flatUseFactor,
    applicableDesignValue,
    dimensionCategory: `${thickness}_${depthCategory}`,
    gradeCategory,
    aspectRatio,
    isWideOrientation,
    validation,
  };
}

/**
 * Calculates multiple flat use factors for different orientations
 * 
 * @param baseInput - Base input parameters (without isLoadOnWideFace)
 * @param orientations - Array of orientation configurations to test
 * @returns Object mapping orientation names to flat use factors
 * 
 * @example
 * ```typescript
 * const factors = getMultipleFlatUseFactors(
 *   {
 *     width: 1.5,
 *     depth: 11.25,
 *     designValueType: DESIGN_VALUE_TYPES.BENDING,
 *     lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL
 *   },
 *   [
 *     { name: 'normal', isLoadOnWideFace: false },
 *     { name: 'flat', isLoadOnWideFace: true }
 *   ]
 * );
 * console.log(factors); // { normal: 1.0, flat: 1.2 }
 * ```
 */
export function getMultipleFlatUseFactors(
  baseInput: Omit<FlatUseFactorInput, 'isLoadOnWideFace'>,
  orientations: { name: string; isLoadOnWideFace: boolean }[]
): Record<string, number> {
  const result: Record<string, number> = {};
  
  for (const orientation of orientations) {
    try {
      const input: FlatUseFactorInput = {
        ...baseInput,
        isLoadOnWideFace: orientation.isLoadOnWideFace,
      };
      result[orientation.name] = getFlatUseFactor(input);
    } catch (error) {
      // Return default factor for invalid configurations
      result[orientation.name] = FLAT_USE_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
    }
  }
  
  return result;
}

/**
 * Checks if a design value is affected by flat use factors
 * 
 * @param designValueType - Type of design value to check
 * @returns True if the design value is affected by flat use factors
 * 
 * @example
 * ```typescript
 * const isAffected = isDesignValueAffectedByFlatUse(DESIGN_VALUE_TYPES.BENDING);
 * console.log(isAffected); // true
 * 
 * const isNotAffected = isDesignValueAffectedByFlatUse(DESIGN_VALUE_TYPES.TENSION_PARALLEL);
 * console.log(isNotAffected); // false
 * ```
 */
export function isDesignValueAffectedByFlatUse(
  designValueType: DesignValueType
): boolean {
  return designValueType === DESIGN_VALUE_TYPES.BENDING;
}

/**
 * Determines optimal orientation for maximum bending capacity
 * 
 * @param width - Member width in inches
 * @param depth - Member depth in inches
 * @param lumberGrade - Lumber grade
 * @returns Analysis of normal vs flat orientation performance
 * 
 * @example
 * ```typescript
 * const optimization = getOrientationOptimization(1.5, 11.25, FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL);
 * console.log(optimization.recommendedOrientation); // 'normal' or 'flat'
 * console.log(optimization.capacityImprovement); // percentage improvement
 * ```
 */
export function getOrientationOptimization(
  width: number,
  depth: number,
  lumberGrade: FlatUseLumberGrade
): {
  normalOrientation: { factor: number; momentCapacity: number };
  flatOrientation: { factor: number; momentCapacity: number };
  recommendedOrientation: 'normal' | 'flat';
  capacityImprovement: number;
  aspectRatio: number;
} {
  const baseInput = {
    width,
    depth,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade,
  };
  
  // Normal orientation (load on narrow face)
  const normalFactor = getFlatUseFactor({
    ...baseInput,
    isLoadOnWideFace: false,
  });
  
  // Flat orientation (load on wide face)
  const flatFactor = getFlatUseFactor({
    ...baseInput,
    isLoadOnWideFace: true,
  });
  
  // Section modulus calculations
  // Normal: S = b*d²/6, Flat: S = d*b²/6
  const normalSectionModulus = (width * Math.pow(depth, 2)) / 6;
  const flatSectionModulus = (depth * Math.pow(width, 2)) / 6;
  
  // Moment capacity = Fb * S * adjustment_factors
  // Using relative comparison (assuming same base Fb)
  const normalCapacity = normalFactor * normalSectionModulus;
  const flatCapacity = flatFactor * flatSectionModulus;
  
  const recommendedOrientation = flatCapacity > normalCapacity ? 'flat' : 'normal';
  const capacityImprovement = Math.abs((flatCapacity - normalCapacity) / normalCapacity) * 100;
  
  return {
    normalOrientation: {
      factor: normalFactor,
      momentCapacity: normalCapacity,
    },
    flatOrientation: {
      factor: flatFactor,
      momentCapacity: flatCapacity,
    },
    recommendedOrientation,
    capacityImprovement,
    aspectRatio: depth / width,
  };
} 