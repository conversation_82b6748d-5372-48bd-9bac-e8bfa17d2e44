/**
 * Tests for Flat Use Factor (Cfu) Calculations
 * 
 * @fileoverview Comprehensive test suite for flat use factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import { describe, test, expect } from '@jest/globals';
import {
  // Constants
  FLAT_USE_LUMBER_GRADES,
  FLAT_USE_DIMENSIONS,
  FLAT_USE_FACTORS,
  FLAT_USE_CONSTANTS,
  
  // Types
  type FlatUseFactorInput,
  type FlatUseFactorValidation,
  type FlatUseFactorAnalysis,
  
  // Functions
  validateFlatUseFactorInput,
  getFlatUseDimensionCategory,
  getFlatUseFactor,
  applyFlatUseFactor,
  getFlatUseFactorAnalysis,
  getMultipleFlatUseFactors,
  isDesignValueAffectedByFlatUse,
  getOrientationOptimization,
} from './flat-use-factor';

import { DESIGN_VALUE_TYPES } from '../../constants';

describe('Flat Use Factor Constants', () => {
  test('should have correct lumber grades defined', () => {
    expect(FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL).toBe('select_structural');
    expect(FLAT_USE_LUMBER_GRADES.NO_1_AND_BETTER).toBe('no_1_and_better');
    expect(FLAT_USE_LUMBER_GRADES.STUD).toBe('stud');
    expect(FLAT_USE_LUMBER_GRADES.CONSTRUCTION).toBe('construction');
  });

  test('should have correct dimension categories defined', () => {
    expect(FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3).toBe('2_and_3_inch');
    expect(FLAT_USE_DIMENSIONS.THICKNESS_4).toBe('4_inch');
    expect(FLAT_USE_DIMENSIONS.DEPTH_12).toBe('12_inch');
    expect(FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER).toBe('14_inch_and_wider');
  });

  test('should have flat use factors for standard grades', () => {
    const factors = FLAT_USE_FACTORS.STANDARD[FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3][FLAT_USE_DIMENSIONS.DEPTH_12];
    expect(factors).toBe(1.2);
  });

  test('should have flat use factors for construction grades', () => {
    const factors = FLAT_USE_FACTORS.CONSTRUCTION_STANDARD_UTILITY[FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3][FLAT_USE_DIMENSIONS.DEPTH_2_3_4];
    expect(factors).toBe(0.4);
  });

  test('should have correct default values', () => {
    expect(FLAT_USE_CONSTANTS.DEFAULT_FLAT_USE_FACTOR).toBe(1.0);
    expect(FLAT_USE_CONSTANTS.MAX_DEPTH_INCHES).toBe(14);
    expect(FLAT_USE_CONSTANTS.ASPECT_RATIO_THRESHOLD).toBe(2.0);
  });
});

describe('validateFlatUseFactorInput', () => {
  const validInput: FlatUseFactorInput = {
    width: 1.5,
    depth: 11.25,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
    isLoadOnWideFace: true,
  };

  test('should validate correct input', () => {
    const result = validateFlatUseFactorInput(validInput);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('should reject invalid width values', () => {
    const invalidInputs = [
      { ...validInput, width: 0 },
      { ...validInput, width: -1 },
      { ...validInput, width: NaN },
      { ...validInput, width: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateFlatUseFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid depth values', () => {
    const invalidInputs = [
      { ...validInput, depth: 0 },
      { ...validInput, depth: -1 },
      { ...validInput, depth: NaN },
      { ...validInput, depth: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateFlatUseFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid design value types', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      designValueType: 'invalid' as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Invalid design value type: invalid');
  });

  test('should warn about non-bending design values', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Flat use factor only applies to bending (Fb), not Ft');
  });

  test('should reject invalid lumber grades', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      lumberGrade: 'invalid' as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Invalid lumber grade: invalid');
  });

  test('should reject invalid load orientation specification', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      isLoadOnWideFace: undefined as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('isLoadOnWideFace must be specified as boolean');
  });

  test('should warn when load is not on wide face', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      isLoadOnWideFace: false,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Flat use factor only applies when load is on wide face');
  });

  test('should warn about large dimensions', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      width: 14,
      depth: 16,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings.length).toBeGreaterThan(0);
  });

  test('should warn about low aspect ratio', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      width: 3.5,
      depth: 5.5,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings.some(w => w.includes('Low aspect ratio'))).toBe(true);
  });

  test('should warn when width is greater than depth', () => {
    const result = validateFlatUseFactorInput({
      ...validInput,
      width: 12,
      depth: 3.5,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Width is greater than depth - verify member orientation for flat use');
  });
});

describe('getFlatUseDimensionCategory', () => {
  test('should categorize 2x4 lumber correctly', () => {
    const result = getFlatUseDimensionCategory(1.5, 3.5);
    expect(result.thickness).toBe(FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3);
    expect(result.depthCategory).toBe(FLAT_USE_DIMENSIONS.DEPTH_2_3_4);
  });

  test('should categorize 2x12 lumber correctly', () => {
    const result = getFlatUseDimensionCategory(1.5, 11.25);
    expect(result.thickness).toBe(FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3);
    expect(result.depthCategory).toBe(FLAT_USE_DIMENSIONS.DEPTH_12);
  });

  test('should categorize 4x10 lumber correctly', () => {
    const result = getFlatUseDimensionCategory(3.5, 9.25);
    expect(result.thickness).toBe(FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3);
    expect(result.depthCategory).toBe(FLAT_USE_DIMENSIONS.DEPTH_10);
  });

  test('should categorize 6x12 lumber correctly', () => {
    const result = getFlatUseDimensionCategory(5.5, 11.25);
    expect(result.thickness).toBe(FLAT_USE_DIMENSIONS.THICKNESS_5_AND_THICKER);
    expect(result.depthCategory).toBe(FLAT_USE_DIMENSIONS.DEPTH_12);
  });

  test('should categorize large lumber correctly', () => {
    const result = getFlatUseDimensionCategory(7.25, 16);
    expect(result.thickness).toBe(FLAT_USE_DIMENSIONS.THICKNESS_5_AND_THICKER);
    expect(result.depthCategory).toBe(FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER);
  });

  test('should handle boundary conditions', () => {
    const tests = [
      { width: 3.5, expectedThickness: FLAT_USE_DIMENSIONS.THICKNESS_2_AND_3 },
      { width: 4.0, expectedThickness: FLAT_USE_DIMENSIONS.THICKNESS_4 },
      { width: 4.5, expectedThickness: FLAT_USE_DIMENSIONS.THICKNESS_4 },
      { width: 5.0, expectedThickness: FLAT_USE_DIMENSIONS.THICKNESS_5_AND_THICKER },
      { depth: 4.0, expectedDepth: FLAT_USE_DIMENSIONS.DEPTH_2_3_4 },
      { depth: 5.0, expectedDepth: FLAT_USE_DIMENSIONS.DEPTH_5 },
      { depth: 12.0, expectedDepth: FLAT_USE_DIMENSIONS.DEPTH_12 },
      { depth: 14.0, expectedDepth: FLAT_USE_DIMENSIONS.DEPTH_14_AND_WIDER },
    ];

    tests.forEach(test => {
      const result = getFlatUseDimensionCategory(test.width || 1.5, test.depth || 1.5);
      if (test.expectedThickness) {
        expect(result.thickness).toBe(test.expectedThickness);
      }
      if (test.expectedDepth) {
        expect(result.depthCategory).toBe(test.expectedDepth);
      }
    });
  });
});

describe('getFlatUseFactor', () => {
  test('should return 1.0 when load is not on wide face', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: false,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should return correct flat use factor for 2x12 on wide face', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.2);
  });

  test('should return 1.0 for non-bending design values', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should handle Construction grade flat use factors', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.CONSTRUCTION,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(0.4);
  });

  test('should handle Standard grade flat use factors', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.STANDARD,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(0.4);
  });

  test('should handle Utility grade flat use factors', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.UTILITY,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(0.4);
  });

  test('should handle all standard lumber grades', () => {
    const grades = [
      FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      FLAT_USE_LUMBER_GRADES.NO_1_AND_BETTER,
      FLAT_USE_LUMBER_GRADES.NO_1,
      FLAT_USE_LUMBER_GRADES.NO_2,
      FLAT_USE_LUMBER_GRADES.NO_3,
      FLAT_USE_LUMBER_GRADES.STUD,
    ];

    grades.forEach(grade => {
      const input: FlatUseFactorInput = {
        width: 1.5,
        depth: 11.25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: grade,
        isLoadOnWideFace: true,
      };
      const factor = getFlatUseFactor(input);
      expect(factor).toBe(1.2);
    });
  });

  test('should throw error for invalid input', () => {
    const input: FlatUseFactorInput = {
      width: -1,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    expect(() => getFlatUseFactor(input)).toThrow();
  });

  test('should handle different dimensions correctly', () => {
    const tests = [
      { width: 1.5, depth: 5.0, expected: 1.1 },
      { width: 1.5, depth: 7.25, expected: 1.15 },
      { width: 1.5, depth: 9.25, expected: 1.2 },
      { width: 3.5, depth: 5.5, expected: 1.15 },
      { width: 5.5, depth: 5.5, expected: 1.0 },
    ];

    tests.forEach(test => {
      const input: FlatUseFactorInput = {
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
        isLoadOnWideFace: true,
      };
      const factor = getFlatUseFactor(input);
      expect(factor).toBe(test.expected);
    });
  });

  test('should handle MSR and MEL grades with standard factors', () => {
    const grades = [FLAT_USE_LUMBER_GRADES.MSR, FLAT_USE_LUMBER_GRADES.MEL];
    
    grades.forEach(grade => {
      const input: FlatUseFactorInput = {
        width: 1.5,
        depth: 11.25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: grade,
        isLoadOnWideFace: true,
      };
      const factor = getFlatUseFactor(input);
      expect(factor).toBe(1.2); // Should use standard table
    });
  });
});

describe('applyFlatUseFactor', () => {
  test('should apply flat use factor to reference value', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const adjustedValue = applyFlatUseFactor(1000, input);
    expect(adjustedValue).toBe(1200); // 1000 × 1.2
  });

  test('should return same value when flat use factor is 1.0', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: false,
    };
    const adjustedValue = applyFlatUseFactor(1000, input);
    expect(adjustedValue).toBe(1000); // 1000 × 1.0
  });

  test('should throw error for invalid reference value', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    
    expect(() => applyFlatUseFactor(-1, input)).toThrow();
    expect(() => applyFlatUseFactor(NaN, input)).toThrow();
    expect(() => applyFlatUseFactor(Infinity, input)).toThrow();
  });

  test('should handle zero reference value', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const adjustedValue = applyFlatUseFactor(0, input);
    expect(adjustedValue).toBe(0);
  });

  test('should handle construction grade reduction', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.CONSTRUCTION,
      isLoadOnWideFace: true,
    };
    const adjustedValue = applyFlatUseFactor(1000, input);
    expect(adjustedValue).toBe(400); // 1000 × 0.4
  });
});

describe('getFlatUseFactorAnalysis', () => {
  test('should provide comprehensive analysis for valid input', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    
    const analysis = getFlatUseFactorAnalysis(input);
    
    expect(analysis.input).toEqual(input);
    expect(analysis.flatUseFactor).toBe(1.2);
    expect(analysis.applicableDesignValue).toBe(true);
    expect(analysis.dimensionCategory).toBe('2_and_3_inch_12_inch');
    expect(analysis.gradeCategory).toBe('standard_grades');
    expect(analysis.aspectRatio).toBe(7.5);
    expect(analysis.isWideOrientation).toBe(true);
    expect(analysis.validation.isValid).toBe(true);
  });

  test('should handle invalid input gracefully', () => {
    const input: FlatUseFactorInput = {
      width: -1,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    
    const analysis = getFlatUseFactorAnalysis(input);
    
    expect(analysis.flatUseFactor).toBe(1.0);
    expect(analysis.applicableDesignValue).toBe(false);
    expect(analysis.dimensionCategory).toBe('invalid');
    expect(analysis.gradeCategory).toBe('invalid');
    expect(analysis.aspectRatio).toBe(0);
    expect(analysis.isWideOrientation).toBe(false);
    expect(analysis.validation.isValid).toBe(false);
  });

  test('should categorize grades correctly', () => {
    const tests = [
      { grade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL, expected: 'standard_grades' },
      { grade: FLAT_USE_LUMBER_GRADES.NO_1, expected: 'standard_grades' },
      { grade: FLAT_USE_LUMBER_GRADES.STUD, expected: 'standard_grades' },
      { grade: FLAT_USE_LUMBER_GRADES.CONSTRUCTION, expected: 'construction_standard_utility' },
      { grade: FLAT_USE_LUMBER_GRADES.STANDARD, expected: 'construction_standard_utility' },
      { grade: FLAT_USE_LUMBER_GRADES.UTILITY, expected: 'construction_standard_utility' },
    ];

    tests.forEach(test => {
      const input: FlatUseFactorInput = {
        width: 1.5,
        depth: 3.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: test.grade,
        isLoadOnWideFace: true,
      };
      
      const analysis = getFlatUseFactorAnalysis(input);
      expect(analysis.gradeCategory).toBe(test.expected);
    });
  });

  test('should detect wide orientation correctly', () => {
    const tests = [
      { width: 1.5, depth: 3.5, expected: true },  // 2.33 > 2.0
      { width: 1.5, depth: 2.5, expected: false }, // 1.67 < 2.0
      { width: 3.5, depth: 7.25, expected: true },  // 2.07 > 2.0
      { width: 5.5, depth: 5.5, expected: false },  // 1.0 < 2.0
    ];

    tests.forEach(test => {
      const input: FlatUseFactorInput = {
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
        isLoadOnWideFace: true,
      };
      
      const analysis = getFlatUseFactorAnalysis(input);
      expect(analysis.isWideOrientation).toBe(test.expected);
    });
  });
});

describe('getMultipleFlatUseFactors', () => {
  test('should calculate factors for multiple orientations', () => {
    const baseInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    
    const orientations = [
      { name: 'normal', isLoadOnWideFace: false },
      { name: 'flat', isLoadOnWideFace: true },
    ];
    
    const factors = getMultipleFlatUseFactors(baseInput, orientations);
    
    expect(factors.normal).toBe(1.0);
    expect(factors.flat).toBe(1.2);
  });

  test('should handle invalid configurations gracefully', () => {
    const baseInput = {
      width: -1, // Invalid
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    
    const orientations = [
      { name: 'invalid', isLoadOnWideFace: true },
    ];
    
    const factors = getMultipleFlatUseFactors(baseInput, orientations);
    
    expect(factors.invalid).toBe(1.0); // Default factor for invalid input
  });

  test('should handle multiple grades', () => {
    const baseInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.CONSTRUCTION,
    };
    
    const orientations = [
      { name: 'normal', isLoadOnWideFace: false },
      { name: 'flat', isLoadOnWideFace: true },
    ];
    
    const factors = getMultipleFlatUseFactors(baseInput, orientations);
    
    expect(factors.normal).toBe(1.0);
    expect(factors.flat).toBe(0.4);
  });
});

describe('isDesignValueAffectedByFlatUse', () => {
  test('should return true for bending design value', () => {
    expect(isDesignValueAffectedByFlatUse(DESIGN_VALUE_TYPES.BENDING)).toBe(true);
  });

  test('should return false for other design values', () => {
    const unaffectedValues = [
      DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
      DESIGN_VALUE_TYPES.SHEAR,
      DESIGN_VALUE_TYPES.E,
      DESIGN_VALUE_TYPES.E_MIN,
      DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
    ];

    unaffectedValues.forEach(designValue => {
      expect(isDesignValueAffectedByFlatUse(designValue)).toBe(false);
    });
  });
});

describe('getOrientationOptimization', () => {
  test('should recommend normal orientation for typical 2x12', () => {
    const optimization = getOrientationOptimization(
      1.5, 11.25, FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL
    );
    
    expect(optimization.recommendedOrientation).toBe('normal');
    expect(optimization.normalOrientation.factor).toBe(1.0);
    expect(optimization.flatOrientation.factor).toBe(1.2);
    expect(optimization.aspectRatio).toBeCloseTo(7.5, 1);
    expect(optimization.capacityImprovement).toBeGreaterThan(0);
  });

  test('should handle wide members correctly', () => {
    const optimization = getOrientationOptimization(
      11.25, 1.5, FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL
    );
    
    // Wide member rotated - flat use factor doesn't help much
    expect(optimization.aspectRatio).toBeCloseTo(0.133, 2);
    expect(optimization.normalOrientation.factor).toBe(1.0);
    expect(optimization.flatOrientation.factor).toBe(1.0);
  });

  test('should calculate section modulus correctly', () => {
    const optimization = getOrientationOptimization(
      1.5, 11.25, FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL
    );
    
    // Normal: S = b*d²/6 = 1.5 * 11.25² / 6 = 31.64
    // Flat: S = d*b²/6 = 11.25 * 1.5² / 6 = 4.22
    // Normal capacity much higher despite lower flat use factor
    
    expect(optimization.normalOrientation.momentCapacity).toBeGreaterThan(
      optimization.flatOrientation.momentCapacity
    );
  });

  test('should handle construction grades', () => {
    const optimization = getOrientationOptimization(
      1.5, 3.5, FLAT_USE_LUMBER_GRADES.CONSTRUCTION
    );
    
    expect(optimization.normalOrientation.factor).toBe(1.0);
    expect(optimization.flatOrientation.factor).toBe(0.4);
    expect(optimization.recommendedOrientation).toBe('normal');
  });

  test('should handle square members', () => {
    const optimization = getOrientationOptimization(
      3.5, 3.5, FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL
    );
    
    expect(optimization.aspectRatio).toBe(1.0);
    expect(optimization.normalOrientation.momentCapacity).toBeCloseTo(
      optimization.flatOrientation.momentCapacity, 3
    );
  });
});

describe('Edge Cases and Integration Tests', () => {
  test('should handle boundary dimension values', () => {
    const boundaryTests = [
      { width: 3.5, depth: 4.0 },
      { width: 4.0, depth: 5.0 },
      { width: 4.5, depth: 6.0 },
      { width: 5.0, depth: 8.0 },
      { width: 1.5, depth: 12.0 },
      { width: 1.5, depth: 14.0 },
    ];

    boundaryTests.forEach(test => {
      const input: FlatUseFactorInput = {
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
        isLoadOnWideFace: true,
      };
      
      expect(() => getFlatUseFactor(input)).not.toThrow();
      const factor = getFlatUseFactor(input);
      expect(typeof factor).toBe('number');
      expect(factor).toBeGreaterThan(0);
    });
  });

  test('should provide consistent results across multiple calls', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 7.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };

    const results = Array(10).fill(null).map(() => ({
      flatUseFactor: getFlatUseFactor(input),
      adjustedValue: applyFlatUseFactor(1000, input),
      analysis: getFlatUseFactorAnalysis(input),
    }));

    // All results should be identical
    results.forEach(result => {
      expect(result.flatUseFactor).toBe(results[0].flatUseFactor);
      expect(result.adjustedValue).toBe(results[0].adjustedValue);
      expect(result.analysis.flatUseFactor).toBe(results[0].analysis.flatUseFactor);
    });
  });

  test('should maintain mathematical relationships', () => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 5.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };

    const flatUseFactor = getFlatUseFactor(input);
    const referenceValue = 1000;
    const adjustedValue = applyFlatUseFactor(referenceValue, input);
    const expectedValue = referenceValue * flatUseFactor;
    
    expect(adjustedValue).toBeCloseTo(expectedValue, 6);
  });

  test('should handle all defined lumber grades', () => {
    const allGrades = Object.values(FLAT_USE_LUMBER_GRADES);
    
    allGrades.forEach(grade => {
      const input: FlatUseFactorInput = {
        width: 1.5,
        depth: 5.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: grade,
        isLoadOnWideFace: true,
      };
      
      expect(() => getFlatUseFactor(input)).not.toThrow();
      const factor = getFlatUseFactor(input);
      expect(typeof factor).toBe('number');
      expect(factor).toBeGreaterThan(0);
    });
  });

  test('should handle extreme dimension ratios', () => {
    const extremeTests = [
      { width: 0.75, depth: 15, name: 'very_thin_deep' },
      { width: 12, depth: 12, name: 'square_large' },
      { width: 8, depth: 2, name: 'wide_shallow' },
    ];

    extremeTests.forEach(test => {
      const input: FlatUseFactorInput = {
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
        isLoadOnWideFace: true,
      };
      
      const validation = validateFlatUseFactorInput(input);
      if (validation.isValid) {
        const factor = getFlatUseFactor(input);
        expect(typeof factor).toBe('number');
        expect(factor).toBeGreaterThanOrEqual(0);
      }
    });
  });
}); 