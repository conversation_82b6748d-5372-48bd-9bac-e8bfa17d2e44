/**
 * Flat Use Factor Usage Examples
 * 
 * This file demonstrates how to use the flat use factor functionality for
 * various lumber orientation scenarios in structural wood design.
 * 
 * @fileoverview Examples of flat use factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  getFlatUseFactor,
  applyFlatUseFactor,
  getFlatUseFactorAnalysis,
  getMultipleFlatUseFactors,
  getOrientationOptimization,
  isDesignValueAffectedByFlatUse,
  FLAT_USE_LUMBER_GRADES,
  type FlatUseFactorInput,
} from './flat-use-factor';

import { DESIGN_VALUE_TYPES } from '../../constants';

/**
 * Example 1: Basic flat use factor calculation for a 2x10 joist
 */
function example1_Basic2x10Joist() {
  console.log('\n=== Example 1: 2x10 Joist - Normal vs Flat Orientation ===');
  
  // Normal orientation (strong axis bending)
  const normalInput: FlatUseFactorInput = {
    width: 1.5,    // 2" nominal = 1.5" actual
    depth: 9.25,   // 10" nominal = 9.25" actual
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: FLAT_USE_LUMBER_GRADES.NO_2,
    isLoadOnWideFace: false,
  };
  
  // Flat orientation (weak axis bending)
  const flatInput: FlatUseFactorInput = {
    ...normalInput,
    isLoadOnWideFace: true,
  };
  
  const normalFactor = getFlatUseFactor(normalInput);
  const flatFactor = getFlatUseFactor(flatInput);
  
  console.log(`Normal orientation flat use factor: ${normalFactor}`);
  console.log(`Flat orientation flat use factor: ${flatFactor}`);
  
  // Apply to reference bending value
  const referenceFb = 900; // psi
  const normalAdjustedFb = applyFlatUseFactor(referenceFb, normalInput);
  const flatAdjustedFb = applyFlatUseFactor(referenceFb, flatInput);
  
  console.log(`Reference Fb: ${referenceFb} psi`);
  console.log(`Normal orientation adjusted Fb: ${normalAdjustedFb} psi`);
  console.log(`Flat orientation adjusted Fb: ${flatAdjustedFb} psi`);
  console.log(`Flat use factor improvement: ${((flatFactor - normalFactor) / normalFactor * 100).toFixed(1)}%`);
}

/**
 * Example 2: Comparing flat use factors across different lumber sizes
 */
function example2_SizeComparison() {
  console.log('\n=== Example 2: Flat Use Factor Size Comparison ===');
  
  const sizes = [
    { name: '2x6', width: 1.5, depth: 5.5 },
    { name: '2x8', width: 1.5, depth: 7.25 },
    { name: '2x10', width: 1.5, depth: 9.25 },
    { name: '2x12', width: 1.5, depth: 11.25 },
    { name: '4x6', width: 3.5, depth: 5.5 },
    { name: '4x8', width: 3.5, depth: 7.25 },
    { name: '4x10', width: 3.5, depth: 9.25 },
    { name: '6x8', width: 5.5, depth: 7.25 },
  ];
  
  console.log('Size\tFlat Use Factor\tCapacity Gain');
  console.log('------------------------------------');
  
  sizes.forEach(size => {
    const input: FlatUseFactorInput = {
      width: size.width,
      depth: size.depth,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    
    const factor = getFlatUseFactor(input);
    const capacityGain = ((factor - 1.0) * 100).toFixed(0);
    
    console.log(`${size.name}\t${factor}\t\t+${capacityGain}%`);
  });
}

/**
 * Example 3: Grade comparison for flat use factors
 */
function example3_GradeComparison() {
  console.log('\n=== Example 3: Grade Comparison for 2x8 Flat Use ===');
  
  const grades = [
    { name: 'Select Structural', grade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL },
    { name: 'No.1 & Better', grade: FLAT_USE_LUMBER_GRADES.NO_1_AND_BETTER },
    { name: 'No.2', grade: FLAT_USE_LUMBER_GRADES.NO_2 },
    { name: 'Stud', grade: FLAT_USE_LUMBER_GRADES.STUD },
    { name: 'Construction', grade: FLAT_USE_LUMBER_GRADES.CONSTRUCTION },
    { name: 'Standard', grade: FLAT_USE_LUMBER_GRADES.STANDARD },
    { name: 'Utility', grade: FLAT_USE_LUMBER_GRADES.UTILITY },
  ];
  
  grades.forEach(gradeInfo => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 7.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: gradeInfo.grade,
      isLoadOnWideFace: true,
    };
    
    const factor = getFlatUseFactor(input);
    console.log(`${gradeInfo.name}: Cfu = ${factor}`);
  });
}

/**
 * Example 4: Comprehensive analysis for design decisions
 */
function example4_ComprehensiveAnalysis() {
  console.log('\n=== Example 4: Comprehensive Analysis for 2x12 Beam ===');
  
  const input: FlatUseFactorInput = {
    width: 1.5,
    depth: 11.25,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
    isLoadOnWideFace: true,
  };
  
  const analysis = getFlatUseFactorAnalysis(input);
  
  console.log('Analysis Results:');
  console.log(`- Flat use factor (Cfu): ${analysis.flatUseFactor}`);
  console.log(`- Dimension category: ${analysis.dimensionCategory}`);
  console.log(`- Grade category: ${analysis.gradeCategory}`);
  console.log(`- Aspect ratio: ${analysis.aspectRatio.toFixed(1)}`);
  console.log(`- Wide orientation suitable: ${analysis.isWideOrientation}`);
  console.log(`- Applicable to bending: ${analysis.applicableDesignValue}`);
  
  if (analysis.validation.warnings.length > 0) {
    console.log('Warnings:');
    analysis.validation.warnings.forEach(warning => console.log(`  - ${warning}`));
  }
}

/**
 * Example 5: Multiple orientation analysis
 */
function example5_MultipleOrientations() {
  console.log('\n=== Example 5: Multiple Orientation Analysis ===');
  
  const baseInput = {
    width: 1.5,
    depth: 9.25,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: FLAT_USE_LUMBER_GRADES.NO_2,
  };
  
  const orientations = [
    { name: 'strong_axis', isLoadOnWideFace: false },
    { name: 'weak_axis', isLoadOnWideFace: true },
  ];
  
  const factors = getMultipleFlatUseFactors(baseInput, orientations);
  
  console.log('Orientation Analysis:');
  Object.entries(factors).forEach(([orientation, factor]) => {
    console.log(`${orientation}: Cfu = ${factor}`);
  });
  
  const improvement = ((factors.weak_axis - factors.strong_axis) / factors.strong_axis * 100);
  console.log(`Flat use improvement: ${improvement.toFixed(1)}%`);
}

/**
 * Example 6: Orientation optimization for maximum capacity
 */
function example6_OrientationOptimization() {
  console.log('\n=== Example 6: Orientation Optimization Study ===');
  
  const members = [
    { name: '2x8', width: 1.5, depth: 7.25 },
    { name: '2x10', width: 1.5, depth: 9.25 },
    { name: '2x12', width: 1.5, depth: 11.25 },
    { name: '4x8', width: 3.5, depth: 7.25 },
    { name: '6x6', width: 5.5, depth: 5.5 },
  ];
  
  console.log('Member\tRecommended\tCapacity Diff\tAspect Ratio');
  console.log('------------------------------------------------');
  
  members.forEach(member => {
    const optimization = getOrientationOptimization(
      member.width,
      member.depth,
      FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL
    );
    
    console.log(
      `${member.name}\t${optimization.recommendedOrientation}\t\t` +
      `${optimization.capacityImprovement.toFixed(1)}%\t\t${optimization.aspectRatio.toFixed(1)}`
    );
  });
}

/**
 * Example 7: Design value applicability check
 */
function example7_DesignValueApplicability() {
  console.log('\n=== Example 7: Design Value Applicability ===');
  
  const designValues = [
    { name: 'Bending (Fb)', type: DESIGN_VALUE_TYPES.BENDING },
    { name: 'Tension Parallel (Ft)', type: DESIGN_VALUE_TYPES.TENSION_PARALLEL },
    { name: 'Compression Parallel (Fc)', type: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL },
    { name: 'Shear (Fv)', type: DESIGN_VALUE_TYPES.SHEAR },
    { name: 'Modulus of Elasticity (E)', type: DESIGN_VALUE_TYPES.E },
  ];
  
  console.log('Design Value\t\tFlat Use Factor Applies');
  console.log('--------------------------------------------');
  
  designValues.forEach(dv => {
    const applies = isDesignValueAffectedByFlatUse(dv.type);
    console.log(`${dv.name}\t${applies ? 'Yes' : 'No'}`);
  });
}

/**
 * Example 8: Construction grade special considerations
 */
function example8_ConstructionGradeConsiderations() {
  console.log('\n=== Example 8: Construction Grade Special Considerations ===');
  
  const constructionGrades = [
    FLAT_USE_LUMBER_GRADES.CONSTRUCTION,
    FLAT_USE_LUMBER_GRADES.STANDARD,
    FLAT_USE_LUMBER_GRADES.UTILITY,
  ];
  
  const standardGrades = [
    FLAT_USE_LUMBER_GRADES.SELECT_STRUCTURAL,
    FLAT_USE_LUMBER_GRADES.NO_1,
    FLAT_USE_LUMBER_GRADES.NO_2,
  ];
  
  console.log('Comparing 2x4 members:');
  console.log('Grade Category\t\tFlat Use Factor');
  console.log('--------------------------------------');
  
  // Construction grades
  constructionGrades.forEach(grade => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: grade,
      isLoadOnWideFace: true,
    };
    
    const factor = getFlatUseFactor(input);
    console.log(`${grade}\t\t${factor}`);
  });
  
  // Standard grades for comparison
  console.log('\nStandard grades for comparison:');
  standardGrades.forEach(grade => {
    const input: FlatUseFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: grade,
      isLoadOnWideFace: true,
    };
    
    const factor = getFlatUseFactor(input);
    console.log(`${grade}\t\t${factor}`);
  });
}

/**
 * Example 9: Practical capacity calculation
 */
function example9_PracticalCapacityCalculation() {
  console.log('\n=== Example 9: Practical Moment Capacity Calculation ===');
  
  const member = {
    width: 1.5,    // 2x10
    depth: 9.25,
    referenceFb: 1000, // psi
  };
  
  // Calculate section moduli
  const normalSectionModulus = (member.width * Math.pow(member.depth, 2)) / 6;
  const flatSectionModulus = (member.depth * Math.pow(member.width, 2)) / 6;
  
  // Get flat use factors
  const normalInput: FlatUseFactorInput = {
    width: member.width,
    depth: member.depth,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: FLAT_USE_LUMBER_GRADES.NO_2,
    isLoadOnWideFace: false,
  };
  
  const flatInput: FlatUseFactorInput = {
    ...normalInput,
    isLoadOnWideFace: true,
  };
  
  const normalFactor = getFlatUseFactor(normalInput);
  const flatFactor = getFlatUseFactor(flatInput);
  
  // Calculate moment capacities
  const normalMoment = member.referenceFb * normalFactor * normalSectionModulus / 12; // ft-lbs
  const flatMoment = member.referenceFb * flatFactor * flatSectionModulus / 12; // ft-lbs
  
  console.log('2x10 No.2 Grade Moment Capacity:');
  console.log(`Normal orientation: ${normalMoment.toFixed(0)} ft-lbs`);
  console.log(`Flat orientation: ${flatMoment.toFixed(0)} ft-lbs`);
  console.log(`Ratio (normal/flat): ${(normalMoment / flatMoment).toFixed(1)}:1`);
  
  const betterOrientation = normalMoment > flatMoment ? 'Normal' : 'Flat';
  console.log(`Recommended orientation: ${betterOrientation}`);
}

/**
 * Run all examples
 */
function runAllExamples() {
  console.log('FLAT USE FACTOR CALCULATION EXAMPLES');
  console.log('===================================');
  
  example1_Basic2x10Joist();
  example2_SizeComparison();
  example3_GradeComparison();
  example4_ComprehensiveAnalysis();
  example5_MultipleOrientations();
  example6_OrientationOptimization();
  example7_DesignValueApplicability();
  example8_ConstructionGradeConsiderations();
  example9_PracticalCapacityCalculation();
  
  console.log('\n=== Examples Complete ===');
}

// Export examples for use in other modules
export {
  example1_Basic2x10Joist,
  example2_SizeComparison,
  example3_GradeComparison,
  example4_ComprehensiveAnalysis,
  example5_MultipleOrientations,
  example6_OrientationOptimization,
  example7_DesignValueApplicability,
  example8_ConstructionGradeConsiderations,
  example9_PracticalCapacityCalculation,
  runAllExamples,
};

// Run examples if this module is executed directly
if (require.main === module) {
  runAllExamples();
} 