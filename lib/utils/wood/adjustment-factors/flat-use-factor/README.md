# Flat Use Factor (Cfu)

## Overview

This module implements the flat use factor (Cfu) calculations according to the National Design Specification (NDS) for Wood Construction, Section 4.3.7. The flat use factor adjusts bending design values when dimension lumber is loaded perpendicular to its wide face (used "flat" or "on edge").

## Key Concepts

### When Flat Use Factor Applies

The flat use factor applies when:
- **Dimension lumber** (2" to 4" thick) is used
- Member is loaded **perpendicular to its wide face**
- Bending occurs about the **weak axis** (narrow dimension)
- Applying to **bending design values (Fb)**

### Orientation Definitions

1. **Strong Axis Bending** (Normal Use):
   - Load applied to narrow face (2" dimension)
   - Bending about strong axis (wide dimension provides depth)
   - No flat use factor applied (Cfu = 1.0)

2. **Weak Axis Bending** (Flat Use):
   - Load applied to wide face
   - Bending about weak axis (narrow dimension provides depth)  
   - Flat use factor applies (Cfu < 1.0)

### Flat Use Factor Values

- **Cfu < 1.0** (flat use factor is always less than 1.0)
- Values depend on lumber grade and nominal dimensions
- Accounts for reduced bending strength when used flat

## API Reference

### Main Functions

#### `getFlatUseFactor(input)`

Calculates the flat use factor for a given member configuration.

**Parameters:**
- `input.lumberGrade`: Grade of lumber ('select_structural', 'no_1', 'no_2', etc.)
- `input.nominalWidth`: Nominal width (2, 4, 6, 8, 10, 12, 14)
- `input.nominalDepth`: Nominal depth (2, 4, 6, 8, 10, 12, 14)
- `input.loadingOrientation`: 'flat_use' or 'normal_use'
- `input.designValue`: Design value type ('Fb')

**Returns:**
- `factor`: The flat use factor (≤ 1.0)
- `applicable`: Whether the factor applies
- `orientation`: Loading orientation analysis
- `explanation`: Detailed explanation

#### `validateFlatUseFactorInput(input)`

Validates input parameters and provides detailed validation results.

#### `getFlatUseFactorAnalysis(input)`

Provides comprehensive analysis including orientation determination and factor selection.

#### `getOrientationOptimization(constraints)`

Suggests optimal member orientation for given loading conditions.

### Usage Examples

```typescript
import { getFlatUseFactor } from './flat-use-factor';

// Example 1: 2x8 used flat (weak axis bending)
const flatUse = getFlatUseFactor({
  lumberGrade: 'no_2',
  nominalWidth: 2,
  nominalDepth: 8,
  loadingOrientation: 'flat_use',
  designValue: 'Fb'
});

console.log(flatUse.factor); // e.g., 1.15

// Example 2: Same member in normal orientation
const normalUse = getFlatUseFactor({
  lumberGrade: 'no_2',
  nominalWidth: 2,
  nominalDepth: 8,
  loadingOrientation: 'normal_use', 
  designValue: 'Fb'
});

console.log(normalUse.factor); // 1.0 (no flat use factor)
```

### Optimization Functions

#### `getOrientationOptimization(input)`

Analyzes both orientations to determine optimal member usage.

**Parameters:**
- `input.lumberGrade`: Lumber grade
- `input.nominalWidth`: Nominal width
- `input.nominalDepth`: Nominal depth
- `input.loadMagnitude`: Applied load magnitude
- `input.span`: Member span length

**Returns:**
- `strongAxis`: Analysis for normal orientation
- `weakAxis`: Analysis for flat use orientation
- `recommendation`: Recommended orientation

## Design Considerations

### When to Use Flat Orientation

- **Space constraints** require shallow members
- **Lateral stability** is provided by adjacent construction
- **Load magnitude** is relatively low
- **Span length** is short

### When to Avoid Flat Orientation

- **High bending loads** where strength reduction is critical
- **Long spans** where deflection becomes governing
- **Lack of lateral support** leading to instability
- **Efficient material usage** is prioritized

## Files

- `flat-use-factor.ts` - Main implementation
- `flat-use-factor.test.ts` - Comprehensive test suite
- `flat-use-factor-examples.ts` - Usage examples and scenarios
- `README.md` - This documentation

## References

- NDS 2018, Section 4.3.7 - Flat Use Factor, Cfu
- NDS 2018, Table 4A - Adjustment Factors for Sawn Lumber
- NDS Supplement - Design Values for Visually Graded Lumber 