/**
 * Wet Service Factor (CM) Calculations
 * 
 * Implements NDS 4.1.4 Wet Service Factor for adjusting reference design 
 * values when lumber is used where moisture content will exceed specified
 * thresholds for extended time periods.
 * 
 * Based on:
 * - NDS Tables 4A-4F: Sawn Lumber Wet Service Factors
 * - NDS Tables 5A-5D: Glued Laminated Timber Wet Service Factors
 * 
 * @fileoverview Wet service factor calculations for wood structural design
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
} from "../../constants";

/**
 * Lumber categories for wet service factor determination
 */
export const WET_SERVICE_LUMBER_CATEGORIES = {
  // Sawn lumber categories
  DIMENSION_LUMBER: 'dimension_lumber',           // 2" to 4" thick
  TIMBERS: 'timbers',                            // 5" x 5" and larger
  DECKING: 'decking',                            // Structural decking
  BEAMS_AND_STRINGERS: 'beams_and_stringers',    // 5" thick, width > 2" greater than thickness
  POSTS_AND_TIMBERS: 'posts_and_timbers',        // 5" x 5" and larger, width ≤ 2" greater than thickness
  
  // Engineered lumber categories  
  GLUED_LAMINATED_TIMBER: 'glued_laminated_timber', // Glulam
} as const;

export type WetServiceLumberCategory = typeof WET_SERVICE_LUMBER_CATEGORIES[keyof typeof WET_SERVICE_LUMBER_CATEGORIES];

/**
 * Species groups for wet service factor determination
 */
export const WET_SERVICE_SPECIES_GROUPS = {
  // Southern Pine species requiring special treatment
  SOUTHERN_PINE: 'southern_pine',
  MIXED_SOUTHERN_PINE: 'mixed_southern_pine',
  DENSE_STRUCTURAL_86: 'dense_structural_86',
  DENSE_STRUCTURAL_72: 'dense_structural_72', 
  DENSE_STRUCTURAL_65: 'dense_structural_65',
  
  // General species groups
  OTHER_SPECIES: 'other_species',
} as const;

export type WetServiceSpeciesGroup = typeof WET_SERVICE_SPECIES_GROUPS[keyof typeof WET_SERVICE_SPECIES_GROUPS];

/**
 * Moisture content thresholds for wet service conditions
 */
export const WET_SERVICE_MOISTURE_THRESHOLDS = {
  SAWN_LUMBER: 19,      // Moisture content > 19% for sawn lumber
  GLUED_LAMINATED: 16,  // Moisture content ≥ 16% for glulam
} as const;

/**
 * Wet service factors for sawn lumber (NDS Tables 4A-4F)
 */
export const SAWN_LUMBER_WET_SERVICE_FACTORS = {
  // Standard factors for most sawn lumber (Tables 4A, 4B, 4C, 4F)
  STANDARD: {
    [DESIGN_VALUE_TYPES.BENDING]: 0.85,                    // Fb - with conditions
    [DESIGN_VALUE_TYPES.TENSION_PARALLEL]: 1.0,            // Ft
    [DESIGN_VALUE_TYPES.SHEAR]: 0.97,                      // Fv
    [DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]: 0.67,  // Fc⊥
    [DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]: 0.8,        // Fc - with conditions
    [DESIGN_VALUE_TYPES.E]: 0.9,                          // E
    [DESIGN_VALUE_TYPES.E_MIN]: 0.9,                       // Emin
  },
  
  // Factors for timbers (Table 4D)
  TIMBERS: {
    [DESIGN_VALUE_TYPES.BENDING]: 1.0,                     // Fb
    [DESIGN_VALUE_TYPES.TENSION_PARALLEL]: 1.0,            // Ft
    [DESIGN_VALUE_TYPES.SHEAR]: 1.0,                       // Fv
    [DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]: 0.67,  // Fc⊥
    [DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]: 0.91,       // Fc
    [DESIGN_VALUE_TYPES.E]: 1.0,                          // E
    [DESIGN_VALUE_TYPES.E_MIN]: 1.0,                       // Emin
  },
  
  // Factors for decking (Table 4E)
  DECKING: {
    [DESIGN_VALUE_TYPES.BENDING]: 0.85,                    // Fb - with conditions
    [DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]: 0.67,  // Fc⊥
    [DESIGN_VALUE_TYPES.E]: 0.9,                          // E
    [DESIGN_VALUE_TYPES.E_MIN]: 0.9,                       // Emin
  },
} as const;

/**
 * Wet service factors for glued laminated timber (NDS Tables 5A, 5B, 5D)
 */
export const GLULAM_WET_SERVICE_FACTORS = {
  [DESIGN_VALUE_TYPES.BENDING]: 0.8,                     // Fb
  [DESIGN_VALUE_TYPES.TENSION_PARALLEL]: 0.8,            // Ft
  [DESIGN_VALUE_TYPES.SHEAR]: 0.875,                     // Fv
  [DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]: 0.53,  // Fc⊥
  [DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]: 0.73,       // Fc
  [DESIGN_VALUE_TYPES.E]: 0.833,                        // E
  [DESIGN_VALUE_TYPES.E_MIN]: 0.833,                     // Emin
} as const;

/**
 * Special condition thresholds for wet service factors
 */
export const WET_SERVICE_SPECIAL_CONDITIONS = {
  // Bending factor conditions (Fb)(CF) ≤ 1,150 psi → CM = 1.0
  BENDING_THRESHOLD_PSI: 1150,
  
  // Compression parallel factor conditions (Fc) ≤ 750 psi → CM = 1.0  
  COMPRESSION_THRESHOLD_PSI: 750,
} as const;

/**
 * Input parameters for wet service factor calculation
 */
export interface WetServiceFactorInput {
  /** Moisture content percentage */
  moistureContent: number;
  
  /** Type of design value (Fb, Ft, Fc, etc.) */
  designValueType: DesignValueType;
  
  /** Lumber category */
  lumberCategory: WetServiceLumberCategory;
  
  /** Species group */
  speciesGroup?: WetServiceSpeciesGroup;
  
  /** Reference design value (for special condition checks) */
  referenceDesignValue?: number;
  
  /** Size factor (CF) for bending calculations */
  sizeFactor?: number;
}

/**
 * Validation result for wet service factor input
 */
export interface WetServiceFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Comprehensive wet service factor analysis result
 */
export interface WetServiceFactorAnalysis {
  input: WetServiceFactorInput;
  wetServiceFactor: number;
  isWetServiceRequired: boolean;
  moistureThreshold: number;
  specialConditionApplied?: string;
  lumberFactorSource: string;
  validation: WetServiceFactorValidation;
}

/**
 * Validates wet service factor input parameters
 * 
 * @param input - Input parameters to validate
 * @returns Validation result with errors and warnings
 * 
 * @example
 * ```typescript
 * const input = {
 *   moistureContent: 25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER
 * };
 * const validation = validateWetServiceFactorInput(input);
 * if (!validation.isValid) {
 *   console.log('Errors:', validation.errors);
 * }
 * ```
 */
export function validateWetServiceFactorInput(
  input: WetServiceFactorInput
): WetServiceFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate moisture content
  if (typeof input.moistureContent !== 'number' || 
      isNaN(input.moistureContent) || 
      !isFinite(input.moistureContent)) {
    errors.push('Moisture content must be a valid number');
  } else {
    if (input.moistureContent < 0) {
      errors.push('Moisture content cannot be negative');
    }
    if (input.moistureContent > 100) {
      warnings.push('Moisture content exceeds 100% - verify measurement');
    }
    if (input.moistureContent < 10) {
      warnings.push('Moisture content is very low - wet service factor may not be needed');
    }
  }
  
  // Validate design value type
  if (!Object.values(DESIGN_VALUE_TYPES).includes(input.designValueType)) {
    errors.push(`Invalid design value type: ${input.designValueType}`);
  }
  
  // Validate lumber category
  if (!Object.values(WET_SERVICE_LUMBER_CATEGORIES).includes(input.lumberCategory)) {
    errors.push(`Invalid lumber category: ${input.lumberCategory}`);
  }
  
  // Validate species group if provided
  if (input.speciesGroup && 
      !Object.values(WET_SERVICE_SPECIES_GROUPS).includes(input.speciesGroup)) {
    errors.push(`Invalid species group: ${input.speciesGroup}`);
  }
  
  // Validate reference design value if provided
  if (input.referenceDesignValue !== undefined) {
    if (typeof input.referenceDesignValue !== 'number' || 
        isNaN(input.referenceDesignValue) || 
        !isFinite(input.referenceDesignValue)) {
      errors.push('Reference design value must be a valid number');
    } else if (input.referenceDesignValue < 0) {
      errors.push('Reference design value cannot be negative');
    }
  }
  
  // Validate size factor if provided
  if (input.sizeFactor !== undefined) {
    if (typeof input.sizeFactor !== 'number' || 
        isNaN(input.sizeFactor) || 
        !isFinite(input.sizeFactor)) {
      errors.push('Size factor must be a valid number');
    } else if (input.sizeFactor <= 0) {
      errors.push('Size factor must be positive');
    } else if (input.sizeFactor > 2.0) {
      warnings.push('Size factor is unusually high - verify calculation');
    }
  }
  
  // Check for compatibility issues
  if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.DECKING) {
    const deckingApplicableValues: DesignValueType[] = [
      DESIGN_VALUE_TYPES.BENDING,
      DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
      DESIGN_VALUE_TYPES.E,
      DESIGN_VALUE_TYPES.E_MIN
    ];
    if (!deckingApplicableValues.includes(input.designValueType)) {
      warnings.push(`Design value ${input.designValueType} may not be applicable for decking`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Determines if wet service conditions apply based on moisture content and lumber type
 * 
 * @param moistureContent - Moisture content percentage
 * @param lumberCategory - Category of lumber
 * @returns True if wet service conditions apply
 * 
 * @example
 * ```typescript
 * const isWetService = isWetServiceRequired(25, WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER);
 * console.log(isWetService); // true (25% > 19% threshold)
 * ```
 */
export function isWetServiceRequired(
  moistureContent: number,
  lumberCategory: WetServiceLumberCategory
): boolean {
  if (lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER) {
    return moistureContent >= WET_SERVICE_MOISTURE_THRESHOLDS.GLUED_LAMINATED;
  }
  
  return moistureContent > WET_SERVICE_MOISTURE_THRESHOLDS.SAWN_LUMBER;
}

/**
 * Gets the moisture content threshold for wet service conditions
 * 
 * @param lumberCategory - Category of lumber
 * @returns Moisture content threshold percentage
 */
export function getWetServiceMoistureThreshold(
  lumberCategory: WetServiceLumberCategory
): number {
  if (lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER) {
    return WET_SERVICE_MOISTURE_THRESHOLDS.GLUED_LAMINATED;
  }
  
  return WET_SERVICE_MOISTURE_THRESHOLDS.SAWN_LUMBER;
}

/**
 * Determines if special conditions apply for wet service factors
 * 
 * @param designValueType - Type of design value
 * @param referenceValue - Reference design value
 * @param sizeFactor - Size factor (CF) for bending calculations
 * @returns True if special conditions result in CM = 1.0
 */
export function checkWetServiceSpecialConditions(
  designValueType: DesignValueType,
  referenceValue?: number,
  sizeFactor?: number
): { applies: boolean; reason?: string } {
  if (!referenceValue) {
    return { applies: false };
  }
  
  // Bending: (Fb)(CF) ≤ 1,150 psi → CM = 1.0
  if (designValueType === DESIGN_VALUE_TYPES.BENDING && sizeFactor) {
    const adjustedValue = referenceValue * sizeFactor;
    if (adjustedValue <= WET_SERVICE_SPECIAL_CONDITIONS.BENDING_THRESHOLD_PSI) {
      return { 
        applies: true, 
        reason: `(Fb)(CF) = ${adjustedValue.toFixed(0)} ≤ ${WET_SERVICE_SPECIAL_CONDITIONS.BENDING_THRESHOLD_PSI} psi` 
      };
    }
  }
  
  // Compression parallel: Fc ≤ 750 psi → CM = 1.0
  if (designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL) {
    if (referenceValue <= WET_SERVICE_SPECIAL_CONDITIONS.COMPRESSION_THRESHOLD_PSI) {
      return { 
        applies: true, 
        reason: `Fc = ${referenceValue.toFixed(0)} ≤ ${WET_SERVICE_SPECIAL_CONDITIONS.COMPRESSION_THRESHOLD_PSI} psi` 
      };
    }
  }
  
  return { applies: false };
}

/**
 * Gets wet service factor for specified lumber and design value
 * 
 * @param input - Input parameters for wet service factor calculation
 * @returns Wet service factor (CM)
 * @throws Error if input validation fails
 * 
 * @example
 * ```typescript
 * const input = {
 *   moistureContent: 25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
 *   referenceDesignValue: 1000,
 *   sizeFactor: 1.2
 * };
 * const factor = getWetServiceFactor(input);
 * console.log(factor); // 0.85 or 1.0 depending on special conditions
 * ```
 */
export function getWetServiceFactor(input: WetServiceFactorInput): number {
  const validation = validateWetServiceFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid wet service factor input: ${validation.errors.join(', ')}`);
  }
  
  // Check if wet service conditions apply
  if (!isWetServiceRequired(input.moistureContent, input.lumberCategory)) {
    return 1.0; // No wet service factor needed
  }
  
  // Check for special conditions that override standard factors
  const specialCondition = checkWetServiceSpecialConditions(
    input.designValueType,
    input.referenceDesignValue,
    input.sizeFactor
  );
  
  if (specialCondition.applies) {
    return 1.0;
  }
  
  // Apply factors based on lumber category
  if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER) {
    return GLULAM_WET_SERVICE_FACTORS[input.designValueType] ?? 1.0;
  }
  
  // Handle Southern Pine species that use tabulated green values
  if (input.speciesGroup) {
    const southernPineSpecies: WetServiceSpeciesGroup[] = [
      WET_SERVICE_SPECIES_GROUPS.SOUTHERN_PINE,
      WET_SERVICE_SPECIES_GROUPS.MIXED_SOUTHERN_PINE,
      WET_SERVICE_SPECIES_GROUPS.DENSE_STRUCTURAL_86,
      WET_SERVICE_SPECIES_GROUPS.DENSE_STRUCTURAL_72,
      WET_SERVICE_SPECIES_GROUPS.DENSE_STRUCTURAL_65
    ];
    
    if (southernPineSpecies.includes(input.speciesGroup)) {
      // For timbers with Southern Pine, no adjustment needed
      if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.TIMBERS) {
        return 1.0;
      }
      
      // For decking with surfaced dry Southern Pine, use tabulated green values (no adjustment)
      if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.DECKING) {
        return 1.0;
      }
    }
  }
  
  // Apply standard factors based on lumber category
  switch (input.lumberCategory) {
    case WET_SERVICE_LUMBER_CATEGORIES.TIMBERS:
      return SAWN_LUMBER_WET_SERVICE_FACTORS.TIMBERS[input.designValueType] ?? 1.0;
      
    case WET_SERVICE_LUMBER_CATEGORIES.DECKING: {
      const deckingFactor = (SAWN_LUMBER_WET_SERVICE_FACTORS.DECKING as any)[input.designValueType];
      return deckingFactor !== undefined ? deckingFactor : 1.0;
    }
      
    default:
      // Standard factors for dimension lumber, beams and stringers, posts and timbers
      return SAWN_LUMBER_WET_SERVICE_FACTORS.STANDARD[input.designValueType] ?? 1.0;
  }
}

/**
 * Applies wet service factor to a reference design value
 * 
 * @param referenceValue - Reference design value
 * @param input - Input parameters for wet service factor calculation
 * @returns Adjusted design value
 * @throws Error if reference value is invalid or input validation fails
 * 
 * @example
 * ```typescript
 * const adjustedValue = applyWetServiceFactor(1200, {
 *   moistureContent: 25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER
 * });
 * console.log(adjustedValue); // 1020 (1200 × 0.85)
 * ```
 */
export function applyWetServiceFactor(
  referenceValue: number,
  input: WetServiceFactorInput
): number {
  if (typeof referenceValue !== 'number' || 
      isNaN(referenceValue) || 
      !isFinite(referenceValue) ||
      referenceValue < 0) {
    throw new Error('Reference value must be a non-negative finite number');
  }
  
  const wetServiceFactor = getWetServiceFactor(input);
  return referenceValue * wetServiceFactor;
}

/**
 * Calculates wet service factors for multiple design values
 * 
 * @param baseInput - Base input parameters (without designValueType)
 * @param designValues - Array of design value types to calculate
 * @returns Object mapping design value types to wet service factors
 * 
 * @example
 * ```typescript
 * const factors = getMultipleWetServiceFactors(
 *   {
 *     moistureContent: 25,
 *     lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER
 *   },
 *   [DESIGN_VALUE_TYPES.BENDING, DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]
 * );
 * console.log(factors); // { Fb: 0.85, Fc: 0.8 }
 * ```
 */
export function getMultipleWetServiceFactors(
  baseInput: Omit<WetServiceFactorInput, 'designValueType'>,
  designValues: DesignValueType[]
): Record<DesignValueType, number> {
  const result: Record<string, number> = {};
  
  for (const designValue of designValues) {
    try {
      const input: WetServiceFactorInput = {
        ...baseInput,
        designValueType: designValue,
      };
      result[designValue] = getWetServiceFactor(input);
    } catch (error) {
      // Return default factor for invalid design values
      result[designValue] = 1.0;
    }
  }
  
  return result as Record<DesignValueType, number>;
}

/**
 * Provides comprehensive analysis of wet service factor calculation
 * 
 * @param input - Input parameters for analysis
 * @returns Detailed analysis including factor, conditions, and validation
 * 
 * @example
 * ```typescript
 * const analysis = getWetServiceFactorAnalysis({
 *   moistureContent: 25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
 *   referenceDesignValue: 1000,
 *   sizeFactor: 1.15
 * });
 * console.log(analysis.wetServiceFactor); // 1.0 (special condition applied)
 * console.log(analysis.specialConditionApplied); // "(Fb)(CF) = 1150 ≤ 1150 psi"
 * ```
 */
export function getWetServiceFactorAnalysis(
  input: WetServiceFactorInput
): WetServiceFactorAnalysis {
  const validation = validateWetServiceFactorInput(input);
  
  // Provide default values for invalid input
  if (!validation.isValid) {
    return {
      input,
      wetServiceFactor: 1.0,
      isWetServiceRequired: false,
      moistureThreshold: getWetServiceMoistureThreshold(input.lumberCategory),
      lumberFactorSource: 'default (invalid input)',
      validation,
    };
  }
  
  const isWetRequired = isWetServiceRequired(input.moistureContent, input.lumberCategory);
  const moistureThreshold = getWetServiceMoistureThreshold(input.lumberCategory);
  
  let wetServiceFactor = 1.0;
  let lumberFactorSource = 'no adjustment needed';
  let specialConditionApplied: string | undefined;
  
  if (isWetRequired) {
    // Check special conditions first
    const specialCondition = checkWetServiceSpecialConditions(
      input.designValueType,
      input.referenceDesignValue,
      input.sizeFactor
    );
    
    if (specialCondition.applies) {
      wetServiceFactor = 1.0;
      specialConditionApplied = specialCondition.reason;
      lumberFactorSource = 'special condition override';
    } else {
      wetServiceFactor = getWetServiceFactor(input);
      
      // Determine factor source
      if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER) {
        lumberFactorSource = 'NDS Tables 5A, 5B, 5D (glulam)';
      } else if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.TIMBERS) {
        lumberFactorSource = 'NDS Table 4D (timbers)';
      } else if (input.lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.DECKING) {
        lumberFactorSource = 'NDS Table 4E (decking)';
      } else {
        lumberFactorSource = 'NDS Tables 4A, 4B, 4C, 4F (sawn lumber)';
      }
    }
  }
  
  return {
    input,
    wetServiceFactor,
    isWetServiceRequired: isWetRequired,
    moistureThreshold,
    specialConditionApplied,
    lumberFactorSource,
    validation,
  };
}

/**
 * Checks if a design value is affected by wet service conditions
 * 
 * @param designValueType - Type of design value to check
 * @param lumberCategory - Category of lumber
 * @returns True if the design value is affected by wet service conditions
 * 
 * @example
 * ```typescript
 * const isAffected = isDesignValueAffectedByWetService(
 *   DESIGN_VALUE_TYPES.BENDING,
 *   WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER
 * );
 * console.log(isAffected); // true
 * ```
 */
export function isDesignValueAffectedByWetService(
  designValueType: DesignValueType,
  lumberCategory: WetServiceLumberCategory
): boolean {
  if (lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER) {
    return designValueType in GLULAM_WET_SERVICE_FACTORS;
  }
  
  if (lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.TIMBERS) {
    return designValueType in SAWN_LUMBER_WET_SERVICE_FACTORS.TIMBERS;
  }
  
  if (lumberCategory === WET_SERVICE_LUMBER_CATEGORIES.DECKING) {
    return designValueType in SAWN_LUMBER_WET_SERVICE_FACTORS.DECKING;
  }
  
  return designValueType in SAWN_LUMBER_WET_SERVICE_FACTORS.STANDARD;
} 