# Wet Service Factor (CM)

## Overview

This module implements the wet service factor (CM) calculations according to the National Design Specification (NDS) for Wood Construction, Section 4.1.4. The wet service factor adjusts design values when wood members will be used in service conditions where the moisture content exceeds 19%.

## Key Concepts

### When Wet Service Factor Applies

The wet service factor applies when:
- **Moisture content in service ≥ 19%**
- **Any design value** except modulus of elasticity (E)
- **Sawn lumber** and **glued laminated timber**
- **Exposure to high humidity** or **direct moisture**

### Moisture Content Thresholds

- **Dry Service**: MC < 19% (CM = 1.0)
- **Wet Service**: MC ≥ 19% (CM < 1.0)

Common wet service conditions:
- Exterior use without protection from weather
- High humidity environments (pools, greenhouses)
- Ground contact applications
- Marine environments

### Wet Service Factor Values

Different factors apply based on:
- **Design value type** (Fb, Ft, Fc, Fv, Fc⊥)
- **Wood product** (sawn lumber vs. glulam)
- **Species group** (for some applications)

## API Reference

### Main Functions

#### `getWetServiceFactor(input)`

Calculates the wet service factor for given conditions.

**Parameters:**
- `input.moistureContent`: Service moisture content (%)
- `input.designValue`: Design value type ('Fb', 'Ft', 'Fc', 'Fv', 'Fc_perp')
- `input.woodProduct`: Product type ('sawn_lumber', 'glulam')
- `input.lumberCategory`: Category ('dimension_lumber', 'timbers', etc.)
- `input.speciesGroup`: Species group (if applicable)

**Returns:**
- `factor`: The wet service factor (≤ 1.0)
- `applicable`: Whether wet service conditions apply
- `explanation`: Detailed explanation
- `moistureAnalysis`: Moisture condition analysis

#### `validateWetServiceFactorInput(input)`

Validates input parameters and provides detailed validation results.

#### `getWetServiceFactorAnalysis(input)`

Provides comprehensive analysis including moisture thresholds and factor selection.

#### `isWetServiceRequired(moistureContent)`

Determines if wet service factor applies based on moisture content.

#### `getWetServiceMoistureThreshold()`

Returns the moisture content threshold for wet service (19%).

### Usage Examples

```typescript
import { getWetServiceFactor } from './wet-service';

// Example 1: Exterior lumber application
const exterior = getWetServiceFactor({
  moistureContent: 22,
  designValue: 'Fb',
  woodProduct: 'sawn_lumber',
  lumberCategory: 'dimension_lumber'
});

console.log(exterior.factor); // e.g., 0.85

// Example 2: Interior application (dry service)
const interior = getWetServiceFactor({
  moistureContent: 15,
  designValue: 'Fb', 
  woodProduct: 'sawn_lumber',
  lumberCategory: 'dimension_lumber'
});

console.log(interior.factor); // 1.0 (no wet service factor)
```

### Constants and Factor Tables

```typescript
// Moisture thresholds
export const WET_SERVICE_MOISTURE_THRESHOLDS = {
  DRY_SERVICE_MAX: 19,     // MC < 19% = dry service
  WET_SERVICE_MIN: 19      // MC ≥ 19% = wet service
} as const;

// Sawn lumber wet service factors
export const SAWN_LUMBER_WET_SERVICE_FACTORS = {
  FB: 0.85,      // Bending
  FT: 1.0,       // Tension parallel to grain
  FV: 0.97,      // Shear
  FC: 0.67,      // Compression parallel to grain  
  FC_PERP: 0.67  // Compression perpendicular to grain
} as const;
```

## Design Considerations

### Application Conditions

**Dry Service Conditions (CM = 1.0):**
- Interior applications
- Covered exterior applications
- Low humidity environments
- Moisture content maintained below 19%

**Wet Service Conditions (CM < 1.0):**
- Exterior exposure to weather
- High humidity environments
- Ground contact
- Marine applications
- Moisture content 19% or higher

### Material Selection

- Consider **pressure-treated lumber** for wet service conditions
- Use **appropriate species** for moisture exposure
- Consider **protective coatings** or finishes
- Design for **dimensional changes** due to moisture

## Files

- `wet-service.ts` - Main implementation
- `wet-service.test.ts` - Comprehensive test suite
- `README.md` - This documentation

## References

- NDS 2018, Section 4.1.4 - Wet Service Factor, CM
- NDS 2018, Table 4A - Adjustment Factors for Sawn Lumber
- NDS 2018, Section 5.1.4 - Wet Service Factor for Glulam
- AWPA Standards - Wood preservation and moisture 