/**
 * Wet Service Factor (CM) Unit Tests
 * Tests for NDS 4.1.4 Wet Service Factor calculations
 */

import {
  getWetServiceFactor,
  applyWetServiceFactor,
  validateWetServiceFactorInput,
  getMultipleWetServiceFactors,
  getWetServiceFactorAnalysis,
  isWetServiceRequired,
  getWetServiceMoistureThreshold,
  checkWetServiceSpecialConditions,
  isDesignValueAffectedByWetService,
  WetServiceFactorInput,
  WET_SERVICE_LUMBER_CATEGORIES,
  WET_SERVICE_SPECIES_GROUPS,
  WET_SERVICE_MOISTURE_THRESHOLDS,
  SAWN_LUMBER_WET_SERVICE_FACTORS,
  GLULAM_WET_SERVICE_FACTORS,
  WET_SERVICE_SPECIAL_CONDITIONS,
} from "./wet-service";
import { DESIGN_VALUE_TYPES } from "../../constants";

describe("Wet Service Factor (CM) Calculations", () => {
  describe("validateWetServiceFactorInput", () => {
    it("should validate correct input parameters", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = validateWetServiceFactorInput(input);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should detect invalid moisture content values", () => {
      const inputs = [
        {
          moistureContent: NaN,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        },
        {
          moistureContent: Infinity,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        },
        {
          moistureContent: -5,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        },
      ];

      inputs.forEach((input) => {
        const result = validateWetServiceFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it("should warn about extreme moisture content values", () => {
      const lowMoistureInput: WetServiceFactorInput = {
        moistureContent: 5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const highMoistureInput: WetServiceFactorInput = {
        moistureContent: 120,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const lowResult = validateWetServiceFactorInput(lowMoistureInput);
      const highResult = validateWetServiceFactorInput(highMoistureInput);

      expect(lowResult.warnings).toContain(
        "Moisture content is very low - wet service factor may not be needed"
      );
      expect(highResult.warnings).toContain(
        "Moisture content exceeds 100% - verify measurement"
      );
    });

    it("should detect invalid design value types", () => {
      const input = {
        moistureContent: 25,
        designValueType: "invalid_type" as any,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = validateWetServiceFactorInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Invalid design value type: invalid_type");
    });

    it("should detect invalid lumber categories", () => {
      const input = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: "invalid_category" as any,
      };

      const result = validateWetServiceFactorInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Invalid lumber category: invalid_category");
    });

    it("should detect invalid species groups", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        speciesGroup: "invalid_species" as any,
      };

      const result = validateWetServiceFactorInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Invalid species group: invalid_species");
    });

    it("should validate reference design value and size factor", () => {
      const invalidRefValueInput: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        referenceDesignValue: -100,
      };

      const invalidSizeFactorInput: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        sizeFactor: 0,
      };

      const refValueResult = validateWetServiceFactorInput(invalidRefValueInput);
      const sizeFactorResult = validateWetServiceFactorInput(invalidSizeFactorInput);

      expect(refValueResult.isValid).toBe(false);
      expect(refValueResult.errors).toContain("Reference design value cannot be negative");

      expect(sizeFactorResult.isValid).toBe(false);
      expect(sizeFactorResult.errors).toContain("Size factor must be positive");
    });

    it("should warn about decking compatibility", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DECKING,
      };

      const result = validateWetServiceFactorInput(input);

      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings[0]).toContain("may not be applicable for decking");
    });
  });

  describe("isWetServiceRequired", () => {
    it("should correctly identify when wet service is required for sawn lumber", () => {
      expect(
        isWetServiceRequired(25, WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER)
      ).toBe(true); // 25% > 19%
      expect(
        isWetServiceRequired(19, WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER)
      ).toBe(false); // 19% = 19% (not >)
      expect(
        isWetServiceRequired(15, WET_SERVICE_LUMBER_CATEGORIES.TIMBERS)
      ).toBe(false); // 15% < 19%
    });

    it("should correctly identify when wet service is required for glulam", () => {
      expect(
        isWetServiceRequired(20, WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER)
      ).toBe(true); // 20% ≥ 16%
      expect(
        isWetServiceRequired(16, WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER)
      ).toBe(true); // 16% = 16% (≥)
      expect(
        isWetServiceRequired(15, WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER)
      ).toBe(false); // 15% < 16%
    });
  });

  describe("getWetServiceMoistureThreshold", () => {
    it("should return correct thresholds for different lumber types", () => {
      expect(
        getWetServiceMoistureThreshold(WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER)
      ).toBe(WET_SERVICE_MOISTURE_THRESHOLDS.SAWN_LUMBER);

      expect(
        getWetServiceMoistureThreshold(WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER)
      ).toBe(WET_SERVICE_MOISTURE_THRESHOLDS.GLUED_LAMINATED);
    });
  });

  describe("checkWetServiceSpecialConditions", () => {
    it("should identify bending special conditions", () => {
      // (Fb)(CF) ≤ 1,150 psi → CM = 1.0
      const result1 = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.BENDING,
        1000, // Fb
        1.15  // CF
      );
      expect(result1.applies).toBe(true);
      expect(result1.reason).toContain("1150 ≤ 1150 psi");

      const result2 = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.BENDING,
        1000, // Fb
        1.2   // CF - results in 1200 > 1150
      );
      expect(result2.applies).toBe(false);
    });

    it("should identify compression parallel special conditions", () => {
      // Fc ≤ 750 psi → CM = 1.0
      const result1 = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        750  // Fc = 750
      );
      expect(result1.applies).toBe(true);
      expect(result1.reason).toContain("750 ≤ 750 psi");

      const result2 = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        800  // Fc > 750
      );
      expect(result2.applies).toBe(false);
    });

    it("should return false when no reference value provided", () => {
      const result = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.BENDING
      );
      expect(result.applies).toBe(false);
    });
  });

  describe("getWetServiceFactor", () => {
    describe("Dry conditions - no wet service factor needed", () => {
      it("should return 1.0 when moisture content is below threshold", () => {
        const input: WetServiceFactorInput = {
          moistureContent: 15, // Below 19% threshold
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        };

        const result = getWetServiceFactor(input);
        expect(result).toBe(1.0);
      });
    });

    describe("Sawn lumber standard factors (Tables 4A, 4B, 4C, 4F)", () => {
      it("should return correct factors for dimension lumber", () => {
        const testCases = [
          { designValue: DESIGN_VALUE_TYPES.BENDING, expected: 0.85 },
          { designValue: DESIGN_VALUE_TYPES.TENSION_PARALLEL, expected: 1.0 },
          { designValue: DESIGN_VALUE_TYPES.SHEAR, expected: 0.97 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR, expected: 0.67 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, expected: 0.8 },
          { designValue: DESIGN_VALUE_TYPES.E, expected: 0.9 },
          { designValue: DESIGN_VALUE_TYPES.E_MIN, expected: 0.9 },
        ];

        testCases.forEach(({ designValue, expected }) => {
          const input: WetServiceFactorInput = {
            moistureContent: 25,
            designValueType: designValue,
            lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
          };

          const result = getWetServiceFactor(input);
          expect(result).toBe(expected);
        });
      });
    });

    describe("Timber factors (Table 4D)", () => {
      it("should return correct factors for timbers", () => {
        const testCases = [
          { designValue: DESIGN_VALUE_TYPES.BENDING, expected: 1.0 },
          { designValue: DESIGN_VALUE_TYPES.TENSION_PARALLEL, expected: 1.0 },
          { designValue: DESIGN_VALUE_TYPES.SHEAR, expected: 1.0 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR, expected: 0.67 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, expected: 0.91 },
          { designValue: DESIGN_VALUE_TYPES.E, expected: 1.0 },
          { designValue: DESIGN_VALUE_TYPES.E_MIN, expected: 1.0 },
        ];

        testCases.forEach(({ designValue, expected }) => {
          const input: WetServiceFactorInput = {
            moistureContent: 25,
            designValueType: designValue,
            lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.TIMBERS,
          };

          const result = getWetServiceFactor(input);
          expect(result).toBe(expected);
        });
      });
    });

    describe("Decking factors (Table 4E)", () => {
      it("should return correct factors for decking", () => {
        const testCases = [
          { designValue: DESIGN_VALUE_TYPES.BENDING, expected: 0.85 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR, expected: 0.67 },
          { designValue: DESIGN_VALUE_TYPES.E, expected: 0.9 },
          { designValue: DESIGN_VALUE_TYPES.E_MIN, expected: 0.9 },
        ];

        testCases.forEach(({ designValue, expected }) => {
          const input: WetServiceFactorInput = {
            moistureContent: 25,
            designValueType: designValue,
            lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DECKING,
          };

          const result = getWetServiceFactor(input);
          expect(result).toBe(expected);
        });
      });

      it("should return 1.0 for unsupported decking design values", () => {
        const input: WetServiceFactorInput = {
          moistureContent: 25,
          designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DECKING,
        };

        const result = getWetServiceFactor(input);
        expect(result).toBe(1.0);
      });
    });

    describe("Glulam factors (Tables 5A, 5B, 5D)", () => {
      it("should return correct factors for glulam", () => {
        const testCases = [
          { designValue: DESIGN_VALUE_TYPES.BENDING, expected: 0.8 },
          { designValue: DESIGN_VALUE_TYPES.TENSION_PARALLEL, expected: 0.8 },
          { designValue: DESIGN_VALUE_TYPES.SHEAR, expected: 0.875 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR, expected: 0.53 },
          { designValue: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL, expected: 0.73 },
          { designValue: DESIGN_VALUE_TYPES.E, expected: 0.833 },
          { designValue: DESIGN_VALUE_TYPES.E_MIN, expected: 0.833 },
        ];

        testCases.forEach(({ designValue, expected }) => {
          const input: WetServiceFactorInput = {
            moistureContent: 20, // Above 16% threshold for glulam
            designValueType: designValue,
            lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER,
          };

          const result = getWetServiceFactor(input);
          expect(result).toBe(expected);
        });
      });
    });

    describe("Southern Pine species special handling", () => {
      it("should return 1.0 for Southern Pine timbers", () => {
        const input: WetServiceFactorInput = {
          moistureContent: 25,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.TIMBERS,
          speciesGroup: WET_SERVICE_SPECIES_GROUPS.SOUTHERN_PINE,
        };

        const result = getWetServiceFactor(input);
        expect(result).toBe(1.0); // Southern Pine timbers use tabulated values
      });

      it("should return 1.0 for Southern Pine decking", () => {
        const input: WetServiceFactorInput = {
          moistureContent: 25,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DECKING,
          speciesGroup: WET_SERVICE_SPECIES_GROUPS.SOUTHERN_PINE,
        };

        const result = getWetServiceFactor(input);
        expect(result).toBe(1.0); // Southern Pine decking uses tabulated green values
      });
    });

    describe("Special condition overrides", () => {
      it("should return 1.0 when bending special condition applies", () => {
        const input: WetServiceFactorInput = {
          moistureContent: 25,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
          referenceDesignValue: 1000,
          sizeFactor: 1.15, // (1000)(1.15) = 1150 ≤ 1150
        };

        const result = getWetServiceFactor(input);
        expect(result).toBe(1.0); // Special condition override
      });

      it("should return 1.0 when compression special condition applies", () => {
        const input: WetServiceFactorInput = {
          moistureContent: 25,
          designValueType: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
          lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
          referenceDesignValue: 700, // ≤ 750 psi
        };

        const result = getWetServiceFactor(input);
        expect(result).toBe(1.0); // Special condition override
      });
    });

    it("should throw error for invalid input", () => {
      const invalidInput: WetServiceFactorInput = {
        moistureContent: NaN,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      expect(() => getWetServiceFactor(invalidInput)).toThrow();
    });
  });

  describe("applyWetServiceFactor", () => {
    it("should correctly apply wet service factor to reference value", () => {
      const referenceValue = 1200; // psi
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = applyWetServiceFactor(referenceValue, input);
      const expectedFactor = SAWN_LUMBER_WET_SERVICE_FACTORS.STANDARD[DESIGN_VALUE_TYPES.BENDING]; // 0.85
      const expected = referenceValue * expectedFactor;

      expect(result).toBe(expected);
      expect(result).toBe(1020); // 1200 × 0.85
    });

    it("should handle zero reference value", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = applyWetServiceFactor(0, input);
      expect(result).toBe(0);
    });

    it("should throw error for invalid reference value", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      expect(() => applyWetServiceFactor(-100, input)).toThrow();
      expect(() => applyWetServiceFactor(NaN, input)).toThrow();
      expect(() => applyWetServiceFactor(Infinity, input)).toThrow();
    });
  });

  describe("getMultipleWetServiceFactors", () => {
    it("should calculate factors for multiple design values", () => {
      const baseInput = {
        moistureContent: 25,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };
      const designValues = [
        DESIGN_VALUE_TYPES.BENDING,
        DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        DESIGN_VALUE_TYPES.E,
      ];

      const result = getMultipleWetServiceFactors(baseInput, designValues);

      expect(result[DESIGN_VALUE_TYPES.BENDING]).toBe(0.85);
      expect(result[DESIGN_VALUE_TYPES.TENSION_PARALLEL]).toBe(1.0);
      expect(result[DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]).toBe(0.8);
      expect(result[DESIGN_VALUE_TYPES.E]).toBe(0.9);
    });

    it("should handle invalid design values gracefully", () => {
      const baseInput = {
        moistureContent: 25,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };
      const designValues = ["invalid_type" as any];

      const result = getMultipleWetServiceFactors(baseInput, designValues);
      expect((result as any)["invalid_type"]).toBe(1.0); // Default factor for invalid values
    });
  });

  describe("getWetServiceFactorAnalysis", () => {
    it("should provide comprehensive analysis", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = getWetServiceFactorAnalysis(input);

      expect(result.input).toEqual(input);
      expect(result.wetServiceFactor).toBe(0.85);
      expect(result.isWetServiceRequired).toBe(true);
      expect(result.moistureThreshold).toBe(19);
      expect(result.lumberFactorSource).toContain("NDS Tables 4A, 4B, 4C, 4F");
      expect(result.validation.isValid).toBe(true);
    });

    it("should identify special condition overrides", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 25,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
        referenceDesignValue: 1000,
        sizeFactor: 1.15,
      };

      const result = getWetServiceFactorAnalysis(input);

      expect(result.wetServiceFactor).toBe(1.0);
      expect(result.specialConditionApplied).toContain("1150 ≤ 1150 psi");
      expect(result.lumberFactorSource).toBe("special condition override");
    });

    it("should handle invalid input gracefully", () => {
      const input: WetServiceFactorInput = {
        moistureContent: NaN,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = getWetServiceFactorAnalysis(input);

      expect(result.wetServiceFactor).toBe(1.0); // Default factor
      expect(result.validation.isValid).toBe(false);
      expect(result.lumberFactorSource).toBe("default (invalid input)");
    });

    it("should identify correct factor sources for different lumber categories", () => {
      const testCases = [
        {
          category: WET_SERVICE_LUMBER_CATEGORIES.TIMBERS,
          expectedSource: "NDS Table 4D (timbers)",
        },
        {
          category: WET_SERVICE_LUMBER_CATEGORIES.DECKING,
          expectedSource: "NDS Table 4E (decking)",
        },
        {
          category: WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER,
          expectedSource: "NDS Tables 5A, 5B, 5D (glulam)",
        },
      ];

      testCases.forEach(({ category, expectedSource }) => {
        const input: WetServiceFactorInput = {
          moistureContent: 25,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          lumberCategory: category,
        };

        const result = getWetServiceFactorAnalysis(input);
        expect(result.lumberFactorSource).toBe(expectedSource);
      });
    });
  });

  describe("isDesignValueAffectedByWetService", () => {
    it("should identify affected design values for different lumber categories", () => {
      // Standard sawn lumber
      expect(
        isDesignValueAffectedByWetService(
          DESIGN_VALUE_TYPES.BENDING,
          WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER
        )
      ).toBe(true);

      // Timbers
      expect(
        isDesignValueAffectedByWetService(
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
          WET_SERVICE_LUMBER_CATEGORIES.TIMBERS
        )
      ).toBe(true);

      // Glulam
      expect(
        isDesignValueAffectedByWetService(
          DESIGN_VALUE_TYPES.SHEAR,
          WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER
        )
      ).toBe(true);

      // Decking - limited design values
      expect(
        isDesignValueAffectedByWetService(
          DESIGN_VALUE_TYPES.BENDING,
          WET_SERVICE_LUMBER_CATEGORIES.DECKING
        )
      ).toBe(true);

      expect(
        isDesignValueAffectedByWetService(
          DESIGN_VALUE_TYPES.TENSION_PARALLEL,
          WET_SERVICE_LUMBER_CATEGORIES.DECKING
        )
      ).toBe(false);
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle boundary moisture content values correctly", () => {
      // Sawn lumber boundary (19%)
      expect(
        isWetServiceRequired(19.0, WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER)
      ).toBe(false); // exactly 19% does not trigger (> 19%)

      expect(
        isWetServiceRequired(19.1, WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER)
      ).toBe(true); // 19.1% > 19%

      // Glulam boundary (16%)
      expect(
        isWetServiceRequired(16.0, WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER)
      ).toBe(true); // exactly 16% triggers (≥ 16%)

      expect(
        isWetServiceRequired(15.9, WET_SERVICE_LUMBER_CATEGORIES.GLUED_LAMINATED_TIMBER)
      ).toBe(false); // 15.9% < 16%
    });

    it("should handle special condition boundary values", () => {
      // Bending boundary (1150 psi)
      const bendingBoundary = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.BENDING,
        1150, // exactly 1150
        1.0   // CF = 1.0
      );
      expect(bendingBoundary.applies).toBe(true);

      // Compression boundary (750 psi)
      const compressionBoundary = checkWetServiceSpecialConditions(
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        750 // exactly 750
      );
      expect(compressionBoundary.applies).toBe(true);
    });

    it("should maintain precision for decimal moisture content", () => {
      const input: WetServiceFactorInput = {
        moistureContent: 19.01, // Just above threshold
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberCategory: WET_SERVICE_LUMBER_CATEGORIES.DIMENSION_LUMBER,
      };

      const result = getWetServiceFactor(input);
      expect(result).toBe(0.85); // Should apply wet service factor
    });
  });

  describe("Constants Verification", () => {
    it("should use all defined moisture thresholds", () => {
      expect(WET_SERVICE_MOISTURE_THRESHOLDS.SAWN_LUMBER).toBe(19);
      expect(WET_SERVICE_MOISTURE_THRESHOLDS.GLUED_LAMINATED).toBe(16);
    });

    it("should use all defined special condition thresholds", () => {
      expect(WET_SERVICE_SPECIAL_CONDITIONS.BENDING_THRESHOLD_PSI).toBe(1150);
      expect(WET_SERVICE_SPECIAL_CONDITIONS.COMPRESSION_THRESHOLD_PSI).toBe(750);
    });

    it("should verify sawn lumber factor values match NDS specifications", () => {
      const standardFactors = SAWN_LUMBER_WET_SERVICE_FACTORS.STANDARD;
      expect(standardFactors[DESIGN_VALUE_TYPES.BENDING]).toBe(0.85);
      expect(standardFactors[DESIGN_VALUE_TYPES.TENSION_PARALLEL]).toBe(1.0);
      expect(standardFactors[DESIGN_VALUE_TYPES.SHEAR]).toBe(0.97);
      expect(standardFactors[DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]).toBe(0.67);
      expect(standardFactors[DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]).toBe(0.8);
      expect(standardFactors[DESIGN_VALUE_TYPES.E]).toBe(0.9);
      expect(standardFactors[DESIGN_VALUE_TYPES.E_MIN]).toBe(0.9);
    });

    it("should verify glulam factor values match NDS specifications", () => {
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.BENDING]).toBe(0.8);
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.TENSION_PARALLEL]).toBe(0.8);
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.SHEAR]).toBe(0.875);
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]).toBe(0.53);
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]).toBe(0.73);
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.E]).toBe(0.833);
      expect(GLULAM_WET_SERVICE_FACTORS[DESIGN_VALUE_TYPES.E_MIN]).toBe(0.833);
    });
  });
}); 