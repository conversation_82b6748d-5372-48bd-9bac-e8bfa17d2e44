/**
 * Load Duration Factor for Sawn Lumber Design Values
 * Based on NDS (National Design Specification) for Wood Construction - Section 2.3.2
 */

import { LoadType } from '../../../../types/load/load-type';
import {
  LOAD_DURATION_FACTORS,
  LOAD_DURATION_CATEGORIES,
  LoadDurationCategory,
  LOAD_DURATION_LIMITATIONS,
  DESIGN_VALUE_TYPES,
  DesignValueType,
} from '../../constants';

/**
 * Gets the load duration factor, CD, based on the load duration category.
 * 
 * Per NDS 2.3.2: <PERSON> has the property of carrying substantially greater maximum 
 * loads for short durations than for long durations of loading. Reference design 
 * values apply to normal load duration (ten years cumulative duration).
 * 
 * @param loadDurationCategory - The load duration category from NDS Table 2.3.2
 * @returns The load duration factor CD
 * @throws Error if load duration category is invalid
 */
export function getLoadDurationFactorCD(loadDurationCategory: LoadDurationCategory): number {
  switch (loadDurationCategory) {
    case LOAD_DURATION_CATEGORIES.PERMANENT:
      return LOAD_DURATION_FACTORS.PERMANENT;
    case LOAD_DURATION_CATEGORIES.TEN_YEARS:
      return LOAD_DURATION_FACTORS.TEN_YEARS;
    case LOAD_DURATION_CATEGORIES.TWO_MONTHS:
      return LOAD_DURATION_FACTORS.TWO_MONTHS;
    case LOAD_DURATION_CATEGORIES.SEVEN_DAYS:
      return LOAD_DURATION_FACTORS.SEVEN_DAYS;
    case LOAD_DURATION_CATEGORIES.TEN_MINUTES:
      return LOAD_DURATION_FACTORS.TEN_MINUTES;
    case LOAD_DURATION_CATEGORIES.IMPACT:
      return LOAD_DURATION_FACTORS.IMPACT;
    default:
      throw new Error(`Invalid load duration category: ${loadDurationCategory}`);
  }
}

/**
 * Gets the load duration factor based on the load type.
 * Maps LoadType enum to appropriate load duration categories per NDS Table 2.3.2.
 * 
 * @param loadType - The type of load from LoadType enum
 * @returns The load duration factor CD
 * @throws Error if load type is not mapped to a duration category
 */
export function getLoadDurationFactorByLoadType(loadType: LoadType): number {
  const category = getLoadDurationCategoryByLoadType(loadType);
  return getLoadDurationFactorCD(category);
}

/**
 * Maps LoadType to LoadDurationCategory based on NDS Table 2.3.2.
 * 
 * @param loadType - The type of load from LoadType enum
 * @returns The corresponding load duration category
 * @throws Error if load type is not recognized
 */
export function getLoadDurationCategoryByLoadType(loadType: LoadType): LoadDurationCategory {
  switch (loadType) {
    case LoadType.DEAD:
      return LOAD_DURATION_CATEGORIES.PERMANENT;
    case LoadType.LIVE:
    case LoadType.ROOF_LIVE:
      return LOAD_DURATION_CATEGORIES.TEN_YEARS;
    case LoadType.SNOW:
      return LOAD_DURATION_CATEGORIES.TWO_MONTHS;
    case LoadType.WIND:
    case LoadType.EARTHQUAKE:
      return LOAD_DURATION_CATEGORIES.TEN_MINUTES;
    case LoadType.RAIN:
    case LoadType.SOIL:
    case LoadType.FLOOD:
      // These typically fall under live load category for duration purposes
      return LOAD_DURATION_CATEGORIES.TEN_YEARS;
    case LoadType.TEMPERATURE:
      // Temperature loads typically short-term
      return LOAD_DURATION_CATEGORIES.SEVEN_DAYS;
    default:
      throw new Error(`Load type ${loadType} is not mapped to a load duration category`);
  }
}

/**
 * Checks if a design value type is exempt from load duration factors.
 * Per NDS 2.3.2: Load duration factors shall not apply to E, Emin, 
 * nor to Fc⊥ based on a deformation limit.
 * 
 * @param designValueType - The type of design value
 * @returns True if the design value is exempt from load duration factors
 */
export function isDesignValueExemptFromLoadDuration(designValueType: DesignValueType): boolean {
  return designValueType === DESIGN_VALUE_TYPES.E ||
         designValueType === DESIGN_VALUE_TYPES.E_MIN ||
         designValueType === DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR;
}

/**
 * Validates load duration factor for specific applications.
 * Per NDS 2.3.2: Load duration factors greater than 1.6 shall not be used 
 * for pressure-treated lumber or structural panel connections.
 * 
 * @param loadDurationFactor - The calculated load duration factor
 * @param isPressureTreated - Whether the lumber is pressure-treated
 * @param isStructuralPanel - Whether this applies to structural panels/connections
 * @returns Object indicating if factor is valid and any limitations applied
 */
export function validateLoadDurationFactor(
  loadDurationFactor: number,
  isPressureTreated: boolean = false,
  isStructuralPanel: boolean = false
): {
  isValid: boolean;
  limitedFactor: number;
  limitations: string[];
} {
  const limitations: string[] = [];
  let limitedFactor = loadDurationFactor;

  if (isPressureTreated && loadDurationFactor > LOAD_DURATION_LIMITATIONS.MAX_FACTOR_TREATED_LUMBER) {
    limitations.push(`Load duration factor ${loadDurationFactor} exceeds maximum ${LOAD_DURATION_LIMITATIONS.MAX_FACTOR_TREATED_LUMBER} for pressure-treated lumber`);
    limitedFactor = LOAD_DURATION_LIMITATIONS.MAX_FACTOR_TREATED_LUMBER;
  }

  if (isStructuralPanel && loadDurationFactor > LOAD_DURATION_LIMITATIONS.MAX_FACTOR_STRUCTURAL_PANELS) {
    limitations.push(`Load duration factor ${loadDurationFactor} exceeds maximum ${LOAD_DURATION_LIMITATIONS.MAX_FACTOR_STRUCTURAL_PANELS} for structural panels`);
    limitedFactor = Math.min(limitedFactor, LOAD_DURATION_LIMITATIONS.MAX_FACTOR_STRUCTURAL_PANELS);
  }

  return {
    isValid: limitations.length === 0,
    limitedFactor,
    limitations,
  };
}

/**
 * Applies load duration factor to a design value with appropriate checks.
 * 
 * @param designValue - The original design value
 * @param designValueType - The type of design value
 * @param loadDurationCategory - The load duration category
 * @param isPressureTreated - Whether the lumber is pressure-treated (optional)
 * @param isStructuralPanel - Whether this applies to structural panels (optional)
 * @returns The adjusted design value with load duration factor applied
 */
export function applyLoadDurationFactor(
  designValue: number,
  designValueType: DesignValueType,
  loadDurationCategory: LoadDurationCategory,
  isPressureTreated: boolean = false,
  isStructuralPanel: boolean = false
): number {
  // Input validation
  if (designValue <= 0) {
    throw new Error('Design value must be positive');
  }

  // Check if design value is exempt from load duration factors
  if (isDesignValueExemptFromLoadDuration(designValueType)) {
    return designValue; // No adjustment for exempt values
  }

  // Get base load duration factor
  const baseFactor = getLoadDurationFactorCD(loadDurationCategory);

  // Validate and limit factor if necessary
  const validation = validateLoadDurationFactor(baseFactor, isPressureTreated, isStructuralPanel);
  
  // Apply the (potentially limited) factor
  return designValue * validation.limitedFactor;
}

/**
 * Applies load duration factor using LoadType directly.
 * 
 * @param designValue - The original design value
 * @param designValueType - The type of design value
 * @param loadType - The type of load from LoadType enum
 * @param isPressureTreated - Whether the lumber is pressure-treated (optional)
 * @param isStructuralPanel - Whether this applies to structural panels (optional)
 * @returns The adjusted design value with load duration factor applied
 */
export function applyLoadDurationFactorByLoadType(
  designValue: number,
  designValueType: DesignValueType,
  loadType: LoadType,
  isPressureTreated: boolean = false,
  isStructuralPanel: boolean = false
): number {
  const category = getLoadDurationCategoryByLoadType(loadType);
  return applyLoadDurationFactor(designValue, designValueType, category, isPressureTreated, isStructuralPanel);
}

/**
 * Gets load duration factors for multiple load types.
 * 
 * @param loadTypes - Array of load types to get factors for
 * @returns Object mapping load type strings to their duration factors
 */
export function getMultipleLoadDurationFactors(loadTypes: LoadType[]): Record<string, number> {
  const factors: Record<string, number> = {};
  
  for (const loadType of loadTypes) {
    factors[loadType] = getLoadDurationFactorByLoadType(loadType);
  }
  
  return factors;
}

/**
 * Gets the controlling (maximum) load duration factor from multiple load types.
 * Per NDS: Use the load duration factor corresponding to the load case producing the maximum effect.
 * 
 * @param loadTypes - Array of load types to evaluate
 * @returns The maximum load duration factor among all load types
 */
export function getControllingLoadDurationFactor(loadTypes: LoadType[]): number {
  if (loadTypes.length === 0) {
    throw new Error('At least one load type must be provided');
  }
  
  const factors = getMultipleLoadDurationFactors(loadTypes);
  return Math.max(...Object.values(factors));
}

/**
 * Provides comprehensive load duration analysis for multiple load types.
 * 
 * @param loadTypes - Array of load types to analyze
 * @returns Complete analysis including individual factors, controlling factor, and categories
 */
export function getLoadDurationAnalysis(loadTypes: LoadType[]): {
  individualFactors: Record<string, number>;
  controllingFactor: number;
  controllingLoadType: LoadType;
  durationCategories: Record<string, LoadDurationCategory>;
} {
  const individualFactors = getMultipleLoadDurationFactors(loadTypes);
  const controllingFactor = getControllingLoadDurationFactor(loadTypes);
  
  // Find the load type that produces the controlling factor
  const controllingLoadType = loadTypes.find(
    loadType => individualFactors[loadType] === controllingFactor
  ) as LoadType;
  
  // Get duration categories for each load type
  const durationCategories: Record<string, LoadDurationCategory> = {};
  for (const loadType of loadTypes) {
    durationCategories[loadType] = getLoadDurationCategoryByLoadType(loadType);
  }
  
  return {
    individualFactors,
    controllingFactor,
    controllingLoadType,
    durationCategories,
  };
} 