/**
 * Unit tests for Sawn Lumber Load Duration Adjustment Factors
 * Based on NDS (National Design Specification) for Wood Construction - Section 2.3.2
 */

import { LoadType } from '../../../../types/load/load-type';
import {
  getLoadDurationFactorCD,
  getLoadDurationFactorByLoadType,
  getLoadDurationCategoryByLoadType,
  isDesignValueExemptFromLoadDuration,
  validateLoadDurationFactor,
  applyLoadDurationFactor,
  applyLoadDurationFactorByLoadType,
  getMultipleLoadDurationFactors,
  getControllingLoadDurationFactor,
  getLoadDurationAnalysis,
} from './load-duration';
import {
  LOAD_DURATION_FACTORS,
  LOAD_DURATION_CATEGORIES,
  LOAD_DURATION_LIMITATIONS,
  DESIGN_VALUE_TYPES,
} from '../../constants';

describe('Load Duration Factor Calculations', () => {
  describe('getLoadDurationFactorCD', () => {
    describe('Valid Duration Categories', () => {
      test('should return 0.9 for permanent loads (dead load)', () => {
        const result = getLoadDurationFactorCD(LOAD_DURATION_CATEGORIES.PERMANENT);
        expect(result).toBe(LOAD_DURATION_FACTORS.PERMANENT);
        expect(result).toBe(0.9);
      });

      test('should return 1.0 for ten years loads (occupancy live load)', () => {
        const result = getLoadDurationFactorCD(LOAD_DURATION_CATEGORIES.TEN_YEARS);
        expect(result).toBe(LOAD_DURATION_FACTORS.TEN_YEARS);
        expect(result).toBe(1.0);
      });

      test('should return 1.15 for two months loads (snow load)', () => {
        const result = getLoadDurationFactorCD(LOAD_DURATION_CATEGORIES.TWO_MONTHS);
        expect(result).toBe(LOAD_DURATION_FACTORS.TWO_MONTHS);
        expect(result).toBe(1.15);
      });

      test('should return 1.25 for seven days loads (construction load)', () => {
        const result = getLoadDurationFactorCD(LOAD_DURATION_CATEGORIES.SEVEN_DAYS);
        expect(result).toBe(LOAD_DURATION_FACTORS.SEVEN_DAYS);
        expect(result).toBe(1.25);
      });

      test('should return 1.6 for ten minutes loads (wind/earthquake load)', () => {
        const result = getLoadDurationFactorCD(LOAD_DURATION_CATEGORIES.TEN_MINUTES);
        expect(result).toBe(LOAD_DURATION_FACTORS.TEN_MINUTES);
        expect(result).toBe(1.6);
      });

      test('should return 2.0 for impact loads', () => {
        const result = getLoadDurationFactorCD(LOAD_DURATION_CATEGORIES.IMPACT);
        expect(result).toBe(LOAD_DURATION_FACTORS.IMPACT);
        expect(result).toBe(2.0);
      });
    });

    describe('Invalid Duration Categories', () => {
      test('should throw error for invalid duration category', () => {
        expect(() => {
          getLoadDurationFactorCD('invalid_category' as any);
        }).toThrow('Invalid load duration category: invalid_category');
      });
    });
  });

  describe('getLoadDurationCategoryByLoadType', () => {
    describe('Load Type Mapping', () => {
      test('should map DEAD load to permanent duration', () => {
        const result = getLoadDurationCategoryByLoadType(LoadType.DEAD);
        expect(result).toBe(LOAD_DURATION_CATEGORIES.PERMANENT);
      });

      test('should map LIVE loads to ten years duration', () => {
        expect(getLoadDurationCategoryByLoadType(LoadType.LIVE)).toBe(LOAD_DURATION_CATEGORIES.TEN_YEARS);
        expect(getLoadDurationCategoryByLoadType(LoadType.ROOF_LIVE)).toBe(LOAD_DURATION_CATEGORIES.TEN_YEARS);
      });

      test('should map SNOW load to two months duration', () => {
        const result = getLoadDurationCategoryByLoadType(LoadType.SNOW);
        expect(result).toBe(LOAD_DURATION_CATEGORIES.TWO_MONTHS);
      });

      test('should map WIND and EARTHQUAKE loads to ten minutes duration', () => {
        expect(getLoadDurationCategoryByLoadType(LoadType.WIND)).toBe(LOAD_DURATION_CATEGORIES.TEN_MINUTES);
        expect(getLoadDurationCategoryByLoadType(LoadType.EARTHQUAKE)).toBe(LOAD_DURATION_CATEGORIES.TEN_MINUTES);
      });

      test('should map environmental loads to ten years duration', () => {
        expect(getLoadDurationCategoryByLoadType(LoadType.RAIN)).toBe(LOAD_DURATION_CATEGORIES.TEN_YEARS);
        expect(getLoadDurationCategoryByLoadType(LoadType.SOIL)).toBe(LOAD_DURATION_CATEGORIES.TEN_YEARS);
        expect(getLoadDurationCategoryByLoadType(LoadType.FLOOD)).toBe(LOAD_DURATION_CATEGORIES.TEN_YEARS);
      });

      test('should map TEMPERATURE load to seven days duration', () => {
        const result = getLoadDurationCategoryByLoadType(LoadType.TEMPERATURE);
        expect(result).toBe(LOAD_DURATION_CATEGORIES.SEVEN_DAYS);
      });
    });
  });

  describe('getLoadDurationFactorByLoadType', () => {
    describe('Load Type to Factor Mapping', () => {
      test('should return 0.9 for dead loads', () => {
        const result = getLoadDurationFactorByLoadType(LoadType.DEAD);
        expect(result).toBe(0.9);
      });

      test('should return 1.0 for live loads', () => {
        expect(getLoadDurationFactorByLoadType(LoadType.LIVE)).toBe(1.0);
        expect(getLoadDurationFactorByLoadType(LoadType.ROOF_LIVE)).toBe(1.0);
      });

      test('should return 1.15 for snow loads', () => {
        const result = getLoadDurationFactorByLoadType(LoadType.SNOW);
        expect(result).toBe(1.15);
      });

      test('should return 1.6 for wind and earthquake loads', () => {
        expect(getLoadDurationFactorByLoadType(LoadType.WIND)).toBe(1.6);
        expect(getLoadDurationFactorByLoadType(LoadType.EARTHQUAKE)).toBe(1.6);
      });

      test('should return 1.25 for temperature loads', () => {
        const result = getLoadDurationFactorByLoadType(LoadType.TEMPERATURE);
        expect(result).toBe(1.25);
      });
    });
  });

  describe('isDesignValueExemptFromLoadDuration', () => {
    describe('Exempt Design Values', () => {
      test('should return true for modulus of elasticity (E)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.E);
        expect(result).toBe(true);
      });

      test('should return true for minimum modulus of elasticity (Emin)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.E_MIN);
        expect(result).toBe(true);
      });

      test('should return true for compression perpendicular to grain (Fc⊥)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR);
        expect(result).toBe(true);
      });
    });

    describe('Non-Exempt Design Values', () => {
      test('should return false for bending (Fb)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.BENDING);
        expect(result).toBe(false);
      });

      test('should return false for tension parallel (Ft)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.TENSION_PARALLEL);
        expect(result).toBe(false);
      });

      test('should return false for compression parallel (Fc)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL);
        expect(result).toBe(false);
      });

      test('should return false for shear (Fv)', () => {
        const result = isDesignValueExemptFromLoadDuration(DESIGN_VALUE_TYPES.SHEAR);
        expect(result).toBe(false);
      });
    });
  });

  describe('validateLoadDurationFactor', () => {
    describe('Valid Factors', () => {
      test('should return valid for factors within normal range', () => {
        const result = validateLoadDurationFactor(1.15);
        expect(result.isValid).toBe(true);
        expect(result.limitedFactor).toBe(1.15);
        expect(result.limitations).toHaveLength(0);
      });

      test('should return valid for factor exactly at limit (1.6)', () => {
        const result = validateLoadDurationFactor(1.6, true, true);
        expect(result.isValid).toBe(true);
        expect(result.limitedFactor).toBe(1.6);
        expect(result.limitations).toHaveLength(0);
      });
    });

    describe('Pressure-Treated Lumber Limitations', () => {
      test('should limit factor for pressure-treated lumber exceeding 1.6', () => {
        const result = validateLoadDurationFactor(2.0, true, false);
        expect(result.isValid).toBe(false);
        expect(result.limitedFactor).toBe(1.6);
        expect(result.limitations).toContain('Load duration factor 2 exceeds maximum 1.6 for pressure-treated lumber');
      });

      test('should allow factor under 1.6 for pressure-treated lumber', () => {
        const result = validateLoadDurationFactor(1.25, true, false);
        expect(result.isValid).toBe(true);
        expect(result.limitedFactor).toBe(1.25);
        expect(result.limitations).toHaveLength(0);
      });
    });

    describe('Structural Panel Limitations', () => {
      test('should limit factor for structural panels exceeding 1.6', () => {
        const result = validateLoadDurationFactor(2.0, false, true);
        expect(result.isValid).toBe(false);
        expect(result.limitedFactor).toBe(1.6);
        expect(result.limitations).toContain('Load duration factor 2 exceeds maximum 1.6 for structural panels');
      });

      test('should apply both limitations when both conditions are true', () => {
        const result = validateLoadDurationFactor(2.0, true, true);
        expect(result.isValid).toBe(false);
        expect(result.limitedFactor).toBe(1.6);
        expect(result.limitations).toHaveLength(2);
      });
    });
  });

  describe('applyLoadDurationFactor', () => {
    describe('Valid Applications', () => {
      test('should apply factor to non-exempt design value', () => {
        const result = applyLoadDurationFactor(
          1000,
          DESIGN_VALUE_TYPES.BENDING,
          LOAD_DURATION_CATEGORIES.TWO_MONTHS
        );
        expect(result).toBe(1000 * 1.15);
        expect(result).toBe(1150);
      });

      test('should return original value for exempt design values', () => {
        const result = applyLoadDurationFactor(
          1800000,
          DESIGN_VALUE_TYPES.E,
          LOAD_DURATION_CATEGORIES.TEN_MINUTES
        );
        expect(result).toBe(1800000); // No change for E
      });

      test('should apply limited factor for pressure-treated lumber', () => {
        const result = applyLoadDurationFactor(
          800,
          DESIGN_VALUE_TYPES.TENSION_PARALLEL,
          LOAD_DURATION_CATEGORIES.IMPACT,
          true  // pressure-treated
        );
        expect(result).toBe(800 * 1.6); // Limited to 1.6 instead of 2.0
        expect(result).toBe(1280);
      });
    });

    describe('Error Conditions', () => {
      test('should throw error for non-positive design value', () => {
        expect(() => {
          applyLoadDurationFactor(
            0,
            DESIGN_VALUE_TYPES.BENDING,
            LOAD_DURATION_CATEGORIES.TEN_YEARS
          );
        }).toThrow('Design value must be positive');
      });

      test('should throw error for negative design value', () => {
        expect(() => {
          applyLoadDurationFactor(
            -100,
            DESIGN_VALUE_TYPES.SHEAR,
            LOAD_DURATION_CATEGORIES.SEVEN_DAYS
          );
        }).toThrow('Design value must be positive');
      });
    });
  });

  describe('applyLoadDurationFactorByLoadType', () => {
    describe('Load Type Integration', () => {
      test('should apply snow load factor correctly', () => {
        const result = applyLoadDurationFactorByLoadType(
          1200,
          DESIGN_VALUE_TYPES.BENDING,
          LoadType.SNOW
        );
        expect(result).toBe(1200 * 1.15);
        expect(result).toBe(1380);
      });

      test('should apply wind load factor correctly with limitations', () => {
        const result = applyLoadDurationFactorByLoadType(
          900,
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
          LoadType.WIND,
          true  // pressure-treated
        );
        expect(result).toBe(900 * 1.6); // Limited to 1.6
        expect(result).toBe(1440);
      });

      test('should handle dead loads correctly', () => {
        const result = applyLoadDurationFactorByLoadType(
          1000,
          DESIGN_VALUE_TYPES.SHEAR,
          LoadType.DEAD
        );
        expect(result).toBe(1000 * 0.9);
        expect(result).toBe(900);
      });
    });
  });

  describe('getMultipleLoadDurationFactors', () => {
    test('should return factors for multiple load types', () => {
      const loadTypes = [LoadType.DEAD, LoadType.LIVE, LoadType.SNOW, LoadType.WIND];
      const result = getMultipleLoadDurationFactors(loadTypes);
      
      expect(result).toEqual({
        [LoadType.DEAD]: 0.9,
        [LoadType.LIVE]: 1.0,
        [LoadType.SNOW]: 1.15,
        [LoadType.WIND]: 1.6,
      });
    });

    test('should handle empty array', () => {
      const result = getMultipleLoadDurationFactors([]);
      expect(result).toEqual({});
    });

    test('should handle single load type', () => {
      const result = getMultipleLoadDurationFactors([LoadType.EARTHQUAKE]);
      expect(result).toEqual({
        [LoadType.EARTHQUAKE]: 1.6,
      });
    });
  });

  describe('getControllingLoadDurationFactor', () => {
    test('should return maximum factor from multiple load types', () => {
      const loadTypes = [LoadType.DEAD, LoadType.LIVE, LoadType.SNOW];
      const result = getControllingLoadDurationFactor(loadTypes);
      expect(result).toBe(1.15); // Snow load factor is highest
    });

    test('should return impact factor as highest', () => {
      const loadTypes = [LoadType.DEAD, LoadType.LIVE, LoadType.SNOW, LoadType.WIND];
      const result = getControllingLoadDurationFactor(loadTypes);
      expect(result).toBe(1.6); // Wind factor is highest
    });

    test('should handle single load type', () => {
      const result = getControllingLoadDurationFactor([LoadType.DEAD]);
      expect(result).toBe(0.9);
    });

    test('should throw error for empty array', () => {
      expect(() => {
        getControllingLoadDurationFactor([]);
      }).toThrow('At least one load type must be provided');
    });
  });

  describe('getLoadDurationAnalysis', () => {
    test('should provide comprehensive analysis for multiple load types', () => {
      const loadTypes = [LoadType.DEAD, LoadType.LIVE, LoadType.SNOW, LoadType.WIND];
      const result = getLoadDurationAnalysis(loadTypes);
      
      expect(result.individualFactors).toEqual({
        [LoadType.DEAD]: 0.9,
        [LoadType.LIVE]: 1.0,
        [LoadType.SNOW]: 1.15,
        [LoadType.WIND]: 1.6,
      });
      
      expect(result.controllingFactor).toBe(1.6);
      expect(result.controllingLoadType).toBe(LoadType.WIND);
      
      expect(result.durationCategories).toEqual({
        [LoadType.DEAD]: LOAD_DURATION_CATEGORIES.PERMANENT,
        [LoadType.LIVE]: LOAD_DURATION_CATEGORIES.TEN_YEARS,
        [LoadType.SNOW]: LOAD_DURATION_CATEGORIES.TWO_MONTHS,
        [LoadType.WIND]: LOAD_DURATION_CATEGORIES.TEN_MINUTES,
      });
    });

    test('should handle analysis with dead load only', () => {
      const result = getLoadDurationAnalysis([LoadType.DEAD]);
      
      expect(result.controllingFactor).toBe(0.9);
      expect(result.controllingLoadType).toBe(LoadType.DEAD);
    });

    test('should identify correct controlling load for mixed scenario', () => {
      const loadTypes = [LoadType.LIVE, LoadType.TEMPERATURE, LoadType.SNOW];
      const result = getLoadDurationAnalysis(loadTypes);
      
      expect(result.controllingFactor).toBe(1.25); // Temperature = 1.25, Snow = 1.15
      expect(result.controllingLoadType).toBe(LoadType.TEMPERATURE);
    });
  });

  describe('Integration Tests', () => {
    describe('Real-world Load Combinations', () => {
      test('should handle typical building load combination (D + L + S)', () => {
        const loadTypes = [LoadType.DEAD, LoadType.LIVE, LoadType.SNOW];
        const analysis = getLoadDurationAnalysis(loadTypes);
        
        expect(analysis.controllingFactor).toBe(1.15); // Snow controls
        
        // Apply to a typical beam bending value
        const adjustedValue = applyLoadDurationFactorByLoadType(
          1200, // psi
          DESIGN_VALUE_TYPES.BENDING,
          analysis.controllingLoadType
        );
        expect(adjustedValue).toBe(1380);
      });

      test('should handle wind load combination (D + W)', () => {
        const loadTypes = [LoadType.DEAD, LoadType.WIND];
        const analysis = getLoadDurationAnalysis(loadTypes);
        
        expect(analysis.controllingFactor).toBe(1.6); // Wind controls
        
        const adjustedValue = applyLoadDurationFactorByLoadType(
          800, // psi  
          DESIGN_VALUE_TYPES.TENSION_PARALLEL,
          analysis.controllingLoadType
        );
        expect(adjustedValue).toBe(1280);
      });

      test('should handle pressure-treated lumber with wind loads', () => {
        const adjustedValue = applyLoadDurationFactorByLoadType(
          900,
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
          LoadType.WIND,
          true // pressure-treated
        );
        expect(adjustedValue).toBe(1440); // Limited to 1.6 factor
      });
    });

    describe('Edge Cases', () => {
      test('should handle very small design values', () => {
        const result = applyLoadDurationFactorByLoadType(
          0.001,
          DESIGN_VALUE_TYPES.SHEAR,
          LoadType.SNOW
        );
        expect(result).toBeCloseTo(0.001 * 1.15, 6);
      });

      test('should handle large design values', () => {
        const result = applyLoadDurationFactorByLoadType(
          10000000,
          DESIGN_VALUE_TYPES.BENDING,
          LoadType.WIND
        );
        expect(result).toBe(16000000);
      });
    });

    describe('Exempt Design Values Integration', () => {
      test('should not apply load duration factors to modulus of elasticity', () => {
        const originalE = 1800000; // psi
        
        // Test with high factor load (wind)
        const adjustedE = applyLoadDurationFactorByLoadType(
          originalE,
          DESIGN_VALUE_TYPES.E,
          LoadType.WIND
        );
        expect(adjustedE).toBe(originalE); // No change
        
        // Test with low factor load (dead)  
        const adjustedE2 = applyLoadDurationFactorByLoadType(
          originalE,
          DESIGN_VALUE_TYPES.E,
          LoadType.DEAD
        );
        expect(adjustedE2).toBe(originalE); // No change
      });

      test('should not apply load duration factors to Fc⊥', () => {
        const originalFcPerp = 625; // psi
        
        const adjustedFcPerp = applyLoadDurationFactorByLoadType(
          originalFcPerp,
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
          LoadType.WIND
        );
        expect(adjustedFcPerp).toBe(originalFcPerp); // No change
      });
    });
  });
}); 