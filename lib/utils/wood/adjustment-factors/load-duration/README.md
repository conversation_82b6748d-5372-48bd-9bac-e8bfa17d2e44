# Load Duration Factor (CD)

## Overview

This module implements the load duration factor (CD) calculations according to the National Design Specification (NDS) for Wood Construction, Section 2.3.2. The load duration factor adjusts design values based on the duration of load application in Allowable Stress Design (ASD).

## Key Concepts

### When Load Duration Factor Applies

The load duration factor applies to:
- **Allowable Stress Design (ASD)** method only
- **Most design values** except <PERSON>, Emin, and Fc⊥ (deformation-limited)
- **Load combinations** with different durations
- **Time-dependent strength** characteristics of wood

### Load Duration Categories

1. **Permanent Load** (CD = 0.9):
   - Dead loads acting continuously
   - 10+ year duration

2. **Ten Years** (CD = 1.0):
   - Normal occupancy live loads
   - Reference duration for design values

3. **Two Months** (CD = 1.15):
   - Snow loads
   - Seasonal loads

4. **Seven Days** (CD = 1.25):
   - Construction loads
   - Short-term loading

5. **Ten Minutes** (CD = 1.6):
   - Wind and earthquake loads
   - Impact-type loading

6. **Impact** (CD = 2.0):
   - Impact loads
   - Very short duration

### Load Duration Limitations

- **Maximum factor**: 1.6 for pressure-treated lumber
- **LRFD method**: Uses time effect factors instead
- **Some design values exempt**: <PERSON>, <PERSON><PERSON>, Fc⊥ (when deformation-based)

## API Reference

### Main Functions

#### `getLoadDurationFactorCD(loadDurationCategory)`

Gets the load duration factor for a specific category.

**Parameters:**
- `loadDurationCategory`: Category ('permanent', 'ten_years', 'two_months', etc.)

**Returns:**
- Load duration factor value

#### `getLoadDurationFactorByLoadType(loadType)`

Gets the load duration factor based on load type.

**Parameters:**
- `loadType`: Type of load ('dead', 'live', 'snow', 'wind', 'earthquake', etc.)

**Returns:**
- Load duration factor value

#### `getControllingLoadDurationFactor(loadDurations)`

Determines controlling load duration factor for load combinations.

**Parameters:**
- `loadDurations`: Array of load duration factors

**Returns:**
- Controlling (maximum applicable) load duration factor

#### `applyLoadDurationFactor(designValue, loadDurationFactor, designValueType)`

Applies load duration factor to a design value.

**Parameters:**
- `designValue`: Base design value
- `loadDurationFactor`: Load duration factor to apply
- `designValueType`: Type of design value ('Fb', 'Ft', etc.)

**Returns:**
- Adjusted design value

### Usage Examples

```typescript
import { 
  getLoadDurationFactorByLoadType,
  getControllingLoadDurationFactor,
  applyLoadDurationFactor
} from './load-duration';

// Example 1: Individual load types
const deadLoad = getLoadDurationFactorByLoadType('dead');    // 0.9
const liveLoad = getLoadDurationFactorByLoadType('live');    // 1.0
const snowLoad = getLoadDurationFactorByLoadType('snow');    // 1.15
const windLoad = getLoadDurationFactorByLoadType('wind');    // 1.6

// Example 2: Load combination
const controlling = getControllingLoadDurationFactor([
  deadLoad,  // 0.9
  liveLoad,  // 1.0
  snowLoad   // 1.15
]);
console.log(controlling); // 1.15 (snow governs)

// Example 3: Apply to design value
const baseFb = 1200; // psi
const adjustedFb = applyLoadDurationFactor(baseFb, 1.15, 'Fb');
console.log(adjustedFb); // 1380 psi
```

### Load Combination Analysis

#### `getLoadDurationAnalysis(loadCombination)`

Provides comprehensive analysis of load duration factors for complex combinations.

**Parameters:**
- `loadCombination`: Object describing load types and their factors

**Returns:**
- Detailed analysis including controlling factor and reasoning

```typescript
const analysis = getLoadDurationAnalysis({
  dead: { factor: 1.0, duration: 'permanent' },
  live: { factor: 0.75, duration: 'ten_years' },
  snow: { factor: 0.75, duration: 'two_months' }
});

console.log(analysis.controllingFactor); // 1.15
console.log(analysis.controllingLoad); // 'snow'
```

## Design Considerations

### Load Combination Rules

**ASD Load Combinations:**
- D (Dead): CD = 0.9
- D + L (Live): CD = 1.0  
- D + (Lr or S or R): CD = 1.15
- D + 0.75L + 0.75(Lr or S or R): CD = 1.15
- D + (0.6W or 0.7E): CD = 1.6
- D + 0.75L + 0.75(0.6W): CD = 1.6
- 0.6D + 0.6W: CD = 1.6

### Design Strategies

**Conservative Approach:**
- Use **lower load duration factors** when uncertain
- Consider **actual load durations** in service
- Account for **load combination effects**

**Load Duration Selection:**
- **Dead loads**: Always CD = 0.9
- **Live loads**: Typically CD = 1.0 (varies by occupancy)
- **Environmental loads**: Use appropriate short-term factors
- **Construction loads**: CD = 1.25 (temporary)

## Files

- `load-duration.ts` - Main implementation
- `load-duration.test.ts` - Comprehensive test suite
- `README.md` - This documentation

## References

- NDS 2018, Section 2.3.2 - Load Duration Factor, CD
- NDS 2018, Table 2.3.2 - Load Duration Factors
- ASCE 7 - Minimum Design Loads for Buildings
- IBC - International Building Code load combinations 