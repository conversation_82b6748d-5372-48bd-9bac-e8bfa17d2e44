/**
 * Test suite for Shear Reduction Factor (Cvr) Calculations
 *
 * @fileoverview Unit tests for NDS 5.3.10 shear reduction factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  getShearReductionFactor,
  getShearReductionFactorForStandardConditions,
  applyShearReductionFactor,
  validateShearReductionFactorInput,
  calculateShearReductionFactorDetails,
  getShearReductionFactorAnalysis,
  getMultipleShearReductionFactors,
  isDesignValueAffectedByShearReduction,
  getShearReductionFactorOptimization,
  getStandardShearReductionFactorScenarios,
  determineActiveConditions,
  assessReductionSeverity,
  generateReductionReasons,
  SHEAR_REDUCTION_CONDITIONS,
  SHEAR_REDUCTION_SEVERITY,
  SHEAR_REDUCTION_FACTOR_CONSTANTS,
  type ShearReductionFactorInput,
  type ShearReductionCondition,
  type ShearReductionSeverity,
} from "./shear-reduction-factor";

import { 
  DESIGN_VALUE_TYPES, 
  GLULAM_MEMBER_TYPES, 
  GLULAM_LOADING_TYPES 
} from "../../constants";

describe("Shear Reduction Factor (Cvr) Calculations", () => {
  describe("validateShearReductionFactorInput", () => {
    test("should validate correct input", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test("should reject missing member type", () => {
      const input: ShearReductionFactorInput = {
        memberType: null as any,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Member type is required");
    });

    test("should reject invalid member type", () => {
      const input: ShearReductionFactorInput = {
        memberType: "invalid_type" as any,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Invalid member type: invalid_type");
    });

    test("should reject missing loading type", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: null as any,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Loading type is required");
    });

    test("should reject invalid loading type", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: "invalid_loading" as any,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Invalid loading type: invalid_loading");
    });

    test("should warn for non-shear design value types", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.warnings.some(w => 
        w.includes("typically applies to shear design value")
      )).toBe(true);
    });

    test("should warn for conflicting conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        conditions: [SHEAR_REDUCTION_CONDITIONS.STANDARD, SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC],
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.warnings.some(w => 
        w.includes("contradictory")
      )).toBe(true);
    });

    test("should validate additional reduction factor", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        additionalReduction: 0.8,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(true);
    });

    test("should reject invalid additional reduction factor", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        additionalReduction: "invalid" as any,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Additional reduction must be a valid number");
    });

    test("should reject invalid boolean values", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        hasNotches: "yes" as any,
        isConnectionZone: "no" as any,
      };

      const validation = validateShearReductionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("hasNotches must be a boolean value");
      expect(validation.errors).toContain("isConnectionZone must be a boolean value");
    });
  });

  describe("determineActiveConditions", () => {
    test("should determine standard conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.STANDARD);
      expect(conditions).toHaveLength(1);
    });

    test("should determine non-prismatic condition", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC);
    });

    test("should determine impact loading condition", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING);
    });

    test("should determine cyclic loading condition", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.REPETITIVE_CYCLIC,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.CYCLIC_LOADING);
    });

    test("should determine notched condition", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        hasNotches: true,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.NOTCHED);
    });

    test("should determine connection zone condition", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        isConnectionZone: true,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.CONNECTION_ZONE);
    });

    test("should include explicitly specified conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        conditions: [SHEAR_REDUCTION_CONDITIONS.COMBINED_CONDITIONS],
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.COMBINED_CONDITIONS);
    });

    test("should determine multiple conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        hasNotches: true,
        isConnectionZone: true,
      };

      const conditions = determineActiveConditions(input);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.NOTCHED);
      expect(conditions).toContain(SHEAR_REDUCTION_CONDITIONS.CONNECTION_ZONE);
      expect(conditions).toHaveLength(4);
    });
  });

  describe("assessReductionSeverity", () => {
    test("should assess no reduction for standard conditions", () => {
      const conditions = [SHEAR_REDUCTION_CONDITIONS.STANDARD];
      const severity = assessReductionSeverity(conditions);
      expect(severity).toBe(SHEAR_REDUCTION_SEVERITY.NONE);
    });

    test("should assess minor reduction for single condition", () => {
      const conditions = [SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC];
      const severity = assessReductionSeverity(conditions);
      expect(severity).toBe(SHEAR_REDUCTION_SEVERITY.MINOR);
    });

    test("should assess moderate reduction for two conditions", () => {
      const conditions = [
        SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC,
        SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING,
      ];
      const severity = assessReductionSeverity(conditions);
      expect(severity).toBe(SHEAR_REDUCTION_SEVERITY.MODERATE);
    });

    test("should assess severe reduction for three or more conditions", () => {
      const conditions = [
        SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC,
        SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING,
        SHEAR_REDUCTION_CONDITIONS.NOTCHED,
      ];
      const severity = assessReductionSeverity(conditions);
      expect(severity).toBe(SHEAR_REDUCTION_SEVERITY.SEVERE);
    });
  });

  describe("generateReductionReasons", () => {
    test("should generate reason for standard conditions", () => {
      const conditions = [SHEAR_REDUCTION_CONDITIONS.STANDARD];
      const reasons = generateReductionReasons(conditions);
      expect(reasons).toContain("Standard conditions - no shear reduction required");
    });

    test("should generate reason for non-prismatic condition", () => {
      const conditions = [SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC];
      const reasons = generateReductionReasons(conditions);
      expect(reasons.some(r => r.includes("Non-prismatic member geometry"))).toBe(true);
    });

    test("should generate reasons for multiple conditions", () => {
      const conditions = [
        SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC,
        SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING,
      ];
      const reasons = generateReductionReasons(conditions);
      expect(reasons).toHaveLength(2);
      expect(reasons.some(r => r.includes("Non-prismatic"))).toBe(true);
      expect(reasons.some(r => r.includes("Impact loading"))).toBe(true);
    });
  });

  describe("calculateShearReductionFactorDetails", () => {
    test("should calculate standard factor for normal conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const calculation = calculateShearReductionFactorDetails(input);

      expect(calculation.shearReductionFactor).toBe(1.0);
      expect(calculation.reductionRequired).toBe(false);
      expect(calculation.reductionSeverity).toBe(SHEAR_REDUCTION_SEVERITY.NONE);
      expect(calculation.activeConditions).toContain(SHEAR_REDUCTION_CONDITIONS.STANDARD);
    });

    test("should calculate reduced factor for reduction conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const calculation = calculateShearReductionFactorDetails(input);

      expect(calculation.shearReductionFactor).toBe(0.72);
      expect(calculation.reductionRequired).toBe(true);
      expect(calculation.reductionSeverity).toBe(SHEAR_REDUCTION_SEVERITY.MODERATE);
      expect(calculation.activeConditions).toContain(SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC);
      expect(calculation.activeConditions).toContain(SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING);
    });

    test("should apply additional reduction factor", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        additionalReduction: 0.8,
      };

      const calculation = calculateShearReductionFactorDetails(input);

      expect(calculation.shearReductionFactor).toBeCloseTo(0.72 * 0.8, 3);
      expect(calculation.additionalReductions).toContain(0.8);
    });

    test("should enforce minimum reduction factor", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        additionalReduction: 0.05, // Very low, should be limited
      };

      const calculation = calculateShearReductionFactorDetails(input);

      expect(calculation.shearReductionFactor).toBeGreaterThanOrEqual(
        SHEAR_REDUCTION_FACTOR_CONSTANTS.MIN_REDUCTION_FACTOR
      );
    });
  });

  describe("getShearReductionFactor", () => {
    test("should return 1.0 for standard conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const Cvr = getShearReductionFactor(input);
      expect(Cvr).toBe(1.0);
    });

    test("should return 0.72 for reduction conditions", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const Cvr = getShearReductionFactor(input);
      expect(Cvr).toBe(0.72);
    });

    test("should throw error for invalid input", () => {
      const input: ShearReductionFactorInput = {
        memberType: null as any,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      expect(() => {
        getShearReductionFactor(input);
      }).toThrow(/Invalid input for shear reduction factor calculation/);
    });

    test("should return consistent values for equivalent conditions", () => {
      const input1: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const input2: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.REPETITIVE_CYCLIC,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const Cvr1 = getShearReductionFactor(input1);
      const Cvr2 = getShearReductionFactor(input2);
      expect(Cvr1).toBe(Cvr2); // Both should result in 0.72
    });
  });

  describe("getShearReductionFactorForStandardConditions", () => {
    test("should always return 1.0", () => {
      const Cvr = getShearReductionFactorForStandardConditions();
      expect(Cvr).toBe(1.0);
    });
  });

  describe("applyShearReductionFactor", () => {
    test("should apply shear reduction factor to reference value", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const referenceValue = 200; // psi
      const adjustedValue = applyShearReductionFactor(referenceValue, input);

      const expectedAdjusted = referenceValue * 0.72;
      expect(adjustedValue).toBeCloseTo(expectedAdjusted, 1);
    });

    test("should reject invalid reference values", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      expect(() => {
        applyShearReductionFactor(-100, input);
      }).toThrow("Reference value must be a non-negative number");

      expect(() => {
        applyShearReductionFactor(NaN, input);
      }).toThrow("Reference value must be a non-negative number");
    });
  });

  describe("getShearReductionFactorAnalysis", () => {
    test("should provide comprehensive analysis", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        hasNotches: true,
      };

      const analysis = getShearReductionFactorAnalysis(input);

      expect(analysis.input).toEqual(input);
      expect(analysis.applicableDesignValue).toBe(true);
      expect(analysis.memberCategory).toBe("non-prismatic_impact");
      expect(analysis.validation.isValid).toBe(true);
      expect(analysis.calculation.shearReductionFactor).toBe(0.72);
      expect(analysis.calculation.reductionSeverity).toBe(SHEAR_REDUCTION_SEVERITY.SEVERE);
    });

    test("should handle invalid input gracefully", () => {
      const input: ShearReductionFactorInput = {
        memberType: null as any,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const analysis = getShearReductionFactorAnalysis(input);

      expect(analysis.validation.isValid).toBe(false);
      expect(analysis.calculation.shearReductionFactor).toBe(1.0); // Default fallback
    });

    test("should identify non-applicable design values", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const analysis = getShearReductionFactorAnalysis(input);

      expect(analysis.applicableDesignValue).toBe(false);
    });
  });

  describe("getMultipleShearReductionFactors", () => {
    test("should calculate factors for multiple scenarios", () => {
      const baseInput = { designValueType: DESIGN_VALUE_TYPES.SHEAR };

      const scenarios = [
        {
          name: "Standard",
          memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
          loadingType: GLULAM_LOADING_TYPES.NORMAL,
        },
        {
          name: "Non-Prismatic",
          memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
          loadingType: GLULAM_LOADING_TYPES.NORMAL,
        },
        {
          name: "Impact",
          memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
          loadingType: GLULAM_LOADING_TYPES.IMPACT,
        },
      ];

      const results = getMultipleShearReductionFactors(baseInput, scenarios);

      expect(results["Standard"]).toBe(1.0);
      expect(results["Non-Prismatic"]).toBe(0.72);
      expect(results["Impact"]).toBe(0.72);
      
      // All should be valid numbers
      Object.values(results).forEach(factor => {
        expect(typeof factor).toBe("number");
        expect(factor).toBeGreaterThan(0);
        expect(factor).toBeLessThanOrEqual(1.0);
      });
    });

    test("should handle invalid scenarios", () => {
      const baseInput = { designValueType: DESIGN_VALUE_TYPES.SHEAR };

      const scenarios = [
        {
          name: "Valid",
          memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
          loadingType: GLULAM_LOADING_TYPES.NORMAL,
        },
        {
          name: "Invalid",
          memberType: "invalid" as any,
          loadingType: GLULAM_LOADING_TYPES.NORMAL,
        },
      ];

      const results = getMultipleShearReductionFactors(baseInput, scenarios);

      expect(typeof results["Valid"]).toBe("number");
      expect(results["Valid"]).toBe(1.0);
      expect(isNaN(results["Invalid"])).toBe(true);
    });
  });

  describe("isDesignValueAffectedByShearReduction", () => {
    test("should return true for shear design values", () => {
      expect(isDesignValueAffectedByShearReduction(DESIGN_VALUE_TYPES.SHEAR)).toBe(true);
    });

    test("should return false for non-shear design values", () => {
      expect(isDesignValueAffectedByShearReduction(DESIGN_VALUE_TYPES.BENDING)).toBe(false);
      expect(isDesignValueAffectedByShearReduction(DESIGN_VALUE_TYPES.TENSION_PARALLEL)).toBe(false);
      expect(isDesignValueAffectedByShearReduction(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL)).toBe(false);
      expect(isDesignValueAffectedByShearReduction(DESIGN_VALUE_TYPES.E)).toBe(false);
    });
  });

  describe("getShearReductionFactorOptimization", () => {
    test("should provide optimization for achievable target", () => {
      const optimization = getShearReductionFactorOptimization(0.95, {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
      });

      expect(optimization.achievable).toBe(true);
      expect(optimization.currentFactor).toBe(1.0);
      expect(optimization.recommendedChanges.some(r => 
        r.includes("achieves target factor")
      )).toBe(true);
    });

    test("should provide optimization for non-achievable target", () => {
      const optimization = getShearReductionFactorOptimization(0.95, {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        hasNotches: true,
        isConnectionZone: true,
      });

      expect(optimization.achievable).toBe(false);
      expect(optimization.currentFactor).toBe(0.72);
      expect(optimization.recommendedChanges.length).toBeGreaterThan(0);
      expect(optimization.alternativeApproaches.length).toBeGreaterThan(0);
    });

    test("should provide specific recommendations for each condition", () => {
      const optimization = getShearReductionFactorOptimization(0.95, {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        hasNotches: true,
        isConnectionZone: true,
      });

      expect(optimization.recommendedChanges.some(r => 
        r.includes("prismatic member geometry")
      )).toBe(true);
      expect(optimization.recommendedChanges.some(r => 
        r.includes("impact loading")
      )).toBe(true);
      expect(optimization.recommendedChanges.some(r => 
        r.includes("notches")
      )).toBe(true);
      expect(optimization.recommendedChanges.some(r => 
        r.includes("connection zone")
      )).toBe(true);
    });
  });

  describe("getStandardShearReductionFactorScenarios", () => {
    test("should return standard scenarios", () => {
      const scenarios = getStandardShearReductionFactorScenarios();

      expect(scenarios["Standard Prismatic"]).toBe(1.0);
      expect(scenarios["Non-Prismatic"]).toBe(0.72);
      expect(scenarios["Impact Loading"]).toBe(0.72);
      expect(scenarios["Cyclic Loading"]).toBe(0.72);
      expect(scenarios["Notched Member"]).toBe(0.72);
      expect(scenarios["Connection Zone"]).toBe(0.72);
      expect(scenarios["Worst Case"]).toBe(0.72);

      // Check that all scenarios are present
      expect(Object.keys(scenarios)).toContain("Standard Prismatic");
      expect(Object.keys(scenarios)).toContain("Non-Prismatic");
      expect(Object.keys(scenarios)).toContain("Impact Loading");
      expect(Object.keys(scenarios)).toContain("Cyclic Loading");
      expect(Object.keys(scenarios)).toContain("Notched Member");
      expect(Object.keys(scenarios)).toContain("Connection Zone");
      expect(Object.keys(scenarios)).toContain("Worst Case");
    });
  });

  describe("Constants and Types", () => {
    test("should export required constants", () => {
      expect(SHEAR_REDUCTION_CONDITIONS).toBeDefined();
      expect(SHEAR_REDUCTION_SEVERITY).toBeDefined();
      expect(SHEAR_REDUCTION_FACTOR_CONSTANTS).toBeDefined();
    });

    test("should have correct condition values", () => {
      expect(Object.values(SHEAR_REDUCTION_CONDITIONS)).toContain("standard");
      expect(Object.values(SHEAR_REDUCTION_CONDITIONS)).toContain("non_prismatic");
      expect(Object.values(SHEAR_REDUCTION_CONDITIONS)).toContain("impact_loading");
      expect(Object.values(SHEAR_REDUCTION_CONDITIONS)).toContain("cyclic_loading");
      expect(Object.values(SHEAR_REDUCTION_CONDITIONS)).toContain("notched");
      expect(Object.values(SHEAR_REDUCTION_CONDITIONS)).toContain("connection_zone");
    });

    test("should have correct severity values", () => {
      expect(Object.values(SHEAR_REDUCTION_SEVERITY)).toContain("none");
      expect(Object.values(SHEAR_REDUCTION_SEVERITY)).toContain("minor");
      expect(Object.values(SHEAR_REDUCTION_SEVERITY)).toContain("moderate");
      expect(Object.values(SHEAR_REDUCTION_SEVERITY)).toContain("severe");
    });

    test("should have reasonable constant values", () => {
      expect(SHEAR_REDUCTION_FACTOR_CONSTANTS.STANDARD_FACTOR).toBe(1.0);
      expect(SHEAR_REDUCTION_FACTOR_CONSTANTS.REDUCED_FACTOR).toBe(0.72);
      expect(SHEAR_REDUCTION_FACTOR_CONSTANTS.DEFAULT_FACTOR).toBe(1.0);
      expect(SHEAR_REDUCTION_FACTOR_CONSTANTS.MIN_REDUCTION_FACTOR).toBe(0.1);
      expect(SHEAR_REDUCTION_FACTOR_CONSTANTS.MAX_REDUCTION_FACTOR).toBe(1.0);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle extreme additional reduction factors", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.IMPACT,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        additionalReduction: 0.01, // Very low
      };

      const Cvr = getShearReductionFactor(input);
      expect(Cvr).toBeGreaterThanOrEqual(SHEAR_REDUCTION_FACTOR_CONSTANTS.MIN_REDUCTION_FACTOR);
    });

    test("should handle missing optional parameters", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        // All optional parameters omitted
      };

      const Cvr = getShearReductionFactor(input);
      expect(typeof Cvr).toBe("number");
      expect(Cvr).toBe(1.0);
    });

    test("should handle floating point precision", () => {
      const input: ShearReductionFactorInput = {
        memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
        loadingType: GLULAM_LOADING_TYPES.NORMAL,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
        additionalReduction: 0.9999999,
      };

      expect(() => {
        getShearReductionFactor(input);
      }).not.toThrow();
    });
  });
}); 