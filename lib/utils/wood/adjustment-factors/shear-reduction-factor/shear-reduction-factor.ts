/**
 * Shear Reduction Factor (Cvr) Calculations
 * 
 * Implements NDS 5.3.10 Shear Reduction Factor for adjusting glulam shear design values
 * based on member configuration and loading conditions for glued laminated timber members.
 * 
 * Based on:
 * - NDS Section 5.3.10: Shear Reduction Factor for glulam members
 * - Cvr = 0.72 for specific conditions (non-prismatic, impact/cyclic loads, notches, connections)
 * - Cvr = 1.0 for standard prismatic members under normal loading
 * - Applicability: All glulam members (affects shear design values Fv)
 * 
 * @fileoverview Shear reduction factor calculations for glulam structural design
 * <AUTHOR> Engineering Application  
 * @version 1.0.0
 * @since 2024
 * 
 * @references
 * - NDS 2018 Section 5.3.10: Shear Reduction Factor
 * - NDS criteria for reduced shear capacity conditions
 * - Shear reduction factor applies to shear design values (Fv) for all glulam members
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  GLULAM_SHEAR_REDUCTION_CONSTANTS,
  GLULAM_MEMBER_TYPES,
  GLULAM_LOADING_TYPES,
  type GlulamMemberType,
  type GlulamLoadingType,
} from "../../constants";

/**
 * Connection and notch conditions affecting shear reduction
 */
export const SHEAR_REDUCTION_CONDITIONS = {
  STANDARD: 'standard',                    // Standard conditions (no reduction)
  NON_PRISMATIC: 'non_prismatic',         // Non-prismatic member geometry
  IMPACT_LOADING: 'impact_loading',       // Impact loading conditions
  CYCLIC_LOADING: 'cyclic_loading',       // Repetitive cyclic loading
  NOTCHED: 'notched',                     // Member has notches
  CONNECTION_ZONE: 'connection_zone',     // At connection locations
  COMBINED_CONDITIONS: 'combined_conditions' // Multiple reduction conditions present
} as const;

export type ShearReductionCondition = typeof SHEAR_REDUCTION_CONDITIONS[keyof typeof SHEAR_REDUCTION_CONDITIONS];

/**
 * Severity levels for shear reduction conditions
 */
export const SHEAR_REDUCTION_SEVERITY = {
  NONE: 'none',           // No reduction conditions (Cvr = 1.0)
  MINOR: 'minor',         // Single condition present
  MODERATE: 'moderate',   // Multiple conditions or severe single condition
  SEVERE: 'severe',       // Critical conditions requiring maximum reduction
} as const;

export type ShearReductionSeverity = typeof SHEAR_REDUCTION_SEVERITY[keyof typeof SHEAR_REDUCTION_SEVERITY];

/**
 * Input parameters for shear reduction factor calculation
 */
export interface ShearReductionFactorInput {
  /** Type of member geometry (prismatic vs non-prismatic) */
  memberType: GlulamMemberType;
  
  /** Type of loading conditions */
  loadingType: GlulamLoadingType;
  
  /** Type of design value (should be Fv for shear reduction factor) */
  designValueType: DesignValueType;
  
  /** Specific conditions affecting shear capacity */
  conditions?: ShearReductionCondition[];
  
  /** Whether member has notches */
  hasNotches?: boolean;
  
  /** Whether member is in connection zone */
  isConnectionZone?: boolean;
  
  /** Additional reduction factor for special cases */
  additionalReduction?: number;
}

/**
 * Validation result for shear reduction factor input
 */
export interface ShearReductionFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Detailed shear reduction factor calculation results
 */
export interface ShearReductionFactorCalculation {
  /** Calculated shear reduction factor */
  shearReductionFactor: number;
  
  /** Member type classification */
  memberType: GlulamMemberType;
  
  /** Loading type classification */
  loadingType: GlulamLoadingType;
  
  /** Active reduction conditions */
  activeConditions: ShearReductionCondition[];
  
  /** Overall severity assessment */
  reductionSeverity: ShearReductionSeverity;
  
  /** Whether reduction is required */
  reductionRequired: boolean;
  
  /** Detailed reasoning for reduction */
  reductionReasons: string[];
  
  /** Applied additional reductions */
  additionalReductions: number[];
}

/**
 * Complete shear reduction factor analysis results
 */
export interface ShearReductionFactorAnalysis {
  input: ShearReductionFactorInput;
  calculation: ShearReductionFactorCalculation;
  applicableDesignValue: boolean;
  memberCategory: string;
  validation: ShearReductionFactorValidation;
}

/**
 * Shear reduction factor constants and specifications
 */
export const SHEAR_REDUCTION_FACTOR_CONSTANTS = {
  // Standard reduction factors (NDS 5.3.10)
  STANDARD_FACTOR: GLULAM_SHEAR_REDUCTION_CONSTANTS.NORMAL_FACTOR,
  REDUCED_FACTOR: GLULAM_SHEAR_REDUCTION_CONSTANTS.REDUCED_FACTOR,
  
  // Default values
  DEFAULT_FACTOR: GLULAM_SHEAR_REDUCTION_CONSTANTS.NORMAL_FACTOR,
  
  // Validation limits
  MIN_REDUCTION_FACTOR: 0.1,              // Minimum practical reduction factor
  MAX_REDUCTION_FACTOR: 1.0,              // Maximum reduction factor (no reduction)
  MIN_ADDITIONAL_REDUCTION: 0.5,          // Minimum additional reduction
  MAX_ADDITIONAL_REDUCTION: 1.0,          // Maximum additional reduction (no additional reduction)
  
  // Condition thresholds
  SEVERE_CONDITION_THRESHOLD: 3,          // Number of conditions for severe classification
  MODERATE_CONDITION_THRESHOLD: 2,        // Number of conditions for moderate classification
} as const;

/**
 * Validate shear reduction factor input parameters
 * @param input - Shear reduction factor input parameters
 * @returns Validation result with errors and warnings
 */
export function validateShearReductionFactorInput(
  input: ShearReductionFactorInput
): ShearReductionFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate member type
  if (!input.memberType) {
    errors.push('Member type is required');
  } else if (!Object.values(GLULAM_MEMBER_TYPES).includes(input.memberType)) {
    errors.push(`Invalid member type: ${input.memberType}`);
  }

  // Validate loading type
  if (!input.loadingType) {
    errors.push('Loading type is required');
  } else if (!Object.values(GLULAM_LOADING_TYPES).includes(input.loadingType)) {
    errors.push(`Invalid loading type: ${input.loadingType}`);
  }

  // Validate design value type
  if (!input.designValueType) {
    errors.push('Design value type is required');
  } else if (input.designValueType !== DESIGN_VALUE_TYPES.SHEAR) {
    warnings.push(`Shear reduction factor typically applies to shear design value (Fv), got: ${input.designValueType}`);
  }

  // Validate conditions if provided
  if (input.conditions) {
    const invalidConditions = input.conditions.filter(
      condition => !Object.values(SHEAR_REDUCTION_CONDITIONS).includes(condition)
    );
    if (invalidConditions.length > 0) {
      warnings.push(`Unknown reduction conditions: ${invalidConditions.join(', ')}`);
    }

    // Check for conflicting conditions
    if (input.conditions.includes(SHEAR_REDUCTION_CONDITIONS.STANDARD) && input.conditions.length > 1) {
      warnings.push('Standard condition specified along with other conditions - this may be contradictory');
    }
  }

  // Validate additional reduction if provided
  if (input.additionalReduction !== undefined) {
    if (typeof input.additionalReduction !== 'number' || isNaN(input.additionalReduction)) {
      errors.push('Additional reduction must be a valid number');
    } else {
      if (input.additionalReduction < SHEAR_REDUCTION_FACTOR_CONSTANTS.MIN_ADDITIONAL_REDUCTION) {
        warnings.push(`Additional reduction ${input.additionalReduction} is very low (< ${SHEAR_REDUCTION_FACTOR_CONSTANTS.MIN_ADDITIONAL_REDUCTION})`);
      } else if (input.additionalReduction > SHEAR_REDUCTION_FACTOR_CONSTANTS.MAX_ADDITIONAL_REDUCTION) {
        warnings.push(`Additional reduction ${input.additionalReduction} exceeds maximum (${SHEAR_REDUCTION_FACTOR_CONSTANTS.MAX_ADDITIONAL_REDUCTION})`);
      }
    }
  }

  // Validate boolean flags
  if (input.hasNotches !== undefined && typeof input.hasNotches !== 'boolean') {
    errors.push('hasNotches must be a boolean value');
  }

  if (input.isConnectionZone !== undefined && typeof input.isConnectionZone !== 'boolean') {
    errors.push('isConnectionZone must be a boolean value');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Determine active reduction conditions based on input
 * @param input - Shear reduction factor input parameters
 * @returns Array of active reduction conditions
 */
export function determineActiveConditions(
  input: ShearReductionFactorInput
): ShearReductionCondition[] {
  const conditions: ShearReductionCondition[] = [];

  // Check member type
  if (input.memberType === GLULAM_MEMBER_TYPES.NON_PRISMATIC) {
    conditions.push(SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC);
  }

  // Check loading type
  if (input.loadingType === GLULAM_LOADING_TYPES.IMPACT) {
    conditions.push(SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING);
  } else if (input.loadingType === GLULAM_LOADING_TYPES.REPETITIVE_CYCLIC) {
    conditions.push(SHEAR_REDUCTION_CONDITIONS.CYCLIC_LOADING);
  }

  // Check boolean flags
  if (input.hasNotches) {
    conditions.push(SHEAR_REDUCTION_CONDITIONS.NOTCHED);
  }

  if (input.isConnectionZone) {
    conditions.push(SHEAR_REDUCTION_CONDITIONS.CONNECTION_ZONE);
  }

  // Include explicitly specified conditions
  if (input.conditions) {
    input.conditions.forEach(condition => {
      if (!conditions.includes(condition)) {
        conditions.push(condition);
      }
    });
  }

  // If no specific conditions, it's standard
  if (conditions.length === 0) {
    conditions.push(SHEAR_REDUCTION_CONDITIONS.STANDARD);
  }

  return conditions;
}

/**
 * Assess reduction severity based on active conditions
 * @param conditions - Array of active reduction conditions
 * @returns Reduction severity level
 */
export function assessReductionSeverity(
  conditions: ShearReductionCondition[]
): ShearReductionSeverity {
  // Standard conditions = no reduction
  if (conditions.length === 1 && conditions.includes(SHEAR_REDUCTION_CONDITIONS.STANDARD)) {
    return SHEAR_REDUCTION_SEVERITY.NONE;
  }

  // Count non-standard conditions
  const nonStandardConditions = conditions.filter(
    condition => condition !== SHEAR_REDUCTION_CONDITIONS.STANDARD
  );

  if (nonStandardConditions.length >= SHEAR_REDUCTION_FACTOR_CONSTANTS.SEVERE_CONDITION_THRESHOLD) {
    return SHEAR_REDUCTION_SEVERITY.SEVERE;
  } else if (nonStandardConditions.length >= SHEAR_REDUCTION_FACTOR_CONSTANTS.MODERATE_CONDITION_THRESHOLD) {
    return SHEAR_REDUCTION_SEVERITY.MODERATE;
  } else if (nonStandardConditions.length > 0) {
    return SHEAR_REDUCTION_SEVERITY.MINOR;
  } else {
    return SHEAR_REDUCTION_SEVERITY.NONE;
  }
}

/**
 * Generate reduction reasons based on active conditions
 * @param conditions - Array of active reduction conditions
 * @returns Array of human-readable reduction reasons
 */
export function generateReductionReasons(
  conditions: ShearReductionCondition[]
): string[] {
  const reasons: string[] = [];

  conditions.forEach(condition => {
    switch (condition) {
      case SHEAR_REDUCTION_CONDITIONS.NON_PRISMATIC:
        reasons.push('Non-prismatic member geometry reduces shear capacity (NDS 5.3.10)');
        break;
      case SHEAR_REDUCTION_CONDITIONS.IMPACT_LOADING:
        reasons.push('Impact loading conditions require reduced shear capacity (NDS 5.3.10)');
        break;
      case SHEAR_REDUCTION_CONDITIONS.CYCLIC_LOADING:
        reasons.push('Repetitive cyclic loading reduces shear capacity (NDS 5.3.10)');
        break;
      case SHEAR_REDUCTION_CONDITIONS.NOTCHED:
        reasons.push('Notches in member reduce effective shear capacity (NDS 5.3.10)');
        break;
      case SHEAR_REDUCTION_CONDITIONS.CONNECTION_ZONE:
        reasons.push('Connection zone effects reduce shear capacity (NDS 5.3.10)');
        break;
      case SHEAR_REDUCTION_CONDITIONS.COMBINED_CONDITIONS:
        reasons.push('Multiple reduction conditions present (NDS 5.3.10)');
        break;
      case SHEAR_REDUCTION_CONDITIONS.STANDARD:
        reasons.push('Standard conditions - no shear reduction required');
        break;
    }
  });

  return reasons;
}

/**
 * Calculate detailed shear reduction factor results
 * @param input - Shear reduction factor input parameters
 * @returns Detailed calculation results
 */
export function calculateShearReductionFactorDetails(
  input: ShearReductionFactorInput
): ShearReductionFactorCalculation {
  // Validate input first
  const validation = validateShearReductionFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input for shear reduction factor calculation: ${validation.errors.join(', ')}`);
  }

  // Determine active conditions
  const activeConditions = determineActiveConditions(input);
  const reductionSeverity = assessReductionSeverity(activeConditions);
  const reductionReasons = generateReductionReasons(activeConditions);

  // Determine if reduction is required
  const reductionRequired = reductionSeverity !== SHEAR_REDUCTION_SEVERITY.NONE;

  // Calculate base shear reduction factor
  let shearReductionFactor: number;
  if (reductionRequired) {
    shearReductionFactor = SHEAR_REDUCTION_FACTOR_CONSTANTS.REDUCED_FACTOR; // 0.72
  } else {
    shearReductionFactor = SHEAR_REDUCTION_FACTOR_CONSTANTS.STANDARD_FACTOR; // 1.0
  }

  // Apply additional reductions if specified
  const additionalReductions: number[] = [];
  if (input.additionalReduction && input.additionalReduction < 1.0) {
    additionalReductions.push(input.additionalReduction);
    shearReductionFactor *= input.additionalReduction;
  }

  // Ensure factor is within valid range
  shearReductionFactor = Math.max(
    SHEAR_REDUCTION_FACTOR_CONSTANTS.MIN_REDUCTION_FACTOR,
    Math.min(SHEAR_REDUCTION_FACTOR_CONSTANTS.MAX_REDUCTION_FACTOR, shearReductionFactor)
  );

  return {
    shearReductionFactor,
    memberType: input.memberType,
    loadingType: input.loadingType,
    activeConditions,
    reductionSeverity,
    reductionRequired,
    reductionReasons,
    additionalReductions,
  };
}

/**
 * Calculate shear reduction factor for glulam members
 * @param input - Shear reduction factor input parameters
 * @returns Shear reduction factor Cvr
 * @throws Error if input is invalid
 * 
 * @example
 * ```typescript
 * const Cvr = getShearReductionFactor({
 *   memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
 *   loadingType: GLULAM_LOADING_TYPES.IMPACT,
 *   designValueType: DESIGN_VALUE_TYPES.SHEAR,
 *   hasNotches: true
 * });
 * console.log(`Shear reduction factor: ${Cvr}`);
 * ```
 */
export function getShearReductionFactor(input: ShearReductionFactorInput): number {
  const calculation = calculateShearReductionFactorDetails(input);
  return calculation.shearReductionFactor;
}

/**
 * Calculate shear reduction factor for standard conditions (convenience function)
 * @returns Shear reduction factor of 1.0 for standard conditions
 */
export function getShearReductionFactorForStandardConditions(): number {
  return SHEAR_REDUCTION_FACTOR_CONSTANTS.STANDARD_FACTOR;
}

/**
 * Apply shear reduction factor to a reference design value
 * @param referenceValue - Reference shear design value (psi)
 * @param input - Shear reduction factor input parameters
 * @returns Adjusted design value
 */
export function applyShearReductionFactor(
  referenceValue: number,
  input: ShearReductionFactorInput
): number {
  if (typeof referenceValue !== 'number' || isNaN(referenceValue) || referenceValue < 0) {
    throw new Error('Reference value must be a non-negative number');
  }
  
  const shearReductionFactor = getShearReductionFactor(input);
  return referenceValue * shearReductionFactor;
}

/**
 * Get comprehensive shear reduction factor analysis
 * @param input - Shear reduction factor input parameters
 * @returns Complete analysis results
 */
export function getShearReductionFactorAnalysis(
  input: ShearReductionFactorInput
): ShearReductionFactorAnalysis {
  const validation = validateShearReductionFactorInput(input);
  
  let calculation: ShearReductionFactorCalculation;
  try {
    calculation = calculateShearReductionFactorDetails(input);
  } catch (error) {
    // If calculation fails, provide default values
    calculation = {
      shearReductionFactor: SHEAR_REDUCTION_FACTOR_CONSTANTS.DEFAULT_FACTOR,
      memberType: input.memberType || GLULAM_MEMBER_TYPES.PRISMATIC,
      loadingType: input.loadingType || GLULAM_LOADING_TYPES.NORMAL,
      activeConditions: [SHEAR_REDUCTION_CONDITIONS.STANDARD],
      reductionSeverity: SHEAR_REDUCTION_SEVERITY.NONE,
      reductionRequired: false,
      reductionReasons: ['Default values used due to calculation error'],
      additionalReductions: [],
    };
  }
  
  const applicableDesignValue = input.designValueType === DESIGN_VALUE_TYPES.SHEAR;
  const memberCategory = `${calculation.memberType}_${calculation.loadingType}`;
  
  return {
    input,
    calculation,
    applicableDesignValue,
    memberCategory,
    validation,
  };
}

/**
 * Get shear reduction factors for multiple scenarios
 * @param baseInput - Base input parameters (without scenario variations)
 * @param scenarios - Array of different conditions to evaluate
 * @returns Record of scenario names to shear reduction factors
 */
export function getMultipleShearReductionFactors(
  baseInput: Pick<ShearReductionFactorInput, 'designValueType'>,
  scenarios: Array<{
    name: string;
    memberType: GlulamMemberType;
    loadingType: GlulamLoadingType;
    conditions?: ShearReductionCondition[];
    hasNotches?: boolean;
    isConnectionZone?: boolean;
  }>
): Record<string, number> {
  const results: Record<string, number> = {};
  
  for (const scenario of scenarios) {
    try {
      const input: ShearReductionFactorInput = {
        ...baseInput,
        memberType: scenario.memberType,
        loadingType: scenario.loadingType,
        conditions: scenario.conditions,
        hasNotches: scenario.hasNotches,
        isConnectionZone: scenario.isConnectionZone,
      };
      results[scenario.name] = getShearReductionFactor(input);
    } catch (error) {
      results[scenario.name] = NaN; // Indicate invalid scenario
    }
  }
  
  return results;
}

/**
 * Check if design value is affected by shear reduction factor
 * @param designValueType - Type of design value
 * @returns True if shear reduction factor applies
 */
export function isDesignValueAffectedByShearReduction(
  designValueType: DesignValueType
): boolean {
  return designValueType === DESIGN_VALUE_TYPES.SHEAR;
}

/**
 * Get shear reduction factor optimization recommendations
 * @param targetShearReductionFactor - Desired shear reduction factor (typically close to 1.0)
 * @param currentConditions - Current member and loading conditions
 * @returns Optimization recommendations
 */
export function getShearReductionFactorOptimization(
  targetShearReductionFactor: number = 0.95,
  currentConditions: {
    memberType: GlulamMemberType;
    loadingType: GlulamLoadingType;
    hasNotches?: boolean;
    isConnectionZone?: boolean;
  }
): {
  targetShearReductionFactor: number;
  achievable: boolean;
  recommendedChanges: string[];
  alternativeApproaches: string[];
  currentFactor: number;
} {
  const currentInput: ShearReductionFactorInput = {
    ...currentConditions,
    designValueType: DESIGN_VALUE_TYPES.SHEAR,
  };

  const currentFactor = getShearReductionFactor(currentInput);
  const achievable = currentFactor >= targetShearReductionFactor;

  const recommendedChanges: string[] = [];
  const alternativeApproaches: string[] = [];

  if (!achievable) {
    // Recommend changes to improve shear reduction factor
    if (currentConditions.memberType === GLULAM_MEMBER_TYPES.NON_PRISMATIC) {
      recommendedChanges.push('Consider using prismatic member geometry instead of non-prismatic');
    }

    if (currentConditions.loadingType === GLULAM_LOADING_TYPES.IMPACT) {
      recommendedChanges.push('Consider modifying design to avoid impact loading conditions');
      alternativeApproaches.push('Use impact-resistant connection details');
    }

    if (currentConditions.loadingType === GLULAM_LOADING_TYPES.REPETITIVE_CYCLIC) {
      recommendedChanges.push('Consider reducing cyclic loading through design modifications');
      alternativeApproaches.push('Use fatigue-resistant connection details');
    }

    if (currentConditions.hasNotches) {
      recommendedChanges.push('Eliminate notches or minimize their impact on shear capacity');
      alternativeApproaches.push('Use mechanical fasteners instead of notched connections');
    }

    if (currentConditions.isConnectionZone) {
      recommendedChanges.push('Move critical shear location away from connection zones');
      alternativeApproaches.push('Use reinforced connection details to maintain shear capacity');
    }

    // General recommendations
    alternativeApproaches.push('Increase member depth to reduce shear stress demand');
    alternativeApproaches.push('Use higher grade glulam material with higher shear capacity');
    alternativeApproaches.push('Consider steel reinforcement in high shear zones');
  } else {
    recommendedChanges.push(`Current design achieves target factor (${currentFactor.toFixed(3)} ≥ ${targetShearReductionFactor})`);
  }

  // Always include verification recommendations
  alternativeApproaches.push('Verify that all NDS shear design requirements are met');
  alternativeApproaches.push('Consider manufacturer recommendations for specific glulam products');

  return {
    targetShearReductionFactor,
    achievable,
    recommendedChanges,
    alternativeApproaches,
    currentFactor,
  };
}

/**
 * Get standard shear reduction factor scenarios for comparison
 * @returns Record of standard scenario names to shear reduction factors
 */
export function getStandardShearReductionFactorScenarios(): Record<string, number> {
  const baseInput = { designValueType: DESIGN_VALUE_TYPES.SHEAR };
  
  const scenarios = [
    {
      name: 'Standard Prismatic',
      memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.NORMAL,
    },
    {
      name: 'Non-Prismatic',
      memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.NORMAL,
    },
    {
      name: 'Impact Loading',
      memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.IMPACT,
    },
    {
      name: 'Cyclic Loading',
      memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.REPETITIVE_CYCLIC,
    },
    {
      name: 'Notched Member',
      memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.NORMAL,
      hasNotches: true,
    },
    {
      name: 'Connection Zone',
      memberType: GLULAM_MEMBER_TYPES.PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.NORMAL,
      isConnectionZone: true,
    },
    {
      name: 'Worst Case',
      memberType: GLULAM_MEMBER_TYPES.NON_PRISMATIC,
      loadingType: GLULAM_LOADING_TYPES.IMPACT,
      hasNotches: true,
      isConnectionZone: true,
    },
  ];

  return getMultipleShearReductionFactors(baseInput, scenarios);
} 