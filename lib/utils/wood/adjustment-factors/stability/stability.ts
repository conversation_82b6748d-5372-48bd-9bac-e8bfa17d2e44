/**
 * Adjustment Factors for Sawn Lumber Design Values
 * Based on NDS (National Design Specification) for Wood Construction
 */

import {
  LOADING_CONDITIONS,
  LoadingCondition,
  BEAM_STABILITY_CONSTANTS,
  EFFECTIVE_LENGTH_THRESHOLDS,
  EFFECTIVE_LENGTH_FACTORS,
} from "../../constants";

/**
 * Calculates the beam stability factor, CL, which accounts for lateral-torsional buckling
 * in bending members.
 *
 * Algorithm per NDS 3.3.3:
 * 1. Determine the effective length, le, from NDS Table 3.3.3
 * 2. Calculate slenderness ratio: RB = √((le*d)/b²). If RB > 50, fail.
 * 3. Calculate critical buckling design value: FbE = (1.20 × E'min)/RB²
 * 4. Calculate CL using the formula from NDS 3.3.6
 * 5. Return CL
 *
 * @param unsupportedLength - Distance between points of lateral support, in inches
 * @param d - The dressed depth of the beam, in inches
 * @param b - The dressed breadth of the beam, in inches
 * @param eMinAdj - Adjusted modulus of elasticity for stability, E'min, in psi
 * @param refFbAdj - Reference bending value multiplied by all factors except CL, CV, and Cfu (Fb*), in psi
 * @returns The beam stability factor CL
 * @throws Error if slenderness ratio exceeds 50 or if inputs are invalid
 */
export function getBeamStabilityFactorCL(
  unsupportedLength: number,
  d: number,
  b: number,
  eMinAdj: number,
  refFbAdj: number
): number {
  // Input validation
  if (d <= 0 || b <= 0 || eMinAdj <= 0 || refFbAdj <= 0) {
    throw new Error(
      "All input parameters except unsupportedLength must be positive values"
    );
  }

  // Handle fully braced case (unsupportedLength = 0)
  if (unsupportedLength <= 0) {
    return 1.0; // Fully braced beam has no lateral-torsional buckling, so CL = 1.0
  }

  // Step 1: Determine the effective length, le, from NDS Table 3.3.3
  // For uniformly distributed load (most common case):
  const luOverD = unsupportedLength / d;
  let le: number;

  if (luOverD > EFFECTIVE_LENGTH_THRESHOLDS.HIGH_RATIO) {
    le =
      EFFECTIVE_LENGTH_FACTORS.UNIFORMLY_DISTRIBUTED.HIGH_RATIO *
      unsupportedLength;
  } else if (luOverD <= EFFECTIVE_LENGTH_THRESHOLDS.LOW_RATIO) {
    le =
      EFFECTIVE_LENGTH_FACTORS.UNIFORMLY_DISTRIBUTED.LOW_RATIO *
      unsupportedLength;
  } else {
    // 7 < lu/d ≤ 14.3
    le =
      EFFECTIVE_LENGTH_FACTORS.UNIFORMLY_DISTRIBUTED.INTERMEDIATE *
        unsupportedLength +
      BEAM_STABILITY_CONSTANTS.DEPTH_MULTIPLIER * d;
  }

  // Step 2: Calculate slenderness ratio: RB = √((le*d)/b²)
  const RB = Math.sqrt((le * d) / (b * b));

  // Check slenderness ratio limit per NDS 3.3.7
  if (RB > BEAM_STABILITY_CONSTANTS.MAX_SLENDERNESS_RATIO) {
    throw new Error(
      `Slenderness ratio RB = ${RB.toFixed(
        2
      )} exceeds maximum allowed value of ${
        BEAM_STABILITY_CONSTANTS.MAX_SLENDERNESS_RATIO
      }`
    );
  }

  // Step 3: Calculate critical buckling design value: FbE = (1.20 × E'min)/RB²
  const FbE =
    (BEAM_STABILITY_CONSTANTS.CRITICAL_BUCKLING_COEFFICIENT * eMinAdj) /
    (RB * RB);

  // Step 4: Calculate CL using the formula from NDS 3.3.6
  // CL = [1+(FbE/Fb*)/1.9] - √{[1+(FbE/Fb*)/1.9]² - FbE/Fb*/0.95}
  const FbEOverFb = FbE / refFbAdj;
  const term1 = 1 + FbEOverFb / BEAM_STABILITY_CONSTANTS.CL_DENOMINATOR_1;
  const term2 = Math.sqrt(
    Math.pow(term1, 2) - FbEOverFb / BEAM_STABILITY_CONSTANTS.CL_DENOMINATOR_2
  );

  const CL = term1 - term2;

  // Step 5: Return CL
  // CL should be ≤ 1.0 per NDS requirements
  return Math.min(CL, BEAM_STABILITY_CONSTANTS.MAX_BEAM_STABILITY_FACTOR);
}

/**
 * Gets the effective length factor based on loading and support conditions
 * from NDS Table 3.3.3
 *
 * @param loadingCondition - Type of loading condition
 * @param luOverD - Ratio of unsupported length to depth
 * @returns Effective length factor
 */
export function getEffectiveLengthFactor(
  loadingCondition: LoadingCondition,
  luOverD: number
): number {
  switch (loadingCondition) {
    case LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED:
      if (luOverD <= EFFECTIVE_LENGTH_THRESHOLDS.LOW_RATIO) {
        return EFFECTIVE_LENGTH_FACTORS.UNIFORMLY_DISTRIBUTED.LOW_RATIO;
      }
      if (luOverD > EFFECTIVE_LENGTH_THRESHOLDS.HIGH_RATIO) {
        return EFFECTIVE_LENGTH_FACTORS.UNIFORMLY_DISTRIBUTED.HIGH_RATIO;
      }
      return EFFECTIVE_LENGTH_FACTORS.UNIFORMLY_DISTRIBUTED.INTERMEDIATE; // For 7 < lu/d ≤ 14.3, coefficient is 1.63 (with +3d term)

    case LOADING_CONDITIONS.CONCENTRATED_CENTER:
      if (luOverD <= EFFECTIVE_LENGTH_THRESHOLDS.LOW_RATIO) {
        return EFFECTIVE_LENGTH_FACTORS.CONCENTRATED_CENTER.LOW_RATIO;
      }
      return EFFECTIVE_LENGTH_FACTORS.CONCENTRATED_CENTER.HIGH_RATIO; // For lu/d > 7

    case LOADING_CONDITIONS.CONCENTRATED_END:
      if (luOverD <= EFFECTIVE_LENGTH_THRESHOLDS.LOW_RATIO) {
        return EFFECTIVE_LENGTH_FACTORS.CONCENTRATED_END.LOW_RATIO;
      }
      return EFFECTIVE_LENGTH_FACTORS.CONCENTRATED_END.HIGH_RATIO; // For lu/d > 7

    case LOADING_CONDITIONS.EQUAL_END_MOMENTS:
      return EFFECTIVE_LENGTH_FACTORS.EQUAL_END_MOMENTS;

    default:
      throw new Error(`Unknown loading condition: ${loadingCondition}`);
  }
}

/**
 * Alternative function signature with loading condition parameter for more precise calculations
 *
 * @param unsupportedLength - Distance between points of lateral support, in inches
 * @param d - The dressed depth of the beam, in inches
 * @param b - The dressed breadth of the beam, in inches
 * @param eMinAdj - Adjusted modulus of elasticity for stability, E'min, in psi
 * @param refFbAdj - Reference bending value multiplied by all factors except CL, CV, and Cfu (Fb*), in psi
 * @param loadingCondition - Type of loading condition (defaults to uniformly distributed)
 * @returns The beam stability factor CL
 */
export function getBeamStabilityFactorCLDetailed(
  unsupportedLength: number,
  d: number,
  b: number,
  eMinAdj: number,
  refFbAdj: number,
  loadingCondition: LoadingCondition = LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED
): number {
  // Input validation
  if (d <= 0 || b <= 0 || eMinAdj <= 0 || refFbAdj <= 0) {
    throw new Error(
      "All input parameters except unsupportedLength must be positive values"
    );
  }

  // Handle fully braced case (unsupportedLength = 0)
  if (unsupportedLength <= 0) {
    return 1.0; // Fully braced beam has no lateral-torsional buckling, so CL = 1.0
  }

  const luOverD = unsupportedLength / d;
  const factor = getEffectiveLengthFactor(loadingCondition, luOverD);

  // Calculate effective length based on loading condition
  let le: number;
  if (
    loadingCondition === LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED &&
    luOverD > EFFECTIVE_LENGTH_THRESHOLDS.LOW_RATIO &&
    luOverD <= EFFECTIVE_LENGTH_THRESHOLDS.HIGH_RATIO
  ) {
    le =
      factor * unsupportedLength +
      BEAM_STABILITY_CONSTANTS.DEPTH_MULTIPLIER * d;
  } else {
    le = factor * unsupportedLength;
  }

  // Calculate slenderness ratio
  const RB = Math.sqrt((le * d) / (b * b));

  if (RB > BEAM_STABILITY_CONSTANTS.MAX_SLENDERNESS_RATIO) {
    throw new Error(
      `Slenderness ratio RB = ${RB.toFixed(
        2
      )} exceeds maximum allowed value of ${
        BEAM_STABILITY_CONSTANTS.MAX_SLENDERNESS_RATIO
      }`
    );
  }

  // Calculate critical buckling design value
  const FbE =
    (BEAM_STABILITY_CONSTANTS.CRITICAL_BUCKLING_COEFFICIENT * eMinAdj) /
    (RB * RB);

  // Calculate CL using NDS formula
  const FbEOverFb = FbE / refFbAdj;
  const term1 = 1 + FbEOverFb / BEAM_STABILITY_CONSTANTS.CL_DENOMINATOR_1;
  const term2 = Math.sqrt(
    Math.pow(term1, 2) - FbEOverFb / BEAM_STABILITY_CONSTANTS.CL_DENOMINATOR_2
  );

  const CL = term1 - term2;

  return Math.min(CL, BEAM_STABILITY_CONSTANTS.MAX_BEAM_STABILITY_FACTOR);
}
