/**
 * Unit tests for Sawn Lumber Beam Stability Factor
 * Based on NDS (National Design Specification) for Wood Construction - Section 3.3
 */

import {
  getBeamStabilityFactorCL,
  getEffectiveLengthFactor,
  getBeamStabilityFactorCLDetailed,
} from './stability';
import {
  LOADING_CONDITIONS,
  BEAM_STABILITY_CONSTANTS,
  EFFECTIVE_LENGTH_THRESHOLDS,
  EFFECTIVE_LENGTH_FACTORS,
} from '../../constants';

describe('Beam Stability Factor Calculations', () => {
  // Test data for typical lumber sizes
  const d = 9.25;      // 2x10 actual depth, inches
  const b = 1.5;       // 2x10 actual breadth, inches
  const eMinAdj = 580000; // Adjusted E_min for stability, psi
  const refFbAdj = 1000;  // Reference Fb adjusted by other factors, psi

  const testCases = {
    typical2x10: { d: 9.25, b: 1.5, eMinAdj: 580000, refFbAdj: 1000 },
    typical2x12: { d: 11.25, b: 1.5, eMinAdj: 580000, refFbAdj: 900 },
    heavyTimber: { d: 12.0, b: 6.0, eMinAdj: 1000000, refFbAdj: 1200 },
  };

  describe('getBeamStabilityFactorCL', () => {
    describe('Input Validation', () => {
      test('should return CL = 1.0 for zero unsupported length (fully braced)', () => {
        const result = getBeamStabilityFactorCL(0, d, b, eMinAdj, refFbAdj);
        expect(result).toBe(1.0);
      });

      test('should return CL = 1.0 for negative unsupported length (fully braced)', () => {
        const result = getBeamStabilityFactorCL(-10, d, b, eMinAdj, refFbAdj);
        expect(result).toBe(1.0);
      });

      test('should throw error for negative depth', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, -d, b, eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for negative breadth', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, d, -b, eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for negative eMinAdj', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, d, b, -eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for negative refFbAdj', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, d, b, eMinAdj, -refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for zero depth', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, 0, b, eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for zero breadth', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, d, 0, eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for zero eMinAdj', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, d, b, 0, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for zero refFbAdj', () => {
        expect(() => {
          getBeamStabilityFactorCL(120, d, b, eMinAdj, 0);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });
    });

    describe('Slenderness Ratio Limits', () => {
      test('should throw error when slenderness ratio exceeds 50', () => {
        // Create conditions for very high slenderness ratio
        const veryLongSpan = 2000; // Very long span

        expect(() => {
          getBeamStabilityFactorCL(veryLongSpan, d, b, eMinAdj, refFbAdj);
        }).toThrow(/Slenderness ratio RB = .+ exceeds maximum allowed value of 50/);
      });

      test('should work when slenderness ratio is exactly at limit', () => {
        // Calculate span that gives RB = 50
        // For uniformly distributed loading with lu/d > 14.3: le = 1.84 * lu
        // RB = sqrt((le * d) / b²) = sqrt((1.84 * lu * d) / b²) = 50
        // Solving: lu = (50² * b²) / (1.84 * d)
        const targetRB = 49.5; // Slightly under 50
        const lu = (targetRB * targetRB * b * b) / (2.06 * d);
        
        const result = getBeamStabilityFactorCL(lu, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });
    });

    describe('Effective Length Calculations - Boundary Cases', () => {
      test('should use LOW_RATIO factor when lu/d = 7 exactly', () => {
        const unsupportedLength = EFFECTIVE_LENGTH_THRESHOLDS.LOW_RATIO * d;
        const result = getBeamStabilityFactorCL(unsupportedLength, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should use INTERMEDIATE factor when lu/d is between 7 and 14.3', () => {
        const unsupportedLength = 10 * d; // lu/d = 10
        const result = getBeamStabilityFactorCL(unsupportedLength, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should use HIGH_RATIO factor when lu/d = 14.3 exactly', () => {
        const unsupportedLength = EFFECTIVE_LENGTH_THRESHOLDS.HIGH_RATIO * d;
        const result = getBeamStabilityFactorCL(unsupportedLength, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should use HIGH_RATIO factor when lu/d > 14.3', () => {
        const unsupportedLength = 20 * d; // lu/d = 20
        const result = getBeamStabilityFactorCL(unsupportedLength, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });
    });

    describe('CL Factor Constraints', () => {
      test('should never return CL greater than 1.0', () => {
        // Use very short span and high E to try to get CL > 1.0
        const shortSpan = 12; // 1 foot
        const highE = 2000000; // Very high E value
        
        const result = getBeamStabilityFactorCL(shortSpan, d, b, highE, refFbAdj);
        expect(result).toBeLessThanOrEqual(BEAM_STABILITY_CONSTANTS.MAX_BEAM_STABILITY_FACTOR);
      });

      test('should return reasonable values for typical spans', () => {
        // Test range of typical spans
        const spans = [96, 144, 192, 240]; // 8, 12, 16, 20 feet
        
        spans.forEach(span => {
          const result = getBeamStabilityFactorCL(span, d, b, eMinAdj, refFbAdj);
          expect(result).toBeGreaterThan(0);
          expect(result).toBeLessThanOrEqual(1.0);
          expect(result).toBeGreaterThan(0.1); // Should be reasonable, not near zero
        });
      });
    });

    describe('Different Member Sizes', () => {
      test('should work with 2x12 members', () => {
        const { d, b, eMinAdj, refFbAdj } = testCases.typical2x12;
        const result = getBeamStabilityFactorCL(144, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should work with heavy timber members', () => {
        const { d, b, eMinAdj, refFbAdj } = testCases.heavyTimber;
        const result = getBeamStabilityFactorCL(240, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });
    });
  });

  describe('getEffectiveLengthFactor', () => {
    describe('Uniformly Distributed Loading', () => {
      test('should return correct factor for lu/d <= 7', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED, 5);
        expect(result).toBe(2.06);
      });

      test('should return correct factor for lu/d = 7 exactly', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED, 7);
        expect(result).toBe(2.06);
      });

      test('should return correct factor for 7 < lu/d <= 14.3', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED, 10);
        expect(result).toBe(1.63);
      });

      test('should return correct factor for lu/d = 14.3 exactly', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED, 14.3);
        expect(result).toBe(1.63);
      });

      test('should return correct factor for lu/d > 14.3', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED, 20);
        expect(result).toBe(1.84);
      });
    });

    describe('Concentrated Center Loading', () => {
      test('should return correct factor for lu/d <= 7', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.CONCENTRATED_CENTER, 5);
        expect(result).toBe(1.80);
      });

      test('should return correct factor for lu/d > 7', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.CONCENTRATED_CENTER, 10);
        expect(result).toBe(1.37);
      });
    });

    describe('Concentrated End Loading', () => {
      test('should return correct factor for lu/d <= 7', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.CONCENTRATED_END, 5);
        expect(result).toBe(1.87);
      });

      test('should return correct factor for lu/d > 7', () => {
        const result = getEffectiveLengthFactor(LOADING_CONDITIONS.CONCENTRATED_END, 10);
        expect(result).toBe(1.44);
      });
    });

    describe('Equal End Moments Loading', () => {
      test('should return constant factor for all ratios', () => {
        const ratios = [3, 7, 10, 14.3, 20];
        ratios.forEach(ratio => {
          const result = getEffectiveLengthFactor(LOADING_CONDITIONS.EQUAL_END_MOMENTS, ratio);
          expect(result).toBe(1.84);
        });
      });
    });

    describe('Invalid Loading Conditions', () => {
      test('should throw error for unknown loading condition', () => {
        expect(() => {
          getEffectiveLengthFactor('invalid_condition' as any, 10);
        }).toThrow('Unknown loading condition: invalid_condition');
      });
    });
  });

  describe('getBeamStabilityFactorCLDetailed', () => {
    describe('Input Validation', () => {
      test('should return CL = 1.0 for zero unsupported length (fully braced)', () => {
        const result = getBeamStabilityFactorCLDetailed(0, d, b, eMinAdj, refFbAdj);
        expect(result).toBe(1.0);
      });

      test('should return CL = 1.0 for negative unsupported length (fully braced)', () => {
        const result = getBeamStabilityFactorCLDetailed(-5, d, b, eMinAdj, refFbAdj);
        expect(result).toBe(1.0);
      });

      test('should throw error for zero depth', () => {
        expect(() => {
          getBeamStabilityFactorCLDetailed(120, 0, b, eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });

      test('should throw error for negative parameters except unsupportedLength', () => {
        expect(() => {
          getBeamStabilityFactorCLDetailed(120, d, -b, eMinAdj, refFbAdj);
        }).toThrow('All input parameters except unsupportedLength must be positive values');
      });
    });

    describe('Fully Braced Beam Scenarios', () => {
      test('should return CL = 1.0 for fully braced beam regardless of other parameters', () => {
        // Test with various member sizes and material properties
        const testCases = [
          { d: 9.25, b: 1.5, eMinAdj: 580000, refFbAdj: 1000 },
          { d: 11.25, b: 1.5, eMinAdj: 580000, refFbAdj: 900 },
          { d: 12.0, b: 6.0, eMinAdj: 1000000, refFbAdj: 1200 },
        ];

        testCases.forEach(({ d, b, eMinAdj, refFbAdj }) => {
          const result = getBeamStabilityFactorCLDetailed(0, d, b, eMinAdj, refFbAdj);
          expect(result).toBe(1.0);
        });
      });

      test('should return CL = 1.0 for all loading conditions when fully braced', () => {
        const loadingConditions = [
          LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED,
          LOADING_CONDITIONS.CONCENTRATED_CENTER,
          LOADING_CONDITIONS.CONCENTRATED_END,
          LOADING_CONDITIONS.EQUAL_END_MOMENTS,
        ];

        loadingConditions.forEach(condition => {
          const result = getBeamStabilityFactorCLDetailed(0, d, b, eMinAdj, refFbAdj, condition);
          expect(result).toBe(1.0);
        });
      });
    });

    describe('Different Loading Conditions', () => {
      test('should work with uniformly distributed loading (default)', () => {
        const result = getBeamStabilityFactorCLDetailed(144, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should work with concentrated center loading', () => {
        const result = getBeamStabilityFactorCLDetailed(
          144, d, b, eMinAdj, refFbAdj, LOADING_CONDITIONS.CONCENTRATED_CENTER
        );
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should work with concentrated end loading', () => {
        const result = getBeamStabilityFactorCLDetailed(
          144, d, b, eMinAdj, refFbAdj, LOADING_CONDITIONS.CONCENTRATED_END
        );
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should work with equal end moments loading', () => {
        const result = getBeamStabilityFactorCLDetailed(
          144, d, b, eMinAdj, refFbAdj, LOADING_CONDITIONS.EQUAL_END_MOMENTS
        );
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });
    });

    describe('Effective Length Calculation with Depth Term', () => {
      test('should add depth term for uniformly distributed loading in intermediate range', () => {
        const unsupportedLength = 10 * d; // lu/d = 10, should be in intermediate range
        
        // This should use le = 1.63 * lu + 3 * d
        const result = getBeamStabilityFactorCLDetailed(
          unsupportedLength, d, b, eMinAdj, refFbAdj, LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED
        );
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should not add depth term for other loading conditions', () => {
        const unsupportedLength = 10 * d; // lu/d = 10
        
        const result = getBeamStabilityFactorCLDetailed(
          unsupportedLength, d, b, eMinAdj, refFbAdj, LOADING_CONDITIONS.CONCENTRATED_CENTER
        );
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });
    });

    describe('Comparison with Main Function', () => {
      test('should give same result as main function for uniformly distributed loading', () => {
        const unsupportedLength = 144;
        
        const mainResult = getBeamStabilityFactorCL(unsupportedLength, d, b, eMinAdj, refFbAdj);
        const detailedResult = getBeamStabilityFactorCLDetailed(
          unsupportedLength, d, b, eMinAdj, refFbAdj, LOADING_CONDITIONS.UNIFORMLY_DISTRIBUTED
        );
        
        expect(detailedResult).toBeCloseTo(mainResult, 6);
      });
    });
  });

  describe('Integration Tests', () => {
    describe('Real-world scenarios', () => {
      test('Fully braced beam should have CL = 1.0 regardless of span length', () => {
        const { d, b, eMinAdj, refFbAdj } = testCases.typical2x10;
        
        // Test various span lengths for fully braced beams
        const spans = [6 * 12, 12 * 12, 20 * 12, 30 * 12]; // 6, 12, 20, 30 feet
        
        spans.forEach(span => {
          const result = getBeamStabilityFactorCL(0, d, b, eMinAdj, refFbAdj);
          expect(result).toBe(1.0);
        });
      });

      test('2x10 @ 16ft span with uniform loading should give reasonable CL', () => {
        const { d, b, eMinAdj, refFbAdj } = testCases.typical2x10;
        const span = 16 * 12; // 16 feet in inches
        
        const result = getBeamStabilityFactorCL(span, d, b, eMinAdj, refFbAdj);
        
        // Should be a reasonable reduction factor for a long-span narrow beam
        // CL around 0.18-0.20 is realistic for a 16ft 2x10 due to lateral-torsional buckling
        expect(result).toBeGreaterThan(0.1);
        expect(result).toBeLessThan(0.5);
      });

      test('Heavy timber beam should have different CL than dimensional lumber', () => {
        const span = 20 * 12; // 20 feet
        
        const dimensionalResult = getBeamStabilityFactorCL(
          span, testCases.typical2x12.d, testCases.typical2x12.b, 
          testCases.typical2x12.eMinAdj, testCases.typical2x12.refFbAdj
        );
        
        const heavyTimberResult = getBeamStabilityFactorCL(
          span, testCases.heavyTimber.d, testCases.heavyTimber.b,
          testCases.heavyTimber.eMinAdj, testCases.heavyTimber.refFbAdj
        );
        
        // Heavy timber should have higher CL due to larger cross-section
        expect(heavyTimberResult).toBeGreaterThan(dimensionalResult);
        expect(heavyTimberResult).toBeLessThanOrEqual(1.0);
        expect(dimensionalResult).toBeLessThanOrEqual(1.0);
      });
    });

    describe('Performance edge cases', () => {
      test('should handle very small spans', () => {
        const { d, b, eMinAdj, refFbAdj } = testCases.typical2x10;
        const smallSpan = 6; // 6 inches
        
        const result = getBeamStabilityFactorCL(smallSpan, d, b, eMinAdj, refFbAdj);
        // Even for very short spans, CL may not reach 1.0 due to the NDS formula behavior
        expect(result).toBeGreaterThan(0.9); // Should be very high but may not be exactly 1.0
        expect(result).toBeLessThanOrEqual(1.0);
      });

      test('should handle maximum practical spans', () => {
        const { d, b, eMinAdj, refFbAdj } = testCases.typical2x10;
        // Calculate a span that approaches but doesn't exceed RB = 50
        const maxSpan = 300; // Safe span that stays well under RB=50 limit
        
        // Should not throw error if slenderness ratio is still reasonable
        const result = getBeamStabilityFactorCL(maxSpan, d, b, eMinAdj, refFbAdj);
        expect(result).toBeGreaterThan(0);
        expect(result).toBeLessThanOrEqual(1.0);
      });
    });
  });
}); 