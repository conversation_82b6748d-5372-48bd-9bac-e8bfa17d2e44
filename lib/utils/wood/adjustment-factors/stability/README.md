# Beam Stability Factor (CL)

## Overview

This module implements the beam stability factor (CL) calculations according to the National Design Specification (NDS) for Wood Construction, Section 3.3.3. The beam stability factor accounts for lateral-torsional buckling of bending members and is critical for determining the allowable bending stress.

## Key Concepts

### When Beam Stability Factor Applies

The beam stability factor applies to:
- **Bending members** subject to lateral-torsional buckling
- **Rectangular cross-sections** (solid sawn lumber, glulam)
- **Members without continuous lateral support**
- **Design values for bending (Fb)**

### Lateral-Torsional Buckling

**Mechanism:**
- **Compression flange** tends to buckle laterally
- **Cross-section rotates** (torsion) during buckling
- **Critical when** depth-to-width ratio is large
- **Prevented by** continuous lateral support

### Slenderness Ratio

The effective slenderness ratio determines the stability behavior:
```
RB = √(ℓu·d/b²)
```

Where:
- ℓu = effective length for lateral buckling
- d = depth of member
- b = width of member

## API Reference

### Main Functions

#### `getBeamStabilityFactorCL(input)`

Calculates the beam stability factor for given conditions.

**Parameters:**
- `input.effectiveLength`: Effective length for lateral buckling (in)
- `input.depth`: Member depth (in)
- `input.width`: Member width (in)
- `input.loadingCondition`: Loading condition type
- `input.modulusOfElasticity`: Modulus of elasticity (psi)
- `input.bendingDesignValue`: Bending design value (psi)

**Returns:**
- `factor`: Beam stability factor CL (≤ 1.0)
- `slendernessRatio`: Effective slenderness ratio RB
- `calculation`: Detailed calculation breakdown
- `applicable`: Whether factor applies

#### `getEffectiveLengthFactor(loadingCondition, slendernessRatio)`

Gets the effective length factor for different loading conditions.

**Parameters:**
- `loadingCondition`: Type of loading ('uniformly_distributed', 'concentrated_center', etc.)
- `slendernessRatio`: ℓu/d ratio

**Returns:**
- Effective length factor

#### `getBeamStabilityFactorCLDetailed(input)`

Provides detailed beam stability analysis with intermediate calculations.

### Usage Examples

```typescript
import { 
  getBeamStabilityFactorCL,
  getEffectiveLengthFactor
} from './stability';

// Example 1: Simply supported beam with uniform load
const uniformLoad = getBeamStabilityFactorCL({
  effectiveLength: 192,        // 16 ft unbraced length
  depth: 11.25,               // 2x12 actual depth
  width: 1.5,                 // 2x12 actual width
  loadingCondition: 'uniformly_distributed',
  modulusOfElasticity: 1600000, // psi
  bendingDesignValue: 1200     // psi
});

console.log(uniformLoad.factor);          // e.g., 0.85
console.log(uniformLoad.slendernessRatio); // e.g., 36.5

// Example 2: Beam with concentrated center load
const concentratedLoad = getBeamStabilityFactorCL({
  effectiveLength: 144,        // 12 ft unbraced length
  depth: 9.25,                // 2x10 actual depth
  width: 1.5,                 // 2x10 actual width
  loadingCondition: 'concentrated_center',
  modulusOfElasticity: 1600000,
  bendingDesignValue: 1200
});

console.log(concentratedLoad.factor); // e.g., 0.92
```

### Loading Conditions

```typescript
// Available loading conditions
export const LOADING_CONDITIONS = {
  UNIFORMLY_DISTRIBUTED: 'uniformly_distributed',
  CONCENTRATED_CENTER: 'concentrated_center',
  CONCENTRATED_END: 'concentrated_end',
  EQUAL_END_MOMENTS: 'equal_end_moments'
} as const;

// Effective length factors vary by loading condition
const effectiveLengthFactor = getEffectiveLengthFactor(
  'uniformly_distributed',
  slendernessRatio  // ℓu/d
);
```

### Detailed Analysis

```typescript
import { getBeamStabilityFactorCLDetailed } from './stability';

const detailedAnalysis = getBeamStabilityFactorCLDetailed({
  effectiveLength: 240,  // 20 ft
  depth: 13.5,          // Glulam depth
  width: 5.125,         // Glulam width
  loadingCondition: 'uniformly_distributed',
  modulusOfElasticity: 1800000,
  bendingDesignValue: 2400
});

console.log(detailedAnalysis.steps); // Step-by-step calculation
console.log(detailedAnalysis.criticalStress); // FbE value
console.log(detailedAnalysis.category); // 'long', 'intermediate', or 'short'
```

## Design Considerations

### Lateral Support Methods

**Continuous Lateral Support (CL = 1.0):**
- **Decking** or **sheathing** attached to compression flange
- **Bridging** at close intervals
- **Composite action** with concrete slab

**Intermittent Lateral Support:**
- **Joists** or **purlins** at regular intervals
- **Bracing** systems
- **Careful calculation** of unbraced length

**No Lateral Support:**
- **Full beam stability** analysis required
- **May govern design** for deep, narrow members
- **Consider depth-to-width ratios**

### Common Applications

**Where CL Typically Governs:**
- **Roof beams** without decking
- **Bridge girders** before deck placement
- **Warehouse beams** with wide spacing
- **Deep glulam members**

**Design Strategies:**
- **Provide lateral support** at regular intervals
- **Use wider members** to improve stability
- **Consider built-up sections** for large spans
- **Verify unbraced length assumptions**

### Slenderness Ratio Guidelines

- **RB ≤ 10**: Usually no stability concern
- **10 < RB ≤ 50**: Beam stability factor applies
- **RB > 50**: Not permitted (NDS limit)

## Files

- `stability.ts` - Main implementation
- `stability.test.ts` - Comprehensive test suite
- `README.md` - This documentation

## References

- NDS 2018, Section 3.3.3 - Beam Stability Factor, CL
- NDS 2018, Table 3.3.3 - Effective Length Factors
- NDS 2018, Section 3.3.6 - Lateral-Torsional Buckling
- Timber Construction Manual (TCM) - Beam stability examples 