# LRFD Factors

## Overview

This module implements the Load and Resistance Factor Design (LRFD) factors according to the National Design Specification (NDS) for Wood Construction, Appendix N. These factors convert ASD design values to LRFD design values and include format conversion factors, resistance factors, and time effect factors.

## Key Concepts

### LRFD Design Philosophy

LRFD uses factored loads and factored resistances:
- **Factored Load Effects**: Qu = γ × Q
- **Factored Resistance**: Rn = φ × Fn
- **Design Requirement**: Qu ≤ Rn

### LRFD Factors

1. **Format Conversion Factor (KF)**:
   - Converts ASD design values to LRFD nominal values
   - Accounts for statistical differences between methods

2. **Resistance Factor (φ)**:
   - Accounts for uncertainties in material properties
   - Applied to nominal resistance values

3. **Time Effect Factor (λ)**:
   - Accounts for duration of load effects
   - Replaces ASD load duration factor

## API Reference

### Main Functions

#### `getLrfdFormatConversionFactorKF(designValueType)`

Gets the format conversion factor for a design value type.

**Parameters:**
- `designValueType`: Type ('FB', 'FT', 'FV', 'FC', 'FC_PERPENDICULAR', 'E_MIN', 'ALL_CONNECTIONS')

**Returns:**
- Format conversion factor (KF)

#### `getLrfdResistanceFactorPhi(designValueType)`

Gets the resistance factor for a design value type.

**Parameters:**
- `designValueType`: Type ('FB', 'FT', 'FV', 'FC', 'FC_PERPENDICULAR', 'E_MIN', 'ALL_CONNECTIONS')

**Returns:**
- Resistance factor (φ)

#### `getLrfdTimeEffectFactorLambda(loadCombination, liveLoadCategory?)`

Gets the time effect factor for a load combination.

**Parameters:**
- `loadCombination`: LRFD load combination type
- `liveLoadCategory`: Live load category ('storage', 'occupancy', 'impact')

**Returns:**
- Time effect factor (λ)

#### `calculateAdjustedLrfdDesignValue(input)`

Calculates adjusted LRFD design value with all factors.

**Parameters:**
- `input.asdDesignValue`: ASD design value
- `input.designValueType`: Design value type
- `input.loadCombination`: Load combination
- `input.liveLoadCategory`: Live load category (if applicable)
- `input.otherAdjustmentFactors`: Other adjustment factors (CM, Ct, etc.)

**Returns:**
- Adjusted LRFD design value

### Usage Examples

```typescript
import { 
  getLrfdFormatConversionFactorKF,
  getLrfdResistanceFactorPhi,
  getLrfdTimeEffectFactorLambda,
  calculateAdjustedLrfdDesignValue
} from './lrfd';

// Example 1: Individual factors
const kf = getLrfdFormatConversionFactorKF('FB');    // 2.54
const phi = getLrfdResistanceFactorPhi('FB');        // 0.85
const lambda = getLrfdTimeEffectFactorLambda('dead_live_roof_snow_rain', 'occupancy'); // 0.8

// Example 2: Complete LRFD design value calculation
const result = calculateAdjustedLrfdDesignValue({
  asdDesignValue: 1200,  // psi (ASD Fb)
  designValueType: 'FB',
  loadCombination: 'dead_live_roof_snow_rain',
  liveLoadCategory: 'occupancy',
  otherAdjustmentFactors: {
    CM: 1.0,    // Wet service factor
    Ct: 1.0,    // Temperature factor
    CF: 1.0,    // Size factor
    Cfu: 1.0,   // Flat use factor
    Ci: 1.0,    // Incising factor
    Cr: 1.15    // Repetitive member factor
  }
});

console.log(result.adjustedDesignValue); // LRFD design value
console.log(result.calculation); // Detailed calculation breakdown
```

### LRFD Factor Tables

```typescript
// Format conversion factors (NDS Table N1)
export const LRFD_FORMAT_CONVERSION_FACTORS = {
  FB: 2.54,               // Bending
  FT: 2.70,               // Tension parallel
  FV: 2.88,               // Shear
  FC: 2.40,               // Compression parallel
  FC_PERPENDICULAR: 1.67, // Compression perpendicular
  E_MIN: 1.76,            // Minimum modulus of elasticity
  ALL_CONNECTIONS: 3.32   // All connections
} as const;

// Resistance factors (NDS Table N2)
export const LRFD_RESISTANCE_FACTORS = {
  FB: 0.85,               // Bending
  FT: 0.80,               // Tension parallel
  FV: 0.75,               // Shear
  FC: 0.90,               // Compression parallel
  FC_PERPENDICULAR: 0.90, // Compression perpendicular
  E_MIN: 0.85,            // Minimum modulus of elasticity
  ALL_CONNECTIONS: 0.65   // All connections
} as const;
```

### Load Combinations and Time Effect Factors

```typescript
// LRFD load combinations with time effect factors
export const LRFD_TIME_EFFECT_FACTORS = {
  DEAD_ONLY: 0.6,                    // 1.4D
  DEAD_LIVE_ROOF_SNOW_RAIN: {        // 1.2D + 1.6L + 0.5(Lr or S or R)
    L_FROM_STORAGE: 0.7,
    L_FROM_OCCUPANCY: 0.8,
    L_FROM_IMPACT: 1.25
  },
  DEAD_WIND_LIVE_ROOF_SNOW_RAIN: 1.0, // 1.2D + 1.0W + L + 0.5(Lr or S or R)
  UPLIFT_WIND: 1.0                   // 0.9D + 1.0W
} as const;
```

## Design Considerations

### When to Use LRFD

**Advantages:**
- **Consistent reliability** across different load types
- **Better handling** of load combinations
- **More rational approach** to uncertainty
- **Compatibility** with modern codes

**Applications:**
- **New construction** projects
- **Complex load combinations**
- **High-importance structures**
- **Engineered wood systems**

### LRFD vs. ASD Comparison

| Aspect | ASD | LRFD |
|--------|-----|------|
| Load Factors | 1.0 (unfactored) | 1.2-1.6 (factored) |
| Resistance Factors | 1.0 | 0.65-0.90 |
| Load Duration | CD factor | λ time effect factor |
| Safety Philosophy | Working stress | Ultimate limit state |

## Files

- `lrfd.ts` - Main implementation
- `lrfd.test.ts` - Comprehensive test suite
- `README.md` - This documentation

## References

- NDS 2018, Appendix N - Load and Resistance Factor Design (LRFD)
- NDS Table N1 - Format Conversion Factors, KF
- NDS Table N2 - Resistance Factors, φ
- NDS Table N3 - Time Effect Factors, λ
- ASCE 7 - LRFD load combinations 