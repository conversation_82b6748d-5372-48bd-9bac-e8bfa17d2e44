/**
 * LRFD Adjustment Factors for Sawn Lumber Design Values
 * Based on NDS (National Design Specification) for Wood Construction - Appendix N
 *
 * LRFD (Load and Resistance Factor Design) uses three main adjustment factors:
 * 1. Format Conversion Factor (KF) - converts ASD to LRFD format
 * 2. Resistance Factor (φ) - accounts for uncertainty in resistance
 * 3. Time Effect Factor (λ) - accounts for load duration effects
 */

import {
  LRFD_FORMAT_CONVERSION_FACTORS,
  LRFD_RESISTANCE_FACTORS,
  LRFD_TIME_EFFECT_FACTORS,
  LRFD_LOAD_COMBINATIONS,
  LRFD_LIVE_LOAD_CATEGORIES,
  LRFDLoadCombination,
  LRFDLiveLoadCategory,
  DESIGN_VALUE_TYPES,
  DesignValueType,
} from "../../constants";

/**
 * Gets the format conversion factor (KF) for LRFD design method.
 * Per NDS Appendix N.3.1: Reference design values shall be multiplied by
 * the format conversion factor KF as specified in Table N1.
 *
 * @param designValueType - The type of design value or 'connection' for connections
 * @returns The format conversion factor KF
 * @throws Error if design value type is not supported for LRFD
 */
export function getLrfdFormatConversionFactorKF(
  designValueType: DesignValueType | "connection"
): number {
  switch (designValueType) {
    case DESIGN_VALUE_TYPES.BENDING:
      return LRFD_FORMAT_CONVERSION_FACTORS.FB;
    case DESIGN_VALUE_TYPES.TENSION_PARALLEL:
      return LRFD_FORMAT_CONVERSION_FACTORS.FT;
    case DESIGN_VALUE_TYPES.SHEAR:
      return LRFD_FORMAT_CONVERSION_FACTORS.FV;
    case DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL:
      return LRFD_FORMAT_CONVERSION_FACTORS.FC;
    case DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR:
      return LRFD_FORMAT_CONVERSION_FACTORS.FC_PERPENDICULAR;
    case DESIGN_VALUE_TYPES.E_MIN:
      return LRFD_FORMAT_CONVERSION_FACTORS.E_MIN;
    case "connection":
      return LRFD_FORMAT_CONVERSION_FACTORS.ALL_CONNECTIONS;
    case DESIGN_VALUE_TYPES.E:
      throw new Error(
        "Modulus of elasticity (E) does not have a format conversion factor in LRFD method"
      );
    default:
      throw new Error(
        `Unsupported design value type for LRFD format conversion: ${designValueType}`
      );
  }
}

/**
 * Gets the resistance factor (φ) for LRFD design method.
 * Per NDS Appendix N.3.2: Reference design values shall be multiplied by
 * the resistance factor φ as specified in Table N2.
 *
 * @param designValueType - The type of design value or 'connection' for connections
 * @returns The resistance factor φ
 * @throws Error if design value type is not supported for LRFD
 */
export function getLrfdResistanceFactorPhi(
  designValueType: DesignValueType | "connection"
): number {
  switch (designValueType) {
    case DESIGN_VALUE_TYPES.BENDING:
      return LRFD_RESISTANCE_FACTORS.FB;
    case DESIGN_VALUE_TYPES.TENSION_PARALLEL:
      return LRFD_RESISTANCE_FACTORS.FT;
    case DESIGN_VALUE_TYPES.SHEAR:
      return LRFD_RESISTANCE_FACTORS.FV;
    case DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL:
      return LRFD_RESISTANCE_FACTORS.FC;
    case DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR:
      return LRFD_RESISTANCE_FACTORS.FC_PERPENDICULAR;
    case DESIGN_VALUE_TYPES.E_MIN:
      return LRFD_RESISTANCE_FACTORS.E_MIN;
    case "connection":
      return LRFD_RESISTANCE_FACTORS.ALL_CONNECTIONS;
    case DESIGN_VALUE_TYPES.E:
      throw new Error(
        "Modulus of elasticity (E) does not have a resistance factor in LRFD method"
      );
    default:
      throw new Error(
        `Unsupported design value type for LRFD resistance factor: ${designValueType}`
      );
  }
}

/**
 * Gets the time effect factor (λ) for LRFD design method.
 * Per NDS Appendix N.3.3: Reference design values shall be multiplied by
 * the time effect factor λ as specified in Table N3.
 *
 * @param loadCombination - The LRFD load combination category
 * @param liveLoadCategory - The live load category (required for DEAD_LIVE_ROOF_SNOW_RAIN combination)
 * @returns The time effect factor λ
 * @throws Error if load combination is invalid or live load category is missing when required
 */
export function getLrfdTimeEffectFactorLambda(
  loadCombination: LRFDLoadCombination,
  liveLoadCategory?: LRFDLiveLoadCategory
): number {
  switch (loadCombination) {
    case LRFD_LOAD_COMBINATIONS.DEAD_ONLY:
      return LRFD_TIME_EFFECT_FACTORS.DEAD_ONLY;

    case LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN:
      if (!liveLoadCategory) {
        throw new Error(
          "Live load category is required for DEAD_LIVE_ROOF_SNOW_RAIN load combination"
        );
      }
      switch (liveLoadCategory) {
        case LRFD_LIVE_LOAD_CATEGORIES.STORAGE:
          return LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN
            .L_FROM_STORAGE;
        case LRFD_LIVE_LOAD_CATEGORIES.OCCUPANCY:
          return LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN
            .L_FROM_OCCUPANCY;
        case LRFD_LIVE_LOAD_CATEGORIES.IMPACT:
          return LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN
            .L_FROM_IMPACT;
        default:
          throw new Error(`Invalid live load category: ${liveLoadCategory}`);
      }

    case LRFD_LOAD_COMBINATIONS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND:
      return LRFD_TIME_EFFECT_FACTORS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND;

    case LRFD_LOAD_COMBINATIONS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN:
      return LRFD_TIME_EFFECT_FACTORS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN;

    case LRFD_LOAD_COMBINATIONS.DEAD_EARTHQUAKE_LIVE_SNOW:
      return LRFD_TIME_EFFECT_FACTORS.DEAD_EARTHQUAKE_LIVE_SNOW;

    case LRFD_LOAD_COMBINATIONS.UPLIFT_WIND:
      return LRFD_TIME_EFFECT_FACTORS.UPLIFT_WIND;

    case LRFD_LOAD_COMBINATIONS.UPLIFT_EARTHQUAKE:
      return LRFD_TIME_EFFECT_FACTORS.UPLIFT_EARTHQUAKE;

    default:
      throw new Error(`Invalid LRFD load combination: ${loadCombination}`);
  }
}

/**
 * Calculates the adjusted LRFD design value by applying all three factors.
 * Per NDS Appendix N: Adjusted LRFD design value = Reference value × KF × φ × λ
 *
 * @param referenceDesignValue - The reference design value from tables
 * @param designValueType - The type of design value or 'connection' for connections
 * @param loadCombination - The LRFD load combination category
 * @param liveLoadCategory - The live load category (required for certain combinations)
 * @returns The adjusted LRFD design value
 * @throws Error if inputs are invalid
 */
export function calculateAdjustedLrfdDesignValue(
  referenceDesignValue: number,
  designValueType: DesignValueType | "connection",
  loadCombination: LRFDLoadCombination,
  liveLoadCategory?: LRFDLiveLoadCategory
): number {
  // Input validation
  if (referenceDesignValue <= 0) {
    throw new Error("Reference design value must be positive");
  }

  // Get format conversion factor
  const kf = getLrfdFormatConversionFactorKF(designValueType);

  // Get resistance factor
  const phi = getLrfdResistanceFactorPhi(designValueType);

  // Get time effect factor
  const lambda = getLrfdTimeEffectFactorLambda(
    loadCombination,
    liveLoadCategory
  );

  // Calculate adjusted design value
  return referenceDesignValue * kf * phi * lambda;
}

/**
 * Gets all LRFD factors for a given design value type and load combination.
 * Useful for analysis and documentation purposes.
 *
 * @param designValueType - The type of design value or 'connection' for connections
 * @param loadCombination - The LRFD load combination category
 * @param liveLoadCategory - The live load category (required for certain combinations)
 * @returns Object containing all factors and their product
 */
export function getLrfdFactorsAnalysis(
  designValueType: DesignValueType | "connection",
  loadCombination: LRFDLoadCombination,
  liveLoadCategory?: LRFDLiveLoadCategory
): {
  formatConversionFactor: number;
  resistanceFactor: number;
  timeEffectFactor: number;
  combinedFactor: number;
} {
  const kf = getLrfdFormatConversionFactorKF(designValueType);
  const phi = getLrfdResistanceFactorPhi(designValueType);
  const lambda = getLrfdTimeEffectFactorLambda(
    loadCombination,
    liveLoadCategory
  );

  return {
    formatConversionFactor: kf,
    resistanceFactor: phi,
    timeEffectFactor: lambda,
    combinedFactor: kf * phi * lambda,
  };
}

/**
 * Validates if a design value type is supported for LRFD method.
 *
 * @param designValueType - The type of design value
 * @returns True if the design value type is supported for LRFD
 */
export function isDesignValueSupportedForLrfd(
  designValueType: DesignValueType
): boolean {
  try {
    getLrfdFormatConversionFactorKF(designValueType);
    getLrfdResistanceFactorPhi(designValueType);
    return true;
  } catch {
    return false;
  }
}

/**
 * Gets available time effect factors for a specific load combination.
 *
 * @param loadCombination - The LRFD load combination category
 * @returns Array of possible time effect factors for the combination
 */
export function getAvailableTimeEffectFactors(
  loadCombination: LRFDLoadCombination
): Array<{ factor: number; description: string }> {
  switch (loadCombination) {
    case LRFD_LOAD_COMBINATIONS.DEAD_ONLY:
      return [
        { factor: LRFD_TIME_EFFECT_FACTORS.DEAD_ONLY, description: "1.4D" },
      ];

    case LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN:
      return [
        {
          factor:
            LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN.L_FROM_STORAGE,
          description: "1.2D + 1.6L + 0.5(Lr or S or R) - L from storage",
        },
        {
          factor:
            LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN.L_FROM_OCCUPANCY,
          description: "1.2D + 1.6L + 0.5(Lr or S or R) - L from occupancy",
        },
        {
          factor:
            LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN.L_FROM_IMPACT,
          description: "1.2D + 1.6L + 0.5(Lr or S or R) - L from impact",
        },
      ];

    case LRFD_LOAD_COMBINATIONS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND:
      return [
        {
          factor: LRFD_TIME_EFFECT_FACTORS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND,
          description: "1.2D + 1.6(Lr or S or R) + (L or 0.5W)",
        },
      ];

    case LRFD_LOAD_COMBINATIONS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN:
      return [
        {
          factor: LRFD_TIME_EFFECT_FACTORS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN,
          description: "1.2D + 1.0W + L + 0.5(Lr or S or R)",
        },
      ];

    case LRFD_LOAD_COMBINATIONS.DEAD_EARTHQUAKE_LIVE_SNOW:
      return [
        {
          factor: LRFD_TIME_EFFECT_FACTORS.DEAD_EARTHQUAKE_LIVE_SNOW,
          description: "1.2D + 1.0E + L + 0.2S",
        },
      ];

    case LRFD_LOAD_COMBINATIONS.UPLIFT_WIND:
      return [
        {
          factor: LRFD_TIME_EFFECT_FACTORS.UPLIFT_WIND,
          description: "0.9D + 1.0W",
        },
      ];

    case LRFD_LOAD_COMBINATIONS.UPLIFT_EARTHQUAKE:
      return [
        {
          factor: LRFD_TIME_EFFECT_FACTORS.UPLIFT_EARTHQUAKE,
          description: "0.9D + 1.0E",
        },
      ];

    default:
      return [];
  }
}
