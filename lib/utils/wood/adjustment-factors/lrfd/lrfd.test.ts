/**
 * Unit tests for Sawn Lumber LRFD Adjustment Factors
 * Based on NDS (National Design Specification) for Wood Construction - Appendix N
 */

import {
  getLrfdFormatConversionFactorKF,
  getLrfdResistanceFactorPhi,
  getLrfdTimeEffectFactorLambda,
  calculateAdjustedLrfdDesignValue,
  getLrfdFactorsAnalysis,
  isDesignValueSupportedForLrfd,
  getAvailableTimeEffectFactors,
} from "./lrfd";
import {
  LRFD_FORMAT_CONVERSION_FACTORS,
  LRFD_RESISTANCE_FACTORS,
  LRFD_TIME_EFFECT_FACTORS,
  LRFD_LOAD_COMBINATIONS,
  LRFD_LIVE_LOAD_CATEGORIES,
  DESIGN_VALUE_TYPES,
} from "../../constants";

describe("LRFD Adjustment Factor Calculations", () => {
  describe("getLrfdFormatConversionFactorKF", () => {
    describe("Valid Design Value Types - Members", () => {
      test("should return 2.54 for bending strength (Fb)", () => {
        const result = getLrfdFormatConversionFactorKF(
          DESIGN_VALUE_TYPES.BENDING
        );
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.FB);
        expect(result).toBe(2.54);
      });

      test("should return 2.70 for tension parallel to grain (Ft)", () => {
        const result = getLrfdFormatConversionFactorKF(
          DESIGN_VALUE_TYPES.TENSION_PARALLEL
        );
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.FT);
        expect(result).toBe(2.7);
      });

      test("should return 2.88 for shear strength (Fv)", () => {
        const result = getLrfdFormatConversionFactorKF(
          DESIGN_VALUE_TYPES.SHEAR
        );
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.FV);
        expect(result).toBe(2.88);
      });

      test("should return 2.40 for compression parallel to grain (Fc)", () => {
        const result = getLrfdFormatConversionFactorKF(
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL
        );
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.FC);
        expect(result).toBe(2.4);
      });

      test("should return 1.67 for compression perpendicular to grain (Fc⊥)", () => {
        const result = getLrfdFormatConversionFactorKF(
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR
        );
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.FC_PERPENDICULAR);
        expect(result).toBe(1.67);
      });

      test("should return 1.76 for minimum modulus of elasticity (Emin)", () => {
        const result = getLrfdFormatConversionFactorKF(
          DESIGN_VALUE_TYPES.E_MIN
        );
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.E_MIN);
        expect(result).toBe(1.76);
      });
    });

    describe("Valid Design Value Types - Connections", () => {
      test("should return 3.32 for all connections", () => {
        const result = getLrfdFormatConversionFactorKF("connection");
        expect(result).toBe(LRFD_FORMAT_CONVERSION_FACTORS.ALL_CONNECTIONS);
        expect(result).toBe(3.32);
      });
    });

    describe("Invalid Design Value Types", () => {
      test("should throw error for modulus of elasticity (E)", () => {
        expect(() => {
          getLrfdFormatConversionFactorKF(DESIGN_VALUE_TYPES.E);
        }).toThrow(
          "Modulus of elasticity (E) does not have a format conversion factor in LRFD method"
        );
      });

      test("should throw error for unsupported design value type", () => {
        expect(() => {
          getLrfdFormatConversionFactorKF("invalid_type" as any);
        }).toThrow(
          "Unsupported design value type for LRFD format conversion: invalid_type"
        );
      });
    });
  });

  describe("getLrfdResistanceFactorPhi", () => {
    describe("Valid Design Value Types - Members", () => {
      test("should return 0.85 for bending strength (Fb)", () => {
        const result = getLrfdResistanceFactorPhi(
          DESIGN_VALUE_TYPES.BENDING
        );
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.FB);
        expect(result).toBe(0.85);
      });

      test("should return 0.80 for tension parallel to grain (Ft)", () => {
        const result = getLrfdResistanceFactorPhi(
          DESIGN_VALUE_TYPES.TENSION_PARALLEL
        );
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.FT);
        expect(result).toBe(0.8);
      });

      test("should return 0.75 for shear strength (Fv)", () => {
        const result = getLrfdResistanceFactorPhi(DESIGN_VALUE_TYPES.SHEAR);
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.FV);
        expect(result).toBe(0.75);
      });

      test("should return 0.90 for compression parallel to grain (Fc)", () => {
        const result = getLrfdResistanceFactorPhi(
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL
        );
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.FC);
        expect(result).toBe(0.9);
      });

      test("should return 0.90 for compression perpendicular to grain (Fc⊥)", () => {
        const result = getLrfdResistanceFactorPhi(
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR
        );
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.FC_PERPENDICULAR);
        expect(result).toBe(0.9);
      });

      test("should return 0.85 for minimum modulus of elasticity (Emin)", () => {
        const result = getLrfdResistanceFactorPhi(DESIGN_VALUE_TYPES.E_MIN);
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.E_MIN);
        expect(result).toBe(0.85);
      });
    });

    describe("Valid Design Value Types - Connections", () => {
      test("should return 0.65 for all connections", () => {
        const result = getLrfdResistanceFactorPhi("connection");
        expect(result).toBe(LRFD_RESISTANCE_FACTORS.ALL_CONNECTIONS);
        expect(result).toBe(0.65);
      });
    });

    describe("Invalid Design Value Types", () => {
      test("should throw error for modulus of elasticity (E)", () => {
        expect(() => {
          getLrfdResistanceFactorPhi(DESIGN_VALUE_TYPES.E);
        }).toThrow(
          "Modulus of elasticity (E) does not have a resistance factor in LRFD method"
        );
      });

      test("should throw error for unsupported design value type", () => {
        expect(() => {
          getLrfdResistanceFactorPhi("invalid_type" as any);
        }).toThrow(
          "Unsupported design value type for LRFD resistance factor: invalid_type"
        );
      });
    });
  });

  describe("getLrfdTimeEffectFactorLambda", () => {
    describe("Load Combinations Without Live Load Category", () => {
      test("should return 0.6 for dead only load combination (1.4D)", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_ONLY
        );
        expect(result).toBe(LRFD_TIME_EFFECT_FACTORS.DEAD_ONLY);
        expect(result).toBe(0.6);
      });

      test("should return 0.8 for dead + roof/snow/rain + live/wind combination", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND
        );
        expect(result).toBe(
          LRFD_TIME_EFFECT_FACTORS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND
        );
        expect(result).toBe(0.8);
      });

      test("should return 1.0 for dead + wind + live + roof/snow/rain combination", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN
        );
        expect(result).toBe(
          LRFD_TIME_EFFECT_FACTORS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN
        );
        expect(result).toBe(1.0);
      });

      test("should return 1.0 for dead + earthquake + live + snow combination", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_EARTHQUAKE_LIVE_SNOW
        );
        expect(result).toBe(LRFD_TIME_EFFECT_FACTORS.DEAD_EARTHQUAKE_LIVE_SNOW);
        expect(result).toBe(1.0);
      });

      test("should return 1.0 for uplift wind combination (0.9D + 1.0W)", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.UPLIFT_WIND
        );
        expect(result).toBe(LRFD_TIME_EFFECT_FACTORS.UPLIFT_WIND);
        expect(result).toBe(1.0);
      });

      test("should return 1.0 for uplift earthquake combination (0.9D + 1.0E)", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.UPLIFT_EARTHQUAKE
        );
        expect(result).toBe(LRFD_TIME_EFFECT_FACTORS.UPLIFT_EARTHQUAKE);
        expect(result).toBe(1.0);
      });
    });

    describe("Load Combinations With Live Load Category", () => {
      test("should return 0.7 for dead + live + roof/snow/rain with storage live load", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.STORAGE
        );
        expect(result).toBe(
          LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN.L_FROM_STORAGE
        );
        expect(result).toBe(0.7);
      });

      test("should return 0.8 for dead + live + roof/snow/rain with occupancy live load", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.OCCUPANCY
        );
        expect(result).toBe(
          LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN.L_FROM_OCCUPANCY
        );
        expect(result).toBe(0.8);
      });

      test("should return 1.25 for dead + live + roof/snow/rain with impact live load", () => {
        const result = getLrfdTimeEffectFactorLambda(
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.IMPACT
        );
        expect(result).toBe(
          LRFD_TIME_EFFECT_FACTORS.DEAD_LIVE_ROOF_SNOW_RAIN.L_FROM_IMPACT
        );
        expect(result).toBe(1.25);
      });
    });

    describe("Error Conditions", () => {
      test("should throw error when live load category is missing for dead + live + roof/snow/rain", () => {
        expect(() => {
          getLrfdTimeEffectFactorLambda(
            LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN
          );
        }).toThrow(
          "Live load category is required for DEAD_LIVE_ROOF_SNOW_RAIN load combination"
        );
      });

      test("should throw error for invalid live load category", () => {
        expect(() => {
          getLrfdTimeEffectFactorLambda(
            LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
            "invalid_category" as any
          );
        }).toThrow("Invalid live load category: invalid_category");
      });

      test("should throw error for invalid load combination", () => {
        expect(() => {
          getLrfdTimeEffectFactorLambda("invalid_combination" as any);
        }).toThrow("Invalid LRFD load combination: invalid_combination");
      });
    });
  });

  describe("calculateAdjustedLrfdDesignValue", () => {
    describe("Valid Calculations", () => {
      test("should calculate adjusted LRFD design value for bending with dead only loads", () => {
        const referenceValue = 1200; // psi
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          DESIGN_VALUE_TYPES.BENDING,
          LRFD_LOAD_COMBINATIONS.DEAD_ONLY
        );

        // Expected: 1200 × 2.54 (KF) × 0.85 (φ) × 0.6 (λ) = 1554.48
        const expected = referenceValue * 2.54 * 0.85 * 0.6;
        expect(result).toBeCloseTo(expected, 2);
        expect(result).toBeCloseTo(1554.48, 2);
      });

      test("should calculate adjusted LRFD design value for tension with occupancy live loads", () => {
        const referenceValue = 800; // psi
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          DESIGN_VALUE_TYPES.TENSION_PARALLEL,
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.OCCUPANCY
        );

        // Expected: 800 × 2.70 (KF) × 0.80 (φ) × 0.8 (λ) = 1382.4
        const expected = referenceValue * 2.7 * 0.8 * 0.8;
        expect(result).toBeCloseTo(expected, 2);
        expect(result).toBeCloseTo(1382.4, 2);
      });

      test("should calculate adjusted LRFD design value for connections with wind loads", () => {
        const referenceValue = 500; // lbs
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          "connection",
          LRFD_LOAD_COMBINATIONS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN
        );

        // Expected: 500 × 3.32 (KF) × 0.65 (φ) × 1.0 (λ) = 1079
        const expected = referenceValue * 3.32 * 0.65 * 1.0;
        expect(result).toBeCloseTo(expected, 2);
        expect(result).toBeCloseTo(1079, 2);
      });

      test("should calculate adjusted LRFD design value for shear with impact loads", () => {
        const referenceValue = 180; // psi
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          DESIGN_VALUE_TYPES.SHEAR,
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.IMPACT
        );

        // Expected: 180 × 2.88 (KF) × 0.75 (φ) × 1.25 (λ) = 486
        const expected = referenceValue * 2.88 * 0.75 * 1.25;
        expect(result).toBeCloseTo(expected, 2);
        expect(result).toBeCloseTo(486, 2);
      });
    });

    describe("Error Conditions", () => {
      test("should throw error for negative reference design value", () => {
        expect(() => {
          calculateAdjustedLrfdDesignValue(
            -100,
            DESIGN_VALUE_TYPES.BENDING,
            LRFD_LOAD_COMBINATIONS.DEAD_ONLY
          );
        }).toThrow("Reference design value must be positive");
      });

      test("should throw error for zero reference design value", () => {
        expect(() => {
          calculateAdjustedLrfdDesignValue(
            0,
            DESIGN_VALUE_TYPES.BENDING,
            LRFD_LOAD_COMBINATIONS.DEAD_ONLY
          );
        }).toThrow("Reference design value must be positive");
      });

      test("should throw error for unsupported design value type", () => {
        expect(() => {
          calculateAdjustedLrfdDesignValue(
            1000,
            DESIGN_VALUE_TYPES.E,
            LRFD_LOAD_COMBINATIONS.DEAD_ONLY
          );
        }).toThrow(
          "Modulus of elasticity (E) does not have a format conversion factor in LRFD method"
        );
      });
    });
  });

  describe("getLrfdFactorsAnalysis", () => {
    describe("Factor Analysis", () => {
      test("should return all factors for bending with dead only loads", () => {
        const result = getLrfdFactorsAnalysis(
          DESIGN_VALUE_TYPES.BENDING,
          LRFD_LOAD_COMBINATIONS.DEAD_ONLY
        );

        expect(result).toEqual({
          formatConversionFactor: 2.54,
          resistanceFactor: 0.85,
          timeEffectFactor: 0.6,
          combinedFactor: 2.54 * 0.85 * 0.6,
        });
        expect(result.combinedFactor).toBeCloseTo(1.2954, 4);
      });

      test("should return all factors for connections with earthquake loads", () => {
        const result = getLrfdFactorsAnalysis(
          "connection",
          LRFD_LOAD_COMBINATIONS.DEAD_EARTHQUAKE_LIVE_SNOW
        );

        expect(result).toEqual({
          formatConversionFactor: 3.32,
          resistanceFactor: 0.65,
          timeEffectFactor: 1.0,
          combinedFactor: 3.32 * 0.65 * 1.0,
        });
        expect(result.combinedFactor).toBeCloseTo(2.158, 3);
      });

      test("should return all factors with live load category specified", () => {
        const result = getLrfdFactorsAnalysis(
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.STORAGE
        );

        expect(result).toEqual({
          formatConversionFactor: 2.4,
          resistanceFactor: 0.9,
          timeEffectFactor: 0.7,
          combinedFactor: 2.4 * 0.9 * 0.7,
        });
        expect(result.combinedFactor).toBeCloseTo(1.512, 3);
      });
    });
  });

  describe("isDesignValueSupportedForLrfd", () => {
    describe("Supported Design Values", () => {
      test("should return true for bending (Fb)", () => {
        const result = isDesignValueSupportedForLrfd(
          DESIGN_VALUE_TYPES.BENDING
        );
        expect(result).toBe(true);
      });

      test("should return true for tension parallel (Ft)", () => {
        const result = isDesignValueSupportedForLrfd(
          DESIGN_VALUE_TYPES.TENSION_PARALLEL
        );
        expect(result).toBe(true);
      });

      test("should return true for shear (Fv)", () => {
        const result = isDesignValueSupportedForLrfd(
          DESIGN_VALUE_TYPES.SHEAR
        );
        expect(result).toBe(true);
      });

      test("should return true for compression parallel (Fc)", () => {
        const result = isDesignValueSupportedForLrfd(
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL
        );
        expect(result).toBe(true);
      });

      test("should return true for compression perpendicular (Fc⊥)", () => {
        const result = isDesignValueSupportedForLrfd(
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR
        );
        expect(result).toBe(true);
      });

      test("should return true for minimum modulus of elasticity (Emin)", () => {
        const result = isDesignValueSupportedForLrfd(
          DESIGN_VALUE_TYPES.E_MIN
        );
        expect(result).toBe(true);
      });
    });

    describe("Unsupported Design Values", () => {
      test("should return false for modulus of elasticity (E)", () => {
        const result = isDesignValueSupportedForLrfd(DESIGN_VALUE_TYPES.E);
        expect(result).toBe(false);
      });
    });
  });

  describe("getAvailableTimeEffectFactors", () => {
    describe("Single Factor Load Combinations", () => {
      test("should return single factor for dead only loads", () => {
        const result = getAvailableTimeEffectFactors(
          LRFD_LOAD_COMBINATIONS.DEAD_ONLY
        );
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          factor: 0.6,
          description: "1.4D",
        });
      });

      test("should return single factor for dead + roof/snow/rain + live/wind", () => {
        const result = getAvailableTimeEffectFactors(
          LRFD_LOAD_COMBINATIONS.DEAD_ROOF_SNOW_RAIN_LIVE_WIND
        );
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          factor: 0.8,
          description: "1.2D + 1.6(Lr or S or R) + (L or 0.5W)",
        });
      });

      test("should return single factor for uplift wind loads", () => {
        const result = getAvailableTimeEffectFactors(
          LRFD_LOAD_COMBINATIONS.UPLIFT_WIND
        );
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          factor: 1.0,
          description: "0.9D + 1.0W",
        });
      });
    });

    describe("Multiple Factor Load Combinations", () => {
      test("should return multiple factors for dead + live + roof/snow/rain", () => {
        const result = getAvailableTimeEffectFactors(
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN
        );
        expect(result).toHaveLength(3);

        expect(result[0]).toEqual({
          factor: 0.7,
          description: "1.2D + 1.6L + 0.5(Lr or S or R) - L from storage",
        });

        expect(result[1]).toEqual({
          factor: 0.8,
          description: "1.2D + 1.6L + 0.5(Lr or S or R) - L from occupancy",
        });

        expect(result[2]).toEqual({
          factor: 1.25,
          description: "1.2D + 1.6L + 0.5(Lr or S or R) - L from impact",
        });
      });
    });

    describe("Invalid Load Combinations", () => {
      test("should return empty array for invalid load combination", () => {
        const result = getAvailableTimeEffectFactors(
          "invalid_combination" as any
        );
        expect(result).toHaveLength(0);
      });
    });
  });

  describe("Integration Tests", () => {
    describe("Real-world Calculation Examples", () => {
      test("should calculate correct LRFD design value for Douglas Fir beam in bending", () => {
        // Typical Douglas Fir #2 bending value: 900 psi
        const referenceValue = 900;
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          DESIGN_VALUE_TYPES.BENDING,
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.OCCUPANCY
        );

        // Expected: 900 × 2.54 × 0.85 × 0.8 = 1554.48
        expect(result).toBeCloseTo(1554.48, 2);
      });

      test("should calculate correct LRFD design value for connection under wind loads", () => {
        // Typical connection capacity: 1000 lbs
        const referenceValue = 1000;
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          "connection",
          LRFD_LOAD_COMBINATIONS.DEAD_WIND_LIVE_ROOF_SNOW_RAIN
        );

        // Expected: 1000 × 3.32 × 0.65 × 1.0 = 2158
        expect(result).toBeCloseTo(2158, 2);
      });

      test("should show factors analysis for typical design scenario", () => {
        const analysis = getLrfdFactorsAnalysis(
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
          LRFD_LOAD_COMBINATIONS.DEAD_LIVE_ROOF_SNOW_RAIN,
          LRFD_LIVE_LOAD_CATEGORIES.IMPACT
        );

        expect(analysis.formatConversionFactor).toBe(1.67);
        expect(analysis.resistanceFactor).toBe(0.9);
        expect(analysis.timeEffectFactor).toBe(1.25);
        expect(analysis.combinedFactor).toBeCloseTo(1.87875, 4);
      });
    });

    describe("Boundary Conditions", () => {
      test("should handle very small reference values", () => {
        const result = calculateAdjustedLrfdDesignValue(
          0.001,
          DESIGN_VALUE_TYPES.BENDING,
          LRFD_LOAD_COMBINATIONS.DEAD_ONLY
        );

        expect(result).toBeGreaterThan(0);
        expect(result).toBeCloseTo(0.001 * 2.54 * 0.85 * 0.6, 6);
      });

      test("should handle large reference values", () => {
        const referenceValue = 1000000;
        const result = calculateAdjustedLrfdDesignValue(
          referenceValue,
          DESIGN_VALUE_TYPES.TENSION_PARALLEL,
          LRFD_LOAD_COMBINATIONS.UPLIFT_EARTHQUAKE
        );

        const expected = referenceValue * 2.7 * 0.8 * 1.0;
        expect(result).toBeCloseTo(expected, 0);
      });
    });
  });
});
