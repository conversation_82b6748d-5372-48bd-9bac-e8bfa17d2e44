/**
 * Incising Factor for Sawn Lumber Design Values
 * Based on NDS (National Design Specification) for Wood Construction - Section 4.3.8
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  INCISING_FACTORS,
  INCISING_SPECIFICATIONS,
} from '../../constants';

/**
 * Calculates the incising factor, Ci, for dimension lumber that has been incised
 * parallel to grain for preservative treatment.
 * 
 * Per NDS 4.3.8: Reference design values for dimension lumber shall be multiplied 
 * by the incising factor when lumber is incised parallel to grain with:
 * - Maximum depth of 0.4"
 * - Maximum length of 3/8" 
 * - Density of incisions up to 1100/ft²
 * 
 * @param designValueType - The type of design value (E, Emin, Fb, Ft, Fc, Fv, Fc_perp)
 * @returns The incising factor Ci from NDS Table 4.3.8
 * @throws Error if design value type is invalid
 */
export function getIncisingFactorCi(designValueType: DesignValueType): number {
  switch (designValueType) {
    case DESIGN_VALUE_TYPES.E:
    case DESIGN_VALUE_TYPES.E_MIN:
      return INCISING_FACTORS.MODULUS_OF_ELASTICITY;
      
    case DESIGN_VALUE_TYPES.BENDING:
    case DESIGN_VALUE_TYPES.TENSION_PARALLEL:
    case DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL:
    case DESIGN_VALUE_TYPES.SHEAR:
      return INCISING_FACTORS.STRENGTH_VALUES;
      
    case DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR:
      return INCISING_FACTORS.COMPRESSION_PERPENDICULAR;
      
    default:
      throw new Error(`Invalid design value type: ${designValueType}`);
  }
}

/**
 * Checks if incising specifications are within NDS limits
 * 
 * @param depth - Maximum depth of incisions, in inches
 * @param length - Maximum length of incisions, in inches  
 * @param density - Density of incisions, in incisions per ft²
 * @returns Object indicating if specifications are valid and any violations
 */
export function validateIncisingSpecifications(
  depth: number,
  length: number,
  density: number
): {
  isValid: boolean;
  violations: string[];
} {
  const violations: string[] = [];
  
  if (depth > INCISING_SPECIFICATIONS.MAX_DEPTH) {
    violations.push(`Incision depth ${depth}" exceeds maximum allowed ${INCISING_SPECIFICATIONS.MAX_DEPTH}"`);
  }
  
  if (length > INCISING_SPECIFICATIONS.MAX_LENGTH) {
    violations.push(`Incision length ${length}" exceeds maximum allowed ${INCISING_SPECIFICATIONS.MAX_LENGTH}"`);
  }
  
  if (density > INCISING_SPECIFICATIONS.MAX_DENSITY) {
    violations.push(`Incision density ${density}/ft² exceeds maximum allowed ${INCISING_SPECIFICATIONS.MAX_DENSITY}/ft²`);
  }
  
  return {
    isValid: violations.length === 0,
    violations,
  };
}

/**
 * Applies incising factor to a design value if incising specifications are valid
 * 
 * @param designValue - The original design value
 * @param designValueType - The type of design value
 * @param depth - Maximum depth of incisions, in inches (optional, defaults to within limits)
 * @param length - Maximum length of incisions, in inches (optional, defaults to within limits)
 * @param density - Density of incisions, in incisions per ft² (optional, defaults to within limits)
 * @returns The adjusted design value with incising factor applied
 * @throws Error if incising specifications exceed NDS limits
 */
export function applyIncisingFactor(
  designValue: number,
  designValueType: DesignValueType,
  depth: number = INCISING_SPECIFICATIONS.MAX_DEPTH,
  length: number = INCISING_SPECIFICATIONS.MAX_LENGTH, 
  density: number = INCISING_SPECIFICATIONS.MAX_DENSITY
): number {
  // Input validation
  if (designValue <= 0) {
    throw new Error('Design value must be positive');
  }
  
  // Validate incising specifications
  const validation = validateIncisingSpecifications(depth, length, density);
  if (!validation.isValid) {
    throw new Error(`Invalid incising specifications: ${validation.violations.join(', ')}`);
  }
  
  // Get and apply incising factor
  const Ci = getIncisingFactorCi(designValueType);
  return designValue * Ci;
}

/**
 * Gets all incising factors for multiple design value types
 * 
 * @param designValueTypes - Array of design value types to get factors for
 * @returns Object mapping design value types to their incising factors
 */
export function getMultipleIncisingFactors(
  designValueTypes: DesignValueType[]
): Record<string, number> {
  const factors: Record<string, number> = {};
  
  designValueTypes.forEach(type => {
    factors[type] = getIncisingFactorCi(type);
  });
  
  return factors;
}

/**
 * Convenience function to get incising factor by string name (case-insensitive)
 * 
 * @param designValueName - String name of design value (e.g., "Fb", "E", "Fc_perp")
 * @returns The incising factor Ci
 * @throws Error if design value name is not recognized
 */
export function getIncisingFactorByName(designValueName: string): number {
  const normalizedName = designValueName.toLowerCase();
  
  // Map common variations to standard design value types
  const nameMapping: Record<string, DesignValueType> = {
    'e': DESIGN_VALUE_TYPES.E,
    'emin': DESIGN_VALUE_TYPES.E_MIN,
    'e_min': DESIGN_VALUE_TYPES.E_MIN,
    'fb': DESIGN_VALUE_TYPES.BENDING,
    'bending': DESIGN_VALUE_TYPES.BENDING,
    'ft': DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    'tension': DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    'tension_parallel': DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    'fc': DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
    'compression': DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
    'compression_parallel': DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
    'fv': DESIGN_VALUE_TYPES.SHEAR,
    'shear': DESIGN_VALUE_TYPES.SHEAR,
    'fc_perp': DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
    'fc⊥': DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
    'compression_perpendicular': DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
  };
  
  const designValueType = nameMapping[normalizedName];
  if (!designValueType) {
    throw new Error(`Unrecognized design value name: ${designValueName}`);
  }
  
  return getIncisingFactorCi(designValueType);
} 