# Incising Factor (Ci)

## Overview

This module implements the incising factor (Ci) calculations according to the National Design Specification (NDS) for Wood Construction, Section 4.3.8. The incising factor adjusts design values for lumber that has been incised to improve preservative penetration.

## Key Concepts

### When Incising Factor Applies

The incising factor applies to:
- **Pressure-treated lumber** that has been incised
- **All design values** except compression perpendicular to grain (Fc⊥)
- **Sawn lumber** with incisions meeting NDS specifications
- **Preservative treatment** enhancement applications

### Incising Process

**Purpose of Incising:**
- Improve **preservative penetration** in difficult-to-treat species
- Create **channels** for preservative chemicals
- Enhance **long-term durability** in harsh environments

**Incising Specifications (NDS 4.3.8):**
- Maximum depth: **0.4 inches**
- Maximum length: **0.375 inches** (3/8")
- Maximum density: **1,100 incisions per ft²**

### Incising Factor Values

- **Modulus of Elasticity** (<PERSON>, Emin): Ci = 0.95
- **Strength Values** (Fb, Ft, Fc, Fv): Ci = 0.80
- **Compression Perpendicular** (Fc⊥): Ci = 1.00 (no reduction)

## API Reference

### Main Functions

#### `getIncisingFactorCi(designValueType)`

Gets the incising factor for a specific design value type.

**Parameters:**
- `designValueType`: Type of design value ('Fb', 'Ft', 'Fc', 'Fv', 'E', 'Emin', 'Fc_perp')

**Returns:**
- Incising factor value (0.80, 0.95, or 1.00)

#### `validateIncisingSpecifications(specifications)`

Validates incising specifications against NDS requirements.

**Parameters:**
- `specifications.depth`: Incision depth (inches)
- `specifications.length`: Incision length (inches)
- `specifications.density`: Incision density (per ft²)

**Returns:**
- Validation results with compliance status

#### `applyIncisingFactor(designValue, designValueType)`

Applies incising factor to a design value.

**Parameters:**
- `designValue`: Base design value
- `designValueType`: Type of design value

**Returns:**
- Adjusted design value

#### `getMultipleIncisingFactors(designValues)`

Applies incising factors to multiple design values.

**Parameters:**
- `designValues`: Object with design value types and values

**Returns:**
- Object with adjusted design values

### Usage Examples

```typescript
import { 
  getIncisingFactorCi,
  applyIncisingFactor,
  validateIncisingSpecifications
} from './incising';

// Example 1: Get individual factors
const bendingFactor = getIncisingFactorCi('Fb');      // 0.80
const elasticityFactor = getIncisingFactorCi('E');    // 0.95
const compressionPerpFactor = getIncisingFactorCi('Fc_perp'); // 1.00

// Example 2: Apply to design values
const baseFb = 1000; // psi
const incisedFb = applyIncisingFactor(baseFb, 'Fb');
console.log(incisedFb); // 800 psi (1000 × 0.80)

// Example 3: Validate incising specifications
const specs = {
  depth: 0.3,     // inches
  length: 0.25,   // inches  
  density: 800    // per ft²
};

const validation = validateIncisingSpecifications(specs);
console.log(validation.compliant); // true
```

### Multiple Design Values

```typescript
import { getMultipleIncisingFactors } from './incising';

const baseValues = {
  Fb: 1200,   // psi
  Ft: 800,    // psi
  Fc: 1350,   // psi
  Fv: 180,    // psi
  E: 1600000, // psi
  Fc_perp: 625 // psi
};

const incisedValues = getMultipleIncisingFactors(baseValues);

console.log(incisedValues.Fb);      // 960 psi (1200 × 0.80)
console.log(incisedValues.E);       // 1520000 psi (1600000 × 0.95)
console.log(incisedValues.Fc_perp); // 625 psi (625 × 1.00)
```

## Design Considerations

### When Incising is Required

**Species Requiring Incising:**
- **Douglas Fir-Larch** (heartwood)
- **Hem-Fir** (heartwood)
- **Southern Pine** (heartwood, when required)
- Other **difficult-to-treat species**

**Applications Requiring Incising:**
- **Ground contact** applications
- **Marine exposure**
- **High decay hazard** environments
- **Structural applications** with preservation requirements

### Material Selection

**Incised vs. Non-Incised:**
- Consider **strength reduction** in design
- Evaluate **preservative requirements**
- Assess **environmental exposure**
- Balance **durability** vs. **strength**

**Design Strategies:**
- **Upsize members** to compensate for strength reduction
- Use **higher grade lumber** when incising is required
- Consider **alternative species** that don't require incising
- Apply **multiple adjustment factors** appropriately

## Files

- `incising.ts` - Main implementation
- `incising.test.ts` - Comprehensive test suite
- `README.md` - This documentation

## References

- NDS 2018, Section 4.3.8 - Incising Factor, Ci
- NDS 2018, Table 4A - Adjustment Factors for Sawn Lumber
- AWPA Standards - Wood preservation requirements
- ASTM D1760 - Standard Test Methods for Pressure Treatment of Lumber 