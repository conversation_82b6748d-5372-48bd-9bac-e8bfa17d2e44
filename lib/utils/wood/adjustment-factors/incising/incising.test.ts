/**
 * Unit tests for Sawn Lumber Incising Adjustment Factors
 * Based on NDS (National Design Specification) for Wood Construction - Section 4.3.8
 */

import {
  getIncisingFactorCi,
  validateIncisingSpecifications,
  applyIncisingFactor,
  getMultipleIncisingFactors,
  getIncisingFactorByName,
} from './incising';
import {
  DESIGN_VALUE_TYPES,
  INCISING_FACTORS,
  INCISING_SPECIFICATIONS,
} from '../../constants';

describe('Incising Factor Calculations', () => {
  describe('getIncisingFactorCi', () => {
    describe('Modulus of Elasticity Values', () => {
      test('should return 0.95 for E (modulus of elasticity)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.E);
        expect(result).toBe(INCISING_FACTORS.MODULUS_OF_ELASTICITY);
        expect(result).toBe(0.95);
      });

      test('should return 0.95 for Emin (minimum modulus of elasticity)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.E_MIN);
        expect(result).toBe(INCISING_FACTORS.MODULUS_OF_ELASTICITY);
        expect(result).toBe(0.95);
      });
    });

    describe('Strength Values', () => {
      test('should return 0.80 for Fb (bending)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.BENDING);
        expect(result).toBe(INCISING_FACTORS.STRENGTH_VALUES);
        expect(result).toBe(0.80);
      });

      test('should return 0.80 for Ft (tension parallel to grain)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.TENSION_PARALLEL);
        expect(result).toBe(INCISING_FACTORS.STRENGTH_VALUES);
        expect(result).toBe(0.80);
      });

      test('should return 0.80 for Fc (compression parallel to grain)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL);
        expect(result).toBe(INCISING_FACTORS.STRENGTH_VALUES);
        expect(result).toBe(0.80);
      });

      test('should return 0.80 for Fv (shear)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.SHEAR);
        expect(result).toBe(INCISING_FACTORS.STRENGTH_VALUES);
        expect(result).toBe(0.80);
      });
    });

    describe('Compression Perpendicular to Grain', () => {
      test('should return 1.00 for Fc⊥ (compression perpendicular to grain)', () => {
        const result = getIncisingFactorCi(DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR);
        expect(result).toBe(INCISING_FACTORS.COMPRESSION_PERPENDICULAR);
        expect(result).toBe(1.00);
      });
    });

    describe('Invalid Input', () => {
      test('should throw error for invalid design value type', () => {
        expect(() => {
          getIncisingFactorCi('invalid_type' as any);
        }).toThrow('Invalid design value type: invalid_type');
      });
    });
  });

  describe('validateIncisingSpecifications', () => {
    describe('Valid Specifications', () => {
      test('should return valid for specifications within limits', () => {
        const result = validateIncisingSpecifications(0.3, 0.25, 1000);
        expect(result.isValid).toBe(true);
        expect(result.violations).toHaveLength(0);
      });

      test('should return valid for specifications at exact limits', () => {
        const result = validateIncisingSpecifications(
          INCISING_SPECIFICATIONS.MAX_DEPTH,
          INCISING_SPECIFICATIONS.MAX_LENGTH,
          INCISING_SPECIFICATIONS.MAX_DENSITY
        );
        expect(result.isValid).toBe(true);
        expect(result.violations).toHaveLength(0);
      });

      test('should return valid for zero values', () => {
        const result = validateIncisingSpecifications(0, 0, 0);
        expect(result.isValid).toBe(true);
        expect(result.violations).toHaveLength(0);
      });
    });

    describe('Invalid Specifications', () => {
      test('should return invalid for depth exceeding limit', () => {
        const excessiveDepth = INCISING_SPECIFICATIONS.MAX_DEPTH + 0.1;
        const result = validateIncisingSpecifications(excessiveDepth, 0.2, 500);
        
        expect(result.isValid).toBe(false);
        expect(result.violations).toHaveLength(1);
        expect(result.violations[0]).toContain('Incision depth');
        expect(result.violations[0]).toContain('exceeds maximum allowed');
      });

      test('should return invalid for length exceeding limit', () => {
        const excessiveLength = INCISING_SPECIFICATIONS.MAX_LENGTH + 0.1;
        const result = validateIncisingSpecifications(0.2, excessiveLength, 500);
        
        expect(result.isValid).toBe(false);
        expect(result.violations).toHaveLength(1);
        expect(result.violations[0]).toContain('Incision length');
        expect(result.violations[0]).toContain('exceeds maximum allowed');
      });

      test('should return invalid for density exceeding limit', () => {
        const excessiveDensity = INCISING_SPECIFICATIONS.MAX_DENSITY + 100;
        const result = validateIncisingSpecifications(0.2, 0.2, excessiveDensity);
        
        expect(result.isValid).toBe(false);
        expect(result.violations).toHaveLength(1);
        expect(result.violations[0]).toContain('Incision density');
        expect(result.violations[0]).toContain('exceeds maximum allowed');
      });

      test('should return multiple violations for multiple excessive values', () => {
        const result = validateIncisingSpecifications(0.5, 0.5, 1500);
        
        expect(result.isValid).toBe(false);
        expect(result.violations).toHaveLength(3);
        expect(result.violations.some(v => v.includes('depth'))).toBe(true);
        expect(result.violations.some(v => v.includes('length'))).toBe(true);
        expect(result.violations.some(v => v.includes('density'))).toBe(true);
      });
    });
  });

  describe('applyIncisingFactor', () => {
    describe('Valid Applications', () => {
      test('should apply incising factor to bending value', () => {
        const originalValue = 1000; // psi
        const result = applyIncisingFactor(originalValue, DESIGN_VALUE_TYPES.BENDING);
        
        expect(result).toBe(originalValue * INCISING_FACTORS.STRENGTH_VALUES);
        expect(result).toBe(800); // 1000 * 0.8
      });

      test('should apply incising factor to modulus of elasticity', () => {
        const originalValue = 1500000; // psi
        const result = applyIncisingFactor(originalValue, DESIGN_VALUE_TYPES.E);
        
        expect(result).toBe(originalValue * INCISING_FACTORS.MODULUS_OF_ELASTICITY);
        expect(result).toBe(1425000); // 1500000 * 0.95
      });

      test('should not change compression perpendicular value', () => {
        const originalValue = 625; // psi
        const result = applyIncisingFactor(originalValue, DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR);
        
        expect(result).toBe(originalValue * INCISING_FACTORS.COMPRESSION_PERPENDICULAR);
        expect(result).toBe(625); // 625 * 1.0
      });

      test('should work with custom valid incising specifications', () => {
        const originalValue = 1200;
        const result = applyIncisingFactor(
          originalValue, 
          DESIGN_VALUE_TYPES.BENDING,
          0.3, // depth
          0.25, // length  
          800  // density
        );
        
        expect(result).toBe(originalValue * INCISING_FACTORS.STRENGTH_VALUES);
      });
    });

    describe('Invalid Applications', () => {
      test('should throw error for negative design value', () => {
        expect(() => {
          applyIncisingFactor(-100, DESIGN_VALUE_TYPES.BENDING);
        }).toThrow('Design value must be positive');
      });

      test('should throw error for zero design value', () => {
        expect(() => {
          applyIncisingFactor(0, DESIGN_VALUE_TYPES.BENDING);
        }).toThrow('Design value must be positive');
      });

      test('should throw error for excessive incision depth', () => {
        expect(() => {
          applyIncisingFactor(1000, DESIGN_VALUE_TYPES.BENDING, 0.2, 0.2, 1500);
        }).toThrow('Invalid incising specifications');
      });
    });
  });

  describe('getMultipleIncisingFactors', () => {
    test('should return factors for multiple design value types', () => {
      const types = [
        DESIGN_VALUE_TYPES.BENDING,
        DESIGN_VALUE_TYPES.E,
        DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR
      ];
      
      const result = getMultipleIncisingFactors(types);
      
      expect(result[DESIGN_VALUE_TYPES.BENDING]).toBe(0.80);
      expect(result[DESIGN_VALUE_TYPES.E]).toBe(0.95);
      expect(result[DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]).toBe(1.00);
    });

    test('should return empty object for empty input', () => {
      const result = getMultipleIncisingFactors([]);
      expect(result).toEqual({});
    });

    test('should handle all design value types', () => {
      const allTypes = Object.values(DESIGN_VALUE_TYPES);
      const result = getMultipleIncisingFactors(allTypes);
      
      expect(Object.keys(result)).toHaveLength(allTypes.length);
      
      // Spot check a few values
      expect(result[DESIGN_VALUE_TYPES.BENDING]).toBe(0.80);
      expect(result[DESIGN_VALUE_TYPES.E]).toBe(0.95);
      expect(result[DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR]).toBe(1.00);
    });
  });

  describe('getIncisingFactorByName', () => {
    describe('Valid Names', () => {
      test('should work with standard abbreviations', () => {
        expect(getIncisingFactorByName('Fb')).toBe(0.80);
        expect(getIncisingFactorByName('E')).toBe(0.95);
        expect(getIncisingFactorByName('Emin')).toBe(0.95);
        expect(getIncisingFactorByName('Ft')).toBe(0.80);
        expect(getIncisingFactorByName('Fc')).toBe(0.80);
        expect(getIncisingFactorByName('Fv')).toBe(0.80);
      });

      test('should work with case variations', () => {
        expect(getIncisingFactorByName('fb')).toBe(0.80);
        expect(getIncisingFactorByName('FB')).toBe(0.80);
        expect(getIncisingFactorByName('e')).toBe(0.95);
        expect(getIncisingFactorByName('E')).toBe(0.95);
      });

      test('should work with descriptive names', () => {
        expect(getIncisingFactorByName('bending')).toBe(0.80);
        expect(getIncisingFactorByName('tension')).toBe(0.80);
        expect(getIncisingFactorByName('compression')).toBe(0.80);
        expect(getIncisingFactorByName('shear')).toBe(0.80);
        expect(getIncisingFactorByName('compression_perpendicular')).toBe(1.00);
      });

      test('should work with special characters', () => {
        expect(getIncisingFactorByName('Fc⊥')).toBe(1.00);
      });

      test('should work with underscore variations', () => {
        expect(getIncisingFactorByName('E_min')).toBe(0.95);
        expect(getIncisingFactorByName('Fc_perp')).toBe(1.00);
      });
    });

    describe('Invalid Names', () => {
      test('should throw error for unrecognized names', () => {
        expect(() => {
          getIncisingFactorByName('invalid');
        }).toThrow('Unrecognized design value name: invalid');
      });

      test('should throw error for empty string', () => {
        expect(() => {
          getIncisingFactorByName('');
        }).toThrow('Unrecognized design value name:');
      });

      test('should throw error for misspelled names', () => {
        expect(() => {
          getIncisingFactorByName('bendng'); // missing 'i'
        }).toThrow('Unrecognized design value name: bendng');
      });
    });
  });

  describe('Integration Tests', () => {
    describe('Real-world scenarios', () => {
      test('should correctly process typical lumber properties', () => {
        // Typical Southern Pine lumber properties before incising
        const lumberProperties = {
          Fb: 1200,  // psi
          Ft: 800,   // psi
          Fc: 1350,  // psi
          Fv: 175,   // psi
          E: 1600000,  // psi
          FcPerp: 565  // psi
        };

        // Apply incising factors
        const incisedProperties = {
          Fb: applyIncisingFactor(lumberProperties.Fb, DESIGN_VALUE_TYPES.BENDING),
          Ft: applyIncisingFactor(lumberProperties.Ft, DESIGN_VALUE_TYPES.TENSION_PARALLEL),
          Fc: applyIncisingFactor(lumberProperties.Fc, DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL),
          Fv: applyIncisingFactor(lumberProperties.Fv, DESIGN_VALUE_TYPES.SHEAR),
          E: applyIncisingFactor(lumberProperties.E, DESIGN_VALUE_TYPES.E),
          FcPerp: applyIncisingFactor(lumberProperties.FcPerp, DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR)
        };

        expect(incisedProperties.Fb).toBe(960);      // 1200 * 0.8
        expect(incisedProperties.Ft).toBe(640);      // 800 * 0.8
        expect(incisedProperties.Fc).toBe(1080);     // 1350 * 0.8
        expect(incisedProperties.Fv).toBe(140);      // 175 * 0.8
        expect(incisedProperties.E).toBe(1520000);   // 1600000 * 0.95
        expect(incisedProperties.FcPerp).toBe(565);  // 565 * 1.0
      });

      test('should handle edge case of minimal incisions', () => {
        const designValue = 1000;
        
        // Very small but valid incisions
        const result = applyIncisingFactor(
          designValue,
          DESIGN_VALUE_TYPES.BENDING,
          0.1,  // depth
          0.1,  // length
          100   // density
        );
        
        expect(result).toBe(800); // Still applies 0.8 factor
      });

      test('should handle boundary conditions', () => {
        const designValue = 1500;
        
        // Exactly at limits
        const result = applyIncisingFactor(
          designValue,
          DESIGN_VALUE_TYPES.BENDING,
          INCISING_SPECIFICATIONS.MAX_DEPTH,
          INCISING_SPECIFICATIONS.MAX_LENGTH,
          INCISING_SPECIFICATIONS.MAX_DENSITY
        );
        
        expect(result).toBe(1200); // 1500 * 0.8
      });
    });

    describe('Error handling', () => {
      test('should provide descriptive error messages', () => {
        expect(() => {
          applyIncisingFactor(1000, DESIGN_VALUE_TYPES.BENDING, 0.5, 0.5, 1500);
        }).toThrow(/Invalid incising specifications.*depth.*length.*density/);
      });
    });
  });
}); 