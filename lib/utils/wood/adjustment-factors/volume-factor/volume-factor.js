/**
 * Volume Factor (CV) Calculations
 *
 * Implements NDS 5.3.6 Volume Factor for adjusting bending design values
 * based on member volume for glulam and other engineered wood products.
 *
 * Based on:
 * - NDS Section 5.3.6: Volume Factor for glulam
 * - NDS equations for different wood product types
 * - Volume factor applies to bending design value (Fb) only
 * - CV = (21/L * 12/d * 5.125/b)^(1/x) where L=length, d=depth, b=breadth
 *
 * @fileoverview Volume factor calculations for wood structural design
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */
import { DESIGN_VALUE_TYPES, } from "../../constants";
/**
 * Wood product types for volume factor determination
 */
export const VOLUME_FACTOR_PRODUCTS = {
    // Glulam products
    GLULAM_BENDING_MEMBERS: 'glulam_bending',
    GLULAM_AXIALLY_LOADED: 'glulam_axial',
    // Structural composite lumber
    LVL: 'lvl',
    PSL: 'psl',
    LSL: 'lsl',
    OSL: 'osl',
    // Cross-laminated timber
    CLT: 'clt',
    // Sawn lumber (for comparison)
    SAWN_LUMBER: 'sawn_lumber',
};
/**
 * Volume factor parameters for different wood products
 */
export const VOLUME_FACTOR_PARAMETERS = {
    // Glulam bending members
    [VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS]: {
        powerFactor: 10,
        referenceDimensions: {
            length: 21,
            depth: 12,
            breadth: 5.125, // Reference breadth in inches
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Glulam axially loaded members
    [VOLUME_FACTOR_PRODUCTS.GLULAM_AXIALLY_LOADED]: {
        powerFactor: 20,
        referenceDimensions: {
            length: 21,
            depth: 12,
            breadth: 5.125,
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Laminated Veneer Lumber (LVL)
    [VOLUME_FACTOR_PRODUCTS.LVL]: {
        powerFactor: 20,
        referenceDimensions: {
            length: 12,
            depth: 12,
            breadth: 1.75,
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Parallel Strand Lumber (PSL)
    [VOLUME_FACTOR_PRODUCTS.PSL]: {
        powerFactor: 20,
        referenceDimensions: {
            length: 12,
            depth: 12,
            breadth: 3.5,
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Laminated Strand Lumber (LSL)
    [VOLUME_FACTOR_PRODUCTS.LSL]: {
        powerFactor: 20,
        referenceDimensions: {
            length: 12,
            depth: 12,
            breadth: 3.5,
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Oriented Strand Lumber (OSL)
    [VOLUME_FACTOR_PRODUCTS.OSL]: {
        powerFactor: 20,
        referenceDimensions: {
            length: 12,
            depth: 12,
            breadth: 3.5,
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Cross-Laminated Timber (CLT)
    [VOLUME_FACTOR_PRODUCTS.CLT]: {
        powerFactor: 20,
        referenceDimensions: {
            length: 12,
            depth: 12,
            breadth: 3.5,
        },
        limits: {
            minimum: 0.1,
            maximum: 1.0, // CV ≤ 1.0 (no increase above reference)
        },
    },
    // Sawn lumber (volume factor = 1.0)
    [VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER]: {
        powerFactor: 1,
        referenceDimensions: {
            length: 12,
            depth: 12,
            breadth: 3.5,
        },
        limits: {
            minimum: 1.0,
            maximum: 1.0, // Always 1.0 for sawn lumber
        },
    },
};
/**
 * Volume factor constants and limitations
 */
export const VOLUME_FACTOR_CONSTANTS = {
    // Default factors
    DEFAULT_VOLUME_FACTOR: 1.0,
    SAWN_LUMBER_VOLUME_FACTOR: 1.0,
    // Calculation limits
    MIN_DIMENSION_INCHES: 0.1,
    MAX_DIMENSION_INCHES: 1000,
    MIN_LENGTH_FEET: 0.1,
    MAX_LENGTH_FEET: 1000,
    // Mathematical constants
    FEET_TO_INCHES: 12,
    PRECISION_DECIMAL_PLACES: 6, // Calculation precision
};
/**
 * Validates volume factor input parameters
 *
 * @param input - Input parameters to validate
 * @returns Validation result with errors and warnings
 *
 * @example
 * ```typescript
 * const input = {
 *   length: 20,
 *   width: 5.125,
 *   depth: 12,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS
 * };
 * const validation = validateVolumeFactorInput(input);
 * ```
 */
export function validateVolumeFactorInput(input) {
    const errors = [];
    const warnings = [];
    // Validate length
    if (typeof input.length !== 'number' ||
        isNaN(input.length) ||
        !isFinite(input.length)) {
        errors.push('Length must be a valid number');
    }
    else if (input.length <= 0) {
        errors.push('Length must be positive');
    }
    else if (input.length < VOLUME_FACTOR_CONSTANTS.MIN_LENGTH_FEET) {
        warnings.push(`Length is very small (${input.length}') - verify dimensions`);
    }
    else if (input.length > VOLUME_FACTOR_CONSTANTS.MAX_LENGTH_FEET) {
        warnings.push(`Length exceeds ${VOLUME_FACTOR_CONSTANTS.MAX_LENGTH_FEET}' - verify practicality`);
    }
    // Validate width
    if (typeof input.width !== 'number' ||
        isNaN(input.width) ||
        !isFinite(input.width)) {
        errors.push('Width must be a valid number');
    }
    else if (input.width <= 0) {
        errors.push('Width must be positive');
    }
    else if (input.width < VOLUME_FACTOR_CONSTANTS.MIN_DIMENSION_INCHES) {
        warnings.push(`Width is very small (${input.width}") - verify dimensions`);
    }
    else if (input.width > VOLUME_FACTOR_CONSTANTS.MAX_DIMENSION_INCHES) {
        warnings.push(`Width exceeds ${VOLUME_FACTOR_CONSTANTS.MAX_DIMENSION_INCHES}" - verify practicality`);
    }
    // Validate depth
    if (typeof input.depth !== 'number' ||
        isNaN(input.depth) ||
        !isFinite(input.depth)) {
        errors.push('Depth must be a valid number');
    }
    else if (input.depth <= 0) {
        errors.push('Depth must be positive');
    }
    else if (input.depth < VOLUME_FACTOR_CONSTANTS.MIN_DIMENSION_INCHES) {
        warnings.push(`Depth is very small (${input.depth}") - verify dimensions`);
    }
    else if (input.depth > VOLUME_FACTOR_CONSTANTS.MAX_DIMENSION_INCHES) {
        warnings.push(`Depth exceeds ${VOLUME_FACTOR_CONSTANTS.MAX_DIMENSION_INCHES}" - verify practicality`);
    }
    // Validate design value type
    if (!Object.values(DESIGN_VALUE_TYPES).includes(input.designValueType)) {
        errors.push(`Invalid design value type: ${input.designValueType}`);
    }
    else if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
        warnings.push(`Volume factor only applies to bending (Fb), not ${input.designValueType}`);
    }
    // Validate product type
    if (!Object.values(VOLUME_FACTOR_PRODUCTS).includes(input.productType)) {
        errors.push(`Invalid product type: ${input.productType}`);
    }
    // Product-specific validations
    if (input.productType === VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER) {
        warnings.push('Volume factor does not apply to sawn lumber (CV = 1.0)');
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}
/**
 * Calculates volume factor for specified product type and dimensions
 *
 * @param input - Input parameters for volume factor calculation
 * @returns Volume factor calculation details
 * @throws Error if input validation fails
 *
 * @example
 * ```typescript
 * const input = {
 *   length: 20,
 *   width: 5.125,
 *   depth: 12,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS
 * };
 * const calculation = calculateVolumeFactorDetails(input);
 * console.log(calculation.volumeFactor); // e.g., 0.95
 * ```
 */
export function calculateVolumeFactorDetails(input) {
    const validation = validateVolumeFactorInput(input);
    if (!validation.isValid) {
        throw new Error(`Invalid volume factor input: ${validation.errors.join(', ')}`);
    }
    // Get product parameters
    const params = VOLUME_FACTOR_PARAMETERS[input.productType];
    if (!params) {
        throw new Error(`No parameters defined for product type: ${input.productType}`);
    }
    // Handle sawn lumber (CV = 1.0)
    if (input.productType === VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER) {
        return {
            volumeFactor: VOLUME_FACTOR_CONSTANTS.SAWN_LUMBER_VOLUME_FACTOR,
            volumeRatio: 1.0,
            lengthRatio: 1.0,
            depthRatio: 1.0,
            breadthRatio: 1.0,
            powerFactor: 1,
            referenceDimensions: params.referenceDimensions,
            memberVolume: input.length * VOLUME_FACTOR_CONSTANTS.FEET_TO_INCHES * input.depth * input.width,
            referenceVolume: params.referenceDimensions.length * VOLUME_FACTOR_CONSTANTS.FEET_TO_INCHES *
                params.referenceDimensions.depth * params.referenceDimensions.breadth,
        };
    }
    // Calculate dimension ratios
    const lengthRatio = params.referenceDimensions.length / input.length;
    const depthRatio = params.referenceDimensions.depth / input.depth;
    const breadthRatio = params.referenceDimensions.breadth / input.width;
    // Calculate volume ratio
    const volumeRatio = lengthRatio * depthRatio * breadthRatio;
    // Calculate volume factor: CV = (volumeRatio)^(1/x)
    const volumeFactor = Math.pow(volumeRatio, 1 / params.powerFactor);
    // Apply limits
    const limitedVolumeFactor = Math.max(params.limits.minimum, Math.min(params.limits.maximum, volumeFactor));
    // Calculate volumes for reference
    const memberVolume = input.length * VOLUME_FACTOR_CONSTANTS.FEET_TO_INCHES * input.depth * input.width;
    const referenceVolume = params.referenceDimensions.length * VOLUME_FACTOR_CONSTANTS.FEET_TO_INCHES *
        params.referenceDimensions.depth * params.referenceDimensions.breadth;
    return {
        volumeFactor: Math.round(limitedVolumeFactor * Math.pow(10, VOLUME_FACTOR_CONSTANTS.PRECISION_DECIMAL_PLACES)) /
            Math.pow(10, VOLUME_FACTOR_CONSTANTS.PRECISION_DECIMAL_PLACES),
        volumeRatio,
        lengthRatio,
        depthRatio,
        breadthRatio,
        powerFactor: params.powerFactor,
        referenceDimensions: params.referenceDimensions,
        memberVolume,
        referenceVolume,
    };
}
/**
 * Gets volume factor for specified product type and dimensions
 *
 * @param input - Input parameters for volume factor calculation
 * @returns Volume factor (CV)
 * @throws Error if input validation fails
 *
 * @example
 * ```typescript
 * const input = {
 *   length: 20,
 *   width: 5.125,
 *   depth: 12,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS
 * };
 * const factor = getVolumeFactor(input);
 * console.log(factor); // 0.95 for smaller volume than reference
 * ```
 */
export function getVolumeFactor(input) {
    // Volume factor only applies to bending
    if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
        return VOLUME_FACTOR_CONSTANTS.DEFAULT_VOLUME_FACTOR;
    }
    const calculation = calculateVolumeFactorDetails(input);
    return calculation.volumeFactor;
}
/**
 * Applies volume factor to a reference bending design value
 *
 * @param referenceValue - Reference bending design value (Fb)
 * @param input - Input parameters for volume factor calculation
 * @returns Adjusted bending design value
 * @throws Error if reference value is invalid or input validation fails
 *
 * @example
 * ```typescript
 * const adjustedFb = applyVolumeFactor(2400, {
 *   length: 20,
 *   width: 5.125,
 *   depth: 12,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS
 * });
 * console.log(adjustedFb); // 2280 (2400 × 0.95)
 * ```
 */
export function applyVolumeFactor(referenceValue, input) {
    if (typeof referenceValue !== 'number' ||
        isNaN(referenceValue) ||
        !isFinite(referenceValue) ||
        referenceValue < 0) {
        throw new Error('Reference value must be a non-negative finite number');
    }
    const volumeFactor = getVolumeFactor(input);
    return referenceValue * volumeFactor;
}
/**
 * Provides comprehensive analysis of volume factor calculation
 *
 * @param input - Input parameters for analysis
 * @returns Detailed analysis including calculation, categories, and validation
 *
 * @example
 * ```typescript
 * const analysis = getVolumeFactorAnalysis({
 *   length: 20,
 *   width: 5.125,
 *   depth: 12,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS
 * });
 * console.log(analysis.calculation.volumeFactor); // 0.95
 * console.log(analysis.productCategory); // 'glulam'
 * ```
 */
export function getVolumeFactorAnalysis(input) {
    const validation = validateVolumeFactorInput(input);
    if (!validation.isValid) {
        return {
            input,
            calculation: {
                volumeFactor: VOLUME_FACTOR_CONSTANTS.DEFAULT_VOLUME_FACTOR,
                volumeRatio: 0,
                lengthRatio: 0,
                depthRatio: 0,
                breadthRatio: 0,
                powerFactor: 0,
                referenceDimensions: { length: 0, depth: 0, breadth: 0 },
                memberVolume: 0,
                referenceVolume: 0,
            },
            applicableDesignValue: false,
            productCategory: 'invalid',
            validation,
        };
    }
    const calculation = calculateVolumeFactorDetails(input);
    // Check if design value is applicable
    const applicableDesignValue = input.designValueType === DESIGN_VALUE_TYPES.BENDING;
    // Determine product category
    let productCategory;
    if (input.productType.includes('glulam')) {
        productCategory = 'glulam';
    }
    else if (['lvl', 'psl', 'lsl', 'osl'].includes(input.productType)) {
        productCategory = 'structural_composite_lumber';
    }
    else if (input.productType === VOLUME_FACTOR_PRODUCTS.CLT) {
        productCategory = 'cross_laminated_timber';
    }
    else if (input.productType === VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER) {
        productCategory = 'sawn_lumber';
    }
    else {
        productCategory = 'other';
    }
    return {
        input,
        calculation,
        applicableDesignValue,
        productCategory,
        validation,
    };
}
/**
 * Calculates multiple volume factors for different product types
 *
 * @param baseInput - Base input parameters (without productType)
 * @param productTypes - Array of product types to test
 * @returns Object mapping product type names to volume factors
 *
 * @example
 * ```typescript
 * const factors = getMultipleVolumeFactors(
 *   {
 *     length: 20,
 *     width: 5.125,
 *     depth: 12,
 *     designValueType: DESIGN_VALUE_TYPES.BENDING
 *   },
 *   [
 *     VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
 *     VOLUME_FACTOR_PRODUCTS.LVL,
 *     VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER
 *   ]
 * );
 * console.log(factors); // { glulam_bending: 0.95, lvl: 0.92, sawn_lumber: 1.0 }
 * ```
 */
export function getMultipleVolumeFactors(baseInput, productTypes) {
    const result = {};
    for (const productType of productTypes) {
        try {
            const input = {
                ...baseInput,
                productType,
            };
            result[productType] = getVolumeFactor(input);
        }
        catch (error) {
            // Return default factor for invalid configurations
            result[productType] = VOLUME_FACTOR_CONSTANTS.DEFAULT_VOLUME_FACTOR;
        }
    }
    return result;
}
/**
 * Checks if a design value is affected by volume factors
 *
 * @param designValueType - Type of design value to check
 * @returns True if the design value is affected by volume factors
 *
 * @example
 * ```typescript
 * const isAffected = isDesignValueAffectedByVolume(DESIGN_VALUE_TYPES.BENDING);
 * console.log(isAffected); // true
 *
 * const isNotAffected = isDesignValueAffectedByVolume(DESIGN_VALUE_TYPES.TENSION_PARALLEL);
 * console.log(isNotAffected); // false
 * ```
 */
export function isDesignValueAffectedByVolume(designValueType) {
    return designValueType === DESIGN_VALUE_TYPES.BENDING;
}
/**
 * Determines size optimization based on volume factor effects
 *
 * @param length - Member length in feet
 * @param productType - Wood product type
 * @param targetVolumeFactor - Target volume factor (optional)
 * @returns Analysis of size optimization opportunities
 *
 * @example
 * ```typescript
 * const optimization = getVolumeFactorOptimization(
 *   20,
 *   VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
 *   0.95
 * );
 * console.log(optimization.recommendedDimensions);
 * ```
 */
export function getVolumeFactorOptimization(length, productType, targetVolumeFactor = 1.0) {
    const params = VOLUME_FACTOR_PARAMETERS[productType];
    if (!params) {
        throw new Error(`No parameters defined for product type: ${productType}`);
    }
    // Calculate length effect on volume factor
    const lengthRatio = params.referenceDimensions.length / length;
    const lengthEffect = Math.pow(lengthRatio, 1 / params.powerFactor);
    const recommendations = [];
    if (lengthEffect < targetVolumeFactor) {
        recommendations.push(`Consider shorter spans to improve volume factor`);
        recommendations.push(`Current length (${length}') reduces volume factor to ${lengthEffect.toFixed(3)}`);
    }
    if (lengthEffect > targetVolumeFactor) {
        recommendations.push(`Length allows for favorable volume factor of ${lengthEffect.toFixed(3)}`);
    }
    if (productType === VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER) {
        recommendations.push(`Volume factor does not apply to sawn lumber`);
    }
    return {
        targetVolumeFactor,
        productType,
        referenceDimensions: params.referenceDimensions,
        lengthEffect,
        recommendations,
    };
}
