# Volume Factor (CV)

## Overview

This module implements the volume factor (CV) calculations according to the National Design Specification (NDS) for Wood Construction. The volume factor accounts for the statistical reduction in variability of bending strength as the volume of wood under stress increases.

## Key Concepts

### When Volume Factor Applies

The volume factor applies to:
- **Glued laminated timber (glulam)**
- **Structural composite lumber (SCL)** products
- **Cross-laminated timber (CLT)** in some cases

The volume factor **does not apply** to:
- Sawn lumber (dimension lumber, timbers, etc.)
- Structural glued laminated timber when CV is already included in published values

### Formula

For glulam members:
```
CV = (21/L)^(1/x) × (12/d)^(1/x) × (5.125/b)^(1/x) ≤ 1.0
```

Where:
- L = length of bending member between points of zero moment (ft)
- d = depth of bending member (in)
- b = width of bending member (in)  
- x = 20 for southern pine, x = 10 for other species

### Limits

- CV ≤ 1.0 (volume factor cannot exceed 1.0)
- Minimum practical values typically around 0.85-0.95

## API Reference

### Main Functions

#### `getVolumeFactor(input)`

Calculates the volume factor for a given member configuration.

**Parameters:**
- `input.product`: Product type ('glulam', 'scl', 'clt', etc.)
- `input.length`: Length between zero moment points (ft)
- `input.depth`: Member depth (in)
- `input.width`: Member width (in)
- `input.species`: Wood species ('southern_pine' or 'other')
- `input.designValue`: Design value type ('Fb', etc.)

**Returns:**
- `factor`: The volume factor (≤ 1.0)
- `applicable`: Whether the factor applies
- `calculation`: Detailed calculation breakdown
- `explanation`: Detailed explanation

#### `validateVolumeFactorInput(input)`

Validates input parameters and provides detailed validation results.

#### `getVolumeFactorAnalysis(input)`

Provides comprehensive analysis including step-by-step calculations.

#### `calculateVolumeFactorDetails(input)`

Returns detailed calculation breakdown with intermediate values.

### Usage Examples

```typescript
import { getVolumeFactor } from './volume-factor';

// Example 1: Glulam beam
const glulam = getVolumeFactor({
  product: 'glulam',
  length: 20,      // 20 ft span
  depth: 18,       // 18" deep
  width: 5.125,    // 5.125" wide
  species: 'southern_pine',
  designValue: 'Fb'
});

console.log(glulam.factor); // e.g., 0.92

// Example 2: SCL member
const scl = getVolumeFactor({
  product: 'scl',
  length: 16,
  depth: 14,
  width: 3.5,
  species: 'other',
  designValue: 'Fb'
});
```

### Optimization Functions

#### `getVolumeFactorOptimization(constraints)`

Helps optimize member dimensions for better volume factors.

**Parameters:**
- `constraints.maxLength`: Maximum allowable length
- `constraints.maxDepth`: Maximum allowable depth
- `constraints.maxWidth`: Maximum allowable width
- `constraints.minVolumeFactor`: Minimum acceptable CV
- `constraints.species`: Wood species
- `constraints.product`: Product type

## Files

- `volume-factor.ts` - Main TypeScript implementation
- `volume-factor.js` - JavaScript implementation (legacy)
- `volume-factor.test.ts` - Comprehensive test suite
- `volume-factor-examples.ts` - TypeScript usage examples
- `volume-factor-examples.js` - JavaScript usage examples (legacy)
- `README.md` - This documentation

## References

- NDS 2018, Section 5.3.6 - Volume Factor, CV (Glulam)
- NDS 2018, Section 8.4.4 - Volume Factor for SCL
- ANSI/APA PRG 320 - Cross-Laminated Timber 