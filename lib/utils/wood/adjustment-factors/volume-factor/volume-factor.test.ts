/**
 * Tests for Volume Factor (CV) Calculations
 * 
 * @fileoverview Comprehensive test suite for volume factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import { describe, test, expect } from '@jest/globals';
import {
  // Constants
  VOLUME_FACTOR_PRODUCTS,
  VOLUME_FACTOR_PARAMETERS,
  VOLUME_FACTOR_CONSTANTS,
  
  // Types
  type VolumeFactorInput,
  type VolumeFactorValidation,
  type VolumeFactorAnalysis,
  type VolumeFactorCalculation,
  
  // Functions
  validateVolumeFactorInput,
  calculateVolumeFactorDetails,
  getVolumeFactor,
  applyVolumeFactor,
  getVolumeFactorAnalysis,
  getMultipleVolumeFactors,
  isDesignValueAffectedByVolume,
  getVolumeFactorOptimization,
} from './volume-factor';

import { DESIGN_VALUE_TYPES } from '../../constants';

describe('Volume Factor Constants', () => {
  test('should have correct product types defined', () => {
    expect(VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS).toBe('glulam_bending');
    expect(VOLUME_FACTOR_PRODUCTS.GLULAM_AXIALLY_LOADED).toBe('glulam_axial');
    expect(VOLUME_FACTOR_PRODUCTS.LVL).toBe('lvl');
    expect(VOLUME_FACTOR_PRODUCTS.PSL).toBe('psl');
    expect(VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER).toBe('sawn_lumber');
  });

  test('should have correct parameters for glulam bending', () => {
    const params = VOLUME_FACTOR_PARAMETERS[VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS];
    expect(params.powerFactor).toBe(10);
    expect(params.referenceDimensions.length).toBe(21);
    expect(params.referenceDimensions.depth).toBe(12);
    expect(params.referenceDimensions.breadth).toBe(5.125);
    expect(params.limits.minimum).toBe(0.1);
    expect(params.limits.maximum).toBe(1.0);
  });

  test('should have correct parameters for structural composite lumber', () => {
    const products = [
      VOLUME_FACTOR_PRODUCTS.LVL,
      VOLUME_FACTOR_PRODUCTS.PSL,
      VOLUME_FACTOR_PRODUCTS.LSL,
      VOLUME_FACTOR_PRODUCTS.OSL,
    ];

    products.forEach(product => {
      const params = VOLUME_FACTOR_PARAMETERS[product];
      expect(params.powerFactor).toBe(20);
      expect(params.limits.minimum).toBe(0.1);
      expect(params.limits.maximum).toBe(1.0);
    });
  });

  test('should have correct default values', () => {
    expect(VOLUME_FACTOR_CONSTANTS.DEFAULT_VOLUME_FACTOR).toBe(1.0);
    expect(VOLUME_FACTOR_CONSTANTS.SAWN_LUMBER_VOLUME_FACTOR).toBe(1.0);
    expect(VOLUME_FACTOR_CONSTANTS.FEET_TO_INCHES).toBe(12);
    expect(VOLUME_FACTOR_CONSTANTS.PRECISION_DECIMAL_PLACES).toBe(6);
  });
});

describe('validateVolumeFactorInput', () => {
  const validInput: VolumeFactorInput = {
    length: 20,
    width: 5.125,
    depth: 12,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
  };

  test('should validate correct input', () => {
    const result = validateVolumeFactorInput(validInput);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('should reject invalid length values', () => {
    const invalidInputs = [
      { ...validInput, length: 0 },
      { ...validInput, length: -1 },
      { ...validInput, length: NaN },
      { ...validInput, length: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateVolumeFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid width values', () => {
    const invalidInputs = [
      { ...validInput, width: 0 },
      { ...validInput, width: -1 },
      { ...validInput, width: NaN },
      { ...validInput, width: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateVolumeFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid depth values', () => {
    const invalidInputs = [
      { ...validInput, depth: 0 },
      { ...validInput, depth: -1 },
      { ...validInput, depth: NaN },
      { ...validInput, depth: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateVolumeFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid design value types', () => {
    const result = validateVolumeFactorInput({
      ...validInput,
      designValueType: 'invalid' as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Invalid design value type: invalid');
  });

  test('should warn about non-bending design values', () => {
    const result = validateVolumeFactorInput({
      ...validInput,
      designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Volume factor only applies to bending (Fb), not Ft');
  });

  test('should reject invalid product types', () => {
    const result = validateVolumeFactorInput({
      ...validInput,
      productType: 'invalid' as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Invalid product type: invalid');
  });

  test('should warn about sawn lumber', () => {
    const result = validateVolumeFactorInput({
      ...validInput,
      productType: VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Volume factor does not apply to sawn lumber (CV = 1.0)');
  });

  test('should warn about extreme dimensions', () => {
    const result = validateVolumeFactorInput({
      ...validInput,
      length: 500,
      width: 0.05,
      depth: 0.05,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings.length).toBeGreaterThan(0);
  });
});

describe('calculateVolumeFactorDetails', () => {
  test('should calculate correct volume factor for reference glulam dimensions', () => {
    const input: VolumeFactorInput = {
      length: 21,    // Reference length
      width: 5.125,  // Reference breadth
      depth: 12,     // Reference depth
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const calculation = calculateVolumeFactorDetails(input);
    
    expect(calculation.lengthRatio).toBe(1.0);
    expect(calculation.depthRatio).toBe(1.0);
    expect(calculation.breadthRatio).toBe(1.0);
    expect(calculation.volumeRatio).toBe(1.0);
    expect(calculation.volumeFactor).toBe(1.0);
    expect(calculation.powerFactor).toBe(10);
  });

  test('should calculate volume factor for larger glulam member', () => {
    const input: VolumeFactorInput = {
      length: 42,     // 2x reference length
      width: 10.25,   // 2x reference breadth
      depth: 24,      // 2x reference depth
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const calculation = calculateVolumeFactorDetails(input);
    
    expect(calculation.lengthRatio).toBe(0.5);
    expect(calculation.depthRatio).toBe(0.5);
    expect(calculation.breadthRatio).toBe(0.5);
    expect(calculation.volumeRatio).toBe(0.125); // 0.5^3
    
    // CV = (0.125)^(1/10) ≈ 0.812
    expect(calculation.volumeFactor).toBeCloseTo(0.812, 2);
  });

  test('should calculate volume factor for smaller glulam member', () => {
    const input: VolumeFactorInput = {
      length: 10.5,   // 0.5x reference length
      width: 2.5625,  // 0.5x reference breadth
      depth: 6,       // 0.5x reference depth
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const calculation = calculateVolumeFactorDetails(input);
    
    expect(calculation.lengthRatio).toBe(2.0);
    expect(calculation.depthRatio).toBe(2.0);
    expect(calculation.breadthRatio).toBe(2.0);
    expect(calculation.volumeRatio).toBe(8.0); // 2^3
    
    // CV = (8.0)^(1/10) ≈ 1.23, but limited to 1.0
    expect(calculation.volumeFactor).toBe(1.0);
  });

  test('should handle LVL with different power factor', () => {
    const input: VolumeFactorInput = {
      length: 24,     // 2x reference length for LVL
      width: 3.5,     // 2x reference breadth
      depth: 24,      // 2x reference depth
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.LVL,
    };

    const calculation = calculateVolumeFactorDetails(input);
    
    expect(calculation.powerFactor).toBe(20);
    expect(calculation.lengthRatio).toBe(0.5);
    expect(calculation.depthRatio).toBe(0.5);
    expect(calculation.breadthRatio).toBe(0.5);
    expect(calculation.volumeRatio).toBe(0.125);
    
    // CV = (0.125)^(1/20) ≈ 0.9
    expect(calculation.volumeFactor).toBeCloseTo(0.9, 2);
  });

  test('should return 1.0 for sawn lumber', () => {
    const input: VolumeFactorInput = {
      length: 20,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER,
    };

    const calculation = calculateVolumeFactorDetails(input);
    
    expect(calculation.volumeFactor).toBe(1.0);
    expect(calculation.volumeRatio).toBe(1.0);
    expect(calculation.powerFactor).toBe(1);
  });

  test('should throw error for invalid input', () => {
    const input: VolumeFactorInput = {
      length: -1,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    expect(() => calculateVolumeFactorDetails(input)).toThrow();
  });

  test('should calculate volumes correctly', () => {
    const input: VolumeFactorInput = {
      length: 20,     // feet
      width: 5.125,   // inches
      depth: 12,      // inches
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const calculation = calculateVolumeFactorDetails(input);
    
    // Member volume = 20 ft × 12 in/ft × 12 in × 5.125 in
    const expectedMemberVolume = 20 * 12 * 12 * 5.125;
    expect(calculation.memberVolume).toBe(expectedMemberVolume);
    
    // Reference volume = 21 ft × 12 in/ft × 12 in × 5.125 in
    const expectedReferenceVolume = 21 * 12 * 12 * 5.125;
    expect(calculation.referenceVolume).toBe(expectedReferenceVolume);
  });
});

describe('getVolumeFactor', () => {
  test('should return 1.0 for non-bending design values', () => {
    const input: VolumeFactorInput = {
      length: 20,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const factor = getVolumeFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should return correct volume factor for bending', () => {
    const input: VolumeFactorInput = {
      length: 42,     // Large member
      width: 10.25,
      depth: 24,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const factor = getVolumeFactor(input);
    expect(factor).toBeLessThan(1.0);
    expect(factor).toBeCloseTo(0.812, 2);
  });

  test('should handle all product types', () => {
    const productTypes = Object.values(VOLUME_FACTOR_PRODUCTS);
    
    productTypes.forEach(productType => {
      const input: VolumeFactorInput = {
        length: 20,
        width: 5.125,
        depth: 12,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType,
      };

      expect(() => getVolumeFactor(input)).not.toThrow();
      const factor = getVolumeFactor(input);
      expect(typeof factor).toBe('number');
      expect(factor).toBeGreaterThan(0);
      expect(factor).toBeLessThanOrEqual(1.0);
    });
  });

  test('should return 1.0 for sawn lumber regardless of dimensions', () => {
    const inputs = [
      { length: 10, width: 2, depth: 4 },
      { length: 50, width: 12, depth: 24 },
      { length: 8, width: 1.5, depth: 3.5 },
    ];

    inputs.forEach(dims => {
      const input: VolumeFactorInput = {
        ...dims,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType: VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER,
      };

      const factor = getVolumeFactor(input);
      expect(factor).toBe(1.0);
    });
  });
});

describe('applyVolumeFactor', () => {
  test('should apply volume factor to reference value', () => {
    const input: VolumeFactorInput = {
      length: 42,     // Large member
      width: 10.25,
      depth: 24,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const volumeFactor = getVolumeFactor(input);
    const adjustedValue = applyVolumeFactor(2400, input);
    const expectedValue = 2400 * volumeFactor;
    expect(adjustedValue).toBeCloseTo(expectedValue, 6);
  });

  test('should return same value when volume factor is 1.0', () => {
    const input: VolumeFactorInput = {
      length: 21,     // Reference dimensions
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const adjustedValue = applyVolumeFactor(2400, input);
    expect(adjustedValue).toBe(2400);
  });

  test('should throw error for invalid reference value', () => {
    const input: VolumeFactorInput = {
      length: 20,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    
    expect(() => applyVolumeFactor(-1, input)).toThrow();
    expect(() => applyVolumeFactor(NaN, input)).toThrow();
    expect(() => applyVolumeFactor(Infinity, input)).toThrow();
  });

  test('should handle zero reference value', () => {
    const input: VolumeFactorInput = {
      length: 20,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const adjustedValue = applyVolumeFactor(0, input);
    expect(adjustedValue).toBe(0);
  });

  test('should handle non-bending design values', () => {
    const input: VolumeFactorInput = {
      length: 42,     // Large member
      width: 10.25,
      depth: 24,
      designValueType: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const adjustedValue = applyVolumeFactor(1600, input);
    expect(adjustedValue).toBe(1600); // No adjustment for non-bending
  });
});

describe('getVolumeFactorAnalysis', () => {
  test('should provide comprehensive analysis for valid input', () => {
    const input: VolumeFactorInput = {
      length: 30,
      width: 8.75,
      depth: 18,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    
    const analysis = getVolumeFactorAnalysis(input);
    
    expect(analysis.input).toEqual(input);
    expect(analysis.calculation.volumeFactor).toBeLessThan(1.0);
    expect(analysis.applicableDesignValue).toBe(true);
    expect(analysis.productCategory).toBe('glulam');
    expect(analysis.validation.isValid).toBe(true);
  });

  test('should handle invalid input gracefully', () => {
    const input: VolumeFactorInput = {
      length: -1,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    
    const analysis = getVolumeFactorAnalysis(input);
    
    expect(analysis.calculation.volumeFactor).toBe(1.0);
    expect(analysis.applicableDesignValue).toBe(false);
    expect(analysis.productCategory).toBe('invalid');
    expect(analysis.validation.isValid).toBe(false);
  });

  test('should categorize products correctly', () => {
    const tests = [
      { product: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS, expected: 'glulam' },
      { product: VOLUME_FACTOR_PRODUCTS.GLULAM_AXIALLY_LOADED, expected: 'glulam' },
      { product: VOLUME_FACTOR_PRODUCTS.LVL, expected: 'structural_composite_lumber' },
      { product: VOLUME_FACTOR_PRODUCTS.PSL, expected: 'structural_composite_lumber' },
      { product: VOLUME_FACTOR_PRODUCTS.LSL, expected: 'structural_composite_lumber' },
      { product: VOLUME_FACTOR_PRODUCTS.OSL, expected: 'structural_composite_lumber' },
      { product: VOLUME_FACTOR_PRODUCTS.CLT, expected: 'cross_laminated_timber' },
      { product: VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER, expected: 'sawn_lumber' },
    ];

    tests.forEach(test => {
      const input: VolumeFactorInput = {
        length: 20,
        width: 5.125,
        depth: 12,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType: test.product,
      };
      
      const analysis = getVolumeFactorAnalysis(input);
      expect(analysis.productCategory).toBe(test.expected);
    });
  });

  test('should detect non-applicable design values', () => {
    const input: VolumeFactorInput = {
      length: 20,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.SHEAR,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    
    const analysis = getVolumeFactorAnalysis(input);
    expect(analysis.applicableDesignValue).toBe(false);
  });
});

describe('getMultipleVolumeFactors', () => {
  test('should calculate factors for multiple product types', () => {
    const baseInput = {
      length: 40,      // Longer span
      width: 8,        // Wider
      depth: 20,       // Deeper - larger member than reference
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    
    const productTypes = [
      VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
      VOLUME_FACTOR_PRODUCTS.LVL,
      VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER,
    ];
    
    const factors = getMultipleVolumeFactors(baseInput, productTypes);
    
    expect(factors[VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS]).toBeLessThan(1.0);
    expect(factors[VOLUME_FACTOR_PRODUCTS.LVL]).toBeLessThan(1.0);
    expect(factors[VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER]).toBe(1.0);
  });

  test('should handle invalid configurations gracefully', () => {
    const baseInput = {
      length: -1, // Invalid
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    
    const productTypes = [VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS];
    
    const factors = getMultipleVolumeFactors(baseInput, productTypes);
    
    expect(factors[VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS]).toBe(1.0);
  });

  test('should handle all product types', () => {
    const baseInput = {
      length: 20,
      width: 5.125,
      depth: 12,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    
    const allProducts = Object.values(VOLUME_FACTOR_PRODUCTS);
    const factors = getMultipleVolumeFactors(baseInput, allProducts);
    
    allProducts.forEach(product => {
      expect(factors[product]).toBeGreaterThan(0);
      expect(factors[product]).toBeLessThanOrEqual(1.0);
    });
  });
});

describe('isDesignValueAffectedByVolume', () => {
  test('should return true for bending design value', () => {
    expect(isDesignValueAffectedByVolume(DESIGN_VALUE_TYPES.BENDING)).toBe(true);
  });

  test('should return false for other design values', () => {
    const unaffectedValues = [
      DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
      DESIGN_VALUE_TYPES.SHEAR,
      DESIGN_VALUE_TYPES.E,
      DESIGN_VALUE_TYPES.E_MIN,
      DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
    ];

    unaffectedValues.forEach(designValue => {
      expect(isDesignValueAffectedByVolume(designValue)).toBe(false);
    });
  });
});

describe('getVolumeFactorOptimization', () => {
  test('should provide optimization recommendations for glulam', () => {
    const optimization = getVolumeFactorOptimization(
      30, // Longer than reference (21 ft)
      VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
      0.95
    );
    
    expect(optimization.targetVolumeFactor).toBe(0.95);
    expect(optimization.productType).toBe(VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS);
    expect(optimization.referenceDimensions.length).toBe(21);
    expect(optimization.lengthEffect).toBeLessThan(1.0);
    expect(optimization.recommendations.length).toBeGreaterThan(0);
    
    // For a 30' span vs 21' reference with power factor 10, the effect is relatively small
    // Length ratio = 21/30 = 0.7, so lengthEffect = 0.7^(1/10) ≈ 0.965
    // Since 0.965 > 0.95 target, it should give favorable message
    if (optimization.lengthEffect >= optimization.targetVolumeFactor) {
      expect(optimization.recommendations[0]).toContain('favorable');
    } else {
      expect(optimization.recommendations[0]).toContain('shorter spans');
    }
  });

  test('should handle favorable length conditions', () => {
    const optimization = getVolumeFactorOptimization(
      15, // Shorter than reference (21 ft)
      VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
      0.95
    );
    
    expect(optimization.lengthEffect).toBeGreaterThan(1.0);
    expect(optimization.recommendations.some(r => r.includes('favorable'))).toBe(true);
  });

  test('should handle sawn lumber appropriately', () => {
    const optimization = getVolumeFactorOptimization(
      30,
      VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER,
      0.95
    );
    
    expect(optimization.recommendations.some(r => r.includes('does not apply'))).toBe(true);
  });

  test('should handle different product types', () => {
    const products = [
      VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
      VOLUME_FACTOR_PRODUCTS.LVL,
      VOLUME_FACTOR_PRODUCTS.PSL,
    ];

    products.forEach(product => {
      expect(() => getVolumeFactorOptimization(20, product, 0.95)).not.toThrow();
      const optimization = getVolumeFactorOptimization(20, product, 0.95);
      expect(optimization.productType).toBe(product);
      expect(optimization.referenceDimensions).toBeDefined();
      expect(optimization.lengthEffect).toBeGreaterThan(0);
    });
  });

  test('should throw error for undefined product type', () => {
    expect(() => {
      getVolumeFactorOptimization(20, 'invalid' as any, 0.95);
    }).toThrow();
  });
});

describe('Edge Cases and Integration Tests', () => {
  test('should handle extreme member sizes', () => {
    const extremeTests = [
      { length: 100, width: 20, depth: 40, name: 'very_large' },
      { length: 5, width: 1, depth: 2, name: 'very_small' },
      { length: 21, width: 5.125, depth: 12, name: 'reference_size' },
    ];

    extremeTests.forEach(test => {
      const input: VolumeFactorInput = {
        length: test.length,
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
      };
      
      const validation = validateVolumeFactorInput(input);
      if (validation.isValid) {
        const factor = getVolumeFactor(input);
        expect(typeof factor).toBe('number');
        expect(factor).toBeGreaterThan(0);
        expect(factor).toBeLessThanOrEqual(1.0);
      }
    });
  });

  test('should provide consistent results across multiple calls', () => {
    const input: VolumeFactorInput = {
      length: 25,
      width: 6.75,
      depth: 15,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const results = Array(10).fill(null).map(() => ({
      volumeFactor: getVolumeFactor(input),
      adjustedValue: applyVolumeFactor(2400, input),
      analysis: getVolumeFactorAnalysis(input),
    }));

    // All results should be identical
    results.forEach(result => {
      expect(result.volumeFactor).toBe(results[0].volumeFactor);
      expect(result.adjustedValue).toBe(results[0].adjustedValue);
      expect(result.analysis.calculation.volumeFactor).toBe(results[0].analysis.calculation.volumeFactor);
    });
  });

  test('should maintain mathematical relationships', () => {
    const input: VolumeFactorInput = {
      length: 30,
      width: 8.75,
      depth: 18,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const volumeFactor = getVolumeFactor(input);
    const referenceValue = 2400;
    const adjustedValue = applyVolumeFactor(referenceValue, input);
    const expectedValue = referenceValue * volumeFactor;
    
    expect(adjustedValue).toBeCloseTo(expectedValue, 6);
  });

  test('should handle all power factors correctly', () => {
    const testCases = [
      { product: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS, expectedPower: 10 },
      { product: VOLUME_FACTOR_PRODUCTS.GLULAM_AXIALLY_LOADED, expectedPower: 20 },
      { product: VOLUME_FACTOR_PRODUCTS.LVL, expectedPower: 20 },
      { product: VOLUME_FACTOR_PRODUCTS.PSL, expectedPower: 20 },
    ];

    testCases.forEach(testCase => {
      const input: VolumeFactorInput = {
        length: 30,
        width: 8,
        depth: 16,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType: testCase.product,
      };

      const calculation = calculateVolumeFactorDetails(input);
      expect(calculation.powerFactor).toBe(testCase.expectedPower);
    });
  });

  test('should apply limits correctly', () => {
    // Test small member that would give CV > 1.0
    const smallInput: VolumeFactorInput = {
      length: 10,     // Half reference length
      width: 2.5,     // Half reference breadth
      depth: 6,       // Half reference depth
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const factor = getVolumeFactor(smallInput);
    expect(factor).toBe(1.0); // Should be limited to 1.0

    // Test large member that should give CV < 1.0
    const largeInput: VolumeFactorInput = {
      length: 60,     // Much larger than reference
      width: 15,
      depth: 30,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };

    const largeFactor = getVolumeFactor(largeInput);
    expect(largeFactor).toBeLessThan(1.0);
    expect(largeFactor).toBeGreaterThan(0.5); // Should not be too small
  });
}); 