/**
 * Volume Factor Usage Examples
 *
 * This file demonstrates how to use the volume factor functionality for
 * various engineered wood products in structural design.
 *
 * @fileoverview Examples of volume factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */
import { getVolumeFactor, applyVolumeFactor, getVolumeFactorAnalysis, getMultipleVolumeFactors, getVolumeFactorOptimization, isDesignValueAffectedByVolume, VOLUME_FACTOR_PRODUCTS, } from './volume-factor.js';
import { DESIGN_VALUE_TYPES } from '../constants.js';
/**
 * Example 1: Basic volume factor calculation for glulam beam
 */
function example1_BasicGlulamBeam() {
    console.log('\n=== Example 1: Glulam Beam Volume Factor ===');
    // Standard glulam beam
    const input = {
        length: 30,
        width: 5.125,
        depth: 18,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    const volumeFactor = getVolumeFactor(input);
    console.log(`Glulam beam: ${input.width}" × ${input.depth}" × ${input.length}'`);
    console.log(`Volume factor (CV): ${volumeFactor.toFixed(3)}`);
    // Apply to reference bending value
    const referenceFb = 2400; // psi
    const adjustedFb = applyVolumeFactor(referenceFb, input);
    console.log(`Reference Fb: ${referenceFb} psi`);
    console.log(`Adjusted Fb: ${adjustedFb.toFixed(0)} psi`);
    console.log(`Reduction: ${((referenceFb - adjustedFb) / referenceFb * 100).toFixed(1)}%`);
}
/**
 * Example 2: Comparing volume factors across different product types
 */
function example2_ProductTypeComparison() {
    console.log('\n=== Example 2: Product Type Comparison ===');
    const baseInput = {
        length: 24,
        width: 3.5,
        depth: 12,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    const productTypes = [
        VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
        VOLUME_FACTOR_PRODUCTS.LVL,
        VOLUME_FACTOR_PRODUCTS.PSL,
        VOLUME_FACTOR_PRODUCTS.LSL,
        VOLUME_FACTOR_PRODUCTS.SAWN_LUMBER,
    ];
    const factors = getMultipleVolumeFactors(baseInput, productTypes);
    console.log('Product Type\t\tVolume Factor');
    console.log('------------------------------------');
    Object.entries(factors).forEach(([product, factor]) => {
        const productName = product.replace(/_/g, ' ').toUpperCase();
        console.log(`${productName}\t${factor.toFixed(3)}`);
    });
}
/**
 * Example 3: Size effect demonstration
 */
function example3_SizeEffectDemonstration() {
    console.log('\n=== Example 3: Size Effect on Volume Factor ===');
    const sizes = [
        { name: 'Reference Size', length: 21, width: 5.125, depth: 12 },
        { name: 'Small Beam', length: 12, width: 3.5, depth: 9 },
        { name: 'Large Beam', length: 40, width: 8.75, depth: 24 },
        { name: 'Very Large', length: 60, width: 10.75, depth: 30 },
    ];
    console.log('Size\t\tLength\tWidth\tDepth\tVolume Factor');
    console.log('------------------------------------------------');
    sizes.forEach(size => {
        const input = {
            length: size.length,
            width: size.width,
            depth: size.depth,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
        };
        const factor = getVolumeFactor(input);
        console.log(`${size.name}\t${size.length}'\t${size.width}"\t${size.depth}"\t${factor.toFixed(3)}`);
    });
}
/**
 * Example 4: Comprehensive analysis for design decisions
 */
function example4_ComprehensiveAnalysis() {
    console.log('\n=== Example 4: Comprehensive Volume Factor Analysis ===');
    const input = {
        length: 35,
        width: 6.75,
        depth: 21,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
    };
    const analysis = getVolumeFactorAnalysis(input);
    console.log('Analysis Results:');
    console.log(`- Member: ${input.width}" × ${input.depth}" × ${input.length}'`);
    console.log(`- Product category: ${analysis.productCategory}`);
    console.log(`- Volume factor (CV): ${analysis.calculation.volumeFactor.toFixed(3)}`);
    console.log(`- Power factor: ${analysis.calculation.powerFactor}`);
    console.log(`- Volume ratio: ${analysis.calculation.volumeRatio.toFixed(3)}`);
    console.log(`- Length ratio: ${analysis.calculation.lengthRatio.toFixed(3)}`);
    console.log(`- Depth ratio: ${analysis.calculation.depthRatio.toFixed(3)}`);
    console.log(`- Breadth ratio: ${analysis.calculation.breadthRatio.toFixed(3)}`);
    console.log(`- Member volume: ${analysis.calculation.memberVolume.toFixed(0)} cubic inches`);
    console.log(`- Reference volume: ${analysis.calculation.referenceVolume.toFixed(0)} cubic inches`);
    console.log(`- Applicable to bending: ${analysis.applicableDesignValue}`);
    if (analysis.validation.warnings.length > 0) {
        console.log('Warnings:');
        analysis.validation.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
}
/**
 * Example 5: Power factor effect comparison
 */
function example5_PowerFactorComparison() {
    console.log('\n=== Example 5: Power Factor Effect Comparison ===');
    const testDimensions = {
        length: 30,
        width: 7,
        depth: 16,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
    };
    const products = [
        { name: 'Glulam Bending', type: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS, power: 10 },
        { name: 'Glulam Axial', type: VOLUME_FACTOR_PRODUCTS.GLULAM_AXIALLY_LOADED, power: 20 },
        { name: 'LVL', type: VOLUME_FACTOR_PRODUCTS.LVL, power: 20 },
        { name: 'PSL', type: VOLUME_FACTOR_PRODUCTS.PSL, power: 20 },
    ];
    console.log('Product\t\tPower Factor\tVolume Factor');
    console.log('------------------------------------------');
    products.forEach(product => {
        const input = {
            ...testDimensions,
            productType: product.type,
        };
        const factor = getVolumeFactor(input);
        console.log(`${product.name}\t${product.power}\t\t${factor.toFixed(3)}`);
    });
}
/**
 * Example 6: Optimization study for span length
 */
function example6_SpanOptimization() {
    console.log('\n=== Example 6: Span Length Optimization ===');
    const spans = [15, 20, 25, 30, 35, 40, 45, 50];
    const memberSize = { width: 5.125, depth: 18 };
    console.log('Span (ft)\tVolume Factor\tFb Reduction (%)');
    console.log('--------------------------------------------');
    spans.forEach(span => {
        const input = {
            length: span,
            width: memberSize.width,
            depth: memberSize.depth,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
        };
        const factor = getVolumeFactor(input);
        const reduction = ((1.0 - factor) * 100);
        console.log(`${span}\t\t${factor.toFixed(3)}\t\t${reduction.toFixed(1)}`);
    });
}
/**
 * Example 7: Design value applicability check
 */
function example7_DesignValueApplicability() {
    console.log('\n=== Example 7: Design Value Applicability ===');
    const designValues = [
        { name: 'Bending (Fb)', type: DESIGN_VALUE_TYPES.BENDING },
        { name: 'Tension Parallel (Ft)', type: DESIGN_VALUE_TYPES.TENSION_PARALLEL },
        { name: 'Compression Parallel (Fc)', type: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL },
        { name: 'Shear (Fv)', type: DESIGN_VALUE_TYPES.SHEAR },
        { name: 'Modulus of Elasticity (E)', type: DESIGN_VALUE_TYPES.E },
    ];
    console.log('Design Value\t\tVolume Factor Applies');
    console.log('--------------------------------------------');
    designValues.forEach(dv => {
        const applies = isDesignValueAffectedByVolume(dv.type);
        console.log(`${dv.name}\t${applies ? 'Yes' : 'No'}`);
    });
}
/**
 * Example 8: Optimization recommendations
 */
function example8_OptimizationRecommendations() {
    console.log('\n=== Example 8: Optimization Recommendations ===');
    const testCases = [
        { length: 25, description: 'Medium span' },
        { length: 40, description: 'Long span' },
        { length: 15, description: 'Short span' },
    ];
    testCases.forEach(testCase => {
        console.log(`\n${testCase.description} (${testCase.length}'):`);
        const optimization = getVolumeFactorOptimization(testCase.length, VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS, 0.95);
        console.log(`Length effect: ${optimization.lengthEffect.toFixed(3)}`);
        console.log('Recommendations:');
        optimization.recommendations.forEach(rec => console.log(`  - ${rec}`));
    });
}
/**
 * Example 9: Practical moment capacity comparison
 */
function example9_PracticalCapacityComparison() {
    console.log('\n=== Example 9: Practical Moment Capacity Comparison ===');
    const members = [
        { name: '5-1/8" × 12" × 20\'', width: 5.125, depth: 12, length: 20 },
        { name: '5-1/8" × 18" × 30\'', width: 5.125, depth: 18, length: 30 },
        { name: '8-3/4" × 24" × 40\'', width: 8.75, depth: 24, length: 40 },
    ];
    const referenceFb = 2400; // psi
    console.log('Member\t\t\tCV\tAdjusted Fb\tMoment Capacity');
    console.log('------------------------------------------------------------');
    members.forEach(member => {
        const input = {
            length: member.length,
            width: member.width,
            depth: member.depth,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
        };
        const volumeFactor = getVolumeFactor(input);
        const adjustedFb = applyVolumeFactor(referenceFb, input);
        // Section modulus calculation
        const sectionModulus = (member.width * Math.pow(member.depth, 2)) / 6;
        const momentCapacity = (adjustedFb * sectionModulus) / 12000; // kip-ft
        console.log(`${member.name}\t${volumeFactor.toFixed(3)}\t${adjustedFb.toFixed(0)} psi\t${momentCapacity.toFixed(1)} kip-ft`);
    });
}
/**
 * Example 10: LVL vs Glulam comparison
 */
function example10_LVLvsGlulamComparison() {
    console.log('\n=== Example 10: LVL vs Glulam Volume Factor Comparison ===');
    const testSizes = [
        { width: 1.75, depth: 12, length: 20 },
        { width: 3.5, depth: 16, length: 30 },
        { width: 5.25, depth: 20, length: 40 },
    ];
    console.log('Size\t\t\tGlulam CV\tLVL CV\t\tDifference');
    console.log('--------------------------------------------------------');
    testSizes.forEach(size => {
        const glulamInput = {
            ...size,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            productType: VOLUME_FACTOR_PRODUCTS.GLULAM_BENDING_MEMBERS,
        };
        const lvlInput = {
            ...size,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            productType: VOLUME_FACTOR_PRODUCTS.LVL,
        };
        const glulamCV = getVolumeFactor(glulamInput);
        const lvlCV = getVolumeFactor(lvlInput);
        const difference = ((lvlCV - glulamCV) / glulamCV * 100);
        console.log(`${size.width}" × ${size.depth}" × ${size.length}'\t${glulamCV.toFixed(3)}\t\t${lvlCV.toFixed(3)}\t\t${difference.toFixed(1)}%`);
    });
}
/**
 * Run all examples
 */
function runAllExamples() {
    console.log('VOLUME FACTOR CALCULATION EXAMPLES');
    console.log('==================================');
    example1_BasicGlulamBeam();
    example2_ProductTypeComparison();
    example3_SizeEffectDemonstration();
    example4_ComprehensiveAnalysis();
    example5_PowerFactorComparison();
    example6_SpanOptimization();
    example7_DesignValueApplicability();
    example8_OptimizationRecommendations();
    example9_PracticalCapacityComparison();
    example10_LVLvsGlulamComparison();
    console.log('\n=== Examples Complete ===');
}
// Export examples for use in other modules
export { example1_BasicGlulamBeam, example2_ProductTypeComparison, example3_SizeEffectDemonstration, example4_ComprehensiveAnalysis, example5_PowerFactorComparison, example6_SpanOptimization, example7_DesignValueApplicability, example8_OptimizationRecommendations, example9_PracticalCapacityComparison, example10_LVLvsGlulamComparison, runAllExamples, };
// Run examples if this module is executed directly
if (require.main === module) {
    runAllExamples();
}
