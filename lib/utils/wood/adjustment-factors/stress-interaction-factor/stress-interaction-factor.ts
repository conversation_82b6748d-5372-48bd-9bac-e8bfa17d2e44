/**
 * Stress Interaction Factor (CI) Calculations
 * 
 * Implements NDS 5.3.9 Stress Interaction Factor for adjusting glulam bending design values
 * based on taper configurations for tapered glued laminated timber members.
 * 
 * Based on:
 * - NDS Section 5.3.9: Stress Interaction Factor for tapered glulam members
 * - NDS equations for compression and tension face tapers
 * - Applicability: Tapered glulam members only, straight members CI = 1.0
 * - Different calculations for compression face vs tension face tapers
 * 
 * @fileoverview Stress interaction factor calculations for glulam structural design
 * <AUTHOR> Engineering Application  
 * @version 1.0.0
 * @since 2024
 * 
 * @references
 * - NDS 2018 Section 5.3.9: Stress Interaction Factor
 * - NDS equations for tapered glulam members
 * - Stress interaction factor applies to bending design values (Fb) only for tapered members
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  GLULAM_STRESS_INTERACTION_CONSTANTS,
  GLULAM_TAPER_LOCATIONS,
  MATHEMATICAL_CONSTANTS,
  type GlulamTaperLocation,
} from "../../constants";

/**
 * Taper configuration types for stress interaction factor
 */
export const STRESS_INTERACTION_MEMBER_TYPES = {
  STRAIGHT: 'straight',
  TAPERED_COMPRESSION: 'tapered_compression',
  TAPERED_TENSION: 'tapered_tension',
  DOUBLE_TAPERED: 'double_tapered',
} as const;

export type StressInteractionMemberType = typeof STRESS_INTERACTION_MEMBER_TYPES[keyof typeof STRESS_INTERACTION_MEMBER_TYPES];

/**
 * Taper severity classifications
 */
export const TAPER_SEVERITY_LEVELS = {
  NONE: 'none',           // No taper (straight member)
  LIGHT: 'light',         // Small taper angle (< 2°)
  MODERATE: 'moderate',   // Moderate taper angle (2° - 5°)
  STEEP: 'steep',         // Large taper angle (> 5°)
} as const;

export type TaperSeverityLevel = typeof TAPER_SEVERITY_LEVELS[keyof typeof TAPER_SEVERITY_LEVELS];

/**
 * Input parameters for stress interaction factor calculation
 */
export interface StressInteractionFactorInput {
  /** Angle of taper in degrees (θ) */
  taperAngle: number;
  
  /** Location of taper (compression or tension face) */
  taperLocation: GlulamTaperLocation;
  
  /** Tensile stress parallel to grain (psi) */
  tensileStress: number;
  
  /** Compressive stress parallel to grain (psi) */
  compressiveStress: number;
  
  /** Type of design value (must be Fb for stress interaction factor) */
  designValueType: DesignValueType;
  
  /** Member configuration type */
  memberType?: StressInteractionMemberType;
}

/**
 * Validation result for stress interaction factor input
 */
export interface StressInteractionFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Detailed stress interaction factor calculation results
 */
export interface StressInteractionFactorCalculation {
  /** Calculated stress interaction factor */
  stressInteractionFactor: number;
  
  /** Taper angle in radians */
  taperAngleRadians: number;
  
  /** Taper location used in calculation */
  taperLocation: GlulamTaperLocation;
  
  /** Stress ratio terms used in calculation */
  stressRatios: {
    tensileStressRatio: number;
    tangentRatio: number;
    denominatorValue: number;
  };
  
  /** Whether member has significant taper */
  hasSignificantTaper: boolean;
  
  /** Taper severity classification */
  taperSeverity: TaperSeverityLevel;
  
  /** Member type classification */
  memberType: StressInteractionMemberType;
}

/**
 * Complete stress interaction factor analysis results
 */
export interface StressInteractionFactorAnalysis {
  input: StressInteractionFactorInput;
  calculation: StressInteractionFactorCalculation;
  applicableDesignValue: boolean;
  memberCategory: string;
  validation: StressInteractionFactorValidation;
}

/**
 * Stress interaction factor constants and specifications
 */
export const STRESS_INTERACTION_FACTOR_CONSTANTS = {
  // Default values
  NON_TAPERED_FACTOR: GLULAM_STRESS_INTERACTION_CONSTANTS.NON_TAPERED_FACTOR,
  DEFAULT_FACTOR: GLULAM_STRESS_INTERACTION_CONSTANTS.DEFAULT_FACTOR,
  
  // Taper angle thresholds (degrees)
  SIGNIFICANT_TAPER_THRESHOLD: 0.5,   // Below this is considered negligible
  LIGHT_TAPER_THRESHOLD: 2.0,         // Light taper classification
  MODERATE_TAPER_THRESHOLD: 5.0,      // Moderate taper classification
  MAX_PRACTICAL_TAPER_ANGLE: 15.0,    // Maximum practical taper angle
  
  // Stress ratio limits
  MIN_STRESS_VALUE: 1.0,              // Minimum stress for calculations (psi)
  MAX_STRESS_VALUE: 10000.0,          // Maximum practical stress (psi)
  MAX_STRESS_RATIO: 10.0,             // Maximum practical stress ratio
  
  // Mathematical constants for calculation
  BASE_DENOMINATOR_TERM: 4,           // Base term in denominator (NDS 5.3.9)
  
  // Validation limits
  MIN_TAPER_ANGLE: 0.0,               // Minimum taper angle (degrees)
  MAX_TAPER_ANGLE: 30.0,              // Maximum allowable taper angle (degrees)
} as const;

/**
 * Validate stress interaction factor input parameters
 * @param input - Stress interaction factor input parameters
 * @returns Validation result with errors and warnings
 */
export function validateStressInteractionFactorInput(
  input: StressInteractionFactorInput
): StressInteractionFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate taper angle
  if (typeof input.taperAngle !== 'number' || isNaN(input.taperAngle)) {
    errors.push('Taper angle must be a valid number');
  } else {
    if (input.taperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.MIN_TAPER_ANGLE) {
      errors.push('Taper angle must be non-negative');
    } else if (input.taperAngle > STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_TAPER_ANGLE) {
      warnings.push(`Taper angle ${input.taperAngle}° is very large (> ${STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_TAPER_ANGLE}°)`);
    } else if (input.taperAngle > STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_PRACTICAL_TAPER_ANGLE) {
      warnings.push(`Taper angle ${input.taperAngle}° exceeds typical practical limit (${STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_PRACTICAL_TAPER_ANGLE}°)`);
    } else if (input.taperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.SIGNIFICANT_TAPER_THRESHOLD) {
      warnings.push(`Taper angle ${input.taperAngle}° is very small and may not significantly affect stress interaction`);
    }
  }

  // Validate taper location
  if (!input.taperLocation) {
    errors.push('Taper location is required');
  } else if (!Object.values(GLULAM_TAPER_LOCATIONS).includes(input.taperLocation)) {
    errors.push(`Invalid taper location: ${input.taperLocation}`);
  }

  // Validate tensile stress
  if (typeof input.tensileStress !== 'number' || isNaN(input.tensileStress)) {
    errors.push('Tensile stress must be a valid number');
  } else {
    if (input.tensileStress < 0) {
      errors.push('Tensile stress must be non-negative');
    } else if (input.tensileStress < STRESS_INTERACTION_FACTOR_CONSTANTS.MIN_STRESS_VALUE) {
      warnings.push(`Tensile stress ${input.tensileStress} psi is very low`);
    } else if (input.tensileStress > STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_STRESS_VALUE) {
      warnings.push(`Tensile stress ${input.tensileStress} psi is very high (> ${STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_STRESS_VALUE} psi)`);
    }
  }

  // Validate compressive stress
  if (typeof input.compressiveStress !== 'number' || isNaN(input.compressiveStress)) {
    errors.push('Compressive stress must be a valid number');
  } else {
    if (input.compressiveStress < 0) {
      errors.push('Compressive stress must be non-negative');
    } else if (input.compressiveStress < STRESS_INTERACTION_FACTOR_CONSTANTS.MIN_STRESS_VALUE) {
      warnings.push(`Compressive stress ${input.compressiveStress} psi is very low`);
    } else if (input.compressiveStress > STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_STRESS_VALUE) {
      warnings.push(`Compressive stress ${input.compressiveStress} psi is very high (> ${STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_STRESS_VALUE} psi)`);
    }
  }

  // Validate design value type
  if (!input.designValueType) {
    errors.push('Design value type is required');
  } else if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
    errors.push(`Stress interaction factor only applies to bending design value (Fb), got: ${input.designValueType}`);
  }

  // Validate member type if provided
  if (input.memberType && !Object.values(STRESS_INTERACTION_MEMBER_TYPES).includes(input.memberType)) {
    warnings.push(`Unknown member type: ${input.memberType}`);
  }

  // Check stress ratio reasonableness
  if (errors.length === 0) {
    if (input.compressiveStress > 0) {
      const stressRatio = input.tensileStress / input.compressiveStress;
      if (stressRatio > STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_STRESS_RATIO) {
        warnings.push(`Tensile to compressive stress ratio ${stressRatio.toFixed(2)} is very high`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Classify taper severity based on angle
 * @param taperAngle - Taper angle in degrees
 * @returns Taper severity classification
 */
export function classifyTaperSeverity(taperAngle: number): TaperSeverityLevel {
  if (taperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.SIGNIFICANT_TAPER_THRESHOLD) {
    return TAPER_SEVERITY_LEVELS.NONE;
  } else if (taperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.LIGHT_TAPER_THRESHOLD) {
    return TAPER_SEVERITY_LEVELS.LIGHT;
  } else if (taperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.MODERATE_TAPER_THRESHOLD) {
    return TAPER_SEVERITY_LEVELS.MODERATE;
  } else {
    return TAPER_SEVERITY_LEVELS.STEEP;
  }
}

/**
 * Determine member type based on taper configuration
 * @param taperAngle - Taper angle in degrees
 * @param taperLocation - Location of taper
 * @returns Member type classification
 */
export function determineMemberType(
  taperAngle: number,
  taperLocation: GlulamTaperLocation
): StressInteractionMemberType {
  if (taperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.SIGNIFICANT_TAPER_THRESHOLD) {
    return STRESS_INTERACTION_MEMBER_TYPES.STRAIGHT;
  } else if (taperLocation === GLULAM_TAPER_LOCATIONS.COMPRESSION) {
    return STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION;
  } else if (taperLocation === GLULAM_TAPER_LOCATIONS.TENSION) {
    return STRESS_INTERACTION_MEMBER_TYPES.TAPERED_TENSION;
  } else {
    return STRESS_INTERACTION_MEMBER_TYPES.STRAIGHT;
  }
}

/**
 * Calculate detailed stress interaction factor results
 * @param input - Stress interaction factor input parameters
 * @returns Detailed calculation results
 */
export function calculateStressInteractionFactorDetails(
  input: StressInteractionFactorInput
): StressInteractionFactorCalculation {
  // Validate input first
  const validation = validateStressInteractionFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input for stress interaction factor calculation: ${validation.errors.join(', ')}`);
  }

  // Determine member configuration
  const memberType = input.memberType || determineMemberType(input.taperAngle, input.taperLocation);
  const taperSeverity = classifyTaperSeverity(input.taperAngle);
  const hasSignificantTaper = input.taperAngle >= STRESS_INTERACTION_FACTOR_CONSTANTS.SIGNIFICANT_TAPER_THRESHOLD;
  
  // For non-tapered or negligible taper members, return 1.0
  if (!hasSignificantTaper || memberType === STRESS_INTERACTION_MEMBER_TYPES.STRAIGHT) {
    return {
      stressInteractionFactor: STRESS_INTERACTION_FACTOR_CONSTANTS.NON_TAPERED_FACTOR,
      taperAngleRadians: 0,
      taperLocation: input.taperLocation,
      stressRatios: {
        tensileStressRatio: 0,
        tangentRatio: 0,
        denominatorValue: STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM,
      },
      hasSignificantTaper: false,
      taperSeverity,
      memberType,
    };
  }

  // Convert angle to radians
  const taperAngleRadians = input.taperAngle * MATHEMATICAL_CONSTANTS.DEGREES_TO_RADIANS;
  const tanTheta = Math.tan(taperAngleRadians);

  let stressInteractionFactor: number;
  let tensileStressRatio: number;
  let tangentRatio: number;
  let denominatorValue: number;

  // NDS 5.3.9 - Calculate stress interaction factor based on taper location
  if (input.taperLocation === GLULAM_TAPER_LOCATIONS.COMPRESSION && input.compressiveStress > 0) {
    // For members tapered on the compression face (NDS 5.3.4)
    // CI = 1 / sqrt(4 + (Ft * tan(θ) / Fc)² + (tan(θ) / Fc)²)
    tensileStressRatio = (input.tensileStress * tanTheta) / input.compressiveStress;
    tangentRatio = tanTheta / input.compressiveStress;
    
    denominatorValue = Math.sqrt(
      STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM +
      Math.pow(tensileStressRatio, 2) +
      Math.pow(tangentRatio, 2)
    );
    
    stressInteractionFactor = 1 / denominatorValue;
    
  } else if (input.taperLocation === GLULAM_TAPER_LOCATIONS.TENSION && input.tensileStress > 0) {
    // For members tapered on the tension face (NDS 5.3.5)  
    // CI = 1 / sqrt(4 + (Ft * tan(θ) / Ft)² + (tan(θ) / Ft)²)
    // Note: This simplifies since Ft appears in both numerator and denominator
    tensileStressRatio = tanTheta; // Ft cancels out in the ratio
    tangentRatio = tanTheta / input.tensileStress;
    
    denominatorValue = Math.sqrt(
      STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM +
      Math.pow(tensileStressRatio, 2) +
      Math.pow(tangentRatio, 2)
    );
    
    stressInteractionFactor = 1 / denominatorValue;
    
  } else {
    // Default to 1.0 if insufficient data or invalid configuration
    stressInteractionFactor = STRESS_INTERACTION_FACTOR_CONSTANTS.DEFAULT_FACTOR;
    tensileStressRatio = 0;
    tangentRatio = 0;
    denominatorValue = STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM;
  }

  return {
    stressInteractionFactor,
    taperAngleRadians,
    taperLocation: input.taperLocation,
    stressRatios: {
      tensileStressRatio,
      tangentRatio,
      denominatorValue,
    },
    hasSignificantTaper,
    taperSeverity,
    memberType,
  };
}

/**
 * Calculate stress interaction factor for tapered glulam members
 * @param input - Stress interaction factor input parameters
 * @returns Stress interaction factor CI
 * @throws Error if input is invalid
 * 
 * @example
 * ```typescript
 * const CI = getStressInteractionFactor({
 *   taperAngle: 5,
 *   taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
 *   tensileStress: 1000,
 *   compressiveStress: 1500,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING
 * });
 * console.log(`Stress interaction factor: ${CI}`);
 * ```
 */
export function getStressInteractionFactor(input: StressInteractionFactorInput): number {
  const calculation = calculateStressInteractionFactorDetails(input);
  return calculation.stressInteractionFactor;
}

/**
 * Calculate stress interaction factor for straight members (convenience function)
 * @returns Stress interaction factor of 1.0 for straight members
 */
export function getStressInteractionFactorForStraightMember(): number {
  return STRESS_INTERACTION_FACTOR_CONSTANTS.NON_TAPERED_FACTOR;
}

/**
 * Apply stress interaction factor to a reference design value
 * @param referenceValue - Reference bending design value (psi)
 * @param input - Stress interaction factor input parameters
 * @returns Adjusted design value
 */
export function applyStressInteractionFactor(
  referenceValue: number,
  input: StressInteractionFactorInput
): number {
  if (typeof referenceValue !== 'number' || isNaN(referenceValue) || referenceValue < 0) {
    throw new Error('Reference value must be a non-negative number');
  }
  
  const stressInteractionFactor = getStressInteractionFactor(input);
  return referenceValue * stressInteractionFactor;
}

/**
 * Get comprehensive stress interaction factor analysis
 * @param input - Stress interaction factor input parameters
 * @returns Complete analysis results
 */
export function getStressInteractionFactorAnalysis(
  input: StressInteractionFactorInput
): StressInteractionFactorAnalysis {
  const validation = validateStressInteractionFactorInput(input);
  
  let calculation: StressInteractionFactorCalculation;
  try {
    calculation = calculateStressInteractionFactorDetails(input);
  } catch (error) {
    // If calculation fails, provide default values
    calculation = {
      stressInteractionFactor: STRESS_INTERACTION_FACTOR_CONSTANTS.DEFAULT_FACTOR,
      taperAngleRadians: 0,
      taperLocation: input.taperLocation || GLULAM_TAPER_LOCATIONS.COMPRESSION,
      stressRatios: {
        tensileStressRatio: 0,
        tangentRatio: 0,
        denominatorValue: STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM,
      },
      hasSignificantTaper: false,
      taperSeverity: TAPER_SEVERITY_LEVELS.NONE,
      memberType: STRESS_INTERACTION_MEMBER_TYPES.STRAIGHT,
    };
  }
  
  const applicableDesignValue = input.designValueType === DESIGN_VALUE_TYPES.BENDING;
  const memberCategory = calculation.memberType;
  
  return {
    input,
    calculation,
    applicableDesignValue,
    memberCategory,
    validation,
  };
}

/**
 * Get stress interaction factors for multiple scenarios
 * @param baseInput - Base input parameters (without taper variations)
 * @param scenarios - Array of taper configurations to evaluate
 * @returns Record of scenario names to stress interaction factors
 */
export function getMultipleStressInteractionFactors(
  baseInput: Omit<StressInteractionFactorInput, 'taperAngle' | 'taperLocation'>,
  scenarios: Array<{
    name: string;
    taperAngle: number;
    taperLocation: GlulamTaperLocation;
  }>
): Record<string, number> {
  const results: Record<string, number> = {};
  
  for (const scenario of scenarios) {
    try {
      const input: StressInteractionFactorInput = {
        ...baseInput,
        taperAngle: scenario.taperAngle,
        taperLocation: scenario.taperLocation,
      };
      results[scenario.name] = getStressInteractionFactor(input);
    } catch (error) {
      results[scenario.name] = NaN; // Indicate invalid scenario
    }
  }
  
  return results;
}

/**
 * Check if design value is affected by stress interaction factor
 * @param designValueType - Type of design value
 * @returns True if stress interaction factor applies
 */
export function isDesignValueAffectedByStressInteraction(
  designValueType: DesignValueType
): boolean {
  return designValueType === DESIGN_VALUE_TYPES.BENDING;
}

/**
 * Get stress interaction factor optimization recommendations
 * @param targetStressInteractionFactor - Desired stress interaction factor (typically close to 1.0)
 * @param tensileStress - Tensile stress parallel to grain (psi)
 * @param compressiveStress - Compressive stress parallel to grain (psi)
 * @param taperLocation - Location of taper
 * @returns Optimization recommendations
 */
export function getStressInteractionFactorOptimization(
  targetStressInteractionFactor: number = 0.95,
  tensileStress: number,
  compressiveStress: number,
  taperLocation: GlulamTaperLocation
): {
  targetStressInteractionFactor: number;
  maximumTaperAngle: number;
  taperLocation: GlulamTaperLocation;
  recommendations: string[];
} {
  const recommendations: string[] = [];
  
  // Calculate maximum taper angle for target factor
  // CI = 1 / sqrt(4 + stress_terms), so sqrt(4 + stress_terms) = 1/CI
  // stress_terms = (1/CI)² - 4
  const targetDenominator = 1 / targetStressInteractionFactor;
  const maxStressTerms = Math.pow(targetDenominator, 2) - STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM;
  
  let maximumTaperAngle: number;
  
  if (maxStressTerms <= 0) {
    // Target factor is too high, essentially no taper allowed
    maximumTaperAngle = 0;
    recommendations.push(`Target stress interaction factor ${targetStressInteractionFactor} is too high for tapered members`);
    recommendations.push(`Consider straight member design or lower target factor`);
  } else {
    // Estimate maximum taper angle (simplified calculation)
    if (taperLocation === GLULAM_TAPER_LOCATIONS.COMPRESSION && compressiveStress > 0) {
      // For compression taper: stress_terms ≈ (Ft * tan(θ) / Fc)² + (tan(θ) / Fc)²
      // Simplified: stress_terms ≈ tan(θ)² * (Ft² + 1) / Fc²
      const stressRatioFactor = (Math.pow(tensileStress, 2) + 1) / Math.pow(compressiveStress, 2);
      const maxTanSquared = maxStressTerms / stressRatioFactor;
      const maxTan = Math.sqrt(Math.max(0, maxTanSquared));
      maximumTaperAngle = Math.atan(maxTan) * MATHEMATICAL_CONSTANTS.RADIANS_TO_DEGREES;
    } else if (taperLocation === GLULAM_TAPER_LOCATIONS.TENSION && tensileStress > 0) {
      // For tension taper: similar calculation but different stress terms
      const stressRatioFactor = (1 + 1 / Math.pow(tensileStress, 2));
      const maxTanSquared = maxStressTerms / stressRatioFactor;
      const maxTan = Math.sqrt(Math.max(0, maxTanSquared));
      maximumTaperAngle = Math.atan(maxTan) * MATHEMATICAL_CONSTANTS.RADIANS_TO_DEGREES;
    } else {
      maximumTaperAngle = 0;
    }
    
    // Provide recommendations based on results
    if (maximumTaperAngle > STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_PRACTICAL_TAPER_ANGLE) {
      recommendations.push(`Maximum taper angle of ${maximumTaperAngle.toFixed(1)}° to achieve target factor, but practical limit is ${STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_PRACTICAL_TAPER_ANGLE}°`);
      maximumTaperAngle = STRESS_INTERACTION_FACTOR_CONSTANTS.MAX_PRACTICAL_TAPER_ANGLE;
    } else if (maximumTaperAngle > 0) {
      recommendations.push(`Maximum taper angle of ${maximumTaperAngle.toFixed(1)}° to achieve stress interaction factor of ${targetStressInteractionFactor}`);
    }
    
    if (maximumTaperAngle < STRESS_INTERACTION_FACTOR_CONSTANTS.SIGNIFICANT_TAPER_THRESHOLD) {
      recommendations.push(`Very small allowable taper angle suggests straight member may be more appropriate`);
    }
  }
  
  // General recommendations
  if (taperLocation === GLULAM_TAPER_LOCATIONS.COMPRESSION) {
    recommendations.push(`Compression face taper selected - consider design implications for buckling`);
    recommendations.push(`Higher compressive stress capacity reduces taper penalty`);
  } else {
    recommendations.push(`Tension face taper selected - consider stress concentrations at taper`);
    recommendations.push(`Higher tensile stress capacity affects taper allowance`);
  }
  
  recommendations.push(`Consider alternative member depths to reduce taper requirements`);
  recommendations.push(`Verify that tapered member design meets all other NDS requirements`);
  
  return {
    targetStressInteractionFactor,
    maximumTaperAngle: Math.max(0, maximumTaperAngle),
    taperLocation,
    recommendations,
  };
} 