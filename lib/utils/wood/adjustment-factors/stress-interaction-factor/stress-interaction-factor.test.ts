/**
 * Test suite for Stress Interaction Factor (CI) Calculations
 *
 * @fileoverview Unit tests for NDS 5.3.9 stress interaction factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  getStressInteractionFactor,
  getStressInteractionFactorForStraightMember,
  applyStressInteractionFactor,
  validateStressInteractionFactorInput,
  calculateStressInteractionFactorDetails,
  getStressInteractionFactorAnalysis,
  getMultipleStressInteractionFactors,
  isDesignValueAffectedByStressInteraction,
  getStressInteractionFactorOptimization,
  classifyTaperSeverity,
  determineMemberType,
  STRESS_INTERACTION_MEMBER_TYPES,
  TAPER_SEVERITY_LEVELS,
  STRESS_INTERACTION_FACTOR_CONSTANTS,
  type StressInteractionFactorInput,
  type StressInteractionMemberType,
  type TaperSeverityLevel,
} from "./stress-interaction-factor";

import { DESIGN_VALUE_TYPES, GLULAM_TAPER_LOCATIONS } from "../../constants";

describe("Stress Interaction Factor (CI) Calculations", () => {
  describe("validateStressInteractionFactorInput", () => {
    test("should validate correct input", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test("should reject invalid taper angle", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: -5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Taper angle must be non-negative");
    });

    test("should reject missing taper location", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: null as any,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Taper location is required");
    });

    test("should reject negative stresses", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: -100,
        compressiveStress: -200,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Tensile stress must be non-negative");
      expect(validation.errors).toContain("Compressive stress must be non-negative");
    });

    test("should reject non-bending design value types", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        "Stress interaction factor only applies to bending design value (Fb), got: Fv"
      );
    });

    test("should warn for very large taper angles", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 20,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.warnings.some(w => 
        w.includes("exceeds typical practical limit")
      )).toBe(true);
    });

    test("should warn for very small taper angles", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 0.1,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.warnings.some(w => 
        w.includes("very small and may not significantly affect")
      )).toBe(true);
    });

    test("should warn for very high stress ratio", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 5000,
        compressiveStress: 100, // Very high ratio
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateStressInteractionFactorInput(input);
      expect(validation.warnings.some(w => 
        w.includes("stress ratio") && w.includes("very high")
      )).toBe(true);
    });
  });

  describe("classifyTaperSeverity", () => {
    test("should classify no taper", () => {
      expect(classifyTaperSeverity(0.1)).toBe(TAPER_SEVERITY_LEVELS.NONE);
    });

    test("should classify light taper", () => {
      expect(classifyTaperSeverity(1.0)).toBe(TAPER_SEVERITY_LEVELS.LIGHT);
    });

    test("should classify moderate taper", () => {
      expect(classifyTaperSeverity(3.0)).toBe(TAPER_SEVERITY_LEVELS.MODERATE);
    });

    test("should classify steep taper", () => {
      expect(classifyTaperSeverity(10.0)).toBe(TAPER_SEVERITY_LEVELS.STEEP);
    });
  });

  describe("determineMemberType", () => {
    test("should determine straight member for negligible taper", () => {
      const memberType = determineMemberType(0.1, GLULAM_TAPER_LOCATIONS.COMPRESSION);
      expect(memberType).toBe(STRESS_INTERACTION_MEMBER_TYPES.STRAIGHT);
    });

    test("should determine tapered compression member", () => {
      const memberType = determineMemberType(5.0, GLULAM_TAPER_LOCATIONS.COMPRESSION);
      expect(memberType).toBe(STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION);
    });

    test("should determine tapered tension member", () => {
      const memberType = determineMemberType(5.0, GLULAM_TAPER_LOCATIONS.TENSION);
      expect(memberType).toBe(STRESS_INTERACTION_MEMBER_TYPES.TAPERED_TENSION);
    });
  });

  describe("calculateStressInteractionFactorDetails", () => {
    test("should calculate stress interaction factor for compression taper", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const calculation = calculateStressInteractionFactorDetails(input);

      expect(calculation.hasSignificantTaper).toBe(true);
      expect(calculation.taperSeverity).toBe(TAPER_SEVERITY_LEVELS.STEEP);
      expect(calculation.memberType).toBe(STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION);
      expect(calculation.stressInteractionFactor).toBeGreaterThan(0);
      expect(calculation.stressInteractionFactor).toBeLessThanOrEqual(1.0);
      expect(calculation.taperAngleRadians).toBeCloseTo(5 * Math.PI / 180, 4);
    });

    test("should calculate stress interaction factor for tension taper", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 3,
        taperLocation: GLULAM_TAPER_LOCATIONS.TENSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const calculation = calculateStressInteractionFactorDetails(input);

      expect(calculation.hasSignificantTaper).toBe(true);
      expect(calculation.taperSeverity).toBe(TAPER_SEVERITY_LEVELS.MODERATE);
      expect(calculation.memberType).toBe(STRESS_INTERACTION_MEMBER_TYPES.TAPERED_TENSION);
      expect(calculation.stressInteractionFactor).toBeGreaterThan(0);
      expect(calculation.stressInteractionFactor).toBeLessThanOrEqual(1.0);
    });

    test("should return 1.0 for negligible taper", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 0.1,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const calculation = calculateStressInteractionFactorDetails(input);

      expect(calculation.hasSignificantTaper).toBe(false);
      expect(calculation.stressInteractionFactor).toBe(1.0);
      expect(calculation.taperSeverity).toBe(TAPER_SEVERITY_LEVELS.NONE);
    });

    test("should handle zero compressive stress for compression taper", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 0,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const calculation = calculateStressInteractionFactorDetails(input);

      expect(calculation.stressInteractionFactor).toBe(1.0); // Default when insufficient data
    });

    test("should handle zero tensile stress for tension taper", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.TENSION,
        tensileStress: 0,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const calculation = calculateStressInteractionFactorDetails(input);

      expect(calculation.stressInteractionFactor).toBe(1.0); // Default when insufficient data
    });
  });

  describe("getStressInteractionFactor", () => {
    test("should calculate stress interaction factor for valid tapered member", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const CI = getStressInteractionFactor(input);
      
      expect(CI).toBeGreaterThan(0);
      expect(CI).toBeLessThanOrEqual(1.0);
    });

    test("should throw error for invalid input", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: -5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      expect(() => {
        getStressInteractionFactor(input);
      }).toThrow(/Invalid input for stress interaction factor calculation/);
    });

    test("should return 1.0 for straight member", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 0,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const CI = getStressInteractionFactor(input);
      expect(CI).toBe(1.0);
    });

    test("should produce smaller factors for larger taper angles", () => {
      const baseInput = {
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const CI_small = getStressInteractionFactor({ ...baseInput, taperAngle: 2 });
      const CI_large = getStressInteractionFactor({ ...baseInput, taperAngle: 8 });

      expect(CI_large).toBeLessThan(CI_small);
    });
  });

  describe("getStressInteractionFactorForStraightMember", () => {
    test("should always return 1.0", () => {
      const CI = getStressInteractionFactorForStraightMember();
      expect(CI).toBe(1.0);
    });
  });

  describe("applyStressInteractionFactor", () => {
    test("should apply stress interaction factor to reference value", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const referenceValue = 2400; // psi
      const adjustedValue = applyStressInteractionFactor(referenceValue, input);

      const CI = getStressInteractionFactor(input);
      const expectedAdjusted = referenceValue * CI;
      expect(adjustedValue).toBeCloseTo(expectedAdjusted, 1);
    });

    test("should reject invalid reference values", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      expect(() => {
        applyStressInteractionFactor(-100, input);
      }).toThrow("Reference value must be a non-negative number");

      expect(() => {
        applyStressInteractionFactor(NaN, input);
      }).toThrow("Reference value must be a non-negative number");
    });
  });

  describe("getStressInteractionFactorAnalysis", () => {
    test("should provide comprehensive analysis", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        memberType: STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION,
      };

      const analysis = getStressInteractionFactorAnalysis(input);

      expect(analysis.input).toEqual(input);
      expect(analysis.applicableDesignValue).toBe(true);
      expect(analysis.memberCategory).toBe(STRESS_INTERACTION_MEMBER_TYPES.TAPERED_COMPRESSION);
      expect(analysis.validation.isValid).toBe(true);
      expect(analysis.calculation.stressInteractionFactor).toBeGreaterThan(0);
      expect(analysis.calculation.stressInteractionFactor).toBeLessThanOrEqual(1.0);
    });

    test("should handle invalid input gracefully", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: -5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const analysis = getStressInteractionFactorAnalysis(input);

      expect(analysis.validation.isValid).toBe(false);
      expect(analysis.calculation.stressInteractionFactor).toBe(1.0); // Default fallback
    });

    test("should identify non-applicable design values", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const analysis = getStressInteractionFactorAnalysis(input);

      expect(analysis.applicableDesignValue).toBe(false);
    });
  });

  describe("getMultipleStressInteractionFactors", () => {
    test("should calculate factors for multiple scenarios", () => {
      const baseInput = {
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const scenarios = [
        { name: "Light Compression", taperAngle: 2, taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION },
        { name: "Moderate Compression", taperAngle: 5, taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION },
        { name: "Light Tension", taperAngle: 2, taperLocation: GLULAM_TAPER_LOCATIONS.TENSION },
        { name: "Moderate Tension", taperAngle: 5, taperLocation: GLULAM_TAPER_LOCATIONS.TENSION },
      ];

      const results = getMultipleStressInteractionFactors(baseInput, scenarios);

      expect(results["Light Compression"]).toBeGreaterThan(results["Moderate Compression"]);
      expect(results["Light Tension"]).toBeGreaterThan(results["Moderate Tension"]);
      
      // All should be valid numbers
      Object.values(results).forEach(factor => {
        expect(typeof factor).toBe("number");
        expect(factor).toBeGreaterThan(0);
        expect(factor).toBeLessThanOrEqual(1.0);
      });
    });

    test("should handle invalid scenarios", () => {
      const baseInput = {
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const scenarios = [
        { name: "Valid", taperAngle: 5, taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION },
        { name: "Invalid", taperAngle: -5, taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION },
      ];

      const results = getMultipleStressInteractionFactors(baseInput, scenarios);

      expect(typeof results["Valid"]).toBe("number");
      expect(results["Valid"]).toBeGreaterThan(0);
      expect(isNaN(results["Invalid"])).toBe(true);
    });
  });

  describe("isDesignValueAffectedByStressInteraction", () => {
    test("should return true for bending design values", () => {
      expect(isDesignValueAffectedByStressInteraction(DESIGN_VALUE_TYPES.BENDING)).toBe(true);
    });

    test("should return false for non-bending design values", () => {
      expect(isDesignValueAffectedByStressInteraction(DESIGN_VALUE_TYPES.SHEAR)).toBe(false);
      expect(isDesignValueAffectedByStressInteraction(DESIGN_VALUE_TYPES.TENSION_PARALLEL)).toBe(false);
      expect(isDesignValueAffectedByStressInteraction(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL)).toBe(false);
      expect(isDesignValueAffectedByStressInteraction(DESIGN_VALUE_TYPES.E)).toBe(false);
    });
  });

  describe("getStressInteractionFactorOptimization", () => {
    test("should provide optimization recommendations", () => {
      const optimization = getStressInteractionFactorOptimization(
        0.95,
        1000,
        1500,
        GLULAM_TAPER_LOCATIONS.COMPRESSION
      );

      expect(optimization.targetStressInteractionFactor).toBe(0.95);
      expect(optimization.maximumTaperAngle).toBeGreaterThanOrEqual(0);
      expect(optimization.taperLocation).toBe(GLULAM_TAPER_LOCATIONS.COMPRESSION);
      expect(optimization.recommendations).toBeInstanceOf(Array);
      expect(optimization.recommendations.length).toBeGreaterThan(0);
    });

    test("should handle very high target factors", () => {
      const optimization = getStressInteractionFactorOptimization(
        0.99,
        1000,
        1500,
        GLULAM_TAPER_LOCATIONS.COMPRESSION
      );

      // Very high target factor should result in very small allowable taper angle
      expect(optimization.maximumTaperAngle).toBeLessThan(2.0);
    });

    test("should handle different taper locations", () => {
      const compressionOptimization = getStressInteractionFactorOptimization(
        0.9,
        1000,
        1500,
        GLULAM_TAPER_LOCATIONS.COMPRESSION
      );

      const tensionOptimization = getStressInteractionFactorOptimization(
        0.9,
        1000,
        1500,
        GLULAM_TAPER_LOCATIONS.TENSION
      );

      expect(compressionOptimization.taperLocation).toBe(GLULAM_TAPER_LOCATIONS.COMPRESSION);
      expect(tensionOptimization.taperLocation).toBe(GLULAM_TAPER_LOCATIONS.TENSION);
      
      // Both should provide specific recommendations for their taper type
      expect(compressionOptimization.recommendations.some(r => r.includes("taper"))).toBe(true);
      expect(tensionOptimization.recommendations.some(r => r.includes("taper"))).toBe(true);
    });

    test("should handle impossible target factors", () => {
      const optimization = getStressInteractionFactorOptimization(
        1.01, // Impossible - greater than 1.0
        1000,
        1500,
        GLULAM_TAPER_LOCATIONS.COMPRESSION
      );

      expect(optimization.maximumTaperAngle).toBe(0);
      expect(optimization.recommendations.some(r => r.includes("too high"))).toBe(true);
    });
  });

  describe("Constants and Types", () => {
    test("should export required constants", () => {
      expect(STRESS_INTERACTION_MEMBER_TYPES).toBeDefined();
      expect(TAPER_SEVERITY_LEVELS).toBeDefined();
      expect(STRESS_INTERACTION_FACTOR_CONSTANTS).toBeDefined();
    });

    test("should have correct member type values", () => {
      expect(Object.values(STRESS_INTERACTION_MEMBER_TYPES)).toContain("straight");
      expect(Object.values(STRESS_INTERACTION_MEMBER_TYPES)).toContain("tapered_compression");
      expect(Object.values(STRESS_INTERACTION_MEMBER_TYPES)).toContain("tapered_tension");
      expect(Object.values(STRESS_INTERACTION_MEMBER_TYPES)).toContain("double_tapered");
    });

    test("should have correct severity level values", () => {
      expect(Object.values(TAPER_SEVERITY_LEVELS)).toContain("none");
      expect(Object.values(TAPER_SEVERITY_LEVELS)).toContain("light");
      expect(Object.values(TAPER_SEVERITY_LEVELS)).toContain("moderate");
      expect(Object.values(TAPER_SEVERITY_LEVELS)).toContain("steep");
    });

    test("should have reasonable constant values", () => {
      expect(STRESS_INTERACTION_FACTOR_CONSTANTS.NON_TAPERED_FACTOR).toBe(1.0);
      expect(STRESS_INTERACTION_FACTOR_CONSTANTS.DEFAULT_FACTOR).toBe(1.0);
      expect(STRESS_INTERACTION_FACTOR_CONSTANTS.BASE_DENOMINATOR_TERM).toBe(4);
      expect(STRESS_INTERACTION_FACTOR_CONSTANTS.SIGNIFICANT_TAPER_THRESHOLD).toBe(0.5);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle very large stress values", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 5000,
        compressiveStress: 8000,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const CI = getStressInteractionFactor(input);
      expect(typeof CI).toBe("number");
      expect(CI).toBeGreaterThan(0);
      expect(CI).toBeLessThanOrEqual(1.0);
    });

    test("should handle very small stress values", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1,
        compressiveStress: 1,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const CI = getStressInteractionFactor(input);
      expect(typeof CI).toBe("number");
      expect(CI).toBeGreaterThan(0);
      expect(CI).toBeLessThanOrEqual(1.0);
    });

    test("should handle floating point precision in calculations", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5.000001,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000.0001,
        compressiveStress: 1500.0001,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      expect(() => {
        getStressInteractionFactor(input);
      }).not.toThrow();
    });

    test("should handle missing optional parameters", () => {
      const input: StressInteractionFactorInput = {
        taperAngle: 5,
        taperLocation: GLULAM_TAPER_LOCATIONS.COMPRESSION,
        tensileStress: 1000,
        compressiveStress: 1500,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        // memberType omitted
      };

      const CI = getStressInteractionFactor(input);
      expect(typeof CI).toBe("number");
      expect(CI).toBeGreaterThan(0);
      expect(CI).toBeLessThanOrEqual(1.0);
    });
  });
}); 