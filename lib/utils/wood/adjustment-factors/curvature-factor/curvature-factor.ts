/**
 * Curvature Factor (Cc) Calculations
 * 
 * Implements NDS 5.3.8 Curvature Factor for adjusting glulam bending design values
 * based on member curvature for curved glued laminated timber members.
 * 
 * Based on:
 * - NDS Section 5.3.8: Curvature Factor for curved glulam members
 * - NDS equation: Cc = 1 - (2000)(t/R)²
 * - Applicability: Curved glulam members only, straight members Cc = 1.0
 * - Limitations: t/R ≤ 1/100 for hardwoods and Southern Pine, t/R ≤ 1/125 for other softwoods
 * 
 * @fileoverview Curvature factor calculations for glulam structural design
 * <AUTHOR> Engineering Application  
 * @version 1.0.0
 * @since 2024
 * 
 * @references
 * - NDS 2018 Section 5.3.8: Curvature Factor
 * - NDS equations for curved glulam members
 * - Curvature factor applies to bending design values (Fb) only for curved members
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
  GLULAM_CURVATURE_CONSTANTS,
  MATHEMATICAL_CONSTANTS,
} from "../../constants";

/**
 * Species groups for curvature ratio limits
 */
export const CURVATURE_SPECIES_GROUPS = {
  HARDWOOD_SOUTHERN_PINE: 'hardwood_southern_pine',
  OTHER_SOFTWOOD: 'other_softwood',
} as const;

export type CurvatureSpeciesGroup = typeof CURVATURE_SPECIES_GROUPS[keyof typeof CURVATURE_SPECIES_GROUPS];

/**
 * Member configuration types for curvature factor
 */
export const CURVATURE_MEMBER_TYPES = {
  STRAIGHT: 'straight',
  CURVED: 'curved',
  COMPOUND_CURVED: 'compound_curved',
} as const;

export type CurvatureMemberType = typeof CURVATURE_MEMBER_TYPES[keyof typeof CURVATURE_MEMBER_TYPES];

/**
 * Input parameters for curvature factor calculation
 */
export interface CurvatureFactorInput {
  /** Radius of curvature of inside face of member (inches) */
  radiusOfCurvature: number;
  
  /** Thickness of individual laminations (inches) */
  laminationThickness: number;
  
  /** Type of design value (must be Fb for curvature factor) */
  designValueType: DesignValueType;
  
  /** Species group for determining curvature ratio limits */
  speciesGroup?: CurvatureSpeciesGroup;
  
  /** Member configuration type */
  memberType?: CurvatureMemberType;
}

/**
 * Validation result for curvature factor input
 */
export interface CurvatureFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Detailed curvature factor calculation results
 */
export interface CurvatureFactorCalculation {
  /** Calculated curvature factor */
  curvatureFactor: number;
  
  /** Thickness to radius ratio (t/R) */
  thicknessToRadiusRatio: number;
  
  /** Maximum allowed curvature ratio for species */
  maxAllowableCurvatureRatio: number;
  
  /** Species group used for limits */
  speciesGroup: CurvatureSpeciesGroup;
  
  /** Whether member is within NDS limits */
  withinNdsLimits: boolean;
  
  /** Raw calculated value before minimum limit application */
  rawCalculatedFactor: number;
  
  /** Applied minimum factor limit */
  minimumFactorApplied: boolean;
}

/**
 * Complete curvature factor analysis results
 */
export interface CurvatureFactorAnalysis {
  input: CurvatureFactorInput;
  calculation: CurvatureFactorCalculation;
  applicableDesignValue: boolean;
  memberCategory: string;
  validation: CurvatureFactorValidation;
}

/**
 * Curvature factor constants and specifications
 */
export const CURVATURE_FACTOR_CONSTANTS = {
  // Curvature ratio limits by species group (NDS 5.3.8)
  MAX_CURVATURE_RATIOS: {
    [CURVATURE_SPECIES_GROUPS.HARDWOOD_SOUTHERN_PINE]: GLULAM_CURVATURE_CONSTANTS.MAX_CURVATURE_RATIO_HARDWOOD_SOUTHERN_PINE,
    [CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD]: GLULAM_CURVATURE_CONSTANTS.MAX_CURVATURE_RATIO_OTHER_SOFTWOOD,
  },
  
  // Default values
  DEFAULT_SPECIES_GROUP: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
  DEFAULT_MEMBER_TYPE: CURVATURE_MEMBER_TYPES.STRAIGHT,
  
  // Calculation parameters
  CURVATURE_COEFFICIENT: GLULAM_CURVATURE_CONSTANTS.CURVATURE_COEFFICIENT,
  MIN_CURVATURE_FACTOR: GLULAM_CURVATURE_CONSTANTS.MIN_CURVATURE_FACTOR,
  STRAIGHT_MEMBER_FACTOR: GLULAM_CURVATURE_CONSTANTS.STRAIGHT_MEMBER_FACTOR,
  
  // Validation limits
  MIN_RADIUS: 1.0,                    // Minimum practical radius (inches)
  MAX_RADIUS: 10000.0,               // Maximum practical radius (inches)
  MIN_LAMINATION_THICKNESS: 0.5,     // Minimum lamination thickness (inches)
  MAX_LAMINATION_THICKNESS: 2.5,     // Maximum standard lamination thickness (inches)
} as const;

/**
 * Validate curvature factor input parameters
 * @param input - Curvature factor input parameters
 * @returns Validation result with errors and warnings
 */
export function validateCurvatureFactorInput(
  input: CurvatureFactorInput
): CurvatureFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate radius of curvature
  if (typeof input.radiusOfCurvature !== 'number' || isNaN(input.radiusOfCurvature)) {
    errors.push('Radius of curvature must be a valid number');
  } else {
    if (input.radiusOfCurvature <= 0) {
      errors.push('Radius of curvature must be positive');
    } else if (input.radiusOfCurvature < CURVATURE_FACTOR_CONSTANTS.MIN_RADIUS) {
      warnings.push(`Radius of curvature ${input.radiusOfCurvature}" is very small (< ${CURVATURE_FACTOR_CONSTANTS.MIN_RADIUS}")`);
    } else if (input.radiusOfCurvature > CURVATURE_FACTOR_CONSTANTS.MAX_RADIUS) {
      warnings.push(`Radius of curvature ${input.radiusOfCurvature}" is very large (> ${CURVATURE_FACTOR_CONSTANTS.MAX_RADIUS}")`);
    }
  }

  // Validate lamination thickness
  if (typeof input.laminationThickness !== 'number' || isNaN(input.laminationThickness)) {
    errors.push('Lamination thickness must be a valid number');
  } else {
    if (input.laminationThickness <= 0) {
      errors.push('Lamination thickness must be positive');
    } else if (input.laminationThickness < CURVATURE_FACTOR_CONSTANTS.MIN_LAMINATION_THICKNESS) {
      warnings.push(`Lamination thickness ${input.laminationThickness}" is less than typical minimum (${CURVATURE_FACTOR_CONSTANTS.MIN_LAMINATION_THICKNESS}")`);
    } else if (input.laminationThickness > CURVATURE_FACTOR_CONSTANTS.MAX_LAMINATION_THICKNESS) {
      warnings.push(`Lamination thickness ${input.laminationThickness}" exceeds typical maximum (${CURVATURE_FACTOR_CONSTANTS.MAX_LAMINATION_THICKNESS}")`);
    }
  }

  // Validate design value type
  if (!input.designValueType) {
    errors.push('Design value type is required');
  } else if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
    errors.push(`Curvature factor only applies to bending design value (Fb), got: ${input.designValueType}`);
  }

  // Validate species group if provided
  if (input.speciesGroup && !Object.values(CURVATURE_SPECIES_GROUPS).includes(input.speciesGroup)) {
    warnings.push(`Unknown species group: ${input.speciesGroup}, using default: ${CURVATURE_FACTOR_CONSTANTS.DEFAULT_SPECIES_GROUP}`);
  }

  // Validate member type if provided
  if (input.memberType && !Object.values(CURVATURE_MEMBER_TYPES).includes(input.memberType)) {
    warnings.push(`Unknown member type: ${input.memberType}, using default: ${CURVATURE_FACTOR_CONSTANTS.DEFAULT_MEMBER_TYPE}`);
  }

  // Check curvature ratio limits if valid inputs
  if (errors.length === 0) {
    const tOverR = input.laminationThickness / input.radiusOfCurvature;
    const speciesGroup = input.speciesGroup || CURVATURE_FACTOR_CONSTANTS.DEFAULT_SPECIES_GROUP;
    const maxRatio = CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[speciesGroup];
    
    if (tOverR > maxRatio) {
      errors.push(`Curvature ratio t/R = ${tOverR.toFixed(4)} exceeds maximum allowed ${maxRatio.toFixed(4)} for ${speciesGroup} (NDS 5.3.8)`);
    } else if (tOverR > maxRatio * 0.9) {
      warnings.push(`Curvature ratio t/R = ${tOverR.toFixed(4)} is approaching maximum limit ${maxRatio.toFixed(4)} for ${speciesGroup}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Calculate detailed curvature factor results
 * @param input - Curvature factor input parameters
 * @returns Detailed calculation results
 */
export function calculateCurvatureFactorDetails(
  input: CurvatureFactorInput
): CurvatureFactorCalculation {
  // Validate input first
  const validation = validateCurvatureFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid input for curvature factor calculation: ${validation.errors.join(', ')}`);
  }

  // Determine species group and member type
  const speciesGroup = input.speciesGroup || CURVATURE_FACTOR_CONSTANTS.DEFAULT_SPECIES_GROUP;
  
  // Intelligently determine member type based on provided parameters
  let memberType = input.memberType;
  if (!memberType) {
    // If radiusOfCurvature and laminationThickness are provided and indicate a curved member
    if (input.radiusOfCurvature && input.laminationThickness) {
      const tOverR = input.laminationThickness / input.radiusOfCurvature;
      const maxRatio = CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[speciesGroup];
      
      // If the t/R ratio is significant (greater than a very small threshold), treat as curved
      // Very large radius (like 1,000,000) with small thickness would result in negligible t/R
      if (tOverR > 0.0001) { // More than 0.01% ratio indicates meaningful curvature
        memberType = CURVATURE_MEMBER_TYPES.CURVED;
      } else {
        memberType = CURVATURE_MEMBER_TYPES.STRAIGHT;
      }
    } else {
      memberType = CURVATURE_FACTOR_CONSTANTS.DEFAULT_MEMBER_TYPE;
    }
  }
  
  // For straight members, return 1.0
  if (memberType === CURVATURE_MEMBER_TYPES.STRAIGHT) {
    return {
      curvatureFactor: CURVATURE_FACTOR_CONSTANTS.STRAIGHT_MEMBER_FACTOR,
      thicknessToRadiusRatio: 0,
      maxAllowableCurvatureRatio: CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[speciesGroup],
      speciesGroup,
      withinNdsLimits: true,
      rawCalculatedFactor: CURVATURE_FACTOR_CONSTANTS.STRAIGHT_MEMBER_FACTOR,
      minimumFactorApplied: false,
    };
  }

  // Calculate thickness to radius ratio
  const tOverR = input.laminationThickness / input.radiusOfCurvature;
  
  // Get maximum allowable ratio for species
  const maxAllowableCurvatureRatio = CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[speciesGroup];
  
  // Check if within NDS limits
  const withinNdsLimits = tOverR <= maxAllowableCurvatureRatio;
  
  // Calculate curvature factor using NDS 5.3.8 equation: Cc = 1 - (2000)(t/R)²
  const rawCalculatedFactor = 1 - (CURVATURE_FACTOR_CONSTANTS.CURVATURE_COEFFICIENT * Math.pow(tOverR, 2));
  
  // Apply minimum limit
  const minimumFactorApplied = rawCalculatedFactor < CURVATURE_FACTOR_CONSTANTS.MIN_CURVATURE_FACTOR;
  const curvatureFactor = Math.max(rawCalculatedFactor, CURVATURE_FACTOR_CONSTANTS.MIN_CURVATURE_FACTOR);

  return {
    curvatureFactor,
    thicknessToRadiusRatio: tOverR,
    maxAllowableCurvatureRatio,
    speciesGroup,
    withinNdsLimits,
    rawCalculatedFactor,
    minimumFactorApplied,
  };
}

/**
 * Calculate curvature factor for curved glulam members
 * @param input - Curvature factor input parameters
 * @returns Curvature factor Cc
 * @throws Error if input is invalid or curvature ratio exceeds NDS limits
 * 
 * @example
 * ```typescript
 * const Cc = getCurvatureFactor({
 *   radiusOfCurvature: 120,
 *   laminationThickness: 1.5,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
 * });
 * console.log(`Curvature factor: ${Cc}`);
 * ```
 */
export function getCurvatureFactor(input: CurvatureFactorInput): number {
  const calculation = calculateCurvatureFactorDetails(input);
  
  if (!calculation.withinNdsLimits) {
    throw new Error(
      `Curvature ratio t/R = ${calculation.thicknessToRadiusRatio.toFixed(4)} ` +
      `exceeds maximum allowed ${calculation.maxAllowableCurvatureRatio.toFixed(4)} ` +
      `for ${calculation.speciesGroup} (NDS 5.3.8)`
    );
  }
  
  return calculation.curvatureFactor;
}

/**
 * Calculate curvature factor for straight members (convenience function)
 * @returns Curvature factor of 1.0 for straight members
 */
export function getCurvatureFactorForStraightMember(): number {
  return CURVATURE_FACTOR_CONSTANTS.STRAIGHT_MEMBER_FACTOR;
}

/**
 * Apply curvature factor to a reference design value
 * @param referenceValue - Reference bending design value (psi)
 * @param input - Curvature factor input parameters
 * @returns Adjusted design value
 */
export function applyCurvatureFactor(
  referenceValue: number,
  input: CurvatureFactorInput
): number {
  if (typeof referenceValue !== 'number' || isNaN(referenceValue) || referenceValue < 0) {
    throw new Error('Reference value must be a non-negative number');
  }
  
  const curvatureFactor = getCurvatureFactor(input);
  return referenceValue * curvatureFactor;
}

/**
 * Get comprehensive curvature factor analysis
 * @param input - Curvature factor input parameters
 * @returns Complete analysis results
 */
export function getCurvatureFactorAnalysis(
  input: CurvatureFactorInput
): CurvatureFactorAnalysis {
  const validation = validateCurvatureFactorInput(input);
  
  let calculation: CurvatureFactorCalculation;
  try {
    calculation = calculateCurvatureFactorDetails(input);
  } catch (error) {
    // If calculation fails, provide default values
    calculation = {
      curvatureFactor: 1.0,
      thicknessToRadiusRatio: 0,
      maxAllowableCurvatureRatio: CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[CURVATURE_FACTOR_CONSTANTS.DEFAULT_SPECIES_GROUP],
      speciesGroup: CURVATURE_FACTOR_CONSTANTS.DEFAULT_SPECIES_GROUP,
      withinNdsLimits: false,
      rawCalculatedFactor: 1.0,
      minimumFactorApplied: false,
    };
  }
  
  const applicableDesignValue = input.designValueType === DESIGN_VALUE_TYPES.BENDING;
  const memberCategory = input.memberType || CURVATURE_FACTOR_CONSTANTS.DEFAULT_MEMBER_TYPE;
  
  return {
    input,
    calculation,
    applicableDesignValue,
    memberCategory,
    validation,
  };
}

/**
 * Get curvature factors for multiple scenarios
 * @param baseInput - Base input parameters (without radius/thickness variations)
 * @param scenarios - Array of radius and thickness combinations to evaluate
 * @returns Record of scenario names to curvature factors
 */
export function getMultipleCurvatureFactors(
  baseInput: Omit<CurvatureFactorInput, 'radiusOfCurvature' | 'laminationThickness'>,
  scenarios: Array<{
    name: string;
    radiusOfCurvature: number;
    laminationThickness: number;
  }>
): Record<string, number> {
  const results: Record<string, number> = {};
  
  for (const scenario of scenarios) {
    try {
      const input: CurvatureFactorInput = {
        ...baseInput,
        radiusOfCurvature: scenario.radiusOfCurvature,
        laminationThickness: scenario.laminationThickness,
      };
      results[scenario.name] = getCurvatureFactor(input);
    } catch (error) {
      results[scenario.name] = NaN; // Indicate invalid scenario
    }
  }
  
  return results;
}

/**
 * Check if design value is affected by curvature factor
 * @param designValueType - Type of design value
 * @returns True if curvature factor applies
 */
export function isDesignValueAffectedByCurvature(
  designValueType: DesignValueType
): boolean {
  return designValueType === DESIGN_VALUE_TYPES.BENDING;
}

/**
 * Get curvature factor optimization recommendations
 * @param targetCurvatureFactor - Desired curvature factor (typically close to 1.0)
 * @param laminationThickness - Fixed lamination thickness (inches)
 * @param speciesGroup - Species group for limits
 * @returns Optimization recommendations
 */
export function getCurvatureFactorOptimization(
  targetCurvatureFactor: number = 0.95,
  laminationThickness: number,
  speciesGroup: CurvatureSpeciesGroup = CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
): {
  targetCurvatureFactor: number;
  minimumRadius: number;
  speciesGroup: CurvatureSpeciesGroup;
  recommendations: string[];
} {
  const maxRatio = CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[speciesGroup];
  
  // Calculate minimum radius for target factor: Cc = 1 - 2000(t/R)²
  // Solving for R: R = t / sqrt((1 - Cc) / 2000)
  const factorDifference = 1 - targetCurvatureFactor;
  const minimumRadiusForTarget = laminationThickness / Math.sqrt(factorDifference / CURVATURE_FACTOR_CONSTANTS.CURVATURE_COEFFICIENT);
  
  // Calculate minimum radius for NDS limits
  const minimumRadiusForLimits = laminationThickness / maxRatio;
  
  const minimumRadius = Math.max(minimumRadiusForTarget, minimumRadiusForLimits);
  
  const recommendations: string[] = [];
  
  if (minimumRadiusForTarget > minimumRadiusForLimits) {
    recommendations.push(`Minimum radius of ${minimumRadius.toFixed(1)}" required to achieve target curvature factor of ${targetCurvatureFactor}`);
  } else {
    recommendations.push(`Minimum radius of ${minimumRadius.toFixed(1)}" required to meet NDS limits for ${speciesGroup}`);
    recommendations.push(`With this radius, curvature factor will be ${(1 - CURVATURE_FACTOR_CONSTANTS.CURVATURE_COEFFICIENT * Math.pow(laminationThickness / minimumRadius, 2)).toFixed(3)}`);
  }
  
  if (speciesGroup === CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD) {
    const hwspMinRadius = laminationThickness / CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[CURVATURE_SPECIES_GROUPS.HARDWOOD_SOUTHERN_PINE];
    if (hwspMinRadius < minimumRadius) {
      recommendations.push(`Consider using hardwood or Southern Pine species to allow smaller radius (minimum ${hwspMinRadius.toFixed(1)}")`);
    }
  }
  
  recommendations.push(`Consider reducing lamination thickness to allow smaller radius`);
  recommendations.push(`Verify that curved member design meets all other NDS requirements`);
  
  return {
    targetCurvatureFactor,
    minimumRadius,
    speciesGroup,
    recommendations,
  };
} 