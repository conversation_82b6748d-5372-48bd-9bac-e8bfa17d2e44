/**
 * Test suite for Curvature Factor (Cc) Calculations
 *
 * @fileoverview Unit tests for NDS 5.3.8 curvature factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  getCurvatureFactor,
  getCurvatureFactorForStraightMember,
  applyCurvatureFactor,
  validateCurvatureFactorInput,
  calculateCurvatureFactorDetails,
  getCurvatureFactorAnalysis,
  getMultipleCurvatureFactors,
  isDesignValueAffectedByCurvature,
  getCurvatureFactorOptimization,
  CURVATURE_SPECIES_GROUPS,
  CURVATURE_MEMBER_TYPES,
  CURVATURE_FACTOR_CONSTANTS,
  type CurvatureFactorInput,
  type CurvatureSpeciesGroup,
  type CurvatureMemberType,
} from "./curvature-factor";

import { DESIGN_VALUE_TYPES } from "../../constants";

describe("Curvature Factor (Cc) Calculations", () => {
  describe("validateCurvatureFactorInput", () => {
    test("should validate correct input", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test("should reject invalid radius of curvature", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: -10,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        "Radius of curvature must be positive"
      );
    });

    test("should reject invalid lamination thickness", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 120,
        laminationThickness: 0,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        "Lamination thickness must be positive"
      );
    });

    test("should reject non-bending design value types", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 120,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain(
        "Curvature factor only applies to bending design value (Fb), got: Fv"
      );
    });

    test("should reject excessive curvature ratio", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 100,
        laminationThickness: 2.0, // t/R = 0.02 > 1/125 = 0.008
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.isValid).toBe(false);
      expect(validation.errors[0]).toMatch(/Curvature ratio.*exceeds maximum allowed/);
    });

    test("should warn for very small radius", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 0.5,
        laminationThickness: 0.1,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.warnings).toContain(
        'Radius of curvature 0.5" is very small (< 1")'
      );
    });

    test("should warn for excessive lamination thickness", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 1000,
        laminationThickness: 3.0,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const validation = validateCurvatureFactorInput(input);
      expect(validation.warnings).toContain(
        'Lamination thickness 3" exceeds typical maximum (2.5")'
      );
    });

    test("should warn when approaching curvature ratio limits", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 110, // t/R ≈ 0.0136, close to 1/125 = 0.008 for other softwood
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const validation = validateCurvatureFactorInput(input);
      // This might generate a warning depending on how close it is to the limit
    });
  });

  describe("calculateCurvatureFactorDetails", () => {
    test("should calculate curvature factor for curved members", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
        memberType: CURVATURE_MEMBER_TYPES.CURVED,
      };

      const calculation = calculateCurvatureFactorDetails(input);

      expect(calculation.thicknessToRadiusRatio).toBeCloseTo(0.0075, 4);
      expect(calculation.speciesGroup).toBe(
        CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
      );
      expect(calculation.withinNdsLimits).toBe(true);
      expect(calculation.curvatureFactor).toBeCloseTo(0.8875, 3);
      expect(calculation.minimumFactorApplied).toBe(false);
    });

    test("should return 1.0 for straight members", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        memberType: CURVATURE_MEMBER_TYPES.STRAIGHT,
      };

      const calculation = calculateCurvatureFactorDetails(input);

      expect(calculation.curvatureFactor).toBe(1.0);
      expect(calculation.thicknessToRadiusRatio).toBe(0);
      expect(calculation.withinNdsLimits).toBe(true);
    });

    test("should apply minimum factor when calculated value is too low", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 105, // t/R = 1.0/105 = 0.0095 < 0.01 (within hardwood/SP limits)
        laminationThickness: 1.0,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.HARDWOOD_SOUTHERN_PINE, // More permissive ratio
        memberType: CURVATURE_MEMBER_TYPES.CURVED,
      };

      const calculation = calculateCurvatureFactorDetails(input);

      // Calculate expected factor: Cc = 1 - 2000 * (1.0/105)^2 = 1 - 2000 * 0.0000907 = 1 - 0.1814 = 0.8186
      // This should be above the minimum factor of 0.1, so minimum shouldn't be applied
      expect(calculation.withinNdsLimits).toBe(true);
      expect(calculation.curvatureFactor).toBeCloseTo(0.8186, 3);
      expect(calculation.minimumFactorApplied).toBe(false);
    });

    test("should handle different species groups correctly", () => {
      const inputOtherSoftwood: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const inputHardwoodSP: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.HARDWOOD_SOUTHERN_PINE,
      };

      const calcOther = calculateCurvatureFactorDetails(inputOtherSoftwood);
      const calcHardwood = calculateCurvatureFactorDetails(inputHardwoodSP);

      expect(calcOther.maxAllowableCurvatureRatio).toBe(1/125);
      expect(calcHardwood.maxAllowableCurvatureRatio).toBe(1/100);
      expect(calcOther.curvatureFactor).toBeCloseTo(calcHardwood.curvatureFactor, 3);
    });
  });

  describe("getCurvatureFactor", () => {
    test("should calculate curvature factor for valid curved member", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const Cc = getCurvatureFactor(input);
      
      // NDS 5.3.8: Cc = 1 - (2000)(t/R)²
      // t/R = 1.5/200 = 0.0075
      // Cc = 1 - (2000)(0.0075)² = 1 - (2000)(0.00005625) = 1 - 0.1125 = 0.8875
      expect(Cc).toBeCloseTo(0.8875, 3);
    });

    test("should throw error for excessive curvature ratio", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 100,
        laminationThickness: 2.0,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      expect(() => {
        getCurvatureFactor(input);
      }).toThrow(/Curvature ratio.*exceeds maximum allowed/);
    });

    test("should throw error for invalid input", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: -10,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      expect(() => {
        getCurvatureFactor(input);
      }).toThrow(/Invalid input for curvature factor calculation/);
    });

    test("should handle edge case at maximum allowable ratio", () => {
      const maxRatio = 1/125; // For other softwood
      const radius = 125;
      const thickness = 1.0; // t/R = 1.0/125 = 0.008 = 1/125

      const input: CurvatureFactorInput = {
        radiusOfCurvature: radius,
        laminationThickness: thickness,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const Cc = getCurvatureFactor(input);
      expect(Cc).toBeGreaterThan(0);
      expect(Cc).toBeLessThanOrEqual(1.0);
    });
  });

  describe("getCurvatureFactorForStraightMember", () => {
    test("should always return 1.0", () => {
      const Cc = getCurvatureFactorForStraightMember();
      expect(Cc).toBe(1.0);
    });
  });

  describe("applyCurvatureFactor", () => {
    test("should apply curvature factor to reference value", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const referenceValue = 2400; // psi
      const adjustedValue = applyCurvatureFactor(referenceValue, input);

      const expectedCc = 0.8875; // From previous calculation
      const expectedAdjusted = referenceValue * expectedCc;
      expect(adjustedValue).toBeCloseTo(expectedAdjusted, 1);
    });

    test("should reject invalid reference values", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      expect(() => {
        applyCurvatureFactor(-100, input);
      }).toThrow("Reference value must be a non-negative number");

      expect(() => {
        applyCurvatureFactor(NaN, input);
      }).toThrow("Reference value must be a non-negative number");
    });
  });

  describe("getCurvatureFactorAnalysis", () => {
    test("should provide comprehensive analysis", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
        memberType: CURVATURE_MEMBER_TYPES.CURVED,
      };

      const analysis = getCurvatureFactorAnalysis(input);

      expect(analysis.input).toEqual(input);
      expect(analysis.applicableDesignValue).toBe(true);
      expect(analysis.memberCategory).toBe(CURVATURE_MEMBER_TYPES.CURVED);
      expect(analysis.validation.isValid).toBe(true);
      expect(analysis.calculation.curvatureFactor).toBeCloseTo(0.8875, 3);
    });

    test("should handle invalid input gracefully", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: -10,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const analysis = getCurvatureFactorAnalysis(input);

      expect(analysis.validation.isValid).toBe(false);
      expect(analysis.calculation.curvatureFactor).toBe(1.0); // Default fallback
    });

    test("should identify non-applicable design values", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.SHEAR,
      };

      const analysis = getCurvatureFactorAnalysis(input);

      expect(analysis.applicableDesignValue).toBe(false);
    });
  });

  describe("getMultipleCurvatureFactors", () => {
    test("should calculate factors for multiple scenarios", () => {
      const baseInput = {
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const scenarios = [
        { name: "Large Radius", radiusOfCurvature: 300, laminationThickness: 1.5 },
        { name: "Medium Radius", radiusOfCurvature: 200, laminationThickness: 1.5 },
        { name: "Small Radius", radiusOfCurvature: 188, laminationThickness: 1.5 },
      ];

      const results = getMultipleCurvatureFactors(baseInput, scenarios);

      expect(results["Large Radius"]).toBeGreaterThan(results["Medium Radius"]);
      expect(results["Medium Radius"]).toBeGreaterThan(results["Small Radius"]);
      
      // All should be valid numbers
      Object.values(results).forEach(factor => {
        expect(typeof factor).toBe("number");
        expect(factor).toBeGreaterThan(0);
        expect(factor).toBeLessThanOrEqual(1.0);
      });
    });

    test("should handle invalid scenarios", () => {
      const baseInput = {
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      const scenarios = [
        { name: "Valid", radiusOfCurvature: 200, laminationThickness: 1.5 },
        { name: "Invalid", radiusOfCurvature: 50, laminationThickness: 3.0 }, // Excessive ratio
      ];

      const results = getMultipleCurvatureFactors(baseInput, scenarios);

      expect(typeof results["Valid"]).toBe("number");
      expect(results["Valid"]).toBeGreaterThan(0);
      expect(isNaN(results["Invalid"])).toBe(true);
    });
  });

  describe("isDesignValueAffectedByCurvature", () => {
    test("should return true for bending design values", () => {
      expect(isDesignValueAffectedByCurvature(DESIGN_VALUE_TYPES.BENDING)).toBe(true);
    });

    test("should return false for non-bending design values", () => {
      expect(isDesignValueAffectedByCurvature(DESIGN_VALUE_TYPES.SHEAR)).toBe(false);
      expect(isDesignValueAffectedByCurvature(DESIGN_VALUE_TYPES.TENSION_PARALLEL)).toBe(false);
      expect(isDesignValueAffectedByCurvature(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL)).toBe(false);
      expect(isDesignValueAffectedByCurvature(DESIGN_VALUE_TYPES.E)).toBe(false);
    });
  });

  describe("getCurvatureFactorOptimization", () => {
    test("should provide optimization recommendations", () => {
      const optimization = getCurvatureFactorOptimization(
        0.95,
        1.5,
        CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
      );

      expect(optimization.targetCurvatureFactor).toBe(0.95);
      expect(optimization.minimumRadius).toBeGreaterThan(0);
      expect(optimization.speciesGroup).toBe(CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD);
      expect(optimization.recommendations).toBeInstanceOf(Array);
      expect(optimization.recommendations.length).toBeGreaterThan(0);
    });

    test("should recommend hardwood/Southern Pine for smaller radius", () => {
      const optimization = getCurvatureFactorOptimization(
        0.9,
        2.0,
        CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
      );

      const hasSpeciesRecommendation = optimization.recommendations.some(
        rec => rec.includes("hardwood or Southern Pine")
      );
      expect(hasSpeciesRecommendation).toBe(true);
    });

    test("should handle edge cases", () => {
      // Very high target factor should require large radius
      const highTargetOptimization = getCurvatureFactorOptimization(
        0.99,
        1.5,
        CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
      );

      expect(highTargetOptimization.minimumRadius).toBeGreaterThan(100);

      // Low target factor should require smaller radius
      const lowTargetOptimization = getCurvatureFactorOptimization(
        0.8,
        1.5,
        CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD
      );

      expect(lowTargetOptimization.minimumRadius).toBeLessThan(highTargetOptimization.minimumRadius);
    });
  });

  describe("Constants and Types", () => {
    test("should export required constants", () => {
      expect(CURVATURE_SPECIES_GROUPS).toBeDefined();
      expect(CURVATURE_MEMBER_TYPES).toBeDefined();
      expect(CURVATURE_FACTOR_CONSTANTS).toBeDefined();
    });

    test("should have correct species group values", () => {
      expect(Object.values(CURVATURE_SPECIES_GROUPS)).toContain("hardwood_southern_pine");
      expect(Object.values(CURVATURE_SPECIES_GROUPS)).toContain("other_softwood");
    });

    test("should have correct member type values", () => {
      expect(Object.values(CURVATURE_MEMBER_TYPES)).toContain("straight");
      expect(Object.values(CURVATURE_MEMBER_TYPES)).toContain("curved");
      expect(Object.values(CURVATURE_MEMBER_TYPES)).toContain("compound_curved");
    });

    test("should have reasonable constant values", () => {
      expect(CURVATURE_FACTOR_CONSTANTS.CURVATURE_COEFFICIENT).toBe(2000);
      expect(CURVATURE_FACTOR_CONSTANTS.MIN_CURVATURE_FACTOR).toBe(0.1);
      expect(CURVATURE_FACTOR_CONSTANTS.STRAIGHT_MEMBER_FACTOR).toBe(1.0);
      expect(CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD]).toBe(1/125);
      expect(CURVATURE_FACTOR_CONSTANTS.MAX_CURVATURE_RATIOS[CURVATURE_SPECIES_GROUPS.HARDWOOD_SOUTHERN_PINE]).toBe(1/100);
    });
  });

  describe("Edge Cases and Error Handling", () => {
    test("should handle very large curvature factors", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 10000, // Very large radius
        laminationThickness: 0.5,  // Very thin lamination
        designValueType: DESIGN_VALUE_TYPES.BENDING,
      };

      const Cc = getCurvatureFactor(input);
      expect(Cc).toBeCloseTo(1.0, 6); // Should be very close to 1.0
    });

    test("should handle floating point precision", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 125.0001, // Just slightly over exact ratio
        laminationThickness: 1.0,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        speciesGroup: CURVATURE_SPECIES_GROUPS.OTHER_SOFTWOOD,
      };

      // Should not throw error due to floating point precision
      expect(() => {
        getCurvatureFactor(input);
      }).not.toThrow();
    });

    test("should handle missing optional parameters", () => {
      const input: CurvatureFactorInput = {
        radiusOfCurvature: 200,
        laminationThickness: 1.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        // speciesGroup and memberType omitted
      };

      const Cc = getCurvatureFactor(input);
      expect(typeof Cc).toBe("number");
      expect(Cc).toBeGreaterThan(0);
      expect(Cc).toBeLessThanOrEqual(1.0);
    });
  });
}); 