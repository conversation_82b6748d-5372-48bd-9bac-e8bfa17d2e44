# Temperature Factor (Ct)

## Overview

This module implements the temperature factor (Ct) calculations according to the National Design Specification (NDS) for Wood Construction, Section 2.3.3. The temperature factor adjusts design values when wood members will be exposed to elevated temperatures for extended periods.

## Key Concepts

### When Temperature Factor Applies

The temperature factor applies when:
- **Service temperatures > 100°F** for extended periods
- **Design values** are affected by temperature exposure
- **Immediate and reversible effects** only (up to 150°F)
- **Permanent strength loss** can occur above 150°F

### Temperature Ranges

1. **Normal Temperature** (≤ 100°F):
   - Ct = 1.0 (no temperature factor)
   - Standard design values apply

2. **Moderate Temperature** (100°F < T ≤ 125°F):
   - Reduced temperature factors apply
   - Different factors for tension/elasticity vs. strength values

3. **High Temperature** (125°F < T ≤ 150°F):
   - Further reduced temperature factors
   - Moisture condition affects factor values

4. **Extreme Temperature** (> 150°F):
   - Permanent strength loss may occur
   - Special analysis required

### Design Value Categories

**Tension and Elasticity Values** (Ft, E, Emin):
- Same factors for wet or dry conditions
- Less affected by temperature

**Strength Values** (Fb, Fv, Fc, Fc⊥):
- Different factors for wet vs. dry conditions
- More significantly affected by temperature

## API Reference

### Main Functions

#### `getTemperatureFactor(input)`

Calculates the temperature factor for given conditions.

**Parameters:**
- `input.temperature`: Service temperature (°F)
- `input.designValue`: Design value type ('Fb', 'Ft', 'Fc', 'Fv', 'Fc_perp', 'E', 'Emin')
- `input.moistureCondition`: Moisture condition ('wet', 'dry', 'wet_or_dry')
- `input.exposureDuration`: Duration of temperature exposure

**Returns:**
- `factor`: The temperature factor (≤ 1.0)
- `applicable`: Whether temperature factor applies
- `temperatureRange`: Temperature classification
- `explanation`: Detailed explanation

#### `validateTemperatureFactorInput(input)`

Validates input parameters and provides detailed validation results.

#### `getTemperatureFactorAnalysis(input)`

Provides comprehensive analysis including temperature classification and factor selection.

#### `getEffectiveMoistureCondition(input)`

Determines effective moisture condition based on temperature and service conditions.

### Usage Examples

```typescript
import { getTemperatureFactor } from './temperature';

// Example 1: High temperature application (dry conditions)
const hotDry = getTemperatureFactor({
  temperature: 140,
  designValue: 'Fb',
  moistureCondition: 'dry',
  exposureDuration: 'permanent'
});

console.log(hotDry.factor); // e.g., 0.7

// Example 2: Moderate temperature (wet conditions)
const warmWet = getTemperatureFactor({
  temperature: 110,
  designValue: 'Fb',
  moistureCondition: 'wet',
  exposureDuration: 'permanent'
});

console.log(warmWet.factor); // e.g., 0.7

// Example 3: Tension/elasticity value
const tensionHot = getTemperatureFactor({
  temperature: 130,
  designValue: 'E',
  moistureCondition: 'wet_or_dry',
  exposureDuration: 'permanent'
});

console.log(tensionHot.factor); // e.g., 0.9
```

### Constants and Factor Tables

```typescript
// Temperature thresholds
export const TEMPERATURE_FACTOR_CONSTANTS = {
  TEMP_THRESHOLD_LOW: 100,     // T ≤ 100°F
  TEMP_THRESHOLD_MID: 125,     // 100°F < T ≤ 125°F  
  TEMP_THRESHOLD_HIGH: 150,    // 125°F < T ≤ 150°F
  MAX_REVERSIBLE_TEMP: 150,    // Above 150°F → permanent loss
  NORMAL_TEMPERATURE: 70       // °F
} as const;

// Temperature factors for strength values (dry conditions)
export const TEMPERATURE_FACTORS = {
  STRENGTH_VALUES_DRY: {
    LOW_TEMP: 1.0,     // T ≤ 100°F
    MID_TEMP: 0.8,     // 100°F < T ≤ 125°F
    HIGH_TEMP: 0.7     // 125°F < T ≤ 150°F
  }
  // ... additional factor tables
} as const;
```

## Design Considerations

### Common High-Temperature Applications

- **Industrial facilities** with process heat
- **Kiln-dried lumber storage** areas
- **Boiler rooms** and mechanical spaces  
- **Solar exposure** on roofs and exterior members
- **Radiant heating systems**
- **Fire-rated assemblies** (special considerations)

### Design Strategies

**Temperature Control:**
- Provide **ventilation** to reduce temperatures
- Use **insulation** to protect structural members
- Design for **thermal expansion**

**Material Selection:**
- Consider **metal connections** for high-temperature areas
- Use **fire-retardant treatments** where appropriate
- Select **appropriate species** for temperature resistance

**Structural Design:**
- Apply **temperature factors** conservatively
- Consider **cumulative effects** of multiple factors
- Design for **worst-case temperature scenarios**

## Files

- `temperature.ts` - Main implementation
- `temperature.test.ts` - Comprehensive test suite
- `README.md` - This documentation

## References

- NDS 2018, Section 2.3.3 - Temperature Factor, Ct
- NDS 2018, Table 2.3.3 - Temperature Factors
- ASTM D2915 - Standard Practice for Evaluating Allowable Properties of Wood
- Research on wood behavior at elevated temperatures 