/**
 * Temperature Factor (Ct) Calculations
 * Based on NDS 2.3.3 and Table 2.3.3
 *
 * Temperature factors account for the reduction in wood strength properties
 * when structural members are exposed to elevated temperatures up to 150°F.
 * The effects are immediate and reversible for temperatures up to 150°F.
 */

import {
  DesignValueType,
  DESIGN_VALUE_TYPES,
  MoistureCondition,
  MOISTURE_CONDITIONS,
  TEMPERATURE_FACTOR_CONSTANTS,
  TEMPERATURE_FACTORS,
  TENSION_ELASTICITY_DESIGN_VALUES,
  STRENGTH_DESIGN_VALUES,
} from "../../constants";

/**
 * Interface for temperature factor input parameters
 */
export interface TemperatureFactorInput {
  /** Temperature in degrees Fahrenheit */
  temperature: number;
  /** Design value type (Fb, Ft, Fc, Fv, Fc_perp, E, Emin) */
  designValueType: DesignValueType;
  /** Moisture condition (wet, dry, or wet_or_dry for E and Emin) */
  moistureCondition: MoistureCondition;
}

/**
 * Interface for temperature factor validation result
 */
export interface TemperatureFactorValidation {
  /** Whether the input parameters are valid */
  isValid: boolean;
  /** Array of validation error messages */
  errors: string[];
  /** Array of validation warnings */
  warnings: string[];
}

/**
 * Interface for temperature factor analysis result
 */
export interface TemperatureFactorAnalysis {
  /** Input parameters used for calculation */
  input: TemperatureFactorInput;
  /** Calculated temperature factor */
  temperatureFactor: number;
  /** Temperature range category */
  temperatureRange: "low" | "mid" | "high" | "excessive";
  /** Design value category */
  designValueCategory: "tension_elasticity" | "strength_dry" | "strength_wet";
  /** Whether temperature effects are reversible */
  isReversible: boolean;
  /** Validation results */
  validation: TemperatureFactorValidation;
}

/**
 * Validates temperature factor input parameters
 *
 * @param input - Temperature factor input parameters
 * @returns Validation result with errors and warnings
 *
 * @example
 * ```typescript
 * const validation = validateTemperatureFactorInput({
 *   temperature: 120,
 *   designValueType: 'Fb',
 *   moistureCondition: 'dry'
 * });
 * ```
 */
export function validateTemperatureFactorInput(
  input: TemperatureFactorInput
): TemperatureFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate temperature
  if (!Number.isFinite(input.temperature)) {
    errors.push("Temperature must be a finite number");
  } else {
    if (input.temperature < -50) {
      warnings.push("Temperature is unusually low for structural applications");
    }
    if (input.temperature > TEMPERATURE_FACTOR_CONSTANTS.MAX_REVERSIBLE_TEMP) {
      warnings.push(
        `Temperature exceeds ${TEMPERATURE_FACTOR_CONSTANTS.MAX_REVERSIBLE_TEMP}°F - effects may not be reversible`
      );
    }
    if (input.temperature > 200) {
      errors.push("Temperature exceeds reasonable limits for wood structures");
    }
  }

  // Validate design value type
  const validDesignValues = Object.values(DESIGN_VALUE_TYPES);
  if (!validDesignValues.includes(input.designValueType)) {
    errors.push(`Invalid design value type: ${input.designValueType}`);
  }

  // Validate moisture condition
  const validMoistureConditions = Object.values(MOISTURE_CONDITIONS);
  if (!validMoistureConditions.includes(input.moistureCondition)) {
    errors.push(`Invalid moisture condition: ${input.moistureCondition}`);
  }

  // Validate moisture condition compatibility with design value type
  if (
    TENSION_ELASTICITY_DESIGN_VALUES.includes(input.designValueType as any) &&
    input.moistureCondition !== MOISTURE_CONDITIONS.WET_OR_DRY
  ) {
    warnings.push(
      `Design value ${input.designValueType} typically uses 'wet_or_dry' moisture condition per NDS Table 2.3.3`
    );
  }

  if (
    STRENGTH_DESIGN_VALUES.includes(input.designValueType as any) &&
    input.moistureCondition === MOISTURE_CONDITIONS.WET_OR_DRY
  ) {
    warnings.push(
      `Design value ${input.designValueType} requires specific moisture condition (wet or dry) per NDS Table 2.3.3`
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Determines the temperature range category based on temperature
 *
 * @param temperature - Temperature in degrees Fahrenheit
 * @returns Temperature range category
 *
 * @private
 */
function getTemperatureRange(
  temperature: number
): "low" | "mid" | "high" | "excessive" {
  if (temperature <= TEMPERATURE_FACTOR_CONSTANTS.TEMP_THRESHOLD_LOW) {
    return "low";
  } else if (temperature <= TEMPERATURE_FACTOR_CONSTANTS.TEMP_THRESHOLD_MID) {
    return "mid";
  } else if (temperature <= TEMPERATURE_FACTOR_CONSTANTS.TEMP_THRESHOLD_HIGH) {
    return "high";
  } else {
    return "excessive";
  }
}

/**
 * Determines the design value category for temperature factor calculation
 *
 * @param designValueType - Design value type
 * @param moistureCondition - Moisture condition
 * @returns Design value category
 *
 * @private
 */
function getDesignValueCategory(
  designValueType: DesignValueType,
  moistureCondition: MoistureCondition
): "tension_elasticity" | "strength_dry" | "strength_wet" {
  // Tension and elasticity values (Ft, E, Emin) - same factors for wet or dry
  if (TENSION_ELASTICITY_DESIGN_VALUES.includes(designValueType as any)) {
    return "tension_elasticity";
  }

  // Strength values (Fb, Fv, Fc, Fc_perp) - different factors for wet vs dry
  if (STRENGTH_DESIGN_VALUES.includes(designValueType as any)) {
    return moistureCondition === MOISTURE_CONDITIONS.WET
      ? "strength_wet"
      : "strength_dry";
  }

  // Default to dry strength if no specific category matches
  return "strength_dry";
}

/**
 * Calculates the temperature factor (Ct) for a given design value
 * Based on NDS Table 2.3.3
 *
 * @param input - Temperature factor input parameters
 * @returns Temperature factor (Ct)
 *
 * @throws Error if input parameters are invalid
 *
 * @example
 * ```typescript
 * // Bending strength at 110°F under dry conditions
 * const Ct = getTemperatureFactor({
 *   temperature: 110,
 *   designValueType: 'Fb',
 *   moistureCondition: 'dry'
 * });
 * // Returns 0.8
 * ```
 */
export function getTemperatureFactor(input: TemperatureFactorInput): number {
  const validation = validateTemperatureFactorInput(input);

  if (!validation.isValid) {
    throw new Error(
      `Invalid temperature factor input: ${validation.errors.join(", ")}`
    );
  }

  const temperatureRange = getTemperatureRange(input.temperature);
  const designValueCategory = getDesignValueCategory(
    input.designValueType,
    input.moistureCondition
  );

  // For temperatures above 150°F, return the 150°F factor with a warning logged
  const effectiveRange =
    temperatureRange === "excessive" ? "high" : temperatureRange;

  // Get the appropriate temperature factor based on design value category
  let temperatureFactor: number;

  switch (designValueCategory) {
    case "tension_elasticity":
      switch (effectiveRange) {
        case "low":
          temperatureFactor =
            TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.LOW_TEMP;
          break;
        case "mid":
          temperatureFactor =
            TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.MID_TEMP;
          break;
        case "high":
          temperatureFactor =
            TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.HIGH_TEMP;
          break;
        default:
          temperatureFactor =
            TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.LOW_TEMP;
      }
      break;

    case "strength_dry":
      switch (effectiveRange) {
        case "low":
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.LOW_TEMP;
          break;
        case "mid":
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP;
          break;
        case "high":
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.HIGH_TEMP;
          break;
        default:
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.LOW_TEMP;
      }
      break;

    case "strength_wet":
      switch (effectiveRange) {
        case "low":
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.LOW_TEMP;
          break;
        case "mid":
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.MID_TEMP;
          break;
        case "high":
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.HIGH_TEMP;
          break;
        default:
          temperatureFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.LOW_TEMP;
      }
      break;

    default:
      temperatureFactor = 1.0;
  }

  return temperatureFactor;
}

/**
 * Applies temperature factor to a reference design value
 *
 * @param referenceValue - Reference design value
 * @param input - Temperature factor input parameters
 * @returns Adjusted design value
 *
 * @example
 * ```typescript
 * const adjustedFb = applyTemperatureFactor(1200, {
 *   temperature: 110,
 *   designValueType: 'Fb',
 *   moistureCondition: 'dry'
 * });
 * // Returns 960 (1200 × 0.8)
 * ```
 */
export function applyTemperatureFactor(
  referenceValue: number,
  input: TemperatureFactorInput
): number {
  if (!Number.isFinite(referenceValue) || referenceValue < 0) {
    throw new Error("Reference value must be a non-negative finite number");
  }

  const temperatureFactor = getTemperatureFactor(input);
  return referenceValue * temperatureFactor;
}

/**
 * Calculates temperature factors for multiple design values at the same temperature
 *
 * @param baseInput - Base temperature factor input (temperature and moisture condition)
 * @param designValueTypes - Array of design value types to calculate factors for
 * @returns Object mapping design value types to their temperature factors
 *
 * @example
 * ```typescript
 * const factors = getMultipleTemperatureFactors(
 *   { temperature: 110, moistureCondition: 'dry' },
 *   ['Fb', 'Ft', 'Fc', 'E']
 * );
 * // Returns: { Fb: 0.8, Ft: 0.9, Fc: 0.8, E: 0.9 }
 * ```
 */
export function getMultipleTemperatureFactors(
  baseInput: Omit<TemperatureFactorInput, "designValueType">,
  designValueTypes: DesignValueType[]
): Record<DesignValueType, number> {
  const results: Record<string, number> = {};

  for (const designValueType of designValueTypes) {
    const input: TemperatureFactorInput = {
      ...baseInput,
      designValueType,
    };

    try {
      results[designValueType] = getTemperatureFactor(input);
    } catch (error) {
      // For invalid combinations, set factor to 1.0 and let validation handle the error
      results[designValueType] = 1.0;
    }
  }

  return results as Record<DesignValueType, number>;
}

/**
 * Performs a comprehensive temperature factor analysis
 *
 * @param input - Temperature factor input parameters
 * @returns Complete analysis including factor, validation, and metadata
 *
 * @example
 * ```typescript
 * const analysis = getTemperatureFactorAnalysis({
 *   temperature: 130,
 *   designValueType: 'Fb',
 *   moistureCondition: 'wet'
 * });
 * ```
 */
export function getTemperatureFactorAnalysis(
  input: TemperatureFactorInput
): TemperatureFactorAnalysis {
  const validation = validateTemperatureFactorInput(input);
  const temperatureRange = getTemperatureRange(input.temperature);
  const designValueCategory = getDesignValueCategory(
    input.designValueType,
    input.moistureCondition
  );

  let temperatureFactor: number;
  try {
    temperatureFactor = getTemperatureFactor(input);
  } catch {
    temperatureFactor = 1.0; // Default factor if calculation fails
  }

  return {
    input,
    temperatureFactor,
    temperatureRange,
    designValueCategory,
    isReversible:
      input.temperature <= TEMPERATURE_FACTOR_CONSTANTS.MAX_REVERSIBLE_TEMP,
    validation,
  };
}

/**
 * Checks if a design value is affected by temperature
 *
 * @param designValueType - Design value type to check
 * @returns True if the design value is affected by temperature
 *
 * @example
 * ```typescript
 * isDesignValueAffectedByTemperature('Fb'); // true
 * isDesignValueAffectedByTemperature('some_other_value'); // false
 * ```
 */
export function isDesignValueAffectedByTemperature(
  designValueType: string
): boolean {
  const affectedValues = [
    ...TENSION_ELASTICITY_DESIGN_VALUES,
    ...STRENGTH_DESIGN_VALUES,
  ];

  return affectedValues.includes(designValueType as DesignValueType);
}

/**
 * Gets the effective moisture condition for temperature factor calculation
 * For E and Emin, the moisture condition is always 'wet_or_dry'
 *
 * @param designValueType - Design value type
 * @param requestedMoistureCondition - Requested moisture condition
 * @returns Effective moisture condition for calculation
 *
 * @example
 * ```typescript
 * getEffectiveMoistureCondition('E', 'dry'); // Returns 'wet_or_dry'
 * getEffectiveMoistureCondition('Fb', 'dry'); // Returns 'dry'
 * ```
 */
export function getEffectiveMoistureCondition(
  designValueType: DesignValueType,
  requestedMoistureCondition: MoistureCondition
): MoistureCondition {
  // For E and Emin, always use wet_or_dry condition per NDS Table 2.3.3
  if (TENSION_ELASTICITY_DESIGN_VALUES.includes(designValueType as any)) {
    return MOISTURE_CONDITIONS.WET_OR_DRY;
  }

  return requestedMoistureCondition;
}
