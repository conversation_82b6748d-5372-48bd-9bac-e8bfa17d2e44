/**
 * Temperature Factor (Ct) Unit Tests
 * Tests for NDS 2.3.3 Temperature Factor calculations
 */

import {
  getTemperatureFactor,
  applyTemperatureFactor,
  validateTemperatureFactorInput,
  getMultipleTemperatureFactors,
  getTemperatureFactorAnalysis,
  isDesignValueAffectedByTemperature,
  getEffectiveMoistureCondition,
  TemperatureFactorInput,
} from "./temperature";
import {
  DESIGN_VALUE_TYPES,
  MOISTURE_CONDITIONS,
  TEMPERATURE_FACTOR_CONSTANTS,
  TEMPERATURE_FACTORS,
} from "../../constants";

describe("Temperature Factor (Ct) Calculations", () => {
  describe("validateTemperatureFactorInput", () => {
    it("should validate correct input parameters", () => {
      const input: TemperatureFactorInput = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = validateTemperatureFactorInput(input);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should detect invalid temperature values", () => {
      const inputs = [
        {
          temperature: NaN,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          moistureCondition: MOISTURE_CONDITIONS.DRY,
        },
        {
          temperature: Infinity,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          moistureCondition: MOISTURE_CONDITIONS.DRY,
        },
        {
          temperature: 300,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          moistureCondition: MOISTURE_CONDITIONS.DRY,
        },
      ];

      inputs.forEach((input) => {
        const result = validateTemperatureFactorInput(input);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it("should warn about extreme temperatures", () => {
      const lowTempInput: TemperatureFactorInput = {
        temperature: -60,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const highTempInput: TemperatureFactorInput = {
        temperature: 160,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const lowResult = validateTemperatureFactorInput(lowTempInput);
      const highResult = validateTemperatureFactorInput(highTempInput);

      expect(lowResult.warnings).toContain(
        "Temperature is unusually low for structural applications"
      );
      expect(highResult.warnings).toContain(
        `Temperature exceeds ${TEMPERATURE_FACTOR_CONSTANTS.MAX_REVERSIBLE_TEMP}°F - effects may not be reversible`
      );
    });

    it("should detect invalid design value types", () => {
      const input = {
        temperature: 110,
        designValueType: "invalid_type" as any,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = validateTemperatureFactorInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        "Invalid design value type: invalid_type"
      );
    });

    it("should detect invalid moisture conditions", () => {
      const input = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: "invalid_moisture" as any,
      };

      const result = validateTemperatureFactorInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        "Invalid moisture condition: invalid_moisture"
      );
    });

    it("should warn about moisture condition compatibility", () => {
      // E value with specific moisture condition (should use wet_or_dry)
      const eInput: TemperatureFactorInput = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.E,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      // Fb value with wet_or_dry (should use specific condition)
      const fbInput: TemperatureFactorInput = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.WET_OR_DRY,
      };

      const eResult = validateTemperatureFactorInput(eInput);
      const fbResult = validateTemperatureFactorInput(fbInput);

      expect(eResult.warnings.length).toBeGreaterThan(0);
      expect(fbResult.warnings.length).toBeGreaterThan(0);
    });
  });

  describe("getTemperatureFactor", () => {
    describe("Tension and Elasticity Values (Ft, E, Emin)", () => {
      it("should return correct factors for different temperature ranges", () => {
        const testCases = [
          {
            temp: 70,
            expected: TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.LOW_TEMP,
          }, // T ≤ 100°F
          {
            temp: 100,
            expected: TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.LOW_TEMP,
          }, // T = 100°F
          {
            temp: 110,
            expected: TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.MID_TEMP,
          }, // 100°F < T ≤ 125°F
          {
            temp: 125,
            expected: TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.MID_TEMP,
          }, // T = 125°F
          {
            temp: 140,
            expected: TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.HIGH_TEMP,
          }, // 125°F < T ≤ 150°F
          {
            temp: 150,
            expected: TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.HIGH_TEMP,
          }, // T = 150°F
        ];

        testCases.forEach(({ temp, expected }) => {
          const input: TemperatureFactorInput = {
            temperature: temp,
            designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
            moistureCondition: MOISTURE_CONDITIONS.WET_OR_DRY,
          };

          const result = getTemperatureFactor(input);
          expect(result).toBe(expected);
        });
      });

      it("should handle E and Emin design values", () => {
        const designValues = [DESIGN_VALUE_TYPES.E, DESIGN_VALUE_TYPES.E_MIN];

        designValues.forEach((designValue) => {
          const input: TemperatureFactorInput = {
            temperature: 110,
            designValueType: designValue,
            moistureCondition: MOISTURE_CONDITIONS.WET_OR_DRY,
          };

          const result = getTemperatureFactor(input);
          expect(result).toBe(
            TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.MID_TEMP
          );
        });
      });
    });

    describe("Strength Values - Dry Conditions (Fb, Fv, Fc, Fc_perp)", () => {
      it("should return correct factors for dry conditions", () => {
        const testCases = [
          {
            temp: 70,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.LOW_TEMP,
          }, // T ≤ 100°F
          {
            temp: 100,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.LOW_TEMP,
          }, // T = 100°F
          {
            temp: 110,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP,
          }, // 100°F < T ≤ 125°F
          {
            temp: 125,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP,
          }, // T = 125°F
          {
            temp: 140,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.HIGH_TEMP,
          }, // 125°F < T ≤ 150°F
          {
            temp: 150,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.HIGH_TEMP,
          }, // T = 150°F
        ];

        testCases.forEach(({ temp, expected }) => {
          const input: TemperatureFactorInput = {
            temperature: temp,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            moistureCondition: MOISTURE_CONDITIONS.DRY,
          };

          const result = getTemperatureFactor(input);
          expect(result).toBe(expected);
        });
      });

      it("should handle all strength design values under dry conditions", () => {
        const designValues = [
          DESIGN_VALUE_TYPES.BENDING,
          DESIGN_VALUE_TYPES.SHEAR,
          DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
          DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
        ];

        designValues.forEach((designValue) => {
          const input: TemperatureFactorInput = {
            temperature: 110,
            designValueType: designValue,
            moistureCondition: MOISTURE_CONDITIONS.DRY,
          };

          const result = getTemperatureFactor(input);
          expect(result).toBe(TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP);
        });
      });
    });

    describe("Strength Values - Wet Conditions (Fb, Fv, Fc, Fc_perp)", () => {
      it("should return correct factors for wet conditions", () => {
        const testCases = [
          {
            temp: 70,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.LOW_TEMP,
          }, // T ≤ 100°F
          {
            temp: 100,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.LOW_TEMP,
          }, // T = 100°F
          {
            temp: 110,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.MID_TEMP,
          }, // 100°F < T ≤ 125°F
          {
            temp: 125,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.MID_TEMP,
          }, // T = 125°F
          {
            temp: 140,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.HIGH_TEMP,
          }, // 125°F < T ≤ 150°F
          {
            temp: 150,
            expected: TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.HIGH_TEMP,
          }, // T = 150°F
        ];

        testCases.forEach(({ temp, expected }) => {
          const input: TemperatureFactorInput = {
            temperature: temp,
            designValueType: DESIGN_VALUE_TYPES.BENDING,
            moistureCondition: MOISTURE_CONDITIONS.WET,
          };

          const result = getTemperatureFactor(input);
          expect(result).toBe(expected);
        });
      });
    });

    describe("Excessive Temperature Handling", () => {
      it("should use high temperature factors for excessive temperatures", () => {
        const input: TemperatureFactorInput = {
          temperature: 180, // Above 150°F
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          moistureCondition: MOISTURE_CONDITIONS.DRY,
        };

        const result = getTemperatureFactor(input);
        expect(result).toBe(TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.HIGH_TEMP);
      });
    });

    describe("NDS Table 2.3.3 Verification", () => {
      it("should match exact values from NDS Table 2.3.3", () => {
        // Test all combinations from the table
        const tableValues = [
          // Ft, E, Emin - Wet or Dry
          {
            designValue: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
            moisture: MOISTURE_CONDITIONS.WET_OR_DRY,
            temp: 90,
            expected: 1.0,
          },
          {
            designValue: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
            moisture: MOISTURE_CONDITIONS.WET_OR_DRY,
            temp: 110,
            expected: 0.9,
          },
          {
            designValue: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
            moisture: MOISTURE_CONDITIONS.WET_OR_DRY,
            temp: 140,
            expected: 0.9,
          },

          // Fb, Fv, Fc, Fc_perp - Dry
          {
            designValue: DESIGN_VALUE_TYPES.BENDING,
            moisture: MOISTURE_CONDITIONS.DRY,
            temp: 90,
            expected: 1.0,
          },
          {
            designValue: DESIGN_VALUE_TYPES.BENDING,
            moisture: MOISTURE_CONDITIONS.DRY,
            temp: 110,
            expected: 0.8,
          },
          {
            designValue: DESIGN_VALUE_TYPES.BENDING,
            moisture: MOISTURE_CONDITIONS.DRY,
            temp: 140,
            expected: 0.7,
          },

          // Fb, Fv, Fc, Fc_perp - Wet
          {
            designValue: DESIGN_VALUE_TYPES.BENDING,
            moisture: MOISTURE_CONDITIONS.WET,
            temp: 90,
            expected: 1.0,
          },
          {
            designValue: DESIGN_VALUE_TYPES.BENDING,
            moisture: MOISTURE_CONDITIONS.WET,
            temp: 110,
            expected: 0.7,
          },
          {
            designValue: DESIGN_VALUE_TYPES.BENDING,
            moisture: MOISTURE_CONDITIONS.WET,
            temp: 140,
            expected: 0.5,
          },
        ];

        tableValues.forEach(({ designValue, moisture, temp, expected }) => {
          const input: TemperatureFactorInput = {
            temperature: temp,
            designValueType: designValue,
            moistureCondition: moisture,
          };

          const result = getTemperatureFactor(input);
          expect(result).toBe(expected);
        });
      });
    });

    it("should throw error for invalid input", () => {
      const invalidInput: TemperatureFactorInput = {
        temperature: NaN,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      expect(() => getTemperatureFactor(invalidInput)).toThrow();
    });
  });

  describe("applyTemperatureFactor", () => {
    it("should correctly apply temperature factor to reference value", () => {
      const referenceValue = 1200; // psi
      const input: TemperatureFactorInput = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = applyTemperatureFactor(referenceValue, input);
      const expectedFactor = TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP; // 0.8
      const expected = referenceValue * expectedFactor;

      expect(result).toBe(expected);
      expect(result).toBe(960); // 1200 × 0.8
    });

    it("should handle zero reference value", () => {
      const input: TemperatureFactorInput = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = applyTemperatureFactor(0, input);
      expect(result).toBe(0);
    });

    it("should throw error for invalid reference value", () => {
      const input: TemperatureFactorInput = {
        temperature: 110,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      expect(() => applyTemperatureFactor(-100, input)).toThrow();
      expect(() => applyTemperatureFactor(NaN, input)).toThrow();
      expect(() => applyTemperatureFactor(Infinity, input)).toThrow();
    });
  });

  describe("getMultipleTemperatureFactors", () => {
    it("should calculate factors for multiple design values", () => {
      const baseInput = {
        temperature: 110,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };
      const designValues = [
        DESIGN_VALUE_TYPES.BENDING,
        DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        DESIGN_VALUE_TYPES.E,
      ];

      const result = getMultipleTemperatureFactors(baseInput, designValues);

      expect(result[DESIGN_VALUE_TYPES.BENDING]).toBe(0.8); // Strength value, dry
      expect(result[DESIGN_VALUE_TYPES.TENSION_PARALLEL]).toBe(0.9); // Tension/elasticity
      expect(result[DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL]).toBe(0.8); // Strength value, dry
      expect(result[DESIGN_VALUE_TYPES.E]).toBe(0.9); // Tension/elasticity
    });

    it("should handle invalid design values gracefully", () => {
      const baseInput = {
        temperature: 110,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };
      const designValues = ["invalid_type" as any];

      const result = getMultipleTemperatureFactors(baseInput, designValues);
      expect((result as any)["invalid_type"]).toBe(1.0); // Default factor for invalid values
    });
  });

  describe("getTemperatureFactorAnalysis", () => {
    it("should provide comprehensive analysis", () => {
      const input: TemperatureFactorInput = {
        temperature: 130,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.WET,
      };

      const result = getTemperatureFactorAnalysis(input);

      expect(result.input).toEqual(input);
      expect(result.temperatureFactor).toBe(0.5); // Wet strength at high temp
      expect(result.temperatureRange).toBe("high");
      expect(result.designValueCategory).toBe("strength_wet");
      expect(result.isReversible).toBe(true); // 130°F ≤ 150°F
      expect(result.validation.isValid).toBe(true);
    });

    it("should identify non-reversible temperature effects", () => {
      const input: TemperatureFactorInput = {
        temperature: 180,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = getTemperatureFactorAnalysis(input);

      expect(result.temperatureRange).toBe("excessive");
      expect(result.isReversible).toBe(false);
      expect(result.validation.warnings.length).toBeGreaterThan(0);
    });

    it("should handle invalid input gracefully", () => {
      const input: TemperatureFactorInput = {
        temperature: NaN,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = getTemperatureFactorAnalysis(input);

      expect(result.temperatureFactor).toBe(1.0); // Default factor
      expect(result.validation.isValid).toBe(false);
    });
  });

  describe("isDesignValueAffectedByTemperature", () => {
    it("should identify temperature-affected design values", () => {
      const affectedValues = [
        DESIGN_VALUE_TYPES.BENDING,
        DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        DESIGN_VALUE_TYPES.SHEAR,
        DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
        DESIGN_VALUE_TYPES.E,
        DESIGN_VALUE_TYPES.E_MIN,
      ];

      affectedValues.forEach((value) => {
        expect(isDesignValueAffectedByTemperature(value)).toBe(true);
      });
    });

    it("should identify non-affected design values", () => {
      const nonAffectedValues = ["some_other_value", "undefined_value", ""];

      nonAffectedValues.forEach((value) => {
        expect(isDesignValueAffectedByTemperature(value)).toBe(false);
      });
    });
  });

  describe("getEffectiveMoistureCondition", () => {
    it("should return wet_or_dry for E and Emin values", () => {
      const elasticityValues = [DESIGN_VALUE_TYPES.E, DESIGN_VALUE_TYPES.E_MIN];

      elasticityValues.forEach((value) => {
        const result = getEffectiveMoistureCondition(
          value,
          MOISTURE_CONDITIONS.DRY
        );
        expect(result).toBe(MOISTURE_CONDITIONS.WET_OR_DRY);
      });
    });

    it("should return requested condition for strength values", () => {
      const strengthValues = [
        DESIGN_VALUE_TYPES.BENDING,
        DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
        DESIGN_VALUE_TYPES.SHEAR,
      ];

      strengthValues.forEach((value) => {
        expect(
          getEffectiveMoistureCondition(value, MOISTURE_CONDITIONS.DRY)
        ).toBe(MOISTURE_CONDITIONS.DRY);
        expect(
          getEffectiveMoistureCondition(value, MOISTURE_CONDITIONS.WET)
        ).toBe(MOISTURE_CONDITIONS.WET);
      });
    });

    it("should return requested condition for tension values", () => {
      const result = getEffectiveMoistureCondition(
        DESIGN_VALUE_TYPES.TENSION_PARALLEL,
        MOISTURE_CONDITIONS.WET_OR_DRY
      );
      expect(result).toBe(MOISTURE_CONDITIONS.WET_OR_DRY);
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle boundary temperatures correctly", () => {
      const boundaryTests = [
        { temp: 100, expectedRange: "low" },
        { temp: 100.1, expectedRange: "mid" },
        { temp: 125, expectedRange: "mid" },
        { temp: 125.1, expectedRange: "high" },
        { temp: 150, expectedRange: "high" },
        { temp: 150.1, expectedRange: "excessive" },
      ];

      boundaryTests.forEach(({ temp, expectedRange }) => {
        const input: TemperatureFactorInput = {
          temperature: temp,
          designValueType: DESIGN_VALUE_TYPES.BENDING,
          moistureCondition: MOISTURE_CONDITIONS.DRY,
        };

        const analysis = getTemperatureFactorAnalysis(input);
        expect(analysis.temperatureRange).toBe(expectedRange);
      });
    });

    it("should handle very low temperatures", () => {
      const input: TemperatureFactorInput = {
        temperature: -20,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = getTemperatureFactor(input);
      expect(result).toBe(1.0); // Low temperature uses normal factor
    });

    it("should maintain precision for decimal temperatures", () => {
      const input: TemperatureFactorInput = {
        temperature: 124.9,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        moistureCondition: MOISTURE_CONDITIONS.DRY,
      };

      const result = getTemperatureFactor(input);
      expect(result).toBe(TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP);
    });
  });

  describe("Integration with Constants", () => {
    it("should use all defined temperature constants", () => {
      // Ensure all constants are properly used
      expect(TEMPERATURE_FACTOR_CONSTANTS.TEMP_THRESHOLD_LOW).toBe(100);
      expect(TEMPERATURE_FACTOR_CONSTANTS.TEMP_THRESHOLD_MID).toBe(125);
      expect(TEMPERATURE_FACTOR_CONSTANTS.TEMP_THRESHOLD_HIGH).toBe(150);
      expect(TEMPERATURE_FACTOR_CONSTANTS.MAX_REVERSIBLE_TEMP).toBe(150);
    });

    it("should use all defined temperature factors", () => {
      // Verify factor values match NDS specifications
      expect(TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.LOW_TEMP).toBe(1.0);
      expect(TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.MID_TEMP).toBe(0.9);
      expect(TEMPERATURE_FACTORS.TENSION_AND_ELASTICITY.HIGH_TEMP).toBe(0.9);

      expect(TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.LOW_TEMP).toBe(1.0);
      expect(TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.MID_TEMP).toBe(0.8);
      expect(TEMPERATURE_FACTORS.STRENGTH_VALUES_DRY.HIGH_TEMP).toBe(0.7);

      expect(TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.LOW_TEMP).toBe(1.0);
      expect(TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.MID_TEMP).toBe(0.7);
      expect(TEMPERATURE_FACTORS.STRENGTH_VALUES_WET.HIGH_TEMP).toBe(0.5);
    });
  });
});
