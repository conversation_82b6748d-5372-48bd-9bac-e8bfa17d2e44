/**
 * Size Factor (CF) and Flat Use Factor (Cflu) Calculations
 * 
 * Implements NDS 4.3.6 Size Factor and NDS 4.3.7 Flat Use Factor for adjusting
 * reference design values based on member size and loading orientation.
 * 
 * Based on:
 * - NDS Tables 4A-4F: Size Factors for sawn lumber and glulam
 * - Size factors apply to Fb, Ft, and Fc design values
 * - Flat use factors apply when load is applied to wide face
 * 
 * @fileoverview Size factor calculations for wood structural design
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  DESIGN_VALUE_TYPES,
  DesignValueType,
} from "../../constants";

/**
 * Lumber grades for size factor determination
 */
export const SIZE_FACTOR_LUMBER_GRADES = {
  // Visually graded lumber
  SELECT_STRUCTURAL: 'select_structural',
  NO_1_AND_BETTER: 'no_1_and_better',
  NO_1: 'no_1',
  NO_2: 'no_2',
  NO_3: 'no_3',
  
  // Machine stress rated lumber  
  STUD: 'stud',
  CONSTRUCTION: 'construction',
  STANDARD: 'standard',
  UTILITY: 'utility',
  
  // Machine evaluated lumber
  MSR: 'msr',
  MEL: 'mel',
} as const;

export type SizeFactorLumberGrade = typeof SIZE_FACTOR_LUMBER_GRADES[keyof typeof SIZE_FACTOR_LUMBER_GRADES];

/**
 * Member dimensions for size factor calculations
 */
export const SIZE_FACTOR_DIMENSIONS = {
  // Standard thickness categories
  THICKNESS_2_AND_3: '2_and_3_inch',    // 2" & 3" thick
  THICKNESS_4: '4_inch',                 // 4" thick
  
  // Width categories (depth for most applications)
  WIDTH_2_3_4: '2_3_4_inch',            // 2", 3", & 4" wide
  WIDTH_4: '4_inch',                     // 4" wide (separate from 2_3_4 for some grades)
  WIDTH_5: '5_inch',                     // 5" wide
  WIDTH_6: '6_inch',                     // 6" wide
  WIDTH_8: '8_inch',                     // 8" wide
  WIDTH_10: '10_inch',                   // 10" wide
  WIDTH_12: '12_inch',                   // 12" wide
  WIDTH_14_AND_WIDER: '14_inch_and_wider', // 14" & wider
} as const;

export type SizeFactorDimension = typeof SIZE_FACTOR_DIMENSIONS[keyof typeof SIZE_FACTOR_DIMENSIONS];

/**
 * Size factors from NDS Tables 4A-4F
 */
export const SIZE_FACTORS = {
  // Select Structural, No.1 & Btr, No.1, No.2, No.3 grades
  VISUALLY_GRADED: {
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: { Fb: 1.5, Ft: 1.5, Fc: 1.15 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_5]: { Fb: 1.4, Ft: 1.4, Fc: 1.1 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_6]: { Fb: 1.3, Ft: 1.3, Fc: 1.1 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_8]: { Fb: 1.2, Ft: 1.3, Fc: 1.05 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_10]: { Fb: 1.1, Ft: 1.2, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_12]: { Fb: 1.0, Ft: 1.1, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER]: { Fb: 0.9, Ft: 1.0, Fc: 0.9 },
    },
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_4]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: { Fb: 1.5, Ft: 1.5, Fc: 1.15 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_5]: { Fb: 1.4, Ft: 1.4, Fc: 1.1 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_6]: { Fb: 1.3, Ft: 1.3, Fc: 1.1 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_8]: { Fb: 1.3, Ft: 1.3, Fc: 1.05 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_10]: { Fb: 1.2, Ft: 1.2, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_12]: { Fb: 1.1, Ft: 1.1, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER]: { Fb: 1.0, Ft: 1.0, Fc: 0.9 },
    },
  },
  
  // Stud grade  
  STUD: {
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: { Fb: 1.1, Ft: 1.1, Fc: 1.05 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_5]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_6]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_8]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 }, // Use No.3 Grade values
    },
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_4]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: { Fb: 1.1, Ft: 1.1, Fc: 1.05 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_5]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_6]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_8]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 }, // Use No.3 Grade values
    },
  },
  
  // Construction, Standard, Utility grades
  CONSTRUCTION_STANDARD_UTILITY: {
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_4]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
    },
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_4]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
      [SIZE_FACTOR_DIMENSIONS.WIDTH_4]: { Fb: 1.0, Ft: 1.0, Fc: 1.0 },
    },
  },
} as const;

/**
 * Flat use factors from NDS Tables 4A-4F
 */
export const FLAT_USE_FACTORS = {
  // Standard flat use factors for different thicknesses
  STANDARD: {
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: 1.0,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_4]: 1.1,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_5]: 1.1,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_6]: 1.15,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_8]: 1.15,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_10]: 1.2,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_12]: 1.2,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER]: 1.2,
    },
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_4]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: 1.0, // —
      [SIZE_FACTOR_DIMENSIONS.WIDTH_4]: 1.0,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_5]: 1.05,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_6]: 1.05,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_8]: 1.05,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_10]: 1.1,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_12]: 1.1,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER]: 1.1,
    },
  },
  
  // Special factors for Construction, Standard, Utility grades
  CONSTRUCTION_STANDARD_UTILITY: {
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: 0.4,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_4]: 0.4,
    },
    [SIZE_FACTOR_DIMENSIONS.THICKNESS_4]: {
      [SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4]: 0.4,
      [SIZE_FACTOR_DIMENSIONS.WIDTH_4]: 0.6,
    },
  },
} as const;

/**
 * Size factor limitations and constants
 */
export const SIZE_FACTOR_CONSTANTS = {
  // Maximum allowable dimensions for size factors
  MAX_DEPTH_INCHES: 14,          // 14" maximum depth for tabulated values
  MAX_WIDTH_INCHES: 4,           // 4" maximum width for standard size factors
  
  // Depth dimension thresholds
  DEPTH_THRESHOLD_12: 12,        // 12" depth threshold for some calculations
  
  // Default factors
  DEFAULT_SIZE_FACTOR: 1.0,      // Default when size factor doesn't apply
  DEFAULT_FLAT_USE_FACTOR: 1.0,  // Default when flat use factor doesn't apply
} as const;

/**
 * Input parameters for size factor calculation
 */
export interface SizeFactorInput {
  /** Width (breadth) of member in inches */
  width: number;
  
  /** Depth (height) of member in inches */
  depth: number;
  
  /** Type of design value (Fb, Ft, Fc) */
  designValueType: DesignValueType;
  
  /** Lumber grade */
  lumberGrade: SizeFactorLumberGrade;
  
  /** Whether load is applied to wide face (for flat use factor) */
  isLoadOnWideFace?: boolean;
}

/**
 * Validation result for size factor input
 */
export interface SizeFactorValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Comprehensive size factor analysis result
 */
export interface SizeFactorAnalysis {
  input: SizeFactorInput;
  sizeFactor: number;
  flatUseFactor?: number;
  applicableDesignValue: boolean;
  dimensionCategory: string;
  gradeCategory: string;
  validation: SizeFactorValidation;
}

/**
 * Validates size factor input parameters
 * 
 * @param input - Input parameters to validate
 * @returns Validation result with errors and warnings
 * 
 * @example
 * ```typescript
 * const input = {
 *   width: 3.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL
 * };
 * const validation = validateSizeFactorInput(input);
 * ```
 */
export function validateSizeFactorInput(
  input: SizeFactorInput
): SizeFactorValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate width
  if (typeof input.width !== 'number' || 
      isNaN(input.width) || 
      !isFinite(input.width)) {
    errors.push('Width must be a valid number');
  } else if (input.width <= 0) {
    errors.push('Width must be positive');
  } else if (input.width > SIZE_FACTOR_CONSTANTS.MAX_WIDTH_INCHES) {
    warnings.push(`Width exceeds ${SIZE_FACTOR_CONSTANTS.MAX_WIDTH_INCHES}" - verify size factor applicability`);
  }
  
  // Validate depth
  if (typeof input.depth !== 'number' || 
      isNaN(input.depth) || 
      !isFinite(input.depth)) {
    errors.push('Depth must be a valid number');
  } else if (input.depth <= 0) {
    errors.push('Depth must be positive');
  } else if (input.depth > SIZE_FACTOR_CONSTANTS.MAX_DEPTH_INCHES) {
    warnings.push(`Depth exceeds ${SIZE_FACTOR_CONSTANTS.MAX_DEPTH_INCHES}" - may require special analysis`);
  }
  
  // Validate design value type
  if (!Object.values(DESIGN_VALUE_TYPES).includes(input.designValueType)) {
    errors.push(`Invalid design value type: ${input.designValueType}`);
  }
  
  // Check if design value is affected by size factors
  const affectedValues: DesignValueType[] = [
    DESIGN_VALUE_TYPES.BENDING,
    DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
  ];
  if (!affectedValues.includes(input.designValueType)) {
    warnings.push(`Size factor does not apply to design value: ${input.designValueType}`);
  }
  
  // Validate lumber grade
  if (!Object.values(SIZE_FACTOR_LUMBER_GRADES).includes(input.lumberGrade)) {
    errors.push(`Invalid lumber grade: ${input.lumberGrade}`);
  }
  
  // Check dimension compatibility
  if (input.width > 0 && input.depth > 0) {
    if (input.width > input.depth) {
      warnings.push('Width is greater than depth - verify member orientation');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Determines dimension category for size factor lookup
 * 
 * @param width - Member width in inches
 * @param depth - Member depth in inches
 * @returns Dimension category information
 */
export function getDimensionCategory(
  width: number,
  depth: number
): { thickness: SizeFactorDimension; widthCategory: SizeFactorDimension } {
  // Determine thickness category
  let thickness: SizeFactorDimension;
  if (width <= 3.5) {
    thickness = SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3;
  } else {
    thickness = SIZE_FACTOR_DIMENSIONS.THICKNESS_4;
  }
  
  // Determine width (depth) category
  let widthCategory: SizeFactorDimension;
  if (depth <= 4) {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4;
  } else if (depth <= 5) {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_5;
  } else if (depth <= 6) {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_6;
  } else if (depth <= 8) {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_8;
  } else if (depth <= 10) {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_10;
  } else if (depth <= 12) {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_12;
  } else {
    widthCategory = SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER;
  }
  
  return { thickness, widthCategory };
}

/**
 * Gets size factor for specified lumber grade and dimensions
 * 
 * @param input - Input parameters for size factor calculation
 * @returns Size factor (CF)
 * @throws Error if input validation fails
 * 
 * @example
 * ```typescript
 * const input = {
 *   width: 3.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL
 * };
 * const factor = getSizeFactor(input);
 * console.log(factor); // 1.1 for 2x12 Select Structural bending
 * ```
 */
export function getSizeFactor(input: SizeFactorInput): number {
  const validation = validateSizeFactorInput(input);
  if (!validation.isValid) {
    throw new Error(`Invalid size factor input: ${validation.errors.join(', ')}`);
  }
  
  // Check if size factor applies to this design value
  const applicableValues: DesignValueType[] = [
    DESIGN_VALUE_TYPES.BENDING,
    DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
  ];
  
  if (!applicableValues.includes(input.designValueType)) {
    return SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR;
  }
  
  const { thickness, widthCategory } = getDimensionCategory(input.width, input.depth);
  
  // Determine factor table based on grade
  let factorTable: any;
  
  switch (input.lumberGrade) {
    case SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL:
    case SIZE_FACTOR_LUMBER_GRADES.NO_1_AND_BETTER:
    case SIZE_FACTOR_LUMBER_GRADES.NO_1:
    case SIZE_FACTOR_LUMBER_GRADES.NO_2:
    case SIZE_FACTOR_LUMBER_GRADES.NO_3:
      factorTable = SIZE_FACTORS.VISUALLY_GRADED;
      break;
      
    case SIZE_FACTOR_LUMBER_GRADES.STUD:
      factorTable = SIZE_FACTORS.STUD;
      break;
      
    case SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION:
    case SIZE_FACTOR_LUMBER_GRADES.STANDARD:
    case SIZE_FACTOR_LUMBER_GRADES.UTILITY:
      factorTable = SIZE_FACTORS.CONSTRUCTION_STANDARD_UTILITY;
      break;
      
    default:
      return SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR;
  }
  
  // Get factor based on design value type
  const dimensionFactors = factorTable[thickness]?.[widthCategory];
  if (!dimensionFactors) {
    return SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR;
  }
  
  let designValueKey: 'Fb' | 'Ft' | 'Fc';
  switch (input.designValueType) {
    case DESIGN_VALUE_TYPES.BENDING:
      designValueKey = 'Fb';
      break;
    case DESIGN_VALUE_TYPES.TENSION_PARALLEL:
      designValueKey = 'Ft';
      break;
    case DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL:
      designValueKey = 'Fc';
      break;
    default:
      return SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR;
  }
  
  return dimensionFactors[designValueKey] ?? SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR;
}

/**
 * Gets flat use factor when load is applied to wide face
 * 
 * @param input - Input parameters including member dimensions and grade
 * @returns Flat use factor (Cflu)
 * 
 * @example
 * ```typescript
 * const input = {
 *   width: 3.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
 *   isLoadOnWideFace: true
 * };
 * const factor = getFlatUseFactor(input);
 * console.log(factor); // 1.2 for load on wide face
 * ```
 */
export function getFlatUseFactor(input: SizeFactorInput): number {
  if (!input.isLoadOnWideFace) {
    return SIZE_FACTOR_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
  }
  
  const validation = validateSizeFactorInput(input);
  if (!validation.isValid) {
    return SIZE_FACTOR_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
  }
  
  // Flat use factor only applies to bending
  if (input.designValueType !== DESIGN_VALUE_TYPES.BENDING) {
    return SIZE_FACTOR_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
  }
  
  const { thickness, widthCategory } = getDimensionCategory(input.width, input.depth);
  
  // Determine factor table based on grade
  let factorTable: any;
  
  const constructionGrades: SizeFactorLumberGrade[] = [
    SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION,
    SIZE_FACTOR_LUMBER_GRADES.STANDARD,
    SIZE_FACTOR_LUMBER_GRADES.UTILITY
  ];
  
  if (constructionGrades.includes(input.lumberGrade)) {
    factorTable = FLAT_USE_FACTORS.CONSTRUCTION_STANDARD_UTILITY;
  } else {
    factorTable = FLAT_USE_FACTORS.STANDARD;
  }
  
  return factorTable[thickness]?.[widthCategory] ?? SIZE_FACTOR_CONSTANTS.DEFAULT_FLAT_USE_FACTOR;
}

/**
 * Applies size factor to a reference design value
 * 
 * @param referenceValue - Reference design value
 * @param input - Input parameters for size factor calculation
 * @returns Adjusted design value
 * @throws Error if reference value is invalid or input validation fails
 * 
 * @example
 * ```typescript
 * const adjustedValue = applySizeFactor(1200, {
 *   width: 3.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL
 * });
 * console.log(adjustedValue); // 1320 (1200 × 1.1)
 * ```
 */
export function applySizeFactor(
  referenceValue: number,
  input: SizeFactorInput
): number {
  if (typeof referenceValue !== 'number' || 
      isNaN(referenceValue) || 
      !isFinite(referenceValue) ||
      referenceValue < 0) {
    throw new Error('Reference value must be a non-negative finite number');
  }
  
  const sizeFactor = getSizeFactor(input);
  const flatUseFactor = getFlatUseFactor(input);
  
  return referenceValue * sizeFactor * flatUseFactor;
}

/**
 * Provides comprehensive analysis of size factor calculation
 * 
 * @param input - Input parameters for analysis
 * @returns Detailed analysis including factors, categories, and validation
 * 
 * @example
 * ```typescript
 * const analysis = getSizeFactorAnalysis({
 *   width: 3.5,
 *   depth: 11.25,
 *   designValueType: DESIGN_VALUE_TYPES.BENDING,
 *   lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
 *   isLoadOnWideFace: true
 * });
 * console.log(analysis.sizeFactor); // 1.1
 * console.log(analysis.flatUseFactor); // 1.2
 * ```
 */
export function getSizeFactorAnalysis(
  input: SizeFactorInput
): SizeFactorAnalysis {
  const validation = validateSizeFactorInput(input);
  
  if (!validation.isValid) {
    return {
      input,
      sizeFactor: SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR,
      applicableDesignValue: false,
      dimensionCategory: 'invalid',
      gradeCategory: 'invalid',
      validation,
    };
  }
  
  const sizeFactor = getSizeFactor(input);
  const flatUseFactor = input.isLoadOnWideFace ? getFlatUseFactor(input) : undefined;
  
  const { thickness, widthCategory } = getDimensionCategory(input.width, input.depth);
  
  // Determine applicable design values
  const applicableValues: DesignValueType[] = [
    DESIGN_VALUE_TYPES.BENDING,
    DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
  ];
  const applicableDesignValue = applicableValues.includes(input.designValueType);
  
  // Determine grade category
  let gradeCategory: string;
  const visuallyGradedGrades: SizeFactorLumberGrade[] = [
    SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    SIZE_FACTOR_LUMBER_GRADES.NO_1_AND_BETTER,
    SIZE_FACTOR_LUMBER_GRADES.NO_1,
    SIZE_FACTOR_LUMBER_GRADES.NO_2,
    SIZE_FACTOR_LUMBER_GRADES.NO_3
  ];
  
  if (visuallyGradedGrades.includes(input.lumberGrade)) {
    gradeCategory = 'visually_graded';
  } else if (input.lumberGrade === SIZE_FACTOR_LUMBER_GRADES.STUD) {
    gradeCategory = 'stud';
  } else {
    gradeCategory = 'construction_standard_utility';
  }
  
  return {
    input,
    sizeFactor,
    flatUseFactor,
    applicableDesignValue,
    dimensionCategory: `${thickness}_${widthCategory}`,
    gradeCategory,
    validation,
  };
}

/**
 * Checks if a design value is affected by size factors
 * 
 * @param designValueType - Type of design value to check
 * @returns True if the design value is affected by size factors
 * 
 * @example
 * ```typescript
 * const isAffected = isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.BENDING);
 * console.log(isAffected); // true
 * ```
 */
export function isDesignValueAffectedBySizeFactor(
  designValueType: DesignValueType
): boolean {
  const applicableValues: DesignValueType[] = [
    DESIGN_VALUE_TYPES.BENDING,
    DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
  ];
  
  return applicableValues.includes(designValueType);
} 