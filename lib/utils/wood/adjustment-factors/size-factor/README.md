# Size Factor (CF)

## Overview

This module implements the size factor (CF) calculations according to the National Design Specification (NDS) for Wood Construction, Section 4.3.6. The size factor adjusts bending design values to account for the effect of member size on strength.

## Key Concepts

### When Size Factor Applies

The size factor applies to:
- **Bending design values (Fb)** for visually graded lumber
- **Dimension lumber** (2" to 4" thick)
- **Beams and stringers** (5" x 5" and larger)
- **Posts and timbers** (5" x 5" and larger, square or rectangular)

### Size Categories

1. **Dimension Lumber** (2" to 4" thick):
   - 2x4, 2x6, 2x8, 2x10, 2x12, 2x14
   - 4x4, 4x6, 4x8, 4x10, 4x12, 4x14

2. **Beams and Stringers** (5" and thicker, depth > width):
   - 6x8, 6x10, 6x12, 8x12, 8x16, etc.

3. **Posts and Timbers** (5" x 5" and larger, square or nearly square):
   - 6x6, 8x8, 10x10, 12x12, etc.

### Size Factor Values

- **CF ≤ 1.0** (size factor is always 1.0 or less)
- Larger members have smaller size factors
- Values depend on lumber grade and cross-sectional dimensions

## API Reference

### Main Functions

#### `getSizeFactor(input)`

Calculates the size factor for a given member configuration.

**Parameters:**
- `input.lumberGrade`: Grade of lumber ('select_structural', 'no_1', 'no_2', etc.)
- `input.width`: Member width (in)
- `input.depth`: Member depth (in)
- `input.designValue`: Design value type ('Fb')

**Returns:**
- `factor`: The size factor (≤ 1.0)
- `applicable`: Whether the factor applies
- `dimension`: Dimension category
- `explanation`: Detailed explanation

#### `validateSizeFactorInput(input)`

Validates input parameters and provides detailed validation results.

#### `getSizeFactorAnalysis(input)`

Provides comprehensive analysis including dimension categorization and factor selection.

#### `getDimensionCategory(width, depth)`

Determines the dimension category (dimension lumber, beams & stringers, posts & timbers).

### Usage Examples

```typescript
import { getSizeFactor } from './size-factor';

// Example 1: 2x10 Select Structural
const dimension = getSizeFactor({
  lumberGrade: 'select_structural',
  width: 1.5,    // actual width
  depth: 9.25,   // actual depth
  designValue: 'Fb'
});

console.log(dimension.factor); // e.g., 1.0

// Example 2: 6x12 Beam
const beam = getSizeFactor({
  lumberGrade: 'select_structural', 
  width: 5.5,
  depth: 11.5,
  designValue: 'Fb'
});

console.log(beam.factor); // e.g., 0.9
```

### Constants and Types

```typescript
// Available lumber grades
export const SIZE_FACTOR_LUMBER_GRADES = {
  SELECT_STRUCTURAL: 'select_structural',
  NO_1: 'no_1',
  NO_2: 'no_2',
  NO_3: 'no_3',
  STUD: 'stud',
  CONSTRUCTION: 'construction',
  STANDARD: 'standard',
  UTILITY: 'utility'
} as const;

// Dimension categories
export const SIZE_FACTOR_DIMENSIONS = {
  DIMENSION_LUMBER: 'dimension_lumber',
  BEAMS_AND_STRINGERS: 'beams_and_stringers', 
  POSTS_AND_TIMBERS: 'posts_and_timbers'
} as const;
```

## Design Considerations

### Member Selection

- Consider size factor when comparing different lumber sizes
- Larger members may have lower allowable stresses due to size factor
- Balance member efficiency with size factor effects

### Grade Selection

- Higher grades typically have better size factors
- Select Structural generally has CF = 1.0 for common sizes
- Lower grades have reduced size factors for larger dimensions

## Files

- `size-factor.ts` - Main implementation
- `size-factor.test.ts` - Comprehensive test suite
- `size-factor-examples.ts` - Usage examples and scenarios
- `README.md` - This documentation

## References

- NDS 2018, Section 4.3.6 - Size Factor, CF
- NDS 2018, Table 4A - Adjustment Factors for Sawn Lumber
- NDS Supplement - Design Values for Visually Graded Lumber 