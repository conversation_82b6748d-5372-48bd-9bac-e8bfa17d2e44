/**
 * Size Factor Usage Examples
 * 
 * This file demonstrates how to use the size factor functionality for common
 * lumber sizing scenarios in structural wood design.
 * 
 * @fileoverview Examples of size factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import {
  getSizeFactor,
  getFlatUseFactor,
  applySizeFactor,
  getSizeFactorAnalysis,
  SIZE_FACTOR_LUMBER_GRADES,
  isDesignValueAffectedBySizeFactor,
  type SizeFactorInput,
} from './size-factor';

import { DESIGN_VALUE_TYPES } from '../../constants';

/**
 * Example 1: Basic size factor calculation for a 2x10 Select Structural beam
 */
function example1_Basic2x10Beam() {
  console.log('\n=== Example 1: 2x10 Select Structural Beam ===');
  
  const input: SizeFactorInput = {
    width: 1.5,    // 2" nominal = 1.5" actual
    depth: 9.25,   // 10" nominal = 9.25" actual
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
  };
  
  const sizeFactor = getSizeFactor(input);
  console.log(`Size factor (CF) for 2x10 Select Structural bending: ${sizeFactor}`);
  
  // Apply to a reference bending value
  const referenceFb = 1200; // psi
  const adjustedFb = applySizeFactor(referenceFb, input);
  console.log(`Reference Fb: ${referenceFb} psi`);
  console.log(`Adjusted Fb: ${adjustedFb} psi`);
}

/**
 * Example 2: Size factor with flat use factor for joist on edge vs flat
 */
function example2_JoistOrientation() {
  console.log('\n=== Example 2: 2x12 Joist - Edge vs Flat Loading ===');
  
  const baseInput: SizeFactorInput = {
    width: 1.5,
    depth: 11.25,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: SIZE_FACTOR_LUMBER_GRADES.NO_2,
  };
  
  // Loading on narrow edge (normal orientation)
  const edgeLoading = getSizeFactor(baseInput);
  console.log(`Edge loading size factor: ${edgeLoading}`);
  
  // Loading on wide face (flat orientation)
  const flatInput = { ...baseInput, isLoadOnWideFace: true };
  const sizeFactor = getSizeFactor(flatInput);
  const flatUseFactor = getFlatUseFactor(flatInput);
  
  console.log(`Flat loading size factor: ${sizeFactor}`);
  console.log(`Flat use factor: ${flatUseFactor}`);
  console.log(`Combined factor for flat loading: ${sizeFactor * flatUseFactor}`);
  
  // Apply to reference value
  const referenceFb = 900; // psi
  const edgeAdjusted = applySizeFactor(referenceFb, baseInput);
  const flatAdjusted = applySizeFactor(referenceFb, flatInput);
  
  console.log(`Reference Fb: ${referenceFb} psi`);
  console.log(`Edge loading adjusted Fb: ${edgeAdjusted} psi`);
  console.log(`Flat loading adjusted Fb: ${flatAdjusted} psi`);
}

/**
 * Example 3: Different lumber grades comparison
 */
function example3_GradeComparison() {
  console.log('\n=== Example 3: Grade Comparison for 2x8 Lumber ===');
  
  const grades = [
    SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    SIZE_FACTOR_LUMBER_GRADES.NO_1,
    SIZE_FACTOR_LUMBER_GRADES.NO_2,
    SIZE_FACTOR_LUMBER_GRADES.STUD,
    SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION,
  ];
  
  grades.forEach(grade => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 7.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: grade,
    };
    
    const sizeFactor = getSizeFactor(input);
    console.log(`${grade}: CF = ${sizeFactor}`);
  });
}

/**
 * Example 4: Comprehensive analysis for a structural member
 */
function example4_ComprehensiveAnalysis() {
  console.log('\n=== Example 4: Comprehensive Analysis for 4x12 Beam ===');
  
  const input: SizeFactorInput = {
    width: 3.5,
    depth: 11.25,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    isLoadOnWideFace: false,
  };
  
  const analysis = getSizeFactorAnalysis(input);
  
  console.log('Analysis Results:');
  console.log(`- Dimension category: ${analysis.dimensionCategory}`);
  console.log(`- Grade category: ${analysis.gradeCategory}`);
  console.log(`- Size factor: ${analysis.sizeFactor}`);
  console.log(`- Applicable to ${input.designValueType}: ${analysis.applicableDesignValue}`);
  console.log(`- Validation valid: ${analysis.validation.isValid}`);
  
  if (analysis.validation.warnings.length > 0) {
    console.log('Warnings:', analysis.validation.warnings);
  }
}

/**
 * Example 5: Design value compatibility check
 */
function example5_DesignValueCompatibility() {
  console.log('\n=== Example 5: Design Value Compatibility ===');
  
  const designValues = [
    DESIGN_VALUE_TYPES.BENDING,
    DESIGN_VALUE_TYPES.TENSION_PARALLEL,
    DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
    DESIGN_VALUE_TYPES.SHEAR,
    DESIGN_VALUE_TYPES.E,
    DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
  ];
  
  designValues.forEach(designValue => {
    const isAffected = isDesignValueAffectedBySizeFactor(designValue);
    console.log(`${designValue}: ${isAffected ? 'Affected' : 'Not affected'} by size factors`);
  });
}

/**
 * Example 6: Size optimization study
 */
function example6_SizeOptimization() {
  console.log('\n=== Example 6: Size Optimization Study ===');
  
  const sizes = [
    { name: '2x6', width: 1.5, depth: 5.5 },
    { name: '2x8', width: 1.5, depth: 7.25 },
    { name: '2x10', width: 1.5, depth: 9.25 },
    { name: '2x12', width: 1.5, depth: 11.25 },
    { name: '4x8', width: 3.5, depth: 7.25 },
    { name: '4x10', width: 3.5, depth: 9.25 },
    { name: '4x12', width: 3.5, depth: 11.25 },
  ];
  
  const referenceFb = 1000; // psi
  
  console.log('Size factor study for Select Structural grade:');
  console.log('Size\tCF\tAdjusted Fb');
  console.log('------------------------');
  
  sizes.forEach(size => {
    const input: SizeFactorInput = {
      width: size.width,
      depth: size.depth,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    
    const sizeFactor = getSizeFactor(input);
    const adjustedFb = referenceFb * sizeFactor;
    
    console.log(`${size.name}\t${sizeFactor}\t${adjustedFb} psi`);
  });
}

/**
 * Run all examples
 */
function runAllExamples() {
  console.log('SIZE FACTOR CALCULATION EXAMPLES');
  console.log('================================');
  
  example1_Basic2x10Beam();
  example2_JoistOrientation();
  example3_GradeComparison();
  example4_ComprehensiveAnalysis();
  example5_DesignValueCompatibility();
  example6_SizeOptimization();
  
  console.log('\n=== Examples Complete ===');
}

// Export examples for use in other modules
export {
  example1_Basic2x10Beam,
  example2_JoistOrientation,
  example3_GradeComparison,
  example4_ComprehensiveAnalysis,
  example5_DesignValueCompatibility,
  example6_SizeOptimization,
  runAllExamples,
};

// Run examples if this module is executed directly
if (require.main === module) {
  runAllExamples();
} 