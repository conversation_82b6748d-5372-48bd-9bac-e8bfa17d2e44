/**
 * Tests for Size Factor (CF) and Flat Use Factor (Cflu) Calculations
 * 
 * @fileoverview Comprehensive test suite for size factor calculations
 * <AUTHOR> Engineering Application
 * @version 1.0.0
 */

import { describe, test, expect } from '@jest/globals';
import {
  // Constants
  SIZE_FACTOR_LUMBER_GRADES,
  SIZE_FACTOR_DIMENSIONS,
  SIZE_FACTORS,
  FLAT_USE_FACTORS,
  SIZE_FACTOR_CONSTANTS,
  
  // Types
  type SizeFactorInput,
  type SizeFactorValidation,
  type SizeFactorAnalysis,
  
  // Functions
  validateSizeFactorInput,
  getDimensionCategory,
  getSizeFactor,
  getFlatUseFactor,
  applySizeFactor,
  getSizeFactorAnalysis,
  isDesignValueAffectedBySizeFactor,
} from './size-factor';

import { DESIGN_VALUE_TYPES } from '../../constants';

describe('Size Factor Constants', () => {
  test('should have correct lumber grades defined', () => {
    expect(SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL).toBe('select_structural');
    expect(SIZE_FACTOR_LUMBER_GRADES.NO_1_AND_BETTER).toBe('no_1_and_better');
    expect(SIZE_FACTOR_LUMBER_GRADES.STUD).toBe('stud');
    expect(SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION).toBe('construction');
  });

  test('should have correct dimension categories defined', () => {
    expect(SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3).toBe('2_and_3_inch');
    expect(SIZE_FACTOR_DIMENSIONS.THICKNESS_4).toBe('4_inch');
    expect(SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4).toBe('2_3_4_inch');
    expect(SIZE_FACTOR_DIMENSIONS.WIDTH_12).toBe('12_inch');
  });

  test('should have size factors for visually graded lumber', () => {
    const factors = SIZE_FACTORS.VISUALLY_GRADED[SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3][SIZE_FACTOR_DIMENSIONS.WIDTH_12];
    expect(factors.Fb).toBe(1.0);
    expect(factors.Ft).toBe(1.1);
    expect(factors.Fc).toBe(1.0);
  });

  test('should have flat use factors', () => {
    const flatFactor = FLAT_USE_FACTORS.STANDARD[SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3][SIZE_FACTOR_DIMENSIONS.WIDTH_12];
    expect(flatFactor).toBe(1.2);
  });

  test('should have correct default values', () => {
    expect(SIZE_FACTOR_CONSTANTS.DEFAULT_SIZE_FACTOR).toBe(1.0);
    expect(SIZE_FACTOR_CONSTANTS.DEFAULT_FLAT_USE_FACTOR).toBe(1.0);
    expect(SIZE_FACTOR_CONSTANTS.MAX_DEPTH_INCHES).toBe(14);
  });
});

describe('validateSizeFactorInput', () => {
  const validInput: SizeFactorInput = {
    width: 3.5,
    depth: 11.25,
    designValueType: DESIGN_VALUE_TYPES.BENDING,
    lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
  };

  test('should validate correct input', () => {
    const result = validateSizeFactorInput(validInput);
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('should reject invalid width values', () => {
    const invalidInputs = [
      { ...validInput, width: 0 },
      { ...validInput, width: -1 },
      { ...validInput, width: NaN },
      { ...validInput, width: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateSizeFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid depth values', () => {
    const invalidInputs = [
      { ...validInput, depth: 0 },
      { ...validInput, depth: -1 },
      { ...validInput, depth: NaN },
      { ...validInput, depth: Infinity },
    ];

    invalidInputs.forEach(input => {
      const result = validateSizeFactorInput(input);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  test('should reject invalid design value types', () => {
    const result = validateSizeFactorInput({
      ...validInput,
      designValueType: 'invalid' as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Invalid design value type: invalid');
  });

  test('should reject invalid lumber grades', () => {
    const result = validateSizeFactorInput({
      ...validInput,
      lumberGrade: 'invalid' as any,
    });
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('Invalid lumber grade: invalid');
  });

  test('should warn about large dimensions', () => {
    const result = validateSizeFactorInput({
      ...validInput,
      width: 6,
      depth: 16,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings.length).toBeGreaterThan(0);
  });

  test('should warn about design values not affected by size factors', () => {
    const result = validateSizeFactorInput({
      ...validInput,
      designValueType: DESIGN_VALUE_TYPES.E,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Size factor does not apply to design value: E');
  });

  test('should warn when width is greater than depth', () => {
    const result = validateSizeFactorInput({
      ...validInput,
      width: 12,
      depth: 3.5,
    });
    expect(result.isValid).toBe(true);
    expect(result.warnings).toContain('Width is greater than depth - verify member orientation');
  });
});

describe('getDimensionCategory', () => {
  test('should categorize 2x4 lumber correctly', () => {
    const result = getDimensionCategory(1.5, 3.5);
    expect(result.thickness).toBe(SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3);
    expect(result.widthCategory).toBe(SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4);
  });

  test('should categorize 2x12 lumber correctly', () => {
    const result = getDimensionCategory(1.5, 11.25);
    expect(result.thickness).toBe(SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3);
    expect(result.widthCategory).toBe(SIZE_FACTOR_DIMENSIONS.WIDTH_12);
  });

  test('should categorize 4x10 lumber correctly', () => {
    const result = getDimensionCategory(3.5, 9.25);
    expect(result.thickness).toBe(SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3);
    expect(result.widthCategory).toBe(SIZE_FACTOR_DIMENSIONS.WIDTH_10);
  });

  test('should categorize 6x12 lumber correctly', () => {
    const result = getDimensionCategory(5.5, 11.25);
    expect(result.thickness).toBe(SIZE_FACTOR_DIMENSIONS.THICKNESS_4);
    expect(result.widthCategory).toBe(SIZE_FACTOR_DIMENSIONS.WIDTH_12);
  });

  test('should categorize large lumber correctly', () => {
    const result = getDimensionCategory(5.5, 16);
    expect(result.thickness).toBe(SIZE_FACTOR_DIMENSIONS.THICKNESS_4);
    expect(result.widthCategory).toBe(SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER);
  });

  test('should handle boundary conditions', () => {
    const tests = [
      { width: 3.5, expectedThickness: SIZE_FACTOR_DIMENSIONS.THICKNESS_2_AND_3 },
      { width: 4.0, expectedThickness: SIZE_FACTOR_DIMENSIONS.THICKNESS_4 },
      { depth: 4.0, expectedWidth: SIZE_FACTOR_DIMENSIONS.WIDTH_2_3_4 },
      { depth: 5.0, expectedWidth: SIZE_FACTOR_DIMENSIONS.WIDTH_5 },
      { depth: 12.0, expectedWidth: SIZE_FACTOR_DIMENSIONS.WIDTH_12 },
      { depth: 14.0, expectedWidth: SIZE_FACTOR_DIMENSIONS.WIDTH_14_AND_WIDER },
    ];

    tests.forEach(test => {
      const result = getDimensionCategory(test.width || 1.5, test.depth || 1.5);
      if (test.expectedThickness) {
        expect(result.thickness).toBe(test.expectedThickness);
      }
      if (test.expectedWidth) {
        expect(result.widthCategory).toBe(test.expectedWidth);
      }
    });
  });
});

describe('getSizeFactor', () => {
  test('should return correct size factor for 2x12 Select Structural bending', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should return correct size factor for 2x4 Select Structural bending', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.5);
  });

  test('should return correct size factor for tension parallel', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.1);
  });

  test('should return correct size factor for compression parallel', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.15);
  });

  test('should return 1.0 for design values not affected by size factors', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.E,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should handle Stud grade correctly', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.STUD,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.1);
  });

  test('should handle Construction grade correctly', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should handle all visually graded lumber grades', () => {
    const grades = [
      SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      SIZE_FACTOR_LUMBER_GRADES.NO_1_AND_BETTER,
      SIZE_FACTOR_LUMBER_GRADES.NO_1,
      SIZE_FACTOR_LUMBER_GRADES.NO_2,
      SIZE_FACTOR_LUMBER_GRADES.NO_3,
    ];

    grades.forEach(grade => {
      const input: SizeFactorInput = {
        width: 1.5,
        depth: 3.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: grade,
      };
      const factor = getSizeFactor(input);
      expect(factor).toBe(1.5);
    });
  });

  test('should throw error for invalid input', () => {
    const input: SizeFactorInput = {
      width: -1,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    expect(() => getSizeFactor(input)).toThrow();
  });

  test('should handle large dimensions with default factor', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 16,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(0.9); // 14" and wider factor
  });
});

describe('getFlatUseFactor', () => {
  test('should return 1.0 when load is not on wide face', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: false,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should return correct flat use factor for 2x12 on wide face', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.2);
  });

  test('should return 1.0 for non-bending design values', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.TENSION_PARALLEL,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should handle Construction grade flat use factors', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(0.4);
  });

  test('should handle invalid input gracefully', () => {
    const input: SizeFactorInput = {
      width: -1,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const factor = getFlatUseFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should handle different dimensions correctly', () => {
    const tests = [
      { width: 1.5, depth: 5.5, expected: 1.15 },
      { width: 1.5, depth: 7.25, expected: 1.15 },
      { width: 1.5, depth: 9.25, expected: 1.2 },
      { width: 5.5, depth: 5.5, expected: 1.05 },
    ];

    tests.forEach(test => {
      const input: SizeFactorInput = {
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
        isLoadOnWideFace: true,
      };
      const factor = getFlatUseFactor(input);
      expect(factor).toBe(test.expected);
    });
  });
});

describe('applySizeFactor', () => {
  test('should apply size factor to reference value', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const adjustedValue = applySizeFactor(1000, input);
    expect(adjustedValue).toBe(1500); // 1000 × 1.5
  });

  test('should apply both size factor and flat use factor', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 5.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    const adjustedValue = applySizeFactor(1000, input);
    expect(adjustedValue).toBeCloseTo(1495, 1);
  });

  test('should throw error for invalid reference value', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    
    expect(() => applySizeFactor(-1, input)).toThrow();
    expect(() => applySizeFactor(NaN, input)).toThrow();
    expect(() => applySizeFactor(Infinity, input)).toThrow();
  });

  test('should handle zero reference value', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    const adjustedValue = applySizeFactor(0, input);
    expect(adjustedValue).toBe(0);
  });
});

describe('getSizeFactorAnalysis', () => {
  test('should provide comprehensive analysis for valid input', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };
    
    const analysis = getSizeFactorAnalysis(input);
    
    expect(analysis.input).toEqual(input);
    expect(analysis.sizeFactor).toBe(1.0);
    expect(analysis.flatUseFactor).toBe(1.2);
    expect(analysis.applicableDesignValue).toBe(true);
    expect(analysis.dimensionCategory).toBe('2_and_3_inch_12_inch');
    expect(analysis.gradeCategory).toBe('visually_graded');
    expect(analysis.validation.isValid).toBe(true);
  });

  test('should handle invalid input gracefully', () => {
    const input: SizeFactorInput = {
      width: -1,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
    };
    
    const analysis = getSizeFactorAnalysis(input);
    
    expect(analysis.sizeFactor).toBe(1.0);
    expect(analysis.applicableDesignValue).toBe(false);
    expect(analysis.dimensionCategory).toBe('invalid');
    expect(analysis.gradeCategory).toBe('invalid');
    expect(analysis.validation.isValid).toBe(false);
  });

  test('should categorize grades correctly', () => {
    const tests = [
      { grade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL, expected: 'visually_graded' },
      { grade: SIZE_FACTOR_LUMBER_GRADES.NO_1, expected: 'visually_graded' },
      { grade: SIZE_FACTOR_LUMBER_GRADES.STUD, expected: 'stud' },
      { grade: SIZE_FACTOR_LUMBER_GRADES.CONSTRUCTION, expected: 'construction_standard_utility' },
      { grade: SIZE_FACTOR_LUMBER_GRADES.UTILITY, expected: 'construction_standard_utility' },
    ];

    tests.forEach(test => {
      const input: SizeFactorInput = {
        width: 1.5,
        depth: 3.5,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: test.grade,
      };
      
      const analysis = getSizeFactorAnalysis(input);
      expect(analysis.gradeCategory).toBe(test.expected);
    });
  });

  test('should not include flat use factor when not on wide face', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 11.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: false,
    };
    
    const analysis = getSizeFactorAnalysis(input);
    expect(analysis.flatUseFactor).toBeUndefined();
  });
});

describe('isDesignValueAffectedBySizeFactor', () => {
  test('should return true for affected design values', () => {
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.BENDING)).toBe(true);
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.TENSION_PARALLEL)).toBe(true);
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL)).toBe(true);
  });

  test('should return false for unaffected design values', () => {
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.E)).toBe(false);
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.E_MIN)).toBe(false);
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.SHEAR)).toBe(false);
    expect(isDesignValueAffectedBySizeFactor(DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR)).toBe(false);
  });
});

describe('Edge Cases and Integration Tests', () => {
  test('should handle MSR and MEL grades with default factors', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 3.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.MSR,
    };
    const factor = getSizeFactor(input);
    expect(factor).toBe(1.0);
  });

  test('should handle boundary dimension values', () => {
    const boundaryTests = [
      { width: 3.5, depth: 4.0 },
      { width: 4.0, depth: 5.0 },
      { width: 1.5, depth: 12.0 },
      { width: 1.5, depth: 14.0 },
    ];

    boundaryTests.forEach(test => {
      const input: SizeFactorInput = {
        width: test.width,
        depth: test.depth,
        designValueType: DESIGN_VALUE_TYPES.BENDING,
        lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      };
      
      expect(() => getSizeFactor(input)).not.toThrow();
      const factor = getSizeFactor(input);
      expect(typeof factor).toBe('number');
      expect(factor).toBeGreaterThan(0);
    });
  });

  test('should provide consistent results across multiple calls', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 7.25,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };

    const results = Array(10).fill(null).map(() => ({
      sizeFactor: getSizeFactor(input),
      flatUseFactor: getFlatUseFactor(input),
      adjustedValue: applySizeFactor(1000, input),
    }));

    // All results should be identical
    results.forEach(result => {
      expect(result.sizeFactor).toBe(results[0].sizeFactor);
      expect(result.flatUseFactor).toBe(results[0].flatUseFactor);
      expect(result.adjustedValue).toBe(results[0].adjustedValue);
    });
  });

  test('should maintain mathematical relationships', () => {
    const input: SizeFactorInput = {
      width: 1.5,
      depth: 5.5,
      designValueType: DESIGN_VALUE_TYPES.BENDING,
      lumberGrade: SIZE_FACTOR_LUMBER_GRADES.SELECT_STRUCTURAL,
      isLoadOnWideFace: true,
    };

    const sizeFactor = getSizeFactor(input);
    const flatUseFactor = getFlatUseFactor(input);
    const combinedFactor = sizeFactor * flatUseFactor;
    
    const referenceValue = 1000;
    const adjustedValue = applySizeFactor(referenceValue, input);
    const expectedValue = referenceValue * combinedFactor;
    
    expect(adjustedValue).toBeCloseTo(expectedValue, 6);
  });
}); 