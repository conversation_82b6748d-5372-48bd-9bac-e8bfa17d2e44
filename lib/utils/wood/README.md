# Wood Structural Design Utilities

This directory contains comprehensive implementations of wood structural design calculations based on the National Design Specification (NDS) for Wood Construction, 2018 edition.

## Overview

The implementation covers the following NDS sections:

- **Section 3.6**: Compression Members - General
- **Section 3.7**: Solid Columns
- **Section 3.8**: Tension Members  
- **Section 3.9**: Combined Bending and Axial Loading

## Modules

### Compression Members (`compression/`)

Implements compression member design according to NDS Sections 3.6 and 3.7.

**Key Features:**
- Column stability factor (Cp) calculations per NDS 3.7.1
- Rectangular, round, and tapered column support
- Slenderness ratio checks and limits
- Critical buckling design value calculations
- Representative dimension calculations for tapered columns

**Main Functions:**
```typescript
import { Compression } from '@/lib/utils/wood';

// Analyze a compression member
const results = Compression.analyzeCompressionMember(geometry, material, loading, FcStar);

// Calculate column stability factor
const stability = Compression.analyzeColumnStability(geometry, material, FcStar);

// Analyze specific column types
const rectResults = Compression.analyzeRectangularColumn(...);
const roundResults = Compression.analyzeRoundColumn(...);
const taperedResults = Compression.analyzeTaperedColumn(...);
```

**References:**
- NDS 2018 Section 3.6: Compression Members - General
- NDS 2018 Section 3.7: Solid Columns
- NDS 2018 Section 3.7.1: Column Stability Factor, Cp
- NDS 2018 Section 3.7.2: Tapered Columns
- NDS 2018 Section 3.7.3: Round Columns

### Tension Members (`tension/`)

Implements tension member design according to NDS Section 3.8.

**Key Features:**
- Tension parallel to grain analysis
- Net section area considerations
- Tension perpendicular to grain warnings
- Connection effect estimation

**Main Functions:**
```typescript
import { Tension } from '@/lib/utils/wood';

// Analyze a tension member
const results = Tension.analyzeTensionMember(geometry, material, loading, FtStar);

// Calculate section properties
const grossArea = Tension.calculateGrossCrossSectionalArea(geometry);
const netArea = Tension.calculateNetCrossSectionalArea(geometry);

// Estimate net area for connections
const estimatedNetArea = Tension.estimateNetArea(grossArea, 'bolted', 4);
```

**References:**
- NDS 2018 Section 3.8.1: Tension Parallel to Grain
- NDS 2018 Section 3.8.2: Tension Perpendicular to Grain

### Combined Loading (`combined-loading/`)

Implements combined bending and axial loading analysis according to NDS Section 3.9.

**Key Features:**
- Bending and axial tension interaction (NDS 3.9.1)
- Bending and axial compression interaction (NDS 3.9.2)
- Biaxial bending considerations
- Critical buckling values for interaction equations

**Main Functions:**
```typescript
import { CombinedLoading } from '@/lib/utils/wood';

// Analyze combined loading
const results = CombinedLoading.analyzeCombinedLoading(geometry, material, forces, FaStar, FbStar);

// Specific analysis types
const tensionResults = CombinedLoading.analyzeBendingAndAxialTension(...);
const compressionResults = CombinedLoading.analyzeBendingAndAxialCompression(...);

// Calculate critical buckling values
const criticalValues = CombinedLoading.calculateCriticalBucklingValues(geometry, material);
```

**References:**
- NDS 2018 Section 3.9.1: Bending and Axial Tension (Equations 3.9-1 and 3.9-2)
- NDS 2018 Section 3.9.2: Bending and Axial Compression (Equation 3.9-3 and 3.9-4)
- NDS 2018 Section 3.9.3: Eccentric Compression Loading

## Usage Examples

### Basic Compression Column Analysis

```typescript
import { Compression } from '@/lib/utils/wood';

const geometry = {
  length: 120, // 10 feet
  dimensions: { width: 5.5, depth: 5.5 },
  crossSection: 'rectangular',
  supportCondition: 'both_ends_simply_supported',
};

const material = {
  type: 'sawn_lumber',
  designValues: {
    Fc: 1000, // psi
    E: 1200000, // psi
    Emin: 440000, // psi
  },
};

const loading = {
  axialForce: 10000, // lbs
};

const FcStar = 800; // Adjusted design value (includes all factors except Cp)

const results = Compression.analyzeCompressionMember(geometry, material, loading, FcStar);

console.log(`Column stability factor: ${results.stability.Cp}`);
console.log(`Utilization ratio: ${results.utilization.compression}`);
console.log(`Design OK: ${results.checks.designOK}`);
```

### Tension Member with Connection Effects

```typescript
import { Tension } from '@/lib/utils/wood';

const geometry = {
  length: 144, // 12 feet
  dimensions: { width: 3.5, depth: 9.25 },
  netArea: 28.5, // Account for bolt holes
};

const material = {
  designValues: {
    Ft: 900, // psi
    E: 1600000, // psi
  },
};

const loading = {
  tensionForce: 15000, // lbs
  direction: 'parallel',
};

const FtStar = 720; // Adjusted design value

const results = Tension.analyzeTensionMember(geometry, material, loading, FtStar);

console.log(`Tension stress: ${results.stresses.ft} psi`);
console.log(`Design notes: ${results.notes.join('; ')}`);
```

### Combined Bending and Compression

```typescript
import { CombinedLoading } from '@/lib/utils/wood';

const geometry = {
  length: 96, // 8 feet
  dimensions: { width: 3.5, depth: 7.25 },
  effectiveLengths: { le1: 96, le2: 96 },
};

const material = {
  designValues: {
    Fb: 1200, // psi
    Fc: 1000, // psi
    E: 1600000, // psi
    Emin: 580000, // psi
  },
};

const forces = {
  axialForce: -8000, // Compression (negative)
  moments: { M1: 24000 }, // lb-in
  scenario: 'bending_and_axial_compression',
};

const FcStar = 850; // Adjusted compression design value
const FbStar = 1080; // Adjusted bending design value

const results = CombinedLoading.analyzeCombinedLoading(geometry, material, forces, FcStar, FbStar);

console.log(`Primary interaction ratio: ${results.interaction.ratio1}`);
console.log(`Secondary interaction ratio: ${results.interaction.ratio2}`);
console.log(`Combined loading OK: ${results.interaction.acceptable}`);
```

## Design Considerations

### Compression Members

1. **Slenderness Ratios**: The code enforces NDS limits of le/d ≤ 50 for normal service and le/d ≤ 75 during construction.

2. **Column Stability Factor**: Calculated using the exact NDS formula with appropriate buckling coefficients:
   - Sawn lumber: c = 0.8
   - Glued laminated timber: c = 0.9
   - Round timber poles: c = 0.85

3. **Tapered Columns**: Representative dimensions calculated per NDS 3.7.2 using support condition factors.

### Tension Members

1. **Net Section**: Stress calculations based on net area per NDS 3.8.1.

2. **Tension Perpendicular to Grain**: Avoided per NDS 3.8.2 with appropriate warnings.

3. **Connection Effects**: Helper functions to estimate net area reductions.

### Combined Loading

1. **Interaction Equations**: Exact implementation of NDS 3.9 equations.

2. **Critical Buckling**: Calculated per NDS 3.9.2 for stability interaction.

3. **Biaxial Bending**: Supported for both tension and compression scenarios.

## Testing

Comprehensive test suites are provided for all modules:

```bash
# Run compression member tests
npm test compression-member.test.ts

# Run all wood design tests
npm test lib/utils/wood/
```

## References

- **NDS 2018**: National Design Specification for Wood Construction, 2018 Edition
- **AWC**: American Wood Council
- **ANSI/AWC NDS-2018**: American National Standard for Wood Construction

## Implementation Notes

- All calculations follow NDS 2018 exactly as specified
- Units are consistent throughout (inches, pounds, psi)
- Extensive input validation and error handling
- Modular design for easy testing and maintenance
- TypeScript interfaces for type safety
- Comprehensive documentation and examples 