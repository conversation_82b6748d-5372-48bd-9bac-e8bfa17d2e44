/**
 * Constants for Sawn Lumber Design Calculations
 * Based on NDS (National Design Specification) for Wood Construction
 * 
 * This is the single source of truth for all wood design constants.
 * Consolidates all constants from previous constants.js and constants.ts files.
 */

// Loading condition types
export const LOADING_CONDITIONS = {
  UNIFORMLY_DISTRIBUTED: 'uniformly_distributed',
  CONCENTRATED_CENTER: 'concentrated_center',
  CONCENTRATED_END: 'concentrated_end',
  EQUAL_END_MOMENTS: 'equal_end_moments',
} as const;

export type LoadingCondition = typeof LOADING_CONDITIONS[keyof typeof LOADING_CONDITIONS];

// Beam stability factor constants (NDS 3.3.3)
export const BEAM_STABILITY_CONSTANTS = {
  // Slenderness ratio limits
  MAX_SLENDERNESS_RATIO: 50,
  
  // Critical buckling design value coefficient
  CRITICAL_BUCKLING_COEFFICIENT: 1.20,
  
  // CL formula coefficients (NDS 3.3.6)
  CL_DENOMINATOR_1: 1.9,
  CL_DENOMINATOR_2: 0.95,
  
  // Maximum beam stability factor
  MAX_BEAM_STABILITY_FACTOR: 1.0,
  
  // Depth multiplier for intermediate range
  DEPTH_MULTIPLIER: 3,
} as const;

// Effective length ratio thresholds (NDS Table 3.3.3)
export const EFFECTIVE_LENGTH_THRESHOLDS = {
  LOW_RATIO: 7,
  HIGH_RATIO: 14.3,
} as const;

// Effective length factors from NDS Table 3.3.3
export const EFFECTIVE_LENGTH_FACTORS = {
  UNIFORMLY_DISTRIBUTED: {
    LOW_RATIO: 2.06,        // ℓu/d ≤ 7
    INTERMEDIATE: 1.63,     // 7 < ℓu/d ≤ 14.3 (with +3d term)
    HIGH_RATIO: 1.84,       // ℓu/d > 14.3
  },
  CONCENTRATED_CENTER: {
    LOW_RATIO: 1.80,        // ℓu/d ≤ 7
    HIGH_RATIO: 1.37,       // ℓu/d > 7
  },
  CONCENTRATED_END: {
    LOW_RATIO: 1.87,        // ℓu/d ≤ 7
    HIGH_RATIO: 1.44,       // ℓu/d > 7
  },
  EQUAL_END_MOMENTS: 1.84,  // All ratios
} as const;

// Design value types for incising factor (NDS 4.3.8)
export const DESIGN_VALUE_TYPES = {
  // Modulus of Elasticity
  E: 'E',
  E_MIN: 'Emin',
  
  // Strength properties
  BENDING: 'Fb',
  TENSION_PARALLEL: 'Ft', 
  COMPRESSION_PARALLEL: 'Fc',
  SHEAR: 'Fv',
  
  // Compression perpendicular to grain
  COMPRESSION_PERPENDICULAR: 'Fc_perp',
} as const;

export type DesignValueType = typeof DESIGN_VALUE_TYPES[keyof typeof DESIGN_VALUE_TYPES];

// Incising factors from NDS Table 4.3.8
export const INCISING_FACTORS = {
  // Modulus of elasticity values (E, Emin)
  MODULUS_OF_ELASTICITY: 0.95,
  
  // Strength values (Fb, Ft, Fc, Fv)  
  STRENGTH_VALUES: 0.80,
  
  // Compression perpendicular to grain (Fc⊥)
  COMPRESSION_PERPENDICULAR: 1.00,
} as const;

// Incising specifications (NDS 4.3.8)
export const INCISING_SPECIFICATIONS = {
  MAX_DEPTH: 0.4,        // inches
  MAX_LENGTH: 0.375,     // inches (3/8")
  MAX_DENSITY: 1100,     // incisions per ft²
} as const;

// Temperature factor constants (NDS 2.3.3 and Table 2.3.3)
export const TEMPERATURE_FACTOR_CONSTANTS = {
  // Temperature thresholds in Fahrenheit
  TEMP_THRESHOLD_LOW: 100,     // T ≤ 100°F
  TEMP_THRESHOLD_MID: 125,     // 100°F < T ≤ 125°F  
  TEMP_THRESHOLD_HIGH: 150,    // 125°F < T ≤ 150°F
  
  // Maximum temperature for immediate reversible effects
  MAX_REVERSIBLE_TEMP: 150,    // Above 150°F can cause permanent loss
  
  // Default temperature for normal conditions
  NORMAL_TEMPERATURE: 70,      // °F
} as const;

// Temperature factors from NDS Table 2.3.3
export const TEMPERATURE_FACTORS = {
  // For Ft, E, Emin (wet or dry conditions)
  TENSION_AND_ELASTICITY: {
    LOW_TEMP: 1.0,     // T ≤ 100°F
    MID_TEMP: 0.9,     // 100°F < T ≤ 125°F
    HIGH_TEMP: 0.9,    // 125°F < T ≤ 150°F
  },
  
  // For Fb, Fv, Fc, Fc⊥ under dry conditions
  STRENGTH_VALUES_DRY: {
    LOW_TEMP: 1.0,     // T ≤ 100°F
    MID_TEMP: 0.8,     // 100°F < T ≤ 125°F
    HIGH_TEMP: 0.7,    // 125°F < T ≤ 150°F
  },
  
  // For Fb, Fv, Fc, Fc⊥ under wet conditions
  STRENGTH_VALUES_WET: {
    LOW_TEMP: 1.0,     // T ≤ 100°F
    MID_TEMP: 0.7,     // 100°F < T ≤ 125°F
    HIGH_TEMP: 0.5,    // 125°F < T ≤ 150°F
  },
} as const;

// Moisture conditions for temperature factor (NDS Table 2.3.3)
export const MOISTURE_CONDITIONS = {
  WET: 'wet',
  DRY: 'dry',
  WET_OR_DRY: 'wet_or_dry',
} as const;

export type MoistureCondition = typeof MOISTURE_CONDITIONS[keyof typeof MOISTURE_CONDITIONS];

// Design value types that receive tension/elasticity temperature factors
export const TENSION_ELASTICITY_DESIGN_VALUES = [
  DESIGN_VALUE_TYPES.TENSION_PARALLEL,
  DESIGN_VALUE_TYPES.E,
  DESIGN_VALUE_TYPES.E_MIN,
] as const;

// Design value types that receive strength temperature factors
export const STRENGTH_DESIGN_VALUES = [
  DESIGN_VALUE_TYPES.BENDING,
  DESIGN_VALUE_TYPES.SHEAR,
  DESIGN_VALUE_TYPES.COMPRESSION_PARALLEL,
  DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR,
] as const;

// Load duration factors from NDS Table 2.3.2 (ASD Only)
export const LOAD_DURATION_FACTORS = {
  PERMANENT: 0.9,        // Dead Load
  TEN_YEARS: 1.0,        // Occupancy Live Load
  TWO_MONTHS: 1.15,      // Snow Load
  SEVEN_DAYS: 1.25,      // Construction Load
  TEN_MINUTES: 1.6,      // Wind/Earthquake Load
  IMPACT: 2.0,           // Impact Load
} as const;

// Load duration categories (NDS 2.3.2)
export const LOAD_DURATION_CATEGORIES = {
  PERMANENT: 'permanent',
  TEN_YEARS: 'ten_years', 
  TWO_MONTHS: 'two_months',
  SEVEN_DAYS: 'seven_days',
  TEN_MINUTES: 'ten_minutes',
  IMPACT: 'impact',
} as const;

export type LoadDurationCategory = typeof LOAD_DURATION_CATEGORIES[keyof typeof LOAD_DURATION_CATEGORIES];

// Load duration factor limitations (NDS 2.3.2)
export const LOAD_DURATION_LIMITATIONS = {
  // Maximum factor for pressure-treated lumber and connections
  MAX_FACTOR_TREATED_LUMBER: 1.6,
  
  // Maximum factor for wood structural panels
  MAX_FACTOR_STRUCTURAL_PANELS: 1.6,
  
  // Design values that do not receive load duration factors
  EXEMPT_DESIGN_VALUES: [
    DESIGN_VALUE_TYPES.E,
    DESIGN_VALUE_TYPES.E_MIN,
    DESIGN_VALUE_TYPES.COMPRESSION_PERPENDICULAR, // when based on deformation limit
  ],
} as const;

// LRFD Design Method Constants (NDS Appendix N)

// Format Conversion Factors from NDS Table N1 (LRFD Only)
export const LRFD_FORMAT_CONVERSION_FACTORS = {
  // Member properties
  FB: 2.54,       // Bending strength
  FT: 2.70,       // Tension parallel to grain
  FV: 2.88,       // Shear strength
  FRT: 2.88,      // Radial tension strength
  FS: 2.88,       // Bearing strength
  FC: 2.40,       // Compression parallel to grain
  FC_PERPENDICULAR: 1.67,  // Compression perpendicular to grain
  E_MIN: 1.76,    // Minimum modulus of elasticity
  
  // All connections
  ALL_CONNECTIONS: 3.32,
} as const;

// Resistance Factors from NDS Table N2 (LRFD Only)
export const LRFD_RESISTANCE_FACTORS = {
  // Member properties
  FB: 0.85,       // Bending strength
  FT: 0.80,       // Tension parallel to grain
  FV: 0.75,       // Shear strength
  FRT: 0.75,      // Radial tension strength
  FS: 0.75,       // Bearing strength
  FC: 0.90,       // Compression parallel to grain
  FC_PERPENDICULAR: 0.90,  // Compression perpendicular to grain
  E_MIN: 0.85,    // Minimum modulus of elasticity
  
  // All connections
  ALL_CONNECTIONS: 0.65,
} as const;

// Time Effect Factors from NDS Table N3 (LRFD Only)
export const LRFD_TIME_EFFECT_FACTORS = {
  // Load combination: 1.4D
  DEAD_ONLY: 0.6,
  
  // Load combination: 1.2D + 1.6L + 0.5(Lr or S or R)
  DEAD_LIVE_ROOF_SNOW_RAIN: {
    L_FROM_STORAGE: 0.7,
    L_FROM_OCCUPANCY: 0.8,
    L_FROM_IMPACT: 1.25,
  },
  
  // Load combination: 1.2D + 1.6(Lr or S or R) + (L or 0.5W)
  DEAD_ROOF_SNOW_RAIN_LIVE_WIND: 0.8,
  
  // Load combination: 1.2D + 1.0W + L + 0.5(Lr or S or R)
  DEAD_WIND_LIVE_ROOF_SNOW_RAIN: 1.0,
  
  // Load combination: 1.2D + 1.0E + L + 0.2S
  DEAD_EARTHQUAKE_LIVE_SNOW: 1.0,
  
  // Load combination: 0.9D + 1.0W
  UPLIFT_WIND: 1.0,
  
  // Load combination: 0.9D + 1.0E
  UPLIFT_EARTHQUAKE: 1.0,
} as const;

// LRFD Load Combination Categories
export const LRFD_LOAD_COMBINATIONS = {
  DEAD_ONLY: 'dead_only',
  DEAD_LIVE_ROOF_SNOW_RAIN: 'dead_live_roof_snow_rain',
  DEAD_ROOF_SNOW_RAIN_LIVE_WIND: 'dead_roof_snow_rain_live_wind',
  DEAD_WIND_LIVE_ROOF_SNOW_RAIN: 'dead_wind_live_roof_snow_rain',
  DEAD_EARTHQUAKE_LIVE_SNOW: 'dead_earthquake_live_snow',
  UPLIFT_WIND: 'uplift_wind',
  UPLIFT_EARTHQUAKE: 'uplift_earthquake',
} as const;

export type LRFDLoadCombination = typeof LRFD_LOAD_COMBINATIONS[keyof typeof LRFD_LOAD_COMBINATIONS];

// LRFD Live Load Categories for Time Effect Factor
export const LRFD_LIVE_LOAD_CATEGORIES = {
  STORAGE: 'storage',
  OCCUPANCY: 'occupancy', 
  IMPACT: 'impact',
} as const;

export type LRFDLiveLoadCategory = typeof LRFD_LIVE_LOAD_CATEGORIES[keyof typeof LRFD_LIVE_LOAD_CATEGORIES];

// Repetitive Member Factor Constants (NDS 4.3.9)
export const REPETITIVE_MEMBER_FACTOR_CONSTANTS = {
  // Standard repetitive member factor value (NDS 4.3.9)
  STANDARD_FACTOR: 1.15,
  
  // Lumber thickness range for applicability (NDS 4.3.9)
  MIN_THICKNESS: 2.0,        // inches
  MAX_THICKNESS: 4.0,        // inches
  
  // Spacing requirements (NDS 4.3.9)
  MAX_SPACING_ON_CENTER: 24, // inches
  MIN_NUMBER_OF_MEMBERS: 3,  // minimum number of members required
  
  // Application conditions  
  NO_FACTOR: 1.0,            // When repetitive member factor doesn't apply
} as const;

// Member types that qualify for repetitive member factor (NDS 4.3.9)
export const REPETITIVE_MEMBER_TYPES = {
  JOISTS: 'joists',
  TRUSS_CHORDS: 'truss_chords',
  RAFTERS: 'rafters', 
  STUDS: 'studs',
  PLANKS: 'planks',
  DECKING: 'decking',
  SIMILAR_MEMBERS: 'similar_members',
} as const;

export type RepetitiveMemberType = typeof REPETITIVE_MEMBER_TYPES[keyof typeof REPETITIVE_MEMBER_TYPES];

// Load distributing element types (NDS 4.3.9)
export const LOAD_DISTRIBUTING_ELEMENTS = {
  SUBFLOORING: 'subflooring',
  FLOORING: 'flooring', 
  SHEATHING: 'sheathing',
  COVERING_ELEMENTS: 'covering_elements',
  NAIL_GLUING: 'nail_gluing',
  TONGUE_AND_GROOVE: 'tongue_and_groove',
  THROUGH_NAILING: 'through_nailing',
} as const;

export type LoadDistributingElement = typeof LOAD_DISTRIBUTING_ELEMENTS[keyof typeof LOAD_DISTRIBUTING_ELEMENTS];

// Lumber grades and products where repetitive member factor applies
export const REPETITIVE_MEMBER_LUMBER_CATEGORIES = {
  DIMENSION_LUMBER: 'dimension_lumber',      // 2" to 4" thick sawn lumber
  VISUALLY_GRADED_DECKING: 'visually_graded_decking', // Special case - already includes Cr
} as const;

export type RepetitiveMemberLumberCategory = typeof REPETITIVE_MEMBER_LUMBER_CATEGORIES[keyof typeof REPETITIVE_MEMBER_LUMBER_CATEGORIES];

// Glulam Constants (NDS Section 5)

// Curvature Factor Constants (NDS 5.3.8)
export const GLULAM_CURVATURE_CONSTANTS = {
  // Curvature factor formula coefficient: Cc = 1 - (2000)(t/R)²
  CURVATURE_COEFFICIENT: 2000,
  
  // Maximum curvature ratios (t/R) (NDS 5.3.8)
  MAX_CURVATURE_RATIO_HARDWOOD_SOUTHERN_PINE: 1/100,  // 0.01
  MAX_CURVATURE_RATIO_OTHER_SOFTWOOD: 1/125,          // 0.008
  
  // Practical minimum curvature factor
  MIN_CURVATURE_FACTOR: 0.1,
  
  // Default curvature factor for straight members
  STRAIGHT_MEMBER_FACTOR: 1.0,
} as const;

// Stress Interaction Factor Constants (NDS 5.3.9)
export const GLULAM_STRESS_INTERACTION_CONSTANTS = {
  // Default factor for non-tapered members
  NON_TAPERED_FACTOR: 1.0,
  
  // Default factor when insufficient data
  DEFAULT_FACTOR: 1.0,
} as const;

// Shear Reduction Factor Constants (NDS 5.3.10)
export const GLULAM_SHEAR_REDUCTION_CONSTANTS = {
  // Reduced factor for special conditions
  REDUCED_FACTOR: 0.72,
  
  // Normal factor for prismatic members under normal loading
  NORMAL_FACTOR: 1.0,
} as const;

// Glulam Member Types
export const GLULAM_MEMBER_TYPES = {
  PRISMATIC: 'prismatic',
  NON_PRISMATIC: 'non-prismatic',
} as const;

export type GlulamMemberType = typeof GLULAM_MEMBER_TYPES[keyof typeof GLULAM_MEMBER_TYPES];

// Glulam Loading Types
export const GLULAM_LOADING_TYPES = {
  NORMAL: 'normal',
  IMPACT: 'impact',
  REPETITIVE_CYCLIC: 'repetitive_cyclic',
} as const;

export type GlulamLoadingType = typeof GLULAM_LOADING_TYPES[keyof typeof GLULAM_LOADING_TYPES];

// Glulam Taper Locations
export const GLULAM_TAPER_LOCATIONS = {
  COMPRESSION: 'compression',
  TENSION: 'tension',
} as const;

export type GlulamTaperLocation = typeof GLULAM_TAPER_LOCATIONS[keyof typeof GLULAM_TAPER_LOCATIONS];

// Shared Mathematical Constants
export const MATHEMATICAL_CONSTANTS = {
  // Angular conversion
  DEGREES_TO_RADIANS: Math.PI / 180,
  RADIANS_TO_DEGREES: 180 / Math.PI,
  
  // Section properties calculation factors
  RECTANGULAR_SECTION_MODULUS_FACTOR: 6,          // S = bd²/6
  RECTANGULAR_SHEAR_STRESS_FACTOR: 1.5,           // τ = 1.5V/A
  
  // Common mathematical values
  PI: Math.PI,
} as const;

// Shared Adjustment Factor Constants
export const ADJUSTMENT_FACTOR_CONSTANTS = {
  // Default factor values (no adjustment)
  NO_ADJUSTMENT: 1.0,
  
  // Default LRFD time effect factor
  DEFAULT_LRFD_TIME_EFFECT: 0.8,
  
  // Stress ratio limits
  MAX_STRESS_RATIO: 1.0,
  
  // Default spacing values (inches)
  DEFAULT_REPETITIVE_SPACING: 16,
  DEFAULT_REPETITIVE_MEMBERS: 3,
} as const;

// Notch Constants (NDS 3.2.3, 4.4.3, 5.4.5, 8.4.1)

// Wood product types for notch considerations
export const NOTCH_WOOD_PRODUCT_TYPES = {
  SAWN_LUMBER: 'sawn_lumber',
  GLULAM: 'glulam', 
  STRUCTURAL_COMPOSITE: 'structural_composite',
} as const;

export type NotchWoodProductType = typeof NOTCH_WOOD_PRODUCT_TYPES[keyof typeof NOTCH_WOOD_PRODUCT_TYPES];

// Notch location types (NDS 4.4.3)
export const NOTCH_LOCATION_TYPES = {
  END_NOTCH: 'end_notch',           // Located at ends for bearing over support
  INTERIOR_NOTCH: 'interior_notch',  // Located in outer thirds of span
} as const;

export type NotchLocationType = typeof NOTCH_LOCATION_TYPES[keyof typeof NOTCH_LOCATION_TYPES];

// Notch face types (tension or compression side)
export const NOTCH_FACE_TYPES = {
  TENSION: 'tension',
  COMPRESSION: 'compression',
} as const;

export type NotchFaceType = typeof NOTCH_FACE_TYPES[keyof typeof NOTCH_FACE_TYPES];

// Span regions for notch location (NDS 4.4.3.2)
export const NOTCH_SPAN_REGIONS = {
  OUTER_THIRD: 'outer_third',    // Outer 1/3 of span
  MIDDLE_THIRD: 'middle_third',  // Middle 1/3 of span
} as const;

export type NotchSpanRegion = typeof NOTCH_SPAN_REGIONS[keyof typeof NOTCH_SPAN_REGIONS];

// Cross-section shapes for notched members (NDS 3.4.3)
export const NOTCH_CROSS_SECTION_SHAPES = {
  RECTANGULAR: 'rectangular',
  CIRCULAR: 'circular',
  OTHER: 'other',
} as const;

export type NotchCrossSectionShape = typeof NOTCH_CROSS_SECTION_SHAPES[keyof typeof NOTCH_CROSS_SECTION_SHAPES];

// Sawn Lumber Notch Limitations (NDS 4.4.3)
export const SAWN_LUMBER_NOTCH_LIMITS = {
  // End notches (NDS 4.4.3.1)
  END_NOTCH: {
    MAX_DEPTH_RATIO: 1/4,  // ≤ 1/4 beam depth
  },
  
  // Interior notches (NDS 4.4.3.2)
  INTERIOR_NOTCH: {
    MAX_DEPTH_RATIO: 1/6,  // ≤ 1/6 beam depth
    NOMINAL_THICKNESS_LIMIT: 4.0,  // 4" nominal thickness
    ACTUAL_THICKNESS_LIMIT: 3.5,   // 3-1/2" actual thickness
  },
} as const;

// Structural Glued Laminated Timber (Glulam) Notch Limitations (NDS 5.4.5)
export const GLULAM_NOTCH_LIMITS = {
  // Tension side notches (NDS 5.4.5.1)
  TENSION_SIDE: {
    GENERAL_PROHIBITION: true,  // Generally not permitted
    END_BEARING_EXCEPTION: {
      ALLOWED: true,
      MAX_DEPTH_RATIO: 1/10,   // ≤ 1/10 beam depth
      MAX_DEPTH_ABSOLUTE: 3.0, // ≤ 3 inches
    },
  },
  
  // Compression side notches (NDS 5.4.5.2)
  COMPRESSION_SIDE: {
    GENERAL_PROHIBITION: true,  // Generally not permitted except at ends
    END_NOTCH: {
      ALLOWED: true,
      MAX_DEPTH_RATIO: 2/5,    // ≤ 2/5 beam depth
      MIDDLE_THIRD_RESTRICTION: true, // Shall not extend into middle 1/3 of span
    },
  },
  
  // Both faces at same cross-section (NDS 5.4.5.3)
  BOTH_FACES_RESTRICTION: true,  // Not permitted on both faces at same cross-section
  
  // Taper cut exception (NDS 5.4.5.2)
  TAPER_CUT_EXCEPTION: {
    ALLOWED: true,
    LOCATION: NOTCH_FACE_TYPES.COMPRESSION,
    CONDITION: 'gradual_taper',
  },
} as const;

// Structural Composite Bending Members Notch Limitations (NDS 8.4.1)
export const STRUCTURAL_COMPOSITE_NOTCH_LIMITS = {
  // Tension side notches (NDS 8.4.1.1)
  TENSION_SIDE: {
    GENERAL_PROHIBITION: true,  // Generally not permitted
    END_BEARING_EXCEPTION: {
      ALLOWED: true,
      MAX_DEPTH_RATIO: 1/10,   // ≤ 1/10 beam depth
    },
  },
  
  // Compression side notches (NDS 8.4.1.1)
  COMPRESSION_SIDE: {
    GENERAL_PROHIBITION: true,  // Generally not permitted except at ends
    END_NOTCH: {
      ALLOWED: true,
      MAX_DEPTH_RATIO: 2/5,    // ≤ 2/5 beam depth
      MIDDLE_THIRD_RESTRICTION: true, // Shall not extend into middle third of span
    },
  },
} as const;

// Stiffness impact thresholds (NDS *******)
export const NOTCH_STIFFNESS_THRESHOLDS = {
  NEGLIGIBLE_IMPACT: {
    DEPTH_RATIO: 1/6,    // notch depth ≤ 1/6 beam depth
    LENGTH_RATIO: 1/3,   // notch length ≤ 1/3 beam depth
  },
} as const;

// Shear strength reduction coefficients for notched members (NDS 3.4.3)
export const NOTCH_SHEAR_COEFFICIENTS = {
  // Basic coefficient for rectangular and circular sections (NDS 3.4.3)
  BASIC_COEFFICIENT: 2/3,
  
  // Connection distance multipliers (NDS *******)
  CONNECTION_DISTANCE: {
    LESS_THAN_5D: 1.0,    // Connection < 5 times member depth from end
    AT_LEAST_5D: 1.0,     // Connection ≥ 5 times member depth from end
  },
} as const;

// Gradual taper cut benefits (NDS *******)
export const NOTCH_STRESS_CONCENTRATION = {
  SQUARE_NOTCH: {
    STRESS_CONCENTRATION_FACTOR: 'high',  // High stress concentration
  },
  GRADUAL_TAPER: {
    STRESS_CONCENTRATION_FACTOR: 'reduced',  // Reduces stress concentrations
    RECOMMENDATION: true,
  },
} as const;

// Reference sections for notch effects on strength (cross-references in NDS)
export const NOTCH_STRENGTH_REFERENCES = {
  GENERAL_BENDING: '3.1.2' as const,    // General effect on bending strength
  SHEAR_STRENGTH: '3.4.3' as const,     // Effect on shear strength  
  SAWN_LUMBER: '4.4.3' as const,        // Sawn lumber specific requirements
  GLULAM: '5.4.5' as const,             // Glulam specific requirements
  STRUCTURAL_COMPOSITE: '8.4.1' as const, // Structural composite specific requirements
} as const;

// General notch prohibition references (NDS *******)
export const GENERAL_NOTCH_PROHIBITIONS = {
  BASIC_RULE: 'Bending members shall not be notched except as permitted',
  PERMITTED_SECTIONS: [
    NOTCH_STRENGTH_REFERENCES.SAWN_LUMBER,
    NOTCH_STRENGTH_REFERENCES.GLULAM, 
    '7.4.4', // Section 7.4.4 (not detailed in provided images)
    NOTCH_STRENGTH_REFERENCES.STRUCTURAL_COMPOSITE,
  ],
} as const;

// Beam Analysis Constants (Common to Sawn Lumber and Glulam)

// Design method enumeration (used in both sawn lumber and glulam analysis)
export const DESIGN_METHODS = {
  ASD: 'ASD',
  LRFD: 'LRFD',
} as const;

export type DesignMethod = typeof DESIGN_METHODS[keyof typeof DESIGN_METHODS];

// Glulam-specific enums and constants

// Glulam axis orientation for design values (NDS 5.2.2)
export const GLULAM_AXIS = {
  X_AXIS: 'x_axis', // Strong axis (parallel to wide face of laminations)
  Y_AXIS: 'y_axis', // Weak axis (perpendicular to wide face of laminations)
} as const;

export type GlulamAxis = typeof GLULAM_AXIS[keyof typeof GLULAM_AXIS];

// Bending stress type for glulam members (NDS 5.2.4)
export const BENDING_STRESS_TYPES = {
  POSITIVE: 'positive', // Tension at bottom of beam (Fbx+)
  NEGATIVE: 'negative', // Tension at top of beam (Fbx-)
} as const;

export type BendingStressType = typeof BENDING_STRESS_TYPES[keyof typeof BENDING_STRESS_TYPES];

// Glulam layup configuration (NDS 5.2.3)
export const LAYUP_TYPES = {
  BALANCED: 'balanced', // Same design values for positive and negative bending
  UNBALANCED: 'unbalanced', // Different design values for positive and negative bending
} as const;

export type LayupType = typeof LAYUP_TYPES[keyof typeof LAYUP_TYPES];

// Beam Analysis Default Values and Calculation Constants

// Default deflection limits (L/deflectionLimit)
export const DEFAULT_DEFLECTION_LIMITS = {
  TOTAL_LOAD: 240, // L/240 for total load deflection
  LIVE_LOAD: 360, // L/360 for live load deflection
  LONG_TERM: 180, // L/180 for long-term deflection
  VIBRATION: 300, // L/300 for vibration serviceability
} as const;

// Section property calculation constants
export const SECTION_PROPERTY_CONSTANTS = {
  // Rectangular section modulus factor: S = bd²/6
  RECTANGULAR_SECTION_MODULUS_FACTOR: 6,
  
  // Rectangular moment of inertia factor: I = bd³/12
  RECTANGULAR_MOMENT_OF_INERTIA_FACTOR: 12,
  
  // Rectangular area factor: A = bd
  RECTANGULAR_AREA_FACTOR: 1,
  
  // Shear stress distribution factor for rectangular sections: τ = 1.5V/A
  RECTANGULAR_SHEAR_STRESS_FACTOR: 1.5,
} as const;

// Common beam analysis validation limits
export const BEAM_ANALYSIS_LIMITS = {
  // Stress ratio limits
  MAX_STRESS_RATIO: 1.0, // Maximum allowable stress ratio
  MIN_STRESS_RATIO: 0.0, // Minimum stress ratio
  
  // Deflection ratio limits
  MAX_DEFLECTION_RATIO: 1.0, // Maximum allowable deflection ratio
  MIN_DEFLECTION_RATIO: 0.0, // Minimum deflection ratio
  
  // Member dimension limits (inches)
  MIN_MEMBER_WIDTH: 0.1,
  MAX_MEMBER_WIDTH: 100.0,
  MIN_MEMBER_DEPTH: 0.1,
  MAX_MEMBER_DEPTH: 100.0,
  MIN_MEMBER_LENGTH: 1.0,
  MAX_MEMBER_LENGTH: 1000.0,
  
  // Service condition limits
  MIN_MOISTURE_CONTENT: 0.0,
  MAX_MOISTURE_CONTENT: 100.0,
  MIN_TEMPERATURE: -50.0, // °F
  MAX_TEMPERATURE: 200.0, // °F
} as const;

// Wood density constants for self-weight calculations
export const WOOD_DENSITY = {
  IMPERIAL: 35, // Approximate density in lb/ft³
  METRIC: 560, // Approximate density in kg/m³
} as const;

// Common lumber categories for classification
export const LUMBER_CATEGORIES = {
  DIMENSION_LUMBER: 'dimension_lumber',
  BEAMS_AND_STRINGERS: 'beams_and_stringers',
  POSTS_AND_TIMBERS: 'posts_and_timbers',
  DECKING: 'decking',
  GLULAM: 'glulam',
  STRUCTURAL_COMPOSITE: 'structural_composite',
} as const;

export type LumberCategory = typeof LUMBER_CATEGORIES[keyof typeof LUMBER_CATEGORIES];

// Analysis result status indicators
export const ANALYSIS_STATUS = {
  PASS: 'pass',
  FAIL: 'fail',
  WARNING: 'warning',
  NOT_APPLICABLE: 'not_applicable',
} as const;

export type AnalysisStatus = typeof ANALYSIS_STATUS[keyof typeof ANALYSIS_STATUS];

// Controlling criteria for beam design
export const CONTROLLING_CRITERIA = {
  BENDING_STRESS: 'bending_stress',
  SHEAR_STRESS: 'shear_stress',
  DEFLECTION: 'deflection',
  LATERAL_STABILITY: 'lateral_stability',
  BEARING: 'bearing',
  VIBRATION: 'vibration',
} as const;

export type ControllingCriteria = typeof CONTROLLING_CRITERIA[keyof typeof CONTROLLING_CRITERIA]; 