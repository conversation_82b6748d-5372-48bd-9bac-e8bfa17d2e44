/**
 * Compression Member Analysis
 * Based on NDS 2018 Section 3.6 - Compression Members - General
 * 
 * This module provides comprehensive analysis of wood compression members including
 * columns, posts, and compression members in trusses and other structural components.
 * 
 * References:
 * - NDS 2018 Section 3.6: Compression Members - General
 * - NDS 2018 Section 3.6.3: Strength in Compression Parallel to Grain
 * - NDS 2018 Section 3.6.4: Compression Members Bearing End to End
 * - NDS 2018 Section 3.6.5: Eccentric Loading or Combined Stresses
 */

import {
  CompressionMemberGeometry,
  CompressionMemberMaterial,
  CompressionMemberLoading,
  CompressionMemberResults,
  ColumnClassification,
  COLUMN_CLASSIFICATIONS,
} from './types';

import { analyzeColumnStability } from './column-stability';
import { DESIGN_TOLERANCES } from './constants';

/**
 * Calculate cross-sectional area for compression members
 * 
 * @param geometry - Member geometry
 * @returns Cross-sectional area (in^2)
 */
export function calculateCrossSectionalArea(geometry: CompressionMemberGeometry): number {
  const { dimensions, crossSection } = geometry;
  
  switch (crossSection) {
    case 'rectangular':
      const depth = dimensions.depth ?? dimensions.width; // Square if depth not provided
      return dimensions.width * depth;
      
    case 'round':
      const radius = dimensions.width / 2;
      return Math.PI * radius * radius;
      
    case 'tapered':
      // For tapered sections, use average area or representative dimension
      if (!dimensions.minDimension || !dimensions.maxDimension) {
        throw new Error('Tapered columns require minDimension and maxDimension');
      }
      // Simplified approach - use average dimension squared
      const avgDimension = (dimensions.minDimension + dimensions.maxDimension) / 2;
      return avgDimension * avgDimension;
      
    default:
      throw new Error(`Unsupported cross-section type: ${crossSection}`);
  }
}

/**
 * Calculate section modulus for bending analysis
 * 
 * @param geometry - Member geometry
 * @returns Section modulus about strong axis (in^3)
 */
export function calculateSectionModulus(geometry: CompressionMemberGeometry): number {
  const { dimensions, crossSection } = geometry;
  
  switch (crossSection) {
    case 'rectangular':
      const width = dimensions.width;
      const depth = dimensions.depth ?? dimensions.width;
      // Section modulus about strong axis (assuming depth is strong direction)
      return (width * depth * depth) / 6;
      
    case 'round':
      const radius = dimensions.width / 2;
      return (Math.PI * Math.pow(radius, 3)) / 4;
      
    case 'tapered':
      // For tapered sections, use representative dimension
      if (!dimensions.minDimension || !dimensions.maxDimension) {
        throw new Error('Tapered columns require minDimension and maxDimension');
      }
      // Use minimum dimension for conservative estimate
      const d = dimensions.minDimension;
      return (d * d * d) / 6;
      
    default:
      throw new Error(`Unsupported cross-section type: ${crossSection}`);
  }
}

/**
 * Calculate actual compression stress
 * Based on NDS 3.6.3 - fc shall be based on net section area
 * 
 * @param axialForce - Applied axial compression force (lbs, positive in compression)
 * @param area - Net cross-sectional area (in^2)
 * @returns Actual compression stress (psi)
 */
export function calculateCompressionStress(axialForce: number, area: number): number {
  if (area <= 0) {
    throw new Error('Cross-sectional area must be positive');
  }
  
  return Math.abs(axialForce) / area;
}

/**
 * Calculate actual bending stress
 * 
 * @param moment - Applied bending moment (lb-in)
 * @param sectionModulus - Section modulus (in^3)
 * @returns Actual bending stress (psi)
 */
export function calculateBendingStress(moment: number, sectionModulus: number): number {
  if (sectionModulus <= 0) {
    throw new Error('Section modulus must be positive');
  }
  
  return Math.abs(moment) / sectionModulus;
}

/**
 * Determine column classification
 * Based on NDS 3.6.2
 * 
 * @param geometry - Member geometry
 * @param isBuiltUp - Whether the column is built-up from multiple pieces
 * @param isSpaced - Whether the column uses spaced members with connectors
 * @returns Column classification
 */
export function determineColumnClassification(
  geometry: CompressionMemberGeometry,
  isBuiltUp: boolean = false,
  isSpaced: boolean = false
): ColumnClassification {
  if (isSpaced) {
    return COLUMN_CLASSIFICATIONS.SPACED_CONNECTOR_JOINED;
  } else if (isBuiltUp) {
    return COLUMN_CLASSIFICATIONS.BUILT_UP;
  } else {
    return COLUMN_CLASSIFICATIONS.SIMPLE_SOLID;
  }
}

/**
 * Check compression design requirements
 * Based on NDS 3.6.3
 * 
 * @param actualStress - Actual compression stress (psi)
 * @param allowableStress - Allowable compression stress including Cp factor (psi)
 * @returns Design check result
 */
export function checkCompressionDesign(
  actualStress: number,
  allowableStress: number
): {
  ratio: number;
  acceptable: boolean;
} {
  const ratio = actualStress / allowableStress;
  
  return {
    ratio,
    acceptable: ratio <= 1.0 + DESIGN_TOLERANCES.EPSILON,
  };
}

/**
 * Analyze compression member without bending
 * Pure axial compression analysis
 * 
 * @param geometry - Member geometry
 * @param material - Member material properties
 * @param loading - Applied loads (axial only)
 * @param FcStar - Reference compression design value with adjustment factors except Cp
 * @param isConstruction - Whether this is during construction phase
 * @returns Compression member analysis results
 */
export function analyzeCompressionMember(
  geometry: CompressionMemberGeometry,
  material: CompressionMemberMaterial,
  loading: CompressionMemberLoading,
  FcStar: number,
  isConstruction: boolean = false
): CompressionMemberResults {
  // Validate inputs
  if (loading.axialForce <= 0) {
    throw new Error('Axial force must be positive (compression)');
  }
  
  if (FcStar <= 0) {
    throw new Error('Reference compression design value must be positive');
  }
  
  // Calculate geometric properties
  const area = calculateCrossSectionalArea(geometry);
  
  // Perform column stability analysis
  const stability = analyzeColumnStability(geometry, material, FcStar, isConstruction);
  
  // Calculate actual compression stress
  const fc = calculateCompressionStress(loading.axialForce, area);
  
  // Calculate allowable compression stress (with column stability factor)
  const Fc_adjusted = FcStar * stability.Cp;
  
  // Check compression design
  const compressionCheck = checkCompressionDesign(fc, Fc_adjusted);
  
  // Check slenderness ratio
  const slendernessOK = stability.slendernessRatios.controlling <= (isConstruction ? 75 : 50);
  
  // Determine controlling failure mode
  let controllingMode: 'compression' | 'stability' | 'slenderness' | 'combined_loading';
  
  if (!slendernessOK) {
    controllingMode = 'slenderness';
  } else if (stability.Cp < 0.9) {
    controllingMode = 'stability';
  } else {
    controllingMode = 'compression';
  }
  
  return {
    stability,
    stresses: {
      fc,
      Fc_adjusted,
    },
    utilization: {
      compression: compressionCheck.ratio,
    },
    checks: {
      compressionOK: compressionCheck.acceptable,
      slendernessOK,
      designOK: compressionCheck.acceptable && slendernessOK,
    },
    controllingMode,
  };
}

/**
 * Validate compression member geometry
 * 
 * @param geometry - Member geometry to validate
 * @throws Error if geometry is invalid
 */
export function validateCompressionMemberGeometry(geometry: CompressionMemberGeometry): void {
  if (geometry.length <= 0) {
    throw new Error('Member length must be positive');
  }
  
  if (geometry.dimensions.width <= 0) {
    throw new Error('Member width must be positive');
  }
  
  if (geometry.dimensions.depth !== undefined && geometry.dimensions.depth <= 0) {
    throw new Error('Member depth must be positive');
  }
  
  if (geometry.crossSection === 'tapered') {
    if (!geometry.dimensions.minDimension || !geometry.dimensions.maxDimension) {
      throw new Error('Tapered columns require minDimension and maxDimension');
    }
    
    if (geometry.dimensions.minDimension <= 0 || geometry.dimensions.maxDimension <= 0) {
      throw new Error('Tapered column dimensions must be positive');
    }
    
    if (geometry.dimensions.minDimension > geometry.dimensions.maxDimension) {
      throw new Error('Minimum dimension cannot be greater than maximum dimension');
    }
  }
}

/**
 * Validate compression member material properties
 * 
 * @param material - Material properties to validate
 * @throws Error if material properties are invalid
 */
export function validateCompressionMemberMaterial(material: CompressionMemberMaterial): void {
  const { designValues } = material;
  
  if (designValues.Fc <= 0) {
    throw new Error('Compression design value must be positive');
  }
  
  if (designValues.E <= 0) {
    throw new Error('Modulus of elasticity must be positive');
  }
  
  if (designValues.Emin <= 0) {
    throw new Error('Minimum modulus of elasticity must be positive');
  }
  
  if (designValues.Emin > designValues.E) {
    throw new Error('Minimum modulus of elasticity cannot be greater than E');
  }
  
  if (material.bucklingCoefficient !== undefined) {
    if (material.bucklingCoefficient <= 0 || material.bucklingCoefficient > 1) {
      throw new Error('Buckling coefficient must be between 0 and 1');
    }
  }
}

/**
 * Validate compression member loading
 * 
 * @param loading - Loading to validate
 * @throws Error if loading is invalid
 */
export function validateCompressionMemberLoading(loading: CompressionMemberLoading): void {
  if (loading.axialForce <= 0) {
    throw new Error('Axial force must be positive (compression)');
  }
  
  // Validate moments if present
  if (loading.moments) {
    // Moments can be positive or negative, but validate they are numbers
    if (loading.moments.M1 !== undefined && isNaN(loading.moments.M1)) {
      throw new Error('Moment M1 must be a valid number');
    }
    
    if (loading.moments.M2 !== undefined && isNaN(loading.moments.M2)) {
      throw new Error('Moment M2 must be a valid number');
    }
  }
  
  // Validate eccentricity if present
  if (loading.eccentricity) {
    if (loading.eccentricity.e1 !== undefined && loading.eccentricity.e1 < 0) {
      throw new Error('Eccentricity e1 must be non-negative');
    }
    
    if (loading.eccentricity.e2 !== undefined && loading.eccentricity.e2 < 0) {
      throw new Error('Eccentricity e2 must be non-negative');
    }
  }
} 