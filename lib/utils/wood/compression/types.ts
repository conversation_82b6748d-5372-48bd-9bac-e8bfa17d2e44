/**
 * Types for Wood Compression Member Design
 * Based on NDS 2018 Sections 3.6 and 3.7
 * 
 * References:
 * - NDS 2018 Section 3.6: Compression Members - General
 * - NDS 2018 Section 3.7: Solid Columns
 */

/**
 * Column classifications from NDS 3.6.2
 */
export const COLUMN_CLASSIFICATIONS = {
  SIMPLE_SOLID: 'simple_solid',
  SPACED_CONNECTOR_JOINED: 'spaced_connector_joined',
  BUILT_UP: 'built_up',
} as const;

export type ColumnClassification = typeof COLUMN_CLASSIFICATIONS[keyof typeof COLUMN_CLASSIFICATIONS];

/**
 * Cross-section shapes for columns
 */
export const COLUMN_CROSS_SECTIONS = {
  RECTANGULAR: 'rectangular',
  ROUND: 'round',
  TAPERED: 'tapered',
} as const;

export type ColumnCrossSection = typeof COLUMN_CROSS_SECTIONS[keyof typeof COLUMN_CROSS_SECTIONS];

/**
 * Support conditions for columns from NDS 3.7.2 (Tapered Columns)
 */
export const COLUMN_SUPPORT_CONDITIONS = {
  LARGE_END_FIXED_SMALL_END_UNSUPPORTED: 'large_fixed_small_unsupported',
  SMALL_END_FIXED_LARGE_END_UNSUPPORTED: 'small_fixed_large_unsupported',
  BOTH_ENDS_SIMPLY_SUPPORTED_TAPERED_ONE_END: 'both_simply_supported_one_end',
  BOTH_ENDS_SIMPLY_SUPPORTED_TAPERED_BOTH_ENDS: 'both_simply_supported_both_ends',
  BOTH_ENDS_SIMPLY_SUPPORTED: 'both_simply_supported',
} as const;

export type ColumnSupportCondition = typeof COLUMN_SUPPORT_CONDITIONS[keyof typeof COLUMN_SUPPORT_CONDITIONS];

/**
 * Buckling coefficient 'c' values from NDS 3.7.1
 */
export const BUCKLING_COEFFICIENTS = {
  SAWN_LUMBER: 0.8,
  ROUND_TIMBER_POLES_PILES: 0.85,
  STRUCTURAL_GLUED_LAMINATED: 0.9,
  STRUCTURAL_COMPOSITE_LUMBER: 0.9,
  CROSS_LAMINATED_TIMBER: 0.9,
} as const;

/**
 * Material types for compression members
 */
export const COMPRESSION_MATERIAL_TYPES = {
  SAWN_LUMBER: 'sawn_lumber',
  GLUED_LAMINATED_TIMBER: 'glued_laminated_timber',
  STRUCTURAL_COMPOSITE_LUMBER: 'structural_composite_lumber',
  ROUND_TIMBER_POLES: 'round_timber_poles',
  CROSS_LAMINATED_TIMBER: 'cross_laminated_timber',
} as const;

export type CompressionMaterialType = typeof COMPRESSION_MATERIAL_TYPES[keyof typeof COMPRESSION_MATERIAL_TYPES];

/**
 * Loading types for combined bending and axial loading (NDS 3.9)
 */
export const COMBINED_LOADING_TYPES = {
  BENDING_AND_AXIAL_TENSION: 'bending_and_axial_tension',
  BENDING_AND_AXIAL_COMPRESSION: 'bending_and_axial_compression',
  ECCENTRIC_COMPRESSION: 'eccentric_compression',
} as const;

export type CombinedLoadingType = typeof COMBINED_LOADING_TYPES[keyof typeof COMBINED_LOADING_TYPES];

/**
 * Bending directions for biaxial bending
 */
export const BENDING_DIRECTIONS = {
  STRONG_AXIS: 'strong_axis',  // About weak axis (fb1)
  WEAK_AXIS: 'weak_axis',      // About strong axis (fb2)
} as const;

export type BendingDirection = typeof BENDING_DIRECTIONS[keyof typeof BENDING_DIRECTIONS];

/**
 * Basic geometric properties for compression members
 */
export interface CompressionMemberGeometry {
  /** Length of member (in) */
  length: number;
  
  /** Cross-sectional dimensions */
  dimensions: {
    /** Width or diameter for round sections (in) */
    width: number;
    /** Depth for rectangular sections (in) */
    depth?: number;
    /** Minimum dimension for tapered columns (in) */
    minDimension?: number;
    /** Maximum dimension for tapered columns (in) */
    maxDimension?: number;
  };
  
  /** Cross-section shape */
  crossSection: ColumnCrossSection;
  
  /** Support conditions */
  supportCondition: ColumnSupportCondition;
  
  /** Effective length factors if known, otherwise calculated */
  effectiveLengthFactors?: {
    /** Effective length factor about strong axis */
    Ke1?: number;
    /** Effective length factor about weak axis */
    Ke2?: number;
  };
}

/**
 * Material properties for compression members
 */
export interface CompressionMemberMaterial {
  /** Material type */
  type: CompressionMaterialType;
  
  /** Reference design values */
  designValues: {
    /** Compression parallel to grain (psi) */
    Fc: number;
    /** Modulus of elasticity for stability calculations (psi) */
    E: number;
    /** Minimum modulus of elasticity (psi) */
    Emin: number;
    /** Bending design value if applicable (psi) */
    Fb?: number;
    /** Tension parallel to grain if applicable (psi) */
    Ft?: number;
  };
  
  /** Buckling coefficient 'c' */
  bucklingCoefficient?: number;
}

/**
 * Applied loads for compression members
 */
export interface CompressionMemberLoading {
  /** Axial compression force (lbs, positive in compression) */
  axialForce: number;
  
  /** Bending moments if applicable */
  moments?: {
    /** Moment about strong axis (lb-in) */
    M1?: number;
    /** Moment about weak axis (lb-in) */
    M2?: number;
  };
  
  /** Eccentricity if applicable */
  eccentricity?: {
    /** Eccentricity in strong axis direction (in) */
    e1?: number;
    /** Eccentricity in weak axis direction (in) */
    e2?: number;
  };
}

/**
 * Column stability analysis results
 */
export interface ColumnStabilityResults {
  /** Column stability factor Cp */
  Cp: number;
  
  /** Critical buckling design value Fce (psi) */
  Fce: number;
  
  /** Effective lengths */
  effectiveLengths: {
    /** Effective length about dimension d1 (in) */
    le1: number;
    /** Effective length about dimension d2 (in) */
    le2: number;
  };
  
  /** Slenderness ratios */
  slendernessRatios: {
    /** le1/d1 ratio */
    ratio1: number;
    /** le2/d2 ratio */
    ratio2: number;
    /** Controlling slenderness ratio */
    controlling: number;
  };
  
  /** Buckling coefficient used */
  bucklingCoefficient: number;
}

/**
 * Compression member analysis results
 */
export interface CompressionMemberResults {
  /** Column stability results */
  stability: ColumnStabilityResults;
  
  /** Design stresses */
  stresses: {
    /** Actual compression stress (psi) */
    fc: number;
    /** Allowable compression stress (psi) */
    Fc_adjusted: number;
    /** Bending stresses if applicable */
    bending?: {
      /** Actual bending stress about strong axis (psi) */
      fb1?: number;
      /** Actual bending stress about weak axis (psi) */
      fb2?: number;
      /** Allowable bending stress (psi) */
      Fb_adjusted?: number;
    };
  };
  
  /** Utilization ratios */
  utilization: {
    /** Compression utilization ratio */
    compression: number;
    /** Combined loading utilization ratio if applicable */
    combined?: number;
  };
  
  /** Design checks */
  checks: {
    /** Compression check passes */
    compressionOK: boolean;
    /** Slenderness ratio check passes */
    slendernessOK: boolean;
    /** Combined loading check passes if applicable */
    combinedLoadingOK?: boolean;
    /** Overall design OK */
    designOK: boolean;
  };
  
  /** Controlling failure mode */
  controllingMode: 'compression' | 'stability' | 'slenderness' | 'combined_loading';
} 