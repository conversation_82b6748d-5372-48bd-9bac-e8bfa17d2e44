/**
 * Constants for Wood Compression Member Design
 * Based on NDS 2018 Sections 3.6 and 3.7
 * 
 * References:
 * - NDS 2018 Section 3.6: Compression Members - General
 * - NDS 2018 Section 3.7: Solid Columns
 */

/**
 * Slenderness ratio limits from NDS 3.7.1.4
 * The slenderness ratio le/d shall not exceed 50, except during construction le/d shall not exceed 75
 */
export const SLENDERNESS_RATIO_LIMITS = {
  /** Maximum during normal service conditions */
  NORMAL_SERVICE: 50,
  /** Maximum during construction */
  CONSTRUCTION: 75,
} as const;

/**
 * Column stability factor formula constants from NDS 3.7.1.5
 * Cp = (1 + (Fce/Fc*)) / 2c - sqrt[((1 + (Fce/Fc*)) / 2c)^2 - (Fce/Fc*) / c]
 */
export const COLUMN_STABILITY_CONSTANTS = {
  /** Denominator constant in Cp formula */
  DENOMINATOR_FACTOR: 2,
  /** Used in square root term */
  SQRT_FACTOR: 1,
} as const;

/**
 * Critical buckling design value formula constants from NDS 3.7.1.5
 * Fce = (0.822 * E'min) / (le/d)^2
 */
export const CRITICAL_BUCKLING_CONSTANTS = {
  /** Coefficient in Fce formula */
  COEFFICIENT: 0.822,
} as const;

/**
 * Tapered column formula constants from NDS 3.7.2
 * d = dmin + (dmax - dmin) * [a - 0.15(1 - dmin/dmax)]
 */
export const TAPERED_COLUMN_CONSTANTS = {
  /** Constant in representative dimension formula */
  COEFFICIENT: 0.15,
  /** Factor for different support conditions */
  SUPPORT_CONDITION_FACTORS: {
    /** Large end fixed, small end unsupported or simply supported */
    LARGE_FIXED_SMALL_UNSUPPORTED: 0.70,
    /** Small end fixed, large end unsupported or simply supported */
    SMALL_FIXED_LARGE_UNSUPPORTED: 0.30,
    /** Both ends simply supported: tapered toward one end */
    BOTH_SIMPLY_SUPPORTED_ONE_END: 0.50,
    /** Both ends simply supported: tapered toward both ends */
    BOTH_SIMPLY_SUPPORTED_BOTH_ENDS: 0.70,
  },
} as const;

/**
 * Alternative formula for all other support conditions from NDS 3.7.2
 * d = dmin + (dmax - dmin)(1/3)
 */
export const ALTERNATIVE_TAPERED_FORMULA = {
  /** Factor for other support conditions */
  FACTOR: 1 / 3,
} as const;

/**
 * Combined bending and axial loading constants from NDS 3.9
 */
export const COMBINED_LOADING_CONSTANTS = {
  /** Maximum allowable interaction ratio */
  MAX_INTERACTION_RATIO: 1.0,
  
  /** Constants for biaxial bending formula NDS 3.9.2 */
  BIAXIAL_FcE1_COEFFICIENT: 0.822,
  BIAXIAL_FcE2_COEFFICIENT: 0.822,
  BIAXIAL_FbE_COEFFICIENT: 1.20,
  
  /** Exponent for buckling radius in biaxial bending */
  BUCKLING_RADIUS_EXPONENT: 2,
} as const;

/**
 * Material type to buckling coefficient mapping
 * From NDS 3.7.1.5 and related sections
 */
export const MATERIAL_BUCKLING_COEFFICIENTS = {
  SAWN_LUMBER: 0.8,
  ROUND_TIMBER_POLES_PILES: 0.85,
  STRUCTURAL_GLUED_LAMINATED: 0.9,
  STRUCTURAL_COMPOSITE_LUMBER: 0.9,
  CROSS_LAMINATED_TIMBER: 0.9,
} as const;

/**
 * Effective length factors for common support conditions
 * These are typical values - actual values should be determined by structural analysis
 */
export const TYPICAL_EFFECTIVE_LENGTH_FACTORS = {
  /** Pinned-pinned (simply supported both ends) */
  PINNED_PINNED: 1.0,
  /** Fixed-free (cantilever) */
  FIXED_FREE: 2.0,
  /** Fixed-pinned */
  FIXED_PINNED: 0.7,
  /** Fixed-fixed */
  FIXED_FIXED: 0.5,
} as const;

/**
 * Design check tolerances
 */
export const DESIGN_TOLERANCES = {
  /** Small value for numerical comparisons */
  EPSILON: 1e-10,
  /** Convergence tolerance for iterative calculations */
  CONVERGENCE_TOLERANCE: 1e-6,
  /** Maximum iterations for iterative solutions */
  MAX_ITERATIONS: 100,
} as const; 