/**
 * Tests for Compression Member Analysis
 * Based on NDS 2018 Section 3.6 and 3.7
 */

import {
  analyzeCompressionMember,
  calculateCrossSectionalArea,
  calculateSectionModulus,
  calculateCompressionStress,
  validateCompressionMemberGeometry,
  validateCompressionMemberMaterial,
  validateCompressionMemberLoading,
} from './compression-member';

import {
  CompressionMemberGeometry,
  CompressionMemberMaterial,
  CompressionMemberLoading,
  COLUMN_CROSS_SECTIONS,
  COLUMN_SUPPORT_CONDITIONS,
  COMPRESSION_MATERIAL_TYPES,
} from './types';

describe('Compression Member Analysis', () => {
  // Test data
  const sampleGeometry: CompressionMemberGeometry = {
    length: 120, // 10 feet
    dimensions: {
      width: 5.5,
      depth: 5.5,
    },
    crossSection: COLUMN_CROSS_SECTIONS.RECTANGULAR,
    supportCondition: COLUMN_SUPPORT_CONDITIONS.BOTH_ENDS_SIMPLY_SUPPORTED,
  };

  const sampleMaterial: CompressionMemberMaterial = {
    type: COMPRESSION_MATERIAL_TYPES.SAWN_LUMBER,
    designValues: {
      Fc: 1000, // psi
      E: 1200000, // psi
      Emin: 440000, // psi
    },
  };

  const sampleLoading: CompressionMemberLoading = {
    axialForce: 10000, // lbs
  };

  describe('Cross-sectional calculations', () => {
    test('should calculate rectangular cross-sectional area correctly', () => {
      const area = calculateCrossSectionalArea(sampleGeometry);
      expect(area).toBeCloseTo(30.25, 2); // 5.5 x 5.5
    });

    test('should calculate round cross-sectional area correctly', () => {
      const roundGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        crossSection: COLUMN_CROSS_SECTIONS.ROUND,
        dimensions: { width: 6.0 }, // 6" diameter
      };
      
      const area = calculateCrossSectionalArea(roundGeometry);
      const expectedArea = Math.PI * (3.0 * 3.0); // π * r²
      expect(area).toBeCloseTo(expectedArea, 2);
    });

    test('should calculate tapered cross-sectional area correctly', () => {
      const taperedGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        crossSection: COLUMN_CROSS_SECTIONS.TAPERED,
        dimensions: {
          width: 0, // Not used for tapered
          minDimension: 4.0,
          maxDimension: 6.0,
        },
      };
      
      const area = calculateCrossSectionalArea(taperedGeometry);
      const expectedArea = 5.0 * 5.0; // Average dimension squared
      expect(area).toBeCloseTo(expectedArea, 2);
    });
  });

  describe('Section modulus calculations', () => {
    test('should calculate rectangular section modulus correctly', () => {
      const sectionModulus = calculateSectionModulus(sampleGeometry);
      const expected = (5.5 * 5.5 * 5.5) / 6; // b * d² / 6
      expect(sectionModulus).toBeCloseTo(expected, 2);
    });

    test('should calculate round section modulus correctly', () => {
      const roundGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        crossSection: COLUMN_CROSS_SECTIONS.ROUND,
        dimensions: { width: 6.0 },
      };
      
      const sectionModulus = calculateSectionModulus(roundGeometry);
      const expected = (Math.PI * Math.pow(3.0, 3)) / 4; // π * r³ / 4
      expect(sectionModulus).toBeCloseTo(expected, 2);
    });
  });

  describe('Stress calculations', () => {
    test('should calculate compression stress correctly', () => {
      const stress = calculateCompressionStress(10000, 30.25);
      expect(stress).toBeCloseTo(330.58, 2);
    });

    test('should throw error for zero area', () => {
      expect(() => calculateCompressionStress(1000, 0)).toThrow('Cross-sectional area must be positive');
    });

    test('should handle negative force (convert to positive)', () => {
      const stress = calculateCompressionStress(-10000, 30.25);
      expect(stress).toBeCloseTo(330.58, 2);
    });
  });

  describe('Input validation', () => {
    describe('Geometry validation', () => {
      test('should pass valid rectangular geometry', () => {
        expect(() => validateCompressionMemberGeometry(sampleGeometry)).not.toThrow();
      });

      test('should reject negative length', () => {
        const invalidGeometry = { ...sampleGeometry, length: -10 };
        expect(() => validateCompressionMemberGeometry(invalidGeometry)).toThrow('Member length must be positive');
      });

      test('should reject negative width', () => {
        const invalidGeometry = { 
          ...sampleGeometry, 
          dimensions: { ...sampleGeometry.dimensions, width: -5 }
        };
        expect(() => validateCompressionMemberGeometry(invalidGeometry)).toThrow('Member width must be positive');
      });

      test('should require min/max dimensions for tapered columns', () => {
        const taperedGeometry: CompressionMemberGeometry = {
          ...sampleGeometry,
          crossSection: COLUMN_CROSS_SECTIONS.TAPERED,
          dimensions: { width: 5.5, depth: 5.5 }, // Missing min/max dimensions
        };
        
        expect(() => validateCompressionMemberGeometry(taperedGeometry))
          .toThrow('Tapered columns require minDimension and maxDimension');
      });

      test('should reject min > max for tapered columns', () => {
        const taperedGeometry: CompressionMemberGeometry = {
          ...sampleGeometry,
          crossSection: COLUMN_CROSS_SECTIONS.TAPERED,
          dimensions: {
            width: 0,
            minDimension: 8.0,
            maxDimension: 6.0, // Min > Max
          },
        };
        
        expect(() => validateCompressionMemberGeometry(taperedGeometry))
          .toThrow('Minimum dimension cannot be greater than maximum dimension');
      });
    });

    describe('Material validation', () => {
      test('should pass valid material properties', () => {
        expect(() => validateCompressionMemberMaterial(sampleMaterial)).not.toThrow();
      });

      test('should reject negative compression design value', () => {
        const invalidMaterial = {
          ...sampleMaterial,
          designValues: { ...sampleMaterial.designValues, Fc: -1000 }
        };
        expect(() => validateCompressionMemberMaterial(invalidMaterial))
          .toThrow('Compression design value must be positive');
      });

      test('should reject Emin > E', () => {
        const invalidMaterial = {
          ...sampleMaterial,
          designValues: { ...sampleMaterial.designValues, E: 400000, Emin: 500000 }
        };
        expect(() => validateCompressionMemberMaterial(invalidMaterial))
          .toThrow('Minimum modulus of elasticity cannot be greater than E');
      });

      test('should reject invalid buckling coefficient', () => {
        const invalidMaterial = {
          ...sampleMaterial,
          bucklingCoefficient: 1.5 // > 1.0
        };
        expect(() => validateCompressionMemberMaterial(invalidMaterial))
          .toThrow('Buckling coefficient must be between 0 and 1');
      });
    });

    describe('Loading validation', () => {
      test('should pass valid loading', () => {
        expect(() => validateCompressionMemberLoading(sampleLoading)).not.toThrow();
      });

      test('should reject negative or zero axial force', () => {
        const invalidLoading = { ...sampleLoading, axialForce: -1000 };
        expect(() => validateCompressionMemberLoading(invalidLoading))
          .toThrow('Axial force must be positive (compression)');
      });

      test('should reject zero axial force', () => {
        const invalidLoading = { ...sampleLoading, axialForce: 0 };
        expect(() => validateCompressionMemberLoading(invalidLoading))
          .toThrow('Axial force must be positive (compression)');
      });
    });
  });

  describe('Complete analysis', () => {
    test('should perform complete compression member analysis', () => {
      const FcStar = 800; // Adjusted compression design value
      
      const results = analyzeCompressionMember(
        sampleGeometry,
        sampleMaterial,
        sampleLoading,
        FcStar
      );

      // Check that results are returned
      expect(results).toBeDefined();
      expect(results.stability).toBeDefined();
      expect(results.stresses).toBeDefined();
      expect(results.utilization).toBeDefined();
      expect(results.checks).toBeDefined();

      // Check stress calculations
      expect(results.stresses.fc).toBeCloseTo(330.58, 1); // 10000 / 30.25
      expect(results.stresses.Fc_adjusted).toBeLessThanOrEqual(FcStar); // Should include Cp factor

      // Check that column stability factor is reasonable
      expect(results.stability.Cp).toBeGreaterThan(0);
      expect(results.stability.Cp).toBeLessThanOrEqual(1);

      // Check utilization ratio
      expect(results.utilization.compression).toBeGreaterThan(0);
    });

    test('should handle slender columns with reduced capacity', () => {
      const slenderGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        length: 240, // 20 feet - more slender
      };

      const FcStar = 800;
      
      const results = analyzeCompressionMember(
        slenderGeometry,
        sampleMaterial,
        sampleLoading,
        FcStar
      );

      // Slender column should have lower Cp factor
      expect(results.stability.Cp).toBeLessThan(0.9);
      expect(results.controllingMode).toBe('stability');
    });

    test('should fail for excessive slenderness ratio', () => {
      const verySlenderGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        length: 400, // Very long
        dimensions: { width: 2.0, depth: 2.0 }, // Small cross-section
      };

      const FcStar = 800;
      
      expect(() => analyzeCompressionMember(
        verySlenderGeometry,
        sampleMaterial,
        sampleLoading,
        FcStar
      )).toThrow(/Slenderness ratio.*exceeds limit/);
    });

    test('should handle construction phase with higher slenderness limit', () => {
      const slenderGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        length: 350, // Would exceed normal limit but OK for construction
        dimensions: { width: 3.5, depth: 3.5 },
      };

      const FcStar = 800;
      
      // Should pass for construction phase
      const results = analyzeCompressionMember(
        slenderGeometry,
        sampleMaterial,
        sampleLoading,
        FcStar,
        true // isConstruction = true
      );

      expect(results).toBeDefined();
      expect(results.checks.slendernessOK).toBe(true);
    });
  });

  describe('Edge cases', () => {
    test('should handle very stocky columns', () => {
      const stockyGeometry: CompressionMemberGeometry = {
        ...sampleGeometry,
        length: 24, // 2 feet - very short
      };

      const FcStar = 800;
      
      const results = analyzeCompressionMember(
        stockyGeometry,
        sampleMaterial,
        sampleLoading,
        FcStar
      );

      // Stocky column should have Cp close to 1.0
      expect(results.stability.Cp).toBeGreaterThan(0.95);
      expect(results.controllingMode).toBe('compression');
    });

    test('should handle very high loads', () => {
      const highLoading: CompressionMemberLoading = {
        axialForce: 50000, // Very high load
      };

      const FcStar = 800;
      
      const results = analyzeCompressionMember(
        sampleGeometry,
        sampleMaterial,
        highLoading,
        FcStar
      );

      expect(results.utilization.compression).toBeGreaterThan(1.0);
      expect(results.checks.designOK).toBe(false);
    });
  });
}); 