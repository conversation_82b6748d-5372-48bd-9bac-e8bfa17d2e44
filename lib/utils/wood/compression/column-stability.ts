/**
 * Column Stability Factor Calculations
 * Based on NDS 2018 Section 3.7.1
 * 
 * This module implements the column stability factor (Cp) calculations for solid wood columns
 * according to the National Design Specification for Wood Construction.
 * 
 * References:
 * - NDS 2018 Section 3.7.1: Column Stability Factor, Cp
 * - NDS 2018 Section 3.7.2: Tapered Columns
 * - NDS 2018 Section 3.7.3: Round Columns
 */

import {
  CompressionMemberGeometry,
  CompressionMemberMaterial,
  ColumnStabilityResults,
  ColumnCrossSection,
  ColumnSupportCondition,
  COLUMN_CROSS_SECTIONS,
  COLUMN_SUPPORT_CONDITIONS,
} from './types';

import {
  SLENDERNESS_RATIO_LIMITS,
  CRITICAL_BUCKLING_CONSTANTS,
  COLUMN_STABILITY_CONSTANTS,
  TAPERED_COLUMN_CONSTANTS,
  ALTERNATIVE_TAPERED_FORMULA,
  MATERIAL_BUCKLING_COEFFICIENTS,
  TYPICAL_EFFECTIVE_LENGTH_FACTORS,
  DESIGN_TOLERANCES,
} from './constants';

/**
 * Calculate effective lengths for compression members
 * Based on NDS ******* and engineering mechanics principles
 * 
 * @param geometry - Member geometry including length and dimensions
 * @returns Object containing effective lengths for both axes
 */
export function calculateEffectiveLengths(geometry: CompressionMemberGeometry): {
  le1: number;
  le2: number;
} {
  const { length, effectiveLengthFactors } = geometry;
  
  // Use provided effective length factors if available, otherwise use typical values
  let Ke1 = effectiveLengthFactors?.Ke1;
  let Ke2 = effectiveLengthFactors?.Ke2;
  
  // If not provided, estimate based on support conditions
  if (Ke1 === undefined || Ke2 === undefined) {
    // Default to pinned-pinned for simplicity - should be determined by analysis
    Ke1 = Ke1 ?? TYPICAL_EFFECTIVE_LENGTH_FACTORS.PINNED_PINNED;
    Ke2 = Ke2 ?? TYPICAL_EFFECTIVE_LENGTH_FACTORS.PINNED_PINNED;
  }
  
  return {
    le1: Ke1 * length,
    le2: Ke2 * length,
  };
}

/**
 * Calculate representative dimension for tapered columns
 * Based on NDS 3.7.2 equations (3.7-2) and (3.7-3)
 * 
 * @param minDimension - Minimum dimension of tapered column (in)
 * @param maxDimension - Maximum dimension of tapered column (in)
 * @param supportCondition - Support condition of the column
 * @returns Representative dimension for stability calculations
 */
export function calculateTaperedColumnRepresentativeDimension(
  minDimension: number,
  maxDimension: number,
  supportCondition: ColumnSupportCondition
): number {
  // Validate inputs
  if (minDimension <= 0 || maxDimension <= 0) {
    throw new Error('Column dimensions must be positive');
  }
  
  if (minDimension > maxDimension) {
    throw new Error('Minimum dimension cannot be greater than maximum dimension');
  }
  
  // Get support condition factor 'a' from NDS 3.7.2
  let a: number;
  
  switch (supportCondition) {
    case COLUMN_SUPPORT_CONDITIONS.LARGE_END_FIXED_SMALL_END_UNSUPPORTED:
      a = TAPERED_COLUMN_CONSTANTS.SUPPORT_CONDITION_FACTORS.LARGE_FIXED_SMALL_UNSUPPORTED;
      break;
    case COLUMN_SUPPORT_CONDITIONS.SMALL_END_FIXED_LARGE_END_UNSUPPORTED:
      a = TAPERED_COLUMN_CONSTANTS.SUPPORT_CONDITION_FACTORS.SMALL_FIXED_LARGE_UNSUPPORTED;
      break;
    case COLUMN_SUPPORT_CONDITIONS.BOTH_ENDS_SIMPLY_SUPPORTED_TAPERED_ONE_END:
      a = TAPERED_COLUMN_CONSTANTS.SUPPORT_CONDITION_FACTORS.BOTH_SIMPLY_SUPPORTED_ONE_END;
      break;
    case COLUMN_SUPPORT_CONDITIONS.BOTH_ENDS_SIMPLY_SUPPORTED_TAPERED_BOTH_ENDS:
      a = TAPERED_COLUMN_CONSTANTS.SUPPORT_CONDITION_FACTORS.BOTH_SIMPLY_SUPPORTED_BOTH_ENDS;
      break;
    default:
      // Use alternative formula (3.7-3) for all other support conditions
      return minDimension + (maxDimension - minDimension) * ALTERNATIVE_TAPERED_FORMULA.FACTOR;
  }
  
  // Apply equation (3.7-2): d = dmin + (dmax - dmin) * [a - 0.15(1 - dmin/dmax)]
  const dimensionRatio = minDimension / maxDimension;
  const coefficient = a - TAPERED_COLUMN_CONSTANTS.COEFFICIENT * (1 - dimensionRatio);
  
  return minDimension + (maxDimension - minDimension) * coefficient;
}

/**
 * Calculate slenderness ratios for compression members
 * Based on NDS 3.7.1.3
 * 
 * @param effectiveLengths - Effective lengths for both axes
 * @param geometry - Member geometry
 * @returns Object containing slenderness ratios
 */
export function calculateSlendernessRatios(
  effectiveLengths: { le1: number; le2: number },
  geometry: CompressionMemberGeometry
): {
  ratio1: number;
  ratio2: number;
  controlling: number;
} {
  const { dimensions, crossSection } = geometry;
  
  let d1: number, d2: number;
  
  if (crossSection === COLUMN_CROSS_SECTIONS.RECTANGULAR) {
    // For rectangular sections, use actual dimensions
    d1 = dimensions.width;
    d2 = dimensions.depth ?? dimensions.width; // Square if depth not provided
  } else if (crossSection === COLUMN_CROSS_SECTIONS.ROUND) {
    // For round sections, both dimensions are the diameter
    d1 = d2 = dimensions.width;
  } else if (crossSection === COLUMN_CROSS_SECTIONS.TAPERED) {
    // For tapered sections, calculate representative dimension
    if (!dimensions.minDimension || !dimensions.maxDimension) {
      throw new Error('Tapered columns require minDimension and maxDimension');
    }
    
    const representativeDimension = calculateTaperedColumnRepresentativeDimension(
      dimensions.minDimension,
      dimensions.maxDimension,
      geometry.supportCondition
    );
    
    d1 = d2 = representativeDimension;
  } else {
    throw new Error(`Unsupported cross-section type: ${crossSection}`);
  }
  
  // Calculate slenderness ratios
  const ratio1 = effectiveLengths.le1 / d1;
  const ratio2 = effectiveLengths.le2 / d2;
  
  // Controlling ratio is the larger of the two
  const controlling = Math.max(ratio1, ratio2);
  
  return { ratio1, ratio2, controlling };
}

/**
 * Calculate critical buckling design value Fce
 * Based on NDS ******* equation: Fce = (0.822 * E'min) / (le/d)^2
 * 
 * @param Emin - Minimum modulus of elasticity (psi)
 * @param slendernessRatio - Controlling slenderness ratio (le/d)
 * @returns Critical buckling design value (psi)
 */
export function calculateCriticalBucklingDesignValue(
  Emin: number,
  slendernessRatio: number
): number {
  if (Emin <= 0) {
    throw new Error('Minimum modulus of elasticity must be positive');
  }
  
  if (slendernessRatio <= 0) {
    throw new Error('Slenderness ratio must be positive');
  }
  
  // Apply equation: Fce = (0.822 * E'min) / (le/d)^2
  return (CRITICAL_BUCKLING_CONSTANTS.COEFFICIENT * Emin) / (slendernessRatio * slendernessRatio);
}

/**
 * Calculate column stability factor Cp
 * Based on NDS ******* equation (3.7-1)
 * 
 * @param Fce - Critical buckling design value (psi)
 * @param FcStar - Reference compression design value parallel to grain multiplied by applicable adjustment factors except Cp (psi)
 * @param c - Buckling coefficient for the material type
 * @returns Column stability factor Cp (dimensionless)
 */
export function calculateColumnStabilityFactor(
  Fce: number,
  FcStar: number,
  c: number
): number {
  if (Fce <= 0 || FcStar <= 0) {
    throw new Error('Design values must be positive');
  }
  
  if (c <= 0 || c > 1) {
    throw new Error('Buckling coefficient must be between 0 and 1');
  }
  
  // Calculate intermediate values
  const ratio = Fce / FcStar;
  const numerator = 1 + ratio;
  const denominator = COLUMN_STABILITY_CONSTANTS.DENOMINATOR_FACTOR * c;
  
  // First term: (1 + Fce/Fc*) / 2c
  const firstTerm = numerator / denominator;
  
  // Second term: sqrt[((1 + Fce/Fc*) / 2c)^2 - (Fce/Fc*) / c]
  const underSqrt = firstTerm * firstTerm - ratio / c;
  
  // Check for negative value under square root (shouldn't happen with valid inputs)
  if (underSqrt < 0) {
    throw new Error('Invalid calculation state: negative value under square root');
  }
  
  const secondTerm = Math.sqrt(underSqrt);
  
  // Calculate Cp
  const Cp = firstTerm - secondTerm;
  
  // Cp should be between 0 and 1, but mathematically could be slightly outside due to rounding
  return Math.max(0, Math.min(1, Cp));
}

/**
 * Get buckling coefficient for material type
 * Based on NDS ******* and material specifications
 * 
 * @param materialType - Type of compression member material
 * @param customBucklingCoefficient - Optional custom buckling coefficient
 * @returns Buckling coefficient 'c'
 */
export function getBucklingCoefficient(
  materialType: string,
  customBucklingCoefficient?: number
): number {
  // Use custom coefficient if provided
  if (customBucklingCoefficient !== undefined) {
    if (customBucklingCoefficient <= 0 || customBucklingCoefficient > 1) {
      throw new Error('Custom buckling coefficient must be between 0 and 1');
    }
    return customBucklingCoefficient;
  }
  
  // Map material type to buckling coefficient
  const coefficient = (MATERIAL_BUCKLING_COEFFICIENTS as any)[materialType.toUpperCase().replace(/\s+/g, '_')];
  
  if (coefficient === undefined) {
    throw new Error(`Unknown material type: ${materialType}`);
  }
  
  return coefficient;
}

/**
 * Check slenderness ratio limits
 * Based on NDS 3.7.1.4
 * 
 * @param slendernessRatio - Controlling slenderness ratio
 * @param isConstruction - Whether this is during construction phase
 * @returns Object indicating if ratio is acceptable and the limit used
 */
export function checkSlendernessRatioLimits(
  slendernessRatio: number,
  isConstruction: boolean = false
): {
  acceptable: boolean;
  limit: number;
  ratio: number;
} {
  const limit = isConstruction 
    ? SLENDERNESS_RATIO_LIMITS.CONSTRUCTION 
    : SLENDERNESS_RATIO_LIMITS.NORMAL_SERVICE;
  
  return {
    acceptable: slendernessRatio <= limit,
    limit,
    ratio: slendernessRatio,
  };
}

/**
 * Perform complete column stability analysis
 * This is the main function that combines all stability calculations
 * 
 * @param geometry - Member geometry
 * @param material - Member material properties
 * @param FcStar - Reference compression design value with adjustment factors except Cp
 * @param isConstruction - Whether this is during construction phase
 * @returns Complete column stability analysis results
 */
export function analyzeColumnStability(
  geometry: CompressionMemberGeometry,
  material: CompressionMemberMaterial,
  FcStar: number,
  isConstruction: boolean = false
): ColumnStabilityResults {
  // Calculate effective lengths
  const effectiveLengths = calculateEffectiveLengths(geometry);
  
  // Calculate slenderness ratios
  const slendernessRatios = calculateSlendernessRatios(effectiveLengths, geometry);
  
  // Check slenderness ratio limits
  const slendernessCheck = checkSlendernessRatioLimits(slendernessRatios.controlling, isConstruction);
  
  if (!slendernessCheck.acceptable) {
    throw new Error(
      `Slenderness ratio ${slendernessRatios.controlling.toFixed(1)} exceeds limit of ${slendernessCheck.limit}`
    );
  }
  
  // Get buckling coefficient
  const bucklingCoefficient = getBucklingCoefficient(
    material.type,
    material.bucklingCoefficient
  );
  
  // Calculate critical buckling design value
  const Fce = calculateCriticalBucklingDesignValue(
    material.designValues.Emin,
    slendernessRatios.controlling
  );
  
  // Calculate column stability factor
  const Cp = calculateColumnStabilityFactor(Fce, FcStar, bucklingCoefficient);
  
  return {
    Cp,
    Fce,
    effectiveLengths,
    slendernessRatios,
    bucklingCoefficient,
  };
} 