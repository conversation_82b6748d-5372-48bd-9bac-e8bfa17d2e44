/**
 * Solid Column Analysis
 * Based on NDS 2018 Section 3.7 - Solid Columns
 * 
 * This module provides analysis for solid wood columns including rectangular,
 * round, and tapered columns according to NDS specifications.
 * 
 * References:
 * - NDS 2018 Section 3.7: Solid Columns
 * - NDS 2018 Section 3.7.2: Tapered Columns
 * - NDS 2018 Section 3.7.3: Round Columns
 */

import {
  CompressionMemberGeometry,
  CompressionMemberMaterial,
  CompressionMemberLoading,
  CompressionMemberResults,
  COLUMN_CROSS_SECTIONS,
} from './types';

import {
  analyzeCompressionMember,
  calculateCrossSectionalArea,
  calculateSectionModulus,
  calculateCompressionStress,
  calculateBendingStress,
  validateCompressionMemberGeometry,
  validateCompressionMemberMaterial,
  validateCompressionMemberLoading,
} from './compression-member';

import { analyzeColumnStability } from './column-stability';
import { DESIGN_TOLERANCES } from './constants';

/**
 * Calculate moment of inertia for solid columns
 * 
 * @param geometry - Column geometry
 * @returns Moment of inertia about strong axis (in^4)
 */
export function calculateMomentOfInertia(geometry: CompressionMemberGeometry): number {
  const { dimensions, crossSection } = geometry;
  
  switch (crossSection) {
    case COLUMN_CROSS_SECTIONS.RECTANGULAR:
      const width = dimensions.width;
      const depth = dimensions.depth ?? dimensions.width;
      // Moment of inertia about strong axis (assuming depth is strong direction)
      return (width * Math.pow(depth, 3)) / 12;
      
    case COLUMN_CROSS_SECTIONS.ROUND:
      const radius = dimensions.width / 2;
      return (Math.PI * Math.pow(radius, 4)) / 4;
      
    case COLUMN_CROSS_SECTIONS.TAPERED:
      // For tapered sections, use representative dimension
      if (!dimensions.minDimension || !dimensions.maxDimension) {
        throw new Error('Tapered columns require minDimension and maxDimension');
      }
      // Use minimum dimension for conservative estimate
      const d = dimensions.minDimension;
      return (Math.pow(d, 4)) / 12;
      
    default:
      throw new Error(`Unsupported cross-section type: ${crossSection}`);
  }
}

/**
 * Calculate radius of gyration for solid columns
 * 
 * @param geometry - Column geometry
 * @returns Radius of gyration about strong axis (in)
 */
export function calculateRadiusOfGyration(geometry: CompressionMemberGeometry): number {
  const area = calculateCrossSectionalArea(geometry);
  const momentOfInertia = calculateMomentOfInertia(geometry);
  
  return Math.sqrt(momentOfInertia / area);
}

/**
 * Analyze rectangular solid column
 * Based on NDS 3.7.1 for rectangular cross-sections
 * 
 * @param geometry - Column geometry (must be rectangular)
 * @param material - Material properties
 * @param loading - Applied loads
 * @param FcStar - Reference compression design value with adjustment factors except Cp
 * @param isConstruction - Whether this is during construction phase
 * @returns Complete analysis results
 */
export function analyzeRectangularColumn(
  geometry: CompressionMemberGeometry,
  material: CompressionMemberMaterial,
  loading: CompressionMemberLoading,
  FcStar: number,
  isConstruction: boolean = false
): CompressionMemberResults {
  // Validate that this is a rectangular column
  if (geometry.crossSection !== COLUMN_CROSS_SECTIONS.RECTANGULAR) {
    throw new Error('This function is only for rectangular columns');
  }
  
  // Validate inputs
  validateCompressionMemberGeometry(geometry);
  validateCompressionMemberMaterial(material);
  validateCompressionMemberLoading(loading);
  
  // If no bending moments, use simple compression analysis
  if (!loading.moments || (!loading.moments.M1 && !loading.moments.M2)) {
    return analyzeCompressionMember(geometry, material, loading, FcStar, isConstruction);
  }
  
  // Combined bending and compression analysis would go here
  // For now, fall back to compression-only analysis
  return analyzeCompressionMember(geometry, material, loading, FcStar, isConstruction);
}

/**
 * Analyze round solid column
 * Based on NDS 3.7.3 for round cross-sections
 * 
 * @param geometry - Column geometry (must be round)
 * @param material - Material properties
 * @param loading - Applied loads
 * @param FcStar - Reference compression design value with adjustment factors except Cp
 * @param isConstruction - Whether this is during construction phase
 * @returns Complete analysis results
 */
export function analyzeRoundColumn(
  geometry: CompressionMemberGeometry,
  material: CompressionMemberMaterial,
  loading: CompressionMemberLoading,
  FcStar: number,
  isConstruction: boolean = false
): CompressionMemberResults {
  // Validate that this is a round column
  if (geometry.crossSection !== COLUMN_CROSS_SECTIONS.ROUND) {
    throw new Error('This function is only for round columns');
  }
  
  // Validate inputs
  validateCompressionMemberGeometry(geometry);
  validateCompressionMemberMaterial(material);
  validateCompressionMemberLoading(loading);
  
  // Round columns follow the same analysis as rectangular columns
  // but with circular cross-section properties
  // The design calculations for round columns are based on design calculations
  // for a square column of the same cross-sectional area and having the same
  // degree of taper (NDS 3.7.3)
  
  return analyzeCompressionMember(geometry, material, loading, FcStar, isConstruction);
}

/**
 * Analyze tapered solid column
 * Based on NDS 3.7.2 for tapered columns
 * 
 * @param geometry - Column geometry (must be tapered)
 * @param material - Material properties
 * @param loading - Applied loads
 * @param FcStar - Reference compression design value with adjustment factors except Cp
 * @param isConstruction - Whether this is during construction phase
 * @returns Complete analysis results
 */
export function analyzeTaperedColumn(
  geometry: CompressionMemberGeometry,
  material: CompressionMemberMaterial,
  loading: CompressionMemberLoading,
  FcStar: number,
  isConstruction: boolean = false
): CompressionMemberResults {
  // Validate that this is a tapered column
  if (geometry.crossSection !== COLUMN_CROSS_SECTIONS.TAPERED) {
    throw new Error('This function is only for tapered columns');
  }
  
  // Validate inputs
  validateCompressionMemberGeometry(geometry);
  validateCompressionMemberMaterial(material);
  validateCompressionMemberLoading(loading);
  
  // Tapered columns require special handling for representative dimension
  // This is handled in the column stability calculations
  
  return analyzeCompressionMember(geometry, material, loading, FcStar, isConstruction);
}

/**
 * Analyze any solid column type
 * Automatically dispatches to the appropriate analysis function based on cross-section
 * 
 * @param geometry - Column geometry
 * @param material - Material properties
 * @param loading - Applied loads
 * @param FcStar - Reference compression design value with adjustment factors except Cp
 * @param isConstruction - Whether this is during construction phase
 * @returns Complete analysis results
 */
export function analyzeSolidColumn(
  geometry: CompressionMemberGeometry,
  material: CompressionMemberMaterial,
  loading: CompressionMemberLoading,
  FcStar: number,
  isConstruction: boolean = false
): CompressionMemberResults {
  switch (geometry.crossSection) {
    case COLUMN_CROSS_SECTIONS.RECTANGULAR:
      return analyzeRectangularColumn(geometry, material, loading, FcStar, isConstruction);
      
    case COLUMN_CROSS_SECTIONS.ROUND:
      return analyzeRoundColumn(geometry, material, loading, FcStar, isConstruction);
      
    case COLUMN_CROSS_SECTIONS.TAPERED:
      return analyzeTaperedColumn(geometry, material, loading, FcStar, isConstruction);
      
    default:
      throw new Error(`Unsupported column cross-section: ${geometry.crossSection}`);
  }
}

/**
 * Calculate allowable bearing stress for end-to-end bearing
 * Based on NDS 3.6.4 - Compression Members Bearing End to End
 * 
 * @param FcPerp - Compression perpendicular to grain design value (psi)
 * @param adjustmentFactors - Array of applicable adjustment factors
 * @returns Allowable bearing stress (psi)
 */
export function calculateEndBearingStress(
  FcPerp: number,
  adjustmentFactors: number[] = []
): number {
  if (FcPerp <= 0) {
    throw new Error('Compression perpendicular design value must be positive');
  }
  
  // Apply adjustment factors
  let adjustedFcPerp = FcPerp;
  for (const factor of adjustmentFactors) {
    if (factor <= 0) {
      throw new Error('Adjustment factors must be positive');
    }
    adjustedFcPerp *= factor;
  }
  
  return adjustedFcPerp;
}

/**
 * Check end bearing design
 * Based on NDS 3.6.4
 * 
 * @param appliedBearingStress - Applied bearing stress (psi)
 * @param allowableBearingStress - Allowable bearing stress (psi)
 * @returns Design check result
 */
export function checkEndBearingDesign(
  appliedBearingStress: number,
  allowableBearingStress: number
): {
  ratio: number;
  acceptable: boolean;
} {
  if (allowableBearingStress <= 0) {
    throw new Error('Allowable bearing stress must be positive');
  }
  
  const ratio = appliedBearingStress / allowableBearingStress;
  
  return {
    ratio,
    acceptable: ratio <= 1.0 + DESIGN_TOLERANCES.EPSILON,
  };
}

/**
 * Get cross-sectional properties summary for solid columns
 * 
 * @param geometry - Column geometry
 * @returns Object containing all geometric properties
 */
export function getSolidColumnProperties(geometry: CompressionMemberGeometry): {
  area: number;
  momentOfInertia: number;
  sectionModulus: number;
  radiusOfGyration: number;
  dimensions: {
    width: number;
    depth?: number;
    diameter?: number;
    minDimension?: number;
    maxDimension?: number;
  };
} {
  const area = calculateCrossSectionalArea(geometry);
  const momentOfInertia = calculateMomentOfInertia(geometry);
  const sectionModulus = calculateSectionModulus(geometry);
  const radiusOfGyration = calculateRadiusOfGyration(geometry);
  
  let dimensions: any = {
    width: geometry.dimensions.width,
  };
  
  if (geometry.crossSection === COLUMN_CROSS_SECTIONS.RECTANGULAR) {
    dimensions.depth = geometry.dimensions.depth ?? geometry.dimensions.width;
  } else if (geometry.crossSection === COLUMN_CROSS_SECTIONS.ROUND) {
    dimensions.diameter = geometry.dimensions.width;
  } else if (geometry.crossSection === COLUMN_CROSS_SECTIONS.TAPERED) {
    dimensions.minDimension = geometry.dimensions.minDimension;
    dimensions.maxDimension = geometry.dimensions.maxDimension;
  }
  
  return {
    area,
    momentOfInertia,
    sectionModulus,
    radiusOfGyration,
    dimensions,
  };
} 