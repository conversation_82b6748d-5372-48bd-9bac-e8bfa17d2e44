/**
 * Wood Structural Design Utilities
 * Based on NDS (National Design Specification) for Wood Construction
 * 
 * This module provides comprehensive functionality for designing wood structural members
 * including beams, columns, tension members, and combined loading scenarios.
 */

// Export existing modules with explicit re-exports to resolve naming conflicts
export * from './beam';

// Export adjustment factors with explicit re-exports for conflicting types
export {
  // Load Duration Factors
  getLoadDurationFactorCD,
  getLoadDurationFactorByLoadType,
  getLoadDurationCategoryByLoadType,
  isDesignValueExemptFromLoadDuration,
  validateLoadDurationFactor,
  applyLoadDurationFactor,
  applyLoadDurationFactorByLoadType,
  getMultipleLoadDurationFactors,
  getControllingLoadDurationFactor,
  getLoadDurationAnalysis,

  // Incising Factors
  getIncisingFactorCi,
  validateIncisingSpecifications,
  applyIncisingFactor,
  getMultipleIncisingFactors,
  getIncisingFactorByName,

  // Temperature Factors
  getTemperatureFactor,
  applyTemperatureFactor,
  validateTemperatureFactorInput,
  getMultipleTemperatureFactors,
  getTemperatureFactorAnalysis,
  isDesignValueAffectedByTemperature,
  getEffectiveMoistureCondition,
  type TemperatureFactorInput,
  type TemperatureFactorValidation,
  type TemperatureFactorAnalysis,

  // Wet Service Factors
  getWetServiceFactor,
  applyWetServiceFactor,
  validateWetServiceFactorInput,
  getMultipleWetServiceFactors,
  getWetServiceFactorAnalysis,
  isWetServiceRequired,
  getWetServiceMoistureThreshold,
  checkWetServiceSpecialConditions,
  isDesignValueAffectedByWetService,
  type WetServiceFactorInput,
  type WetServiceFactorValidation,
  type WetServiceFactorAnalysis,
  type WetServiceLumberCategory,
  type WetServiceSpeciesGroup,
  WET_SERVICE_LUMBER_CATEGORIES,
  WET_SERVICE_SPECIES_GROUPS,
  WET_SERVICE_MOISTURE_THRESHOLDS,
  SAWN_LUMBER_WET_SERVICE_FACTORS,
  GLULAM_WET_SERVICE_FACTORS,
  WET_SERVICE_SPECIAL_CONDITIONS,

  // Repetitive Member Factor (Cr) exports
  getRepetitiveMemberFactor,
  validateRepetitiveMemberFactorInput,
  getRepetitiveMemberFactorAnalysis,
  applyRepetitiveMemberFactor,
  isRepetitiveMemberFactorApplicable,
  getMultipleRepetitiveMemberFactors,
  isMemberTypeQualified,
  getQualifiedMemberTypes,
  getQualifiedLoadDistributingElements,
  type RepetitiveMemberFactorInput,
  type RepetitiveMemberFactorValidation,
  type RepetitiveMemberFactorAnalysis,

  // Repetitive Member Factor Constants exports
  REPETITIVE_MEMBER_FACTOR_CONSTANTS,
  REPETITIVE_MEMBER_TYPES,
  type RepetitiveMemberType,
  LOAD_DISTRIBUTING_ELEMENTS,
  type LoadDistributingElement,
  REPETITIVE_MEMBER_LUMBER_CATEGORIES,
  type RepetitiveMemberLumberCategory,

  // Beam Stability Factors
  getBeamStabilityFactorCL,
  getEffectiveLengthFactor,
  getBeamStabilityFactorCLDetailed,

  // LRFD Factors
  getLrfdFormatConversionFactorKF,
  getLrfdResistanceFactorPhi,
  getLrfdTimeEffectFactorLambda,
  calculateAdjustedLrfdDesignValue,
  getLrfdFactorsAnalysis,
  isDesignValueSupportedForLrfd,
  getAvailableTimeEffectFactors,

  // Size Factor (CF) and Flat Use Factor (Cflu) exports
  SIZE_FACTOR_LUMBER_GRADES,
  SIZE_FACTOR_DIMENSIONS,
  SIZE_FACTORS,
  FLAT_USE_FACTORS,
  SIZE_FACTOR_CONSTANTS,
  type SizeFactorInput,
  type SizeFactorValidation,
  type SizeFactorAnalysis,
  type SizeFactorLumberGrade,
  type SizeFactorDimension,
  validateSizeFactorInput,
  getDimensionCategory,
  getSizeFactor,
  getFlatUseFactor,
  applySizeFactor,
  getSizeFactorAnalysis,
  isDesignValueAffectedBySizeFactor,

  // Flat Use Factor (Cfu) exports
  FLAT_USE_LUMBER_GRADES,
  FLAT_USE_DIMENSIONS,
  FLAT_USE_FACTORS as FLAT_USE_FACTOR_VALUES,
  FLAT_USE_CONSTANTS,
  type FlatUseFactorInput,
  type FlatUseFactorValidation,
  type FlatUseFactorAnalysis,
  type FlatUseLumberGrade,
  type FlatUseDimension,
  validateFlatUseFactorInput,
  getFlatUseDimensionCategory,
  getFlatUseFactor as getFlatUseFactorCfu,
  applyFlatUseFactor,
  getFlatUseFactorAnalysis,
  getMultipleFlatUseFactors,
  isDesignValueAffectedByFlatUse,
  getOrientationOptimization,

  // Volume Factor (CV) exports
  VOLUME_FACTOR_PRODUCTS,
  VOLUME_FACTOR_PARAMETERS,
  VOLUME_FACTOR_CONSTANTS,
  type VolumeFactorInput,
  type VolumeFactorValidation,
  type VolumeFactorAnalysis,
  type VolumeFactorCalculation,
  type VolumeFactorProduct,
  validateVolumeFactorInput,
  calculateVolumeFactorDetails,
  getVolumeFactor,
  applyVolumeFactor,
  getVolumeFactorAnalysis,
  getMultipleVolumeFactors,
  isDesignValueAffectedByVolume,
  getVolumeFactorOptimization,

  // Notch Validation and Adjustment exports (with aliased MemberGeometry)
  validateNotchConfiguration,
  calculateNotchShearAdjustment,
  analyzeStiffnessImpact,
  performNotchAnalysis,
  type NotchValidationInput,
  type NotchShearAdjustmentInput,
  type NotchValidationResult,
  type NotchAnalysisResult,
  type StiffnessImpactAnalysis,
  type ShearStrengthAdjustmentResult,
  type NotchGeometry,
  type MemberGeometry as NotchMemberGeometry,
  type ConnectionGeometry,

  // Notch Constants exports
  NOTCH_WOOD_PRODUCT_TYPES,
  type NotchWoodProductType,
  NOTCH_LOCATION_TYPES,
  type NotchLocationType,
  NOTCH_FACE_TYPES,
  type NotchFaceType,
  NOTCH_SPAN_REGIONS,
  type NotchSpanRegion,
  NOTCH_CROSS_SECTION_SHAPES,
  type NotchCrossSectionShape,
  SAWN_LUMBER_NOTCH_LIMITS,
  GLULAM_NOTCH_LIMITS,
  STRUCTURAL_COMPOSITE_NOTCH_LIMITS,
  NOTCH_STIFFNESS_THRESHOLDS,
  NOTCH_SHEAR_COEFFICIENTS,
  NOTCH_STRESS_CONCENTRATION,
  NOTCH_STRENGTH_REFERENCES,
  GENERAL_NOTCH_PROHIBITIONS,

  // Bearing Area Factor (Cb) exports
  getBearingAreaFactor,
  applyBearingAreaFactor,
  calculateBearingAtAngle,
  getBearingAreaFactorAnalysis,
  getBearingAtAngleAnalysis,
  validateBearingAreaFactorInput,
  validateBearingAtAngleInput,
  getEffectiveBearingLength,
  calculateBearingAreaFactorFormula,
  getBearingAreaFactorFromTable,
  getMultipleBearingAreaFactors,
  isDesignValueAffectedByBearingAreaFactor,
  getBearingAreaFactorOptimization,
  type BearingAreaFactorInput,
  type BearingAtAngleInput,
  type BearingAreaFactorValidation,
  type BearingAreaFactorAnalysis,
  type BearingAtAngleAnalysis,
  type BearingConfigurationType,

  // Bearing Area Factor Constants exports
  BEARING_AREA_FACTORS_TABLE,
  BEARING_AREA_FACTOR_CONSTANTS,
  BEARING_CONFIGURATION_TYPES,

  // Curvature Factor (Cc) exports - Glulam NDS 5.3.8
  getCurvatureFactor,
  getCurvatureFactorForStraightMember,
  applyCurvatureFactor,
  validateCurvatureFactorInput,
  calculateCurvatureFactorDetails,
  getCurvatureFactorAnalysis,
  getMultipleCurvatureFactors,
  isDesignValueAffectedByCurvature,
  getCurvatureFactorOptimization,
  CURVATURE_SPECIES_GROUPS,
  CURVATURE_MEMBER_TYPES,
  CURVATURE_FACTOR_CONSTANTS,
  type CurvatureFactorInput,
  type CurvatureFactorValidation,
  type CurvatureFactorCalculation,
  type CurvatureFactorAnalysis,
  type CurvatureSpeciesGroup,
  type CurvatureMemberType,

  // Stress Interaction Factor (CI) exports - Glulam NDS 5.3.9
  getStressInteractionFactor,
  getStressInteractionFactorForStraightMember,
  applyStressInteractionFactor,
  validateStressInteractionFactorInput,
  calculateStressInteractionFactorDetails,
  getStressInteractionFactorAnalysis,
  getMultipleStressInteractionFactors,
  isDesignValueAffectedByStressInteraction,
  getStressInteractionFactorOptimization,
  classifyTaperSeverity,
  determineMemberType,
  STRESS_INTERACTION_MEMBER_TYPES,
  TAPER_SEVERITY_LEVELS,
  STRESS_INTERACTION_FACTOR_CONSTANTS,
  type StressInteractionFactorInput,
  type StressInteractionFactorValidation,
  type StressInteractionFactorCalculation,
  type StressInteractionFactorAnalysis,
  type StressInteractionMemberType,
  type TaperSeverityLevel,

  // Shear Reduction Factor (Cvr) exports - Glulam NDS 5.3.10
  getShearReductionFactor,
  getShearReductionFactorForStandardConditions,
  applyShearReductionFactor,
  validateShearReductionFactorInput,
  calculateShearReductionFactorDetails,
  getShearReductionFactorAnalysis,
  getMultipleShearReductionFactors,
  isDesignValueAffectedByShearReduction,
  getShearReductionFactorOptimization,
  getStandardShearReductionFactorScenarios,
  determineActiveConditions,
  assessReductionSeverity,
  generateReductionReasons,
  SHEAR_REDUCTION_CONDITIONS,
  SHEAR_REDUCTION_SEVERITY,
  SHEAR_REDUCTION_FACTOR_CONSTANTS,
  type ShearReductionFactorInput,
  type ShearReductionFactorValidation,
  type ShearReductionFactorCalculation,
  type ShearReductionFactorAnalysis,
  type ShearReductionCondition,
  type ShearReductionSeverity,

} from './adjustment-factors';

// Export new NDS 3.6-3.9 implementation modules  
export * as Compression from './compression';
export * as Tension from './tension';
export * as CombinedLoading from './combined-loading';

// Export NDS Chapter 15 modules
export * as LateralDistribution from './lateral-distribution';
export * as SpacedColumns from './spaced-columns';
export * as BuiltUpColumns from './built-up-columns';

// Export shared constants
export * from './constants'; 