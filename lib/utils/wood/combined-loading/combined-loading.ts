/**
 * Combined Bending and Axial Loading Analysis
 * Based on NDS 2018 Section 3.9 - Combined Bending and Axial Loading
 * 
 * This module implements the interaction equations for wood members subjected to
 * combined bending and axial loading according to NDS 3.9.
 * 
 * References:
 * - NDS 2018 Section 3.9.1: Bending and Axial Tension (Equations 3.9-1 and 3.9-2)
 * - NDS 2018 Section 3.9.2: Bending and Axial Compression (Equation 3.9-3 and 3.9-4)
 * - NDS 2018 Section 3.9.3: Eccentric Compression Loading
 */

import {
  CombinedLoadingGeometry,
  CombinedLoadingMaterial,
  CombinedLoadingForces,
  CombinedLoadingResults,
  CombinedLoadingScenario,
  CriticalBucklingValues,
  COMBINED_LOADING_SCENARIOS,
} from './types';

import {
  INTERACTION_CONSTANTS,
  COMBINED_LOADING_TOLERANCES,
} from './constants';

/**
 * Calculate cross-sectional properties for combined loading analysis
 * 
 * @param geometry - Member geometry
 * @returns Object containing area and section moduli
 */
export function calculateCombinedLoadingProperties(geometry: CombinedLoadingGeometry): {
  area: number;
  sectionModulus1: number;
  sectionModulus2: number;
} {
  const { width, depth } = geometry.dimensions;
  
  const area = width * depth;
  const sectionModulus1 = (width * depth * depth) / 6; // Strong axis (about weak axis)
  const sectionModulus2 = (depth * width * width) / 6; // Weak axis (about strong axis)
  
  return {
    area,
    sectionModulus1,
    sectionModulus2,
  };
}

/**
 * Calculate critical buckling design values for biaxial analysis
 * Based on NDS 3.9.2
 * 
 * @param geometry - Member geometry
 * @param material - Material properties
 * @returns Critical buckling values
 */
export function calculateCriticalBucklingValues(
  geometry: CombinedLoadingGeometry,
  material: CombinedLoadingMaterial
): CriticalBucklingValues {
  const { dimensions, effectiveLengths } = geometry;
  const { designValues } = material;
  
  // Default effective lengths to actual length if not provided
  const le1 = effectiveLengths?.le1 ?? geometry.length;
  const le2 = effectiveLengths?.le2 ?? geometry.length;
  
  // Calculate slenderness ratios
  const slenderness1 = le1 / dimensions.depth; // Strong axis
  const slenderness2 = le2 / dimensions.width; // Weak axis
  
  // Calculate critical buckling values per NDS 3.9.2
  const FcE1 = (INTERACTION_CONSTANTS.CRITICAL_BUCKLING_COEFFICIENT * designValues.Emin) / 
                (slenderness1 * slenderness1);
  
  const FcE2 = (INTERACTION_CONSTANTS.CRITICAL_BUCKLING_COEFFICIENT * designValues.Emin) / 
                (slenderness2 * slenderness2);
  
  // Calculate radius of gyration for biaxial bending
  const area = dimensions.width * dimensions.depth;
  const momentOfInertia = (dimensions.width * Math.pow(dimensions.depth, 3)) / 12;
  const radiusOfGyration = Math.sqrt(momentOfInertia / area);
  
  const FbE = (INTERACTION_CONSTANTS.BIAXIAL_BENDING_COEFFICIENT * designValues.Emin) / 
              Math.pow(radiusOfGyration, INTERACTION_CONSTANTS.BUCKLING_RADIUS_EXPONENT);
  
  return {
    FcE1,
    FcE2,
    FbE,
  };
}

/**
 * Analyze bending and axial tension
 * Based on NDS 3.9.1 equations (3.9-1) and (3.9-2)
 * 
 * @param geometry - Member geometry
 * @param material - Material properties
 * @param forces - Applied forces
 * @param FtStar - Adjusted tension design value
 * @param FbStar - Adjusted bending design value
 * @returns Analysis results
 */
export function analyzeBendingAndAxialTension(
  geometry: CombinedLoadingGeometry,
  material: CombinedLoadingMaterial,
  forces: CombinedLoadingForces,
  FtStar: number,
  FbStar: number
): CombinedLoadingResults {
  // Validate input
  if (forces.axialForce <= 0) {
    throw new Error('Axial force must be positive for tension analysis');
  }
  
  // Calculate properties
  const properties = calculateCombinedLoadingProperties(geometry);
  
  // Calculate actual stresses
  const ft = forces.axialForce / properties.area;
  const fb1 = forces.moments.M1 ? Math.abs(forces.moments.M1) / properties.sectionModulus1 : 0;
  const fb2 = forces.moments.M2 ? Math.abs(forces.moments.M2) / properties.sectionModulus2 : 0;
  
  // Apply interaction equations from NDS 3.9.1
  // Equation (3.9-1): ft/Ft' + fb/Fb' <= 1.0
  let ratio1 = ft / FtStar + fb1 / FbStar;
  
  // Equation (3.9-2): (fb - ft)/Fb' <= 1.0
  let ratio2 = (fb1 - ft) / FbStar;
  
  // For biaxial bending, consider both moments
  if (fb2 > 0) {
    ratio1 += fb2 / FbStar;
    ratio2 = Math.max(ratio2, (fb2 - ft) / FbStar);
  }
  
  const acceptable = ratio1 <= INTERACTION_CONSTANTS.MAX_INTERACTION_RATIO + COMBINED_LOADING_TOLERANCES.EPSILON &&
                    ratio2 <= INTERACTION_CONSTANTS.MAX_INTERACTION_RATIO + COMBINED_LOADING_TOLERANCES.EPSILON;
  
  return {
    scenario: COMBINED_LOADING_SCENARIOS.BENDING_AND_AXIAL_TENSION,
    geometry: properties,
    stresses: {
      fa: ft,
      fb1,
      fb2: fb2 > 0 ? fb2 : undefined,
      allowable: {
        Fa: FtStar,
        Fb_adjusted: FbStar,
      },
    },
    interaction: {
      ratio1,
      ratio2: fb2 > 0 ? ratio2 : undefined,
      acceptable,
    },
    designOK: acceptable,
    notes: [],
  };
}

/**
 * Analyze bending and axial compression
 * Based on NDS 3.9.2 equations (3.9-3) and (3.9-4)
 * 
 * @param geometry - Member geometry
 * @param material - Material properties
 * @param forces - Applied forces
 * @param FcStar - Adjusted compression design value
 * @param FbStar - Adjusted bending design value
 * @returns Analysis results
 */
export function analyzeBendingAndAxialCompression(
  geometry: CombinedLoadingGeometry,
  material: CombinedLoadingMaterial,
  forces: CombinedLoadingForces,
  FcStar: number,
  FbStar: number
): CombinedLoadingResults {
  // Validate input
  if (forces.axialForce >= 0) {
    throw new Error('Axial force must be negative for compression analysis');
  }
  
  // Calculate properties
  const properties = calculateCombinedLoadingProperties(geometry);
  const criticalBuckling = calculateCriticalBucklingValues(geometry, material);
  
  // Calculate actual stresses
  const fc = Math.abs(forces.axialForce) / properties.area;
  const fb1 = forces.moments.M1 ? Math.abs(forces.moments.M1) / properties.sectionModulus1 : 0;
  const fb2 = forces.moments.M2 ? Math.abs(forces.moments.M2) / properties.sectionModulus2 : 0;
  
  // Apply interaction equations from NDS 3.9.2
  // For uniaxial bending, equation (3.9-3):
  // [fc/Fc']^2 + fb1/[Fb1'(1-(fc/FcE1))] + fb2/[Fb2'(1-(fc/FcE2)-(fb1/FbE)^2)] <= 1.0
  
  const fcRatio = fc / FcStar;
  const fcE1Term = fc / (criticalBuckling.FcE1 || Infinity);
  const fcE2Term = fc / (criticalBuckling.FcE2 || Infinity);
  const fbETerm = fb1 / (criticalBuckling.FbE || Infinity);
  
  // Primary interaction equation
  let ratio1 = fcRatio * fcRatio;
  
  if (fb1 > 0 && criticalBuckling.FcE1) {
    const denominator1 = 1 - fcE1Term;
    if (denominator1 <= 0) {
      throw new Error('Buckling instability detected in strong axis');
    }
    ratio1 += fb1 / (FbStar * denominator1);
  }
  
  if (fb2 > 0 && criticalBuckling.FcE2 && criticalBuckling.FbE) {
    const denominator2 = 1 - fcE2Term - (fbETerm * fbETerm);
    if (denominator2 <= 0) {
      throw new Error('Buckling instability detected in weak axis');
    }
    ratio1 += fb2 / (FbStar * denominator2);
  }
  
  // Secondary interaction equation (3.9-4):
  // fc/FcE2 + (fb1/FbE)^2 < 1.0
  let ratio2: number | undefined;
  if (criticalBuckling.FcE2 && criticalBuckling.FbE) {
    ratio2 = fcE2Term + (fbETerm * fbETerm);
  }
  
  const acceptable = ratio1 <= INTERACTION_CONSTANTS.MAX_INTERACTION_RATIO + COMBINED_LOADING_TOLERANCES.EPSILON &&
                    (ratio2 === undefined || ratio2 <= INTERACTION_CONSTANTS.MAX_INTERACTION_RATIO + COMBINED_LOADING_TOLERANCES.EPSILON);
  
  return {
    scenario: COMBINED_LOADING_SCENARIOS.BENDING_AND_AXIAL_COMPRESSION,
    geometry: properties,
    stresses: {
      fa: -fc, // Negative for compression
      fb1,
      fb2: fb2 > 0 ? fb2 : undefined,
      allowable: {
        Fa: FcStar,
        Fb_adjusted: FbStar,
      },
    },
    interaction: {
      ratio1,
      ratio2,
      acceptable,
    },
    criticalBuckling,
    designOK: acceptable,
    notes: [],
  };
}

/**
 * Analyze combined loading based on scenario
 * Main analysis function that dispatches to appropriate method
 * 
 * @param geometry - Member geometry
 * @param material - Material properties
 * @param forces - Applied forces
 * @param FaStar - Adjusted axial design value (Ft* for tension, Fc* for compression)
 * @param FbStar - Adjusted bending design value
 * @returns Complete analysis results
 */
export function analyzeCombinedLoading(
  geometry: CombinedLoadingGeometry,
  material: CombinedLoadingMaterial,
  forces: CombinedLoadingForces,
  FaStar: number,
  FbStar: number
): CombinedLoadingResults {
  // Validate inputs
  if (FaStar <= 0 || FbStar <= 0) {
    throw new Error('Design values must be positive');
  }
  
  // Determine analysis type based on axial force sign
  if (forces.axialForce > 0) {
    // Tension
    return analyzeBendingAndAxialTension(geometry, material, forces, FaStar, FbStar);
  } else if (forces.axialForce < 0) {
    // Compression
    return analyzeBendingAndAxialCompression(geometry, material, forces, FaStar, FbStar);
  } else {
    // Pure bending - not a combined loading case
    throw new Error('Pure bending is not a combined loading scenario');
  }
}

/**
 * Validate combined loading inputs
 * 
 * @param geometry - Member geometry
 * @param material - Material properties
 * @param forces - Applied forces
 * @throws Error if inputs are invalid
 */
export function validateCombinedLoadingInputs(
  geometry: CombinedLoadingGeometry,
  material: CombinedLoadingMaterial,
  forces: CombinedLoadingForces
): void {
  // Validate geometry
  if (geometry.length <= 0) {
    throw new Error('Member length must be positive');
  }
  
  if (geometry.dimensions.width <= 0 || geometry.dimensions.depth <= 0) {
    throw new Error('Cross-sectional dimensions must be positive');
  }
  
  // Validate material
  if (material.designValues.Fb <= 0) {
    throw new Error('Bending design value must be positive');
  }
  
  if (material.designValues.E <= 0 || material.designValues.Emin <= 0) {
    throw new Error('Modulus of elasticity values must be positive');
  }
  
  // Validate forces
  if (forces.axialForce === 0) {
    throw new Error('Combined loading requires non-zero axial force');
  }
  
  if (!forces.moments.M1 && !forces.moments.M2) {
    throw new Error('Combined loading requires at least one bending moment');
  }
  
  // Validate that compression scenarios have compression design value
  if (forces.axialForce < 0 && !material.designValues.Fc) {
    throw new Error('Compression scenarios require compression design value');
  }
  
  // Validate that tension scenarios have tension design value
  if (forces.axialForce > 0 && !material.designValues.Ft) {
    throw new Error('Tension scenarios require tension design value');
  }
} 