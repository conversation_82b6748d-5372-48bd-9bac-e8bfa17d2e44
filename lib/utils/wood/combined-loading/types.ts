/**
 * Types for Wood Combined Bending and Axial Loading Design
 * Based on NDS 2018 Section 3.9 - Combined Bending and Axial Loading
 * 
 * References:
 * - NDS 2018 Section 3.9.1: Bending and Axial Tension
 * - NDS 2018 Section 3.9.2: Bending and Axial Compression
 * - NDS 2018 Section 3.9.3: Eccentric Compression Loading
 */

/**
 * Types of combined loading scenarios
 */
export const COMBINED_LOADING_SCENARIOS = {
  BENDING_AND_AXIAL_TENSION: 'bending_and_axial_tension',
  BENDING_AND_AXIAL_COMPRESSION: 'bending_and_axial_compression',
  BIAXIAL_BENDING_AND_COMPRESSION: 'biaxial_bending_and_compression',
  ECCENTRIC_COMPRESSION: 'eccentric_compression',
} as const;

export type CombinedLoadingScenario = typeof COMBINED_LOADING_SCENARIOS[keyof typeof COMBINED_LOADING_SCENARIOS];

/**
 * Basic geometric properties for combined loading analysis
 */
export interface CombinedLoadingGeometry {
  /** Length of member (in) */
  length: number;
  
  /** Cross-sectional dimensions */
  dimensions: {
    /** Width of member (in) */
    width: number;
    /** Depth of member (in) */
    depth: number;
  };
  
  /** Effective lengths for buckling analysis */
  effectiveLengths?: {
    /** Effective length about strong axis (in) */
    le1?: number;
    /** Effective length about weak axis (in) */
    le2?: number;
  };
}

/**
 * Material properties for combined loading analysis
 */
export interface CombinedLoadingMaterial {
  /** Reference design values */
  designValues: {
    /** Bending design value (psi) */
    Fb: number;
    /** Tension parallel to grain (psi) */
    Ft?: number;
    /** Compression parallel to grain (psi) */
    Fc?: number;
    /** Modulus of elasticity (psi) */
    E: number;
    /** Minimum modulus of elasticity (psi) */
    Emin: number;
  };
  
  /** Buckling coefficient for compression members */
  bucklingCoefficient?: number;
}

/**
 * Applied loads for combined loading analysis
 */
export interface CombinedLoadingForces {
  /** Axial force (lbs, positive in tension, negative in compression) */
  axialForce: number;
  
  /** Bending moments */
  moments: {
    /** Moment about strong axis (lb-in) */
    M1?: number;
    /** Moment about weak axis (lb-in) */
    M2?: number;
  };
  
  /** Loading scenario type */
  scenario: CombinedLoadingScenario;
}

/**
 * Critical buckling values for biaxial analysis
 */
export interface CriticalBucklingValues {
  /** Critical buckling value for compression (psi) */
  FcE1?: number;
  FcE2?: number;
  
  /** Critical buckling value for bending (psi) */
  FbE?: number;
}

/**
 * Combined loading analysis results
 */
export interface CombinedLoadingResults {
  /** Loading scenario analyzed */
  scenario: CombinedLoadingScenario;
  
  /** Geometric properties */
  geometry: {
    /** Cross-sectional area (in^2) */
    area: number;
    /** Section modulus about strong axis (in^3) */
    sectionModulus1: number;
    /** Section modulus about weak axis (in^3) */
    sectionModulus2?: number;
  };
  
  /** Design stresses */
  stresses: {
    /** Actual axial stress (psi) */
    fa?: number;
    /** Actual bending stress about strong axis (psi) */
    fb1?: number;
    /** Actual bending stress about weak axis (psi) */
    fb2?: number;
    
    /** Allowable stresses */
    allowable: {
      /** Allowable axial stress (psi) */
      Fa?: number;
      /** Allowable bending stress (psi) */
      Fb_adjusted: number;
    };
  };
  
  /** Interaction equation results */
  interaction: {
    /** Primary interaction ratio */
    ratio1: number;
    /** Secondary interaction ratio (for biaxial cases) */
    ratio2?: number;
    /** Combined interaction check */
    acceptable: boolean;
  };
  
  /** Critical buckling values if applicable */
  criticalBuckling?: CriticalBucklingValues;
  
  /** Overall design check */
  designOK: boolean;
  
  /** Design notes and warnings */
  notes: string[];
} 