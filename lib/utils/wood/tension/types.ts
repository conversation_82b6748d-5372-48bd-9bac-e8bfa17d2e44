/**
 * Types for Wood Tension Member Design
 * Based on NDS 2018 Section 3.8 - Tension Members
 * 
 * References:
 * - NDS 2018 Section 3.8.1: Tension Parallel to Grain
 * - NDS 2018 Section 3.8.2: Tension Perpendicular to Grain
 */

/**
 * Tension loading directions
 */
export const TENSION_DIRECTIONS = {
  PARALLEL: 'parallel',
  PERPENDICULAR: 'perpendicular',
} as const;

export type TensionDirection = typeof TENSION_DIRECTIONS[keyof typeof TENSION_DIRECTIONS];

/**
 * Basic geometric properties for tension members
 */
export interface TensionMemberGeometry {
  /** Length of member (in) */
  length: number;
  
  /** Cross-sectional dimensions */
  dimensions: {
    /** Width of member (in) */
    width: number;
    /** Depth of member (in) */
    depth: number;
  };
  
  /** Net cross-sectional area if different from gross area (in^2) */
  netArea?: number;
}

/**
 * Material properties for tension members
 */
export interface TensionMemberMaterial {
  /** Reference design values */
  designValues: {
    /** Tension parallel to grain (psi) */
    Ft: number;
    /** Modulus of elasticity (psi) */
    E: number;
  };
}

/**
 * Applied loads for tension members
 */
export interface TensionMemberLoading {
  /** Axial tension force (lbs, positive in tension) */
  tensionForce: number;
  
  /** Direction of tension loading relative to grain */
  direction: TensionDirection;
}

/**
 * Tension member analysis results
 */
export interface TensionMemberResults {
  /** Geometric properties */
  geometry: {
    /** Gross cross-sectional area (in^2) */
    grossArea: number;
    /** Net cross-sectional area (in^2) */
    netArea: number;
  };
  
  /** Design stresses */
  stresses: {
    /** Actual tension stress (psi) */
    ft: number;
    /** Allowable tension stress (psi) */
    Ft_adjusted: number;
  };
  
  /** Utilization ratio */
  utilization: {
    /** Tension utilization ratio */
    tension: number;
  };
  
  /** Design checks */
  checks: {
    /** Tension check passes */
    tensionOK: boolean;
    /** Overall design OK */
    designOK: boolean;
  };
  
  /** Design notes and warnings */
  notes: string[];
} 