/**
 * Tension Member Analysis
 * Based on NDS 2018 Section 3.8 - Tension Members
 * 
 * This module provides analysis of wood tension members including
 * tension parallel to grain and considerations for tension perpendicular to grain.
 * 
 * References:
 * - NDS 2018 Section 3.8.1: Tension Parallel to Grain
 * - NDS 2018 Section 3.8.2: Tension Perpendicular to Grain
 */

import {
  TensionMemberGeometry,
  TensionMemberMaterial,
  TensionMemberLoading,
  TensionMemberResults,
  TensionDirection,
  TENSION_DIRECTIONS,
} from './types';

import {
  TENSION_DESIGN_TOLERANCES,
  NET_AREA_FACTORS,
} from './constants';

/**
 * Calculate gross cross-sectional area
 * 
 * @param geometry - Member geometry
 * @returns Gross cross-sectional area (in^2)
 */
export function calculateGrossCrossSectionalArea(geometry: TensionMemberGeometry): number {
  return geometry.dimensions.width * geometry.dimensions.depth;
}

/**
 * Calculate net cross-sectional area
 * Based on NDS 3.8.1 - tension stress shall be based on net section area
 * 
 * @param geometry - Member geometry
 * @returns Net cross-sectional area (in^2)
 */
export function calculateNetCrossSectionalArea(geometry: TensionMemberGeometry): number {
  const grossArea = calculateGrossCrossSectionalArea(geometry);
  
  // Use specified net area if provided, otherwise assume gross area
  return geometry.netArea ?? grossArea;
}

/**
 * Calculate actual tension stress
 * Based on NDS 3.8.1 - tension stress shall be based on net section area
 * 
 * @param tensionForce - Applied tension force (lbs, positive in tension)
 * @param netArea - Net cross-sectional area (in^2)
 * @returns Actual tension stress (psi)
 */
export function calculateTensionStress(tensionForce: number, netArea: number): number {
  if (netArea <= 0) {
    throw new Error('Net cross-sectional area must be positive');
  }
  
  if (tensionForce < 0) {
    throw new Error('Tension force must be positive (tension)');
  }
  
  return tensionForce / netArea;
}

/**
 * Check tension design requirements
 * Based on NDS 3.8.1 - actual tension stress shall not exceed adjusted tension design value
 * 
 * @param actualStress - Actual tension stress (psi)
 * @param allowableStress - Allowable tension stress (psi)
 * @returns Design check result
 */
export function checkTensionDesign(
  actualStress: number,
  allowableStress: number
): {
  ratio: number;
  acceptable: boolean;
} {
  if (allowableStress <= 0) {
    throw new Error('Allowable tension stress must be positive');
  }
  
  const ratio = actualStress / allowableStress;
  
  return {
    ratio,
    acceptable: ratio <= TENSION_DESIGN_TOLERANCES.MAX_UTILIZATION + TENSION_DESIGN_TOLERANCES.EPSILON,
  };
}

/**
 * Validate tension member geometry
 * 
 * @param geometry - Member geometry to validate
 * @throws Error if geometry is invalid
 */
export function validateTensionMemberGeometry(geometry: TensionMemberGeometry): void {
  if (geometry.length <= 0) {
    throw new Error('Member length must be positive');
  }
  
  if (geometry.dimensions.width <= 0) {
    throw new Error('Member width must be positive');
  }
  
  if (geometry.dimensions.depth <= 0) {
    throw new Error('Member depth must be positive');
  }
  
  if (geometry.netArea !== undefined && geometry.netArea <= 0) {
    throw new Error('Net area must be positive if specified');
  }
  
  // Check that net area is not greater than gross area
  if (geometry.netArea !== undefined) {
    const grossArea = calculateGrossCrossSectionalArea(geometry);
    if (geometry.netArea > grossArea) {
      throw new Error('Net area cannot be greater than gross area');
    }
  }
}

/**
 * Validate tension member material properties
 * 
 * @param material - Material properties to validate
 * @throws Error if material properties are invalid
 */
export function validateTensionMemberMaterial(material: TensionMemberMaterial): void {
  const { designValues } = material;
  
  if (designValues.Ft <= 0) {
    throw new Error('Tension design value must be positive');
  }
  
  if (designValues.E <= 0) {
    throw new Error('Modulus of elasticity must be positive');
  }
}

/**
 * Validate tension member loading
 * 
 * @param loading - Loading to validate
 * @throws Error if loading is invalid
 */
export function validateTensionMemberLoading(loading: TensionMemberLoading): void {
  if (loading.tensionForce <= 0) {
    throw new Error('Tension force must be positive (tension)');
  }
  
  if (!Object.values(TENSION_DIRECTIONS).includes(loading.direction)) {
    throw new Error(`Invalid tension direction: ${loading.direction}`);
  }
}

/**
 * Generate design notes for tension members
 * 
 * @param geometry - Member geometry
 * @param loading - Applied loading
 * @param results - Analysis results
 * @returns Array of design notes and warnings
 */
export function generateTensionDesignNotes(
  geometry: TensionMemberGeometry,
  loading: TensionMemberLoading,
  results: TensionMemberResults
): string[] {
  const notes: string[] = [];
  
  // Check for tension perpendicular to grain
  if (loading.direction === TENSION_DIRECTIONS.PERPENDICULAR) {
    notes.push(
      'WARNING: Tension perpendicular to grain should be avoided whenever possible (NDS 3.8.2). ' +
      'When unavoidable, mechanical reinforcement should be considered.'
    );
  }
  
  // Check net area reduction
  if (geometry.netArea) {
    const grossArea = calculateGrossCrossSectionalArea(geometry);
    const reduction = ((grossArea - geometry.netArea) / grossArea) * 100;
    
    if (reduction > 25) {
      notes.push(
        `Net area reduction of ${reduction.toFixed(1)}% is significant. ` +
        'Verify connection design and consider larger member size.'
      );
    }
  } else {
    notes.push(
      'Net area not specified - assuming gross area. ' +
      'Consider actual net area accounting for bolt holes and connections.'
    );
  }
  
  // Check utilization ratio
  if (results.utilization.tension > 0.9) {
    notes.push(
      `High utilization ratio (${(results.utilization.tension * 100).toFixed(1)}%). ` +
      'Consider larger member size for better performance.'
    );
  }
  
  return notes;
}

/**
 * Analyze tension member
 * Main analysis function for wood tension members
 * 
 * @param geometry - Member geometry
 * @param material - Member material properties
 * @param loading - Applied loads
 * @param FtStar - Reference tension design value with applicable adjustment factors
 * @returns Complete tension member analysis results
 */
export function analyzeTensionMember(
  geometry: TensionMemberGeometry,
  material: TensionMemberMaterial,
  loading: TensionMemberLoading,
  FtStar: number
): TensionMemberResults {
  // Validate inputs
  validateTensionMemberGeometry(geometry);
  validateTensionMemberMaterial(material);
  validateTensionMemberLoading(loading);
  
  if (FtStar <= 0) {
    throw new Error('Reference tension design value must be positive');
  }
  
  // Check for tension perpendicular to grain
  if (loading.direction === TENSION_DIRECTIONS.PERPENDICULAR) {
    // Per NDS 3.8.2, designs that induce tension stress perpendicular to grain
    // shall be avoided whenever possible
    console.warn('Tension perpendicular to grain should be avoided (NDS 3.8.2)');
  }
  
  // Calculate geometric properties
  const grossArea = calculateGrossCrossSectionalArea(geometry);
  const netArea = calculateNetCrossSectionalArea(geometry);
  
  // Calculate actual tension stress (based on net area per NDS 3.8.1)
  const ft = calculateTensionStress(loading.tensionForce, netArea);
  
  // Allowable tension stress is the adjusted design value
  const Ft_adjusted = FtStar;
  
  // Check tension design
  const tensionCheck = checkTensionDesign(ft, Ft_adjusted);
  
  // Create results object
  const results: TensionMemberResults = {
    geometry: {
      grossArea,
      netArea,
    },
    stresses: {
      ft,
      Ft_adjusted,
    },
    utilization: {
      tension: tensionCheck.ratio,
    },
    checks: {
      tensionOK: tensionCheck.acceptable,
      designOK: tensionCheck.acceptable,
    },
    notes: [],
  };
  
  // Generate design notes
  results.notes = generateTensionDesignNotes(geometry, loading, results);
  
  return results;
}

/**
 * Estimate net area for tension members with connections
 * Helper function to estimate net area when not explicitly provided
 * 
 * @param grossArea - Gross cross-sectional area (in^2)
 * @param connectionType - Type of connection ('bolted', 'nailed', 'screwed', etc.)
 * @param numberOfConnectors - Number of connectors reducing the cross-section
 * @returns Estimated net area (in^2)
 */
export function estimateNetArea(
  grossArea: number,
  connectionType: string = 'bolted',
  numberOfConnectors: number = 1
): number {
  if (grossArea <= 0) {
    throw new Error('Gross area must be positive');
  }
  
  if (numberOfConnectors < 0) {
    throw new Error('Number of connectors cannot be negative');
  }
  
  if (numberOfConnectors === 0) {
    return grossArea;
  }
  
  // Apply reduction factor based on connection type and number of connectors
  let reductionFactor: number;
  
  switch (connectionType.toLowerCase()) {
    case 'bolted':
      // More conservative for bolted connections
      reductionFactor = numberOfConnectors > 2 
        ? NET_AREA_FACTORS.CONSERVATIVE_REDUCTION 
        : NET_AREA_FACTORS.TYPICAL_REDUCTION;
      break;
      
    case 'nailed':
    case 'screwed':
      // Less reduction for smaller fasteners
      reductionFactor = 0.95;
      break;
      
    default:
      // Default to typical reduction
      reductionFactor = NET_AREA_FACTORS.TYPICAL_REDUCTION;
  }
  
  return grossArea * reductionFactor;
} 