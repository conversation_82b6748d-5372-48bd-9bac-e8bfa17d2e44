import type { DesignValues } from '@/components/materials';
import type { AdjustmentFactorSet } from '@/lib/types/beam/beam-data';

export const getFlatUseFactor = (width: number, thickness: number): number => {
  if (thickness >= 2 && thickness <= 3) {
    if (width >= 2 && width <= 3) return 1.0;
    if (width === 4 || width === 5) return 1.1;
    if (width === 6 || width === 8) return 1.15;
    if (width >= 10) return 1.2;
  }

  if (thickness === 4) {
    if (width >= 2 && width <= 3) return 1.0;
    if (width === 4) return 1.0;
    if (width === 5 || width === 6 || width === 8) return 1.05;
    if (width >= 10) return 1.1;
  }

  return 1.0;
};

export const getRepetitiveMemberFactor = (thickness: number): number => {
  if (thickness >= 2 && thickness <= 4) {
    return 1.15;
  }
  return 1.0;
};

export const DEFAULT_WET_SERVICE_FACTORS: AdjustmentFactorSet = {
  Fb: { factor: 1.0, note: "No wet service adjustment" },
  Ft: { factor: 1.0, note: "No wet service adjustment" },
  Fv: { factor: 1.0, note: "No wet service adjustment" },
  Fc_perp: { factor: 1.0, note: "No wet service adjustment" },
  Fc: { factor: 1.0, note: "No wet service adjustment" },
  E: { factor: 1.0, note: "No wet service adjustment" },
  Emin: { factor: 1.0, note: "No wet service adjustment" },
};

export const DEFAULT_TEMPERATURE_FACTORS: AdjustmentFactorSet = {
  Fb: { factor: 1.0, note: "No temperature adjustment" },
  Ft: { factor: 1.0, note: "No temperature adjustment" },
  Fv: { factor: 1.0, note: "No temperature adjustment" },
  Fc_perp: { factor: 1.0, note: "No temperature adjustment" },
  Fc: { factor: 1.0, note: "No temperature adjustment" },
  E: { factor: 1.0, note: "No temperature adjustment" },
  Emin: { factor: 1.0, note: "No temperature adjustment" },
};

export const DEFAULT_INCISING_FACTORS: AdjustmentFactorSet = {
  Fb: { factor: 1.0, note: "Not incised" },
  Ft: { factor: 1.0, note: "Not incised" },
  Fv: { factor: 1.0, note: "Not incised" },
  Fc_perp: { factor: 1.0, note: "Not incised" },
  Fc: { factor: 1.0, note: "Not incised" },
  E: { factor: 1.0, note: "Not incised" },
  Emin: { factor: 1.0, note: "Not incised" },
};

/**
* Returns the wet service factors to be applied to the design values.
*/
export const getWetServiceFactor = (
  designValues: DesignValues,
  isWetService: boolean
): AdjustmentFactorSet => {
  if (!designValues || !isWetService) {
    return DEFAULT_WET_SERVICE_FACTORS;
  }

  // Tables 4D and 4E don't use wet service factors
  if (designValues.design_values_table === "4D" || designValues.design_values_table === "4E") {
    return DEFAULT_WET_SERVICE_FACTORS;
  }

  // Apply specific logic for Table 4A
  if (designValues.design_values_table === "4A" || designValues.design_values_table === "4B") {
    const Fb = designValues.Fb * (designValues.size_factor_Cf_for_Fb || 1);
    let Fc = designValues.Fc * (designValues.size_factor_Cf_for_Fc || 1);

    if (designValues.design_values_table === "4B") {
      Fc = designValues.Fc;
    }

    const factorFb = Fb <= 1150 ? 1.0 : designValues.wet_service_factor_Cm_for_Fb ?? 1.0;
    const factorFc = Fc <= 750 ? 1.0 : designValues.wet_service_factor_Cm_for_Fc ?? 1.0;
    return {
      Fb: {
        factor: factorFb,
        note: `Wet service factor Cm = ${factorFb.toFixed(2)}`
      },
      Ft: {
        factor: designValues.wet_service_factor_Cm_for_Ft || 1.0,
        note: `Wet service factor Cm = ${(designValues.wet_service_factor_Cm_for_Ft || 1.0).toFixed(2)}`
      },
      Fv: {
        factor: designValues.wet_service_factor_Cm_for_Fv || 1.0,
        note: `Wet service factor Cm = ${(designValues.wet_service_factor_Cm_for_Fv || 1.0).toFixed(2)}`
      },
      Fc_perp: {
        factor: designValues.wet_service_factor_Cm_for_Fc_perp || 1.0,
        note: `Wet service factor Cm = ${(designValues.wet_service_factor_Cm_for_Fc_perp || 1.0).toFixed(2)}`
      },
      Fc: {
        factor: factorFc,
        note: `Wet service factor Cm = ${factorFc.toFixed(2)}`
      },
      E: {
        factor: designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0,
        note: `Wet service factor Cm = ${(designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0).toFixed(2)}`
      },
      Emin: {
        factor: designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0,
        note: `Wet service factor Cm = ${(designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0).toFixed(2)}`
      }
    };
  }

  // For tables other than 4A or 4B.
  return {
    Fb: {
      factor: designValues.wet_service_factor_Cm_for_Fb || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_Fb || 1.0).toFixed(2)}`
    },
    Ft: {
      factor: designValues.wet_service_factor_Cm_for_Ft || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_Ft || 1.0).toFixed(2)}`
    },
    Fv: {
      factor: designValues.wet_service_factor_Cm_for_Fv || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_Fv || 1.0).toFixed(2)}`
    },
    Fc_perp: {
      factor: designValues.wet_service_factor_Cm_for_Fc_perp || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_Fc_perp || 1.0).toFixed(2)}`
    },
    Fc: {
      factor: designValues.wet_service_factor_Cm_for_Fc || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_Fc || 1.0).toFixed(2)}`
    },
    E: {
      factor: designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0).toFixed(2)}`
    },
    Emin: {
      factor: designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0,
      note: `Wet service factor Cₘ = ${(designValues.wet_service_factor_Cm_for_E_and_Emin || 1.0).toFixed(2)}`
    }
  };
};

export const calculateSizeFactors = (width: number, depth: number, grade: string, table: string, sizeFactorsData: any) => {
  const defaultSizeFactors = { CF_for_Fb: 1, CF_for_Ft: 1, CF_for_Fc: 1 };
  if (!sizeFactorsData) return defaultSizeFactors;

  if (table === "4D" && depth >= 12) {
    return { CF_for_Fb: Math.pow(12 / depth, 1 / 9), CF_for_Ft: 1.0, CF_for_Fc: 1.0 };
  }

  const matchingFactor = sizeFactorsData.find((factor: any) => {
    const minWidth = (factor.min_width ? factor.min_width : -Infinity);
    const maxWidth = (factor.max_width ? factor.max_width : Infinity);
    const minThickness = (factor.min_thickness ? factor.min_thickness : -Infinity);
    const maxThickness = (factor.max_thickness ? factor.max_thickness : Infinity);

    return factor.commercial_grade === grade &&
      factor.table === table &&
      width >= minWidth &&
      width <= maxWidth &&
      depth >= minThickness &&
      depth <= maxThickness;
  });

  if (!matchingFactor) return defaultSizeFactors;

  return {
    CF_for_Fb: matchingFactor.CF_for_Fb,
    CF_for_Ft: matchingFactor.CF_for_Ft,
    CF_for_Fc: matchingFactor.CF_for_Fc,
  };
};

// Helper functions for beam stability calculations
export const calculateEffectiveLengthLe = (lu: number): number => {
  // Per NDS 3.3.3, le = 2.06 * lu for concentrated load at center with no intermediate brace
  // Assuming this is the case for now, will need to adjust if other load cases are considered
  return 2.06 * lu;
};

export const calculateSlendernessRatioRB = (le: number, d: number, b: number): number => {
  if (b === 0) return Infinity; // Avoid division by zero
  const Rb = Math.sqrt((le * d) / (b * b));
  return Rb > 50 ? 50 : Rb; // NDS 3.3.3.7: Rb shall not exceed 50
};

export const calculateBeamStabilityFactorCL = (Rb: number, Fb_star: number, E_min_prime: number): number => {
  if (Fb_star === 0 || E_min_prime === 0) return 1.0; // Avoid division by zero or invalid inputs

  const Fbe = (1.20 * E_min_prime) / (Rb * Rb);
  const ratio = Fbe / Fb_star;

  // Check if the value inside sqrt is negative
  const underSqrt = Math.pow((1 + ratio) / 1.9, 2) - (ratio / 0.95);
  if (underSqrt < 0) {
    // This can happen if Fbe is very small compared to Fb_star (e.g. very slender beam)
    // Or if Fbe is negative (should not happen with E_min_prime > 0)
    // NDS Commentary suggests CL can be taken as 0 in such cases, or Rb limit applies.
    // Since Rb is already capped at 50, this implies a very low CL.
    // For robustness, returning a small positive or 0, or relying on Rb<50 check to prevent this.
    // For now, if Fbe/Fb* is very small (e.g., < ~0.05), CL is effectively 0 or very close.
    // Let's return 0 as a safe lower bound for CL if underSqrt is negative.
    return 0;
  }

  const term1 = (1 + ratio) / 1.9;
  const term2 = Math.sqrt(underSqrt);
  
  const CL = term1 - term2;

  return CL > 1.0 ? 1.0 : CL < 0 ? 0 : CL; // CL should not be greater than 1.0
};

export const getIncisingFactors = (isIncised: boolean): AdjustmentFactorSet => {
  if (!isIncised) {
    return DEFAULT_INCISING_FACTORS;
  }
  return {
    E: { factor: 0.95, note: "Incising factor Cᵢ = 0.95" },
    Emin: { factor: 0.95, note: "Incising factor Cᵢ = 0.95" },
    Fb: { factor: 0.80, note: "Incising factor Cᵢ = 0.80" },
    Ft: { factor: 0.80, note: "Incising factor Cᵢ = 0.80" },
    Fc: { factor: 0.80, note: "Incising factor Cᵢ = 0.80" },
    Fv: { factor: 0.80, note: "Incising factor Cᵢ = 0.80" },
    Fc_perp: { factor: 1.00, note: "Incising factor Cᵢ = 1.00 (No reduction for Fc⊥)" },
  };
};

export const getTemperatureFactors = (
  isTemperatureFactored: boolean,
  isWetService: boolean,
  temperatureF: number | null
): AdjustmentFactorSet => {
  if (!isTemperatureFactored || temperatureF === null) {
    return DEFAULT_TEMPERATURE_FACTORS;
  }

  let factor_Ft_E_Emin = 1.0;
  let factor_Fb_Fv_Fc_FcPerp = 1.0;
  let note_Ft_E_Emin = "No temperature adjustment";
  let note_Fb_Fv_Fc_FcPerp = "No temperature adjustment";

  if (isWetService) {
    // Wet Service Conditions
    if (temperatureF <= 100) {
      factor_Ft_E_Emin = 1.0;
      factor_Fb_Fv_Fc_FcPerp = 1.0;
    } else if (temperatureF > 100 && temperatureF <= 125) {
      factor_Ft_E_Emin = 0.9;
      factor_Fb_Fv_Fc_FcPerp = 0.7;
    } else { // temperatureF > 125 (assuming table's 125°F<T≤150°F range applies)
      factor_Ft_E_Emin = 0.9;
      factor_Fb_Fv_Fc_FcPerp = 0.5;
    }
  } else {
    // Dry Service Conditions
    if (temperatureF <= 100) {
      factor_Ft_E_Emin = 1.0;
      factor_Fb_Fv_Fc_FcPerp = 1.0;
    } else if (temperatureF > 100 && temperatureF <= 125) {
      factor_Ft_E_Emin = 0.9;
      factor_Fb_Fv_Fc_FcPerp = 0.8;
    } else { // temperatureF > 125 (assuming table's 125°F<T≤150°F range applies)
      factor_Ft_E_Emin = 0.9;
      factor_Fb_Fv_Fc_FcPerp = 0.7;
    }
  }

  if (factor_Ft_E_Emin !== 1.0) {
    note_Ft_E_Emin = `Temperature factor Cₜ = ${factor_Ft_E_Emin.toFixed(2)}`;
  }
  if (factor_Fb_Fv_Fc_FcPerp !== 1.0) {
    note_Fb_Fv_Fc_FcPerp = `Temperature factor Cₜ = ${factor_Fb_Fv_Fc_FcPerp.toFixed(2)}`;
  }

  return {
    Ft: { factor: factor_Ft_E_Emin, note: note_Ft_E_Emin },
    E: { factor: factor_Ft_E_Emin, note: note_Ft_E_Emin },
    Emin: { factor: factor_Ft_E_Emin, note: note_Ft_E_Emin },
    Fb: { factor: factor_Fb_Fv_Fc_FcPerp, note: note_Fb_Fv_Fc_FcPerp },
    Fv: { factor: factor_Fb_Fv_Fc_FcPerp, note: note_Fb_Fv_Fc_FcPerp },
    Fc: { factor: factor_Fb_Fv_Fc_FcPerp, note: note_Fb_Fv_Fc_FcPerp },
    Fc_perp: { factor: factor_Fb_Fv_Fc_FcPerp, note: note_Fb_Fv_Fc_FcPerp },
  };
};

// Replace existing calculateAllAdjustments with this version
export const calculateAllAdjustments = (
  baseDesignValues: DesignValues | null,
  nominal_b_param: number,
  nominal_d_param: number,
  isWet: boolean,
  isRepetitive: boolean,
  isBracedBeam: boolean,
  unbracedLength: number,
  isIncised: boolean,
  isTemperatureFactored: boolean,
  temperature: number | null,
  sizeFactorsData: any 
): {
  adjustedValues: DesignValues | null;
  wetServiceFactorResult: AdjustmentFactorSet;
  incisingFactorResult: AdjustmentFactorSet;
  temperatureFactorResult: AdjustmentFactorSet;
  flatUseFactorResult: number;
  repetitiveMemberFactorResult: number;
  sizeFactorsResult: { CF_for_Fb: number; CF_for_Fc: number; CF_for_Ft: number; };
  beamStabilityFactorCLResult: number | null;
} | null => {
  // #### NEW DEBUG LOG ####
  console.log('[calculateAllAdjustments] Received isIncised:', isIncised);
  console.log('[calculateAllAdjustments] Received isTemperatureFactored:', isTemperatureFactored, 'Temperature:', temperature);
  // #######################

  if (!baseDesignValues || nominal_b_param <= 0 || nominal_d_param <= 0) return null;

  const incisingFactorResult = getIncisingFactors(isIncised);

  // Apply Incising Factors first
  const Ci_adjusted_baseDV: DesignValues = {
    ...baseDesignValues,
    Fb: (baseDesignValues.Fb ?? 0) * incisingFactorResult.Fb.factor,
    Ft: (baseDesignValues.Ft ?? 0) * incisingFactorResult.Ft.factor,
    Fv: (baseDesignValues.Fv ?? 0) * incisingFactorResult.Fv.factor,
    Fc: (baseDesignValues.Fc ?? 0) * incisingFactorResult.Fc.factor,
    Fc_perp: (baseDesignValues.Fc_perp ?? 0) * incisingFactorResult.Fc_perp.factor,
    E: (baseDesignValues.E ?? 0) * incisingFactorResult.E.factor,
    Emin: (baseDesignValues.Emin ?? 0) * incisingFactorResult.Emin.factor,
  };

  // Then Apply Temperature Factors
  const temperatureFactorResult = getTemperatureFactors(isTemperatureFactored, isWet, temperature);
  const Ct_adjusted_baseDV: DesignValues = {
    ...Ci_adjusted_baseDV,
    Fb: (Ci_adjusted_baseDV.Fb ?? 0) * temperatureFactorResult.Fb.factor,
    Ft: (Ci_adjusted_baseDV.Ft ?? 0) * temperatureFactorResult.Ft.factor,
    Fv: (Ci_adjusted_baseDV.Fv ?? 0) * temperatureFactorResult.Fv.factor,
    Fc: (Ci_adjusted_baseDV.Fc ?? 0) * temperatureFactorResult.Fc.factor,
    Fc_perp: (Ci_adjusted_baseDV.Fc_perp ?? 0) * temperatureFactorResult.Fc_perp.factor,
    E: (Ci_adjusted_baseDV.E ?? 0) * temperatureFactorResult.E.factor,
    Emin: (Ci_adjusted_baseDV.Emin ?? 0) * temperatureFactorResult.Emin.factor,
  };

  const sizeFactorsResult = calculateSizeFactors(
    nominal_b_param,
    nominal_d_param,
    Ct_adjusted_baseDV.commercial_grade, // Use grade from Ct_adjusted_baseDV
    Ct_adjusted_baseDV.design_values_table, // Use table from Ct_adjusted_baseDV
    sizeFactorsData
  );

  // Wet service factor is determined based on the already C_i and C_t adjusted values
  const wetServiceFactorResult = isWet
    ? getWetServiceFactor(Ct_adjusted_baseDV, isWet) // Pass Ct_adjusted_baseDV here
    : DEFAULT_WET_SERVICE_FACTORS;

  const flatUseFactorResult = Ct_adjusted_baseDV.design_values_table === "4A" // Use table from Ct_adjusted
    ? getFlatUseFactor(nominal_b_param, nominal_d_param)
    : 1.0;

  const repetitiveMemberFactorResult = getRepetitiveMemberFactor(nominal_b_param);

  let beamStabilityFactorCLResult: number | null = null;
  if (isBracedBeam && unbracedLength > 0 && nominal_b_param > 0 && nominal_d_param > 0) {
    const le = calculateEffectiveLengthLe(unbracedLength);
    const Rb = calculateSlendernessRatioRB(le, nominal_d_param, nominal_b_param);
    
    // Fb_for_CL_calc starts with C_i and C_t adjusted Fb, then applies C_F, C_fu, C_r
    const Fb_for_CL_calc = Ct_adjusted_baseDV.Fb * 
                           sizeFactorsResult.CF_for_Fb * 
                           flatUseFactorResult * 
                           (isRepetitive ? repetitiveMemberFactorResult : 1.0);
    // Then apply C_M (wet service) to get Fb_star
    const Fb_star = Fb_for_CL_calc * wetServiceFactorResult.Fb.factor;

    // Emin_for_CL_calc starts with C_i and C_t adjusted Emin
    const Emin_for_CL_calc = Ct_adjusted_baseDV.Emin;
    // Then apply C_M (wet service) to get Emin_prime
    const Emin_prime = Emin_for_CL_calc * wetServiceFactorResult.Emin.factor;

    if (Rb < 50 && Fb_star > 0 && Emin_prime > 0) {
      beamStabilityFactorCLResult = calculateBeamStabilityFactorCL(Rb, Fb_star, Emin_prime);
    } else if (Rb >= 50) {
        beamStabilityFactorCLResult = null; 
    }
  }
  
  // Final adjusted Fb includes ALL factors: Ci, Ct (already in Ct_adjusted_baseDV.Fb), CF, Cfu, Cr, CM, CL
  const final_adjusted_Fb = Ct_adjusted_baseDV.Fb * 
                             sizeFactorsResult.CF_for_Fb *
                             flatUseFactorResult *
                             (isRepetitive ? repetitiveMemberFactorResult : 1.0) *
                             wetServiceFactorResult.Fb.factor *
                             (beamStabilityFactorCLResult ?? 1.0);

  const adjustedValues: DesignValues = {
    ...Ct_adjusted_baseDV, // Start with Ci and Ct adjusted values
    
    size_factor_Cf_for_Fb: sizeFactorsResult.CF_for_Fb,
    size_factor_Cf_for_Fc: sizeFactorsResult.CF_for_Fc,
    size_factor_Cf_for_Ft: sizeFactorsResult.CF_for_Ft,
    flatUseFactor: flatUseFactorResult, 
    repetitive_member_factor_Cr: repetitiveMemberFactorResult, 

    adjusted_Fb: final_adjusted_Fb,
    adjusted_Ft:
      Ct_adjusted_baseDV.Ft * sizeFactorsResult.CF_for_Ft * wetServiceFactorResult.Ft.factor,
    adjusted_Fc:
      Ct_adjusted_baseDV.Fc * sizeFactorsResult.CF_for_Fc * wetServiceFactorResult.Fc.factor,
    adjusted_Fv: Ct_adjusted_baseDV.Fv * wetServiceFactorResult.Fv.factor, // Ci, Ct then Cm
    adjusted_Fc_perp:
      Ct_adjusted_baseDV.Fc_perp * wetServiceFactorResult.Fc_perp.factor, // Ci, Ct then Cm
    adjusted_E: Ct_adjusted_baseDV.E * wetServiceFactorResult.E.factor, // Ci, Ct then Cm
    adjusted_Emin: Ct_adjusted_baseDV.Emin * wetServiceFactorResult.Emin.factor, // Ci, Ct then Cm
  };

  return {
    adjustedValues,
    wetServiceFactorResult,
    incisingFactorResult,
    temperatureFactorResult,
    flatUseFactorResult,
    repetitiveMemberFactorResult,
    sizeFactorsResult,
    beamStabilityFactorCLResult,
  };
};