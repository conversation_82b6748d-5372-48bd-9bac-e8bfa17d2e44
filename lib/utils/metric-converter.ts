/**
 * Conversion factors for common engineering units
 */
export const CONVERSION_FACTORS = {
  LENGTH: {
    M_TO_FT: 3.28084,    // meters to feet
    FT_TO_M: 1/3.28084,  // feet to meters
    MM_TO_IN: 0.0393701, // millimeters to inches
    IN_TO_MM: 25.4,      // inches to millimeters
  },
  AREA: {
    M2_TO_FT2: 3.28084 * 3.28084, // meters² to feet²
    FT2_TO_M2: (1/3.28084) * (1/3.28084), // feet² to meters²
    MM2_TO_IN2: 0.0393701 * 0.0393701, // millimeters² to inches²
    IN2_TO_MM2: 25.4 * 25.4, // inches² to millimeters²
  },
  FORCE: {
    N_TO_LBF: 0.224809,   // newtons to pound-force
    LBF_TO_N: 1/0.224809, // pound-force to newtons
    KN_TO_KIPS: 0.224809, // kilonewtons to kips
    KIPS_TO_KN: 1/0.224809, // kips to kilonewtons
  },
  PRESSURE: {
    PA_TO_PSI: 0.000145038,  // pascals to pounds per square inch
    PSI_TO_PA: 1/0.000145038, // pounds per square inch to pascals
    MPA_TO_KSI: 0.145038,    // megapascals to kips per square inch
    KSI_TO_MPA: 1/0.145038,  // kips per square inch to megapascals
  },
  MOMENT_OF_INERTIA: {
    M4_TO_FT4: 2.4025,     // meters⁴ to feet⁴
    FT4_TO_M4: 1/2.4025,   // feet⁴ to meters⁴
    MM4_TO_IN4: 2.4025e-6, // millimeters⁴ to inches⁴
    IN4_TO_MM4: 1/(2.4025e-6), // inches⁴ to millimeters⁴
  },
  DISTRIBUTED_LOAD: {
    N_PER_M_TO_LBF_PER_FT: 0.068522,  // newtons per meter to pound-force per foot
    LBF_PER_FT_TO_N_PER_M: 1/0.068522, // pound-force per foot to newtons per meter
    KN_PER_M_TO_KIPS_PER_FT: 0.068522, // kilonewtons/meter to kips/foot
    KIPS_PER_FT_TO_KN_PER_M: 1/0.068522, // kips/foot to kilonewtons/meter
  },
  // Additional factors for Imperial <-> Imperial conversions
  IMPERIAL: {
    FT_TO_IN: 12,
    IN_TO_FT: 1/12,
    KIP_TO_LB: 1000,
    LB_TO_KIP: 1/1000,
    KSI_TO_PSI: 1000,
    PSI_TO_KSI: 1/1000,
    KIP_FT_TO_LB_IN: 1000 * 12,
    LB_IN_TO_KIP_FT: 1/(1000 * 12),
    KIP_PER_FT_TO_LB_PER_IN: 1000 / 12,
    LB_PER_IN_TO_KIP_PER_FT: 12 / 1000,
    LBF_PER_FT_TO_LBF_PER_IN: 1 / 12,
  }
};

import { UnitSystem } from "@/lib/types/units/unit-system";
import { BeamProperties } from "@/lib/types/beam/beam-data"; // Assuming area is here

// --- Specific Unit Conversion Functions --- 

export const ftToIn = (ft: number): number => ft * CONVERSION_FACTORS.IMPERIAL.FT_TO_IN;
export const inToFt = (inches: number): number => inches * CONVERSION_FACTORS.IMPERIAL.IN_TO_FT;
export const kipToLb = (kip: number): number => kip * CONVERSION_FACTORS.IMPERIAL.KIP_TO_LB;
export const lbToKip = (lb: number): number => lb * CONVERSION_FACTORS.IMPERIAL.LB_TO_KIP;
export const ksiToPsi = (ksi: number): number => ksi * CONVERSION_FACTORS.IMPERIAL.KSI_TO_PSI;
export const psiToKsi = (psi: number): number => psi * CONVERSION_FACTORS.IMPERIAL.PSI_TO_KSI;
export const kipFtToLbIn = (kipFt: number): number => kipFt * CONVERSION_FACTORS.IMPERIAL.KIP_FT_TO_LB_IN;
export const lbInToKipFt = (lbIn: number): number => lbIn * CONVERSION_FACTORS.IMPERIAL.LB_IN_TO_KIP_FT;
export const kipPerFtToLbPerIn = (kipPerFt: number): number => kipPerFt * CONVERSION_FACTORS.IMPERIAL.KIP_PER_FT_TO_LB_PER_IN;
export const lbPerInToKipPerFt = (lbPerIn: number): number => lbPerIn * CONVERSION_FACTORS.IMPERIAL.LB_PER_IN_TO_KIP_PER_FT;
export const lbfPerFtToLbfPerIn = (lbfPerFt: number): number => lbfPerFt * CONVERSION_FACTORS.IMPERIAL.LBF_PER_FT_TO_LBF_PER_IN;

export const mToIn = (m: number): number => m * CONVERSION_FACTORS.LENGTH.M_TO_FT * CONVERSION_FACTORS.IMPERIAL.FT_TO_IN;
export const inToM = (inches: number): number => inches * CONVERSION_FACTORS.IMPERIAL.IN_TO_FT * CONVERSION_FACTORS.LENGTH.FT_TO_M;
export const nToLb = (n: number): number => n * CONVERSION_FACTORS.FORCE.N_TO_LBF;
export const lbToN = (lb: number): number => lb * CONVERSION_FACTORS.FORCE.LBF_TO_N;
export const paToPsi = (pa: number): number => pa * CONVERSION_FACTORS.PRESSURE.PA_TO_PSI;
export const psiToPa = (psi: number): number => psi * CONVERSION_FACTORS.PRESSURE.PSI_TO_PA;
export const m4ToIn4 = (m4: number): number => m4 * CONVERSION_FACTORS.MOMENT_OF_INERTIA.M4_TO_FT4 * Math.pow(CONVERSION_FACTORS.IMPERIAL.FT_TO_IN, 4);
export const in4ToM4 = (in4: number): number => in4 * Math.pow(CONVERSION_FACTORS.IMPERIAL.IN_TO_FT, 4) * CONVERSION_FACTORS.MOMENT_OF_INERTIA.FT4_TO_M4;
export const nPerMToLbPerIn = (nPerM: number): number => nPerM * CONVERSION_FACTORS.DISTRIBUTED_LOAD.N_PER_M_TO_LBF_PER_FT * CONVERSION_FACTORS.IMPERIAL.IN_TO_FT;
export const lbPerInToNPerM = (lbPerIn: number): number => lbPerIn * CONVERSION_FACTORS.IMPERIAL.FT_TO_IN * CONVERSION_FACTORS.DISTRIBUTED_LOAD.LBF_PER_FT_TO_N_PER_M;
export const nMmToLbIn = (n_mm: number): number => (n_mm * CONVERSION_FACTORS.FORCE.N_TO_LBF) * CONVERSION_FACTORS.LENGTH.MM_TO_IN;
export const lbInToNMm = (lb_in: number): number => (lb_in * CONVERSION_FACTORS.FORCE.LBF_TO_N) * CONVERSION_FACTORS.LENGTH.IN_TO_MM;
// Add N-m to lb-in if needed (N-m -> N-mm -> lb-in)
export const nmToLbIn = (nm: number): number => nMmToLbIn(nm * 1000);
export const lbInToNm = (lbIn: number): number => lbInToNMm(lbIn) / 1000;

// --- Old Conversion Functions (Potentially Deprecate or Refactor) --- 

/**
 * Converts a length value between metric and imperial units
 */
export function convertLength(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC 
    ? value * CONVERSION_FACTORS.LENGTH.M_TO_FT
    : value * CONVERSION_FACTORS.LENGTH.FT_TO_M;
}

/**
 * Converts a force value between metric and imperial units
 */
export function convertForce(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC
    ? value * CONVERSION_FACTORS.FORCE.N_TO_LBF
    : value * CONVERSION_FACTORS.FORCE.LBF_TO_N;
}

/**
 * Converts a pressure value between metric and imperial units
 */
export function convertPressure(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC
    ? value * CONVERSION_FACTORS.PRESSURE.PA_TO_PSI
    : value * CONVERSION_FACTORS.PRESSURE.PSI_TO_PA;
}

/**
 * Converts a moment of inertia value between metric and imperial units
 */
export function convertMomentOfInertia(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC
    ? value * CONVERSION_FACTORS.MOMENT_OF_INERTIA.M4_TO_FT4
    : value * CONVERSION_FACTORS.MOMENT_OF_INERTIA.FT4_TO_M4;
}

/**
 * Converts a distributed load value between metric and imperial units
 */
export function convertDistributedLoad(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC
    ? value * CONVERSION_FACTORS.DISTRIBUTED_LOAD.N_PER_M_TO_LBF_PER_FT
    : value * CONVERSION_FACTORS.DISTRIBUTED_LOAD.LBF_PER_FT_TO_N_PER_M;
}

/**
 * Converts elastic modulus between metric (Pa) and imperial (ksi) units
 */
export function convertElasticModulus(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC
    ? value * CONVERSION_FACTORS.PRESSURE.PA_TO_PSI
    : value * CONVERSION_FACTORS.PRESSURE.PSI_TO_PA;
}

/**
 * Converts an area value between metric and imperial units
 */
export function convertArea(value: number, fromUnit: UnitSystem): number {
  return fromUnit === UnitSystem.METRIC
    ? value * CONVERSION_FACTORS.AREA.M2_TO_FT2 // Assuming base metric is m²
    : value * CONVERSION_FACTORS.AREA.FT2_TO_M2; // Assuming base imperial is ft²
    // NOTE: May need adjustment if base units are mm²/in² depending on how they are stored
}

/**
 * Converts beam properties based on UnitSystem (intended for display changes)
 * Assumes input properties match the fromUnit system.
 */
export function convertBeamPropertiesForDisplay(properties: BeamProperties, fromUnit: UnitSystem): BeamProperties {
  const toUnit = fromUnit === UnitSystem.METRIC ? UnitSystem.IMPERIAL : UnitSystem.METRIC;
  // This needs refinement based on which imperial system (ft/kip or in/psi) is the target
  // Currently matches old behavior (Metric <-> ft/lb/psi?)
  return {
    length: convertLength(properties.length, fromUnit),
    elasticModulus: convertElasticModulus(properties.elasticModulus, fromUnit),
    momentOfInertia: convertMomentOfInertia(properties.momentOfInertia, fromUnit),
    area: convertArea(properties.area, fromUnit), // Add area back if needed
  };
}