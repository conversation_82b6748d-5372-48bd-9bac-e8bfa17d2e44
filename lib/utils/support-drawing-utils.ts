import { CanvasRenderingContext2D } from "canvas";

interface SupportDrawingConfig {
  x: number;
  centerY: number;
  supportSize: number;
  supportColor: string;
  beamLength: number;
}

export function drawSupportBase(
  ctx: CanvasRenderingContext2D,
  x: number,
  supportCenterY: number,
  baseWidth: number,
  baseHeight: number,
  stripeCount: number,
  supportColor: string
) {
  const stripeSpacing = baseWidth / (stripeCount + 1);
  
  for (let i = 0; i < stripeCount; i++) {
    ctx.beginPath();
    ctx.moveTo(x - baseWidth/2 + i * stripeSpacing, supportCenterY + baseHeight);
    ctx.lineTo(x - baseWidth/2 + (i + 1) * stripeSpacing, supportCenterY);
    ctx.strokeStyle = supportColor;
    ctx.lineWidth = 2;
    ctx.stroke();
  }
}

export function drawPinSupport(
  ctx: CanvasRenderingContext2D,
  config: SupportDrawingConfig
) {
  const supportCenterY = config.centerY + 20;

  // Draw horizontal line
  ctx.beginPath();
  ctx.moveTo(config.x - 15, supportCenterY);
  ctx.lineTo(config.x + 15, supportCenterY);
  ctx.strokeStyle = config.supportColor;
  ctx.lineWidth = 1;
  ctx.stroke();

  // Draw base stripes
  drawSupportBase(
    ctx,
    config.x,
    supportCenterY,
    30, // baseWidth
    5,  // baseHeight
    4,  // stripeCount
    config.supportColor
  );

  // Draw triangle
  ctx.beginPath();
  ctx.moveTo(config.x, supportCenterY - 20);
  ctx.lineTo(config.x - config.supportSize, supportCenterY);
  ctx.lineTo(config.x + config.supportSize, supportCenterY);
  ctx.closePath();
  ctx.fillStyle = config.supportColor;
  ctx.fill();
}

export function drawRollerSupport(
  ctx: CanvasRenderingContext2D,
  config: SupportDrawingConfig
) {
  const supportCenterY = config.centerY + 20;

  // Draw horizontal line
  ctx.beginPath();
  ctx.moveTo(config.x - 15, supportCenterY);
  ctx.lineTo(config.x + 15, supportCenterY);
  ctx.strokeStyle = config.supportColor;
  ctx.lineWidth = 1;
  ctx.stroke();

  // Draw base stripes
  drawSupportBase(
    ctx,
    config.x,
    supportCenterY,
    30, // baseWidth
    5,  // baseHeight
    3,  // stripeCount
    config.supportColor
  );

  // Draw circle
  ctx.beginPath();
  ctx.arc(config.x, supportCenterY - 10, config.supportSize, 0, Math.PI * 2);
  ctx.fillStyle = config.supportColor;
  ctx.fill();
}

export function drawFixedSupport(
  ctx: CanvasRenderingContext2D,
  config: SupportDrawingConfig
) {
  const isAtStart = Math.abs(config.x) < 0.001;
  const isAtEnd = Math.abs(config.x - config.beamLength) < 0.001;

  // Draw vertical line
  ctx.beginPath();
  ctx.moveTo(config.x, config.centerY - config.supportSize * 2);
  ctx.lineTo(config.x, config.centerY + config.supportSize * 2);
  ctx.lineWidth = 4;
  ctx.strokeStyle = config.supportColor;
  ctx.stroke();

  if (isAtStart || isAtEnd) {
    const baseWidth = 15;
    const baseHeight = 25;
    const stripeSpacing = baseHeight / 4;
    const baseX = isAtStart ? config.x - 20 : config.x + 20;
    
    for (let i = 0; i < 3; i++) {
      ctx.beginPath();
      if (isAtStart) {
        ctx.moveTo(baseX, config.centerY - baseHeight/2 + i * stripeSpacing);
        ctx.lineTo(baseX + baseWidth, config.centerY - baseHeight/2 + (i + 1) * stripeSpacing);
      } else {
        ctx.moveTo(baseX - baseWidth, config.centerY - baseHeight/2 + i * stripeSpacing);
        ctx.lineTo(baseX, config.centerY - baseHeight/2 + (i + 1) * stripeSpacing);
      }
      ctx.strokeStyle = config.supportColor;
      ctx.lineWidth = 2;
      ctx.stroke();
    }
  }
}

export function drawSupportLabel(
  ctx: CanvasRenderingContext2D,
  x: number,
  centerY: number,
  position: number,
  units: string
) {
  ctx.font = "12px Arial";
  ctx.fillStyle = "#666";
  ctx.textAlign = "center";
  ctx.fillText(
    `${position.toFixed(2)} ${units}`,
    x,
    centerY + 40
  );
}