/**
 * Timber Design Factors Utility Functions
 */

import { ftToIn } from "@/lib/utils/metric-converter";

/**
 * Calculates the Volume Factor (Cv) for Glued Laminated Timber.
 * As per NDS.
 *
 * @param L - Length of bending member between points of zero moment, in feet.
 * @param d - Depth of bending member, in inches.
 * @param b - Width (breadth) of bending member, in inches.
 * @param speciesGroup - The species group string (e.g., "Southern Pine", "Douglas Fir-Larch (DF)").
 * @returns The calculated Volume Factor (Cv), not exceeding 1.0.
 */
export function calculateVolumeFactorCv(
  L: number,
  d: number,
  b: number,
  speciesGroup: string
): number {
  if (L <= 0 || d <= 0 || b <= 0) {
    return 1.0; // Or throw an error, but NDS factors are typically <= 1.0
  }

  const L_inches = ftToIn(L);
  if (L_inches <= 0) return 1.0;

  const x = speciesGroup === "Southern Pine" ? 20 : 10;
  
  // Per NDS, b for Cv calculation is the width of the widest piece in the layup, thus b <= 10.75"
  const b_calc = Math.min(b, 10.75);
  if (b_calc <= 0) return 1.0; // After capping, if b_calc is not positive.

  const termL = (21 / L_inches);
  const termD = (12 / d);
  const termB = (5.125 / b_calc);

  // Check for non-positive terms before raising to a power if L, d, b_calc could be such that terms are <=0
  // However, initial checks for L,d,b > 0 and b_calc > 0 should prevent Math.pow with negative base if 1/x is not integer.
  if (termL <= 0 || termD <= 0 || termB <= 0) {
      // This case should ideally not be reached if inputs L, d, b > 0
      // and constants are positive. It implies an issue if L, d, or b_calc
      // resulted in a non-positive ratio which is not expected for typical dimensions.
      // However, as a safeguard:
      return 1.0;
  }

  const Cv_calculated = 
    Math.pow(termL, 1 / x) *
    Math.pow(termD, 1 / x) *
    Math.pow(termB, 1 / x);

  return Math.min(Cv_calculated, 1.0);
} 

/**
 * Calculates the Flat Use Factor (Cfu) for Glued Laminated Timber.
 * Based on NDS Section 5.3.7 formula: Cfu = (12 / dy)^(1/9).
 * Applies when member is loaded in bending about the y-y axis (flat use)
 * and dy (depth in flat use orientation) is less than 12".
 *
 * @param dy - The depth of the member when oriented for flat use (bending about y-y axis), in inches.
 *             This corresponds to the beam's actual cross-sectional depth.
 * @returns The calculated Flat Use Factor (Cfu).
 */
export function calculateFlatUseFactorCfu(dy: number): number {
  if (dy <= 0) { // Should not happen with valid inputs, but as a safeguard
    return 1.0;
  }
  if (dy >= 12) {
    return 1.0;
  }
  // Formula: Cfu = (12 / dy)^(1/9)
  const Cfu = Math.pow(12 / dy, 1 / 9);
  return Cfu; // NDS doesn't explicitly cap Cfu at 1.0, it can be > 1.0 for dy < 12
} 