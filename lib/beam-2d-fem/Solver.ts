import * as math from "mathjs";
import { DofID, LabelType } from "./index";
import { Domain } from "./Domain";
import { LoadCase } from "./LoadCase";

/**
 * Class representing linear elastic solver.
 */
export abstract class Solver {
  domain: Domain;
  neq!: number; // number of unknowns
  pneq!: number; // number of prescribed unknowns
  k!: math.Matrix;
  m!: math.Matrix;
  f!: math.MathCollection | number[] | number[][];
  loadCases = new Array<LoadCase>();
  codeNumberGenerated: boolean = false;

  constructor() {
    this.domain = new Domain(this);
    this.loadCases.push(new LoadCase("DefaultLC", this.domain));
  }
  // code numbers assigned to supported as well as free DOFs
  nodeCodeNumbers = new Map<LabelType, { [code: number]: number }>();

  getNodeLocationArray(num: LabelType, dofs: Array<DofID>): number[] {
    let ans: number[] = [];
    //console.log("Node:", num, "Locatioan Array dofs:", dofs);
    const nodeCodes = this.nodeCodeNumbers.get(num.toString());
    if (nodeCodes) {
      for (const i of dofs) {
        //console.log(num, i, nodeCodes[i]);
        if (nodeCodes[i] !== undefined) {
          ans = ans.concat(nodeCodes[i]);
        } else {
          console.warn(`DOF ${i} not found for node ${num} in nodeCodeNumbers map.`);
        }
      }
    } else {
      console.warn(`Node ${num} not found in nodeCodeNumbers map.`);
    }
    return ans;
  }

  getNodeDofIDs(num: LabelType): number[] {
    const ans: number[] = [];
    const nodeCodes = this.nodeCodeNumbers.get(num.toString());
    if (nodeCodes) {
      for (const d in nodeCodes) {
        if (Object.prototype.hasOwnProperty.call(nodeCodes, d)) {
          ans.push(parseInt(d));
        }
      }
    }
    return ans;
  }

  generateCodeNumbers() {
    const nodalDofs = new Map<LabelType, Set<DofID>>();
    for (const [key, node] of Array.from(this.domain.nodes.entries())) {
      const keyStr = key.toString();
      this.nodeCodeNumbers.set(keyStr, {});
      nodalDofs.set(keyStr, new Set<DofID>());
    }
    // compile list of DOFs needed in nodes from element requirements
    for (const [ie, elem] of Array.from(this.domain.elements.entries())) {
      for (const en of elem.nodes) {
        const dofs = elem.getNodeDofs(en);
        for (const d of dofs) {
          const nodeDofSet = nodalDofs.get(en);
          if (nodeDofSet) {
            nodeDofSet.add(d);
          } else {
            console.log(en, nodalDofs.has(en), nodeDofSet);
            throw new RangeError("Node label " + en + " does not exists in nodalDofs map during element processing");
          }
        }
      }
    }
    //console.log(nodalDofs);
    // compute number of unknown and prescribed DOFs
    this.neq = 0;
    this.pneq = 0;
    for (const [num, node] of Array.from(this.domain.nodes.entries())) {
      const keyStr = num.toString();
      const dofSet = nodalDofs.get(keyStr)!;
      for (const d of Array.from(dofSet)) {
        if (node.bcs.has(d)) {
          this.pneq++;
        } else {
          this.neq++;
        }
      }
    }

    // assign equation (code) numbers to dofs
    let eq: number = 0;
    let peq: number = this.neq;
    for (const [num, node] of Array.from(this.domain.nodes.entries())) {
      const keyStr = num.toString();
      const dofSet = nodalDofs.get(keyStr)!;
      const nodeCodes = this.nodeCodeNumbers.get(keyStr)!;
      for (const d of Array.from(dofSet)) {
        if (node.bcs.has(d)) {
          nodeCodes[d] = peq++;
        } else {
          nodeCodes[d] = eq++;
        }
      }
    }
    //console.log("Number of equations: ",this.neq, ", number of prescribved: ", this.pneq);
    //console.log(this.nodeCodeNumbers);
    this.codeNumberGenerated = true;
  }

  assembleVecLC(f: math.Matrix, fe: number[], loc: number[], lc: number) {
    for (let i = 0; i < loc.length; i++) {
      f.set([loc[i], lc], f.get([loc[i], lc]) + fe[i]);
    }
  }
  assembleVec(f: math.Matrix, fe: number[], loc: number[]) {
    for (let i = 0; i < loc.length; i++) {
      f.set([loc[i]], f.get([loc[i]]) + fe[i]);
    }
  }
}
