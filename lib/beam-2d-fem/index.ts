/**
 * Enum to define physical meaning of degrees of freedom (DOFs)
 */
// Replaced enum with const object for compatibility with type stripping
export const DofID = {
  Dx: 0, // Displacement in x direction
  Dy: 1, // Displacement in y direction
  Dz: 2, // Displacement in z direction
  Rx: 3, // Rotation around x axis
  Ry: 4, // Rotation around y axis
  Rz: 5, // Rotation around z axis
} as const;

// Derive the DofID type from the const object's values
export type DofID = typeof DofID[keyof typeof DofID];

export type EnumDictionary<T extends string | symbol | number, U> = {
  [K in T]?: U;
};

export type LabelType = number | string;

// --- Add Exports (without .js extension) ---
export { Node } from "./Node";
export { Element } from "./Element";
export { Beam2D } from "./Beam2D";
export { Solver } from "./Solver";
export { LinearStaticSolver } from "./LinearStaticSolver";
export { Material } from "./Material";
export { CrossSection } from "./CrossSection";
export { Domain } from "./Domain";
export { LoadCase } from "./LoadCase";
export { Load } from "./Load";
export { NodalLoad } from "./NodalLoad";
export { PrescribedDisplacement } from "./PrescribedDisplacement";
export { BeamElementLoad } from "./BeamElementLoad";
export { BeamConcentratedLoad } from "./BeamConcentratedLoad";
export { BeamElementUniformEdgeLoad } from "./BeamElementUniformEdgeLoad";
export { BeamTemperatureLoad } from "./BeamTemperatureLoad";
// --- End Add Exports ---
