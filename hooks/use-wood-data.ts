import { useQuery } from "@tanstack/react-query";
// import type { WoodData } from "@/components/beam-analysis/beam-properties"; // Removed incorrect import
import type { LumberData } from "@/lib/types/lumber";
import type { DesignValues } from "@/components/materials"; // Updated to use new materials component
import {
  DEFAULT_NDS_VERSION,
  API_ENDPOINTS,
  QUERY_KEYS,
  ERROR_MESSAGES,
} from "@/lib/constants/nds-constants";
import useSWR from "swr";

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then((res) => res.json());

// Define WoodData interface based on /api/wood-data/route.ts structure
export interface WoodData {
  speciesCombinations: string[];
  designValues: {
    [speciesCombination: string]: DesignValues[];
  };
  commercialGrades: { [speciesCombination: string]: string[] };
  availableVersions: string[]; // Add available versions
}

interface GluLamData {
  speciesGroups: string[];
  species: {
    [group: string]: {
      symbol: string;
      species: string[];
      designValues: string;
    };
  };
}

interface GluLamSectionProperties {
  width: string;
  depth: string;
  area: number;
  Ix: number;
  Sx: number;
  rx: number;
  Iy: number;
  Sy: number;
  ry: number;
  table: string;
  speciesCombination: string;
  version: string;
}

interface GluLamSectionData {
  westernSpecies: {
    widths: number[];
    depths: { [width: string]: number[] };
    properties: { [key: string]: GluLamSectionProperties };
  };
  southernPine: {
    widths: number[];
    depths: { [width: string]: number[] };
    properties: { [key: string]: GluLamSectionProperties };
  };
}

interface SawnLumberData {
  speciesCombinations: string[];
  species: {
    [combination: string]: {
      species: string[];
      gradingRulesAgencies: string;
      designValues: string;
      location: string;
    };
  };
}

interface SizeFactor {
  commercial_grade: string;
  table: string;
  min_width: number;
  max_width: number;
  min_thickness: number;
  max_thickness: number;
  CF_for_Fb: number;
  CF_for_Ft: number;
  CF_for_Fc: number;
}

export function useWoodData(version: string = DEFAULT_NDS_VERSION) {
  return useQuery<WoodData>({
    queryKey: [QUERY_KEYS.WOOD_DATA, version],
    queryFn: async () => {
      const response = await fetch(`${API_ENDPOINTS.WOOD_DATA}?version=${encodeURIComponent(version)}`);
      if (!response.ok) {
        throw new Error(ERROR_MESSAGES.WOOD_DATA_LOAD_FAILED);
      }
      return response.json();
    },
  });
}

export function useAvailableNdsVersions() {
  return useQuery<string[]>({
    queryKey: [QUERY_KEYS.WOOD_DATA_VERSIONS],
    queryFn: async () => {
      const response = await fetch(`${API_ENDPOINTS.WOOD_DATA}?version=${DEFAULT_NDS_VERSION}`); // Get cached data with any version
      if (!response.ok) {
        throw new Error(ERROR_MESSAGES.VERSIONS_LOAD_FAILED);
      }
      const data: WoodData = await response.json();
      return data.availableVersions;
    },
  });
}

/**
 * Hook to fetch lumber properties from API
 */
export function useLumberProperties(version: string = 'NDS 2018') {
  return useSWR(
    version ? `/api/lumber-properties?version=${encodeURIComponent(version)}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 300000, // 5 minutes
    }
  );
}

export function useGluLamSpecies(version: string = DEFAULT_NDS_VERSION) {
  return useQuery<GluLamData>({
    queryKey: ['glulam-species', version],
    queryFn: async () => {
      const response = await fetch(`/api/glulam-species?version=${encodeURIComponent(version)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch GluLam species data');
      }
      return response.json();
    },
  });
}

export function useGluLamSectionProperties(version: string = DEFAULT_NDS_VERSION) {
  return useQuery<GluLamSectionData>({
    queryKey: ['glulam-section-properties', version],
    queryFn: async () => {
      const response = await fetch(`/api/glulam-section-properties?version=${encodeURIComponent(version)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch GluLam section properties data');
      }
      return response.json();
    },
  });
}

export function useSawnLumberSpecies(version: string = DEFAULT_NDS_VERSION) {
  return useQuery<SawnLumberData>({
    queryKey: ['sawn-lumber-species', version],
    queryFn: async () => {
      const response = await fetch(`/api/sawn-lumber-species?version=${encodeURIComponent(version)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch sawn lumber species data');
      }
      return response.json();
    },
  });
}

/**
 * Hook to fetch size factors from API
 */
export function useSizeFactors() {
  return useSWR(
    '/api/size-factors',
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 300000, // 5 minutes
    }
  );
}

// Interface for Table 5A Row (copied from app/api/glulam-table-5a/route.ts)
export interface Table5ARow {
  stress_class: string;
  species_outer: string;
  species_core: string;
  F_bx_pos_psi: number;
  F_bx_neg_psi: number;
  tension_face: number; 
  compression_face: number; 
  F_vx_psi: number;
  E_xtrue_ksi: number;
  E_xapp_ksi: number;
  E_xmin_ksi: number;
  F_by_psi: number;
  F_cperp_y_psi: number; 
  F_vy_psi: number;
  E_ytrue_ksi: number;
  E_yapp_ksi: number;
  E_ymin_ksi: number;
  F_t_psi: number;
  F_c_psi: number;
  G_top_or_bottom: number; 
  G_side: number;          
  table: string;
  version: string;
  wet_service_factor_Cm_for_Fb: number;
  wet_service_factor_Cm_for_Ft: number;
  wet_service_factor_Cm_for_Fv: number;
  wet_service_factor_Cm_for_Fc_perp: number; 
  wet_service_factor_Cm_for_Fc: number;
  wet_service_factor_Cm_for_E_and_Emin: number;
  Notes?: string; 
}

// Interface for Table 5B Row (copied from app/api/glulam-table-5b/route.ts)
export interface Table5BRow {
  combination_symbol: string;
  species_group: string;
  Grade: string; 
  E_axial_ksi: number;
  E_axial_ksi_095: number; 
  Eaxial_min: number; 
  perp_to_grain_Fc_perp_psi: number; 
  Ft_psi: number;
  Fc_psi_4_or_more_laminations: number;
  Fc_psi_2_or_3_laminations: number;
  Fby_psi_4_or_more_laminations: number;
  Fby_psi_3_laminations: number;
  Fby_psi_2_laminations: number;
  Fvy_psi: number;
  Fbx_psi_2_laminations_to_15_in_deep: number;
  Fvx_psi: number;
  G: number; 
  table: string;
  version: string;
  wet_service_factor_Cm_for_Fb: number;
  wet_service_factor_Cm_for_Ft: number;
  wet_service_factor_Cm_for_Fv: number;
  wet_service_factor_Cm_for_Fc_perp: number; 
  wet_service_factor_Cm_for_Fc: number;
  wet_service_factor_Cm_for_E_and_Emin: number;
  Notes?: string; 
}

export function useGluLamTable5AData(version: string = DEFAULT_NDS_VERSION) {
  return useQuery<Table5ARow[]>({
    queryKey: ['glulam-table-5a', version],
    queryFn: async () => {
      const response = await fetch(`/api/glulam-table-5a?version=${encodeURIComponent(version)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch GluLam Table 5A data');
      }
      return response.json();
    },
  });
}

export function useGluLamTable5BData(version: string = DEFAULT_NDS_VERSION) {
  return useQuery<Table5BRow[]>({
    queryKey: ['glulam-table-5b', version],
    queryFn: async () => {
      const response = await fetch(`/api/glulam-table-5b?version=${encodeURIComponent(version)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch GluLam Table 5B data');
      }
      return response.json();
    },
  });
}