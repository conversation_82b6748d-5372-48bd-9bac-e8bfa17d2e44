'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Spinner from '../../../components/ui/Spinner';
import { useOrganizations, Organization } from '../../../context/OrganizationsContext';

export default function OrganizationSettingsPage() {
  const router = useRouter();
  const params = useParams();
  const { orgId } = params;
  const { organizations, updateOrganization, isLoading: isOrganizationsLoading, isMutating } = useOrganizations();

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [name, setName] = useState("");
  const [address, setAddress] = useState("");
  const [phone, setPhone] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isOrganizationsLoading && orgId && typeof orgId === 'string') {
      const currentOrg = organizations.find((o: Organization) => o.id === orgId);
      if (currentOrg) {
        setOrganization(currentOrg);
        setName(currentOrg.name);
        setAddress(currentOrg.address || "");
        setPhone(currentOrg.phone || "");
        setIsLoading(false);
      } else {
        // Optionally handle organization not found, e.g., redirect or show error
        setIsLoading(false);
        router.push('/organizations'); // Or a 404 page
      }
    }
  }, [orgId, organizations, isOrganizationsLoading, router]);

  const handleSave = async () => {
    if (!organization || !name.trim()) {
      alert("Organization name is required.");
      return;
    }
    const success = await updateOrganization(organization.id, {
      name,
      address: address || undefined,
      phone: phone || undefined,
    });
    if (success) {
      router.push('/organizations');
    }
    // Error handling is managed within useOrganizations context
  };

  if (isLoading || isOrganizationsLoading) {
    return (
      <div className="container mx-auto py-10 flex justify-center items-center h-screen">
        <Spinner loading={true} size={50} />
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container mx-auto py-10 text-center">
        <p>Organization not found.</p>
        <Button onClick={() => router.push('/organizations')} className="mt-4">Go to Organizations</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">Edit Organization: {organization.name}</h1>
      <div className="max-w-md mx-auto space-y-6">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Organization Name"
            className="bg-[#5c5289]"
          />
        </div>
        <div>
          <Label htmlFor="address">Address</Label>
          <Input
            id="address"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="(Optional)"
            className="bg-[#5c5289]"
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            placeholder="(Optional)"
            className="bg-[#5c5289]"
          />
        </div>
        <div className="flex justify-end space-x-2">
          {isMutating ? (
            <Spinner loading={true} size={30} />
          ) : (
            <>
              <Button variant="outline" onClick={() => router.push('/organizations')} disabled={isMutating}>
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={isMutating}>
                Save Changes
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 