'use client';

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { BeamAnalysis } from '@/components/beam-analysis/beam-analysis';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface CalculationPageProps {
  params: {
    orgId: string;
    projectId: string;
    calculationId: string;
  };
}

export default function CalculationPage({ params }: CalculationPageProps) {
  const unitSystem = UnitSystem.IMPERIAL;
  const searchParams = useSearchParams();
  const folderId = searchParams.get('folderId');

  const [projectName, setProjectName] = useState<string>('Project');
  const [folderName, setFolderName] = useState<string>('Folder');
  const [calculationName, setCalculationName] = useState<string>('Beam Analysis');
  const [isLoadingBreadcrumbs, setIsLoadingBreadcrumbs] = useState<boolean>(true);

  useEffect(() => {
    const fetchBreadcrumbData = async () => {
      setIsLoadingBreadcrumbs(true);
      console.log('[Breadcrumb] Fetching data for projectId:', params.projectId, 'folderId:', folderId, 'calculationId:', params.calculationId);
      
      // Determine if it's a new calculation page
      const isNewCalculation = params.calculationId === 'new';
      if (isNewCalculation) {
        // Set a specific name for new calculations and don't fetch calculation details
        // Query for 'type' might exist, e.g. ?type=BEAM
        const typeParam = searchParams.get('type');
        if (typeParam === 'BEAM') {
            setCalculationName('New Beam Analysis');
        } else {
            setCalculationName('New Calculation');
        }
        console.log('[Breadcrumb] Detected new calculation page. Set name to:', calculationName);
      }

      try {
        // Fetch Project Name (always try if projectId exists)
        if (params.projectId) {
          const projectResponse = await fetch(`/api/projects/${params.projectId}`);
          console.log('[Breadcrumb] Project API Response Status:', projectResponse.status);
          if (projectResponse.ok) {
            const projectData = await projectResponse.json();
            console.log('[Breadcrumb] Project API Data (raw):', JSON.stringify(projectData, null, 2));
            const title = projectData?.project?.title || projectData?.title || projectData?.name;
            if (title) {
              setProjectName(title);
              console.log('[Breadcrumb] Project name set to:', title);
            } else {
              console.error('[Breadcrumb] Failed to extract project title from:', projectData);
              setProjectName('Project (Error)');
            }
          } else {
            const errorText = await projectResponse.text();
            console.error('[Breadcrumb] Failed to fetch project details. Status:', projectResponse.status, 'Response:', errorText);
            setProjectName('Project (Fetch Error)');
          }
        } else {
          setProjectName('Project (No ID)'); // Should ideally not happen if routing is correct
        }

        // Fetch Folder Name if folderId exists (always try if folderId exists)
        if (folderId) {
          console.log(`[Breadcrumb] Fetching folder details for folderId: ${folderId} under projectId: ${params.projectId}`);
          const folderResponse = await fetch(`/api/projects/${params.projectId}/folders/${folderId}`);
          console.log('[Breadcrumb] Folder API Response Status:', folderResponse.status);
          if (folderResponse.ok) {
            const folderData = await folderResponse.json();
            console.log('[Breadcrumb] Folder API Data (raw):', JSON.stringify(folderData, null, 2));
            const name = folderData?.folder?.name || folderData?.name;
            if (name) {
              setFolderName(name);
              console.log('[Breadcrumb] Folder name set to:', name);
            } else {
              console.error('[Breadcrumb] Failed to extract folder name from:', folderData);
              setFolderName('Folder (Error)');
            }
          } else {
            const errorText = await folderResponse.text();
            console.error('[Breadcrumb] Failed to fetch folder details. Status:', folderResponse.status, 'Response:', errorText);
            setFolderName('Folder (Fetch Error)');
          }
        } else {
          console.log('[Breadcrumb] No folderId present, skipping folder fetch.');
          // If no folderId, ensure folderName is not shown or is reset (it defaults to 'Folder')
          // Depending on desired UX, you might setFolderName('') or handle it in render
        }

        // Fetch Calculation Name ONLY if it's NOT a new calculation
        if (!isNewCalculation && params.calculationId) {
          console.log(`[Breadcrumb] Fetching calculation details for calculationId: ${params.calculationId}`);
          const calcResponse = await fetch(`/api/calculations/${params.calculationId}/analysis-results`); 
          console.log('[Breadcrumb] Calculation API Response Status:', calcResponse.status);
          if (calcResponse.ok) {
            const calcData = await calcResponse.json();
            console.log('[Breadcrumb] Calculation API Data (raw):', JSON.stringify(calcData, null, 2));
            const name = calcData?.name || calcData?.calculation?.name || calcData?.beamAnalysisResult?.name;
            if (name) {
              setCalculationName(name);
              console.log('[Breadcrumb] Calculation name set to:', name);
            } else {
              console.error('[Breadcrumb] Failed to extract calculation name from:', calcData);
              setCalculationName('Analysis (Error)');
            }
          } else {
            const errorText = await calcResponse.text();
            console.error('[Breadcrumb] Failed to fetch calculation details. Status:', calcResponse.status, 'Response:', errorText);
            setCalculationName('Analysis (Fetch Error)');
          }
        } else if (!isNewCalculation) {
          // This case implies params.calculationId is missing but it's not 'new' - should be caught by outer checks
          console.log('[Breadcrumb] No valid calculationId present for name fetch.');
          setCalculationName('Analysis (No ID)');
        }

      } catch (error) {
        console.error('[Breadcrumb] General error fetching breadcrumb data:', error);
        if (!projectName || projectName === 'Project') setProjectName('Project (Catch Error)');
        if(folderId && (!folderName || folderName === 'Folder')) setFolderName('Folder (Catch Error)');
        if(!isNewCalculation && (!calculationName || calculationName === 'Beam Analysis' || calculationName === 'Analysis (Fetch Error)')) {
            setCalculationName('Analysis (Catch Error)');
        }
      } finally {
        setIsLoadingBreadcrumbs(false);
        console.log('[Breadcrumb] Finished fetching breadcrumb data.');
      }
    };

    // We need projectId to fetch project and folder names.
    // calculationId is needed for an existing calculation's name or to detect 'new'.
    if (params.projectId && params.calculationId) { 
      fetchBreadcrumbData();
    } else {
      console.error('[Breadcrumb] Critical ID (projectId or calculationId) is missing. Cannot fetch breadcrumb data.');
      setIsLoadingBreadcrumbs(false);
      if (!params.projectId) setProjectName('Project (No ID)');
      // If calculationId is missing and not 'new', it's an issue.
      // If it is 'new', it would have been handled inside fetchBreadcrumbData if projectId was present.
      if (!params.calculationId && params.calculationId !== 'new') setCalculationName('Analysis (No ID)');
       // Set a default for new if calculationId is 'new' but projectId was missing
      if (params.calculationId === 'new' && !params.projectId) {
        const typeParam = searchParams.get('type');
        if (typeParam === 'BEAM') {
            setCalculationName('New Beam Analysis');
        } else {
            setCalculationName('New Calculation');
        }
      }
    }
  }, [params.orgId, params.projectId, params.calculationId, folderId, searchParams]); // Added searchParams for type query, orgId for completeness

  if (!params.orgId || !params.projectId || !params.calculationId) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-red-500 text-lg">Error: Missing organization, project, or calculation ID.</p>
        <Link href="/organizations">
          <span className="text-blue-600 hover:underline mt-4 inline-block">Go to Organizations</span>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <nav className="flex items-center text-sm text-muted-foreground mb-2">
          {isLoadingBreadcrumbs ? (
            <span>Loading breadcrumbs...</span>
          ) : (
            <>
              <Link href={`/organizations/${params.orgId}/projects/${params.projectId}`} className="hover:text-foreground">
                <Home size={16} className="mr-1 inline-block" />
                {projectName}
              </Link>
              {folderId && (
                <>
                  <ChevronRight size={16} className="mx-1" />
                  <Link href={`/organizations/${params.orgId}/projects/${params.projectId}?folderId=${folderId}`} className="hover:text-foreground">
                    {folderName}
                  </Link>
                </>
              )}
              <ChevronRight size={16} className="mx-1" />
              <span className="text-foreground">{calculationName}</span>
            </>
          )}
        </nav>
        <h1 className="text-2xl font-bold text-foreground">Beam Analysis Details</h1>
      </div>
      <Card>
        <CardContent className="p-0 sm:p-2 md:p-4">
          <BeamAnalysis 
            unitSystem={unitSystem} 
            analysisIdToLoad={params.calculationId}
            currentOrgId={params.orgId}
            currentProjectId={params.projectId}
          />
        </CardContent>
      </Card>
    </div>
  );
} 