'use client';

import { useEffect, useState, useCallback, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { BeamAnalysisResultCard } from '@/components/projects/BeamAnalysisResultCard';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { PlusCircle, ArrowLeft, FileDown, Eye, Settings, List, LayoutGrid, CheckCircle2, XCircle, GripVertical, Loader2, MoreHorizontal, Trash2, FolderPlus, FolderIcon, MoveIcon, MoveUpIcon, Edit3, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import dynamic from 'next/dynamic';
import { ProjectCalculationsPDF, type PDFBeamAnalysisResult } from '../../../../components/projects/ProjectCalculationsPDF';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import { useOrganizations, Organization } from '../../../../context/OrganizationsContext';
import { DependentsModal } from '@/components/projects/DependentsModal';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { toast } from 'sonner';
import { FolderCard } from '@/components/projects/FolderCard';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

// Define DetailedLoadFromRaw interface locally for use in pdfAnalyses mapping
interface DetailedLoadFromRaw {
  label: string;
  startPosition: number;
  endPosition: number;
  loadType?: string; 
  type?: string; 
  startMagnitude?: number;
  endMagnitude?: number;
}

// Dynamically import PDFDownloadLink and PDFViewer
const PDFDownloadLink = dynamic(
  () => import('@react-pdf/renderer').then((mod) => mod.PDFDownloadLink),
  {
    ssr: false,
    loading: () => <p>Loading download link...</p>,
  }
);

const PDFViewer = dynamic(
  () => import('@react-pdf/renderer').then((mod) => mod.PDFViewer),
  {
    ssr: false,
    loading: () => <p>Loading PDF viewer...</p>,
  }
);

// Interfaces
interface Calculation {
  id: string;
  name: string;
  type: string;
  order: number;
  folderId?: string | null; // Ensure folderId is part of Calculation interface
}

interface BeamAnalysisResultForCard {
  id: string;
  calculationId: string;
  name?: string | null;
  length: number;
  modulusOfElasticity: number;
  momentOfInertia: number;
  beamProperties: string; 
  loads: string;          
  supports: string;       
  results: string | null; 
  createdAt: string;
  updatedAt: string;
  originalUnitSystem?: UnitSystem | string;
  order?: number | null; 
  dependentsCount?: number; 
  bendingStressRatio?: number | null; 
  shearStressRatio?: number | null;   
  isReanalyzing?: boolean; 
  folderId?: string | null; // Ensure folderId is part of BeamAnalysisResultForCard
}

interface ApiCalculationNode { 
  id: string;
  name: string;
  type: string | null;
}

interface ApiGraphEdge { 
  source: string;
  target: string;
}

interface ApiDependencyGraphData { 
  nodes: ApiCalculationNode[];
  edges: ApiGraphEdge[];
}

interface Project {
  id: string;
  title: string;
  clientName?: string | null;
  address?: string | null; 
  clientAddress?: string | null; 
  clientPhone?: string | null;   
  status?: string | null; 
  dueDate?: string | null;  
  createdAt: string;
  updatedAt: string;
  calculations: Calculation[];
  beamAnalysisResults: BeamAnalysisResultForCard[]; 
  folders: Folder[]; 
  jobNo?: string;
  projectBy?: string;
  projectDate?: string;
  sheetNo?: string;
  organizationId?: string | null;
  projectNumber?: string;
}

interface Folder {
  id: string;
  name: string;
  projectId: string;
  order?: number | null;
  createdAt: string;
  updatedAt: string;
  calculationsCount?: number; // Optional: to show count on folder card
}

// Pagination component props interface
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
}

// Pagination component
const Pagination: React.FC<PaginationProps> = ({ 
  currentPage, 
  totalPages, 
  onPageChange, 
  totalItems, 
  itemsPerPage 
}) => {
  const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);

  return (
    <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-6">
      <div className="flex flex-1 justify-between sm:hidden">
        <Button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          variant="outline"
          size="sm"
        >
          Previous
        </Button>
        <Button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          variant="outline"
          size="sm"
        >
          Next
        </Button>
      </div>
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-white">
            Showing <span className="font-medium">{currentPage * itemsPerPage - itemsPerPage + 1}</span> to <span className="font-medium">{Math.min(currentPage * itemsPerPage, totalItems)}</span> of <span className="font-medium">{totalItems}</span> results
          </p>
        </div>
        <div>
          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            <Button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              <span className="sr-only">Previous</span>
              <ChevronLeft className="h-5 w-5" aria-hidden="true" />
            </Button>
            {pageNumbers.map(number => (
              <Button
                key={number}
                onClick={() => onPageChange(number)}
                variant={number === currentPage ? "default" : "outline"}
                size="sm"
              >
                {number}
              </Button>
            ))}
            <Button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              <span className="sr-only">Next</span>
              <ChevronRight className="h-5 w-5" aria-hidden="true" />
            </Button>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { getOrganizationById, isLoading: organizationsLoading } = useOrganizations();

  // Add scroll detection state
  const [isScrolled, setIsScrolled] = useState(false);

  const projectId = params?.projectId as string;
  const orgId = params?.orgId as string; // Added orgId from params
  const currentFolderIdQuery = searchParams.get('folderId');

  const [project, setProject] = useState<Project | null>(null);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null | undefined>(null);
  const [currentFolder, setCurrentFolder] = useState<Folder | null>(null); // State for the current folder if folderId is present
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  
  // These will hold all items from the project
  const [allProjectAnalyses, setAllProjectAnalyses] = useState<BeamAnalysisResultForCard[]>([]);
  const [allProjectCalculations, setAllProjectCalculations] = useState<Calculation[]>([]);
  
  // These will hold filtered items for display (either project root or specific folder)
  const [displayedAnalyses, setDisplayedAnalyses] = useState<BeamAnalysisResultForCard[]>([]);
  const [displayedCalculations, setDisplayedCalculations] = useState<Calculation[]>([]);
  const [displayedFolders, setDisplayedFolders] = useState<Folder[]>([]); 

  const [displayMode, setDisplayMode] = useState<'grid' | 'list'>('list');
  const [selectedAnalysisForModal, setSelectedAnalysisForModal] = useState<BeamAnalysisResultForCard | null>(null);
  const [isDependentsModalOpen, setIsDependentsModalOpen] = useState(false);

  const [searchTerm, setSearchTerm] = useState("");
  const [materialTypeFilter, setMaterialTypeFilter] = useState("all");
  const [bendingCheckFilter, setBendingCheckFilter] = useState("all");
  const [shearCheckFilter, setShearCheckFilter] = useState("all");
  const [selectedAnalysesForActions, setSelectedAnalysesForActions] = useState<string[]>([]);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isDeletingAnalyses, setIsDeletingAnalyses] = useState(false);

  // Add state for selected folders
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);

  const [isMoveToFolderModalOpen, setIsMoveToFolderModalOpen] = useState(false);
  const [targetFolderForMove, setTargetFolderForMove] = useState<string | null>(null);
  const [isMovingAnalyses, setIsMovingAnalyses] = useState(false);

  const PROJECT_ROOT_ID = "_PROJECT_ROOT_";

  const availableFoldersForMove = useMemo(() => {
    if (!project?.folders) return [];
    return project.folders.filter(folder => folder.id !== currentFolderIdQuery);
  }, [project?.folders, currentFolderIdQuery]);

  // Memoized value to determine if bulk deletion is allowed
  const canBulkDeleteSelected = useMemo(() => {
    if (selectedAnalysesForActions.length === 0) {
      return false; 
    }
    return selectedAnalysesForActions.every(selectedId => {
      const analysis = allProjectAnalyses.find(a => a.id === selectedId);
      return analysis ? analysis.dependentsCount === 0 : false;
    });
  }, [selectedAnalysesForActions, allProjectAnalyses]);

  const [analysisForDependentsModal, setAnalysisForDependentsModal] = useState<BeamAnalysisResultForCard | null>(null);
  const [childrenForModalReanalysis, setChildrenForModalReanalysis] = useState<string[]>([]); 
  const [isCreateCalculationModalOpen, setIsCreateCalculationModalOpen] = useState(false);
  const [newCalculationType, setNewCalculationType] = useState<string>("BEAM"); 

  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [renamingFolderId, setRenamingFolderId] = useState<string | null>(null);
  const [editingFolderName, setEditingFolderName] = useState("");
  const [isDeletingFolder, setIsDeletingFolder] = useState(false);
  const [folderToDelete, setFolderToDelete] = useState<Folder | null>(null);

  // Pagination state
  const [folderCurrentPage, setFolderCurrentPage] = useState(1);
  const [calculationCurrentPage, setCalculationCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Calculate score card statistics
  const scoreCardStats = useMemo(() => {
    // Use allProjectAnalyses if at project root, otherwise use displayedAnalyses for folder view
    const analysesToUse = currentFolderIdQuery ? displayedAnalyses : allProjectAnalyses;
    
    const totalCalculations = analysesToUse.length;
    const failedBendingChecks = analysesToUse.filter(analysis => 
      analysis.bendingStressRatio !== null && analysis.bendingStressRatio !== undefined && analysis.bendingStressRatio > 1
    ).length;
    const failedShearChecks = analysesToUse.filter(analysis => 
      analysis.shearStressRatio !== null && analysis.shearStressRatio !== undefined && analysis.shearStressRatio > 1
    ).length;

    return {
      totalCalculations,
      failedBendingChecks,
      failedShearChecks
    };
  }, [allProjectAnalyses, displayedAnalyses, currentFolderIdQuery]);

  const fetchProjectAndFilterData = useCallback(async () => {
    if (!projectId) return;
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch(`/api/projects/${projectId}?includeCalculations=true&includeBeamAnalyses=true&includeFolders=true`);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `Failed to fetch project: ${res.status}`);
      }
      const data: Project = await res.json();
      setProject(data);

      const sortedCalculations = [...(data.calculations || [])].sort((a, b) => (a.order ?? Infinity) - (b.order ?? Infinity));
      
      type RawBeamAnalysisResult = Omit<BeamAnalysisResultForCard, 'calculationId' | 'dependentsCount' | 'bendingStressRatio' | 'shearStressRatio' | 'folderId'> & {
        calculation?: { id: string; name?: string; type?: string; folderId?: string | null }; // Add folderId here
        dependentsCount?: number;
        bendingStressRatio?: number | null;
        shearStressRatio?: number | null;
      };

      const mappedAnalyses: BeamAnalysisResultForCard[] = (data.beamAnalysisResults as unknown as RawBeamAnalysisResult[] || []).map(br => ({
        ...br,
        calculationId: br.calculation?.id || '',
        dependentsCount: br.dependentsCount,
        bendingStressRatio: br.bendingStressRatio,
        shearStressRatio: br.shearStressRatio,
        name: br.name || br.calculation?.name || 'Unnamed Analysis',
        folderId: br.calculation?.folderId || null, // Get folderId from calculation
        updatedAt: br.updatedAt || new Date().toISOString(), // Ensure updatedAt is always present
      }));

      const sortedAnalyses = mappedAnalyses.sort((a, b) => (a.order ?? Infinity) - (b.order ?? Infinity));
      
      setAllProjectCalculations(sortedCalculations);
      setAllProjectAnalyses(sortedAnalyses);

      if (currentFolderIdQuery) {
        const folder = data.folders.find(f => f.id === currentFolderIdQuery);
        setCurrentFolder(folder || null);
        setDisplayedCalculations(sortedCalculations.filter(c => c.folderId === currentFolderIdQuery));
        setDisplayedAnalyses(sortedAnalyses.filter(ba => ba.folderId === currentFolderIdQuery));
        setDisplayedFolders([]); // No sub-folders displayed when inside a folder (for now)
      } else {
        setCurrentFolder(null);
        setDisplayedCalculations(sortedCalculations.filter(c => !c.folderId)); // Only top-level
        setDisplayedAnalyses(sortedAnalyses.filter(ba => !ba.folderId));     // Only top-level
        const sortedFolders = [...(data.folders || [])].sort((a, b) => (a.order ?? Infinity) - (b.order ?? Infinity));
        setDisplayedFolders(sortedFolders);
      }
      
      // Status and due date are now handled by the project settings modal
      
      // Clear selections when data is refetched
      clearAllSelections();

    } catch (err) {
      console.error("Error in fetchProjectAndFilterData:",err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [projectId, currentFolderIdQuery]);

  useEffect(() => {
    fetchProjectAndFilterData();
  }, [fetchProjectAndFilterData]);

  // Add organization loading effect
  useEffect(() => {
    if (!organizationsLoading && orgId) {
      const orgDetails = getOrganizationById(orgId);
      setCurrentOrganization(orgDetails);
    }
  }, [organizationsLoading, orgId, getOrganizationById]);

  // Initialize filters from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const materialFilter = urlParams.get('material');
    const bendingFilter = urlParams.get('bending');
    const shearFilter = urlParams.get('shear');
    
    if (materialFilter) setMaterialTypeFilter(materialFilter);
    if (bendingFilter) setBendingCheckFilter(bendingFilter);
    if (shearFilter) setShearCheckFilter(shearFilter);
  }, []);

  // Function to update URL parameters
  const updateUrlParams = (key: string, value: string) => {
    const url = new URL(window.location.href);
    if (value === 'all') {
      url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, value);
    }
    window.history.replaceState({}, '', url.toString());
  };

  // Handler for filtering by failed bending checks
  const handleFilterFailedBending = () => {
    const newFilter = bendingCheckFilter === 'fail' ? 'all' : 'fail';
    setBendingCheckFilter(newFilter);
    updateUrlParams('bending', newFilter);
  };

  // Handler for filtering by failed shear checks
  const handleFilterFailedShear = () => {
    const newFilter = shearCheckFilter === 'fail' ? 'all' : 'fail';
    setShearCheckFilter(newFilter);
    updateUrlParams('shear', newFilter);
  };

  // Handler for clearing all filters
  const handleClearAllFilters = () => {
    setMaterialTypeFilter('all');
    setBendingCheckFilter('all');
    setShearCheckFilter('all');
    
    // Clear all filter parameters from URL
    const url = new URL(window.location.href);
    url.searchParams.delete('material');
    url.searchParams.delete('bending');
    url.searchParams.delete('shear');
    window.history.replaceState({}, '', url.toString());
  };

  const getBeamDescription = (beamPropertiesString: string): string => {
    try {
      const properties = JSON.parse(beamPropertiesString);
      const designValuesFromProps = properties.designValues || {};
      const lumberProps = properties.lumberProperties || {};

      let materialName = 'N/A';
      let sizeClassification = 'N/A';
      
      // Check if this is a steel beam first
      if (properties.selectedMaterial === "steel") {
        const steelGrade = properties.selectedSteelGrade || "N/A";
        const steelShape = properties.selectedSteelShape || "N/A";
        const steelShapeSize = properties.selectedSteelShapeSize || "N/A";
        
        // Try to get more descriptive steel section information
        if (properties.steelSectionProperties) {
          const sectionName = properties.steelSectionProperties.EDI_Std_Nomenclature || 
                             properties.steelSectionProperties.AISC_Manual_Label || 
                             `${steelShape} ${steelShapeSize}`;
          return `${steelGrade} - ${sectionName}`;
        }
        
        return `${steelGrade} - ${steelShape} ${steelShapeSize}`;
      }
      
      // Check if this is manual mode first
      if (properties.isManualMode) {
        const species = properties.manual_species || "Custom";
        const grade = properties.manual_grade || "N/A";
        const size = (properties.manualWidth && properties.manualDepth) 
          ? `${properties.manualWidth}" x ${properties.manualDepth}"`
          : "N/A";
        return `${species} ${grade} ${size}`;
      }
      
      if (properties.selectedSpeciesCombination && properties.selectedGrade) {
        materialName = `${String(properties.selectedSpeciesCombination).trim()} ${String(properties.selectedGrade).trim()}`;
      } else if (properties.selectedSpeciesCombination) {
        materialName = String(properties.selectedSpeciesCombination).trim();
      } else if (properties.selectedSpecies && properties.selectedGrade) {
        materialName = `${String(properties.selectedSpecies).trim()} ${String(properties.selectedGrade).trim()}`;
      } else if (designValuesFromProps.speciesCombination && designValuesFromProps.commercial_grade) {
        materialName = `${String(designValuesFromProps.speciesCombination).trim()} ${String(designValuesFromProps.commercial_grade).trim()}`;
      } else if (designValuesFromProps.speciesCombination) {
        materialName = String(designValuesFromProps.speciesCombination).trim();
      } else if (properties.material?.name) {
        materialName = String(properties.material.name).trim();
      } else if (lumberProps.species && lumberProps.grade) {
        materialName = `${lumberProps.species} ${lumberProps.grade}`.trim();
      } else if (lumberProps.species) {
        materialName = String(lumberProps.species).trim();
      } else if (properties.materialProperties?.materialName) {
        materialName = String(properties.materialProperties.materialName).trim();
      } else if (properties.materialName) {
        materialName = String(properties.materialName).trim();
      } else if (properties.material) {
        materialName = String(properties.material).trim();
      }
      
      if (materialName === 'N/A' && properties.customMaterialName) { 
        materialName = String(properties.customMaterialName).trim();
      }

      if (properties.selectedSizeClassification) {
        sizeClassification = String(properties.selectedSizeClassification).trim();
      } else if (designValuesFromProps.size_classification) {
        sizeClassification = String(designValuesFromProps.size_classification).trim();
      } else if (lumberProps.size_classification) {
        sizeClassification = String(lumberProps.size_classification).trim();
      } else if (properties.material?.size_classification) {
        sizeClassification = String(properties.material.size_classification).trim();
      } else if (lumberProps.lumber_size_classification) {
        sizeClassification = String(lumberProps.lumber_size_classification).trim();
      } else if (properties.size_classification) {
        sizeClassification = String(properties.size_classification).trim();
      }
      return `${materialName} - ${sizeClassification}`;
    } catch (e) {
      console.warn("Failed to parse beamProperties for description:", e);
      return "N/A";
    }
  };

  const getBeamSize = (beamPropertiesString: string): string => {
    try {
      const properties = JSON.parse(beamPropertiesString);
      const lumberProps = properties.lumberProperties || {};

      // Check if this is a steel beam first
      if (properties.selectedMaterial === "steel") {
        if (properties.steelSectionProperties) {
          // For steel beams, show the section designation (e.g., "W12X26", "L4X4X1/2")
          const sectionName = properties.steelSectionProperties.EDI_Std_Nomenclature || 
                             properties.steelSectionProperties.AISC_Manual_Label || 
                             `${properties.selectedSteelShape || ""}${properties.selectedSteelShapeSize || ""}`.trim();
          return sectionName || "N/A";
        }
        return `${properties.selectedSteelShape || ""} ${properties.selectedSteelShapeSize || ""}`.trim() || "N/A";
      }

      // Check if this is manual mode first
      if (properties.isManualMode) {
        if (properties.manualWidth && properties.manualDepth) {
          return `${properties.manualWidth}" x ${properties.manualDepth}"`;
        }
        return "N/A";
      }

      // Check for glulam properties
      if (properties.lumberType === 'glulam' && properties.selectedGluLamProperties) {
        const width = properties.selectedGluLamProperties.width;
        const depth = properties.selectedGluLamProperties.depth;
        if (width && depth) {
          return `${width}" x ${depth}"`;
        }
      }

      // Check for sawn lumber properties
      if (properties.lumberType === 'sawn') {
        // Try standard dressed dimensions first
        if (lumberProps.standard_width && lumberProps.standard_depth) {
          return `${lumberProps.standard_width}" x ${lumberProps.standard_depth}"`;
        }
        // Fall back to manual dimensions if available
        if (properties.manualWidth && properties.manualDepth) {
          return `${properties.manualWidth}" x ${properties.manualDepth}"`;
        }
      }

      // Generic fallback for any width/depth properties
      if (properties.manualWidth && properties.manualDepth) {
        return `${properties.manualWidth}" x ${properties.manualDepth}"`;
      }

      return "N/A";
    } catch (e) {
      console.warn("Failed to parse beamProperties for size:", e);
      return "N/A";
    }
  };

  const getMaterialType = (beamPropertiesString: string): string => {
    try {
      const properties = JSON.parse(beamPropertiesString);
      if (properties.materialType) return String(properties.materialType).toLowerCase();
      
      // Check for steel material first
      if (properties.selectedMaterial === "steel" || properties.steelSection || properties.selectedSteelGrade || properties.selectedSteelShape || properties.steelSectionProperties) {
        return "steel";
      }
      
      if (properties.selectedSpeciesCombination || properties.selectedSpecies || properties.material?.name?.toLowerCase().includes('glulam') || properties.materialName?.toLowerCase().includes('glulam') || properties.materialProperties?.materialName?.toLowerCase().includes('glulam') ) {
          return "glulam";
      }
      if (properties.material?.name?.toLowerCase().includes('sawn') || properties.materialName?.toLowerCase().includes('sawn') || properties.materialProperties?.materialName?.toLowerCase().includes('sawn') ) {
          return "sawn lumber";
      }
      return "other";
    } catch (e) {
      return "other";
    }
  };

  const formatNumber = (num: any, decimalPlaces: number = 2): number | undefined => {
    if (typeof num === 'number' && isFinite(num)) {
      return parseFloat(num.toFixed(decimalPlaces));
    }
    return undefined;
  };
  
  const pdfAnalyses: PDFBeamAnalysisResult[] = useMemo(() => {
    console.log('PDF_DEBUG: Starting pdfAnalyses calculation...'); // Log when function starts
    let analysesToUse = allProjectAnalyses;
    
    // Check if we have selections (folders and/or calculations)
    const hasSelectedFolders = selectedFolders.length > 0;
    const hasSelectedCalculations = selectedAnalysesForActions.length > 0;
    
    if (hasSelectedFolders || hasSelectedCalculations) {
      // If selections are made, include only selected items
      let selectedAnalyses: BeamAnalysisResultForCard[] = [];
      
      // Add calculations from selected folders
      if (hasSelectedFolders) {
        const calculationsFromSelectedFolders = allProjectAnalyses.filter(
          (analysis) => selectedFolders.includes(analysis.folderId || '')
        );
        selectedAnalyses = [...selectedAnalyses, ...calculationsFromSelectedFolders];
      }
      
      // Add individually selected calculations
      if (hasSelectedCalculations) {
        const individuallySelectedCalculations = allProjectAnalyses.filter(
          (analysis) => selectedAnalysesForActions.includes(analysis.id)
        );
        selectedAnalyses = [...selectedAnalyses, ...individuallySelectedCalculations];
      }
      
      // Remove duplicates (in case a calculation is both in a selected folder and individually selected)
      analysesToUse = selectedAnalyses.filter((analysis, index, self) => 
        index === self.findIndex(a => a.id === analysis.id)
      );
    } else if (currentFolderIdQuery) {
      // If no selections but in folder view, show folder contents
      analysesToUse = allProjectAnalyses.filter(
        (analysis) => analysis.folderId === currentFolderIdQuery
      );
    }
    // Otherwise, show all project analyses (default behavior)
    
    console.log(`PDF_DEBUG: Processing ${analysesToUse.length} analyses for PDF.`);

    return analysesToUse.map((analysis, index) => {
      console.log(`PDF_DEBUG: Mapping analysis #${index + 1}, ID: ${analysis.id}, Name: ${analysis.name}`);

      let rawLoadGroups: any[] = [];
      try {
        if (analysis.name && analysis.name.toLowerCase().includes('test 5')) {
          console.log(`PDF_DEBUG [${analysis.name}] PRE-PARSE Raw analysis.loads string:`, analysis.loads);
        }
        rawLoadGroups = JSON.parse(analysis.loads || '[]');
      } catch (e) {
        console.error(`PDF_ERROR [${analysis.id}] Failed to parse load groups:`, e);
        rawLoadGroups = [];
      }
      
      // Flatten the load groups to get the actual loads array for the PDF
      const actualLoadsForPDF: DetailedLoadFromRaw[] = rawLoadGroups.reduce((acc, loadGroup) => {
        if (loadGroup && Array.isArray(loadGroup.loads)) {
          // Map properties from loadGroup.loads to DetailedLoadFromRaw structure
          const groupLoads = loadGroup.loads.map((l: any) => ({
            label: l.label || loadGroup.label || 'load', // Use load's own label, fallback to group label
            startPosition: l.startPosition !== undefined ? l.startPosition : loadGroup.startPosition,
            endPosition: l.endPosition !== undefined ? l.endPosition : loadGroup.endPosition,
            loadType: l.loadType, // This is important for D,L,R columns
            type: l.type || loadGroup.type, // Point or Distributed
            startMagnitude: l.startMagnitude,
            endMagnitude: l.endMagnitude,
          }));
          return acc.concat(groupLoads);
        }
        return acc;
      }, []);

      let parsedSupports: any[] = [];
      try {
        if (analysis.name && analysis.name.toLowerCase().includes('test 5')) {
           console.log(`PDF_DEBUG [${analysis.name}] PRE-PARSE Raw analysis.supports string:`, analysis.supports);
        }
        parsedSupports = JSON.parse(analysis.supports || '[]');
      } catch (e) {
        console.error(`PDF_ERROR [${analysis.id}] Failed to parse supports:`, e);
        parsedSupports = [];
      }
      
      let parsedBeamProperties: any = {};
      try {
        if (analysis.name && analysis.name.toLowerCase().includes('test 5')) {
            console.log(`PDF_DEBUG [${analysis.name}] PRE-PARSE Raw analysis.beamProperties string:`, analysis.beamProperties);
        }
        if (analysis.beamProperties) {
          parsedBeamProperties = JSON.parse(analysis.beamProperties);
        }
      } catch (e) {
        console.error(`PDF_ERROR [${analysis.id}] Error parsing beamProperties JSON:`, e);
        parsedBeamProperties = {};
      }

      let analysisResultsData: any = {}; // No longer assuming summaryData sub-object here for the base
      try {
          if (analysis.name && analysis.name.toLowerCase().includes('test 5')) {
              console.log(`PDF_DEBUG [${analysis.name}] PRE-PARSE Raw analysis.results string:`, analysis.results);
          }
          if (analysis.results) {
              analysisResultsData = JSON.parse(analysis.results); // Parse directly
          }
      } catch (e) {
          console.error(`PDF_ERROR [${analysis.id}] Error parsing analysis results JSON:`, e);
          analysisResultsData = {}; 
      }
      
      if (analysis.name && analysis.name.toLowerCase().includes('test 5')) {
        console.log(`PDF_DEBUG [${analysis.name}] POST-PARSE Parsed analysisResultsData:`, JSON.parse(JSON.stringify(analysisResultsData)));
        console.log(`PDF_DEBUG [${analysis.name}] POST-PARSE actualLoadsForPDF:`, JSON.parse(JSON.stringify(actualLoadsForPDF)));
      }

      const designValues = parsedBeamProperties.designValues || {};
      const summary = analysisResultsData || {}; // Use the top-level parsed results
      const lumberProps = parsedBeamProperties.lumberProperties || {};

      const L_ft = analysis.length; 
      const Lu_ft = parseFloat(parsedBeamProperties.lu) || L_ft; 

      // Enhanced beam properties handling for manual mode
      let try_b_in, try_d_in, try_A_in2, try_S_in3, try_I_in4;
      
      if (analysis.name && analysis.name.toLowerCase().includes('test 1')) {
        console.log(`PDF_DEBUG [${analysis.name}] Manual mode check:`, parsedBeamProperties.isManualMode);
        console.log(`PDF_DEBUG [${analysis.name}] Manual width:`, parsedBeamProperties.manualWidth);
        console.log(`PDF_DEBUG [${analysis.name}] Manual depth:`, parsedBeamProperties.manualDepth);
        console.log(`PDF_DEBUG [${analysis.name}] Manual Area:`, parsedBeamProperties.manual_Area);
        console.log(`PDF_DEBUG [${analysis.name}] Lumber props:`, JSON.stringify(lumberProps, null, 2));
      }
      
      if (parsedBeamProperties.isManualMode) {
        // Manual mode - get properties from parsedBeamProperties
        try_b_in = parsedBeamProperties.manualWidth ? parseFloat(parsedBeamProperties.manualWidth) : undefined;
        try_d_in = parsedBeamProperties.manualDepth ? parseFloat(parsedBeamProperties.manualDepth) : undefined;
        try_A_in2 = parsedBeamProperties.manual_Area;
        try_S_in3 = parsedBeamProperties.manual_Sxx;
        try_I_in4 = parsedBeamProperties.manual_Ixx;
        
        // Calculate missing section properties for manual beams
        if (try_b_in && try_d_in && (!try_A_in2 || !try_S_in3 || !try_I_in4)) {
          const b = try_b_in;
          const d = try_d_in;
          if (!try_A_in2) try_A_in2 = b * d;
          if (!try_S_in3) try_S_in3 = (b * Math.pow(d, 2)) / 6;
          if (!try_I_in4) try_I_in4 = (b * Math.pow(d, 3)) / 12;
        }
      } else if (parsedBeamProperties.lumberType === 'glulam' && parsedBeamProperties.selectedGluLamProperties) {
        // Glulam mode - get properties from selectedGluLamProperties
        const glulamProps = parsedBeamProperties.selectedGluLamProperties;
        try_b_in = glulamProps.width;
        try_d_in = glulamProps.depth;
        try_A_in2 = glulamProps.sectionProperties?.area;
        try_S_in3 = glulamProps.sectionProperties?.Sx;
        try_I_in4 = glulamProps.sectionProperties?.Ix;
        
        // Calculate missing section properties for glulam beams if needed
        if (try_b_in && try_d_in && (!try_A_in2 || !try_S_in3 || !try_I_in4)) {
          const b = try_b_in;
          const d = try_d_in;
          if (!try_A_in2) try_A_in2 = b * d;
          if (!try_S_in3) try_S_in3 = (b * Math.pow(d, 2)) / 6;
          if (!try_I_in4) try_I_in4 = (b * Math.pow(d, 3)) / 12;
        }
      } else {
        // Standard lumber mode - get from lumberProps
        try_b_in = lumberProps.standard_width;
        try_d_in = lumberProps.standard_depth;
        try_A_in2 = lumberProps.area_of_section_a_in2;
        try_S_in3 = lumberProps.Sxx;
        try_I_in4 = lumberProps.Ixx;
      }

      if (analysis.name && analysis.name.toLowerCase().includes('test 1')) {
        console.log(`PDF_DEBUG [${analysis.name}] Final beam properties:`, {
          try_b_in, try_d_in, try_A_in2, try_S_in3, try_I_in4
        });
        console.log(`PDF_DEBUG [${analysis.name}] Design values object:`, JSON.stringify(designValues, null, 2));
        console.log(`PDF_DEBUG [${analysis.name}] Looking for E values:`, {
          'designValues.adjusted_E': designValues.adjusted_E,
          'designValues.adjusted_Emin': designValues.adjusted_Emin,
          'designValues.E': designValues.E,
          'designValues.Emin': designValues.Emin,
          'parsedBeamProperties.manual_E': parsedBeamProperties.manual_E,
          'parsedBeamProperties.manual_Emin': parsedBeamProperties.manual_Emin,
          'parsedBeamProperties.E': parsedBeamProperties.E,
          'parsedBeamProperties.Emin': parsedBeamProperties.Emin,
          'lumberProps.E': lumberProps.E,
          'lumberProps.Emin': lumberProps.Emin,
        });
        console.log(`PDF_DEBUG [${analysis.name}] Looking for allowable stress values:`, {
          'designValues.adjusted_Fv': designValues.adjusted_Fv,
          'designValues.adjusted_Fb_pos': designValues.adjusted_Fb_pos,
          'designValues.Fv': designValues.Fv,
          'designValues.Fb': designValues.Fb,
          'parsedBeamProperties.manual_Fv': parsedBeamProperties.manual_Fv,
          'parsedBeamProperties.manual_Fb': parsedBeamProperties.manual_Fb,
          'parsedBeamProperties.allowable_Fv': parsedBeamProperties.allowable_Fv,
          'parsedBeamProperties.allowable_Fb': parsedBeamProperties.allowable_Fb,
          'parsedBeamProperties.Fv': parsedBeamProperties.Fv,
          'parsedBeamProperties.Fb': parsedBeamProperties.Fb,
          'lumberProps.Fv': lumberProps.Fv,
          'lumberProps.Fb': lumberProps.Fb,
        });
        console.log(`PDF_DEBUG [${analysis.name}] Full parsedBeamProperties keys:`, Object.keys(parsedBeamProperties));
        console.log(`PDF_DEBUG [${analysis.name}] Full analysisResultsData keys:`, Object.keys(analysisResultsData));
        console.log(`PDF_DEBUG [${analysis.name}] Full analysisResultsData:`, JSON.stringify(analysisResultsData, null, 2));
      }

      const combinedData: PDFBeamAnalysisResult = {
        id: analysis.id,
        analysisName: analysis.name || 'Unnamed Beam Analysis',
        beamMaterialName: (() => {
          // Check if this is manual mode first
          if (parsedBeamProperties.isManualMode) {
            const species = parsedBeamProperties.manual_species || "Custom";
            const grade = parsedBeamProperties.manual_grade || "N/A";
            return `${species} ${grade}`;
          }
          return `${designValues.speciesCombination || parsedBeamProperties.selectedSpeciesCombination || ''}`;
        })(),
        sizeClassification: (() => {
          // For manual mode, include the size in sizeClassification
          if (parsedBeamProperties.isManualMode) {
            return (parsedBeamProperties.manualWidth && parsedBeamProperties.manualDepth) 
              ? `${parsedBeamProperties.manualWidth}" x ${parsedBeamProperties.manualDepth}"`
              : "N/A";
          }
          return designValues.size_classification || parsedBeamProperties.selectedSizeClassification || '';
        })(),
        L_ft: L_ft,
        Lu_ft: Lu_ft,
        try_b_in: try_b_in,
        try_d_in: try_d_in,
        try_A_in2: try_A_in2,
        try_S_in3: try_S_in3,
        try_I_in4: try_I_in4,
        memberSize: lumberProps.standard_dressed_size_bxd || `${parsedBeamProperties.manualWidth}\"x${parsedBeamProperties.manualDepth}\"`,
        totalLoad_LDF: designValues.C_D,
        CL_value: designValues.C_L,
        Cr_value: designValues.C_r,
        adjCM_factor: designValues.C_M_Fb,
        adjCF_factor: designValues.C_F,
        adjCfu_factor: designValues.C_fu,
        adjCi_factor: designValues.C_i_E,
        adjCt_factor: designValues.C_t_E,
        adjCV_factor: parsedBeamProperties.volumeFactorCv || designValues.C_V,
        adjE_ksi: designValues.adjusted_E ? designValues.adjusted_E / 1000 : (
          // Fallbacks for manual mode or alternative property names
          designValues.E ? designValues.E / 1000 : (
            parsedBeamProperties.manual_E ? parsedBeamProperties.manual_E / 1000 : (
              parsedBeamProperties.E ? parsedBeamProperties.E / 1000 : (
                lumberProps.E ? lumberProps.E / 1000 : undefined
              )
            )
          )
        ),
        adjEmin_ksi: designValues.adjusted_Emin ? designValues.adjusted_Emin / 1000 : (
          // Fallbacks for manual mode or alternative property names
          designValues.Emin ? designValues.Emin / 1000 : (
            parsedBeamProperties.manual_E_min ? parsedBeamProperties.manual_E_min / 1000 : (
              parsedBeamProperties.Emin ? parsedBeamProperties.Emin / 1000 : (
                lumberProps.Emin ? lumberProps.Emin / 1000 : undefined
              )
            )
          )
        ),
        adjG_psi: designValues.original_G,
        totalLoad_FvPrime_psi: designValues.adjusted_Fv || (
          // Fallbacks for manual mode allowable shear stress
          designValues.Fv || 
          parsedBeamProperties.manual_Fv_allow ||  // Fixed: correct field name for manual allowable shear stress
          parsedBeamProperties.manual_Fv || 
          parsedBeamProperties.allowable_Fv || 
          parsedBeamProperties.Fv || 
          lumberProps.Fv || 
          summary.maxShearStressRatio?.allowableStress ||  // Added: get F'v from analysis results
          undefined
        ),
        totalLoad_FbPrime_psi: designValues.adjusted_Fb_pos || (
          // Fallbacks for manual mode allowable bending stress
          designValues.Fb || 
          parsedBeamProperties.manual_Fb_allow ||  // Fixed: correct field name for manual allowable bending stress
          parsedBeamProperties.manual_Fb || 
          parsedBeamProperties.allowable_Fb || 
          parsedBeamProperties.Fb || 
          lumberProps.Fb || 
          summary.maxBendingStressRatio?.allowableStress ||  // Added: get F'b from analysis results
          undefined
        ),
        fv_psi: summary.maxShearStressRatio?.actualStress,
        fb_psi: summary.maxBendingStressRatio?.actualStress,
        d_TL_in: summary.maxTotalDeflectionDownward?.value,
        d_LL_in: summary.maxLiveLoadDeflectionDownward?.value, 
        fv_percentage: summary.maxShearStressRatio?.ratio !== undefined ? `${(summary.maxShearStressRatio.ratio * 100).toFixed(0)}%` : undefined,
        fb_percentage: summary.maxBendingStressRatio?.ratio !== undefined ? `${(summary.maxBendingStressRatio.ratio * 100).toFixed(0)}%` : undefined,
        d_TL_L_ratio: summary.maxTotalDeflectionDownward?.limit,
        d_TL_loadCombo: summary.maxTotalDeflectionDownward?.loadComboName,
        d_LL_L_ratio: summary.maxLiveLoadDeflectionDownward?.limit,
        loads: actualLoadsForPDF,
        supports: parsedSupports,
        Vmax_lbs: summary.maxShearValue?.value,
        M_in_kips: summary.maxMomentValue?.value !== undefined ? summary.maxMomentValue.value / 1000 : undefined, 
        Vmax_position_ft: summary.maxShearValue?.position,
        M_position_ft: summary.maxMomentValue?.position,
        case_value: summary.maxBendingStressRatio?.loadComboName || summary.maxShearValue?.loadComboName,
        maxShearValue_loadComboName: summary.maxShearValue?.loadComboName,
        maxMomentValue_loadComboName: summary.maxMomentValue?.loadComboName,
        folderId: analysis.folderId,
      };
      return combinedData;
    });
  }, [allProjectAnalyses, currentFolderIdQuery, selectedFolders, selectedAnalysesForActions]);

  const handleAnalysisDeleted = (deletedAnalysisId: string) => {
    // This function updates both allProject* and displayed* states
    setAllProjectAnalyses(prev => prev.filter(a => a.id !== deletedAnalysisId));
    setDisplayedAnalyses(prev => prev.filter(a => a.id !== deletedAnalysisId));
    
    // No change needed for calculationId part here, as it's correctly finding the associated calculation
    const deletedCalcData = allProjectAnalyses.find(a => a.id === deletedAnalysisId);
    if (deletedCalcData && deletedCalcData.calculationId) {
        setAllProjectCalculations(prev => prev.filter(c => c.id !== deletedCalcData.calculationId));
        setDisplayedCalculations(prev => prev.filter(c => c.id !== deletedCalcData.calculationId));
    }
    setSelectedAnalysesForActions(prev => prev.filter(id => id !== deletedAnalysisId)); // Ensure this uses the correct ID type
  };

  const onDragEnd = async (result: DropResult) => {
    const { source, destination, type, draggableId } = result;

    // Dropped outside the list or no destination
    if (!destination) return;

    // If dropped on the same place it started (for reordering)
    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return;
    }

    if (type === 'analysis') {
      // Handle dropping an analysis onto a folder
      if (destination.droppableId.startsWith('folder-drop-')) {
        const targetFolderId = destination.droppableId.replace('folder-drop-', '');
        // draggableId from react-beautiful-dnd is set to analysis.calculationId in the Draggable component
        // For consistency and clarity, let's ensure we get the actual calculationId for folder operations
        const analysisBeingDragged = allProjectAnalyses.find(a => a.calculationId === draggableId);
        
        if (!analysisBeingDragged) {
            toast.error("Could not find the analysis being dragged.");
            return;
        }
        const calculationIdToMove = analysisBeingDragged.calculationId;

        if (analysisBeingDragged && analysisBeingDragged.folderId === targetFolderId) {
          toast.info("Calculation is already in this folder.");
          return;
        }

        try {
          // Use the existing endpoint for updating folder contents
          const res = await fetch(`/api/projects/${projectId}/folders/${targetFolderId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            // The existing endpoint expects an array of calculation IDs to be associated with the folder.
            // When dropping one, we send an array with that one ID.
            // The backend should ideally merge this or set this as the list.
            // Based on the provided backend code, sending calculationIds will set those calculations to this folder.
            body: JSON.stringify({ calculationIds: [calculationIdToMove] }), 
          });

          if (!res.ok) {
            // Check content type before parsing as JSON
            const contentType = res.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
              const errorData = await res.json();
              toast.error(errorData.error || 'Failed to move calculation to folder.');
            } else {
              const textError = await res.text();
              toast.error(`Failed to move calculation. Server responded with ${res.status}: ${textError.substring(0, 100)}...`);
            }
            return; // Don't refetch if API call failed
          }

          toast.success('Calculation moved to folder successfully.');
          fetchProjectAndFilterData(); // Re-fetch all data to update UI comprehensively

        } catch (error) {
          console.error('Error moving calculation to folder:', error);
          toast.error('An error occurred while moving the calculation.');
        }
        return; // End here after handling drop to folder
      }

      // Handle reordering analyses within the current view (project root or inside a folder)
      if (source.droppableId === destination.droppableId && (destination.droppableId === 'analysesDroppableGrid' || destination.droppableId === 'analysesDroppableList')) {
        let items = Array.from(displayedAnalyses); // Use displayedAnalyses for reordering current view
        const [reorderedItem] = items.splice(source.index, 1);
        items.splice(destination.index, 0, reorderedItem);
        setDisplayedAnalyses(items); // Update UI immediately for perceived speed

        const calculationsToUpdate = items.map((item, index) => ({
          id: item.calculationId, // Using calculationId for reorder API as it expects Calculation model IDs
          order: index,
        }));

        try {
          let res;
          
          if (currentFolderIdQuery) {
            // Reordering within a folder - use folder-specific endpoint
            res = await fetch(`/api/projects/${projectId}/folders/${currentFolderIdQuery}/reorder-calculations`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ calculations: calculationsToUpdate }),
            });
          } else {
            // Reordering at project root - use project-level endpoint
            res = await fetch(`/api/projects/${projectId}/calculations/reorder`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ calculations: calculationsToUpdate }),
            });
          }
          
          if (!res.ok) {
            const errorData = await res.json();
            toast.error(errorData.error || 'Failed to save new order.');
            fetchProjectAndFilterData(); // Revert UI by re-fetching on error
          } else {
            toast.success('Order saved successfully.');
            // Optionally re-fetch to ensure data consistency, though local update might be enough here.
            fetchProjectAndFilterData(); 
          }
        } catch (error) {
          console.error('Error saving order:', error);
          toast.error('An error occurred while saving the new order.');
          fetchProjectAndFilterData(); // Revert on error
        }
      }
    } else if (type === 'folder') {
        // Handle folder reordering logic
        if (source.droppableId === destination.droppableId && (destination.droppableId === 'foldersDroppableGrid' || destination.droppableId === 'foldersDroppableList')) {
          let items = Array.from(displayedFolders);
          const [reorderedItem] = items.splice(source.index, 1);
          items.splice(destination.index, 0, reorderedItem);
          setDisplayedFolders(items); // Update UI immediately for perceived speed

          const foldersToUpdate = items.map((item, index) => ({
            id: item.id,
            order: index,
          }));

          try {
            const res = await fetch(`/api/projects/${projectId}/folders/reorder`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ folders: foldersToUpdate }),
            });
            
            if (!res.ok) {
              const errorData = await res.json();
              toast.error(errorData.error || 'Failed to save new folder order.');
              fetchProjectAndFilterData(); // Revert UI by re-fetching on error
            } else {
              toast.success('Folder order saved successfully.');
              // Optionally re-fetch to ensure data consistency
              fetchProjectAndFilterData(); 
            }
          } catch (error) {
            console.error('Error saving folder order:', error);
            toast.error('An error occurred while saving the new folder order.');
            fetchProjectAndFilterData(); // Revert on error
          }
        }
    }
  };
  
  // const handleSaveStatusAndDate = async () => {
  //   if (!project) return;
  //   setIsSavingStatus(true);
  //   try {
  //     const res = await fetch(`/api/projects/${project.id}`, {
  //       method: 'PATCH',
  //       headers: { 'Content-Type': 'application/json' },
  //       body: JSON.stringify({ status: currentStatus, dueDate: currentDueDate }),
  //     });
  //     if (!res.ok) {
  //       throw new Error('Failed to update project status and due date.');
  //     }
  //     const updatedProject = await res.json();
  //     setProject(prev => prev ? { ...prev, status: updatedProject.status, dueDate: updatedProject.dueDate } : null);
  //     toast.success('Project status and due date updated.');
  //   } catch (error) {
  //     console.error('Error updating project:', error);
  //     toast.error(error instanceof Error ? error.message : 'Failed to update project.');
  //   } finally {
  //     setIsSavingStatus(false);
  //   }
  // };

  const handleOpenDeleteConfirmation = async () => {
    if (selectedAnalysesForActions.length === 0) {
      toast.info("No analyses selected for deletion.");
      return;
    }
    setShowDeleteConfirmation(true);
  };
  
  const handleInitiateDeleteSingleAnalysis = async (analysis: BeamAnalysisResultForCard) => {
    setAnalysisForDependentsModal(analysis); 
    try {
        const dependentsRes = await fetch(`/api/calculations/${analysis.calculationId}/dependents`);
        if (!dependentsRes.ok) throw new Error("Failed to fetch dependents.");
        const dependentsData: ApiDependencyGraphData = await dependentsRes.json();
        
        const childrenIds = dependentsData.edges
            .filter(edge => edge.source === analysis.calculationId)
            .map(edge => edge.target);

        if (childrenIds.length > 0) {
            setChildrenForModalReanalysis(childrenIds);
        } else {
            // For single delete, we are passing the BeamAnalysisResult.id to handleActualDeletionViaModal
            // but selectedAnalysesForActions should still use the same ID type as bulk delete.
            setSelectedAnalysesForActions([analysis.id]); // Correctly use BeamAnalysisResult ID here
            setShowDeleteConfirmation(true); 
        }
    } catch (error) {
        console.error("Error checking dependents for single delete:", error);
        toast.error("Could not check for dependent analyses. Please try again.");
        setAnalysisForDependentsModal(null); 
    }
  };

  const handleActualDeletionViaModal = async (beamAnalysisResultIdToDelete: string) => {
      setIsDeletingAnalyses(true);
      try {
          const res = await fetch(`/api/beam-analysis/results/${beamAnalysisResultIdToDelete}`, {
              method: 'DELETE',
          });
          if (!res.ok) {
              const errorData = await res.json();
              throw new Error(errorData.error || 'Failed to delete analysis.');
          }
          toast.success(`Analysis deleted successfully.`);
          handleAnalysisDeleted(beamAnalysisResultIdToDelete); 
          fetchProjectAndFilterData(); 
      } catch (error) {
          console.error('Error deleting analysis:', error);
          toast.error(error instanceof Error ? error.message : 'An error occurred during deletion.');
      } finally {
          setIsDeletingAnalyses(false);
          setAnalysisForDependentsModal(null); 
          setChildrenForModalReanalysis([]);
      }
  };

  const handleModalReanalysisComplete = () => {
      // The DependentsModal has completed reanalysis and deletion
      // Just clean up the modal state
      setAnalysisForDependentsModal(null);
      setChildrenForModalReanalysis([]);
      setIsDeletingAnalyses(false);
      fetchProjectAndFilterData(); // Refresh the data to show updated state
  };

  const handleConfirmBulkDelete = async () => {
    setIsDeletingAnalyses(true);
    const analysesToDelete = [...selectedAnalysesForActions]; 
    try {
      const res = await fetch(`/api/beam-analysis/bulk-delete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: analysesToDelete }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to delete selected analyses.');
      }

      const resultData = await res.json();
      const successfullyDeletedCount = resultData.count || 0; 

      toast.success(`${successfullyDeletedCount} analysis/analyses deleted successfully.`);
      analysesToDelete.forEach(id => handleAnalysisDeleted(id));
      setSelectedAnalysesForActions([]); 
      fetchProjectAndFilterData(); 

    } catch (error) {
      console.error('Error deleting analyses:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred during bulk deletion.');
    } finally {
      setIsDeletingAnalyses(false);
      setShowDeleteConfirmation(false);
    }
  };

  const handleOpenCreateCalculationModal = () => {
    setNewCalculationType("BEAM"); 
    setIsCreateCalculationModalOpen(true);
  };

  const handleConfirmCreateCalculation = () => {
    if (!project) {
      toast.error("Project context is not available.");
      return;
    }

    let targetUrl = `/organizations/${orgId}/projects/${project.id}/calculations/new?type=${newCalculationType}`;
    if (currentFolderIdQuery) {
      targetUrl += `&folderId=${currentFolderIdQuery}`;
    }
    
    router.push(targetUrl);
    setIsCreateCalculationModalOpen(false);
  };

  const handleConfirmCreateFolder = async () => {
    if (!project || !newFolderName.trim()) {
        toast.error("Project not loaded or folder name is empty.");
        return;
    }
    setIsCreatingFolder(true);
    try {
        const res = await fetch(`/api/projects/${project.id}/folders`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: newFolderName }),
        });
        if (!res.ok) {
            const errorData = await res.json();
            throw new Error(errorData.error || 'Failed to create folder.');
        }
        const newFolder = await res.json();
        // Add to displayedFolders if at project root, and also refetch project data for robustness
        if (!currentFolderIdQuery) {
            setDisplayedFolders(prev => [...prev, newFolder].sort((a,b) => a.name.localeCompare(b.name)));
        }
        fetchProjectAndFilterData(); // Full refetch to update all lists
        toast.success(`Folder "${newFolder.name}" created successfully.`);
        setIsCreateFolderModalOpen(false);
        setNewFolderName("");
    } catch (error) {
        console.error("Error creating folder:", error);
        toast.error(error instanceof Error ? error.message : "Could not create folder.");
    } finally {
        setIsCreatingFolder(false);
    }
  };

  const handleRenameFolder = async (folderIdToRename: string, updatedName: string) => {
    if (!project || !updatedName.trim()) {
        toast.error("Folder name cannot be empty.");
        return;
    }
    try {
        const res = await fetch(`/api/projects/${project.id}/folders/${folderIdToRename}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: updatedName }),
        });
        if (!res.ok) {
            const errorData = await res.json();
            throw new Error(errorData.error || 'Failed to rename folder.');
        }
        const updatedFolder = await res.json();
        // Update in displayedFolders if at project root
        if (!currentFolderIdQuery) {
            setDisplayedFolders(prev => 
                prev.map(f => f.id === updatedFolder.id ? updatedFolder : f)
                    .sort((a,b) => a.name.localeCompare(b.name))
            );
        }
        if (currentFolder && currentFolder.id === updatedFolder.id) {
            setCurrentFolder(updatedFolder); // Update current folder name if it's the one being viewed
        }
        fetchProjectAndFilterData(); // Full refetch
        toast.success(`Folder renamed to "${updatedFolder.name}".`);
        setRenamingFolderId(null);
        setEditingFolderName("");
    } catch (error) {
        console.error("Error renaming folder:", error);
        toast.error(error instanceof Error ? error.message : "Could not rename folder.");
    }
  };

  const handleDeleteFolder = async (folderIdToDelete: string) => {
    if (!project) return;
    setIsDeletingFolder(true); 
    try {
        const res = await fetch(`/api/projects/${project.id}/folders/${folderIdToDelete}`, {
            method: 'DELETE',
        });
        if (!res.ok) {
            const errorData = await res.json();
            if (res.status === 409 && errorData.error === "Folder is not empty. Cannot delete.") {
                 toast.error(errorData.error + " Please move or delete its contents first.");
            } else {
                throw new Error(errorData.error || 'Failed to delete folder.');
            }
        } else {
            // Remove from displayedFolders if at project root
            if (!currentFolderIdQuery) {
                setDisplayedFolders(prev => prev.filter(f => f.id !== folderIdToDelete));
            }
            fetchProjectAndFilterData(); // Full refetch
            toast.success(`Folder deleted successfully.`);
        }
    } catch (error) {
        console.error("Error deleting folder:", error);
        if (!(error instanceof Error && error.message.includes("Folder is not empty"))) {
            toast.error(error instanceof Error ? error.message : "Could not delete folder.");
        }
    } finally {
        setIsDeletingFolder(false);
        setFolderToDelete(null); 
    }
  };
  
  const handleCreateBeamAnalysis = () => { // This function now acts as a pre-config for the modal
    handleOpenCreateCalculationModal(); // The modal will use currentFolderIdQuery
  };

  const handleSelectAllAnalyses = (checked: boolean | string) => {
    if (checked) {
      // Select all *displayed* analyses
      setSelectedAnalysesForActions(displayedAnalyses.map(a => a.id)); // Use BeamAnalysisResult ID
    } else {
      setSelectedAnalysesForActions([]);
    }
  };

  const handleSelectAnalysis = (beamAnalysisResultId: string, checked: boolean | string) => {
    if (checked) {
      setSelectedAnalysesForActions(prev => [...prev, beamAnalysisResultId]);
    } else {
      setSelectedAnalysesForActions(prev => prev.filter(id => id !== beamAnalysisResultId));
    }
  };

  // Add folder selection handlers
  const handleSelectAllFolders = (checked: boolean | string) => {
    if (checked) {
      setSelectedFolders(displayedFolders.map(folder => folder.id));
    } else {
      setSelectedFolders([]);
    }
  };

  const handleSelectFolder = (folderId: string, checked: boolean | string) => {
    if (checked) {
      setSelectedFolders(prev => [...prev, folderId]);
    } else {
      setSelectedFolders(prev => prev.filter(id => id !== folderId));
    }
  };

  // Function to clear all selections
  const clearAllSelections = () => {
    setSelectedFolders([]);
    setSelectedAnalysesForActions([]);
  };

  const filteredAndSortedAnalyses = useMemo(() => {
    // Start with displayedAnalyses (which is already filtered by folderId if applicable)
    return displayedAnalyses.filter(analysis => {
      const nameMatch = analysis.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
                        analysis.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        analysis.calculationId?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const material = getMaterialType(analysis.beamProperties);
      const materialMatch = materialTypeFilter === "all" || material === materialTypeFilter;
      
      const bendingRatio = analysis.bendingStressRatio;
      const shearRatio = analysis.shearStressRatio;

      let bendingPass = true;
      if (bendingCheckFilter === "pass" && (bendingRatio === null || bendingRatio === undefined || bendingRatio > 1)) bendingPass = false;
      if (bendingCheckFilter === "fail" && (bendingRatio !== null && bendingRatio !== undefined && bendingRatio <= 1)) bendingPass = false;
      if (bendingCheckFilter === "not-checked" && bendingRatio !== null && bendingRatio !== undefined) bendingPass = false;

      let shearPass = true;
      if (shearCheckFilter === "pass" && (shearRatio === null || shearRatio === undefined || shearRatio > 1)) shearPass = false;
      if (shearCheckFilter === "fail" && (shearRatio !== null && shearRatio !== undefined && shearRatio <= 1)) shearPass = false;
      if (shearCheckFilter === "not-checked" && shearRatio !== null && shearRatio !== undefined) shearPass = false;

      return nameMatch && materialMatch && bendingPass && shearPass;
    });
    // Sorting is handled by onDragEnd or initial fetch (setDisplayedAnalyses)
  }, [displayedAnalyses, searchTerm, materialTypeFilter, bendingCheckFilter, shearCheckFilter]);

  // Pagination for folders
  const folderTotalPages = Math.ceil(displayedFolders.length / itemsPerPage);

  // Pagination for calculations
  const calculationTotalPages = Math.ceil(filteredAndSortedAnalyses.length / itemsPerPage);

  // Reset pagination when filters change
  useEffect(() => {
    setCalculationCurrentPage(1);
  }, [searchTerm, materialTypeFilter, bendingCheckFilter, shearCheckFilter]);

  // Reset folder pagination when switching between project root and folder view
  useEffect(() => {
    setFolderCurrentPage(1);
    setCalculationCurrentPage(1);
  }, [currentFolderIdQuery]);

  const paginatedFolders = useMemo(() => {
    const startIndex = (folderCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return displayedFolders.slice(startIndex, endIndex);
  }, [displayedFolders, folderCurrentPage, itemsPerPage]);

  const paginatedCalculations = useMemo(() => {
    const startIndex = (calculationCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredAndSortedAnalyses.slice(startIndex, endIndex);
  }, [filteredAndSortedAnalyses, calculationCurrentPage, itemsPerPage]);

  const pageTitle = currentFolderIdQuery 
    ? currentFolder?.name || "Folder"
    : project?.title || "Project Details";
  
  const projectBaseUrl = `/organizations/${orgId}/projects/${projectId}`;

  const handleConfirmMoveToFolder = async () => {
    if (!project || !targetFolderForMove || selectedAnalysesForActions.length === 0) {
        toast.error("No target folder selected or no analyses selected.");
        return;
    }
    setIsMovingAnalyses(true);

    const calculationIdsToMove = selectedAnalysesForActions.map(analysisId => {
        const analysis = allProjectAnalyses.find(a => a.id === analysisId);
        return analysis?.calculationId;
    }).filter(id => id !== undefined) as string[];

    if (calculationIdsToMove.length === 0) {
        toast.error("Could not find calculation details for selected items.");
        setIsMovingAnalyses(false);
        return;
    }
    
    let itemsAlreadyInTarget = false;
    if (targetFolderForMove === PROJECT_ROOT_ID) {
        itemsAlreadyInTarget = calculationIdsToMove.every(calcId => {
            const analysis = allProjectAnalyses.find(a => a.calculationId === calcId);
            return !analysis?.folderId; // Already at root if folderId is null/undefined
        });
    } else {
        itemsAlreadyInTarget = calculationIdsToMove.every(calcId => {
            const analysis = allProjectAnalyses.find(a => a.calculationId === calcId);
            return analysis?.folderId === targetFolderForMove;
        });
    }

    if (itemsAlreadyInTarget) {
        const targetName = targetFolderForMove === PROJECT_ROOT_ID
            ? "Project Root"
            : project.folders.find(f => f.id === targetFolderForMove)?.name || "the selected folder";
        toast.info(`Selected items are already in ${targetName}.`);
        setIsMovingAnalyses(false);
        setIsMoveToFolderModalOpen(false);
        setTargetFolderForMove(null);
        setSelectedAnalysesForActions([]); 
        return;
    }

    try {
        if (targetFolderForMove === PROJECT_ROOT_ID) {
            // Move to Project Root: Update each calculation to set folderId to null
            const updatePromises = calculationIdsToMove.map(calcId => {
                return fetch(`/api/calculations/${calcId}`, {
                    method: 'PATCH',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folderId: null }),
                });
            });

            const results = await Promise.allSettled(updatePromises);
            const failedUpdates = results.filter(result => result.status === 'rejected' || (result.status === 'fulfilled' && !result.value.ok));

            if (failedUpdates.length > 0) {
                toast.error(`Failed to move ${failedUpdates.length} of ${calculationIdsToMove.length} items to Project Root. Please check console for details.`);
                failedUpdates.forEach(async (failure) => {
                    if (failure.status === 'rejected') {
                        console.error("Error during fetch for moving to root:", failure.reason);
                    } else if (failure.status === 'fulfilled' && !failure.value.ok) {
                        const errorData = await failure.value.json().catch(() => ({ error: "Unknown error parsing response"}));
                        console.error("API error moving item to root:", errorData.error);
                    }
                });
            } else {
                toast.success(`${calculationIdsToMove.length} calculation(s) moved to Project Root successfully.`);
            }

        } else {
            // Move to a specific folder (existing logic)
            const res = await fetch(`/api/projects/${project.id}/folders/${targetFolderForMove}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ calculationIds: calculationIdsToMove }), 
            });

            if (!res.ok) {
                const contentType = res.headers.get("content-type");
                if (contentType && contentType.indexOf("application/json") !== -1) {
                    const errorData = await res.json();
                    throw new Error(errorData.error || 'Failed to move calculations to folder.');
                } else {
                    const textError = await res.text();
                    throw new Error(`Failed to move calculations. Server responded with ${res.status}: ${textError.substring(0, 100)}...`);
                }
            }
            toast.success(`${calculationIdsToMove.length} calculation(s) moved successfully.`);
        }

        fetchProjectAndFilterData(); 
        setIsMoveToFolderModalOpen(false);
        setSelectedAnalysesForActions([]);
        setTargetFolderForMove(null);
    } catch (error) {
        console.error('Error moving calculations:', error);
        toast.error(error instanceof Error ? error.message : 'An error occurred while moving calculations.');
    } finally {
        setIsMovingAnalyses(false);
    }
  };

  // Add state for project settings modal
  const [isProjectSettingsModalOpen, setIsProjectSettingsModalOpen] = useState(false);
  const [projectSettingsForm, setProjectSettingsForm] = useState({
    title: '',
    clientName: '',
    clientAddress: '',
    clientPhone: '',
    address: '',
    projectNumber: '',
    status: '',
    dueDate: ''
  });
  const [isSavingProjectSettings, setIsSavingProjectSettings] = useState(false);

  // Initialize project settings form when project loads
  useEffect(() => {
    if (project) {
      setProjectSettingsForm({
        title: project.title || '',
        clientName: project.clientName || '',
        clientAddress: project.clientAddress || '',
        clientPhone: project.clientPhone || '',
        address: project.address || '',
        projectNumber: project.projectNumber || '',
        status: project.status || '',
        dueDate: project.dueDate ? new Date(project.dueDate).toISOString().split('T')[0] : ''
      });
    }
  }, [project]);

  // Handler to open project settings modal
  const handleOpenProjectSettings = () => {
    if (project) {
      setProjectSettingsForm({
        title: project.title || '',
        clientName: project.clientName || '',
        clientAddress: project.clientAddress || '',
        clientPhone: project.clientPhone || '',
        address: project.address || '',
        projectNumber: project.projectNumber || '',
        status: project.status || '',
        dueDate: project.dueDate ? new Date(project.dueDate).toISOString().split('T')[0] : ''
      });
      setIsProjectSettingsModalOpen(true);
    }
  };

  // Handler to save project settings
  const handleSaveProjectSettings = async () => {
    if (!project || !projectSettingsForm.title.trim()) {
      toast.error("Project title is required.");
      return;
    }
    
    setIsSavingProjectSettings(true);
    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectSettingsForm),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update project');
      }
      
      const updatedProject = await response.json();
      setProject(updatedProject);
      toast.success('Project settings updated successfully!');
      setIsProjectSettingsModalOpen(false);
    } catch (err) {
      console.error('Error updating project settings:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to update project settings');
    } finally {
      setIsSavingProjectSettings(false);
    }
  };

  // Handler for form input changes
  const handleProjectSettingsChange = (field: string, value: string) => {
    setProjectSettingsForm(prev => ({ ...prev, [field]: value }));
  };

  // Add scroll listener effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (isLoading && !project) return (
    <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-lg">Loading project data...</p>
    </div>
  );
  if (error) return <div className="text-red-500 p-4">Error: {error}</div>;
  if (!project) return <div className="p-4">Project not found.</div>;
  // If it's a folder view and the folder itself isn't found after loading
  if (currentFolderIdQuery && !currentFolder && !isLoading) return (
    <div className="text-red-500 p-4">Error: Folder with ID "{currentFolderIdQuery}" not found in this project. 
        <Link href={projectBaseUrl} className="underline ml-2">Back to Project</Link>
    </div>
  );


  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="container mx-auto p-4 sm:p-6 lg:p-8">
          {/* Main Layout with Left Sidebar */}
          <div className="flex gap-6">
            {/* Left Sidebar - Score Cards */}
            <div className="w-80 flex-shrink-0">
              <div 
                className={`space-y-4 transition-all duration-200 ${
                  isScrolled ? 'sticky top-20' : 'sticky top-4'
                }`}
                style={{ 
                  // Add top margin when scrolled to account for sticky header
                  marginTop: isScrolled ? '120px' : '0px'
                }}
              >
                {/* Total Calculations Card */}
                <Card className="relative overflow-hidden border-0 shadow-lg" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                  <CardContent className="p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white/80 text-sm font-medium">Total Calculations</p>
                        <p className="text-3xl font-bold mt-1">{scoreCardStats.totalCalculations}</p>
                        <p className="text-white/90 text-xs mt-1">
                          {currentFolderIdQuery && currentFolder ? `in folder "${currentFolder.name}"` : 'in this project'}
                        </p>
                      </div>
                      <div className="p-3 bg-white/20 rounded-full">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 p-0 hover:bg-white/30"
                                onClick={handleClearAllFilters}
                              >
                                <List className="h-4 w-4 text-white" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Show all calculations</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Failed Bending Checks Card */}
                <Card className="relative overflow-hidden border-0 shadow-lg" style={{ backgroundColor: scoreCardStats.failedBendingChecks > 0 ? '#ea7f51' : '#6db145' }}>
                  <CardContent className="p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white/80 text-sm font-medium">
                          {scoreCardStats.failedBendingChecks > 0 ? 'Failed Bending Checks' : 'All passed bending checks'}
                        </p>
                        <p className="text-3xl font-bold mt-1">
                          {scoreCardStats.failedBendingChecks > 0 ? scoreCardStats.failedBendingChecks : '✓'}
                        </p>
                        <p className="text-white/90 text-xs mt-1">
                          {scoreCardStats.totalCalculations > 0 
                            ? scoreCardStats.failedBendingChecks === 0 
                              ? '100% pass checks'
                              : `${Math.round((scoreCardStats.failedBendingChecks / scoreCardStats.totalCalculations) * 100)}% of total`
                            : 'No calculations'
                          }
                        </p>
                      </div>
                      <div className="p-3 bg-white/20 rounded-full">
                        {scoreCardStats.failedBendingChecks > 0 ? (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 p-0 hover:bg-white/30"
                                  onClick={handleFilterFailedBending}
                                >
                                  <Filter className="h-4 w-4 text-white" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Show failed calculations</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <CheckCircle2 className="h-6 w-6 text-white" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Failed Shear Checks Card */}
                <Card className="relative overflow-hidden border-0 shadow-lg" style={{ backgroundColor: scoreCardStats.failedShearChecks > 0 ? '#ea7f51' : '#6db145' }}>
                  <CardContent className="p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white/80 text-sm font-medium">
                          {scoreCardStats.failedShearChecks > 0 ? 'Failed Shear Checks' : 'All passed shear checks'}
                        </p>
                        <p className="text-3xl font-bold mt-1">
                          {scoreCardStats.failedShearChecks > 0 ? scoreCardStats.failedShearChecks : '✓'}
                        </p>
                        <p className="text-white/90 text-xs mt-1">
                          {scoreCardStats.totalCalculations > 0 
                            ? scoreCardStats.failedShearChecks === 0 
                              ? '100% pass checks'
                              : `${Math.round((scoreCardStats.failedShearChecks / scoreCardStats.totalCalculations) * 100)}% of total`
                            : 'No calculations'
                          }
                        </p>
                      </div>
                      <div className="p-3 bg-white/20 rounded-full">
                        {scoreCardStats.failedShearChecks > 0 ? (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 p-0 hover:bg-white/30"
                                  onClick={handleFilterFailedShear}
                                >
                                  <Filter className="h-4 w-4 text-white" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Show failed calculations</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <CheckCircle2 className="h-6 w-6 text-white" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Right Content Area - All Page Content */}
            <div className="flex-1 min-w-0">
              {/* Sticky Header Container */}
              <div 
                className={`transition-all duration-200 ${
                  isScrolled 
                    ? 'sticky top-16 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border/40 shadow-sm' 
                    : ''
                }`}
                style={{ 
                  // Add minimal padding when sticky to reduce the blur gap
                  padding: isScrolled ? '0.5rem 0 0.25rem 0' : '0'
                }}
              >
                {/* Breadcrumbs and Header */}
                <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                        <div className="text-sm text-muted-foreground">
                        <Link href={`/organizations/${orgId}`} className="hover:underline">
                            {currentOrganization?.name || 'Organization'}
                        </Link>
                        {' > '}
                        <Link href={projectBaseUrl} className="hover:underline">
                            {project?.title || 'Project'}
                        </Link>
                        {currentFolderIdQuery && currentFolder && (
                            <>
                            {' > '}
                            <span className="font-semibold">{currentFolder.name}</span>
                            </>
                        )}
                        </div>
                    </div>

                    {/* Combined Page Title and Actions Toolbar */}
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-6">
                        <div className="flex items-center gap-2">
                            <h1 className="text-3xl font-bold tracking-tight text-foreground flex items-center whitespace-nowrap">
                                {currentFolderIdQuery && <FolderIcon className="h-7 w-7 mr-2 text-blue-500 flex-shrink-0" />}
                                {pageTitle}
                            </h1>
                            {!currentFolderIdQuery && project && (
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={handleOpenProjectSettings}
                                                className="h-8 w-8 text-muted-foreground hover:text-foreground"
                                            >
                                                <Settings className="h-4 w-4" />
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>Project Settings</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            )}
                        </div>
                    </div>

                    {/* New Toolbar for Actions - Search Bar and Buttons */}
                    <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-2 mb-6 sm:gap-3 md:gap-4">
                        <Input
                            placeholder="Search by name or ID..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="h-9 w-full sm:w-auto sm:flex-grow md:max-w-md lg:max-w-lg" 
                        />
                        <div className="flex items-center space-x-2 w-full sm:w-auto justify-end">
                            {project && (
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button 
                                                variant="outline" 
                                                size="sm" 
                                                onClick={() => setShowPdfPreview(true)} 
                                                disabled={pdfAnalyses.length === 0} 
                                                className="w-full sm:w-auto"
                                                style={{ backgroundColor: '#4a8cda', color: 'white', borderColor: '#4a8cda' }}
                                            >
                                                <Eye className="mr-2 h-4 w-4" /> 
                                                {(selectedFolders.length > 0 || selectedAnalysesForActions.length > 0) 
                                                  ? 'Preview PDF for selected items' 
                                                  : 'Preview PDF'}
                                            </Button>
                                        </TooltipTrigger>
                                        {pdfAnalyses.length === 0 && !isLoading && (
                                            <TooltipContent>
                                                <p>No calculations in {currentFolderIdQuery && currentFolder ? `folder "${currentFolder.name}"` : 'this project'} to generate PDF.</p>
                                            </TooltipContent>
                                        )}
                                    </Tooltip>
                                </TooltipProvider>
                            )}
                            <Button 
                                onClick={handleOpenCreateCalculationModal} 
                                variant="default" 
                                size="sm" 
                                className="w-full sm:w-auto"
                                style={{ backgroundColor: '#4a8cda', color: 'white' }}
                            >
                                <PlusCircle className="mr-2 h-4 w-4" /> 
                                {currentFolderIdQuery ? 'Add Calculation to Folder' : 'Add Calculation'}
                            </Button>
                            {!currentFolderIdQuery && ( 
                                <Button 
                                    onClick={() => setIsCreateFolderModalOpen(true)} 
                                    variant="outline" 
                                    size="sm" 
                                    className="w-full sm:w-auto"
                                    style={{ backgroundColor: '#71b841', color: 'white', borderColor: '#71b841' }}
                                >
                                    <FolderPlus className="mr-2 h-4 w-4" /> Create Folder
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
              </div>

              {/* Display Mode Toggle and Filters - Common for project and folder view */}
              <div className="mb-4 flex flex-col sm:flex-row justify-between items-center gap-4">
                  <div className="flex items-center space-x-2"> {/* This div is for List/Grid toggle only now */}
                      <Button
                          variant={displayMode === 'list' ? 'secondary' : 'ghost'}
                          size="sm"
                          onClick={() => setDisplayMode('list')}
                      >
                          <List className="mr-2 h-4 w-4" /> List
                      </Button>
                      <Button
                          variant={displayMode === 'grid' ? 'secondary' : 'ghost'}
                          size="sm"
                          onClick={() => setDisplayMode('grid')}
                      >
                          <LayoutGrid className="mr-2 h-4 w-4" /> Grid
                      </Button>
                  </div>
                  {/* The search input was moved to the header actions group above */}
                  {/* Original div for search input is removed */}
              </div>
              
              {displayMode === 'list' && ( // Filters only shown in list view for now
                  <div className="mb-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                       <Select value={materialTypeFilter} onValueChange={(value) => {
                          setMaterialTypeFilter(value);
                          updateUrlParams('material', value);
                       }}>
                          <SelectTrigger><SelectValue placeholder="Filter by material..." /></SelectTrigger>
                          <SelectContent>
                              <SelectItem value="all">All Materials</SelectItem>
                              <SelectItem value="glulam">Glulam</SelectItem>
                              <SelectItem value="sawn lumber">Sawn Lumber</SelectItem>
                              <SelectItem value="steel">Steel</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                      </Select>
                      <Select value={bendingCheckFilter} onValueChange={(value) => {
                          setBendingCheckFilter(value);
                          updateUrlParams('bending', value);
                      }}>
                          <SelectTrigger><SelectValue placeholder="Filter by bending check..." /></SelectTrigger>
                          <SelectContent>
                              <SelectItem value="all">All Bending Checks</SelectItem>
                              <SelectItem value="pass">Pass</SelectItem>
                              <SelectItem value="fail">Fail</SelectItem>
                              <SelectItem value="not-checked">Not Checked</SelectItem>
                          </SelectContent>
                      </Select>
                      <Select value={shearCheckFilter} onValueChange={(value) => {
                          setShearCheckFilter(value);
                          updateUrlParams('shear', value);
                      }}>
                          <SelectTrigger><SelectValue placeholder="Filter by shear check..." /></SelectTrigger>
                          <SelectContent>
                              <SelectItem value="all">All Shear Checks</SelectItem>
                              <SelectItem value="pass">Pass</SelectItem>
                              <SelectItem value="fail">Fail</SelectItem>
                              <SelectItem value="not-checked">Not Checked</SelectItem>
                          </SelectContent>
                      </Select>
                  </div>
              )}

              {/* Action bar for selected items */}
              {(selectedAnalysesForActions.length > 0 || selectedFolders.length > 0) && (
                  <div className="my-4 p-3 bg-muted rounded-lg flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {selectedAnalysesForActions.length > 0 && selectedFolders.length > 0 
                            ? `${selectedAnalysesForActions.length} calculation(s) and ${selectedFolders.length} folder(s) selected`
                            : selectedAnalysesForActions.length > 0 
                            ? `${selectedAnalysesForActions.length} calculation(s) selected`
                            : `${selectedFolders.length} folder(s) selected`
                          }
                        </span>
                        {selectedAnalysesForActions.length > 0 && !canBulkDeleteSelected && (
                          <span className="text-xs text-muted-foreground italic">
                            Bulk delete unavailable: One or more selected calculations have dependents. Please manage them individually.
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={clearAllSelections}
                        >
                            Clear Selection
                        </Button>
                        {selectedAnalysesForActions.length > 0 && (
                          <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                  setTargetFolderForMove(null);
                                  setIsMoveToFolderModalOpen(true);
                              }}
                              disabled={!project?.folders || project.folders.length === 0}
                              title={(!project?.folders || project.folders.length === 0) ? "No folders available to move to" : "Move selected to another folder"}
                          >
                              <MoveIcon className="mr-2 h-4 w-4" />
                              Move to...
                          </Button>
                        )}
                        {selectedAnalysesForActions.length > 0 && canBulkDeleteSelected && (
                            <Button variant="destructive" size="sm" onClick={handleOpenDeleteConfirmation} disabled={isDeletingAnalyses}>
                                {isDeletingAnalyses && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                <Trash2 className="mr-2 h-4 w-4" /> Delete Selected
                            </Button>
                        )}
                      </div>
                  </div>
              )}

              {/* Folders Display - Only at project root (when currentFolderIdQuery is null) */}
              {!currentFolderIdQuery && displayedFolders.length > 0 && (
                  <div className="mb-8">
                      <h2 className="text-2xl font-semibold tracking-tight text-foreground mb-4">Folders</h2>
                      {displayMode === 'list' ? (
                          <div> {/* Added a wrapping div for list view content */}
                              <Table>
                                  <TableHeader>
                                      <TableRow>
                                          <TableHead className="w-[60px] text-right">
                                              <Checkbox
                                                  checked={paginatedFolders.length > 0 && selectedFolders.length === displayedFolders.length} /* Corrected to use paginatedFolders for check if needed, but length check should be on displayedFolders */
                                                  onCheckedChange={handleSelectAllFolders}
                                                  aria-label="Select all folders"
                                                  disabled={paginatedFolders.length === 0}
                                              />
                                          </TableHead>
                                          <TableHead>Folder Name</TableHead>
                                          <TableHead className="text-right">Actions</TableHead>
                                      </TableRow>
                                  </TableHeader>
                                  <Droppable droppableId="foldersDroppableList" type="folder">
                                  {(provided) => (
                                      <TableBody ref={provided.innerRef} {...provided.droppableProps}>
                                  {paginatedFolders.map((folder, index) => (
                                      renamingFolderId === folder.id ? (
                                          <TableRow key={folder.id}>
                                              <TableCell className="text-center">
                                                  <Checkbox disabled />
                                              </TableCell>
                                              <TableCell>
                                                  <Input 
                                                      value={editingFolderName}
                                                      onChange={(e) => setEditingFolderName(e.target.value)}
                                                      autoFocus
                                                      onBlur={() => {
                                                          if (editingFolderName.trim() && editingFolderName !== folder.name) {
                                                              handleRenameFolder(folder.id, editingFolderName);
                                                          } else {
                                                              setRenamingFolderId(null); 
                                                          }
                                                      }}
                                                      onKeyDown={(e) => {
                                                          if (e.key === 'Enter') handleRenameFolder(folder.id, editingFolderName);
                                                          if (e.key === 'Escape') setRenamingFolderId(null);
                                                      }}
                                                  />
                                              </TableCell>
                                              <TableCell className="text-right">
                                                  <div className="flex justify-end gap-2">
                                                      <Button size="sm" onClick={() => handleRenameFolder(folder.id, editingFolderName)}>Save</Button>
                                                      <Button size="sm" variant="ghost" onClick={() => setRenamingFolderId(null)}>Cancel</Button>
                                                  </div>
                                              </TableCell>
                                          </TableRow>
                                      ) : (
                                          <Draggable key={folder.id} draggableId={folder.id} index={index}>
                                          {(providedDraggable) => (
                                          <TableRow ref={providedDraggable.innerRef} {...providedDraggable.draggableProps} className="hover:bg-muted/50">
                                              <TableCell className="text-right">
                                                  <div className="flex items-center justify-end">
                                                      <span {...providedDraggable.dragHandleProps} className="cursor-grab mr-2">
                                                          <GripVertical className="h-5 w-5 text-muted-foreground" />
                                                      </span>
                                                      <Checkbox
                                                          checked={selectedFolders.includes(folder.id)}
                                                          onCheckedChange={(checked) => handleSelectFolder(folder.id, checked)}
                                                          aria-label={`Select folder ${folder.name}`}
                                                      />
                                                  </div>
                                              </TableCell>
                                              <TableCell className="font-medium py-3">
                                                  <div className="flex items-center">
                                                      <FolderIcon className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
                                                      <Link 
                                                          href={`${projectBaseUrl}?folderId=${folder.id}`}
                                                          className="hover:underline"
                                                      >
                                                          {folder.name || 'Unnamed Folder'}
                                                      </Link>
                                                  </div>
                                              </TableCell>
                                              <TableCell className="text-right py-3">
                                                  <DropdownMenu>
                                                      <DropdownMenuTrigger asChild>
                                                          <Button variant="ghost" size="icon" className="h-8 w-8">
                                                              <MoreHorizontal className="h-4 w-4" />
                                                          </Button>
                                                      </DropdownMenuTrigger>
                                                      <DropdownMenuContent align="end">
                                                          <DropdownMenuItem onClick={() => router.push(`${projectBaseUrl}?folderId=${folder.id}`)}>
                                                              <Eye className="mr-2 h-4 w-4" /> Open
                                                          </DropdownMenuItem>
                                                          <DropdownMenuItem onClick={() => {
                                                              setRenamingFolderId(folder.id);
                                                              setEditingFolderName(folder.name);
                                                          }}>
                                                              <Edit3 className="mr-2 h-4 w-4" /> Rename
                                                          </DropdownMenuItem>
                                                          <DropdownMenuSeparator />
                                                          <DropdownMenuItem
                                                              onClick={() => setFolderToDelete(displayedFolders.find(f => f.id === folder.id) || null)}
                                                              className="text-red-600 focus:text-red-600 focus:bg-red-50"
                                                          >
                                                              <Trash2 className="mr-2 h-4 w-4" /> Delete
                                                          </DropdownMenuItem>
                                                      </DropdownMenuContent>
                                                  </DropdownMenu>
                                              </TableCell>
                                          </TableRow>
                                          )}
                                          </Draggable>
                                      )
                                  ))}
                                  {provided.placeholder}
                                  </TableBody>
                                  )}
                                  </Droppable>
                              </Table>
                              
                              {/* Folder Pagination */}
                              <Pagination
                                  currentPage={folderCurrentPage}
                                  totalPages={folderTotalPages}
                                  onPageChange={setFolderCurrentPage}
                                  totalItems={displayedFolders.length}
                                  itemsPerPage={itemsPerPage}
                              />
                          </div> /* Closing the wrapping div */
                      ) : (
                          <div>
                              <Droppable droppableId="foldersDroppableGrid" direction="horizontal" type="folder">
                              {(provided) => (
                              <div
                                  ref={provided.innerRef}
                                  {...provided.droppableProps}
                                  className={`grid gap-4 ${displayMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'}`}
                              >
                                  {paginatedFolders.map((folder, index) => (
                                      renamingFolderId === folder.id ? (
                                          <Card key={folder.id}>
                                              <CardHeader>
                                                  <Input 
                                                      value={editingFolderName}
                                                      onChange={(e) => setEditingFolderName(e.target.value)}
                                                      autoFocus
                                                      onBlur={() => {
                                                          if (editingFolderName.trim() && editingFolderName !== folder.name) {
                                                              handleRenameFolder(folder.id, editingFolderName);
                                                          } else {
                                                              setRenamingFolderId(null); 
                                                          }
                                                      }}
                                                      onKeyDown={(e) => {
                                                          if (e.key === 'Enter') handleRenameFolder(folder.id, editingFolderName);
                                                          if (e.key === 'Escape') setRenamingFolderId(null);
                                                      }}
                                                  />
                                              </CardHeader>
                                              <CardFooter className="flex justify-end gap-2">
                                                  <Button size="sm" onClick={() => handleRenameFolder(folder.id, editingFolderName)}>Save</Button>
                                                  <Button size="sm" variant="ghost" onClick={() => setRenamingFolderId(null)}>Cancel</Button>
                                              </CardFooter>
                                          </Card>
                                      ) : (
                                          <Draggable key={folder.id} draggableId={folder.id} index={index}>
                                          {(providedDraggable) => (
                                          <div
                                              ref={providedDraggable.innerRef}
                                              {...providedDraggable.draggableProps}
                                              {...providedDraggable.dragHandleProps}
                                              className="transition-all duration-200 hover:shadow-lg hover:shadow-gray-200 hover:-translate-y-1 cursor-grab active:cursor-grabbing"
                                          >
                                              <Card className="relative">
                                                  <FolderCard 
                                                      folder={folder}
                                                      orgId={orgId}
                                                      onRename={(id, currentName) => {
                                                          setRenamingFolderId(id);
                                                          setEditingFolderName(currentName);
                                                      }}
                                                      onDelete={(id) => setFolderToDelete(displayedFolders.find(f => f.id === id) || null)}
                                                  />
                                              </Card>
                                          </div>
                                          )}
                                          </Draggable>
                                      )
                                  ))}
                                  {provided.placeholder}
                              </div>
                              )}
                              </Droppable>
                              
                              {/* Folder Pagination for Grid View */}
                              <Pagination
                                  currentPage={folderCurrentPage}
                                  totalPages={folderTotalPages}
                                  onPageChange={setFolderCurrentPage}
                                  totalItems={displayedFolders.length}
                                  itemsPerPage={itemsPerPage}
                              />
                          </div>
                      )}
                  </div>
              )}
              { !currentFolderIdQuery && displayedFolders.length === 0 && !isLoading && (
                  <div className="py-8 text-center">
                      <FolderIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-2 text-sm font-medium text-foreground">No folders</h3>
                      <p className="mt-1 text-sm text-muted-foreground">Get started by creating a new folder.</p>
                      <div className="mt-6">
                          <Button onClick={() => setIsCreateFolderModalOpen(true)}>
                          <FolderPlus className="mr-2 h-4 w-4" /> Create Folder
                          </Button>
                      </div>
                  </div>
              )}

              {/* Calculations (Beam Analyses) Display */}
              <div>
                  <div className="flex items-center justify-between mb-4">
                      <h2 className="text-2xl font-semibold">
                        {"Calculations"}
                      </h2>
                  </div>

                  {isLoading && displayedAnalyses.length === 0 && (
                      <div className="text-center py-8">
                          <Loader2 className="mx-auto h-8 w-8 animate-spin text-muted-foreground" />
                          <p className="mt-2 text-muted-foreground">Loading calculations...</p>
                      </div>
                  )}

                  {!isLoading && filteredAndSortedAnalyses.length === 0 && (
                      <div className="py-8 text-center">
                          <List className="mx-auto h-12 w-12 text-muted-foreground" />
                          <h3 className="mt-2 text-sm font-medium text-foreground">
                              {currentFolderIdQuery ? "No calculations in this folder" : "No calculations in this project"}
                          </h3>
                          <p className="mt-1 text-sm text-muted-foreground">
                              {searchTerm ? "Try adjusting your search or filters." : (currentFolderIdQuery ? "Get started by adding a calculation to this folder." : "Get started by adding a new calculation.")}
                          </p>
                          <div className="mt-6">
                              <Button onClick={handleOpenCreateCalculationModal}>
                                  <PlusCircle className="mr-2 h-4 w-4" /> 
                                  {currentFolderIdQuery ? 'Add Calculation to Folder' : 'Add Calculation'}
                              </Button>
                          </div>
                      </div>
                  )}

                  {!isLoading && filteredAndSortedAnalyses.length > 0 && displayMode === 'grid' && (
                      <div>
                          <Droppable droppableId="analysesDroppableGrid" direction="horizontal" type="analysis">
                          {(provided) => (
                              <div
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                              className="grid grid-cols-1 gap-6"
                              >
                              {paginatedCalculations.map((analysis, index) => (
                                  <Draggable key={analysis.calculationId} draggableId={analysis.calculationId} index={index}>
                                  {(providedDraggable) => (
                                      <div
                                      ref={providedDraggable.innerRef}
                                      {...providedDraggable.draggableProps}
                                      {...providedDraggable.dragHandleProps}
                                      className="transition-all duration-200 hover:shadow-lg hover:shadow-gray-200 hover:-translate-y-1 cursor-grab active:cursor-grabbing"
                                      >
                                      <BeamAnalysisResultCard
                                          analysisResult={analysis}
                                          orgId={orgId}
                                          projectId={projectId}
                                          currentFolderId={currentFolderIdQuery}
                                          onInitiateDelete={handleInitiateDeleteSingleAnalysis}
                                          unitSystem={analysis.originalUnitSystem as UnitSystem || project?.beamAnalysisResults.find(br => br.id === analysis.id)?.originalUnitSystem as UnitSystem || UnitSystem.IMPERIAL}
                                      />
                                      </div>
                                  )}
                                  </Draggable>
                              ))}
                              {provided.placeholder}
                              </div>
                          )}
                          </Droppable>
                          
                          <Pagination
                              currentPage={calculationCurrentPage}
                              totalPages={calculationTotalPages}
                              onPageChange={setCalculationCurrentPage}
                              totalItems={filteredAndSortedAnalyses.length}
                              itemsPerPage={itemsPerPage}
                          />
                      </div>
                  )}

                  {!isLoading && filteredAndSortedAnalyses.length > 0 && displayMode === 'list' && (
                      <div>
                          <Table>
                              <TableHeader>
                                  <TableRow>
                                      <TableHead className="w-[60px] text-right">
                                          <Checkbox
                                              checked={paginatedCalculations.length > 0 && selectedAnalysesForActions.length === filteredAndSortedAnalyses.length}
                                              onCheckedChange={handleSelectAllAnalyses}
                                              aria-label="Select all rows"
                                              disabled={paginatedCalculations.length === 0}
                                          />
                                      </TableHead>
                                      <TableHead>Name</TableHead>
                                      <TableHead>Material</TableHead>
                                      <TableHead>Size</TableHead>
                                      <TableHead>Length ({paginatedCalculations[0]?.originalUnitSystem === UnitSystem.METRIC ? 'm' : 'ft'})</TableHead>
                                      <TableHead>Bending Stress Ratio</TableHead>
                                      <TableHead>Shear Stress Ratio</TableHead>
                                      <TableHead className="text-right">Actions</TableHead>
                                  </TableRow>
                              </TableHeader>
                              <Droppable droppableId="analysesDroppableList" type="analysis">
                              {(provided) => (
                                  <TableBody ref={provided.innerRef} {...provided.droppableProps}>
                                  {paginatedCalculations.map((analysis, index) => (
                                      <Draggable key={analysis.calculationId} draggableId={analysis.calculationId} index={index}>
                                      {(providedDraggable) => (
                                          <TableRow ref={providedDraggable.innerRef} {...providedDraggable.draggableProps} className="hover:bg-muted/50">
                                              <TableCell className="text-right">
                                                  <div className="flex items-center justify-end">
                                                      <span {...providedDraggable.dragHandleProps} className="cursor-grab mr-2">
                                                          <GripVertical className="h-5 w-5 text-muted-foreground" />
                                                      </span>
                                                      <Checkbox
                                                          checked={selectedAnalysesForActions.includes(analysis.id)}
                                                          onCheckedChange={(checked) => handleSelectAnalysis(analysis.id, checked)}
                                                          aria-label={`Select row for ${analysis.name || 'analysis'}`}
                                                      />
                                                  </div>
                                              </TableCell>
                                              <TableCell className="font-medium py-3">
                                                  <Link 
                                                      href={`${projectBaseUrl}/calculations/${analysis.calculationId}${currentFolderIdQuery ? `?folderId=${currentFolderIdQuery}` : ''}`}
                                                      className="hover:underline"
                                                  >
                                                      {analysis.name || 'Unnamed Analysis'}
                                                  </Link>
                                              </TableCell>
                                              <TableCell className="py-3">{getBeamDescription(analysis.beamProperties)}</TableCell>
                                              <TableCell className="py-3">{getBeamSize(analysis.beamProperties)}</TableCell>
                                              <TableCell className="py-3">{formatNumber(analysis.length)}</TableCell>
                                              <TableCell className={`py-3 ${ (analysis.bendingStressRatio ?? 0) > 1 ? 'text-red-500 font-semibold' : (analysis.bendingStressRatio && (analysis.bendingStressRatio ?? 0) <=1) ? 'text-green-600 font-semibold' : ''}`}>
                                                  {analysis.bendingStressRatio !== null && analysis.bendingStressRatio !== undefined 
                                                      ? formatNumber(analysis.bendingStressRatio, 2) 
                                                      : <span className="text-muted-foreground">-</span>}
                                                  {(analysis.bendingStressRatio ?? 0) > 1 && <XCircle className="inline ml-1 h-4 w-4" />}
                                                  {(analysis.bendingStressRatio && (analysis.bendingStressRatio ?? 0) <= 1) && <CheckCircle2 className="inline ml-1 h-4 w-4" />}
                                              </TableCell>
                                              <TableCell className={`py-3 ${ (analysis.shearStressRatio ?? 0) > 1 ? 'text-red-500 font-semibold' : (analysis.shearStressRatio && (analysis.shearStressRatio ?? 0) <= 1) ? 'text-green-600 font-semibold' : ''}`}>
                                                  {analysis.shearStressRatio !== null && analysis.shearStressRatio !== undefined 
                                                      ? formatNumber(analysis.shearStressRatio, 2) 
                                                      : <span className="text-muted-foreground">-</span>}
                                                  {(analysis.shearStressRatio ?? 0) > 1 && <XCircle className="inline ml-1 h-4 w-4" />}
                                                  {(analysis.shearStressRatio && (analysis.shearStressRatio ?? 0) <= 1) && <CheckCircle2 className="inline ml-1 h-4 w-4" />}
                                              </TableCell>
                                              <TableCell className="text-right py-3">
                                              <DropdownMenu>
                                                  <DropdownMenuTrigger asChild>
                                                      <Button variant="ghost" size="icon" className="h-8 w-8">
                                                          <MoreHorizontal className="h-4 w-4" />
                                                      </Button>
                                                  </DropdownMenuTrigger>
                                                  <DropdownMenuContent align="end">
                                                      <DropdownMenuItem onClick={() => router.push(`${projectBaseUrl}/calculations/${analysis.calculationId}${currentFolderIdQuery ? `?folderId=${currentFolderIdQuery}` : ''}`)}>
                                                          <Eye className="mr-2 h-4 w-4" /> View/Edit
                                                      </DropdownMenuItem>
                                                      <DropdownMenuItem onClick={() => {
                                                          toast.info("Re-analysis trigger to be implemented.");
                                                      }}>
                                                          <Settings className="mr-2 h-4 w-4" /> Re-analyze 
                                                      </DropdownMenuItem>
                                                      <DropdownMenuSeparator />
                                                      <DropdownMenuItem 
                                                          onClick={() => handleInitiateDeleteSingleAnalysis(analysis)} 
                                                          className="text-red-600 focus:text-red-600 focus:bg-red-50"
                                                      >
                                                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                                                      </DropdownMenuItem>
                                                  </DropdownMenuContent>
                                              </DropdownMenu>
                                              </TableCell>
                                          </TableRow>
                                      )}
                                      </Draggable>
                                  ))}
                                  {provided.placeholder}
                                  </TableBody>
                              )}
                              </Droppable>
                          </Table>
                          
                          <Pagination
                              currentPage={calculationCurrentPage}
                              totalPages={calculationTotalPages}
                              onPageChange={setCalculationCurrentPage}
                              totalItems={filteredAndSortedAnalyses.length}
                              itemsPerPage={itemsPerPage}
                          />
                      </div>
                  )}
              </div>
            </div>
          </div>

          {/* PDF Preview Modal - Now available in folder view as well */}
          {showPdfPreview && project && (
              <Dialog open={showPdfPreview} onOpenChange={setShowPdfPreview}>
              <DialogContent className="max-w-4xl h-[90vh]">
                  <DialogHeader>
                  <DialogTitle>
                    {'PDF Preview: '}
                    {currentFolderIdQuery && currentFolder
                      ? project?.title === currentFolder.name
                        ? `${currentFolder.name} (Folder)`
                        : `${project?.title} - ${currentFolder.name}`
                      : project?.title}
                  </DialogTitle>
                  <DialogDescription>
                      This is a preview of the {currentFolderIdQuery && currentFolder ? `calculations in folder "${currentFolder.name}"` : 'project calculations report'}.
                  </DialogDescription>
                  </DialogHeader>
                  <div className="h-[calc(90vh-200px)]"> {/* Adjusted height for footer space */}
                  {pdfAnalyses.length > 0 ? (
                      <PDFViewer width="100%" height="100%">
                          <ProjectCalculationsPDF
                              projectData={{
                                  title: project.title, // Always use main project title
                                  clientName: project.clientName,
                                  projectAddress: project.address,
                                  clientAddress: project.clientAddress,
                                  clientPhone: project.clientPhone,
                                  jobNo: project.projectNumber || 'N/A',
                                  by: project.projectBy || 'N/A',
                                  date: project.projectDate || new Date().toISOString().split('T')[0],
                                  sheet: project.sheetNo || 'N/A',
                                  beamAnalysisResults: pdfAnalyses, // These are already filtered if in folder view, or all if in project view
                                  organization: currentOrganization || undefined,
                                  allProjectFolders: project.folders || [],
                                  isProjectLevelPdf: !currentFolderIdQuery,
                                  currentFolderNameForSingleFolderPdf: currentFolderIdQuery && currentFolder ? currentFolder.name : null,
                              }}
                          />
                      </PDFViewer>
                  ) : (
                      <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No calculations to preview for {currentFolderIdQuery && currentFolder ? `folder "${currentFolder.name}"` : 'this project'}.</p>
                      </div>
                  )}
                  </div>
                  <DialogFooter className="sm:justify-between pt-4">
                      <DialogClose asChild>
                          <Button variant="outline">Close</Button>
                      </DialogClose>
                      {pdfAnalyses.length > 0 && (
                          <PDFDownloadLink
                              document={ (
                                  <ProjectCalculationsPDF
                                      projectData={{
                                          title: project.title, // Always use main project title
                                          clientName: project.clientName,
                                          projectAddress: project.address,
                                          clientAddress: project.clientAddress,
                                          clientPhone: project.clientPhone,
                                          jobNo: project.projectNumber || 'N/A',
                                          by: project.projectBy || 'N/A',
                                          date: project.projectDate || new Date().toISOString().split('T')[0],
                                          sheet: project.sheetNo || 'N/A',
                                          beamAnalysisResults: pdfAnalyses, // Same logic as above
                                          organization: currentOrganization || undefined,
                                          allProjectFolders: project.folders || [],
                                          isProjectLevelPdf: !currentFolderIdQuery,
                                          currentFolderNameForSingleFolderPdf: currentFolderIdQuery && currentFolder ? currentFolder.name : null,
                                      }}
                                  />
                              )}
                              fileName={`${
                                currentFolderIdQuery && currentFolder
                                  ? project?.title === currentFolder.name
                                    ? `${currentFolder.name}_Folder` 
                                    : `${project?.title}_-_${currentFolder.name}`
                                  : project?.title || 'Project'
                              }_Calculations_${new Date().toISOString().split('T')[0]}.pdf`}
                          >
                              {({ loading }) => (
                                  <Button variant="default" disabled={loading}>
                                      <FileDown className="mr-2 h-4 w-4" />
                                      {loading ? 'Generating...' : 'Download PDF'}
                                  </Button>
                              )}
                          </PDFDownloadLink>
                      )}
                  </DialogFooter>
              </DialogContent>
              </Dialog>
          )}
          
          {/* Create Calculation Modal */}
          <Dialog open={isCreateCalculationModalOpen} onOpenChange={setIsCreateCalculationModalOpen}>
              <DialogContent>
                  <DialogHeader>
                      <DialogTitle>Create New Calculation</DialogTitle>
                      <DialogDescription>
                          Select the type of calculation you want to add to {currentFolderIdQuery && currentFolder ? `folder "${currentFolder.name}"` : `project "${project?.title}"`}.
                      </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="calculationType" className="text-right">
                              Type
                          </Label>
                          <Select value={newCalculationType} onValueChange={setNewCalculationType}>
                              <SelectTrigger id="calculationType" className="col-span-3">
                                  <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                  <SelectItem value="BEAM">Beam Analysis</SelectItem>
                                  <SelectItem value="COLUMN" disabled>Column Design (Coming Soon)</SelectItem>
                                  <SelectItem value="FOOTING" disabled>Footing Design (Coming Soon)</SelectItem>
                              </SelectContent>
                          </Select>
                      </div>
                  </div>
                  <DialogFooter>
                      <Button variant="outline" onClick={() => setIsCreateCalculationModalOpen(false)}>Cancel</Button>
                      <Button onClick={handleConfirmCreateCalculation}>Create Calculation</Button>
                  </DialogFooter>
              </DialogContent>
          </Dialog>

          {/* Create Folder Modal - Only for Project Page (not in folder view) */}
          {!currentFolderIdQuery && (
              <Dialog open={isCreateFolderModalOpen} onOpenChange={setIsCreateFolderModalOpen}>
                  <DialogContent>
                      <DialogHeader>
                          <DialogTitle>Create New Folder</DialogTitle>
                          <DialogDescription>Enter a name for your new folder in project "{project?.title}".</DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                          <Label htmlFor="newFolderNameInput">Folder Name</Label> {/* Changed ID to avoid conflict with state var */}
                          <Input 
                              id="newFolderNameInput" 
                              value={newFolderName} 
                              onChange={(e) => setNewFolderName(e.target.value)}
                              placeholder="e.g., Floor Beams, Roof Trusses"
                          />
                      </div>
                      <DialogFooter>
                          <Button variant="outline" onClick={() => {setIsCreateFolderModalOpen(false); setNewFolderName("");}}>Cancel</Button>
                          <Button onClick={handleConfirmCreateFolder} disabled={isCreatingFolder || !newFolderName.trim()}>
                              {isCreatingFolder && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                              Create Folder
                          </Button>
                      </DialogFooter>
                  </DialogContent>
              </Dialog>
          )}
          
          {/* Delete Confirmation Modal (for analyses) */}
          <AlertDialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
              <AlertDialogContent>
                  <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                          This action will permanently delete {selectedAnalysesForActions.length} selected analysis/analyses. 
                          Any direct dependents might be affected if not re-analyzed or updated. This cannot be undone.
                      </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                          onClick={handleConfirmBulkDelete}
                          disabled={isDeletingAnalyses}
                          className="bg-red-600 hover:bg-red-700"
                      >
                          {isDeletingAnalyses ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                          Yes, delete
                      </AlertDialogAction>
                  </AlertDialogFooter>
              </AlertDialogContent>
          </AlertDialog>

          {/* Dependents Modal (for single analysis deletion check) */}
          {analysisForDependentsModal && (
              <DependentsModal
                  isOpen={!!analysisForDependentsModal}
                  onClose={() => {
                      setAnalysisForDependentsModal(null);
                      setChildrenForModalReanalysis([]);
                  }}
                  calculationId={analysisForDependentsModal.calculationId}
                  calculationName={analysisForDependentsModal.name || "Selected Analysis"}
                  orgId={orgId}
                  projectId={projectId}
                  isDeleteConfirmation={true} // This modal is for delete confirmation path
                  deleteFunction={async () => {
                      // This function will be called AFTER reanalysis is complete
                      return handleActualDeletionViaModal(analysisForDependentsModal.id);
                  }}
                  isProcessingDelete={isDeletingAnalyses}
                  directChildrenToReanalyze={childrenForModalReanalysis} // Corrected prop name
                  onReanalysisComplete={handleModalReanalysisComplete} 
              />
          )}

          {/* Delete Folder Confirmation Modal */}
          {folderToDelete && (
              <AlertDialog open={!!folderToDelete} onOpenChange={() => setFolderToDelete(null)}>
                  <AlertDialogContent>
                      <AlertDialogHeader>
                          <AlertDialogTitle>Delete Folder "{folderToDelete.name}"?</AlertDialogTitle>
                          <AlertDialogDescription>
                              Are you sure you want to delete this folder? 
                              This action cannot be undone. Calculations inside this folder will NOT be deleted but will be moved to the project root. 
                          </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                          <AlertDialogCancel onClick={() => setFolderToDelete(null)}>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                              onClick={() => handleDeleteFolder(folderToDelete.id)}
                              disabled={isDeletingFolder}
                              className="bg-red-600 hover:bg-red-700"
                          >
                              {isDeletingFolder && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                              Yes, Delete Folder
                          </AlertDialogAction>
                      </AlertDialogFooter>
                  </AlertDialogContent>
              </AlertDialog>
          )}

          {/* Move to Folder Modal */}
          <Dialog open={isMoveToFolderModalOpen} onOpenChange={setIsMoveToFolderModalOpen}>
              <DialogContent>
                  <DialogHeader>
                      <DialogTitle>Move Selected Calculations</DialogTitle>
                      <DialogDescription>
                          Select a folder to move the {selectedAnalysesForActions.length} selected calculation(s) to.
                      </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                      <Label htmlFor="targetFolderSelectMove">Target Folder</Label> {/* Ensure unique ID if "targetFolderSelect" is used elsewhere */}
                      <Select
                          value={targetFolderForMove || ""}
                          onValueChange={(value) => setTargetFolderForMove(value)}
                      >
                          <SelectTrigger id="targetFolderSelectMove">
                              <SelectValue placeholder="Select a folder" />
                          </SelectTrigger>
                          <SelectContent>
                              <SelectItem value={PROJECT_ROOT_ID}>
                                  <div className="flex items-center">
                                      <MoveUpIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                                      Project Root
                                  </div>
                              </SelectItem>
                              {availableFoldersForMove.map(folder => (
                                  <SelectItem key={folder.id} value={folder.id}>
                                      <div className="flex items-center">
                                          <FolderIcon className="h-4 w-4 mr-2 text-blue-500 flex-shrink-0" />
                                          {folder.name}
                                      </div>
                                  </SelectItem>
                              ))}
                              {availableFoldersForMove.length === 0 && (
                                  <div className="p-2 text-center text-xs text-muted-foreground italic">
                                      (No other folders to move to)
                                  </div>
                              )}
                          </SelectContent>
                      </Select>
                  </div>
                  <DialogFooter>
                      <Button variant="outline" onClick={() => {
                          setIsMoveToFolderModalOpen(false);
                          setTargetFolderForMove(null); // Reset selection on cancel
                      }}>Cancel</Button>
                      <Button 
                          onClick={handleConfirmMoveToFolder} 
                          disabled={!targetFolderForMove || isMovingAnalyses}
                      >
                          {isMovingAnalyses && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                          Move
                      </Button>
                  </DialogFooter>
              </DialogContent>
          </Dialog>

          {/* Project Settings Modal */}
          <Dialog open={isProjectSettingsModalOpen} onOpenChange={setIsProjectSettingsModalOpen}>
              <DialogContent className="max-w-2xl">
                  <DialogHeader>
                      <DialogTitle>Project Settings</DialogTitle>
                      <DialogDescription>
                          Update project information and client details.
                      </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-6 py-4">
                      <div className="space-y-4">
                          <div>
                              <Label htmlFor="projectTitle">Project Title*</Label>
                              <Input
                                  id="projectTitle"
                                  value={projectSettingsForm.title}
                                  onChange={(e) => handleProjectSettingsChange('title', e.target.value)}
                                  placeholder="Enter project title"
                                  className="mt-1"
                              />
                          </div>
                          
                          <div>
                              <Label htmlFor="projectNumber">Project Number</Label>
                              <Input
                                  id="projectNumber"
                                  value={projectSettingsForm.projectNumber}
                                  onChange={(e) => handleProjectSettingsChange('projectNumber', e.target.value)}
                                  placeholder="Enter project number"
                                  className="mt-1"
                              />
                          </div>
                          
                          <div>
                              <Label htmlFor="projectAddress">Project Address</Label>
                              <Input
                                  id="projectAddress"
                                  value={projectSettingsForm.address}
                                  onChange={(e) => handleProjectSettingsChange('address', e.target.value)}
                                  placeholder="Enter project address"
                                  className="mt-1"
                              />
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                              <div>
                                  <Label htmlFor="projectStatus">Project Status</Label>
                                  <Select 
                                      value={projectSettingsForm.status} 
                                      onValueChange={(value) => handleProjectSettingsChange('status', value)}
                                  >
                                      <SelectTrigger id="projectStatus" className="mt-1">
                                          <SelectValue placeholder="Set status" />
                                      </SelectTrigger>
                                      <SelectContent>
                                          <SelectItem value="Active">Active</SelectItem>
                                          <SelectItem value="On Hold">On Hold</SelectItem>
                                          <SelectItem value="Completed">Completed</SelectItem>
                                          <SelectItem value="Cancelled">Cancelled</SelectItem>
                                      </SelectContent>
                                  </Select>
                              </div>
                              
                              <div>
                                  <Label htmlFor="projectDueDate">Due Date</Label>
                                  <Input
                                      id="projectDueDate"
                                      type="date"
                                      value={projectSettingsForm.dueDate}
                                      onChange={(e) => handleProjectSettingsChange('dueDate', e.target.value)}
                                      className="mt-1"
                                  />
                              </div>
                          </div>
                      </div>
                      
                      <div className="border-t pt-4">
                          <h3 className="text-lg font-semibold mb-4">Client Information</h3>
                          <div className="space-y-4">
                              <div>
                                  <Label htmlFor="clientName">Client Name</Label>
                                  <Input
                                      id="clientName"
                                      value={projectSettingsForm.clientName}
                                      onChange={(e) => handleProjectSettingsChange('clientName', e.target.value)}
                                      placeholder="Enter client name"
                                      className="mt-1"
                                  />
                              </div>
                              
                              <div>
                                  <Label htmlFor="clientAddress">Client Address</Label>
                                  <Input
                                      id="clientAddress"
                                      value={projectSettingsForm.clientAddress}
                                      onChange={(e) => handleProjectSettingsChange('clientAddress', e.target.value)}
                                      placeholder="Enter client address"
                                      className="mt-1"
                                  />
                              </div>
                              
                              <div>
                                  <Label htmlFor="clientPhone">Client Phone</Label>
                                  <Input
                                      id="clientPhone"
                                      value={projectSettingsForm.clientPhone}
                                      onChange={(e) => handleProjectSettingsChange('clientPhone', e.target.value)}
                                      placeholder="Enter client phone"
                                      className="mt-1"
                                  />
                              </div>
                          </div>
                      </div>
                  </div>
                  <DialogFooter>
                      <Button 
                          variant="outline" 
                          onClick={() => setIsProjectSettingsModalOpen(false)}
                          disabled={isSavingProjectSettings}
                      >
                          Cancel
                      </Button>
                      <Button 
                          onClick={handleSaveProjectSettings} 
                          disabled={isSavingProjectSettings || !projectSettingsForm.title.trim()}
                      >
                          {isSavingProjectSettings ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                          Save Changes
                      </Button>
                  </DialogFooter>
              </DialogContent>
          </Dialog>

      </div>
    </DragDropContext>
  );
}