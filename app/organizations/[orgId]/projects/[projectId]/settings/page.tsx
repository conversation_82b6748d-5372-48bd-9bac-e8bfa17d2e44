'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Spinner from '../../../../../components/ui/Spinner';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

// Interface for the Project data (subset of what might be in your main Project interface)
interface ProjectSettingsData {
  title: string;
  clientName?: string | null;
  clientAddress?: string | null; // New field
  clientPhone?: string | null;   // New field
  // Include other fields from your Project model that you want to edit here if needed
}

export default function ProjectSettingsPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params?.projectId as string;
  const orgId = params?.orgId as string;

  const [project, setProject] = useState<ProjectSettingsData | null>(null);
  const [formData, setFormData] = useState<ProjectSettingsData>({ 
    title: '', 
    clientName: '', 
    clientAddress: '', 
    clientPhone: '' 
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProjectDetails = useCallback(async () => {
    if (!projectId) return;
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/projects/${projectId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch project details');
      }
      const data = await response.json();
      setProject(data);
      setFormData({
        title: data.title || '',
        clientName: data.clientName || '',
        clientAddress: data.clientAddress || '', // Populate new field
        clientPhone: data.clientPhone || '',     // Populate new field
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchProjectDetails();
  }, [fetchProjectDetails]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    if (!projectId || !formData.title.trim()) {
      alert("Project title is required.");
      return;
    }
    setIsSaving(true);
    setError(null);
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update project');
      }
      // Optionally, show a success message or redirect
      router.push(`/organizations/${orgId}/projects/${projectId}`);
      // Consider re-fetching data on the project detail page or managing state globally if needed
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error during save');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-10 flex justify-center items-center h-screen">
        <Spinner loading={true} size={50} />
      </div>
    );
  }

  if (error && !project) { // Show error primarily if project couldn't be loaded
    return (
      <div className="container mx-auto py-10 text-center">
        <p className="text-red-500">Error: {error}</p>
        <Link href={orgId && projectId ? `/organizations/${orgId}/projects/${projectId}` : (projectId ? `/projects/${projectId}` : '/organizations')}>
          <Button variant="outline" className="mt-4">Back to Project</Button>
        </Link>
      </div>
    );
  }
  
  if (!project) {
     return (
      <div className="container mx-auto py-10 text-center">
        <p>Project not found, or you may not have permission to view it.</p>
        <Link href={orgId ? `/organizations/${orgId}` : "/organizations"}>
            <Button variant="outline" className="mt-4">Back to Organization</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <Link href={`/organizations/${orgId}/projects/${projectId}`} className="text-sm text-blue-600 hover:underline flex items-center mb-4">
        <ArrowLeft size={16} className="mr-1" /> Back to Project Details
      </Link>
      <h1 className="text-3xl font-bold mb-8">Project Settings: {project.title}</h1>
      
      {error && <p className="text-red-500 mb-4">Save Error: {error}</p>} {/* Display save error here */}

      <div className="max-w-2xl mx-auto space-y-6 bg-white p-8 rounded-lg shadow">
        <div>
          <Label htmlFor="title">Project Title</Label>
          <Input
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Project Title"
            required
            className="mt-1"
          />
        </div>

        <h2 className="text-xl font-semibold border-t pt-6 mt-6">Client Information</h2>
        <div>
          <Label htmlFor="clientName">Client Name</Label>
          <Input
            id="clientName"
            name="clientName"
            value={formData.clientName || ''}
            onChange={handleChange}
            placeholder="Client Name (Optional)"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="clientAddress">Client Address</Label>
          <Input // Or use a TextArea if multi-line is needed
            id="clientAddress"
            name="clientAddress"
            value={formData.clientAddress || ''}
            onChange={handleChange}
            placeholder="Client Address (Optional)"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="clientPhone">Client Phone</Label>
          <Input
            id="clientPhone"
            name="clientPhone"
            value={formData.clientPhone || ''}
            onChange={handleChange}
            placeholder="Client Phone (Optional)"
            className="mt-1"
          />
        </div>

        <div className="flex justify-end space-x-2 pt-6 border-t mt-6">
          <Button variant="outline" onClick={() => router.push(`/organizations/${orgId}/projects/${projectId}`)} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving || isLoading}>
            {isSaving ? <Spinner loading={true} size={20} /> : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
} 