'use client';

import { useEffect, useState, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { BeamAnalysisResultCard } from '@/components/projects/BeamAnalysisResultCard'; // Assuming this component can be reused
import { UnitSystem } from '@/lib/types/units/unit-system';
import { ArrowLeft, Loader2, AlertTriangle, Edit3, Trash2, PlusCircle } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { toast } from 'sonner';

// Interfaces (mirroring structure from project page and API response)
// These might need adjustment based on the exact structure of your Prisma models and API responses.

interface Calculation {
  id: string;
  name: string | null;
  type: string;
  order: number | null;
  // Assuming beamAnalysisResults are nested if type is BEAM
  beamAnalysisResults?: BeamAnalysisResultForCard[]; 
  // Potentially other calculation types
  // columnAnalysisResults?: any[]; 
  // footingAnalysisResults?: any[];
  folderId: string | null;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

interface BeamAnalysisResultForCard {
  id: string; 
  calculationId: string; 
  name?: string | null;
  length: number; // Made required
  modulusOfElasticity: number; // Made required
  momentOfInertia: number; // Made required
  beamProperties: any; 
  loads: any;          
  supports: any;       
  results: any | null; 
  createdAt: string;
  updatedAt: string;
  originalUnitSystem?: UnitSystem | string;
  order?: number | null; 
  dependentsCount?: number; // Keep optional or provide default if card expects it
  bendingStressRatio?: number | null; 
  shearStressRatio?: number | null;   
  isReanalyzing?: boolean; 
}

interface FolderData {
  id: string;
  name: string;
  projectId: string;
  calculations: Calculation[];
  createdAt: string;
  updatedAt: string;
  project?: { // Optional: if you need project context like project name
    id: string;
    title: string;
  };
}


export default function FolderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params?.projectId as string;
  const folderId = params?.folderId as string;
  const orgId = params?.orgId as string;

  const [folderData, setFolderData] = useState<FolderData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);


  const fetchFolderDetails = useCallback(async () => {
    if (!projectId || !folderId) return;
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/projects/${projectId}/folders/${folderId}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.message || `Failed to fetch folder details: ${response.status}`);
      }
      const data: FolderData = await response.json();
      setFolderData(data);
      setNewFolderName(data.name); 
    } catch (err: any) {
      console.error("Error fetching folder details:", err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [projectId, folderId]);

  useEffect(() => {
    fetchFolderDetails();
  }, [fetchFolderDetails]);

  const handleRenameFolder = async () => {
    if (!folderData || !newFolderName || newFolderName === folderData.name) {
      setIsRenaming(false);
      return;
    }
    try {
      const response = await fetch(`/api/projects/${projectId}/folders/${folderId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newFolderName }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.error || errorData.message || `Failed to rename folder: ${response.status}`);
      }
      const updatedFolder: FolderData = await response.json();
      setFolderData(updatedFolder);
      setNewFolderName(updatedFolder.name);
      toast.success("Folder renamed successfully!");
    } catch (err: any) {
      console.error("Error renaming folder:", err);
      toast.error(err.message || "Failed to rename folder.");
      setNewFolderName(folderData.name); // Reset to original name on error
    } finally {
      setIsRenaming(false);
    }
  };

  const handleDeleteFolder = async () => {
    if (!folderData) return;
    try {
      const response = await fetch(`/api/projects/${projectId}/folders/${folderId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        if (response.status === 204) { // No content means success for DELETE
            toast.success("Folder deleted successfully!");
            router.push(`/organizations/${orgId}/projects/${projectId}`); // Navigate back to project page
            return;
        }
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        throw new Error(errorData.error || errorData.message || `Failed to delete folder: ${response.status}`);
      }
      // If response.ok is true but not 204 (e.g. 200 with body), it's still a success case for some APIs
      toast.success("Folder deleted successfully!");
      router.push(`/organizations/${orgId}/projects/${projectId}`);
    } catch (err: any) {
      console.error("Error deleting folder:", err);
      toast.error(err.message || "Failed to delete folder. Make sure it's empty.");
    } finally {
      setShowDeleteConfirm(false);
    }
  };
  
  const handleCreateBeamAnalysis = () => {
    if (!orgId || !projectId || !folderId) return;
    // Navigate to the beam creation page, passing folderId to associate
    // The creation page should handle the folderId param to set it on the new calculation
    router.push(`/organizations/${orgId}/projects/${projectId}/calculations/new?type=BEAM&folderId=${folderId}`);
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="ml-4 text-lg">Loading folder details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen text-destructive">
        <AlertTriangle className="h-16 w-16 mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Error Loading Folder</h2>
        <p className="mb-4 text-center">{error}</p>
        <Button onClick={() => fetchFolderDetails()} variant="outline">
          Try Again
        </Button>
        <Link href={`/organizations/${orgId}/projects/${projectId}`} className="mt-4">
          <Button variant="link">Back to Project</Button>
        </Link>
      </div>
    );
  }

  if (!folderData) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <AlertTriangle className="h-16 w-16 mb-4 text-muted-foreground" />
        <h2 className="text-2xl font-semibold mb-2">Folder Not Found</h2>
        <p className="mb-4">The requested folder could not be found or you do not have access.</p>
         <Link href={`/organizations/${orgId}/projects/${projectId}`} className="mt-4">
          <Button variant="outline">Back to Project</Button>
        </Link>
      </div>
    );
  }
  
  // Filter out calculations that are not beam analyses for this simplified view
  // You might want to handle different calculation types differently in a real app
  const beamAnalyses = folderData.calculations
    .filter(calc => calc.type === 'BEAM' && calc.beamAnalysisResults && calc.beamAnalysisResults.length > 0)
    .flatMap(calc => 
      calc.beamAnalysisResults!.map(br => {
        let unitSystemForCard: UnitSystem = UnitSystem.IMPERIAL; 
        if (br.originalUnitSystem) {
          if (typeof br.originalUnitSystem === 'string') {
            const enumKey = br.originalUnitSystem.toUpperCase() as keyof typeof UnitSystem;
            if (UnitSystem[enumKey]) {
              unitSystemForCard = UnitSystem[enumKey];
            }
          } else if (Object.values(UnitSystem).includes(br.originalUnitSystem as UnitSystem)) {
            unitSystemForCard = br.originalUnitSystem as UnitSystem;
          }
        }

        return {
          ...br,
          name: br.name ?? calc.name ?? 'Unnamed Beam Analysis',
          calculationId: calc.id,
          originalUnitSystem: br.originalUnitSystem, // Keep original value for processing later
          // Provide defaults for fields that BeamAnalysisResultCard might expect as required numbers
          length: br.length ?? 0, 
          modulusOfElasticity: br.modulusOfElasticity ?? 0,
          momentOfInertia: br.momentOfInertia ?? 0,
          // Ensure other potentially required fields by the card also have fallbacks if necessary
          // For example, if `dependentsCount` was required and number by card, you might do:
          // dependentsCount: br.dependentsCount ?? 0,
        };
      })
    ) // Don't cast to BeamAnalysisResultForCard[] here yet, let map handle it.
    // The final mapping for the card component will ensure the unitSystem prop is correctly typed.


  return (
    <div className="container mx-auto p-4 md:p-8">
      <Button onClick={() => router.push(`/organizations/${orgId}/projects/${projectId}`)} variant="outline" className="mb-6">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Project
      </Button>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        {isRenaming ? (
          <div className="flex gap-2 items-center flex-grow">
            <Input 
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              className="text-3xl font-bold flex-grow"
              onKeyDown={(e) => e.key === 'Enter' && handleRenameFolder()}
            />
            <Button onClick={handleRenameFolder} size="sm">Save</Button>
            <Button onClick={() => { setIsRenaming(false); setNewFolderName(folderData.name);}} variant="ghost" size="sm">Cancel</Button>
          </div>
        ) : (
          <div className="flex gap-2 items-center">
            <h1 className="text-3xl font-bold">{folderData.name}</h1>
            <Button onClick={() => setIsRenaming(true)} variant="ghost" size="icon" aria-label="Rename folder">
              <Edit3 className="h-5 w-5" />
            </Button>
          </div>
        )}
        <div className="flex gap-2 flex-shrink-0">
            <Button onClick={handleCreateBeamAnalysis} variant="default">
                <PlusCircle className="mr-2 h-4 w-4" /> Add Beam Analysis
            </Button>
            <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
                <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="icon" aria-label="Delete folder">
                        <Trash2 className="h-5 w-5" />
                    </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure you want to delete this folder?</AlertDialogTitle>
                    <AlertDialogDescription>
                        This action cannot be undone. This will delete the folder '<strong>{folderData.name}</strong>'.
                        Calculations inside this folder will NOT be deleted but will be moved out of the folder.
                        Ensure the folder is empty if your API enforces it for deletion.
                    </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDeleteFolder} className="bg-destructive hover:bg-destructive/90">
                        Delete Folder
                    </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
      </div>
      
      <CardDescription className="mb-6">
        This folder contains {folderData.calculations.length} calculation(s).
        {folderData.project ? ` Part of project: ${folderData.project.title}` : ''}
      </CardDescription>

      {beamAnalyses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {beamAnalyses.map((analysis) => {
            // Determine UnitSystem for the card here, ensuring it's always a valid enum value
            let cardUnitSystem: UnitSystem = UnitSystem.IMPERIAL;
            if (analysis.originalUnitSystem) {
              if (typeof analysis.originalUnitSystem === 'string') {
                const enumKey = analysis.originalUnitSystem.toUpperCase() as keyof typeof UnitSystem;
                if (UnitSystem[enumKey]) {
                  cardUnitSystem = UnitSystem[enumKey];
                }
              } else if (Object.values(UnitSystem).includes(analysis.originalUnitSystem as UnitSystem)) {
                cardUnitSystem = analysis.originalUnitSystem as UnitSystem;
              }
            }
            return (
              <BeamAnalysisResultCard
                key={analysis.id}
                analysisResult={analysis as BeamAnalysisResultForCard} 
                unitSystem={cardUnitSystem} 
                projectId={projectId}
                orgId={orgId}
              />
            );
          })}
        </div>
      ) : (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>No Beam Analyses</CardTitle>
          </CardHeader>
          <CardContent>
            <p>There are no beam analyses in this folder yet, or they could not be displayed.</p>
             <Button onClick={handleCreateBeamAnalysis} variant="default" className="mt-4">
                <PlusCircle className="mr-2 h-4 w-4" /> Add Beam Analysis to this Folder
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 