'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import Link from 'next/link'; // Keep Link for potential future use (e.g., linking to project details)
import { useParams } from 'next/navigation';
import { useOrganizations, Organization, Project } from '../../context/OrganizationsContext'; // Import Project as well
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea"; // For project description
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox"; // For selection
import { PlusCircle, MoreHorizontal, Trash2, Edit3, ArrowUpDown } from 'lucide-react'; // Added icons for actions
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // For action menu
import Spinner from '../../components/ui/Spinner'; // Adjusted import path

// Project interface is available via OrganizationsContext, no need to redefine if imported.

// Define a type for sortable project keys
type SortableProjectKeys = keyof Pick<Project, 'title' | 'status' | 'dueDate' | 'updatedAt'>;

export default function OrganizationDetailPage() {
  const params = useParams();
  const orgId = params.orgId as string;
  const { 
    getOrganizationById, 
    addProjectToOrganization, 
    deleteProjectsFromOrganization, 
    isLoading: isContextLoading, // Renaming to avoid conflict with local loading state
    isMutating, 
    fetchOrganizations // Get fetchOrganizations from context
  } = useOrganizations();

  // Local state for the organization being viewed
  const [organization, setOrganization] = useState<Organization | null | undefined>(undefined);

  // State for the create project dialog
  const [isProjectDialogOpen, setIsProjectDialogOpen] = useState(false);
  const [newProjectTitle, setNewProjectTitle] = useState("");
  const [newProjectDescription, setNewProjectDescription] = useState("");

  // State for project list interactions (search, sort, select)
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProjectIds, setSelectedProjectIds] = useState<Set<string>>(new Set());
  const [sortColumn, setSortColumn] = useState<SortableProjectKeys | null>('dueDate'); // Default sort by dueDate
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc'); // Default to ascending for dates

  useEffect(() => {
    // Fetch all organizations to ensure the list is up-to-date when page is focused
    // This will also update the specific organization via getOrganizationById if its data changed
    fetchOrganizations().then(() => {
        if (orgId) {
            const currentOrg = getOrganizationById(orgId);
            // Only update if there's a meaningful change to avoid infinite loops 
            // if objects are recreated without actual data change, or if it's the initial set.
            if (organization === undefined || JSON.stringify(currentOrg) !== JSON.stringify(organization)) {
                setOrganization(currentOrg);
            }
        }
    });
  }, [orgId, getOrganizationById, fetchOrganizations]); // Add fetchOrganizations to dependencies

  const handleCreateProject = async () => {
    if (!newProjectTitle.trim() || !orgId) return;
    const newProject = await addProjectToOrganization(orgId, {
      title: newProjectTitle,
      description: newProjectDescription || undefined,
      // status will be defaulted by API or context
    });
    if (newProject) {
      setNewProjectTitle("");
      setNewProjectDescription("");
      setIsProjectDialogOpen(false);
    }
  };

  // Placeholder action handlers
  const handleEditProject = (projectId: string) => {
    console.log("Edit project:", projectId, "for org:", orgId);
    alert("Edit project functionality not implemented yet.");
  };

  const handleDeleteSingleProject = async (projectId: string) => {
    if (orgId && window.confirm("Are you sure you want to delete this project?")) {
      await deleteProjectsFromOrganization(orgId, [projectId]);
      setSelectedProjectIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  const handleBulkDeleteProjects = async () => {
    if (!orgId || selectedProjectIds.size === 0) return;
    if (window.confirm(`Are you sure you want to delete ${selectedProjectIds.size} selected project(s)?`)) {
      await deleteProjectsFromOrganization(orgId, Array.from(selectedProjectIds));
      setSelectedProjectIds(new Set()); // Clear selection after deletion
    }
  };

  const handleSelectProject = (projectId: string) => {
    setSelectedProjectIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) newSet.delete(projectId);
      else newSet.add(projectId);
      return newSet;
    });
  };

  const handleSelectAllProjects = (isChecked: boolean) => {
    if (isChecked) {
      setSelectedProjectIds(new Set(filteredAndSortedProjects.map(p => p.id)));
    } else {
      setSelectedProjectIds(new Set());
    }
  };

  const handleSort = (column: SortableProjectKeys) => {
    if (sortColumn === column) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const filteredAndSortedProjects = useMemo(() => {
    if (!organization?.projects) return [];
    let projects = organization.projects.filter(p =>
      p.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (p.description && p.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    if (sortColumn) {
      projects.sort((a, b) => {
        const valA = a[sortColumn];
        const valB = b[sortColumn];
        let comparison = 0;
        if (valA === null || valA === undefined) comparison = -1;
        else if (valB === null || valB === undefined) comparison = 1;
        else if (typeof valA === 'string' && typeof valB === 'string') comparison = valA.localeCompare(valB);
        else if (typeof valA === 'number' && typeof valB === 'number') comparison = valA - valB;
        else comparison = String(valA).localeCompare(String(valB));
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    return projects;
  }, [organization?.projects, searchTerm, sortColumn, sortDirection]);

  if (isContextLoading && !organization) { // Initial loading from context and org is not yet resolved
    return (
      <div className="container mx-auto py-10 flex justify-center items-center h-[calc(100vh-200px)]">
        <Spinner loading={true} size={50} />
      </div>
    );
  }

  if (!organization) { // If, after initial context load, organization is still not found or is null
    return <div className="container mx-auto py-10"><p className="text-foreground">Organization not found.</p></div>;
  }

  return (
    <div className="container mx-auto py-10 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 text-foreground">{organization.name}</h1>
        {organization.address && <p className="text-foreground/80">Address: {organization.address}</p>}
        {organization.phone && <p className="text-foreground/80">Phone: {organization.phone}</p>}
      </div>

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-foreground">{organization.name} Projects</h2>
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm h-9 bg-input text-foreground border-border focus:ring-ring focus:border-ring placeholder:text-muted-foreground"
          />
          {selectedProjectIds.size > 0 && (
            <Button 
              variant="destructive" 
              onClick={handleBulkDeleteProjects} 
              size="sm" 
              disabled={isMutating}
            >
              {isMutating && selectedProjectIds.size > 0 ? (
                <Spinner loading={true} size={20} color="#FFFFFF" className="mr-2" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete ({selectedProjectIds.size})
            </Button>
          )}
          <Dialog open={isProjectDialogOpen} onOpenChange={setIsProjectDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <PlusCircle className="mr-2 h-4 w-4" /> Create Project
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Create New Project for {organization.name}</DialogTitle>
                <DialogDescription>
                  Fill in the details for the new project. Name is required.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="projectTitle" className="text-right">
                    Title
                  </Label>
                  <Input
                    id="projectTitle"
                    value={newProjectTitle}
                    onChange={(e) => setNewProjectTitle(e.target.value)}
                    className="col-span-3"
                    placeholder="Project Title"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="projectDescription" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="projectDescription"
                    value={newProjectDescription}
                    onChange={(e) => setNewProjectDescription(e.target.value)}
                    className="col-span-3"
                    placeholder="(Optional) Project Description"
                  />
                </div>
              </div>
              <DialogFooter>
                {isMutating ? (
                  <Spinner loading={true} size={30} />
                ) : (
                  <>
                    <Button type="button" variant="outline" onClick={() => setIsProjectDialogOpen(false)} disabled={isMutating}>
                      Cancel
                    </Button>
                    <Button type="submit" onClick={handleCreateProject} disabled={isMutating}>
                      Create Project
                    </Button>
                  </>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {filteredAndSortedProjects.length === 0 && !searchTerm ? (
        <p className="text-center text-foreground/70 py-8">This organization has no projects yet. Click &quot;Create Project&quot; to add one.</p>
      ) : (
        <div className="shadow border-b border-border sm:rounded-lg overflow-x-auto bg-card">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted">
              <tr>
                <th scope="col" className="px-6 py-3">
                  <Checkbox
                    checked={selectedProjectIds.size > 0 && selectedProjectIds.size === filteredAndSortedProjects.length}
                    onCheckedChange={(checked) => handleSelectAllProjects(checked as boolean)}
                    aria-label="Select all projects"
                    disabled={filteredAndSortedProjects.length === 0}
                  />
                </th>
                {(Object.keys(filteredAndSortedProjects[0] || { title: '', description: '', status: '', dueDate: '', updatedAt: '' }) as Array<keyof Project>)
                  .filter(key => ['title', 'description', 'status', 'dueDate', 'updatedAt'].includes(key))
                  .map((key) => {
                     const typedKey = key as SortableProjectKeys;
                     if (!['title', 'status', 'dueDate', 'updatedAt'].includes(typedKey) && key !== 'description') return null;

                     return (
                      <th key={key} scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        {key === 'description' ? (
                            <span>Description</span>
                        ) : (
                            <Button variant="ghost" onClick={() => handleSort(typedKey)} className="px-1 py-1 h-auto text-xs text-muted-foreground hover:text-foreground">
                            {key === 'dueDate' ? 'Due Date' : key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} {/* Prettify name, specific for Due Date */}
                            {sortColumn === typedKey && (sortDirection === 'asc' ? <ArrowUpDown className="ml-2 h-3 w-3 rotate-180" /> : <ArrowUpDown className="ml-2 h-3 w-3" />)}
                            </Button>
                        )}
                      </th>
                    );
                })}
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {filteredAndSortedProjects.map((project) => (
                <tr key={project.id} className={selectedProjectIds.has(project.id) ? 'bg-muted/50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Checkbox
                      checked={selectedProjectIds.has(project.id)}
                      onCheckedChange={() => handleSelectProject(project.id)}
                      aria-label={`Select project ${project.title}`}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Link href={`/organizations/${orgId}/projects/${project.id}`} className="text-sm font-medium text-primary hover:text-primary/80 hover:underline">
                      {project.title} 
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-normal max-w-xs"><div className="text-sm text-card-foreground/80 truncate" title={project.description}>{project.description || 'N/A'}</div></td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${project.status === 'Completed' || project.status === 'Done' ? 'bg-green-700 text-green-100' : project.status === 'In Progress' ? 'bg-yellow-600 text-yellow-100' : project.status === 'Blocked' ? 'bg-red-700 text-red-100' : 'bg-gray-600 text-gray-100'}`}>
                      {project.status || 'N/A'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-card-foreground/80">{project.dueDate ? new Date(project.dueDate).toLocaleDateString() : 'N/A'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-card-foreground/80">{project.updatedAt ? new Date(project.updatedAt).toLocaleDateString() : 'N/A'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild><Button variant="ghost" className="h-8 w-8 p-0"><MoreHorizontal className="h-4 w-4" /></Button></DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditProject(project.id)}><Edit3 className="mr-2 h-4 w-4" />Edit</DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteSingleProject(project.id)} 
                          disabled={isMutating} 
                          className="text-red-600 hover:text-red-700"
                        >
                          {isMutating && selectedProjectIds.has(project.id) ? (
                             <Spinner loading={true} size={16} color="#DC2626" className="mr-2"/> 
                          ) : (
                            <Trash2 className="mr-2 h-4 w-4" />
                          )}
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {filteredAndSortedProjects.length === 0 && searchTerm && (
             <p className="text-center text-foreground/70 py-8">No projects found matching your search criteria.</p>
          )}
        </div>
      )}
    </div>
  );
} 