import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

interface LumberProperties {
  size_classification: string;
  nominal_size_bxd: string;
  nominal_b: number;
  nominal_d: number;
  standard_dressed_size_bxd: string;
  standard_width: number;
  standard_depth: number;
  area_of_section_a_in2: number;
  Sxx: number;
  Ixx: number;
  Syy: number;
  Iyy: number;
  weight_25_lbspft3: number;
  weight_30_lbspft3: number;
  weight_35_lbspft3: number;
  weight_40_lbspft3: number;
  weight_45_lbspft3: number;
  weight_50_lbspft3: number;
  notes?: string;
  version: string;
}

interface LumberData {
  sizeClassifications: string[];
  properties: {
    [sizeClassification: string]: LumberProperties[];
  };
}

// --- In-memory cache for Lumber Properties ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}

const CACHE_DURATION_MS_LUMBER_PROPS = 60 * 60 * 1000; // 1 hour

let lumberPropertiesCache: Cache<LumberData> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Lumber Properties ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || 'NDS 2018'; // Default version
  
  // --- Cache check for Lumber Properties ---
  const now = Date.now();
  if (lumberPropertiesCache.data && lumberPropertiesCache.expires && lumberPropertiesCache.expires > now) {
    console.log("Serving lumber properties data from cache.");
    // Filter cached data by version
    const filteredData: LumberData = {
      sizeClassifications: [],
      properties: {},
    };
    
    Object.keys(lumberPropertiesCache.data.properties).forEach(sizeClass => {
      const versionFilteredProps = lumberPropertiesCache.data!.properties[sizeClass].filter(
        prop => prop.version === version
      );
      if (versionFilteredProps.length > 0) {
        filteredData.sizeClassifications.push(sizeClass);
        filteredData.properties[sizeClass] = versionFilteredProps;
      }
    });
    
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh lumber properties data.");
  // --- End cache check for Lumber Properties ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'PropertiesOfStandardDressedSawnLumber.csv');
    
    // Check if file exists before trying to read it
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: 'Lumber properties data file not found' },
        { 
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    const lumberData: LumberData = {
      sizeClassifications: [],
      properties: {},
    };

    const parseNumber = (value: string): number => {
      if (!value || value.trim() === '') return 0;
      return parseFloat(value.replace(/,/g, '')) || 0;
    };

    return new Promise<NextResponse>((resolve, reject) => {
      const rows: any[] = [];

      stream
        .pipe(csv())
        .on('data', (row) => rows.push(row))
        .on('end', () => {
          try {
            rows.forEach((row) => {
              const sizeClassification = row.size_classification?.trim();
              const rowVersion = row.Version?.trim();
              
              if (!sizeClassification) return;

              const properties: LumberProperties = {
                size_classification: sizeClassification,
                nominal_size_bxd: row.nominal_size_bxd,
                nominal_b: parseNumber(row.nominal_b),
                nominal_d: parseNumber(row.nominal_d),
                standard_dressed_size_bxd: row.standard_dressed_size_bxd,
                standard_width: parseNumber(row.standard_width),
                standard_depth: parseNumber(row.standard_depth),
                area_of_section_a_in2: parseNumber(row.area_of_section_a_in2),
                Sxx: parseNumber(row.Sxx),
                Ixx: parseNumber(row.Ixx),
                Syy: parseNumber(row.Syy),
                Iyy: parseNumber(row.Iyy),
                weight_25_lbspft3: parseNumber(row['25_lbspft3']),
                weight_30_lbspft3: parseNumber(row['30_lbspft3']),
                weight_35_lbspft3: parseNumber(row['35_lbspft3']),
                weight_40_lbspft3: parseNumber(row['40_lbspft3']),
                weight_45_lbspft3: parseNumber(row['45_lbspft3']),
                weight_50_lbspft3: parseNumber(row['50_lbspft3']),
                notes: row.Notes || undefined,
                version: rowVersion,
              };

              // Store all data in cache, but only include requested version in response
              if (!lumberData.sizeClassifications.includes(sizeClassification)) {
                lumberData.sizeClassifications.push(sizeClassification);
                lumberData.properties[sizeClassification] = [];
              }
              lumberData.properties[sizeClassification].push(properties);
            });

            lumberData.sizeClassifications.sort();
            // --- Cache update for Lumber Properties ---
            lumberPropertiesCache.data = lumberData;
            lumberPropertiesCache.expires = Date.now() + CACHE_DURATION_MS_LUMBER_PROPS;
            console.log("Lumber properties data cached.");
            // --- End cache update for Lumber Properties ---
            
            // Filter response data by requested version
            const filteredResponseData: LumberData = {
              sizeClassifications: [],
              properties: {},
            };
            
            Object.keys(lumberData.properties).forEach(sizeClass => {
              const versionFilteredProps = lumberData.properties[sizeClass].filter(
                prop => prop.version === version
              );
              if (versionFilteredProps.length > 0) {
                filteredResponseData.sizeClassifications.push(sizeClass);
                filteredResponseData.properties[sizeClass] = versionFilteredProps;
              }
            });
            
            resolve(NextResponse.json(filteredResponseData, {
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, max-age=3600',
              },
            }));
          } catch (error) {
            reject(NextResponse.json(
              { error: 'Failed to process lumber data', details: error instanceof Error ? error.message : 'Unknown error' },
              { 
                status: 500,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            ));
          }
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse lumber data', details: error.message },
            { 
              status: 500,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load lumber data', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}