import { NextResponse } from "next/server";
import { BeamData, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { performServerSideCalculations } from "@/lib/beam-analysis/server-calculations";

export const dynamic = "force-dynamic"; // Ensure the route is always dynamic

interface RequestBody {
  beamData: BeamData;
  unitSystem: UnitSystem;
  beamPropertiesState: BeamPropertiesState | null;
  designMethod?: 'ASD' | 'LRFD';
}

export async function POST(request: Request) {
  console.log("[API /api/beam-analysis] POST request received.");
  try {
    const body = (await request.json()) as RequestBody;
    const { beamData, unitSystem, beamPropertiesState, designMethod = 'ASD' } = body;

    if (!beamData || !unitSystem) {
      console.error("[API /api/beam-analysis] Missing required parameters: beamData or unitSystem.", body);
      return NextResponse.json(
        { error: "Missing required parameters: beamData and unitSystem." },
        { status: 400 }
      );
    }

    // Optional: Add more detailed logging of the input if needed for debugging
    // console.log("[API /api/beam-analysis] Processing request with:", JSON.stringify(body, null, 2));

    const analysisResults = await performServerSideCalculations(
      beamData,
      unitSystem,
      beamPropertiesState
    );

    if (analysisResults) {
      console.log("[API /api/beam-analysis] Analysis successful, returning results.");
      return NextResponse.json(analysisResults);
    } else {
      console.error(
        "[API /api/beam-analysis] Analysis performed but resulted in null output without specific error."
      );
      return NextResponse.json(
        { error: "Analysis failed to produce results. The server calculation returned null." },
        { status: 500 }
      );
    }
  } catch (error) {
    let errorMessage = "An unknown error occurred during beam analysis.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    // For SyntaxError when request.json() fails
    if (error instanceof SyntaxError) {
      errorMessage = `Invalid JSON in request body: ${errorMessage}`;
      console.error("[API /api/beam-analysis] SyntaxError processing request:", errorMessage, error);
      return NextResponse.json({ error: errorMessage }, { status: 400 }); // Bad request for JSON errors
    }
    
    console.error("[API /api/beam-analysis] Error during beam analysis:", errorMessage, error);
    return NextResponse.json({ error: `Server Error: ${errorMessage}` }, { status: 500 });
  }
}

// Removing the GET handler as it's not standard for this type of API endpoint
// and was only for testing the route's existence.
// export async function GET(request: Request) {
//   console.log("[API /api/beam-analysis] GET endpoint was hit! This is for testing only.");
//   return NextResponse.json({ message: "GET request received. Use POST for analysis." });
// }
