import { NextResponse } from 'next/server';
import { db } from '@/lib/db'; // Assuming your Prisma client is exported from here

export async function POST(request: Request) {
  try {
    const { ids } = await request.json(); // These are BeamAnalysisResult IDs

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({ error: 'Invalid input: Array of BeamAnalysisResult IDs is required.' }, { status: 400 });
    }

    // Find Calculation records where their result_id (BeamAnalysisResult ID) is in the provided list
    const calculationsToDelete = await db.calculation.findMany({
      where: {
        result_id: {
          in: ids,
        },
      },
      select: {
        id: true, // Select the IDs of these Calculation records
      },
    });

    const calculationIdsToDelete = calculationsToDelete.map((calc: { id: string }) => calc.id);

    // Start a transaction
    const transactionOperations = [];

    // 1. Delete BeamAnalysisResult records by their own IDs
    transactionOperations.push(
      db.beamAnalysisResult.deleteMany({
        where: {
          id: {
            in: ids,
          },
        },
      })
    );

    // 2. Delete associated Calculation records if any were found
    if (calculationIdsToDelete.length > 0) {
      transactionOperations.push(
        db.calculation.deleteMany({
          where: {
            id: {
              in: calculationIdsToDelete,
            },
          },
        })
      );
    }

    const transactionResults = await db.$transaction(transactionOperations);

    const deletedBeamAnalysesCount = transactionResults[0].count;
    // transactionResults[1]?.count would be the count for calculations if that transaction ran

    return NextResponse.json({ 
      message: `${deletedBeamAnalysesCount} beam analysis/analyses and associated calculations deleted successfully.`, 
      count: deletedBeamAnalysesCount 
    }, { status: 200 });

  } catch (e) { // Changed error variable to e to avoid conflict with error property name
    const error = e as any; // Cast to any to access properties
    console.error('Error in bulk-delete beam analyses:', error);
    if (error instanceof SyntaxError) {
        return NextResponse.json({ error: 'Invalid JSON payload.' }, { status: 400 });
    }
    // Log the specific Prisma error if available
    if (error.name === 'PrismaClientValidationError' || error.name === 'PrismaClientKnownRequestError') {
      console.error('Prisma Error:', JSON.stringify(error, null, 2));
      return NextResponse.json({ error: 'Database error during bulk deletion.', details: error.message }, { status: 500 });
    }
    return NextResponse.json({ error: 'An internal server error occurred.' }, { status: 500 });
  }
} 