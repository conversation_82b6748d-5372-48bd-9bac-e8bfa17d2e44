import { NextResponse } from "next/server";
import { db as prisma } from "@/lib/db"; // CORRECT: Use the shared Prisma client from lib/db
import { LoadGroup } from "@/lib/types/load/load-group"; // Assuming LoadGroup can be imported
// @ts-ignore: This is a valid import
import { Prisma, CalculationRelationshipType } from '@prisma/client'; // Standard import for Prisma enums and types

interface RouteParams {
  params: {
    id: string;
  };
}

export const dynamic = "force-dynamic";

export async function GET(request: Request, { params }: RouteParams) {
  const { id } = params;
  // console.log(`[API /api/beam-analysis/results/${id}] GET request received.`);

  if (!id) {
    return NextResponse.json({ error: "Missing analysis ID." }, { status: 400 });
  }

  try {
    const savedAnalysis = await prisma.beamAnalysisResult.findUnique({
      where: { id },
    });

    if (!savedAnalysis) {
      // console.warn(`[API /api/beam-analysis/results/${id}] Analysis with ID ${id} not found.`);
      return NextResponse.json({ error: "Analysis not found." }, { status: 404 });
    }

    // console.log(`[API /api/beam-analysis/results/${id}] Found analysis: ${savedAnalysis.name}`);
    return NextResponse.json(savedAnalysis);
  } catch (error) {
    let errorMessage = "An unknown error occurred while fetching the saved analysis.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    // console.error(`[API /api/beam-analysis/results/${id}] Error fetching analysis:`, errorMessage, error);
    return NextResponse.json({ error: `Server Error: ${errorMessage}` }, { status: 500 });
  }
}

export async function PUT(request: Request, { params }: RouteParams) {
  const { id: calculationId } = params; // param.id is actually the Calculation.id
  // console.log(`[API /api/beam-analysis/results/${calculationId}] PUT request received for Calculation ID.`);

  if (!calculationId) {
    return NextResponse.json({ error: "Missing Calculation ID for update." }, { status: 400 });
  }

  try {
    const body = await request.json();
    const {
      name: analysisNameFromBody, // Name of the analysis/calculation
      length,
      modulusOfElasticity,
      momentOfInertia,
      beamProperties, // Stringified BeamPropertiesState
      loads,          // Stringified LoadGroup[] - THIS IS KEY
      supports,       // Stringified Support[]
      beamData,       // Stringified BeamData - Complete beam data including designMethod and selectedLoadCombos
      results,        // Stringified SummaryData
      bendingStressRatio,
      shearStressRatio,
      maxDeflection,
    } = body;

    if (
      length === undefined ||
      modulusOfElasticity === undefined ||
      momentOfInertia === undefined ||
      beamProperties === undefined || 
      loads === undefined ||          
      supports === undefined ||       
      results === undefined          
    ) {
      return NextResponse.json(
        { error: "Missing required fields for update." },
        { status: 400 }
      );
    }

    const transactionResult = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      // 0. Fetch the Calculation record using the provided calculationId
      const existingCalculation = await tx.calculation.findUnique({
        where: { id: calculationId },
      });

      if (!existingCalculation) {
        throw new Error(`Calculation with ID ${calculationId} not found.`);
      }
      if (!existingCalculation.result_id) {
        throw new Error(`Calculation ${calculationId} does not have a linked BeamAnalysisResult (result_id is missing).`);
      }

      const beamAnalysisResultIdToUpdate = existingCalculation.result_id;

      // 1. Fetch existing BeamAnalysisResult using the result_id from the Calculation
      const existingBeamAnalysisResult = await tx.beamAnalysisResult.findUnique({
        where: { id: beamAnalysisResultIdToUpdate },
      });

      if (!existingBeamAnalysisResult) {
        // This would indicate an inconsistency, as calculation.result_id should point to a valid BAR
        throw new Error(`BeamAnalysisResult with ID ${beamAnalysisResultIdToUpdate} (linked from Calculation ${calculationId}) not found.`);
      }
      if (!existingBeamAnalysisResult.projectId) { // Should match existingCalculation.projectId
        throw new Error(`BeamAnalysisResult ${beamAnalysisResultIdToUpdate} is missing projectId.`);
      }
      // No need to check existingBeamAnalysisResult.name for finding the calculation anymore,
      // as we already have the existingCalculation.

      // 2. The associated Calculation record is already existingCalculation.
      const associatedCalculation = existingCalculation;
      const dependentCalculationId = associatedCalculation.id; // This is calculationId

      // 3. Update the Calculation record's name (if changed) and always update its updatedAt timestamp
      const calculationUpdateData: { name?: string; updatedAt: Date } = {
        updatedAt: new Date(),
      };
      if (associatedCalculation.name !== analysisNameFromBody) {
        calculationUpdateData.name = analysisNameFromBody;
      }
      await tx.calculation.update({
        where: { id: dependentCalculationId },
        data: calculationUpdateData,
      });
      
      // 4. Update the BeamAnalysisResult record
      const updatedBeamAnalysisResult = await tx.beamAnalysisResult.update({
        where: { id: beamAnalysisResultIdToUpdate }, // Use the actual ID of the BeamAnalysisResult
        data: {
          name: analysisNameFromBody, 
          length,
          modulusOfElasticity,
          momentOfInertia,
          beamProperties,
          loads, 
          supports,
          beamData, // Update the complete beam data
          results,
          bendingStressRatio,
          shearStressRatio,
          maxDeflection,
          updatedAt: new Date(),
        },
      });

      // 5. Delete existing CalculationRelationship records for this dependent calculation
      await tx.calculationRelationship.deleteMany({
        where: {
          calculation_id: dependentCalculationId,
        },
      });

      // 6. Parse LoadGroup[] and create new CalculationRelationship records
      let createdRelationships: any[] = [];
      if (loads) {
        const parsedLoadGroups: Partial<LoadGroup>[] = JSON.parse(loads);
        for (const lg of parsedLoadGroups) {
          if (lg.isLinkedLoad && lg.linkedSourceCalculationId) {
            const sourceCalcExists = await tx.calculation.findUnique({
                where: { id: lg.linkedSourceCalculationId }
            });
            if (!sourceCalcExists) {
                throw new Error(`Source calculation with ID ${lg.linkedSourceCalculationId} not found. Cannot create beam link.`);
            }
            if (sourceCalcExists.id === dependentCalculationId) {
              throw new Error(`Cannot link a beam calculation to itself. Source ID ${lg.linkedSourceCalculationId} and dependent ID ${dependentCalculationId} are the same.`);
            }

            const relationship = await tx.calculationRelationship.create({
              data: {
                source_calculation_id: lg.linkedSourceCalculationId,
                calculation_id: dependentCalculationId,
                type: CalculationRelationshipType.BEAM_LINK,
                source_support_position: lg.linkedSourceSupportPosition || 0,
                load_application_position: typeof lg.startPosition === 'string' ? parseFloat(lg.startPosition) : (lg.startPosition || 0),
                projectId: existingBeamAnalysisResult.projectId,
              },
            });
            createdRelationships.push(relationship);
          }
        }
      }
      return { updatedBeamAnalysisResult, createdRelationships };
    });

    return NextResponse.json(transactionResult.updatedBeamAnalysisResult, { status: 200 });

  } catch (error) {
    console.error(`Error updating calculation ${calculationId} and its beam analysis result:`, error);
    let errorMessage = "Internal Server Error";
    let statusCode = 500;
    if (error instanceof Error) {
      errorMessage = error.message;
      if (error.message.includes("not found") || error.message.includes("Cannot link a beam calculation to itself") || error.message.includes("name is missing")) {
        statusCode = 400; // Or 404 if a record isn't found
      }
    }
    return NextResponse.json(
      { error: `Failed to update beam analysis and/or relationships`, details: errorMessage }, 
      { status: statusCode }
    );
  }
}

export async function DELETE(request: Request, { params }: RouteParams) {
  const { id } = params;
  // console.log(`[API /api/beam-analysis/results/${id}] DELETE request received.`);

  if (!id) {
    return NextResponse.json({ error: "Missing analysis ID for deletion." }, { status: 400 });
  }

  try {
    // Check if the analysis exists before attempting to delete
    const existingAnalysis = await prisma.beamAnalysisResult.findUnique({
      where: { id },
    });

    if (!existingAnalysis) {
      // console.warn(`[API /api/beam-analysis/results/${id}] Analysis with ID ${id} not found for deletion.`);
      return NextResponse.json({ error: "Analysis not found for deletion." }, { status: 404 });
    }
    
    // Also delete the associated Calculation record and its relationships.
    // This assumes a 1-to-1 or identifying relationship that when BAR is deleted, its Calculation should also be.
    // And CalculationRelationships will cascade delete if schema is set up for it, or need manual deletion.

    await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
        // Ensure existingAnalysis.name is not null before using it to find the Calculation
        if (existingAnalysis.name === null || existingAnalysis.name === undefined || existingAnalysis.projectId === null || existingAnalysis.projectId === undefined) {
            console.warn(`BeamAnalysisResult ${id} has no name or projectId, cannot reliably find associated Calculation to delete. Deleting only BeamAnalysisResult.`);
        } else {
            // Both name and projectId are non-null here
            const currentAnalysisNameForDelete = existingAnalysis.name;
            const currentProjectIdForDelete = existingAnalysis.projectId;

            const associatedCalculation = await tx.calculation.findFirst({
                where: {
                    name: currentAnalysisNameForDelete, 
                    projectId: currentProjectIdForDelete,
                    type: "BEAM", 
                },
            });

            if (associatedCalculation) {
                await tx.calculation.delete({
                    where: { id: associatedCalculation.id },
                });
            }
        }
        // Finally, delete the BeamAnalysisResult itself
        await tx.beamAnalysisResult.delete({
            where: { id },
        });
    });

    // console.log(`[API /api/beam-analysis/results/${id}] Successfully deleted analysis with ID ${id}.`);
    return NextResponse.json({ message: "Analysis and associated calculation data deleted successfully." }, { status: 200 });
  } catch (error) {
    let errorMessage = "An unknown error occurred while deleting the analysis.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    // console.error(`[API /api/beam-analysis/results/${id}] Error deleting analysis:`, errorMessage, error);
    return NextResponse.json({ error: `Server Error: ${errorMessage}` }, { status: 500 });
  }
} 