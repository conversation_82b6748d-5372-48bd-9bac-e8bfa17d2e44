import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient(); // Initialize Prisma Client once

export const dynamic = "force-dynamic"; // Ensure fresh data on every request

export async function GET() {
  console.log("[API /api/beam-analysis/results/list] GET request received.");
  try {
    console.log("[API /api/beam-analysis/results/list] Attempting to fetch analyses from DB...");
    const savedAnalyses = await prisma.beamAnalysisResult.findMany({
      select: {
        id: true,
        name: true,
        length: true,
        modulusOfElasticity: true,
        momentOfInertia: true,
        beamProperties: true, // Stringified BeamPropertiesState
        loads: true,          // Stringified LoadGroup[]
        supports: true,       // Stringified Support[]
        results: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(`[API /api/beam-analysis/results/list] Successfully fetched ${savedAnalyses.length} analyses from DB.`);
    // Log the actual data carefully, it might be large
    if (savedAnalyses.length > 0) {
      console.log("[API /api/beam-analysis/results/list] First fetched analysis (sample):", JSON.stringify(savedAnalyses[0], null, 2));
    }
    
    console.log("[API /api/beam-analysis/results/list] Attempting to return JSON response...");
    const response = NextResponse.json(savedAnalyses);
    console.log("[API /api/beam-analysis/results/list] Successfully created JSON response.");
    return response;
  } catch (error) {
    let errorMessage = "An unknown error occurred while fetching saved analyses.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    console.error("[API /api/beam-analysis/results/list] Error fetching saved analyses:", errorMessage, error);
    return NextResponse.json({ error: `Server Error: ${errorMessage}` }, { status: 500 });
  } finally {
    // Generally, you don't need to explicitly disconnect after every query in a serverless function context.
    // Prisma Client is designed to manage connections efficiently.
    // However, if you have specific reasons or encounter connection pooling issues, you might include it.
    // For Next.js API routes, it's often omitted to let Prisma manage the connection lifecycle.
    // await prisma.$disconnect(); 
  }
} 