import { NextResponse } from "next/server";
import { db as prisma } from "@/lib/db";
import { LoadGroup } from "@/lib/types/load/load-group"; // Assuming LoadGroup can be imported
// import { CalculationRelationshipType, Prisma } from "@prisma/client"; // Temporarily removed due to linter issues

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      name, // Name of the analysis/calculation
      length,
      modulusOfElasticity,
      momentOfInertia,
      beamProperties, // Stringified BeamPropertiesState
      loads,          // Stringified LoadGroup[] - THIS IS KEY
      supports,       // Stringified Support[]
      beamData,       // Stringified BeamData - Complete beam data including designMethod and selectedLoadCombos
      results,        // Stringified SummaryData
      bendingStressRatio,
      shearStressRatio,
      maxDeflection,
      projectId,      // ID of the project this belongs to
      organizationId,
    } = body;

    console.log("[API /api/beam-analysis/results] Received projectId:", projectId);
    console.log("[API /api/beam-analysis/results] Received organizationId:", organizationId);
    
    if (
      length === undefined ||
      modulusOfElasticity === undefined ||
      momentOfInertia === undefined ||
      beamProperties === undefined ||
      loads === undefined ||
      supports === undefined ||
      results === undefined ||
      !projectId // Project ID is crucial for linking
    ) {
      return NextResponse.json(
        { error: "Missing required beam properties, loads, supports, results data, or projectId." },
        { status: 400 }
      );
    }

    const analysisName = name || "Unnamed Beam Analysis";

    // Verify that the project exists before attempting to create the calculation
    const projectExists = await prisma.project.findUnique({
      where: { id: projectId }
    });
    
    if (!projectExists) {
      console.error(`[API /api/beam-analysis/results] Project with ID ${projectId} does not exist`);
      console.log("[API /api/beam-analysis/results] Attempting to save without project association...");
      
      // Try to save without project association as fallback
      try {
        const fallbackResult = await prisma.beamAnalysisResult.create({
          data: {
            name: analysisName,
            length,
            modulusOfElasticity,
            momentOfInertia,
            beamProperties,
            loads,
            supports,
            beamData,
            results,
            bendingStressRatio,
            shearStressRatio,
            maxDeflection,
            // No projectId or organizationId
          },
        });
        
        return NextResponse.json(
          {
            message: "Beam analysis saved successfully (without project association due to invalid project ID)",
            calculationId: null, // No calculation record created
            beamAnalysisResult: fallbackResult,
            warning: `Project with ID ${projectId} does not exist. Analysis saved without project association.`
          },
          { status: 201 }
        );
      } catch (fallbackError) {
        console.error("[API /api/beam-analysis/results] Fallback save also failed:", fallbackError);
        return NextResponse.json(
          { error: `Project with ID ${projectId} does not exist and fallback save failed. Please check the project ID or create the project first.` },
          { status: 400 }
        );
      }
    }
    
    console.log("[API /api/beam-analysis/results] Project exists:", projectExists.title);

    // Use a Prisma transaction to ensure all related records are created or none are.
    const transactionResult = await prisma.$transaction(async (tx: any) => { // tx temporarily as any
      // 1. Create the main Calculation record for this analysis
      const newCalculation = await tx.calculation.create({
        data: {
          name: analysisName,
          type: "BEAM", // Or "BEAM_ANALYSIS", ensure consistency
          projectId: projectId,
          // 'order' can be handled later if needed, e.g., by a separate function or default
          order: 0, // Placeholder for order
        },
      });
      const dependentCalculationId = newCalculation.id;

      // 2. Create the BeamAnalysisResult record
      const dataToCreateBeamResult: any = {
        name: analysisName, // Ensure BAR name matches Calculation name for current linking strategy
        length,
        modulusOfElasticity,
        momentOfInertia,
        beamProperties,
        loads, // Save the original stringified loads
        supports,
        beamData, // Save the complete beam data
        results,
        bendingStressRatio,
        shearStressRatio,
        maxDeflection,
        projectId, // Link BAR to project
        // We could add a direct link from BeamAnalysisResult to Calculation if the schema changes:
        // calculationId: dependentCalculationId,
      };
      if (organizationId) {
        dataToCreateBeamResult.organizationId = organizationId;
      }

      const newBeamAnalysisResult = await tx.beamAnalysisResult.create({
        data: dataToCreateBeamResult,
      });

      // 3. Update the Calculation record with the BeamAnalysisResult ID
      const updatedCalculationWithResultLink = await tx.calculation.update({
        where: { id: newCalculation.id },
        data: { result_id: newBeamAnalysisResult.id }, // Use 'result_id' as per schema
      });

      // 4. Parse LoadGroup[] and create CalculationRelationship records
      let createdRelationships: any[] = [];
      if (loads) {
        // Ensure LoadGroup type is compatible; properties like isLinkedLoad might be optional.
        const parsedLoadGroups: Partial<LoadGroup>[] = JSON.parse(loads);
        for (const lg of parsedLoadGroups) {
          if (lg.isLinkedLoad && 
              lg.linkedSourceCalculationId && 
              typeof lg.linkedSourceSupportPosition === 'number' && // Check for the new field
              lg.startPosition !== undefined // Position on the current (dependent) beam
            ) {
            const sourceCalcExists = await tx.calculation.findUnique({
              where: { id: lg.linkedSourceCalculationId }
            });
            if (!sourceCalcExists) {
              throw new Error(`Source calculation with ID ${lg.linkedSourceCalculationId} not found. Cannot create beam link.`);
            }
            if (sourceCalcExists.id === dependentCalculationId) {
              throw new Error(`Cannot link a beam calculation to itself. Source and dependent ID ${dependentCalculationId} are the same.`);
            }

            const loadApplicationPos = typeof lg.startPosition === 'number' 
                                          ? lg.startPosition 
                                          : parseFloat(lg.startPosition as string); // Fallback if startPosition is string
            
            if (isNaN(loadApplicationPos)) {
                throw new Error(`Invalid startPosition for linked load on dependent calculation ${dependentCalculationId}.`);
            }

            const relationship = await tx.calculationRelationship.create({ 
              data: {
                source_calculation_id: lg.linkedSourceCalculationId,
                calculation_id: dependentCalculationId, 
                type: "BEAM_LINK", // Using string literal temporarily
                source_support_position: lg.linkedSourceSupportPosition,
                load_application_position: loadApplicationPos,
                projectId: projectId, 
              },
            });
            createdRelationships.push(relationship);
          } else if (lg.isLinkedLoad) {
            // Log a warning if it's a linked load but missing necessary fields
            console.warn(`Skipping relationship creation for a linked load on calculation ${dependentCalculationId} due to missing fields: linkedSourceCalculationId: ${lg.linkedSourceCalculationId}, linkedSourceSupportPosition: ${lg.linkedSourceSupportPosition}, startPosition: ${lg.startPosition}`);
          }
        }
      }
      // Return the Calculation object, which now includes the result_id implicitly, and the result itself
      return { calculation: updatedCalculationWithResultLink, beamAnalysisResult: newBeamAnalysisResult, createdRelationships };
    });

    // The frontend will likely need the Calculation ID for the URL, and perhaps the result details.
    return NextResponse.json(
      {
        message: "Beam analysis saved successfully",
        calculationId: transactionResult.calculation.id,
        beamAnalysisResult: transactionResult.beamAnalysisResult,
        // Optionally include createdRelationships if the client needs them immediately
        // relationships: transactionResult.createdRelationships 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error("Error saving beam analysis result and relationships:", error);
    let errorMessage = "Internal Server Error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      // Check for specific error messages that might warrant a 4xx status
      if (error.message.includes("Source calculation with ID") || error.message.includes("Cannot link a beam calculation to itself")) {
        statusCode = 400; // Bad request due to invalid link
      }
    }
    // Add more specific Prisma error code handling if necessary
    // For instance, if (error.code === 'P2002') for unique constraint violations.

    return NextResponse.json(
      { error: "Failed to save beam analysis and/or relationships", details: errorMessage },
      { status: statusCode }
    );
  }
} 