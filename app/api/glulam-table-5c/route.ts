import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';
import {
  DEFAULT_NDS_VERSION,
  ERROR_MESSAGES,
  HTTP_STATUS,
  CACHE_DURATION_MS,
} from '@/lib/constants/nds-constants';

interface Table5CRow {
  combination_symbol: string;
  F_bx_pos_psi: number;
  F_bx_neg_psi: number;
  F_C_perp_x_psi: number; // Compression Perpendicular to Grain (x-axis)
  F_vx_psi: number;
  E_x_ksi: number;
  E_x_min_ksi: number;
  F_by_psi: number;
  F_C_perp_y_psi: number; // Compression Perpendicular to Grain (y-axis)
  F_v_y_psi: number;    // Shear (y-axis)
  E_y_ksi: number;
  E_y_min_ksi: number;
  F_t_psi: number;
  F_c_psi: number;
  G: number; // Specific Gravity
  table: string;
  version: string;
  wet_service_factor_Cm_for_Fb: number;
  wet_service_factor_Cm_for_Ft: number;
  wet_service_factor_Cm_for_Fv: number;
  wet_service_factor_Cm_for_Fc_perp: number; // For 'wet_service_factor_Cm_for_Fc⊥'
  wet_service_factor_Cm_for_Fc: number;
  wet_service_factor_Cm_for_E_and_Emin: number;
  Notes?: string;
}

// --- In-memory cache for Table5C ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}



let table5cCache: Cache<Table5CRow[]> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Table5C ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || DEFAULT_NDS_VERSION;
  // --- Cache check for Table5C ---
  const now = Date.now();
  if (table5cCache.data && table5cCache.expires && table5cCache.expires > now) {
    console.log("Serving Table5C data from cache.");
    // Filter by version before returning cached data
    const filteredData = table5cCache.data.filter(row => row.version === version);
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh Table5C data.");
  // --- End cache check for Table5C ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'glulam', 'Table5C.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      console.error('File access error for Table5C.csv:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.WOOD_DATA_NOT_FOUND },
        { 
          status: HTTP_STATUS.NOT_FOUND,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);
    const results: Table5CRow[] = [];

    const numericKeys: Array<keyof Table5CRow> = [
      'F_bx_pos_psi', 'F_bx_neg_psi', 'F_C_perp_x_psi', 'F_vx_psi',
      'E_x_ksi', 'E_x_min_ksi', 'F_by_psi', 'F_C_perp_y_psi', 'F_v_y_psi',
      'E_y_ksi', 'E_y_min_ksi', 'F_t_psi', 'F_c_psi', 'G',
      'wet_service_factor_Cm_for_Fb', 'wet_service_factor_Cm_for_Ft',
      'wet_service_factor_Cm_for_Fv', 'wet_service_factor_Cm_for_Fc_perp',
      'wet_service_factor_Cm_for_Fc', 'wet_service_factor_Cm_for_E_and_Emin'
    ];
    const optionalStringKeys: Array<keyof Table5CRow> = ['Notes'];

    return new Promise<NextResponse>((resolve, reject) => {
      stream
        .pipe(csv({
          mapHeaders: ({ header }) => {
            let normalizedHeader = header.trim().replace(/\s+/g, '_');
            // Specific normalizations for Table5C columns if needed, beyond generic ones
            normalizedHeader = normalizedHeader.replace('wet_service_factor_Cm_for_Fc⊥', 'wet_service_factor_Cm_for_Fc_perp');
            // F_C_perp_x_psi and F_C_perp_y_psi should be handled by the generic replace if spaces are the only issue.
            // If exact casing or other characters differ, add specific replaces here.
            return normalizedHeader;
          }
        }))
        .on('data', (dataFromCsv) => {
          const processedRow = {} as Table5CRow;
          for (const keyFromCsv in dataFromCsv) {
            const key = keyFromCsv as keyof Table5CRow; 
            const rawValue = dataFromCsv[keyFromCsv];
            const valueString = String(rawValue || '').trim();

            if (numericKeys.includes(key)) {
              if (valueString === '' || valueString.toLowerCase() === 'na' || valueString.toLowerCase() === 'n/a') {
                (processedRow as any)[key] = NaN; 
              } else {
                (processedRow as any)[key] = parseFloat(valueString.replace(',', ''));
              }
            } else if (optionalStringKeys.includes(key)) {
              if (valueString === '' || valueString.toLowerCase() === 'na' || valueString.toLowerCase() === 'n/a') {
                (processedRow as any)[key] = undefined;
              } else {
                (processedRow as any)[key] = valueString;
              }
            } else { 
              (processedRow as any)[key] = valueString;
            }
          }
          results.push(processedRow);
        })
        .on('end', () => {
          // --- Cache update for Table5C ---
          table5cCache.data = results;
          table5cCache.expires = Date.now() + CACHE_DURATION_MS.WOOD_DATA;
          console.log("Table5C data cached.");
          // --- End cache update for Table5C ---
          // Filter by version before returning
          const filteredResults = results.filter(row => row.version === version);
          resolve(NextResponse.json(filteredResults, {
            headers: { 
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=3600',
            },
          }));
        })
        .on('error', (error) => {
          console.error('CSV parsing error for Table5C.csv:', error);
          reject(NextResponse.json(
            { error: ERROR_MESSAGES.WOOD_DATA_PARSE_FAILED, details: error.message },
            { 
              status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
              headers: { 'Content-Type': 'application/json' },
            }
          ));
        });
    });
  } catch (error) {
    console.error('Overall GET request error for Table5C:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json(
      { error: ERROR_MESSAGES.WOOD_DATA_LOAD_FAILED, details: errorMessage },
      { 
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
} 