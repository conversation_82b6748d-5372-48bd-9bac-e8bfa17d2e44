import { NextResponse } from 'next/server';
import { db as prisma } from '@/lib/db';
// We might not need explicit types from @prisma/client here if we type the results of queries directly

// Helper function to recursively fetch dependency graph data
// This should ideally be in a shared utility file if used by multiple routes
// For now, duplicating the core logic here for simplicity, but refactor later.
async function fetchDependencyGraphData(startCalculationId: string): Promise<{ nodes: Array<{ id: string }>, edges: Array<{ source: string, target: string }> }> {
  const nodes: Array<{ id: string }> = [];
  const edges: Array<{ source: string, target: string }> = [];
  const queue: string[] = [startCalculationId];
  const visited: Set<string> = new Set();
  const nodeMap: Set<string> = new Set(); // Use Set for nodeMap to just track existence by ID

  console.log(`[fetchDependencyGraphData] Starting with ID: ${startCalculationId}`);

  while (queue.length > 0) {
    const currentId = queue.shift()!;
    console.log(`[fetchDependencyGraphData] Processing currentId from queue: ${currentId}`);

    if (visited.has(currentId)) {
      console.log(`[fetchDependencyGraphData] Already visited ${currentId}, skipping.`);
      continue;
    }
    visited.add(currentId);

    const currentCalc = await prisma.calculation.findUnique({
      where: { id: currentId },
      select: { id: true }, // Only need ID for graph structure here
    });

    if (currentCalc) {
      if (!nodeMap.has(currentCalc.id)) {
        nodes.push({ id: currentCalc.id });
        nodeMap.add(currentCalc.id);
        console.log(`[fetchDependencyGraphData] Added node: ${currentCalc.id}`);
      }

      const relationships = await prisma.calculationRelationship.findMany({
        where: { source_calculation_id: currentId },
        include: {
          dependentCalculation: {
            select: { id: true },
          },
        },
      });

      console.log(`[fetchDependencyGraphData] Found ${relationships.length} relationships where ${currentId} is source:`, JSON.stringify(relationships, null, 2));

      for (const rel of relationships) {
        if (rel.dependentCalculation) {
          const dependentCalc = rel.dependentCalculation;
          if (!nodeMap.has(dependentCalc.id)) {
            nodes.push({ id: dependentCalc.id });
            nodeMap.add(dependentCalc.id);
            console.log(`[fetchDependencyGraphData] Added node: ${dependentCalc.id}`);
          }
          edges.push({
            source: currentId,
            target: dependentCalc.id,
          });
          console.log(`[fetchDependencyGraphData] Added edge: ${currentId} -> ${dependentCalc.id}`);
          if (!visited.has(dependentCalc.id)) {
            queue.push(dependentCalc.id);
            console.log(`[fetchDependencyGraphData] Added to queue: ${dependentCalc.id}`);
          }
        }
      }
    }
  }
  return { nodes, edges };
}

export async function GET(
  request: Request,
  { params }: { params: { calculationId: string } }
) {
  const { calculationId: rawCalculationId } = params;
  console.log(`[API /analysis-results] Received raw calculationId: '${rawCalculationId}'`);
  
  const calculationId = rawCalculationId ? rawCalculationId.trim() : undefined;
  console.log(`[API /analysis-results] Trimmed calculationId: '${calculationId}'`);

  // Ensure you have your DATABASE_URL in .env and it's being picked up
  console.log(`[API /analysis-results] DATABASE_URL from env (check if Accelerate is active): ${process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, process.env.DATABASE_URL.indexOf('@') > 0 ? process.env.DATABASE_URL.indexOf('@') : process.env.DATABASE_URL.length) : 'NOT SET'}`);

  if (!calculationId) {
    console.error('[API /analysis-results] Error: Calculation ID is required (either null, undefined, or empty after trim)');
    return NextResponse.json(
      { error: 'Calculation ID is required' },
      { 
        status: 400,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );
  }

  try {
    console.log(`[API /analysis-results] Attempting to fetch Calculation with ID (trimmed): '${calculationId}'`);
    const calculationWithResult = await prisma.calculation.findUnique({
      where: { id: calculationId }, // Use the trimmed ID
      include: {
        beamAnalysisResult: true,
      }
    }); 

    console.log(`[API /analysis-results] Prisma findUnique result for Calculation ID ${calculationId} (with included BAR):`, calculationWithResult);

    if (!calculationWithResult) {
      console.error(`[API /analysis-results] Error: Calculation with ID ${calculationId} not found in database.`);
      return NextResponse.json(
        { error: `Calculation with ID ${calculationId} not found.` },
        { 
          status: 404,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        }
      );
    }

    if (!calculationWithResult.beamAnalysisResult) {
      console.error(`[API /analysis-results] Error: No BeamAnalysisResult linked to calculation ID ${calculationId}.`);
      return NextResponse.json(
        { error: `No analysis result linked to calculation ID ${calculationId}. Calculation name: ${calculationWithResult.name}` },
        { 
          status: 404,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        }
      );
    }
    
    const beamResult = calculationWithResult.beamAnalysisResult;
    // const calculation = calculationWithResult; // calculation is now calculationWithResult

    console.log(`[API /analysis-results] BeamAnalysisResult found. Name: ${beamResult.name}`);
    console.log(`[API /analysis-results] Raw loads string from DB for ${beamResult.id}:`, beamResult.loads);

    // Fetch full dependency graph to get an accurate count of all related nodes
    const graphData = await fetchDependencyGraphData(calculationId);
    // The count for the button should be the number of *other* nodes in the graph
    const dependentsCount = graphData.nodes.length > 0 ? graphData.nodes.length - 1 : 0;

    console.log(`[API /analysis-results] Fetched graph-based dependentsCount for calculationId ${calculationId}: ${dependentsCount}`);

    // Add dependentsCount to the result object
    const resultWithDependentsCount = {
      ...beamResult,
      dependentsCount: dependentsCount,
      id: calculationWithResult.id, // Ensure this is the Calculation ID
      name: calculationWithResult.name, // Ensure name from Calculation is used
      projectId: calculationWithResult.projectId,
      organizationId: (calculationWithResult as any).organizationId, // Keep existing access pattern if it worked
    };

    console.log('[API /analysis-results] Successfully fetched BeamAnalysisResult and dependentsCount. Returning to client:', resultWithDependentsCount);
    return NextResponse.json(resultWithDependentsCount, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error(`[API /analysis-results] CATCH BLOCK: Error fetching analysis results for calculation ${calculationId}:`, error);
    if ((error as any).code === 'P2023' && (error as any).message?.includes('Malformed ObjectID')) { 
      return NextResponse.json(
        { error: `Invalid Calculation ID or Result ID format.` },
        { 
          status: 400,
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        }
      );
    }
    return NextResponse.json(
      { error: 'Internal Server Error fetching analysis results' },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );
  }
} 