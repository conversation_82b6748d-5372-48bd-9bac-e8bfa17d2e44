import { NextResponse } from "next/server";
import { db } from "@/lib/db";

interface PatchBody {
  folderId: string | null;
  name?: string; // Allow renaming as part of PATCH
  // Add other fields that can be patched later
}

export async function PATCH(
  request: Request,
  { params }: { params: { calculationId: string } }
) {
  try {
    const { calculationId } = params;
    if (!calculationId) {
      return NextResponse.json(
        { error: "Calculation ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json() as PatchBody;
    const { folderId, name } = body;

    // Validate that at least one updatable field is provided
    if (folderId === undefined && name === undefined) {
      return NextResponse.json(
        { error: "No updateable fields provided (folderId or name)" },
        { status: 400 }
      );
    }

    const calculation = await db.calculation.findUnique({
      where: { id: calculationId },
      select: { projectId: true }, // Select projectId for folder validation
    });

    if (!calculation) {
      return NextResponse.json(
        { error: "Calculation not found" },
        { status: 404 }
      );
    }

    const dataToUpdate: { folderId?: string | null; name?: string } = {};

    if (name !== undefined) {
        if (typeof name !== 'string' || name.trim() === '') {
            return NextResponse.json({ error: "Name must be a non-empty string" }, { status: 400 });
        }
        dataToUpdate.name = name;
    }

    if (folderId !== undefined) { // folderId can be null (to remove from folder) or a string
      if (folderId !== null) {
        // If folderId is provided (and not null), validate it
        const folder = await db.folder.findFirst({
          where: {
            id: folderId,
            projectId: calculation.projectId, // Ensure folder is in the same project
          },
        });
        if (!folder) {
          return NextResponse.json(
            { error: "Folder not found in the calculation\'s project" },
            { status: 404 }
          );
        }
      }
      dataToUpdate.folderId = folderId;
    }

    const updatedCalculation = await db.calculation.update({
      where: { id: calculationId },
      data: dataToUpdate,
      include: {
        beamAnalysisResult: true, // Include related data if typically needed by the frontend
        project: { select: { organizationId: true } }, // For context if needed
        folder: true, // Include folder info
      },
    });

    return NextResponse.json(updatedCalculation, { status: 200 });
  } catch (error) {
    console.error("Failed to update calculation:", error);
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }
    if ((error as any)?.code?.startsWith("P20") || (error as any).name === "PrismaClientValidationError") {
      return NextResponse.json(
        { error: "Database validation error.", details: (error as any).message },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// Placeholder for GET handler if you want to fetch a single calculation by ID
// export async function GET(
//   request: Request,
//   { params }: { params: { calculationId: string } }
// ) {
//   // ... implementation ...
// }

// Placeholder for DELETE handler
// export async function DELETE(
//   request: Request,
//   { params }: { params: { calculationId: string } }
// ) {
//   // ... implementation ...
// } 