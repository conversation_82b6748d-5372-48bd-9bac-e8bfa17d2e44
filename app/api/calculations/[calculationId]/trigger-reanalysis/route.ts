import { NextResponse, NextRequest } from 'next/server';
import { db as prisma } from '@/lib/db';
import { auth } from '@clerk/nextjs/server';
import { performServerSideCalculations } from '@/lib/beam-analysis/server-calculations';

// Define a minimal interface for the structure we expect from Prisma's BeamAnalysisResult
interface BeamAnalysisResultFromPrisma {
  id: string;
  name: string | null;
  length: number;
  modulusOfElasticity: number;
  momentOfInertia: number;
  beamProperties: string | null;
  loads: string | null;
  supports: string | null;
  results: string | null;
  beamData?: string | null; // Add beamData field to store complete BeamData
  // Add other fields if used from originalBeamAnalysisResult elsewhere in this file
}

import type { BeamPropertiesState, Support, BeamData } from '@/lib/types/beam/beam-data';
import { LoadGroup as ActualLoadGroupType } from "@/lib/types/load/load-group";
import type { AnalysisOutput, SummaryData } from '@/lib/types/analysis/analysis-output';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { Load } from '@/lib/types/load/load'; 
import { LoadType, Type as LoadApplicationType } from '@/lib/types/load/load-type';

// Helper to parse JSON safely
function safeJsonParse<T>(jsonString: string | null | undefined, defaultValue: T): T {
  if (jsonString === null || jsonString === undefined) return defaultValue;
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.warn("Failed to parse JSON string:", jsonString, error);
    return defaultValue;
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { calculationId: string } }
) {
  try {
    //const { userId } = await auth(); // Added await here
    // if (!userId) { // UserID check commented out as per request
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const { calculationId } = params;
    if (!calculationId) {
      return NextResponse.json({ error: 'Calculation ID is required' }, { status: 400 });
    }

    const calculation = await prisma.calculation.findUnique({
      where: { id: calculationId },
      include: {
        project: { select: { organizationId: true, id: true } },
        beamAnalysisResult: true, 
        dependentOnRelationships: {
          include: {
            sourceCalculation: { include: { beamAnalysisResult: true } }
          }
        }
      },
    });

    if (!calculation) return NextResponse.json({ error: 'Calculation not found' }, { status: 404 });
    if (!calculation.project) return NextResponse.json({ error: 'Project information missing' }, { status: 500 });
    if (!calculation.beamAnalysisResult) return NextResponse.json({ error: 'Associated BeamAnalysisResult not found' }, { status: 404 });
    
    // Use the locally defined interface
    const originalBeamAnalysisResult: BeamAnalysisResultFromPrisma = calculation.beamAnalysisResult;

    // const userOrg = await prisma.userOrganization.findFirst({ // Authorization check removed as per request
    //   where: { userId, organizationId: calculation.project.organizationId },
    // });
    // if (!userOrg) return NextResponse.json({ error: 'Forbidden' }, { status: 403 });

    const beamPropertiesState = safeJsonParse<BeamPropertiesState | null>(originalBeamAnalysisResult.beamProperties, null);
    let loadGroups = safeJsonParse<ActualLoadGroupType[]>(originalBeamAnalysisResult.loads, []);
    const supports = safeJsonParse<Support[]>(originalBeamAnalysisResult.supports, []);
    
    // Try to load complete beam data first (new format)
    let savedBeamData = safeJsonParse<BeamData | null>(originalBeamAnalysisResult.beamData, null);
    
    if (!beamPropertiesState) return NextResponse.json({ error: 'Failed to parse beam properties state.' }, { status: 500 });

    const unitSystem: UnitSystem = UnitSystem.IMPERIAL; 
    // Filter out ALL pre-existing linked loads. We will re-create them based on current parent data.
    loadGroups = loadGroups.filter(lg => !lg.isLinkedLoad);

    let lastCreatedLinkedLoadGroupId: string | null = null;

    // Attempt to rebuild linked loads from existing relationships
    for (const relationship of calculation.dependentOnRelationships) {
      if (relationship.sourceCalculation?.beamAnalysisResult) {
        const sourceResult = relationship.sourceCalculation.beamAnalysisResult as BeamAnalysisResultFromPrisma;
        const rawSourceCalcName = relationship.sourceCalculation.name;
        const derivedSourceCalculationName = rawSourceCalcName || `Cal ID ${relationship.source_calculation_id.substring(0,8)}`;
        const generatedLinkedLoadLabel = `\"${derivedSourceCalculationName}\"`; // Keep original quotes for label

        console.log(`[Trigger Reanalysis] Processing relationship for source ID: ${relationship.source_calculation_id} (Name: ${derivedSourceCalculationName})`);
        
        const sourceSummaryData = safeJsonParse<SummaryData | null>(sourceResult.results, null);

        if (typeof relationship.source_support_position !== 'number' || 
            typeof relationship.load_application_position !== 'number') {
          console.warn(`[Trigger Reanalysis] Skipping relationship from ${relationship.source_calculation_id} due to missing position data.`);
          continue;
        }
        
        // If the source calculation itself has no results (e.g. it was also just cleared or failed previously)
        // then there are no reactions to link from it.
        if (!sourceSummaryData || !sourceSummaryData.supportReactions?.forces) {
            console.warn(`[Trigger Reanalysis] Source calc ${relationship.source_calculation_id} (Name: ${derivedSourceCalculationName}) has no valid supportReactions data in its results. Cannot link load.`);
            continue; // Skip to the next relationship
        }

        // Ensure this block is robust if sourceSummaryData.supportReactions.forces[femNodeLabelForReaction] is undefined
        let reactionValueToApply = 0;
        let reactionFound = false;
        const sourceSupports = safeJsonParse<Support[]>(sourceResult.supports, []);
        const sourceBeamLength = sourceResult.length;
        const sourceNodePositions = new Set<number>([0, sourceBeamLength]);
        sourceSupports.forEach((s: Support) => sourceNodePositions.add(s.position));
        const sortedSourcePositions = Array.from(sourceNodePositions).sort((a, b) => a - b);
        let femNodeLabelForReaction = "";
        const targetSourceSupportPosition = relationship.source_support_position;
        const supportIndex = sortedSourcePositions.findIndex(p => Math.abs(p - targetSourceSupportPosition) < 0.001);

        if (supportIndex !== -1) {
          femNodeLabelForReaction = (supportIndex + 1).toString();
        }

        if (femNodeLabelForReaction && sourceSummaryData.supportReactions.forces[femNodeLabelForReaction]) {
          reactionValueToApply = sourceSummaryData.supportReactions.forces[femNodeLabelForReaction].maxUp; // Or appropriate reaction component
          if (typeof reactionValueToApply === 'number' && reactionValueToApply !== 0) { // Only link if there's a non-zero reaction
             reactionFound = true;
             console.log(`[Trigger Reanalysis] Found reaction value ${reactionValueToApply} from source ${derivedSourceCalculationName} using FEM node label '${femNodeLabelForReaction}'`);
          } else {
            console.warn(`[Trigger Reanalysis] Zero or invalid reaction value (${reactionValueToApply}) found for source ${derivedSourceCalculationName} at FEM node '${femNodeLabelForReaction}'. Load not linked.`);
          }
        } else {
          console.warn(`[Trigger Reanalysis] Reaction not found for source ${derivedSourceCalculationName} using FEM node label '${femNodeLabelForReaction}'. Available keys:`, sourceSummaryData.supportReactions.forces ? Object.keys(sourceSummaryData.supportReactions.forces) : 'N/A');
        }

        if (reactionFound) {
          const newLinkedLoad = new Load({
            label: generatedLinkedLoadLabel,
            type: LoadApplicationType.POINT, 
            loadType: LoadType.DEAD, 
            startPosition: relationship.load_application_position, 
            startMagnitude: reactionValueToApply,
          });
          const newLinkedLoadGroupId = `lg_linked_${relationship.id.substring(0,8)}_${Math.random().toString(36).substring(2, 7)}`;
          lastCreatedLinkedLoadGroupId = newLinkedLoadGroupId;
          const newLinkedLoadGroup = new ActualLoadGroupType(
            newLinkedLoadGroupId,
            generatedLinkedLoadLabel,
            { start: newLinkedLoad.startPosition as number, end: newLinkedLoad.startPosition as number }, 
            { [LoadType.DEAD]: newLinkedLoad.startMagnitude as number }, // Simplified magnitudes
            {},
            LoadApplicationType.POINT, 
            undefined, false, false, true,  
            relationship.source_calculation_id,
            relationship.source_support_position
          );
          newLinkedLoadGroup.loads = [newLinkedLoad];
          loadGroups.push(newLinkedLoadGroup);
          console.log(`[Trigger Reanalysis] Created LoadGroup ${newLinkedLoadGroup.id} for ${derivedSourceCalculationName}`);
        }
      }
    }

    console.log(`[Trigger Reanalysis] Load groups for calcId ${calculationId} AFTER rebuilding linked loads:`, JSON.stringify(loadGroups.map(lg=>({id: lg.id, label: lg.label, loads: lg.loads.map(l=>({val:l.startMagnitude}))})), null, 2));

    let area = 0;
    if (beamPropertiesState) {
      if (beamPropertiesState.selectedMaterial === "steel") {
        // For steel beams, get area from steel section properties
        const steelSectionProperties = (beamPropertiesState as any).steelSectionProperties;
        if (steelSectionProperties) {
          area = parseFloat(steelSectionProperties.A || "0");
          console.log("[Trigger Reanalysis] Using steel beam area:", area, "from section:", steelSectionProperties.EDI_Std_Nomenclature || "Unknown");
        }
      } else if (beamPropertiesState.manual_Area) {
        area = beamPropertiesState.manual_Area;
      } else if (beamPropertiesState.lumberType === 'sawn' && beamPropertiesState.lumberProperties?.A) {
        area = beamPropertiesState.lumberProperties.A;
      } else if (beamPropertiesState.lumberType === 'glulam' && beamPropertiesState.selectedGluLamProperties?.sectionProperties?.area) {
        area = beamPropertiesState.selectedGluLamProperties.sectionProperties.area;
      }
    }

    const beamDataForAnalysis: BeamData = {
      properties: {
        length: originalBeamAnalysisResult.length,
        elasticModulus: originalBeamAnalysisResult.modulusOfElasticity,
        momentOfInertia: originalBeamAnalysisResult.momentOfInertia,
        area: area,
      },
      loadGroups: loadGroups, 
      supports: supports,
      designMethod: savedBeamData?.designMethod || 'ASD', // Use saved designMethod or default to ASD
      selectedLoadCombos: savedBeamData?.selectedLoadCombos || [] // Use saved selectedLoadCombos or default to empty array
    };
    
    if (beamDataForAnalysis.properties.area === 0) {
        console.warn("[Trigger Reanalysis] Beam area is 0.");
    }

    const hasEffectiveLoads = loadGroups.some(lg => 
      lg.loads && lg.loads.some(load => 
        (typeof load.startMagnitude === 'number' && load.startMagnitude !== 0) || 
        (typeof load.endMagnitude === 'number' && load.endMagnitude !== 0)
      )
    );

    if (!hasEffectiveLoads) {
      console.warn(`[Trigger Reanalysis] No effective loads found for calculation ${calculationId} after rebuilding. Clearing existing results and loads field.`);
      const updatedBeamAnalysisResult = await prisma.beamAnalysisResult.update({
        where: { id: originalBeamAnalysisResult.id },
        data: {
          results: JSON.stringify({ status: "NO_EFFECTIVE_LOADS", message: "Analysis skipped: No effective loads after attempting to rebuild from sources." }),
          loads: JSON.stringify([]), // Explicitly clear the loads field
          bendingStressRatio: null,
          shearStressRatio: null,
          maxDeflection: null,
          updatedAt: new Date(),
        },
      });
      return NextResponse.json({ 
        message: 'Re-analysis skipped: No effective loads. Results and loads field cleared.',
        updatedAnalysis: updatedBeamAnalysisResult 
      }, { status: 200 });
    }

    console.log(`[Trigger Reanalysis] Proceeding with performServerSideCalculations for ${calculationId} as effective loads ARE present.`);

    console.log(`[Trigger Reanalysis] Calling performServerSideCalculations (unit: ${unitSystem})`);

    const newAnalysisOutput: AnalysisOutput | null = await performServerSideCalculations(
      beamDataForAnalysis,
      unitSystem,
      beamPropertiesState
    );

    if (!newAnalysisOutput?.summaryData) {
      // If it still fails here, it implies an issue with the calculation engine itself, even with loads.
      console.error(`[Trigger Reanalysis] performServerSideCalculations for ${calculationId} returned null or no summaryData even with loads. Output:`, newAnalysisOutput);
      // Update DB to reflect failed analysis state
      await prisma.beamAnalysisResult.update({
        where: { id: originalBeamAnalysisResult.id },
        data: {
          results: JSON.stringify({ status: "ANALYSIS_ENGINE_ERROR", message: "Analysis engine failed to produce results.", detail: newAnalysisOutput }),
          bendingStressRatio: null,
          shearStressRatio: null,
          maxDeflection: null,
          updatedAt: new Date(),
        }
      });
      return NextResponse.json({ error: 'Re-analysis failed: Calculation engine did not produce results.', detail: newAnalysisOutput }, { status: 500 });
    }
    console.log("[Trigger Reanalysis] Re-analysis successful.");

    // Convert LoadGroup and Load instances to plain objects before stringifying
    const plainLoadGroupsForDb = loadGroups.map(lgInstance => {
      // Create a plain object from the LoadGroup instance
      const plainLg: { [key: string]: any } = {};
      for (const key in lgInstance) {
        if (Object.prototype.hasOwnProperty.call(lgInstance, key)) {
          plainLg[key] = (lgInstance as any)[key];
        }
      }
      
      // Specifically ensure the 'loads' array contains plain objects
      if (lgInstance.loads && Array.isArray(lgInstance.loads)) {
        plainLg.loads = lgInstance.loads.map(loadInstance => {
          const plainLoad: { [key: string]: any } = {};
          for (const key in loadInstance) {
            if (Object.prototype.hasOwnProperty.call(loadInstance, key)) {
              plainLoad[key] = (loadInstance as any)[key];
            }
          }
          return plainLoad;
        });
      }
      return plainLg;
    });

    console.log(`[Trigger Reanalysis] Load groups for calcId ${calculationId} (plain objects) BEFORE saving to DB (checking linked group ${lastCreatedLinkedLoadGroupId || 'N/A'}):`, JSON.stringify(plainLoadGroupsForDb.find(lg => lg.id === lastCreatedLinkedLoadGroupId), null, 2));
    console.log(`[Trigger Reanalysis] ALL Load groups for calcId ${calculationId} (plain objects) BEFORE saving to DB:`, JSON.stringify(plainLoadGroupsForDb, null, 2));

    if (lastCreatedLinkedLoadGroupId) {
      const linkedGroupForLog = plainLoadGroupsForDb.find(lg => lg.id === lastCreatedLinkedLoadGroupId);
      console.log(`[Trigger Reanalysis] Details of LAST CREATED linked LoadGroup (ID: ${lastCreatedLinkedLoadGroupId}) BEFORE saving to DB:`, JSON.stringify(linkedGroupForLog, null, 2));
    } else {
      console.log(`[Trigger Reanalysis] No new linked load groups were created in this run.`);
    }

    await prisma.beamAnalysisResult.update({
      where: { id: originalBeamAnalysisResult.id },
      data: {
        name: originalBeamAnalysisResult.name, 
        length: originalBeamAnalysisResult.length,
        modulusOfElasticity: originalBeamAnalysisResult.modulusOfElasticity,
        momentOfInertia: originalBeamAnalysisResult.momentOfInertia,
        beamProperties: originalBeamAnalysisResult.beamProperties, 
        supports: JSON.stringify(supports), 
        loads: JSON.stringify(plainLoadGroupsForDb), // Use plain objects for saving
        beamData: JSON.stringify(beamDataForAnalysis), // Save the complete beam data
        results: JSON.stringify(newAnalysisOutput.summaryData), 
        bendingStressRatio: newAnalysisOutput.summaryData.maxBendingStressRatio?.ratio,
        shearStressRatio: newAnalysisOutput.summaryData.maxShearStressRatio?.ratio,    
        maxDeflection: newAnalysisOutput.summaryData.maxTotalDeflectionDownward?.value, 
        updatedAt: new Date(),
      },
    });
    
    return NextResponse.json(
      { message: 'Re-analysis successful and results updated.', calculationId: calculation.id, updatedBeamAnalysisResultId: originalBeamAnalysisResult.id },
      { status: 200 }
    );

  } catch (error) {
    console.error(`[Trigger Reanalysis] Error for calcId ${params.calculationId}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error during re-analysis.';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
} 