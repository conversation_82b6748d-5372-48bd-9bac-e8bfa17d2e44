import { NextResponse } from 'next/server';
import { db as prisma } from '@/lib/db';

export const dynamic = 'force-dynamic';

interface CalculationNode {
  id: string;
  name: string;
  type: string | null;
  // Add other relevant properties for display in the graph
}

interface GraphEdge {
  source: string;
  target: string;
}

interface DependencyGraphResponse {
  nodes: CalculationNode[];
  edges: GraphEdge[];
}

async function fetchDependencyGraph(startCalculationId: string): Promise<DependencyGraphResponse> {
  const nodes: CalculationNode[] = [];
  const edges: GraphEdge[] = [];
  const queue: string[] = [startCalculationId];
  const visited: Set<string> = new Set();
  const nodeMap: Map<string, CalculationNode> = new Map();

  while (queue.length > 0) {
    const currentId = queue.shift()!; // Non-null assertion as queue.length > 0

    if (visited.has(currentId)) {
      continue;
    }
    visited.add(currentId);

    // Fetch current calculation details
    const currentCalc = await prisma.calculation.findUnique({
      where: { id: currentId },
      select: { 
        id: true, 
        name: true, 
        type: true, 
        projectId: true, 
        project: { // Include project to get organizationId
          select: {
            organizationId: true
          }
        } 
      },
    });

    if (currentCalc) {
      if (!nodeMap.has(currentCalc.id)) {
        const node = {
          id: currentCalc.id,
          name: currentCalc.name || 'Unnamed Calculation',
          type: currentCalc.type,
        };
        nodes.push(node);
        nodeMap.set(currentCalc.id, node);
      }

      // Find direct dependents (calculations that depend on currentId)
      const relationships = await prisma.calculationRelationship.findMany({
        where: { source_calculation_id: currentId },
        include: {
          dependentCalculation: { // The calculation that IS dependent
            select: { id: true, name: true, type: true },
          },
        },
      });

      for (const rel of relationships) {
        if (rel.dependentCalculation) {
          const dependentCalc = rel.dependentCalculation;
          if (!nodeMap.has(dependentCalc.id)) {
            const depNode = {
              id: dependentCalc.id,
              name: dependentCalc.name || 'Unnamed Calculation',
              type: dependentCalc.type,
            };
            nodes.push(depNode);
            nodeMap.set(dependentCalc.id, depNode);
          }

          edges.push({
            source: currentId,
            target: dependentCalc.id,
          });

          if (!visited.has(dependentCalc.id)) {
            queue.push(dependentCalc.id);
          }
        }
      }
    }
  }
  return { nodes, edges };
}

export async function GET(
  request: Request,
  { params }: { params: { calculationId: string } }
) {
  const { calculationId } = params;

  if (!calculationId) {
    return NextResponse.json({ error: 'Calculation ID is required' }, { status: 400 });
  }

  try {
    const graphData = await fetchDependencyGraph(calculationId);
    
    // If the starting node itself wasn't added (e.g., no dependents but should still be shown as a root)
    // Ensure the root node is part of the nodes list if not already captured by the traversal (e.g. an isolated node)
    if (!graphData.nodes.find(n => n.id === calculationId)) {
        const rootCalc = await prisma.calculation.findUnique({
            where: { id: calculationId },
            select: { id: true, name: true, type: true }
        });
        if (rootCalc) {
            graphData.nodes.unshift({ // Add to the beginning
                id: rootCalc.id,
                name: rootCalc.name || 'Unnamed Calculation',
                type: rootCalc.type
            });
        }
    }
    
    return NextResponse.json(graphData, { status: 200 });

  } catch (error) {
    console.error(`Error fetching dependency graph for calculation ${calculationId}:`, error);
    return NextResponse.json({ error: 'Error fetching dependency graph' }, { status: 500 });
  }
}
