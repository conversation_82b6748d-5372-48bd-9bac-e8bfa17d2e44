import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

interface SawnLumberSpecies {
  speciesCombination: string;
  species: string;
  gradingRulesAgencies: string;
  designValues: string;
  location: string;
  version: string;
}

interface SawnLumberData {
  speciesCombinations: string[];
  species: {
    [combination: string]: {
      species: string[];
      gradingRulesAgencies: string;
      designValues: string;
      location: string;
    };
  };
}

// Raw data structure that preserves version info for filtering
interface SawnLumberRawData {
  allData: SawnLumberSpecies[];
}

// --- In-memory cache for Sawn Lumber Species ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}

const CACHE_DURATION_MS_SAWN_SPECIES = 60 * 60 * 1000; // 1 hour

let sawnLumberSpeciesCache: Cache<SawnLumberRawData> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Sawn Lumber Species ---

// Helper function to build SawnLumberData from raw data
function buildSawnLumberData(rawData: SawnLumberSpecies[]): SawnLumberData {
  const sawnLumberData: SawnLumberData = {
    speciesCombinations: [],
    species: {},
  };

  rawData.forEach((row) => {
    const speciesCombination = row.speciesCombination;
    const species = row.species;
    
    if (!speciesCombination || !species) return;

    if (!sawnLumberData.speciesCombinations.includes(speciesCombination)) {
      sawnLumberData.speciesCombinations.push(speciesCombination);
      sawnLumberData.species[speciesCombination] = {
        species: [],
        gradingRulesAgencies: row.gradingRulesAgencies,
        designValues: row.designValues,
        location: row.location,
      };
    }

    if (!sawnLumberData.species[speciesCombination].species.includes(species)) {
      sawnLumberData.species[speciesCombination].species.push(species);
    }
  });

  // Remove duplicates and sort
  sawnLumberData.speciesCombinations = Array.from(new Set(sawnLumberData.speciesCombinations)).sort();
  
  // Sort species lists within each combination
  Object.keys(sawnLumberData.species).forEach(combination => {
    sawnLumberData.species[combination].species = Array.from(new Set(sawnLumberData.species[combination].species)).sort();
  });

  return sawnLumberData;
}

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || 'NDS 2018'; // Default version
  
  // --- Cache check for Sawn Lumber Species ---
  const now = Date.now();
  if (sawnLumberSpeciesCache.data && sawnLumberSpeciesCache.expires && sawnLumberSpeciesCache.expires > now) {
    console.log("Serving sawn lumber species data from cache.");
    // Filter cached data by version
    const filteredRawData = sawnLumberSpeciesCache.data!.allData.filter(
      item => item.version === version
    );
    
    const filteredData = buildSawnLumberData(filteredRawData);
    
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh sawn lumber species data.");
  // --- End cache check for Sawn Lumber Species ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'SawnLumberSpeciesCombinations.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: 'Sawn lumber species data file not found' },
        { 
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    return new Promise<NextResponse>((resolve, reject) => {
      const rows: any[] = [];

      stream
        .pipe(csv())
        .on('data', (row) => rows.push(row))
        .on('end', () => {
          try {
            const allRawData: SawnLumberSpecies[] = [];
            
            rows.forEach((row) => {
              // Match exact column names from the CSV
              const speciesCombination = row['Species Combination\n']?.trim().toUpperCase();
              const species = row['Species That May Be Included in Combination']?.trim().toUpperCase();
              const gradingRulesAgencies = row['Grading Rules Agencies']?.trim();
              const designValues = row['Design Values Provided in Tables']?.trim();
              const location = row['Location']?.trim();
              const version = row['Version']?.trim();
              
              if (!speciesCombination || !species) return;

              allRawData.push({
                speciesCombination,
                species,
                gradingRulesAgencies,
                designValues,
                location,
                version,
              });
            });

            // --- Cache update for Sawn Lumber Species ---
            sawnLumberSpeciesCache.data = { allData: allRawData };
            sawnLumberSpeciesCache.expires = Date.now() + CACHE_DURATION_MS_SAWN_SPECIES;
            console.log("Sawn lumber species data cached.");
            // --- End cache update for Sawn Lumber Species ---

            // Filter data by requested version and build response
            const filteredRawData = allRawData.filter(item => item.version === version);
            const responseData = buildSawnLumberData(filteredRawData);

            resolve(NextResponse.json(responseData, {
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, max-age=3600',
              },
            }));
          } catch (error) {
            reject(NextResponse.json(
              { error: 'Failed to process sawn lumber species data', details: error instanceof Error ? error.message : 'Unknown error' },
              { 
                status: 500,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            ));
          }
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse sawn lumber species data', details: error.message },
            { 
              status: 500,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load sawn lumber species data', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}