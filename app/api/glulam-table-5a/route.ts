import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';
import {
  DEFAULT_NDS_VERSION,
  API_ENDPOINTS,
  ERROR_MESSAGES,
  HTTP_STATUS,
  CACHE_DURATION_MS,
} from '@/lib/constants/nds-constants';

// Tentative interface based on typical NDS Table 5A content
interface Table5ARow {
  stress_class: string;
  species_outer: string;
  species_core: string;
  F_bx_pos_psi: number;
  F_bx_neg_psi: number;
  tension_face: number; // Assuming this is a numeric value like psi, if not, change to string
  compression_face: number; // Assuming this is a numeric value like psi, if not, change to string
  F_vx_psi: number;
  E_xtrue_ksi: number;
  E_xapp_ksi: number;
  E_xmin_ksi: number;
  F_by_psi: number;
  F_cperp_y_psi: number; // Header in CSV might be F_c⊥_y_psi
  F_vy_psi: number;
  E_ytrue_ksi: number;
  E_yapp_ksi: number;
  E_ymin_ksi: number;
  F_t_psi: number;
  F_c_psi: number;
  G_top_or_bottom: number; // Specific Gravity
  G_side: number;          // Specific Gravity
  table: string;
  version: string;
  wet_service_factor_Cm_for_Fb: number;
  wet_service_factor_Cm_for_Ft: number;
  wet_service_factor_Cm_for_Fv: number;
  wet_service_factor_Cm_for_Fc_perp: number; // Header will be normalized to this
  wet_service_factor_Cm_for_Fc: number;
  wet_service_factor_Cm_for_E_and_Emin: number;
  // Add any other columns if they exist and were missed, or mark as optional if they might be empty
  Notes?: string; // Adding notes as optional string, was in previous tentative interface
}

// --- In-memory cache for Table5A ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}

let table5aCache: Cache<Table5ARow[]> = { // Type is Table5ARow[]
  data: null,
  expires: null,
};
// --- End in-memory cache for Table5A ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || DEFAULT_NDS_VERSION;
  // --- Cache check for Table5A ---
  const now = Date.now();
  if (table5aCache.data && table5aCache.expires && table5aCache.expires > now) {
    console.log("Serving Table5A data from cache.");
    // Filter by version before returning cached data
    const filteredData = table5aCache.data.filter(row => row.version === version);
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh Table5A data.");
  // --- End cache check for Table5A ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'glulam', 'Table5A.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      console.error('File access error:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.WOOD_DATA_NOT_FOUND },
        { 
          status: HTTP_STATUS.NOT_FOUND,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);
    const results: Table5ARow[] = [];

    const numericKeys: Array<keyof Table5ARow> = [
      'F_bx_pos_psi', 'F_bx_neg_psi', 'tension_face', 'compression_face',
      'F_vx_psi', 'E_xtrue_ksi', 'E_xapp_ksi', 'E_xmin_ksi', 'F_by_psi',
      'F_cperp_y_psi', 'F_vy_psi', 'E_ytrue_ksi', 'E_yapp_ksi', 'E_ymin_ksi',
      'F_t_psi', 'F_c_psi', 'G_top_or_bottom', 'G_side',
      'wet_service_factor_Cm_for_Fb', 'wet_service_factor_Cm_for_Ft',
      'wet_service_factor_Cm_for_Fv', 'wet_service_factor_Cm_for_Fc_perp',
      'wet_service_factor_Cm_for_Fc', 'wet_service_factor_Cm_for_E_and_Emin'
    ];
    const optionalStringKeys: Array<keyof Table5ARow> = ['Notes'];
    // All other keys from Table5ARow are assumed to be required strings

    return new Promise<NextResponse>((resolve, reject) => {
      stream
        .pipe(csv({
          mapHeaders: ({ header }) => {
            let normalizedHeader = header.trim().replace(/\s+/g, '_');
            normalizedHeader = normalizedHeader.replace('F_c⊥_y_psi', 'F_cperp_y_psi');
            normalizedHeader = normalizedHeader.replace('wet_service_factor_Cm_for_Fc⊥', 'wet_service_factor_Cm_for_Fc_perp');
            return normalizedHeader;
          }
        }))
        .on('data', (dataFromCsv) => {
          const processedRow = {} as Table5ARow;

          // Iterate over the keys expected by the Table5ARow interface
          // This ensures that processedRow will have all properties of Table5ARow
          // even if some are missing from a CSV row (they'll be undefined or NaN).
          const allInterfaceKeys = Object.keys(processedRow) as Array<keyof Table5ARow>; 
          // ^^^ This placeholder approach for keys isn't ideal.
          // A better way is to have a static list of all keys or derive from an instance.
          // For now, let's process based on keys present in dataFromCsv, 
          // assuming they are correctly mapped to Table5ARow keys by mapHeaders.

          for (const keyFromCsv in dataFromCsv) {
            const key = keyFromCsv as keyof Table5ARow; // Assume mapHeaders correctly maps to Table5ARow keys
            const rawValue = dataFromCsv[keyFromCsv];
            const valueString = String(rawValue || '').trim();

            if (numericKeys.includes(key)) {
              if (valueString === '' || valueString.toLowerCase() === 'na' || valueString.toLowerCase() === 'n/a') {
                (processedRow as any)[key] = NaN; 
              } else {
                (processedRow as any)[key] = parseFloat(valueString.replace(',', ''));
              }
            } else if (optionalStringKeys.includes(key)) {
              if (valueString === '' || valueString.toLowerCase() === 'na' || valueString.toLowerCase() === 'n/a') {
                (processedRow as any)[key] = undefined;
              } else {
                (processedRow as any)[key] = valueString;
              }
            } else { // Required string keys
              // Potentially validate if required string is empty, but for now, assign it.
              (processedRow as any)[key] = valueString;
            }
          }
          results.push(processedRow);
        })
        .on('end', () => {
          // --- Cache update for Table5A ---
          table5aCache.data = results;
          table5aCache.expires = Date.now() + CACHE_DURATION_MS.WOOD_DATA;
          console.log("Table5A data cached.");
          // --- End cache update for Table5A ---
          
          // Filter by version before returning
          const filteredResults = results.filter(row => row.version === version);
          resolve(NextResponse.json(filteredResults, {
            headers: { 
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=3600',
            },
          }));
        })
        .on('error', (error) => {
          console.error('CSV parsing error:', error);
          reject(NextResponse.json(
            { error: ERROR_MESSAGES.WOOD_DATA_PARSE_FAILED, details: error.message },
            { 
              status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
              headers: { 'Content-Type': 'application/json' },
            }
          ));
        });
    });
  } catch (error) {
    console.error('Overall GET request error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json(
      { error: ERROR_MESSAGES.WOOD_DATA_LOAD_FAILED, details: errorMessage },
      { 
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}

// Placeholder instance of Table5ARow to help derive keys if needed elsewhere or for robust key iteration.
// This is not directly used in the loop above for now to simplify, but demonstrates a way to get all keys.
const processedRow_placeholder_for_keys: Table5ARow = {
  stress_class: '',
  species_outer: '',
  species_core: '',
  F_bx_pos_psi: NaN,
  F_bx_neg_psi: NaN,
  tension_face: NaN,
  compression_face: NaN,
  F_vx_psi: NaN,
  E_xtrue_ksi: NaN,
  E_xapp_ksi: NaN,
  E_xmin_ksi: NaN,
  F_by_psi: NaN,
  F_cperp_y_psi: NaN,
  F_vy_psi: NaN,
  E_ytrue_ksi: NaN,
  E_yapp_ksi: NaN,
  E_ymin_ksi: NaN,
  F_t_psi: NaN,
  F_c_psi: NaN,
  G_top_or_bottom: NaN,
  G_side: NaN,
  table: '',
  version: '',
  wet_service_factor_Cm_for_Fb: NaN,
  wet_service_factor_Cm_for_Ft: NaN,
  wet_service_factor_Cm_for_Fv: NaN,
  wet_service_factor_Cm_for_Fc_perp: NaN,
  wet_service_factor_Cm_for_Fc: NaN,
  wet_service_factor_Cm_for_E_and_Emin: NaN,
  Notes: undefined
}; 