import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { title, clientName, address } = body;

    if (!title) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 });
    }

    const project = await prisma.project.create({
      data: {
        title,
        clientName,
        address,
      },
    });

    return NextResponse.json(project, { status: 201 });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json({ error: 'Error creating project' }, { status: 500 });
  }
}

export async function GET() {
  try {
    const projects = await prisma.project.findMany({
      include: {
        calculations: true, // Include calculations if you want to display them on the project list
        beamAnalysisResults: true, // Include beam analysis results
      }
    });
    return NextResponse.json(projects, { status: 200 });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json({ error: 'Error fetching projects' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const body = await request.json();
    const { projectIds } = body;

    if (!projectIds || !Array.isArray(projectIds) || projectIds.length === 0) {
      return NextResponse.json({ error: 'Project IDs are required and must be an array.' }, { status: 400 });
    }

    // Validate that all projectIds are strings (or whatever your ID type is)
    if (!projectIds.every(id => typeof id === 'string')) {
        return NextResponse.json({ error: 'All project IDs must be strings.' }, { status: 400 });
    }

    const deleteResult = await prisma.project.deleteMany({
      where: {
        id: {
          in: projectIds,
        },
      },
    });

    if (deleteResult.count === 0) {
      // This could mean none of the provided IDs matched any projects,
      // or the list was valid but empty after some internal filtering not shown here.
      // Depending on desired behavior, this might not be an error.
      // For now, let's inform the client that no projects were deleted.
      return NextResponse.json({ message: 'No projects found matching the provided IDs. Nothing deleted.', count: 0 }, { status: 200 });
    }

    return NextResponse.json({ message: `${deleteResult.count} project(s) deleted successfully.`, count: deleteResult.count }, { status: 200 });
  } catch (error) {
    console.error('Error deleting projects:', error);
    // Check for specific Prisma errors if needed, e.g., P2025 (Record to delete does not exist)
    // though deleteMany doesn't typically throw P2025 for non-existent records in `in` list.
    return NextResponse.json({ error: 'Error deleting projects' }, { status: 500 });
  }
} 