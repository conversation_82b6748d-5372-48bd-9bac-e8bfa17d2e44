import { NextResponse } from 'next/server';
import { db as prisma } from '@/lib/db';

export const dynamic = 'force-dynamic';

export async function GET(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  try {
    const projectId = params.projectId;
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        calculations: true,
        beamAnalysisResults: {
          include: {
            calculation: {
              include: {
                sourceOfRelationships: {
                  select: { id: true }
                }
              }
            },
          }
        },
        folders: true,
      },
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Check if beamAnalysisResults exists and is not empty before processing
    if (project.beamAnalysisResults && project.beamAnalysisResults.length > 0) {
      // Define a type for the beam analysis result items after the Prisma query
      type BeamAnalysisResultWithDependentsCount = typeof project.beamAnalysisResults[0] & {
        calculation?: (typeof project.beamAnalysisResults[0]['calculation']) & {
          sourceOfRelationships?: Array<{ id: string }>;
        };
      };

      // Manually process beamAnalysisResults to add dependentsCount
      const processedProject = {
        ...project,
        beamAnalysisResults: project.beamAnalysisResults.map((bar: BeamAnalysisResultWithDependentsCount) => {
          console.log(`API Processing BAR ID: ${bar.id}, Calc ID: ${bar.calculation?.id}, Calc Name: ${bar.calculation?.name}, Raw SourceRelationships:`, bar.calculation?.sourceOfRelationships);
          const dependentsCount = bar.calculation?.sourceOfRelationships?.length ?? 0;
          console.log(`API BAR ID: ${bar.id}, Calc ID: ${bar.calculation?.id}, DependentsCount: ${dependentsCount}`);
          
          const calculationData = bar.calculation ? { ...bar.calculation } : null;
          if (calculationData && 'sourceOfRelationships' in calculationData) {
            delete (calculationData as any).sourceOfRelationships;
          }

          return {
            ...bar,
            dependentsCount: dependentsCount,
          };
        })
      };
      return NextResponse.json(processedProject, { status: 200 });
    } else {
      // If beamAnalysisResults is empty or null, return the project as is (with empty/null beamAnalysisResults)
      return NextResponse.json(project, { status: 200 });
    }

  } catch (error) {
    console.error(`Error fetching project ${params.projectId}:`, error);
    return NextResponse.json({ error: 'Error fetching project' }, { status: 500 });
  }
} 

interface UpdateProjectData {
  title?: string;
  clientName?: string | null;
  clientAddress?: string | null;
  clientPhone?: string | null;
  status?: string | null;
  dueDate?: string | null; 
}

export async function PUT(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  const { projectId } = params;
  try {
    const body = await request.json() as UpdateProjectData;
    const { title, clientName, clientAddress, clientPhone, status, dueDate } = body;

    // Basic validation: title is required
    if (title !== undefined && (typeof title !== 'string' || title.trim() === '')) {
      return NextResponse.json({ error: 'Project title is required and cannot be empty' }, { status: 400 });
    }

    // Construct update data, only including fields that are actually provided
    const updateData: { 
      title?: string;
      clientName?: string | null;
      clientAddress?: string | null;
      clientPhone?: string | null;
      status?: string | null;
      dueDate?: Date | null;
    } = {};

    if (title !== undefined) updateData.title = title.trim();
    if (clientName !== undefined) updateData.clientName = clientName === '' ? null : clientName;
    if (clientAddress !== undefined) updateData.clientAddress = clientAddress === '' ? null : clientAddress;
    if (clientPhone !== undefined) updateData.clientPhone = clientPhone === '' ? null : clientPhone;
    if (status !== undefined) updateData.status = status === '' ? null : status;
    if (dueDate !== undefined) {
      updateData.dueDate = dueDate ? new Date(dueDate) : null;
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({ error: 'No update data provided' }, { status: 400 });
    }

    const updatedProject = await prisma.project.update({
      where: { id: projectId },
      data: updateData,
      include: { // Optionally include related data if your settings page or subsequent logic needs it
        calculations: true,
        beamAnalysisResults: true,
      },
    });

    return NextResponse.json(updatedProject, { status: 200 });

  } catch (error: any) {
    console.error(`Error updating project ${projectId}:`, error);
    if (error.code === 'P2025') { // Prisma error code for record not found
      return NextResponse.json({ error: 'Project not found to update' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Error updating project' }, { status: 500 });
  }
} 