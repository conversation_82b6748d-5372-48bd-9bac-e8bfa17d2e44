import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
// import { adminAuth } from '@/lib/firebase-admin'; // Removed adminAuth
import { z } from 'zod';

// Define a more specific type for calculations that include beamAnalysisResults
interface CalculationWithBeamResults {
  id: string;
  name: string;
  beamAnalysisResults: any[];
}

interface BeamAnalysisResultWithName {
  id: string;
  name: string;
  calculationId: string;
}

// Schema for updating folder name
const folderUpdateNameSchema = z.object({
  name: z.string().min(1, { message: "Folder name cannot be empty" }).max(255),
});

// Schema for updating calculations in a folder
const folderUpdateCalculationsSchema = z.object({
  calculationIds: z.array(z.string()).optional(), // Array of calculation IDs to be IN this folder
});

// Combined schema for flexibility, though we'll handle logic separately
const folderUpdateSchema = folderUpdateNameSchema.merge(folderUpdateCalculationsSchema);

// Schema for updating calculations in a folder (PATCH)
const folderPatchSchema = folderUpdateNameSchema.partial().merge(folderUpdateCalculationsSchema.partial());

// Define an interface for what a calculation object looks like after the include
// This helps in typing `calc` explicitly later.
interface CalculationWithIncludedResult {
  id: string;
  name: string;
  beamAnalysisResult: any | null;
  // Add other relations if included, e.g., project: Project | null;
}

export async function PATCH(
  request: Request,
  { params }: { params: { projectId: string; folderId: string } }
) {
  try {
    // Authentication removed
    // const authorization = request.headers.get("Authorization");
    // if (!authorization || !authorization.startsWith("Bearer ")) {
    //   return NextResponse.json({ error: "Unauthorized: Missing or invalid token" }, { status: 401 });
    // }
    // const idToken = authorization.split("Bearer ")[1];
    // let decodedToken;
    // try {
    //   decodedToken = await adminAuth.verifyIdToken(idToken);
    // } catch (error) {
    //   return NextResponse.json({ error: "Unauthorized: Invalid token" }, { status: 401 });
    // }
    // const userId = decodedToken.uid;
    // if (!userId) {
    //   return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    // }

    const { projectId, folderId } = params;
    if (!projectId || !folderId) {
      return NextResponse.json({ error: 'Project ID and Folder ID are required' }, { status: 400 });
    }

    const json = await request.json();
    const parsed = folderPatchSchema.safeParse(json);

    if (!parsed.success) {
      return NextResponse.json({ error: 'Invalid input', details: parsed.error.format() }, { status: 400 });
    }

    const { name, calculationIds } = parsed.data;

    if (name === undefined && calculationIds === undefined) {
        return NextResponse.json({ error: 'No update data provided (name or calculationIds)' }, { status: 400 });
    }

    // Verify folder exists in project (user access check removed)
    const folder = await db.folder.findFirst({
      where: {
        id: folderId,
        projectId: projectId,
        // project: { // User-specific access check removed
        //   organization: {
        //     memberships: {
        //       some: { userId },
        //     },
        //   },
        // },
      },
    });

    if (!folder) {
      // Changed error message
      return NextResponse.json({ error: 'Folder not found in this project' }, { status: 404 });
    }

    const updateData: { name?: string } = {};
    
    if (name !== undefined) {
      if (name !== folder.name) {
        const existingFolderWithNewName = await db.folder.findFirst({
          where: {
            projectId,
            name,
            id: { not: folderId },
          },
        });
        if (existingFolderWithNewName) {
          return NextResponse.json({ error: 'A folder with this name already exists in the project' }, { status: 409 });
        }
        updateData.name = name;
      }
    }
    
    const transactionOperations: any[] = [];

    if (Object.keys(updateData).length > 0) {
        transactionOperations.push(db.folder.update({
            where: { id: folderId },
            data: updateData,
        }));
    }

    if (calculationIds !== undefined) {
      if (calculationIds.length > 0) {
        const calculationsToUpdate = await db.calculation.findMany({
            where: {
                id: { in: calculationIds },
                projectId: projectId,
            }
        });
        if (calculationsToUpdate.length !== calculationIds.length) {
            return NextResponse.json({ error: 'One or more calculation IDs are invalid or do not belong to this project' }, { status: 400 });
        }
        // Detach specified calculations from their current folders if they are in one (within the same project)
        await db.calculation.updateMany({
            where: {
                id: { in: calculationIds },
                projectId: projectId,
                // folderId: { not: null } // Only update those that are currently in a folder
            },
            data: { folderId: null } 
        });
        // Attach to new folder
         transactionOperations.push(db.calculation.updateMany({
            where: {
                id: { in: calculationIds },
                projectId: projectId, 
            },
            data: { folderId: folderId }, 
        }));
      } else { // if calculationIds is an empty array, means unassign all from this folder
         transactionOperations.push(db.calculation.updateMany({
            where: { folderId: folderId, projectId: projectId },
            data: { folderId: null },
        }));
      }
    }
    
    if (transactionOperations.length > 0) {
        await db.$transaction(transactionOperations);
        const finalFolderState = await db.folder.findUnique({
            where: {id: folderId},
            include: { calculations: { select: {id: true}}} 
        });
        return NextResponse.json(finalFolderState);
    }

    return NextResponse.json(folder);

  } catch (error) {
    console.error('[FOLDER_PATCH]', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { projectId: string; folderId: string } }
) {
  try {
    // Authentication removed
    // const authorization = request.headers.get("Authorization");
    // if (!authorization || !authorization.startsWith("Bearer ")) {
    //   return NextResponse.json({ error: "Unauthorized: Missing or invalid token" }, { status: 401 });
    // }
    // const idToken = authorization.split("Bearer ")[1];
    // let decodedToken;
    // try {
    //   decodedToken = await adminAuth.verifyIdToken(idToken);
    // } catch (error) {
    //   return NextResponse.json({ error: "Unauthorized: Invalid token" }, { status: 401 });
    // }
    // const userId = decodedToken.uid;
    // if (!userId) {
    //   return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    // }

    const { projectId, folderId } = params;
    if (!projectId || !folderId) {
      return NextResponse.json({ error: 'Project ID and Folder ID are required' }, { status: 400 });
    }

    // Verify folder exists in project (user access check removed)
    const folder = await db.folder.findFirst({
      where: {
        id: folderId,
        projectId: projectId,
      },
      // No longer need to include calculations here for the check
    });

    if (!folder) {
      // Changed error message
      return NextResponse.json({ error: 'Folder not found in this project' }, { status: 404 });
    }

    // Perform operations in a transaction
    await db.$transaction(async (prisma: any) => {
      // Unassociate all calculations currently in this folder
      await prisma.calculation.updateMany({
        where: {
          folderId: folderId,
          projectId: projectId, // Ensure we only affect calculations in the current project
        },
        data: {
          folderId: null,
        },
      });

      // Delete the folder
      await prisma.folder.delete({
        where: {
          id: folderId,
          // Optionally, you can add projectId here again for safety, 
          // but findFirst already confirmed it's in the project.
          // For delete, only 'id' is typically needed if it's unique.
        },
      });
    });

    return new NextResponse(null, { status: 204 }); 

  } catch (error) {
    console.error('[FOLDER_DELETE]', error);
    if ((error as any).code === 'P2025') { // Prisma error code for record not found on delete
        return NextResponse.json({ error: 'Failed to delete folder. It might have already been deleted.'}, { status: 404});
    }
    // Catch other potential errors during deletion
    return NextResponse.json({ error: 'Internal Server Error during folder deletion' }, { status: 500 });
  }
}

export async function GET(
  req: Request, 
  { params }: { params: { projectId: string; folderId: string } }
) {
  try {
    const { projectId, folderId } = params;

    if (!projectId) {
      return new NextResponse('Project ID is required', { status: 400 });
    }
    if (!folderId) {
      return new NextResponse('Folder ID is required', { status: 400 });
    }

    const folder = await db.folder.findUnique({
      where: {
        id: folderId,
        projectId: projectId,
      },
      include: {
        calculations: {
          orderBy: {
            order: 'asc',
          },
          include: {
            beamAnalysisResult: true, 
          },
        },
      },
    });

    if (!folder) {
      return new NextResponse('Folder not found or does not belong to this project', { status: 404 });
    }

    const processedCalculations = folder.calculations.map((calc: CalculationWithIncludedResult) => {
      let beamResultForFrontend = null;
      if (calc.beamAnalysisResult) {
        const br = calc.beamAnalysisResult; // Already typed as PrismaClient.BeamAnalysisResult | null
        const parseJsonSafe = (jsonString: string | null | undefined) => {
          if (!jsonString || typeof jsonString !== 'string') return jsonString; 
          try {
            return JSON.parse(jsonString);
          } catch (e) {
            console.error(`Failed to parse JSON for BeamAnalysisResult ${br.id}:`, e);
            return jsonString; 
          }
        };
        beamResultForFrontend = {
          ...br,
          name: br.name ?? calc.name ?? 'Unnamed Analysis', 
          calculationId: calc.id, 
          beamProperties: parseJsonSafe(br.beamProperties),
          loads: parseJsonSafe(br.loads),
          supports: parseJsonSafe(br.supports),
          results: parseJsonSafe(br.results),
          length: br.length ?? 0, 
          modulusOfElasticity: br.modulusOfElasticity ?? 0,
          momentOfInertia: br.momentOfInertia ?? 0,
        };
      }
      
      return {
        ...calc, 
        beamAnalysisResults: beamResultForFrontend ? [beamResultForFrontend] : [],
      };
    });

    return NextResponse.json({ ...folder, calculations: processedCalculations });

  } catch (error) {
    console.error('[GET_FOLDER_DETAILS_API]', error);
    if ((error as any).code && (error as any).clientVersion) { 
        console.error("Prisma Error Details:", JSON.stringify(error, null, 2));
        return new NextResponse(`Error fetching folder: ${(error as any).message || 'Database error'}`, { status: 500 });
    }
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { projectId: string; folderId: string } }
) {
  try {
    const { projectId, folderId } = params;
    if (!projectId || !folderId) {
      return NextResponse.json({ error: 'Project ID and Folder ID are required' }, { status: 400 });
    }

    const json = await request.json();
    const parsed = folderUpdateNameSchema.safeParse(json); // PUT is for full replacement, so name is required

    if (!parsed.success) {
      return NextResponse.json({ error: 'Invalid input for folder name', details: parsed.error.format() }, { status: 400 });
    }

    const { name } = parsed.data;

    const folderToUpdate = await db.folder.findFirst({
      where: {
        id: folderId,
        projectId: projectId,
      },
    });

    if (!folderToUpdate) {
      return NextResponse.json({ error: 'Folder not found in this project' }, { status: 404 });
    }

    if (name !== folderToUpdate.name) {
      const existingFolderWithNewName = await db.folder.findFirst({
        where: {
          projectId,
          name,
          id: { not: folderId },
        },
      });
      if (existingFolderWithNewName) {
        return NextResponse.json({ error: 'A folder with this name already exists in the project' }, { status: 409 });
      }
    }

    const updatedFolder = await db.folder.update({
      where: { id: folderId },
      data: { name: name },
    });

    return NextResponse.json(updatedFolder);

  } catch (error) {
    console.error('[FOLDER_PUT]', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 