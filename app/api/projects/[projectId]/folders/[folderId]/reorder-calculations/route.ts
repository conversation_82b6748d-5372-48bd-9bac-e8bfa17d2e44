import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { z } from 'zod';

const reorderSchema = z.object({
  calculations: z.array(
    z.object({
      id: z.string(),
      order: z.number(),
    })
  ),
});

export async function PUT(
  request: Request,
  { params }: { params: { projectId: string; folderId: string } }
) {
  try {
    const { projectId, folderId } = params;
    
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }
    
    if (!folderId) {
      return NextResponse.json({ error: 'Folder ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const validation = reorderSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: 'Invalid request body', details: validation.error.formErrors }, { status: 400 });
    }

    const { calculations } = validation.data;

    // Verify folder exists and belongs to the project
    const folder = await db.folder.findFirst({
      where: {
        id: folderId,
        projectId: projectId,
      },
    });

    if (!folder) {
      return NextResponse.json({ error: 'Folder not found or does not belong to project' }, { status: 404 });
    }

    // Ensure all calculations belong to the specified folder and project
    const folderCalculations = await db.calculation.findMany({
      where: { 
        projectId: projectId,
        folderId: folderId 
      },
      select: { id: true, result_id: true },
    });
    const folderCalculationIds = new Set(folderCalculations.map((calc: { id: string; result_id: any }) => calc.id));

    for (const calc of calculations) {
      if (!folderCalculationIds.has(calc.id)) {
        return NextResponse.json({ 
          error: `Calculation with id ${calc.id} does not belong to folder ${folderId} or does not exist.` 
        }, { status: 400 });
      }
    }
    
    // Use a transaction to update both calculation orders and beam analysis result orders within the folder
    const updatePromises = calculations.map((calc: { id: string; order: number }) =>
      db.calculation.update({
        where: { 
          id: calc.id, 
          projectId: projectId,
          folderId: folderId 
        },
        data: { order: calc.order },
      })
    );

    await db.$transaction(updatePromises);

    return NextResponse.json({ message: 'Calculations reordered successfully within folder' }, { status: 200 });
  } catch (error) {
    console.error('Error reordering calculations in folder:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request body', details: error.flatten() }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 