import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { z } from 'zod';

const reorderSchema = z.object({
  folders: z.array(
    z.object({
      id: z.string(),
      order: z.number(),
    })
  ),
});

export async function PUT(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  try {
    const projectId = params.projectId;
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const validation = reorderSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: 'Invalid request body', details: validation.error.formErrors }, { status: 400 });
    }

    const { folders } = validation.data;

    // Ensure all folders belong to the specified project
    const projectFolders = await db.folder.findMany({
      where: { projectId: projectId },
      select: { id: true },
    });
    const projectFolderIds = new Set(projectFolders.map((folder: { id: string }) => folder.id));

    for (const folder of folders) {
      if (!projectFolderIds.has(folder.id)) {
        return NextResponse.json({ 
          error: `Folder with id ${folder.id} does not belong to project ${projectId} or does not exist.` 
        }, { status: 400 });
      }
    }
    
    // Update folder orders
    const updatePromises = folders.map((folder: { id: string; order: number }) =>
      db.folder.update({
        where: { id: folder.id, projectId: projectId },
        data: { order: folder.order },
      })
    );

    await db.$transaction(updatePromises);

    return NextResponse.json({ message: 'Folders reordered successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error reordering folders:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request body', details: error.flatten() }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 