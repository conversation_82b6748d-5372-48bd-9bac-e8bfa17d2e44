import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
// import { adminAuth } from '@/lib/firebase-admin'; // Removed adminAuth
import { z } from 'zod';

const folderCreateSchema = z.object({
  name: z.string().min(1, { message: "Folder name cannot be empty" }),
});

export async function POST(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  try {
    // Authentication removed
    // const authorization = request.headers.get("Authorization");
    // if (!authorization || !authorization.startsWith("Bearer ")) {
    //   return NextResponse.json({ error: "Unauthorized: Missing or invalid token" }, { status: 401 });
    // }
    // const idToken = authorization.split("Bearer ")[1];
    // let decodedToken;
    // try {
    //   decodedToken = await adminAuth.verifyIdToken(idToken);
    // } catch (error) {
    //   console.error("Error verifying ID token:", error);
    //   return NextResponse.json({ error: "Unauthorized: Invalid token" }, { status: 401 });
    // }
    // const userId = decodedToken.uid;
    // if (!userId) {
    //   return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    // }

    const { projectId } = params;
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    const json = await request.json();
    const parsed = folderCreateSchema.safeParse(json);

    if (!parsed.success) {
      return NextResponse.json({ error: 'Invalid input', details: parsed.error.errors }, { status: 400 });
    }

    const { name } = parsed.data;

    // Verify project exists (user access check removed)
    const project = await db.project.findFirst({
      where: {
        id: projectId,
        // organization: { // User-specific access check removed
        //   memberships: {
        //     some: {
        //       userId: userId,
        //     },
        //   },
        // },
      },
    });

    if (!project) {
      // Changed error message to reflect that only project existence is checked now
      return NextResponse.json({ error: 'Project not found' }, { status: 404 }); 
    }

    // Check for existing folder with the same name in the project
    const existingFolder = await db.folder.findFirst({
      where: {
        projectId,
        name,
      },
    });

    if (existingFolder) {
      return NextResponse.json({ error: 'A folder with this name already exists in the project' }, { status: 409 });
    }

    const newFolder = await db.folder.create({
      data: {
        name,
        projectId,
      },
    });

    return NextResponse.json(newFolder, { status: 201 });
  } catch (error) {
    console.error('[FOLDERS_POST]', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 