import { NextResponse } from 'next/server';
import { db as prisma } from '@/lib/db';
// import type { Prisma } from '@prisma/client'; // Comment out problematic import for now

// type TransactionClient = Prisma.TransactionClient; // Comment out for now

export async function DELETE(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  const { projectId } = params;

  try {
    const body = await request.json();
    const { ids: beamAnalysisResultIds } = body as { ids: string[] }; 

    if (!beamAnalysisResultIds || !Array.isArray(beamAnalysisResultIds) || beamAnalysisResultIds.length === 0) {
      return NextResponse.json({ error: 'Invalid or empty BeamAnalysisResult IDs array provided.' }, { status: 400 });
    }
    if (!beamAnalysisResultIds.every((id: string) => typeof id === 'string')) {
      return NextResponse.json({ error: 'All BeamAnalysisResult IDs must be strings.' }, { status: 400 });
    }

    const calculationsToClean = await prisma.calculation.findMany({
      where: {
        result_id: {
          in: beamAnalysisResultIds,
        },
        projectId: projectId, 
      },
      select: {
        id: true, 
      },
    });
    
    const calculationIdsToDelete = calculationsToClean.map((calc: { id: string }) => calc.id);

    const transactionResults = await prisma.$transaction(async (tx: any) => { // Use any for tx for now
      const deletedBeamAnalysisResults = await tx.beamAnalysisResult.deleteMany({
        where: {
          id: {
            in: beamAnalysisResultIds,
          },
          projectId: projectId,
        },
      });

      let deletedCalculationsCount = 0;
      if (calculationIdsToDelete.length > 0) {
        const deletedCalculations = await tx.calculation.deleteMany({
          where: {
            id: {
              in: calculationIdsToDelete,
            },
            projectId: projectId, 
          },
        });
        deletedCalculationsCount = deletedCalculations.count;
        console.log(`[Bulk Delete] Deleted ${deletedCalculationsCount} Calculation records associated with the beam analyses.`);
      }
      
      return { deletedBeamAnalysisResults, deletedCalculationsCount };
    });

    if (transactionResults.deletedBeamAnalysisResults.count === 0 && (!calculationIdsToDelete.length || transactionResults.deletedCalculationsCount === 0) ) {
      console.warn(`Bulk delete attempt for project ${projectId} with BAR IDs [${beamAnalysisResultIds.join(', ')}] resulted in 0 deletions (neither BeamAnalysisResults nor associated Calculations).`);
    }
    
    return new NextResponse(null, { status: 204 });

  } catch (error: any) {
    console.error(`Error during bulk deletion for project ${projectId}:`, error);
    return NextResponse.json({ error: 'Internal server error during bulk deletion.' }, { status: 500 });
  }
} 