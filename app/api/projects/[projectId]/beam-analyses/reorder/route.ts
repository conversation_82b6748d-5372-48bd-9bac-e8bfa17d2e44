import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { z } from 'zod';

const reorderSchema = z.object({
  beamAnalyses: z.array(
    z.object({
      id: z.string(),
      order: z.number(),
    })
  ),
});

export async function PUT(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  try {
    const projectId = params.projectId;
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const validation = reorderSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: 'Invalid request body', details: validation.error.formErrors }, { status: 400 });
    }

    const { beamAnalyses } = validation.data;

    // Ensure all beam analyses belong to the specified project
    const projectAnalyses = await db.beamAnalysisResult.findMany({
      where: { projectId: projectId },
      select: { id: true },
    });
    const projectAnalysisIds = new Set(projectAnalyses.map(analysis => analysis.id));

    for (const analysis of beamAnalyses) {
      if (!projectAnalysisIds.has(analysis.id)) {
        return NextResponse.json({ error: `Beam analysis with id ${analysis.id} does not belong to project ${projectId} or does not exist.` }, { status: 400 });
      }
    }
    
    const updatePromises = beamAnalyses.map((analysis) =>
      db.beamAnalysisResult.update({
        where: { id: analysis.id, projectId: projectId },
        data: { order: analysis.order },
      })
    );

    await db.$transaction(updatePromises);

    return NextResponse.json({ message: 'Beam analyses reordered successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error reordering beam analyses:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request body', details: error.flatten() }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 