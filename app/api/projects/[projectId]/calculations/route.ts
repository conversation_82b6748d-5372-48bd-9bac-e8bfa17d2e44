import { NextResponse } from 'next/server';
import { db as prisma } from '@/lib/db'; // Corrected import path for Prisma client

export async function GET(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  const { projectId } = params;
  const { searchParams } = new URL(request.url);
  const calculatorTypeFromQuery = searchParams.get('calculatorType'); // Renamed to avoid confusion

  if (!projectId) {
    return NextResponse.json(
      { error: 'Project ID is required' },
      { status: 400 }
    );
  }

  try {
    // Use a more specific type for whereClause if possible, or ensure correct field names.
    const whereClause: { projectId: string; type?: string } = {
      projectId: projectId,
    };

    if (calculatorTypeFromQuery) {
      // CORRECTED: Use 'type' which is the actual field name in the Calculation model
      whereClause.type = calculatorTypeFromQuery;
    }

    const calculations = await prisma.calculation.findMany({
      where: {
        ...whereClause,
        // Ensure that only calculations with an existing BeamAnalysisResult are returned
        beamAnalysisResult: {
          isNot: null,
        },
      },
      orderBy: {
        createdAt: 'desc', // Optional: order by creation date or name
      },
      // If you need the name from BeamAnalysisResult for display, consider including it:
      // include: { beamAnalysisResult: { select: { name: true, id: true } } }
    });

    // If using include, you might need to map the results to the expected structure
    // For example, if the frontend expects CalculationSummary { id: string, name: string | null }
    // const calculationSummaries = calculations.map(calc => ({
    //   id: calc.id,
    //   name: calc.beamAnalysisResult?.name || calc.name || `Calculation ${calc.id.substring(0,5)}` 
    // }));
    // return NextResponse.json(calculationSummaries, { status: 200 });

    return NextResponse.json(calculations, { status: 200 });

  } catch (error) {
    console.error(`Error fetching calculations for project ${projectId}:`, error);
    // Check for Prisma-specific errors if needed, e.g., P2025 for record not found
    if ((error as any).code === 'P2025') {
        return NextResponse.json(
        { error: `Project with ID ${projectId} not found or no calculations match criteria.` },
        { status: 404 }
      );
    }
    return NextResponse.json(
      { error: 'Internal Server Error fetching calculations' },
      { status: 500 }
    );
  }
} 