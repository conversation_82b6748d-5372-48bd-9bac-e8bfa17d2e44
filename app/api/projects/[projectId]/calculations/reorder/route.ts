import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { z } from 'zod';

const reorderSchema = z.object({
  calculations: z.array(
    z.object({
      id: z.string(),
      order: z.number(),
    })
  ),
});

export async function PUT(
  request: Request,
  { params }: { params: { projectId: string } }
) {
  try {
    const projectId = params.projectId;
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const validation = reorderSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ error: 'Invalid request body', details: validation.error.formErrors }, { status: 400 });
    }

    const { calculations } = validation.data;

    // Ensure all calculations belong to the specified project and are at project root (not in folders)
    const projectRootCalculations = await db.calculation.findMany({
      where: { 
        projectId: projectId,
        folderId: null // Only calculations at project root
      },
      select: { id: true, result_id: true },
    });
    const projectRootCalculationIds = new Set(projectRootCalculations.map((calc: { id: string; result_id: any }) => calc.id));

    for (const calc of calculations) {
      if (!projectRootCalculationIds.has(calc.id)) {
        return NextResponse.json({ 
          error: `Calculation with id ${calc.id} does not belong to project root or does not exist.` 
        }, { status: 400 });
      }
    }
    
    // Use a transaction to update both calculation orders and beam analysis result orders at project root
    const updatePromises = calculations.map((calc: { id: string; order: number }) =>
      db.calculation.update({
        where: { id: calc.id, projectId: projectId },
        data: { order: calc.order },
      })
    );

    await db.$transaction(updatePromises);

    return NextResponse.json({ message: 'Calculations reordered successfully at project root' }, { status: 200 });
  } catch (error) {
    console.error('Error reordering calculations:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request body', details: error.flatten() }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 