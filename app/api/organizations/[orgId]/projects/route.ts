import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: Request, { params }: { params: { orgId: string } }) {
  try {
    const { orgId } = params;
    const body = await request.json();
    const { title, description, status } = body;

    if (!orgId || !title) {
      return NextResponse.json({ error: 'Organization ID and project title are required' }, { status: 400 });
    }

    const newProject = await prisma.project.create({
      data: {
        title,
        description,
        status: status || 'Planning', // Default status if not provided
        organizationId: orgId,
      },
    });

    return NextResponse.json(newProject, { status: 201 });
  } catch (error) {
    console.error('Error creating project:', error);
    if ((error as any).code === 'P2003') {
        return NextResponse.json({ error: 'Organization not found or invalid organizationId' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Failed to create project' }, { status: 500 });
  }
}

export async function DELETE(request: Request, { params }: { params: { orgId: string } }) {
  try {
    const { orgId } = params;
    const body = await request.json();
    const { projectIds } = body;

    if (!orgId || !projectIds || !Array.isArray(projectIds) || projectIds.length === 0) {
      return NextResponse.json({ error: 'Organization ID and a non-empty array of project IDs are required' }, { status: 400 });
    }

    // Ensure all project IDs are valid strings (basic validation)
    if (!projectIds.every(id => typeof id === 'string')) {
        return NextResponse.json({ error: 'All project IDs must be strings' }, { status: 400 });
    }

    const deleteResult = await prisma.project.deleteMany({
      where: {
        id: {
          in: projectIds,
        },
        organizationId: orgId, // Ensure projects belong to the specified organization
      },
    });

    if (deleteResult.count === 0) {
      // This could mean projects were not found or didn't belong to the org.
      // Depending on desired behavior, you might return 404 or just a success with count 0.
      console.warn(`No projects deleted for org ${orgId} with projectIds: ${projectIds.join(', ')}. They might not exist or belong to another organization.`);
    }

    return NextResponse.json({ message: `${deleteResult.count} project(s) deleted successfully.` }, { status: 200 });
  } catch (error) {
    console.error('Error deleting projects:', error);
    return NextResponse.json({ error: 'Failed to delete projects' }, { status: 500 });
  }
}
