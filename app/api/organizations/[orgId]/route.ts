import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface UpdateOrgData {
  name?: string;
  address?: string;
  phone?: string;
}

// GET an organization by ID - Not strictly required for this task, but good practice for a RESTful API
export async function GET(request: Request, { params }: { params: { orgId: string } }) {
  const { orgId } = params;
  try {
    const organization = await prisma.organization.findUnique({
      where: { id: orgId },
      include: { projects: true }, // Include projects if needed
    });
    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }
    return NextResponse.json(organization);
  } catch (error) {
    console.error("Failed to fetch organization:", error);
    return NextResponse.json({ error: 'Failed to fetch organization' }, { status: 500 });
  }
}

// PUT update an organization by ID
export async function PUT(request: Request, { params }: { params: { orgId: string } }) {
  const { orgId } = params;
  try {
    const body = await request.json() as UpdateOrgData;
    const { name, address, phone } = body;

    // Basic validation
    if (name !== undefined && typeof name !== 'string') {
        return NextResponse.json({ error: 'Invalid name format' }, { status: 400 });
    }
    if (address !== undefined && typeof address !== 'string') {
        return NextResponse.json({ error: 'Invalid address format' }, { status: 400 });
    }
     if (phone !== undefined && typeof phone !== 'string') {
        return NextResponse.json({ error: 'Invalid phone format' }, { status: 400 });
    }

    // Construct update data, only including fields that are present in the request
    const updateData: UpdateOrgData = {};
    if (name !== undefined) updateData.name = name;
    if (address !== undefined) updateData.address = address; // Allow empty string to clear address
    if (phone !== undefined) updateData.phone = phone; // Allow empty string to clear phone

    if (Object.keys(updateData).length === 0) {
        return NextResponse.json({ error: 'No update data provided' }, { status: 400 });
    }

    const updatedOrganization = await prisma.organization.update({
      where: { id: orgId },
      data: updateData,
      include: { projects: true }, // Return projects with the updated org
    });

    return NextResponse.json(updatedOrganization);

  } catch (error: any) {
    console.error("Failed to update organization:", error);
    if (error.code === 'P2025') { // Prisma error code for record not found
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }
    return NextResponse.json({ error: 'Failed to update organization' }, { status: 500 });
  }
}

// DELETE an organization by ID
export async function DELETE(request: Request, { params }: { params: { orgId: string } }) {
    const { orgId } = params;
    try {
        // Optional: Add logic to handle related entities, e.g., delete or disassociate projects
        // For now, we assume cascading delete is set up in Prisma schema or handle it manually

        // First, delete related projects if your schema doesn't cascade deletes or if you need specific logic
        await prisma.project.deleteMany({
            where: { organizationId: orgId },
        });

        // Then, delete the organization
        const deletedOrganization = await prisma.organization.delete({
            where: { id: orgId },
        });

        return NextResponse.json(deletedOrganization);
    } catch (error: any) {
        console.error("Failed to delete organization:", error);
        if (error.code === 'P2025') { // Prisma error code for record not found
            return NextResponse.json({ error: 'Organization not found to delete' }, { status: 404 });
        }
        return NextResponse.json({ error: 'Failed to delete organization' }, { status: 500 });
    }
} 