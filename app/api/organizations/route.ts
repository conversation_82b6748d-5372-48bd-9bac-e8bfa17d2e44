import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/organizations - Fetch all organizations
export async function GET() {
  try {
    const organizations = await prisma.organization.findMany({
      include: {
        projects: true, // Include related projects, adjust as needed for performance later
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });
    return NextResponse.json(organizations);
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return NextResponse.json(
      { error: "Failed to fetch organizations" },
      { status: 500 }
    );
  }
}

// POST /api/organizations - Create a new organization
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, address, phone } = body;

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    const newOrganization = await prisma.organization.create({
      data: {
        name,
        address: address || null,
        phone: phone || null,
      },
      include: {
        projects: true, // Return projects with the new org (will be empty initially)
      }
    });

    return NextResponse.json(newOrganization, { status: 201 });
  } catch (error) {
    console.error("Error creating organization:", error);
    // Consider more specific error handling, e.g., for unique constraint violations if 'name' should be unique
    return NextResponse.json(
      { error: "Failed to create organization" },
      { status: 500 }
    );
  }
}

// Simple cleanup for Prisma client instance in dev environment
// In a real production app, you might manage this differently.
if (process.env.NODE_ENV !== 'production') {
  // @ts-ignore
  if (!global.prisma) {
    // @ts-ignore
    global.prisma = prisma;
  }
} 