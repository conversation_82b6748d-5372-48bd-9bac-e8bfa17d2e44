import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

interface SteelShape {
  Type: string;
  EDI_Std_Nomenclature: string;
  Version: string;
  [key: string]: string;
}

interface ShapeResponse {
  Type: string;
  EDI_Std_Nomenclature: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const version = searchParams.get('version') || 'AISC Shapes Database v16.0';
    
    // Read the CSV file
    const csvFilePath = path.join(process.cwd(), 'lib', 'tables', 'steel', 'aisc-shapes-database.csv');
    const csvData = fs.readFileSync(csvFilePath, 'utf8');
    
    // Parse CSV data
    const results: SteelShape[] = [];
    
    return new Promise<NextResponse>((resolve) => {
      const readable = Readable.from([csvData]);
      
      readable
        .pipe(csv())
        .on('data', (data: SteelShape) => {
          results.push(data);
        })
        .on('end', () => {
          console.log(`Total rows parsed: ${results.length}`);
          console.log(`Looking for version: "${version}"`);
          
          // Debug: Check first few rows to see what versions we have
          const sampleVersions = results.slice(0, 5).map(row => `"${row.Version}"`);
          console.log('Sample versions from first 5 rows:', sampleVersions);
          
          // Filter by version and extract Type and EDI_Std_Nomenclature
          const filteredData = results
            .filter((row: SteelShape) => {
              const matches = row.Version && row.Version.trim() === version.trim();
              return matches;
            })
            .map((row: SteelShape): ShapeResponse => ({
              Type: row.Type,
              EDI_Std_Nomenclature: row.EDI_Std_Nomenclature,
            }));
          
          console.log(`Filtered rows: ${filteredData.length}`);
          
          // Remove duplicates if any
          const uniqueShapes = filteredData.filter((shape: ShapeResponse, index: number, self: ShapeResponse[]) =>
            index === self.findIndex((s: ShapeResponse) =>
              s.Type === shape.Type && s.EDI_Std_Nomenclature === shape.EDI_Std_Nomenclature
            )
          );
          
          resolve(NextResponse.json({
            version,
            shapes: uniqueShapes,
            count: uniqueShapes.length,
          }));
        })
        .on('error', (error: Error) => {
          console.error('CSV parsing error:', error);
          resolve(NextResponse.json(
            { error: 'Error parsing CSV file' },
            { status: 500 }
          ));
        });
    });
    
  } catch (error) {
    console.error('Error reading shapes data:', error);
    return NextResponse.json(
      { error: 'Failed to read shapes data' },
      { status: 500 }
    );
  }
} 