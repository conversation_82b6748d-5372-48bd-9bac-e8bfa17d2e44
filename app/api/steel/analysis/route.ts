import { NextRequest, NextResponse } from 'next/server';
import { 
  getDesignCheckResults, 
  BeamAnalysisInputs,
  DesignCheckResults
} from '@/lib/utils/steel/steel-beam-orchestrator';
import { Section } from '@/lib/utils/steel/slenderness/types';
import { Material, DesignMethod, SupportedSectionType } from '@/lib/utils/steel/constants';

// Interface for the API request body
interface SteelBeamAnalysisRequest {
  // Section properties - aligned with the Section interface
  section: {
    name: string;
    Type: SupportedSectionType;  // Must be one of the supported section types
    A: number;      // Cross-sectional area (in²)
    d: number;      // Depth (in)
    bf: number;     // Flange width (in)
    tw: number;     // Web thickness (in)
    tf: number;     // Flange thickness (in)
    // Optional properties that may be required for advanced calculations
    Ix?: number;    // Strong-axis moment of inertia (in⁴)
    Iy?: number;    // Weak-axis moment of inertia (in⁴)
    Sx?: number;    // Strong-axis elastic section modulus (in³)
    Sy?: number;    // Weak-axis elastic section modulus (in³)
    Zx?: number;    // Strong-axis plastic section modulus (in³)
    Zy?: number;    // Weak-axis plastic section modulus (in³)
    rx?: number;    // Strong-axis radius of gyration (in)
    ry?: number;    // Weak-axis radius of gyration (in)
    J?: number;     // Torsional constant (in⁴)
    Cw?: number;    // Warping constant (in⁶)
  };
  
  // Material properties
  material: {
    Fy: number;     // Yield strength (ksi)
    Fu: number;     // Ultimate strength (ksi)
    grade?: string; // Steel grade (e.g., "ASTM A992", "ASTM A572 Gr 50")
  };
  
  // Lateral-torsional buckling parameters
  ltbParameters: {
    Lb: number;     // Unbraced length (in)
    Cb?: number;    // Lateral-torsional buckling modification factor (default 1.0)
  };
  
  // Demand forces
  demandForces: {
    Mu: number;     // Required flexural strength (moment) (kip-in)
    Vu: number;     // Required shear strength (kips)
    Pu?: number;    // Required axial strength (kips) - optional
  };
  
  // Design parameters
  designMethod: 'LRFD' | 'ASD';              // Design method
  applyStiffnessAdjustments?: boolean;        // Whether to apply AISC 360-22 Section C2.3 adjustments
  nominalAxialStrength?: number;              // Pn for stiffness adjustments (kips)
}

// Interface for API response
interface SteelBeamAnalysisResponse {
  success: boolean;
  data?: DesignCheckResults;
  error?: string;
  warnings?: string[];
}

// Validation helper functions
function validateSection(section: any): string[] {
  const errors: string[] = [];
  const requiredProps = ['A', 'd', 'tw'];
  const optionalProps = ['bf', 'tf', 'Ix', 'Iy', 'Sx', 'Sy', 'Zx', 'Zy', 'rx', 'ry', 'J', 'Cw'];
  const validSectionTypes: SupportedSectionType[] = ['W', 'I', 'S', 'M', 'C', 'MC', 'HSS-RECT', 'HSS-ROUND', 'L', '2L'];
  
  // Check required properties
  for (const prop of requiredProps) {
    if (typeof section[prop] !== 'number' || isNaN(section[prop]) || section[prop] < 0) {
      errors.push(`Section property '${prop}' must be a non-negative number`);
    }
  }
  
  // Check optional properties if provided
  for (const prop of optionalProps) {
    if (section[prop] !== undefined && (typeof section[prop] !== 'number' || isNaN(section[prop]) || section[prop] < 0)) {
      errors.push(`Section property '${prop}' must be a non-negative number if provided`);
    }
  }
  
  if (!section.name || typeof section.name !== 'string') {
    errors.push('Section name is required and must be a string');
  }
  
  if (!section.Type || !validSectionTypes.includes(section.Type)) {
    errors.push(`Section Type must be one of: ${validSectionTypes.join(', ')}`);
  }
  
  return errors;
}

function validateMaterial(material: any): string[] {
  const errors: string[] = [];
  
  if (typeof material.Fy !== 'number' || isNaN(material.Fy) || material.Fy <= 0) {
    errors.push('Material Fy must be a positive number');
  }
  
  if (typeof material.Fu !== 'number' || isNaN(material.Fu) || material.Fu <= 0) {
    errors.push('Material Fu must be a positive number');
  }
  
  if (material.Fy && material.Fu && material.Fy >= material.Fu) {
    errors.push('Material Fy must be less than Fu');
  }
  
  return errors;
}

function validateDemandForces(demandForces: any): string[] {
  const errors: string[] = [];
  
  if (typeof demandForces.Mu !== 'number' || isNaN(demandForces.Mu) || demandForces.Mu < 0) {
    errors.push('Demand moment Mu must be a non-negative number');
  }
  
  if (typeof demandForces.Vu !== 'number' || isNaN(demandForces.Vu) || demandForces.Vu < 0) {
    errors.push('Demand shear Vu must be a non-negative number');
  }
  
  if (demandForces.Pu !== undefined && (typeof demandForces.Pu !== 'number' || isNaN(demandForces.Pu) || demandForces.Pu < 0)) {
    errors.push('Demand axial force Pu must be a non-negative number if provided');
  }
  
  return errors;
}

function validateLTBParameters(ltbParameters: any): string[] {
  const errors: string[] = [];
  
  if (typeof ltbParameters.Lb !== 'number' || isNaN(ltbParameters.Lb) || ltbParameters.Lb <= 0) {
    errors.push('Unbraced length Lb must be a positive number');
  }
  
  if (ltbParameters.Cb !== undefined && (typeof ltbParameters.Cb !== 'number' || isNaN(ltbParameters.Cb) || ltbParameters.Cb < 1.0)) {
    errors.push('Lateral-torsional buckling modification factor Cb must be >= 1.0 if provided');
  }
  
  return errors;
}

export async function POST(request: NextRequest) {
  try {
    const body: SteelBeamAnalysisRequest = await request.json();
    
    // Validate input structure
    if (!body) {
      return NextResponse.json(
        { success: false, error: 'Request body is required' },
        { status: 400 }
      );
    }
    
    // Validate required sections
    const validationErrors: string[] = [];
    
    if (!body.section) {
      validationErrors.push('Section properties are required');
    } else {
      validationErrors.push(...validateSection(body.section));
    }
    
    if (!body.material) {
      validationErrors.push('Material properties are required');
    } else {
      validationErrors.push(...validateMaterial(body.material));
    }
    
    if (!body.ltbParameters) {
      validationErrors.push('Lateral-torsional buckling parameters are required');
    } else {
      validationErrors.push(...validateLTBParameters(body.ltbParameters));
    }
    
    if (!body.demandForces) {
      validationErrors.push('Demand forces are required');
    } else {
      validationErrors.push(...validateDemandForces(body.demandForces));
    }
    
    if (!body.designMethod || !['LRFD', 'ASD'].includes(body.designMethod)) {
      validationErrors.push('Design method must be either "LRFD" or "ASD"');
    }
    
    // Validate stiffness adjustment parameters
    if (body.applyStiffnessAdjustments) {
      if (typeof body.nominalAxialStrength !== 'number' || body.nominalAxialStrength <= 0) {
        validationErrors.push('Nominal axial strength Pn is required and must be positive when applying stiffness adjustments');
      }
      if (body.demandForces.Pu === undefined) {
        validationErrors.push('Demand axial force Pu is required when applying stiffness adjustments');
      }
    }
    
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Validation errors', details: validationErrors },
        { status: 400 }
      );
    }
    
    // Convert request to BeamAnalysisInputs format
    const section: Section = {
      name: body.section.name,
      Type: body.section.Type,
      A: body.section.A,
      d: body.section.d,
      bf: body.section.bf,
      tw: body.section.tw,
      tf: body.section.tf,
      Ix: body.section.Ix,
      Iy: body.section.Iy,
      Sx: body.section.Sx,
      Sy: body.section.Sy,
      Zx: body.section.Zx,
      Zy: body.section.Zy,
      rx: body.section.rx,
      ry: body.section.ry,
      J: body.section.J,
      Cw: body.section.Cw,
    };
    
    const material: Material = {
      Fy: body.material.Fy,
      Fu: body.material.Fu,
    };
    
    const analysisInputs: BeamAnalysisInputs = {
      section,
      material,
      ltbParameters: {
        Lb: body.ltbParameters.Lb,
        Cb: body.ltbParameters.Cb || 1.0,
      },
      demandForces: {
        Mu: body.demandForces.Mu,
        Vu: body.demandForces.Vu,
        Pu: body.demandForces.Pu,
      },
      designMethod: body.designMethod === 'LRFD' ? DesignMethod.LRFD : DesignMethod.ASD,
      applyStiffnessAdjustments: body.applyStiffnessAdjustments || false,
      nominalAxialStrength: body.nominalAxialStrength,
    };
    
    // Perform the analysis
    const results = getDesignCheckResults(analysisInputs);
    
    // Generate warnings if applicable
    const warnings: string[] = [];
    
    if (results.flexure.dcr > 0.95 && results.flexure.dcr <= 1.0) {
      warnings.push('Flexural design is near capacity (DCR > 0.95)');
    }
    
    if (results.shear.dcr > 0.95 && results.shear.dcr <= 1.0) {
      warnings.push('Shear design is near capacity (DCR > 0.95)');
    }
    
    if (results.stiffnessAdjustments.applied) {
      warnings.push('Stiffness adjustments per AISC 360-22 Section C2.3 have been applied');
    }
    
    // Return successful response
    const response: SteelBeamAnalysisResponse = {
      success: true,
      data: results,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Steel beam analysis error:', error);
    
    // Handle specific calculation errors
    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      );
    }
    
    // Generic server error
    return NextResponse.json(
      { success: false, error: 'Internal server error during steel beam analysis' },
      { status: 500 }
    );
  }
}

// GET endpoint for API documentation/health check
export async function GET() {
  const apiInfo = {
    endpoint: '/api/steel/analysis',
    method: 'POST',
    description: 'Comprehensive steel beam design check using AISC 360-22',
    features: [
      'Flexural strength calculation (yielding, lateral-torsional buckling, local buckling)',
      'Shear strength calculation (yielding, buckling)',
      'LRFD and ASD design methods',
      'Stiffness adjustments per AISC 360-22 Section C2.3',
      'Comprehensive validation and error handling',
      'Support for all major steel section types (W, I, S, M, C, MC, HSS)',
    ],
    requiredParameters: [
      'section: Section properties (name, Type, A, d, bf, tw, tf)',
      'material: Steel material properties (Fy, Fu)',
      'ltbParameters: Lateral-torsional buckling parameters (Lb, optional Cb)',
      'demandForces: Applied forces (Mu, Vu, optional Pu)',
      'designMethod: Either "LRFD" or "ASD"',
    ],
    optionalParameters: [
      'section: Additional section properties (Ix, Iy, Sx, Sy, Zx, Zy, rx, ry, J, Cw)',
      'applyStiffnessAdjustments: Apply AISC 360-22 stiffness adjustments',
      'nominalAxialStrength: Required if applying stiffness adjustments',
    ],
    response: {
      success: 'boolean',
      data: 'DesignCheckResults object with flexural and shear analysis',
      error: 'Error message if analysis fails',
      warnings: 'Array of warning messages',
    },
    sampleRequest: {
      section: {
        name: "W24X68",
        Type: "W",
        A: 20.1,
        d: 23.7,
        bf: 8.965,
        tw: 0.415,
        tf: 0.585,
        Ix: 1830,
        Zx: 177,
        Sx: 154,
        rx: 9.55,
      },
      material: {
        Fy: 50,
        Fu: 65,
        grade: "ASTM A992"
      },
      ltbParameters: {
        Lb: 120,
        Cb: 1.0
      },
      demandForces: {
        Mu: 5000,
        Vu: 50
      },
      designMethod: "LRFD"
    }
  };
  
  return NextResponse.json(apiInfo);
} 