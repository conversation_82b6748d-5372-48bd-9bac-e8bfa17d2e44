import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

interface SteelGrade {
  astm_designation: string;
  grade: string;
  yield_stress_fy_ksi: string;
  tensile_stress_fu_ksi: string;
  table: string;
}

export async function GET(request: NextRequest) {
  try {
    // Read the CSV file
    const csvFilePath = path.join(process.cwd(), 'lib', 'tables', 'steel', 'ASTM_Grade_stress_table.csv');
    const csvData = fs.readFileSync(csvFilePath, 'utf8');
    
    // Parse CSV data
    const results: SteelGrade[] = [];
    
    return new Promise<NextResponse>((resolve) => {
      const readable = Readable.from([csvData]);
      
      readable
        .pipe(csv())
        .on('data', (data: SteelGrade) => {
          results.push(data);
        })
        .on('end', () => {
          resolve(NextResponse.json({
            success: true,
            count: results.length,
            data: results,
          }));
        })
        .on('error', (error: Error) => {
          console.error('CSV parsing error:', error);
          resolve(NextResponse.json(
            { error: 'Error parsing CSV file' },
            { status: 500 }
          ));
        });
    });
    
  } catch (error) {
    console.error('Error reading steel grades:', error);
    return NextResponse.json(
      { error: 'Failed to read steel grades data' },
      { status: 500 }
    );
  }
} 