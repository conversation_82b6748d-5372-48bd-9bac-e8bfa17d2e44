import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

interface SteelShape {
  Type: string;
  EDI_Std_Nomenclature: string;
  Version: string;
  [key: string]: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const nomenclature = searchParams.get('nomenclature');
    const version = searchParams.get('version') || 'AISC Shapes Database v16.0';
    
    // Validate required parameters
    if (!type || !nomenclature) {
      return NextResponse.json(
        { error: 'Missing required parameters: type and nomenclature' },
        { status: 400 }
      );
    }
    
    // Read the CSV file
    const csvFilePath = path.join(process.cwd(), 'lib', 'tables', 'steel', 'aisc-shapes-database.csv');
    const csvData = fs.readFileSync(csvFilePath, 'utf8');
    
    // Parse CSV data
    const results: SteelShape[] = [];
    
    return new Promise<NextResponse>((resolve) => {
      const readable = Readable.from([csvData]);
      
      readable
        .pipe(csv())
        .on('data', (data: SteelShape) => {
          results.push(data);
        })
        .on('end', () => {
          // Find the specific shape
          const shape = results.find((row: SteelShape) => 
            row.Type === type && 
            row.EDI_Std_Nomenclature === nomenclature && 
            row.Version?.trim() === version.trim()
          );
          
          if (!shape) {
            resolve(NextResponse.json(
              { error: 'Shape not found' },
              { status: 404 }
            ));
            return;
          }
          
          // Return all section properties (excluding Version as it's metadata)
          const { Version: _, ...sectionProperties } = shape;
          
          resolve(NextResponse.json({
            type,
            nomenclature,
            version,
            sectionProperties,
          }));
        })
        .on('error', (error: Error) => {
          console.error('CSV parsing error:', error);
          resolve(NextResponse.json(
            { error: 'Error parsing CSV file' },
            { status: 500 }
          ));
        });
    });
    
  } catch (error) {
    console.error('Error reading section properties:', error);
    return NextResponse.json(
      { error: 'Failed to read section properties' },
      { status: 500 }
    );
  }
} 