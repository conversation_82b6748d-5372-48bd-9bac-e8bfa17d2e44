import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';
import {
  DEFAULT_NDS_VERSION,
  ERROR_MESSAGES,
  HTTP_STATUS,
  CACHE_DURATION_MS,
} from '@/lib/constants/nds-constants';

interface Table5BRow {
  combination_symbol: string;
  species_group: string;
  Grade: string; // Or 'grade' if normalized
  E_axial_ksi: number;
  E_axial_ksi_095: number; // Or E_axial_ksi_0_95
  Eaxial_min: number; // Header in CSV is "Eaxial  min" - will be normalized
  perp_to_grain_Fc_perp_psi: number; // Will be normalized to Fc_perp_psi for consistency if desired, or used as is
  Ft_psi: number;
  Fc_psi_4_or_more_laminations: number;
  Fc_psi_2_or_3_laminations: number;
  Fby_psi_4_or_more_laminations: number;
  Fby_psi_3_laminations: number;
  Fby_psi_2_laminations: number;
  Fvy_psi: number;
  Fbx_psi_2_laminations_to_15_in_deep: number;
  Fvx_psi: number;
  G: number; // Specific Gravity
  table: string;
  version: string;
  wet_service_factor_Cm_for_Fb: number;
  wet_service_factor_Cm_for_Ft: number;
  wet_service_factor_Cm_for_Fv: number;
  wet_service_factor_Cm_for_Fc_perp: number; // For 'wet_service_factor_Cm_for_Fc⊥'
  wet_service_factor_Cm_for_Fc: number;
  wet_service_factor_Cm_for_E_and_Emin: number;
  Notes?: string; // Assuming Notes might be an optional column, add if not present in actual CSV
}

// --- In-memory cache for Table5B ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}

let table5bCache: Cache<Table5BRow[]> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Table5B ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || DEFAULT_NDS_VERSION;
  // --- Cache check for Table5B ---
  const now = Date.now();
  if (table5bCache.data && table5bCache.expires && table5bCache.expires > now) {
    console.log("Serving Table5B data from cache.");
    // Filter by version before returning cached data
    const filteredData = table5bCache.data.filter(row => row.version === version);
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh Table5B data.");
  // --- End cache check for Table5B ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'glulam', 'Table5B.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      console.error('File access error for Table5B.csv:', error);
      return NextResponse.json(
        { error: ERROR_MESSAGES.WOOD_DATA_NOT_FOUND },
        { 
          status: HTTP_STATUS.NOT_FOUND,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);
    const results: Table5BRow[] = [];

    const numericKeys: Array<keyof Table5BRow> = [
      'E_axial_ksi', 'E_axial_ksi_095', 'Eaxial_min', 
      'perp_to_grain_Fc_perp_psi', // Or the normalized version if you change it
      'Ft_psi', 'Fc_psi_4_or_more_laminations', 'Fc_psi_2_or_3_laminations',
      'Fby_psi_4_or_more_laminations', 'Fby_psi_3_laminations', 'Fby_psi_2_laminations',
      'Fvy_psi', 'Fbx_psi_2_laminations_to_15_in_deep', 'Fvx_psi', 'G',
      'wet_service_factor_Cm_for_Fb', 'wet_service_factor_Cm_for_Ft',
      'wet_service_factor_Cm_for_Fv', 'wet_service_factor_Cm_for_Fc_perp',
      'wet_service_factor_Cm_for_Fc', 'wet_service_factor_Cm_for_E_and_Emin'
    ];
    const optionalStringKeys: Array<keyof Table5BRow> = ['Notes'];

    return new Promise<NextResponse>((resolve, reject) => {
      stream
        .pipe(csv({
          mapHeaders: ({ header }) => {
            let normalizedHeader = header.trim().replace(/\\s+/g, '_'); // Handles "Eaxial  min"
            // Normalize "perp_to_grain_Fc_perp_psi" to "Fc_perp_psi" for consistency with Table5A, or keep as is
            // For now, let's assume we want to keep it closer to the CSV for Table5B's specific case
            // normalizedHeader = normalizedHeader.replace('perp_to_grain_Fc_perp_psi', 'Fc_perp_psi'); 
            normalizedHeader = normalizedHeader.replace('wet_service_factor_Cm_for_Fc⊥', 'wet_service_factor_Cm_for_Fc_perp');
            normalizedHeader = normalizedHeader.replace('E_axial_ksi_0.95', 'E_axial_ksi_095'); // Handle if dot becomes underscore
            return normalizedHeader;
          }
        }))
        .on('data', (dataFromCsv) => {
          const processedRow = {} as Table5BRow;

          for (const keyFromCsv in dataFromCsv) {
            const key = keyFromCsv as keyof Table5BRow; 
            const rawValue = dataFromCsv[keyFromCsv];
            const valueString = String(rawValue || '').trim();

            if (numericKeys.includes(key)) {
              if (valueString === '' || valueString.toLowerCase() === 'na' || valueString.toLowerCase() === 'n/a') {
                (processedRow as any)[key] = NaN; 
              } else {
                (processedRow as any)[key] = parseFloat(valueString.replace(',', ''));
              }
            } else if (optionalStringKeys.includes(key)) {
              if (valueString === '' || valueString.toLowerCase() === 'na' || valueString.toLowerCase() === 'n/a') {
                (processedRow as any)[key] = undefined;
              } else {
                (processedRow as any)[key] = valueString;
              }
            } else { 
              (processedRow as any)[key] = valueString;
            }
          }
          results.push(processedRow);
        })
        .on('end', () => {
          // --- Cache update for Table5B ---
          table5bCache.data = results;
          table5bCache.expires = Date.now() + CACHE_DURATION_MS.WOOD_DATA;
          console.log("Table5B data cached.");
          // --- End cache update for Table5B ---
          
          // Filter by version before returning
          const filteredResults = results.filter(row => row.version === version);
          resolve(NextResponse.json(filteredResults, {
            headers: { 
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=3600',
            },
          }));
        })
        .on('error', (error) => {
          console.error('CSV parsing error for Table5B.csv:', error);
          reject(NextResponse.json(
            { error: ERROR_MESSAGES.WOOD_DATA_PARSE_FAILED, details: error.message },
            { 
              status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
              headers: { 'Content-Type': 'application/json' },
            }
          ));
        });
    });
  } catch (error) {
    console.error('Overall GET request error for Table5B:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json(
      { error: ERROR_MESSAGES.WOOD_DATA_LOAD_FAILED, details: errorMessage },
      { 
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
} 