import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';
import {
  DEFAULT_NDS_VERSION,
  ERROR_MESSAGES,
  HTTP_STATUS,
  CACHE_DURATION_MS,
} from '@/lib/constants/nds-constants';

// Helper function to parse strings like "8-1/4" or "10" or "1/2" to number for sorting
function fractionalStringToNumber(value: string): number {
  if (!value || typeof value !== 'string') {
    return 0;
  }
  value = value.trim();
  if (value.includes('-') && value.includes('/')) {
    const parts = value.split('-');
    const whole = parseInt(parts[0], 10);
    const fractionParts = parts[1].split('/');
    const numerator = parseInt(fractionParts[0], 10);
    const denominator = parseInt(fractionParts[1], 10);
    if (isNaN(whole) || isNaN(numerator) || isNaN(denominator) || denominator === 0) {
      return parseFloat(value) || 0; // Fallback
    }
    return whole + numerator / denominator;
  } else if (value.includes('/')) {
    const fractionParts = value.split('/');
    const numerator = parseInt(fractionParts[0], 10);
    const denominator = parseInt(fractionParts[1], 10);
    if (isNaN(numerator) || isNaN(denominator) || denominator === 0) {
      return parseFloat(value) || 0; // Fallback
    }
    return numerator / denominator;
  }
  return parseFloat(value) || 0;
}

// Custom sort function for fractional strings
function compareFractionalStrings(a: string, b: string): number {
  return fractionalStringToNumber(a) - fractionalStringToNumber(b);
}

interface GluLamSectionProperties {
  width: string;
  depth: string;
  area: number;
  Ix: number;
  Sx: number;
  rx: number;
  Iy: number;
  Sy: number;
  ry: number;
  table: string;
  species: string;
  version: string;
}

interface GluLamSectionData {
  westernSpecies: {
    widths: string[];
    depths: { [width: string]: string[] };
    properties: { [key: string]: GluLamSectionProperties };
  };
  southernPine: {
    widths: string[];
    depths: { [width: string]: string[] };
    properties: { [key: string]: GluLamSectionProperties };
  };
}

// --- In-memory cache for GluLam Section Properties ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}



let gluLamSectionCache: Cache<GluLamSectionData> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for GluLam Section Properties ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || DEFAULT_NDS_VERSION;
  // --- Cache check for GluLam Section Properties ---
  const now = Date.now();
  if (gluLamSectionCache.data && gluLamSectionCache.expires && gluLamSectionCache.expires > now) {
    console.log("Serving GluLam section properties data from cache.");
    return NextResponse.json(gluLamSectionCache.data, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh GluLam section properties data.");
  // --- End cache check for GluLam Section Properties ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'glulam', 'SectionProperties_GluLamTimber.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: 'GluLam section properties data file not found' },
        { 
          status: HTTP_STATUS.NOT_FOUND,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    const sectionData: GluLamSectionData = {
      westernSpecies: {
        widths: [],
        depths: {},
        properties: {},
      },
      southernPine: {
        widths: [],
        depths: {},
        properties: {},
      }
    };

    return new Promise<NextResponse>((resolve, reject) => {
      const rows: any[] = [];

      stream
        .pipe(csv())
        .on('data', (row) => rows.push(row))
        .on('end', () => {
          try {
            rows.forEach((row) => {
              const width = row.Width?.trim();
              const depth = row.Depth?.trim();
              const species = row.species?.trim();
              const rowVersion = row.version?.trim();
              
              // Filter by version
              if (rowVersion !== version) return;
              
              if (!width || !depth || !species) return;

              const data = species === 'Southern Pine' ? sectionData.southernPine : sectionData.westernSpecies;

              // Add width if not already in the list
              if (!data.widths.includes(width)) {
                data.widths.push(width);
                data.depths[width] = [];
              }

              // Add depth if not already in the list for this width
              if (!data.depths[width].includes(depth)) {
                data.depths[width].push(depth);
              }

              // Store properties
              const key = `${width}x${depth}`;
              data.properties[key] = {
                width,
                depth,
                area: parseFloat(row.Area) || 0,
                Ix: parseFloat(row.Ix) || 0,
                Sx: parseFloat(row.Sx) || 0,
                rx: parseFloat(row.rx) || 0,
                Iy: parseFloat(row.Iy) || 0,
                Sy: parseFloat(row.Sy) || 0,
                ry: parseFloat(row.ry) || 0,
                table: row.table,
                species,
                version: row.version,
              };
            });

            // Sort widths and depths using custom sort
            sectionData.westernSpecies.widths.sort(compareFractionalStrings);
            sectionData.southernPine.widths.sort(compareFractionalStrings);

            Object.keys(sectionData.westernSpecies.depths).forEach(widthKey => {
              sectionData.westernSpecies.depths[widthKey].sort(compareFractionalStrings);
            });

            Object.keys(sectionData.southernPine.depths).forEach(widthKey => {
              sectionData.southernPine.depths[widthKey].sort(compareFractionalStrings);
            });

            // --- Cache update for GluLam Section Properties ---
            gluLamSectionCache.data = sectionData;
            gluLamSectionCache.expires = Date.now() + CACHE_DURATION_MS.WOOD_DATA;
            console.log("GluLam section properties data cached.");
            // --- End cache update for GluLam Section Properties ---

            resolve(NextResponse.json(sectionData, {
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, max-age=3600',
              },
            }));
          } catch (error) {
            reject(NextResponse.json(
              { error: 'Failed to process GluLam section properties data', details: error instanceof Error ? error.message : 'Unknown error' },
              { 
                status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            ));
          }
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse GluLam section properties data', details: error.message },
            { 
              status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load GluLam section properties data', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}