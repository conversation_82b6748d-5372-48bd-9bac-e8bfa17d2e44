import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';
import {
  DEFAULT_NDS_VERSION,
  ERROR_MESSAGES,
  HTTP_STATUS,
  CACHE_DURATION_MS,
} from '@/lib/constants/nds-constants';

interface GluLamSpecies {
  speciesGroup: string;
  symbol: string;
  species: string;
  designValues: string;
  version: string;
}

interface GluLamData {
  speciesGroups: string[];
  species: {
    [group: string]: {
      symbol: string;
      species: string[];
      designValues: string;
    };
  };
}

// --- In-memory cache --- A
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}



let gluLamSpeciesCache: Cache<GluLamData> = {
  data: null,
  expires: null,
};
// --- End in-memory cache ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || DEFAULT_NDS_VERSION;
  // --- Cache check --- B
  const now = Date.now();
  if (gluLamSpeciesCache.data && gluLamSpeciesCache.expires && gluLamSpeciesCache.expires > now) {
    console.log("Serving GluLam species data from cache.");
    return NextResponse.json(gluLamSpeciesCache.data, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh GluLam species data.");
  // --- End cache check ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'glulam', 'GluLamSpeciesCombinations.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: 'GluLam species data file not found' },
        { 
          status: HTTP_STATUS.NOT_FOUND,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    const gluLamData: GluLamData = {
      speciesGroups: [],
      species: {},
    };

    return new Promise<NextResponse>((resolve, reject) => {
      const rows: any[] = [];

      stream
        .pipe(csv())
        .on('data', (row) => rows.push(row))
        .on('end', () => {
          try {
            rows.forEach((row) => {
              const speciesGroup = row['Species Group']?.trim();
              const symbol = row['Symbol']?.trim();
              const species = row['Species']?.trim();
              const rowVersion = row['version']?.trim();
              
              // Filter by version
              if (rowVersion !== version) return;
              
              // Find the correct key for design values, accounting for potential trailing tabs/spaces
              let designValuesKey = 'Design Values Provided in Tables'; // Default key
              const csvKeys = Object.keys(row);
              const foundKey = csvKeys.find(k => k.trim().startsWith('Design Values Provided in Tables'));
              if (foundKey) {
                designValuesKey = foundKey;
              }
              const designValues = row[designValuesKey]?.trim();
              
              if (!speciesGroup || !symbol || !species) return;

              if (!gluLamData.speciesGroups.includes(speciesGroup)) {
                gluLamData.speciesGroups.push(speciesGroup);
                gluLamData.species[speciesGroup] = {
                  symbol,
                  species: [],
                  designValues,
                };
              }

              if (!gluLamData.species[speciesGroup].species.includes(species)) {
                gluLamData.species[speciesGroup].species.push(species);
              }
            });

            gluLamData.speciesGroups.sort();
            Object.keys(gluLamData.species).forEach(group => {
              gluLamData.species[group].species.sort();
            });

            // --- Cache update --- C
            gluLamSpeciesCache.data = gluLamData;
            gluLamSpeciesCache.expires = Date.now() + CACHE_DURATION_MS.WOOD_DATA;
            console.log("GluLam species data cached.");
            // --- End cache update ---

            resolve(NextResponse.json(gluLamData, {
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, max-age=3600',
              },
            }));
          } catch (error) {
            reject(NextResponse.json(
              { error: 'Failed to process GluLam species data', details: error instanceof Error ? error.message : 'Unknown error' },
              { 
                status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            ));
          }
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse GluLam species data', details: error.message },
            { 
              status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load GluLam species data', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}