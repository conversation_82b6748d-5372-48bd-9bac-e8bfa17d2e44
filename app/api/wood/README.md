# Wood Beam Analysis APIs

This directory contains comprehensive wood beam analysis APIs that integrate the NDS (National Design Specification) compliant wood beam analysis module with the structural analysis engine.

## Available Endpoints

### 1. Sawn Lumber Beam Analysis
**Endpoint:** `POST /api/wood/sawn-lumber-analysis`

Performs comprehensive analysis of sawn lumber beams according to NDS Section 4 standards.

#### Features:
- NDS 2018 compliant calculations
- Adjustment factor calculations (CD, CM, Ct, CL, CF, Cfu, Ci, Cr, etc.)
- Design verification (bending, shear, deflection)
- Both ASD and LRFD design methods
- Integration with structural analysis results

#### Request Body:
```typescript
{
  beamData: BeamData;              // Beam geometry, loads, and supports
  unitSystem: UnitSystem;          // METRIC or IMPERIAL
  beamPropertiesState: BeamPropertiesState; // Material properties and design values
  designMethod?: 'ASD' | 'LRFD';   // Design method (defaults to ASD)
}
```

#### Response:
```typescript
{
  structuralAnalysis: AnalysisOutput;  // Standard beam analysis results
  woodAnalysis: SawnLumberBeamAnalysisResults; // Complete NDS-specific analysis
  adjustmentFactors: {              // Top-level adjustment factors for easy display
    CD: number;     // Load duration factor
    CM: number;     // Wet service factor
    Ct: number;     // Temperature factor
    CL: number;     // Beam stability factor
    CF: number;     // Size factor
    Cfu: number;    // Flat use factor
    Ci: number;     // Incising factor
    Cr: number;     // Repetitive member factor
    CP: number;     // Column stability factor
    Cb: number;     // Bearing area factor
    CT: number;     // Buckling stiffness factor
    CV: number;     // Volume factor
    KF: number;     // Format conversion factor (LRFD)
    phi: number;    // Resistance factor (LRFD)
    lambda: number; // Time effect factor (LRFD)
  };
  adjustedDesignValues: {           // Top-level adjusted design values
    Fb_prime: number;      // F'b - Adjusted bending stress
    Ft_prime: number;      // F't - Adjusted tension stress
    Fv_prime: number;      // F'v - Adjusted shear stress
    Fc_perp_prime: number; // F'c⊥ - Adjusted compression perpendicular
    Fc_prime: number;      // F'c - Adjusted compression parallel
    E_prime: number;       // E' - Adjusted modulus of elasticity
    Emin_prime: number;    // E'min - Adjusted minimum modulus
  };
  designChecks: {                   // Design verification results
    bending: { allowableStress: number; actualStress: number; ratio: number; passes: boolean; };
    shear: { allowableStress: number; actualStress: number; ratio: number; passes: boolean; };
    deflection: { allowableDeflection: number; actualDeflection: number; ratio: number; passes: boolean; };
  };
  summary: {                        // Analysis summary
    isDesignAcceptable: boolean;
    controllingCriteria: string;
    designMethod: string;
    deflectionLimit: number;
  };
  success: boolean;
  message?: string;
}
```

### 2. Glulam Beam Analysis
**Endpoint:** `POST /api/wood/glulam-analysis`

Performs comprehensive analysis of glued laminated timber (glulam) beams according to NDS Section 5 standards.

#### Features:
- NDS 2018 Section 5 compliant calculations
- Directional design values (x-x strong axis, y-y weak axis)
- Positive and negative bending analysis (Fbx+, Fbx-)
- Additional glulam-specific adjustment factors (CV, Cc, CI, Cvr)
- Both ASD and LRFD design methods
- Integration with structural analysis results

#### Request Body:
```typescript
{
  beamData: BeamData;              // Beam geometry, loads, and supports
  unitSystem: UnitSystem;          // METRIC or IMPERIAL
  beamPropertiesState: BeamPropertiesState; // Material properties and design values
  designMethod?: 'ASD' | 'LRFD';   // Design method (defaults to ASD)
}
```

#### Response:
```typescript
{
  structuralAnalysis: AnalysisOutput;  // Standard beam analysis results
  woodAnalysis: GlulamBeamAnalysisResults; // Complete NDS-specific glulam analysis
  adjustmentFactors: {              // Top-level adjustment factors for easy display
    CD: number;     // Load duration factor
    CM: number;     // Wet service factor
    Ct: number;     // Temperature factor
    CL: number;     // Beam stability factor
    Cfu: number;    // Flat use factor
    Cb: number;     // Bearing area factor
    // Glulam-specific factors
    CV: number;     // Volume factor
    Cc: number;     // Curvature factor
    CI: number;     // Stress interaction factor
    Cvr: number;    // Shear reduction factor
    CP: number;     // Column stability factor
    KF: number;     // Format conversion factor (LRFD)
    phi: number;    // Resistance factor (LRFD)
    lambda: number; // Time effect factor (LRFD)
  };
  adjustedDesignValues: {           // Top-level adjusted design values (glulam-specific)
    Fbx_pos_prime: number;    // F'bx+ - Adjusted positive bending stress
    Fbx_neg_prime: number;    // F'bx- - Adjusted negative bending stress
    Fby_prime: number;        // F'by - Adjusted bending stress about y-axis
    Ft_prime: number;         // F't - Adjusted tension stress
    Fvx_prime: number;        // F'vx - Adjusted shear stress in x-direction
    Fvy_prime: number;        // F'vy - Adjusted shear stress in y-direction
    Fc_perp_x_prime: number;  // F'c⊥x - Adjusted compression perpendicular x-face
    Fc_perp_y_prime: number;  // F'c⊥y - Adjusted compression perpendicular y-face
    Fc_prime: number;         // F'c - Adjusted compression parallel
    Ex_prime: number;         // E'x - Adjusted modulus of elasticity x-axis
    Ex_min_prime: number;     // E'x min - Adjusted minimum modulus x-axis
    Ey_prime: number;         // E'y - Adjusted modulus of elasticity y-axis
    Ey_min_prime: number;     // E'y min - Adjusted minimum modulus y-axis
    Frt_prime?: number;       // F'rt - Adjusted radial tension (if applicable)
    Frc_prime?: number;       // F'rc - Adjusted radial compression (if applicable)
  };
  designChecks: {                   // Design verification results
    bending: { 
      allowableStress: number; actualStress: number; ratio: number; passes: boolean; 
      stressType: string; // 'positive' or 'negative'
    };
    shear: { allowableStress: number; actualStress: number; ratio: number; passes: boolean; };
    deflection: { allowableDeflection: number; actualDeflection: number; ratio: number; passes: boolean; };
  };
  summary: {                        // Analysis summary
    isDesignAcceptable: boolean;
    controllingCriteria: string;
    designMethod: string;
    deflectionLimit: number;
    primaryAxis: string;      // 'x_axis' or 'y_axis'
    layupType: string;        // 'balanced' or 'unbalanced'
  };
  success: boolean;
  message?: string;
}
```

## Enhanced Response Structure

The wood analysis APIs provide a comprehensive response structure with adjustment factors and design values prominently displayed at the top level for easy access and visualization.

### Key Response Components:

1. **structuralAnalysis**: Complete structural analysis results from the FEM engine
2. **woodAnalysis**: Full NDS-compliant analysis object with all detailed calculations
3. **adjustmentFactors**: All NDS adjustment factors at the top level for easy display
4. **adjustedDesignValues**: All effective (adjusted) design values for immediate use
5. **designChecks**: Pass/fail status and ratios for each design criterion
6. **summary**: Overall analysis summary with controlling criteria

### Adjustment Factors Included:

#### Sawn Lumber:
- **CD**: Load duration factor (NDS 2.3.2)
- **CM**: Wet service factor (NDS 4.1.4)  
- **Ct**: Temperature factor (NDS 2.3.3)
- **CL**: Beam stability factor (NDS 3.3.3)
- **CF**: Size factor (NDS 4.3.6)
- **Cfu**: Flat use factor (NDS 4.3.7)
- **Ci**: Incising factor (NDS 4.3.8)
- **Cr**: Repetitive member factor (NDS 4.3.9)
- **CP**: Column stability factor
- **Cb**: Bearing area factor
- **CT**: Buckling stiffness factor
- **CV**: Volume factor
- **KF**, **phi**, **lambda**: LRFD factors

#### Glulam (Additional factors):
- **CV**: Volume factor (NDS 5.3.6)
- **Cc**: Curvature factor (NDS 5.3.8)
- **CI**: Stress interaction factor (NDS 5.3.9)
- **Cvr**: Shear reduction factor (NDS 5.3.10)

### Design Values Structure:

The APIs return both reference (tabulated) and adjusted (effective) design values:
- Reference values are provided in the input material properties
- Adjusted values apply all relevant adjustment factors
- Both sawn lumber and glulam have material-specific design value structures

This enhanced structure allows frontend applications to easily display:
- Tabulated vs. adjusted design values
- Individual adjustment factors and their effects
- Pass/fail status for each design criterion
- Overall design adequacy with controlling criteria

## Integration Architecture

The wood analysis APIs follow a two-stage approach:

1. **Structural Analysis**: First performs standard structural analysis using the existing FEM engine to calculate:
   - Maximum moments, shears, and deflections
   - Load combinations according to ASCE 7-10
   - Support reactions
   - Deflection curves

2. **Wood-Specific Analysis**: Then applies NDS standards to:
   - Calculate adjustment factors
   - Apply adjusted design values
   - Verify design criteria
   - Provide code-compliant results

## Usage Examples

### Sawn Lumber Analysis
```typescript
const response = await fetch('/api/wood/sawn-lumber-analysis', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    beamData: {
      properties: { length: 20, elasticModulus: 1600000, momentOfInertia: 177.979, area: 12.1875 },
      supports: [{ position: 0, type: 'PIN' }, { position: 20, type: 'ROLLER' }],
      loadGroups: [/* load definitions */]
    },
    unitSystem: 'IMPERIAL',
    beamPropertiesState: {
      lumberType: 'sawn',
      selectedSpecies: 'Douglas Fir-Larch',
      selectedGrade: 'Select Structural',
      // ... other properties
    },
    designMethod: 'ASD'
  })
});

const result = await response.json();
```

### Glulam Analysis
```typescript
const response = await fetch('/api/wood/glulam-analysis', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    beamData: {
      properties: { length: 30, elasticModulus: 1800000, momentOfInertia: 455.625, area: 37.5 },
      supports: [{ position: 0, type: 'PIN' }, { position: 30, type: 'ROLLER' }],
      loadGroups: [/* load definitions */]
    },
    unitSystem: 'IMPERIAL',
    beamPropertiesState: {
      lumberType: 'glulam',
      selectedGluLamProperties: {
        speciesGroup: 'Western Species',
        selectedCombinationSymbolKey: '24F-V4',
        width: 3.125,
        depth: 12,
      },
      // ... other properties
    },
    designMethod: 'LRFD'
  })
});

const result = await response.json();
```

## Input Validation

Both APIs perform comprehensive validation to ensure all required design values and geometry are provided. **No default values are used for critical structural properties** to prevent unsafe designs based on missing data.

### Required Design Values

#### For Sawn Lumber:
- **Fb**: Bending stress parallel to grain (psi or Pa)
- **Ft**: Tension stress parallel to grain (psi or Pa)
- **Fv**: Shear stress parallel to grain (psi or Pa)
- **Fc_perp**: Compression stress perpendicular to grain (psi or Pa)
- **Fc**: Compression stress parallel to grain (psi or Pa)
- **E**: Modulus of elasticity (psi or Pa)
- **Emin**: Minimum modulus of elasticity (psi or Pa)
- **G**: Shear modulus (psi or Pa)

#### For Glulam:
- **Fb** OR **Fb_pos/Fb_neg**: Bending stress (positive/negative) (psi or Pa)
- **Ft**: Tension stress parallel to grain (psi or Pa)
- **Fv**: Shear stress parallel to grain (psi or Pa)
- **Fc_perp**: Compression stress perpendicular to grain (psi or Pa)
- **Fc**: Compression stress parallel to grain (psi or Pa)
- **E**: Modulus of elasticity (psi or Pa)
- **Emin**: Minimum modulus of elasticity (psi or Pa)
- **G**: Shear modulus (psi or Pa)

### Required Geometry

#### For Sawn Lumber:
- **Width**: Member width (provide `manualWidth` or `lumberProperties.width`)
- **Depth**: Member depth (provide `manualDepth` or `lumberProperties.depth`)

#### For Glulam:
- **Width**: Member width (provide `selectedGluLamProperties.width` or `manualWidth`)
- **Depth**: Member depth (provide `selectedGluLamProperties.depth` or `manualDepth`)

### Validation Error Response

If required values are missing, the API will return a detailed error response:

```typescript
{
  error: "Missing or invalid design values",
  details: [
    "Fb (bending stress) is required and must be a positive number",
    "E (modulus of elasticity) is required and must be a positive number"
  ],
  success: false
}
```

## Error Handling

Both APIs include comprehensive error handling:

- **400 Bad Request**: Missing required parameters, invalid lumber type, or missing design values
- **500 Internal Server Error**: Analysis calculation errors

### Common Error Types:

#### Missing Design Values:
```typescript
{
  error: "Missing or invalid design values",
  details: ["Fb (bending stress) is required and must be a positive number"],
  success: false
}
```

#### Missing Geometry:
```typescript
{
  error: "Missing or invalid member geometry", 
  details: ["Member width is required and must be a positive number"],
  success: false
}
```

#### Wrong Lumber Type:
```typescript
{
  error: "This endpoint is specifically for sawn lumber analysis. Use glulam endpoint for glulam analysis.",
  success: false
}
```

## Required BeamPropertiesState Fields

### For Sawn Lumber:
- `lumberType: "sawn"`
- `designValues`: **All design values are required** (Fb, Ft, Fv, Fc_perp, Fc, E, Emin, G)
- **Member geometry**: Either `manualWidth`/`manualDepth` OR `lumberProperties.width`/`lumberProperties.depth`
- `selectedSpecies`, `selectedGrade`, `selectedSizeClassification` (for documentation - will show "Unknown" if missing)
- Service condition flags (`moistureContent`, `temperature`, `isIncised`, `isRepetitiveMember`, etc.) - *optional with reasonable defaults*

### For Glulam:
- `lumberType: "glulam"`
- `designValues`: **All design values are required** (Fb, Ft, Fv, Fc_perp, Fc, E, Emin, G)
  - For glulam, provide either `Fb` or separate `Fb_pos`/`Fb_neg` values
- **Member geometry**: Either `selectedGluLamProperties.width`/`selectedGluLamProperties.depth` OR `manualWidth`/`manualDepth`
- `selectedGluLamProperties` with species and grade information (for documentation - will show "Unknown" if missing)
- Service condition properties (`moistureContent`, `temperature`, etc.) - *optional with reasonable defaults*

### Validation Philosophy

The APIs follow a **strict validation approach**:
- ✅ **Required**: All structural design values and member geometry must be explicitly provided
- ✅ **Optional with defaults**: Non-critical service conditions (moisture content, temperature, etc.)
- ❌ **No defaults for critical values**: No default design values are used to prevent unsafe designs

This ensures engineers are aware of exactly what design values are being used in the analysis.

## Design Methods

Both APIs support:
- **ASD (Allowable Stress Design)**: Traditional factor of safety approach
- **LRFD (Load and Resistance Factor Design)**: Probability-based design method

The choice affects load combinations and resistance factors applied during analysis.

## Units

The APIs handle unit conversions automatically to ensure proper wood analysis:

### Internal Processing:
- **Wood analysis modules** work internally in **Imperial units** (psi, inches, pounds)
- All input design values and structural results are converted to Imperial before wood analysis
- All output design values and stresses are converted back to the requested unit system

### Unit Conversion Flow:

#### For METRIC Input:
1. **Input Conversion**: Reference design values (Pa → psi), structural results (N⋅m → lb⋅in, N → lb, m → in)
2. **Wood Analysis**: Performed in Imperial units (psi, inches, pounds)
3. **Output Conversion**: All results converted back to Metric
   - Adjusted design values: psi → Pa
   - Actual/allowable stresses: psi → Pa
   - Adjustment factors: dimensionless (no conversion)
   - Deflections: already converted in structural analysis

#### For IMPERIAL Input:
1. **Input Conversion**: Structural results (kip⋅ft → lb⋅in, kips → lb, ft → in)
2. **Wood Analysis**: Performed in Imperial units (psi, inches, pounds)
3. **Output**: Results remain in Imperial units (psi, inches, pounds)

### Consistent User Experience:
- **Input**: Provide design values in your preferred unit system
- **Output**: Receive all results in the same unit system
- **Internal**: Wood analysis calculations use consistent Imperial units for accuracy

## Dependencies

These APIs depend on:
- `@/lib/utils/wood/beam` - NDS analysis module
- `@/lib/beam-analysis/server-calculations` - Structural analysis engine
- `@/lib/utils/metric-converter` - Unit conversion utilities
- Various type definitions for beam data and properties