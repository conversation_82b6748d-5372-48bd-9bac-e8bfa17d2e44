import { NextResponse } from "next/server";
import { BeamData, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { performServerSideCalculations } from "@/lib/beam-analysis/server-calculations";
import {
  analyzeGlulamBeam,
  GlulamBeamAnalysisInputs,
  GlulamBeamAnalysisResults,
  GlulamReferenceDesignValues,
  GlulamMemberGeometry,
  GlulamServiceConditions,
  GlulamMaterialProperties,
  DesignMethod,
  GlulamAxis,
  LayupType,
} from "@/lib/utils/wood/beam";
import { DESIGN_METHODS, GLULAM_AXIS, LAYUP_TYPES } from "@/lib/utils/wood/constants";
import { LoadType } from "@/lib/types/load/load-type";
import {
  getControllingLoadDurationFactor,
  getLoadDurationAnalysis,
} from "@/lib/utils/wood/adjustment-factors/load-duration/load-duration";
import {
  ftToIn,
  inToFt,
  kipToLb,
  lbToKip,
  kipFtToLbIn,
  lbInToKipFt,
  mToIn,
  inToM,
  nToLb,
  lbToN,
  nmToLbIn,
  lbInToNm,
  paToPsi,
  psiToPa,
} from "@/lib/utils/metric-converter";

export const dynamic = "force-dynamic";

interface GlulamAnalysisRequest {
  beamData: BeamData;
  unitSystem: UnitSystem;
  beamPropertiesState: BeamPropertiesState | null;
  designMethod?: 'ASD' | 'LRFD';
}

interface GlulamAnalysisResponse {
  structuralAnalysis: any; // Results from performServerSideCalculations
  woodAnalysis: GlulamBeamAnalysisResults;
  adjustmentFactors: any; // DesignValueSpecificFactors from woodAnalysis
  adjustedDesignValues: {
    Fbx_pos_prime: number; // F'bx+ - Adjusted positive bending stress
    Fbx_neg_prime: number; // F'bx- - Adjusted negative bending stress
    Fby_prime: number; // F'by - Adjusted bending stress about y-axis
    Ft_prime: number; // F't - Adjusted tension stress
    Fvx_prime: number; // F'vx - Adjusted shear stress in x-direction
    Fvy_prime: number; // F'vy - Adjusted shear stress in y-direction
    Fc_perp_x_prime: number; // F'c⊥x - Adjusted compression perpendicular x-face
    Fc_perp_y_prime: number; // F'c⊥y - Adjusted compression perpendicular y-face
    Fc_prime: number; // F'c - Adjusted compression parallel
    Ex_prime: number; // E'x - Adjusted modulus of elasticity x-axis
    Ex_min_prime: number; // E'x min - Adjusted minimum modulus x-axis
    Ey_prime: number; // E'y - Adjusted modulus of elasticity y-axis
    Ey_min_prime: number; // E'y min - Adjusted minimum modulus y-axis
    Frt_prime?: number; // F'rt - Adjusted radial tension (if applicable)
    Frc_prime?: number; // F'rc - Adjusted radial compression (if applicable)
  };
  designChecks: {
    bending: {
      allowableStress: number;
      actualStress: number;
      ratio: number;
      passes: boolean;
      stressType: string; // 'positive' or 'negative'
    };
    shear: {
      allowableStress: number;
      actualStress: number;
      ratio: number;
      passes: boolean;
    };
    deflection: {
      allowableDeflection: number;
      actualDeflection: number;
      ratio: number;
      passes: boolean;
    };
  };
  summary: {
    isDesignAcceptable: boolean;
    controllingCriteria: string;
    designMethod: string;
    deflectionLimit: number;
    primaryAxis: string; // 'x_axis' or 'y_axis'
    layupType: string; // 'balanced' or 'unbalanced'
  };
  success: boolean;
  message?: string;
}

/**
 * Convert beam properties and analysis results to glulam analysis inputs
 */
function convertToGlulamInputs(
  beamData: BeamData,
  beamPropertiesState: BeamPropertiesState,
  structuralResults: any,
  designMethod: DesignMethod,
  unitSystem: UnitSystem
): GlulamBeamAnalysisInputs {
  
  // Convert structural analysis results to consistent units (lb-in system)
  let maxMoment = structuralResults.summaryData.maxMomentValue?.value || 0;
  let maxShear = structuralResults.summaryData.maxShearValue?.value || 0;
  let maxDeflection = Math.abs(structuralResults.summaryData.maxTotalDeflectionDownward?.value || 0);
  let beamLength = beamData.properties.length;

  // Convert units if needed
  if (unitSystem === UnitSystem.METRIC) {
    maxMoment = nmToLbIn(maxMoment);
    maxShear = nToLb(maxShear);
    maxDeflection = mToIn(maxDeflection);
    beamLength = mToIn(beamLength);
  } else if (unitSystem === UnitSystem.IMPERIAL) {
    maxMoment = kipFtToLbIn(maxMoment);
    maxShear = kipToLb(maxShear);
    // maxDeflection already in inches
    beamLength = ftToIn(beamLength);
  }

  // Extract dimensions from glulam properties (already validated)
  const glulamProps = beamPropertiesState.selectedGluLamProperties;
  const width = glulamProps?.width || parseFloat(beamPropertiesState.manualWidth!);
  const depth = glulamProps?.depth || parseFloat(beamPropertiesState.manualDepth!);
  
  // Member geometry
  const member: GlulamMemberGeometry = {
    length: beamLength,
    width: width,
    depth: depth,
    unbracedLength: beamPropertiesState.isBraced ? 
      0 : // When braced (switch ON), set unbracedLength to 0 (fully braced, CL = 1.0)
      (beamPropertiesState.lu ? parseFloat(beamPropertiesState.lu) : beamLength), // When unbraced (switch OFF), use actual unbraced length
  };

  // Reference design values for glulam (already validated - no defaults)
  const designValues = beamPropertiesState.designValues!;
  let referenceDesignValues: GlulamReferenceDesignValues = {
    Fbx_pos: designValues.Fb_pos || designValues.Fb,
    Fbx_neg: designValues.Fb_neg || designValues.Fb,
    Fby: designValues.Fb, // Weak axis bending uses same base value
    Ft: designValues.Ft,
    Fvx: designValues.Fv,
    Fvy: designValues.Fv, // Same shear value for both directions initially
    Fc_perp_x: designValues.Fc_perp,
    Fc_perp_y: designValues.Fc_perp, // Same compression perp for both faces initially
    Fc: designValues.Fc,
    Ex: designValues.E,
    Ex_min: designValues.Emin,
    Ey: designValues.E, // Same E for both axes initially
    Ey_min: designValues.Emin, // Same Emin for both axes initially
    G: designValues.G && designValues.G > 1 ? designValues.G : designValues.E / 16, // Calculate shear modulus from E if G is specific gravity
  };

  // Convert reference design values from metric to imperial if needed (wood analysis works in imperial)
  if (unitSystem === UnitSystem.METRIC) {
    referenceDesignValues = {
      Fbx_pos: paToPsi(referenceDesignValues.Fbx_pos),
      Fbx_neg: paToPsi(referenceDesignValues.Fbx_neg),
      Fby: paToPsi(referenceDesignValues.Fby),
      Ft: paToPsi(referenceDesignValues.Ft),
      Fvx: paToPsi(referenceDesignValues.Fvx),
      Fvy: paToPsi(referenceDesignValues.Fvy),
      Fc_perp_x: paToPsi(referenceDesignValues.Fc_perp_x),
      Fc_perp_y: paToPsi(referenceDesignValues.Fc_perp_y),
      Fc: paToPsi(referenceDesignValues.Fc),
      Ex: paToPsi(referenceDesignValues.Ex),
      Ex_min: paToPsi(referenceDesignValues.Ex_min),
      Ey: paToPsi(referenceDesignValues.Ey),
      Ey_min: paToPsi(referenceDesignValues.Ey_min),
      G: paToPsi(referenceDesignValues.G),
    };
  }

  // Material properties
  const material: GlulamMaterialProperties = {
    species: beamPropertiesState.selectedSpecies || glulamProps?.species || "Unknown Species",
    grade: glulamProps?.selectedCombinationSymbolKey || glulamProps?.grade || "Unknown Grade",
    layupGrade: glulamProps?.grade || "Unknown Layup Grade",
    combination: glulamProps?.speciesGroup || "Unknown Species Group",
    referenceDesignValues,
  };

  // Service conditions - these can have reasonable defaults for non-critical parameters
  const serviceConditions: GlulamServiceConditions = {
    moistureContent: beamPropertiesState.isWetService ? 
      (beamPropertiesState.moistureContent || 19) : // Use actual moisture content or default to 19% when wet service is ON
      12, // Use 12% (below threshold) when wet service is OFF to prevent wet service factors
    temperature: beamPropertiesState.isTemperatureFactored ? 
      (beamPropertiesState.temperature || 100) : // Use actual temperature or default to 100°F when temperature effects are ON
      70, // Use 70°F (normal temperature) when temperature effects are OFF to prevent temperature factors
    loadDuration: getControllingLoadType(beamData),
    isFlatUse: false, // Default to edge use
    isRepetitiveMember: beamPropertiesState.isRepetitiveMember || false,
    isIncised: beamPropertiesState.isIncised || false,
    layupType: LAYUP_TYPES.BALANCED, // Default layup type
    primaryAxis: GLULAM_AXIS.X_AXIS, // Default to strong axis bending
  };

  // Analysis results
  const analysisResults = {
    maxMoment,
    maxShear,
    maxDeflection,
  };

  return {
    member,
    material,
    serviceConditions,
    analysisResults,
    designMethod,
    deflectionLimit: beamPropertiesState.manual_totalDeflectionLimit || 240,
    includeSelfWeight: beamPropertiesState.includeBeamWeight || false,
  };
}

/**
 * Convert glulam analysis results from Imperial (solver units) back to display units
 */
function convertGlulamAnalysisResults(
  adjustmentFactors: any,
  adjustedDesignValues: any,
  designChecks: any,
  unitSystem: UnitSystem
) {
  // Adjustment factors are dimensionless, so no conversion needed
  const convertedAdjustmentFactors = { ...adjustmentFactors };

  // Convert design values from psi to Pa if metric
  const convertedAdjustedDesignValues = { ...adjustedDesignValues };
  if (unitSystem === UnitSystem.METRIC) {
    convertedAdjustedDesignValues.Fbx_pos_prime = psiToPa(adjustedDesignValues.Fbx_pos_prime);
    convertedAdjustedDesignValues.Fbx_neg_prime = psiToPa(adjustedDesignValues.Fbx_neg_prime);
    convertedAdjustedDesignValues.Fby_prime = psiToPa(adjustedDesignValues.Fby_prime);
    convertedAdjustedDesignValues.Ft_prime = psiToPa(adjustedDesignValues.Ft_prime);
    convertedAdjustedDesignValues.Fvx_prime = psiToPa(adjustedDesignValues.Fvx_prime);
    convertedAdjustedDesignValues.Fvy_prime = psiToPa(adjustedDesignValues.Fvy_prime);
    convertedAdjustedDesignValues.Fc_perp_x_prime = psiToPa(adjustedDesignValues.Fc_perp_x_prime);
    convertedAdjustedDesignValues.Fc_perp_y_prime = psiToPa(adjustedDesignValues.Fc_perp_y_prime);
    convertedAdjustedDesignValues.Fc_prime = psiToPa(adjustedDesignValues.Fc_prime);
    convertedAdjustedDesignValues.Ex_prime = psiToPa(adjustedDesignValues.Ex_prime);
    convertedAdjustedDesignValues.Ex_min_prime = psiToPa(adjustedDesignValues.Ex_min_prime);
    convertedAdjustedDesignValues.Ey_prime = psiToPa(adjustedDesignValues.Ey_prime);
    convertedAdjustedDesignValues.Ey_min_prime = psiToPa(adjustedDesignValues.Ey_min_prime);
    if (adjustedDesignValues.Frt_prime) {
      convertedAdjustedDesignValues.Frt_prime = psiToPa(adjustedDesignValues.Frt_prime);
    }
    if (adjustedDesignValues.Frc_prime) {
      convertedAdjustedDesignValues.Frc_prime = psiToPa(adjustedDesignValues.Frc_prime);
    }
  }

  // Convert design check stresses from psi to Pa if metric
  const convertedDesignChecks = { ...designChecks };
  if (unitSystem === UnitSystem.METRIC) {
    convertedDesignChecks.bending = {
      ...designChecks.bending,
      allowableStress: psiToPa(designChecks.bending.allowableStress),
      actualStress: psiToPa(designChecks.bending.actualStress),
    };
    convertedDesignChecks.shear = {
      ...designChecks.shear,
      allowableStress: psiToPa(designChecks.shear.allowableStress),
      actualStress: psiToPa(designChecks.shear.actualStress),
    };
    // Deflection values are already converted in structural analysis
  }

  return {
    convertedAdjustmentFactors,
    convertedAdjustedDesignValues,
    convertedDesignChecks,
  };
}

/**
 * Validate that all required glulam design values are present
 */
function validateGlulamDesignValues(designValues: any): string[] {
  const errors: string[] = [];
  
  if (!designValues) {
    errors.push("Design values are required but not provided");
    return errors;
  }

  // Check required bending stress values
  if (typeof designValues.Fb !== 'number' || designValues.Fb <= 0) {
    // For glulam, we need at least Fb, though Fb_pos and Fb_neg are preferred
    if ((!designValues.Fb_pos || designValues.Fb_pos <= 0) && (!designValues.Fb_neg || designValues.Fb_neg <= 0)) {
      errors.push("Fb (bending stress) or Fb_pos/Fb_neg (positive/negative bending stress) is required and must be positive");
    }
  }
  
  if (typeof designValues.Ft !== 'number' || designValues.Ft <= 0) {
    errors.push("Ft (tension stress parallel to grain) is required and must be a positive number");
  }
  if (typeof designValues.Fv !== 'number' || designValues.Fv <= 0) {
    errors.push("Fv (shear stress parallel to grain) is required and must be a positive number");
  }
  if (typeof designValues.Fc_perp !== 'number' || designValues.Fc_perp <= 0) {
    errors.push("Fc_perp (compression stress perpendicular to grain) is required and must be a positive number");
  }
  if (typeof designValues.Fc !== 'number' || designValues.Fc <= 0) {
    errors.push("Fc (compression stress parallel to grain) is required and must be a positive number");
  }

  // Check required modulus values
  if (typeof designValues.E !== 'number' || designValues.E <= 0) {
    errors.push("E (modulus of elasticity) is required and must be a positive number");
  }
  if (typeof designValues.Emin !== 'number' || designValues.Emin <= 0) {
    errors.push("Emin (minimum modulus of elasticity) is required and must be a positive number");
  }
  if (typeof designValues.G !== 'number' || designValues.G <= 0) {
    errors.push("G (shear modulus) is required and must be a positive number");
  }

  return errors;
}

/**
 * Validate that all required glulam member geometry is present
 */
function validateGlulamMemberGeometry(beamPropertiesState: BeamPropertiesState): string[] {
  const errors: string[] = [];

  const glulamProps = beamPropertiesState.selectedGluLamProperties;

  // Check width
  const width = glulamProps?.width || (beamPropertiesState.manualWidth ? parseFloat(beamPropertiesState.manualWidth) : null);
  if (!width || width <= 0) {
    errors.push("Member width is required and must be a positive number (provide selectedGluLamProperties.width or manualWidth)");
  }

  // Check depth  
  const depth = glulamProps?.depth || (beamPropertiesState.manualDepth ? parseFloat(beamPropertiesState.manualDepth) : null);
  if (!depth || depth <= 0) {
    errors.push("Member depth is required and must be a positive number (provide selectedGluLamProperties.depth or manualDepth)");
  }

  return errors;
}

/**
 * Extract active load types from beam data to determine controlling load duration factor
 */
function getActiveLoadTypes(beamData: BeamData): LoadType[] {
  const activeTypes = new Set<LoadType>();
  beamData.loadGroups?.forEach(group => {
    group.loads?.forEach(load => {
      activeTypes.add(load.loadType);
    });
  });
  // Define a standard order for prioritization
  const orderedTypes = [
    LoadType.DEAD, LoadType.LIVE, LoadType.ROOF_LIVE, LoadType.SNOW,
    LoadType.WIND, LoadType.EARTHQUAKE, LoadType.RAIN, LoadType.SOIL,
    LoadType.FLOOD, LoadType.TEMPERATURE
  ];
  return orderedTypes.filter(type => activeTypes.has(type));
}

/**
 * Determine the controlling load type for load duration factor calculation
 */
function getControllingLoadType(beamData: BeamData): LoadType {
  const activeLoadTypes = getActiveLoadTypes(beamData);
  
  if (activeLoadTypes.length === 0) {
    console.warn("No active load types found in beam data, defaulting to LIVE load duration");
    return LoadType.LIVE;
  }
  
  if (activeLoadTypes.length === 1) {
    console.log(`Single load type found: ${activeLoadTypes[0]}, using for load duration factor`);
    return activeLoadTypes[0];
  }
  
  // Multiple load types - use load duration analysis to find controlling
  try {
    const analysis = getLoadDurationAnalysis(activeLoadTypes);
    console.log(`Multiple load types found [${activeLoadTypes.join(', ')}], controlling type: ${analysis.controllingLoadType} (CD = ${analysis.controllingFactor})`);
    return analysis.controllingLoadType;
  } catch (error) {
    console.error("Error determining controlling load type:", error);
    console.log("Falling back to LIVE load duration");
    return LoadType.LIVE;
  }
}

export async function POST(request: Request) {
  console.log("[API /api/wood/glulam-analysis] POST request received.");
  
  try {
    const body = (await request.json()) as GlulamAnalysisRequest;
    const { beamData, unitSystem, beamPropertiesState, designMethod = 'ASD' } = body;

    // Validate inputs
    if (!beamData || !unitSystem) {
      return NextResponse.json(
        { error: "Missing required parameters: beamData and unitSystem." },
        { status: 400 }
      );
    }

    if (!beamPropertiesState) {
      return NextResponse.json(
        { error: "Missing beam properties state for wood analysis." },
        { status: 400 }
      );
    }

    // Ensure we're analyzing glulam
    if (beamPropertiesState.lumberType !== "glulam") {
      return NextResponse.json(
        { error: "This endpoint is specifically for glulam analysis. Use sawn-lumber endpoint for sawn lumber analysis." },
        { status: 400 }
      );
    }

    // Validate required design values (only if they exist - allow null for condition-only updates)
    if (beamPropertiesState.designValues) {
      const designValueErrors = validateGlulamDesignValues(beamPropertiesState.designValues);
      if (designValueErrors.length > 0) {
        return NextResponse.json(
          { 
            error: "Missing or invalid design values", 
            details: designValueErrors,
            success: false 
          },
          { status: 400 }
        );
      }
    }

    // Validate required member geometry
    const geometryErrors = validateGlulamMemberGeometry(beamPropertiesState);
    if (geometryErrors.length > 0) {
      return NextResponse.json(
        { 
          error: "Missing or invalid member geometry", 
          details: geometryErrors,
          success: false 
        },
        { status: 400 }
      );
    }

    console.log("[API /api/wood/glulam-analysis] Performing structural analysis...");
    
    // Ensure beamData includes selected load combinations from beam properties
    const beamDataWithCombos = {
      ...beamData,
      selectedLoadCombos: beamPropertiesState.selectedLoadCombos || beamData.selectedLoadCombos || []
    };
    
    // First, perform the standard structural analysis
    const structuralResults = await performServerSideCalculations(
      beamDataWithCombos,
      unitSystem,
      beamPropertiesState
    );

    if (!structuralResults) {
      return NextResponse.json(
        { error: "Structural analysis failed to produce results." },
        { status: 500 }
      );
    }

    // If design values are missing, return structural results only with a message
    if (!beamPropertiesState.designValues) {
      return NextResponse.json({
        structuralAnalysis: structuralResults,
        woodAnalysis: null,
        adjustmentFactors: null,
        adjustedDesignValues: null,
        designChecks: null,
        summary: {
          isDesignAcceptable: false,
          controllingCriteria: "Design values required",
          designMethod: designMethod,
          deflectionLimit: beamPropertiesState.manual_totalDeflectionLimit || 240,
          primaryAxis: 'x_axis',
          layupType: 'balanced',
        },
        success: true,
        message: "Structural analysis completed. Select glulam species and grade to perform wood design checks.",
      });
    }

    console.log("[API /api/wood/glulam-analysis] Performing glulam-specific analysis...");

    // Convert to glulam analysis inputs
    const glulamInputs = convertToGlulamInputs(
      beamDataWithCombos,
      beamPropertiesState,
      structuralResults,
      designMethod as DesignMethod,
      unitSystem
    );

    // Perform glulam analysis
    const woodAnalysis = analyzeGlulamBeam(glulamInputs);

    const response: GlulamAnalysisResponse = {
      structuralAnalysis: structuralResults,
      woodAnalysis,
      adjustmentFactors: woodAnalysis.designValueSpecificFactors,
      adjustedDesignValues: woodAnalysis.adjustedDesignValues,
      designChecks: {
        bending: {
          allowableStress: woodAnalysis.bendingCheck.allowableStress,
          actualStress: woodAnalysis.bendingCheck.actualStress,
          ratio: woodAnalysis.bendingCheck.ratio,
          passes: woodAnalysis.bendingCheck.passes,
          stressType: woodAnalysis.bendingCheck.stressType,
        },
        shear: {
          allowableStress: woodAnalysis.shearCheck.allowableStress,
          actualStress: woodAnalysis.shearCheck.actualStress,
          ratio: woodAnalysis.shearCheck.ratio,
          passes: woodAnalysis.shearCheck.passes,
        },
        deflection: {
          allowableDeflection: woodAnalysis.deflectionCheck.allowableDeflection,
          actualDeflection: woodAnalysis.deflectionCheck.actualDeflection,
          ratio: woodAnalysis.deflectionCheck.ratio,
          passes: woodAnalysis.deflectionCheck.passes,
        },
      },
      summary: {
        isDesignAcceptable: woodAnalysis.isDesignAcceptable,
        controllingCriteria: woodAnalysis.controllingCriteria,
        designMethod: designMethod,
        deflectionLimit: beamPropertiesState.manual_totalDeflectionLimit || 240,
        primaryAxis: glulamInputs.serviceConditions.primaryAxis,
        layupType: glulamInputs.serviceConditions.layupType,
      },
      success: true,
      message: "Glulam beam analysis completed successfully.",
    };

    // Convert glulam analysis results from Imperial (solver units) back to display units
    const convertedResults = convertGlulamAnalysisResults(
      response.adjustmentFactors,
      response.adjustedDesignValues,
      response.designChecks,
      unitSystem
    );

    response.adjustmentFactors = convertedResults.convertedAdjustmentFactors;
    response.adjustedDesignValues = convertedResults.convertedAdjustedDesignValues;
    response.designChecks = convertedResults.convertedDesignChecks;

    console.log("[API /api/wood/glulam-analysis] Analysis completed successfully.");
    return NextResponse.json(response);

  } catch (error) {
    let errorMessage = "An unknown error occurred during glulam analysis.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    if (error instanceof SyntaxError) {
      errorMessage = `Invalid JSON in request body: ${errorMessage}`;
      console.error("[API /api/wood/glulam-analysis] SyntaxError:", errorMessage);
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }
    
    console.error("[API /api/wood/glulam-analysis] Error:", errorMessage, error);
    return NextResponse.json({ 
      error: `Server Error: ${errorMessage}`,
      success: false 
    }, { status: 500 });
  }
}

export async function GET(request: Request) {
  return NextResponse.json({ 
    message: "Glulam Beam Analysis API",
    description: "Use POST method to analyze glulam beams with comprehensive NDS calculations.",
    requiredFields: ["beamData", "unitSystem", "beamPropertiesState"],
    optionalFields: ["designMethod"],
    supportedDesignMethods: ["ASD", "LRFD"]
  });
} 