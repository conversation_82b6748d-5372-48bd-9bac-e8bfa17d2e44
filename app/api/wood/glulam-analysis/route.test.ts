/**
 * Unit tests for Glulam Analysis API
 * Tests comprehensive validation, success cases, and error handling
 */

import { NextRequest } from 'next/server';
import { POST, GET } from './route';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { BeamData, BeamPropertiesState } from '@/lib/types/beam/beam-data';
import { SupportType } from '@/lib/types/support/support-type';
import { Support } from '@/lib/types/support/support';
import { DesignValues } from '@/components/materials';

// Mock the dependencies
jest.mock('@/lib/beam-analysis/server-calculations');
jest.mock('@/lib/utils/wood/beam');

import { performServerSideCalculations } from '@/lib/beam-analysis/server-calculations';
import { analyzeGlulamBeam } from '@/lib/utils/wood/beam';

const mockPerformServerSideCalculations = performServerSideCalculations as jest.MockedFunction<typeof performServerSideCalculations>;
const mockAnalyzeGlulamBeam = analyzeGlulamBeam as jest.MockedFunction<typeof analyzeGlulamBeam>;

describe('Glulam Analysis API', () => {
  // Test data setup
  const validBeamData: BeamData = {
    properties: {
      length: 30,
      elasticModulus: 1800000,
      momentOfInertia: 455.625,
      area: 37.5,
    },
    supports: [
      new Support(SupportType.PIN, 0),
      new Support(SupportType.ROLLER, 30)
    ],
    loadGroups: []
  };

  const validGlulamDesignValues: Partial<DesignValues> = {
    Fb: 2400,
    Fb_pos: 2400,
    Fb_neg: 1850,
    Ft: 1450,
    Fv: 265,
    Fc_perp: 650,
    Fc: 1600,
    E: 1800000,
    Emin: 850000,
    G: 90000,
    minThickness: 1.5,
    maxThickness: 10.75,
    minWidth: 2.5,
    maxWidth: 12.0,
    commercial_grade: '24F-V4',
    speciesCombination: 'Western Species',
    grading_rules_agency: 'APA',
    design_values_table: 'NDS 2018 Table 5A',
    location: 'US',
    version: '2018'
  };

  const validGlulamBeamPropertiesState: Partial<BeamPropertiesState> = {
    lumberType: 'glulam',
    selectedSpecies: 'Douglas Fir',
    designValues: validGlulamDesignValues as DesignValues,
    selectedGluLamProperties: {
      speciesGroup: 'Western Species',
      species: 'Douglas Fir',
      width: 3.125,
      depth: 12.0,
      selectedCombinationSymbolKey: '24F-V4',
      grade: 'V4',
    },
    moistureContent: 12,
    temperature: 70,
    includeBeamWeight: false,
    manual_totalDeflectionLimit: 240,
  };

  const validGlulamStructuralResults = {
    summaryData: {
      maxMomentValue: { value: 120000 }, // lb-in
      maxShearValue: { value: 3000 }, // lb
      maxTotalDeflectionDownward: { value: 0.75 }, // in
    },
    diagramData: {},
  };

  const validGlulamWoodAnalysisResults = {
    inputs: {},
    analysisResults: {},
    adjustmentFactors: {
      CD: 1.0, CM: 1.0, Ct: 1.0, CL: 1.0, Cfu: 1.0, Cb: 1.0,
      CV: 1.0, Cc: 1.0, CI: 1.0, Cvr: 1.0, CP: 1.0,
      KF: 1.0, phi: 1.0, lambda: 1.0
    },
    designValueSpecificFactors: {
      Fbx_pos: { CD: 1.0, CM: 1.0, Ct: 1.0, CL: 0.95, CV: 1.05, Cfu: 1.0, Cc: 1.0, CI: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fbx_neg: { CD: 1.0, CM: 1.0, Ct: 1.0, CL: 0.95, CV: 1.05, Cfu: 1.0, Cc: 1.0, CI: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fby: { CD: 1.0, CM: 1.0, Ct: 1.0, CL: 0.95, CV: 1.05, Cfu: 1.0, Cc: 1.0, CI: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Ft: { CD: 1.0, CM: 1.0, Ct: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fvx: { CD: 1.0, CM: 1.0, Ct: 1.0, Cvr: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fvy: { CD: 1.0, CM: 1.0, Ct: 1.0, Cvr: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fc_perp_x: { CM: 1.0, Ct: 1.0, Cb: 1.0 },
      Fc_perp_y: { CM: 1.0, Ct: 1.0, Cb: 1.0 },
      Fc: { CD: 1.0, CM: 1.0, Ct: 1.0, CP: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Ex: { CM: 1.0, Ct: 1.0 },
      Ex_min: { CM: 1.0, Ct: 1.0 },
      Ey: { CM: 1.0, Ct: 1.0 },
      Ey_min: { CM: 1.0, Ct: 1.0 },
    },
    adjustedDesignValues: {
      Fbx_pos_prime: 2400, Fbx_neg_prime: 1850, Fby_prime: 1450,
      Ft_prime: 1450, Fvx_prime: 265, Fvy_prime: 265,
      Fc_perp_x_prime: 650, Fc_perp_y_prime: 650, Fc_prime: 1600,
      Ex_prime: 1800000, Ex_min_prime: 850000,
      Ey_prime: 1600000, Ey_min_prime: 850000,
      Frt_prime: 100, Frc_prime: 500
    },
    bendingCheck: { 
      allowableStress: 2400, actualStress: 1800, ratio: 0.75, passes: true,
      stressType: 'positive'
    },
    shearCheck: { allowableStress: 265, actualStress: 200, ratio: 0.75, passes: true },
    deflectionCheck: { allowableDeflection: 1.5, actualDeflection: 0.75, ratio: 0.5, passes: true },
    isDesignAcceptable: true,
    controllingCriteria: 'Bending',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockPerformServerSideCalculations.mockResolvedValue(validGlulamStructuralResults as any);
    mockAnalyzeGlulamBeam.mockReturnValue(validGlulamWoodAnalysisResults as any);
  });

  describe('GET method', () => {
    it('should return API information', async () => {
      const request = new NextRequest('http://localhost/api/wood/glulam-analysis');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Glulam Beam Analysis API');
      expect(data.supportedDesignMethods).toEqual(['ASD', 'LRFD']);
    });
  });

  describe('POST method - Success Cases', () => {
    it('should successfully analyze glulam beam with valid inputs (Imperial)', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validGlulamBeamPropertiesState,
        designMethod: 'ASD'
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.structuralAnalysis).toBeDefined();
      expect(data.woodAnalysis).toBeDefined();
      expect(data.adjustmentFactors).toBeDefined();
      expect(data.adjustedDesignValues).toBeDefined();
      expect(data.designChecks).toBeDefined();
      expect(data.summary).toBeDefined();

      // Check glulam-specific properties
      expect(data.adjustmentFactors).toHaveProperty('Fbx_pos'); // Bending factors
      expect(data.adjustmentFactors.Fbx_pos).toHaveProperty('CV'); // Volume factor
      expect(data.adjustmentFactors.Fbx_pos).toHaveProperty('Cc'); // Curvature factor
      expect(data.adjustmentFactors.Fbx_pos).toHaveProperty('CI'); // Stress interaction factor
      expect(data.adjustmentFactors).toHaveProperty('Fvx'); // Shear factors
      expect(data.adjustmentFactors.Fvx).toHaveProperty('Cvr'); // Shear reduction factor

      expect(data.adjustedDesignValues).toHaveProperty('Fbx_pos_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fbx_neg_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fby_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fvx_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fvy_prime');

      expect(data.summary).toHaveProperty('primaryAxis');
      expect(data.summary).toHaveProperty('layupType');
    });

    it('should successfully analyze glulam beam with valid inputs (Metric)', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.METRIC,
        beamPropertiesState: validGlulamBeamPropertiesState,
        designMethod: 'LRFD'
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(mockPerformServerSideCalculations).toHaveBeenCalledWith(
        expect.objectContaining({
          ...validBeamData,
          selectedLoadCombos: []
        }),
        UnitSystem.METRIC,
        validGlulamBeamPropertiesState
      );
    });

    it('should work with manual dimensions instead of glulam properties', async () => {
      const beamPropertiesWithManualDimensions = {
        ...validGlulamBeamPropertiesState,
        selectedGluLamProperties: {
          ...validGlulamBeamPropertiesState.selectedGluLamProperties!,
          width: undefined,
          depth: undefined,
        },
        manualWidth: '3.125',
        manualDepth: '12.0',
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: beamPropertiesWithManualDimensions,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('POST method - Validation Errors', () => {
    it('should return 400 for missing beamData', async () => {
      const requestBody = {
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validGlulamBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Missing required parameters');
    });

    it('should return 400 for missing unitSystem', async () => {
      const requestBody = {
        beamData: validBeamData,
        beamPropertiesState: validGlulamBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Missing required parameters');
    });

    it('should return 400 for missing beamPropertiesState', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Missing beam properties state');
    });

    it('should return 400 for wrong lumber type', async () => {
      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        lumberType: 'sawn' as const
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('specifically for glulam analysis');
    });
  });

  describe('POST method - Design Values Validation', () => {
    it('should return 200 with limited analysis for missing design values', async () => {
      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        designValues: undefined
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.structuralAnalysis).toBeDefined();
      expect(data.woodAnalysis).toBeNull();
      expect(data.adjustmentFactors).toBeNull();
      expect(data.adjustedDesignValues).toBeNull();
      expect(data.designChecks).toBeNull();
      expect(data.summary.controllingCriteria).toBe("Design values required");
      expect(data.message).toContain("Select glulam species and grade to perform wood design checks");
    });

    it('should return 400 for missing Fb and Fb_pos/Fb_neg', async () => {
      const invalidDesignValues = { ...validGlulamDesignValues };
      delete (invalidDesignValues as any).Fb;
      delete (invalidDesignValues as any).Fb_pos;
      delete (invalidDesignValues as any).Fb_neg;

      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        designValues: invalidDesignValues as DesignValues
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid design values');
      expect(data.details).toContain('Fb (bending stress) or Fb_pos/Fb_neg (positive/negative bending stress) is required and must be positive');
    });

    it('should accept valid Fb when Fb_pos/Fb_neg are missing', async () => {
      const validDesignValuesWithFb = { ...validGlulamDesignValues };
      delete (validDesignValuesWithFb as any).Fb_pos;
      delete (validDesignValuesWithFb as any).Fb_neg;
      // Keep Fb as valid

      const beamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        designValues: validDesignValuesWithFb as DesignValues
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: beamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('should return 400 for negative Ft', async () => {
      const invalidDesignValues = { ...validGlulamDesignValues, Ft: -100 };

      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        designValues: invalidDesignValues as DesignValues
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.details).toContain('Ft (tension stress parallel to grain) is required and must be a positive number');
    });

    it('should validate all required design values', async () => {
      const requiredValues = ['Ft', 'Fv', 'Fc_perp', 'Fc', 'E', 'Emin', 'G'];
      
      for (const valueKey of requiredValues) {
        const invalidDesignValues = { ...validGlulamDesignValues };
        delete (invalidDesignValues as any)[valueKey];

        const invalidBeamPropertiesState = {
          ...validGlulamBeamPropertiesState,
          designValues: invalidDesignValues as DesignValues
        };

        const requestBody = {
          beamData: validBeamData,
          unitSystem: UnitSystem.IMPERIAL,
          beamPropertiesState: invalidBeamPropertiesState,
        };

        const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
          method: 'POST',
          body: JSON.stringify(requestBody)
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toBe('Missing or invalid design values');
        expect(data.details.some((detail: string) => detail.includes(valueKey))).toBe(true);
      }
    });
  });

  describe('POST method - Geometry Validation', () => {
    it('should return 400 for missing width and depth', async () => {
      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        manualWidth: undefined,
        manualDepth: undefined,
        selectedGluLamProperties: {
          ...validGlulamBeamPropertiesState.selectedGluLamProperties!,
          width: undefined,
          depth: undefined,
        }
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid member geometry');
      expect(data.details).toContain('Member width is required and must be a positive number (provide selectedGluLamProperties.width or manualWidth)');
      expect(data.details).toContain('Member depth is required and must be a positive number (provide selectedGluLamProperties.depth or manualDepth)');
    });

    it('should return 400 for negative width', async () => {
      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        selectedGluLamProperties: {
          ...validGlulamBeamPropertiesState.selectedGluLamProperties!,
          width: -3.125,
        }
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid member geometry');
      expect(data.details).toContain('Member width is required and must be a positive number (provide selectedGluLamProperties.width or manualWidth)');
    });

    it('should return 400 for zero depth from manual input', async () => {
      const invalidBeamPropertiesState = {
        ...validGlulamBeamPropertiesState,
        selectedGluLamProperties: {
          ...validGlulamBeamPropertiesState.selectedGluLamProperties!,
          width: undefined,
          depth: undefined,
        },
        manualWidth: '3.125',
        manualDepth: '0',
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid member geometry');
      expect(data.details).toContain('Member depth is required and must be a positive number (provide selectedGluLamProperties.depth or manualDepth)');
    });
  });

  describe('POST method - Error Handling', () => {
    it('should return 500 for invalid JSON', async () => {
      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: 'invalid json'
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Server Error');
    });

    it('should return 500 for structural analysis failure', async () => {
      mockPerformServerSideCalculations.mockResolvedValue(null);

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validGlulamBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Structural analysis failed');
    });

    it('should return 500 for glulam analysis error', async () => {
      mockAnalyzeGlulamBeam.mockImplementation(() => {
        throw new Error('Glulam analysis calculation error');
      });

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validGlulamBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Server Error');
      expect(data.success).toBe(false);
    });
  });

  describe('POST method - Response Structure', () => {
    it('should return correct glulam response structure', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validGlulamBeamPropertiesState,
        designMethod: 'ASD'
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('structuralAnalysis');
      expect(data).toHaveProperty('woodAnalysis');
      expect(data).toHaveProperty('adjustmentFactors');
      expect(data).toHaveProperty('adjustedDesignValues');
      expect(data).toHaveProperty('designChecks');
      expect(data).toHaveProperty('summary');
      expect(data).toHaveProperty('success', true);

      // Check glulam-specific adjustment factors
      expect(data.adjustmentFactors).toHaveProperty('Fbx_pos'); // Bending factors
      expect(data.adjustmentFactors.Fbx_pos).toHaveProperty('CV'); // Volume factor
      expect(data.adjustmentFactors.Fbx_pos).toHaveProperty('Cc'); // Curvature factor
      expect(data.adjustmentFactors.Fbx_pos).toHaveProperty('CI'); // Stress interaction factor
      expect(data.adjustmentFactors).toHaveProperty('Fvx'); // Shear factors
      expect(data.adjustmentFactors.Fvx).toHaveProperty('Cvr'); // Shear reduction factor

      // Check glulam-specific adjusted design values
      expect(data.adjustedDesignValues).toHaveProperty('Fbx_pos_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fbx_neg_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fby_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fvx_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fvy_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fc_perp_x_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fc_perp_y_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Ex_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Ey_prime');

      // Check glulam-specific design checks
      expect(data.designChecks.bending).toHaveProperty('stressType');

      // Check glulam-specific summary
      expect(data.summary).toHaveProperty('primaryAxis');
      expect(data.summary).toHaveProperty('layupType');
      expect(data.summary).toHaveProperty('designMethod', 'ASD');
    });
  });

  describe('POST method - Unit Conversion', () => {
    it('should handle metric unit conversion properly', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.METRIC,
        beamPropertiesState: validGlulamBeamPropertiesState,
        designMethod: 'ASD'
      };

      const request = new NextRequest('http://localhost/api/wood/glulam-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify that the wood analysis module is called with the converted inputs
      expect(mockAnalyzeGlulamBeam).toHaveBeenCalled();
      
      // The response should contain converted values back to metric
      expect(typeof data.adjustedDesignValues.Fbx_pos_prime).toBe('number');
      expect(typeof data.adjustedDesignValues.Ex_prime).toBe('number');
    });
  });
}); 