/**
 * Unit tests for Sawn Lumber Analysis API
 * Tests comprehensive validation, success cases, and error handling
 */

import { NextRequest } from 'next/server';
import { POST, GET } from './route';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { BeamData, BeamPropertiesState } from '@/lib/types/beam/beam-data';
import { SupportType } from '@/lib/types/support/support-type';
import { Support } from '@/lib/types/support/support';
import { DesignValues } from '@/components/materials';

// Mock the dependencies
jest.mock('@/lib/beam-analysis/server-calculations');
jest.mock('@/lib/utils/wood/beam');

import { performServerSideCalculations } from '@/lib/beam-analysis/server-calculations';
import { analyzeSawnLumberBeam } from '@/lib/utils/wood/beam';

const mockPerformServerSideCalculations = performServerSideCalculations as jest.MockedFunction<typeof performServerSideCalculations>;
const mockAnalyzeSawnLumberBeam = analyzeSawnLumberBeam as jest.MockedFunction<typeof analyzeSawnLumberBeam>;

describe('Sawn Lumber Analysis API', () => {
  // Test data setup
  const validBeamData: BeamData = {
    properties: {
      length: 20,
      elasticModulus: 1600000,
      momentOfInertia: 177.979,
      area: 12.1875,
    },
    supports: [
      new Support(SupportType.PIN, 0),
      new Support(SupportType.ROLLER, 20)
    ],
    loadGroups: []
  };

  const validDesignValues: Partial<DesignValues> = {
    Fb: 1000,
    Ft: 675,
    Fv: 175,
    Fc_perp: 625,
    Fc: 1350,
    E: 1600000,
    Emin: 580000,
    G: 90000,
    minThickness: 0.75,
    maxThickness: 4.0,
    minWidth: 2.0,
    maxWidth: 12.0,
    commercial_grade: 'Select Structural',
    speciesCombination: 'Douglas Fir-Larch',
    grading_rules_agency: 'WWPA',
    design_values_table: 'NDS 2018',
    location: 'US',
    version: '2018'
  };

  const validBeamPropertiesState: Partial<BeamPropertiesState> = {
    lumberType: 'sawn',
    selectedSpecies: 'Douglas Fir-Larch',
    selectedGrade: 'Select Structural',
    selectedSizeClassification: 'Dimension Lumber',
    designValues: validDesignValues as DesignValues,
    manualWidth: '3.5',
    manualDepth: '11.25',
    moistureContent: 19,
    temperature: 70,
    isIncised: false,
    isRepetitiveMember: false,
    includeBeamWeight: false,
    manual_totalDeflectionLimit: 240,
  };

  const validStructuralResults = {
    summaryData: {
      maxMomentValue: { value: 50000 }, // lb-in
      maxShearValue: { value: 2000 }, // lb
      maxTotalDeflectionDownward: { value: 0.5 }, // in
    },
    diagramData: {}, // Added to satisfy AnalysisOutput interface
  };

  const validWoodAnalysisResults = {
    inputs: {}, // Added to satisfy SawnLumberBeamAnalysisResults interface
    analysisResults: {}, // Added to satisfy SawnLumberBeamAnalysisResults interface
    adjustmentFactors: {
      CD: 1.0, CM: 1.0, Ct: 1.0, CL: 1.0, CF: 1.0, Cfu: 1.0,
      Ci: 1.0, Cr: 1.0, CP: 1.0, Cb: 1.0, CT: 1.0, CV: 1.0,
      KF: 1.0, phi: 1.0, lambda: 1.0
    },
    designValueSpecificFactors: {
      Fb: { CD: 1.0, CM: 1.0, Ct: 1.0, CL: 0.95, CF: 1.2, Cfu: 1.0, Ci: 1.0, Cr: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Ft: { CD: 1.0, CM: 1.0, Ct: 1.0, CF: 1.2, Ci: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fv: { CD: 1.0, CM: 1.0, Ct: 1.0, Ci: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      Fc_perp: { CM: 1.0, Ct: 1.0, Ci: 1.0, Cb: 1.0 },
      Fc: { CD: 1.0, CM: 1.0, Ct: 1.0, CF: 1.2, Ci: 1.0, CP: 1.0, KF: 1.0, phi: 1.0, lambda: 1.0 },
      E: { CM: 1.0, Ct: 1.0, Ci: 1.0 },
      Emin: { CM: 1.0, Ct: 1.0, Ci: 1.0, CT: 1.0 },
    },
    adjustedDesignValues: {
      Fb_prime: 1000, Ft_prime: 675, Fv_prime: 175,
      Fc_perp_prime: 625, Fc_prime: 1350, E_prime: 1600000, Emin_prime: 580000
    },
    bendingCheck: { allowableStress: 1000, actualStress: 800, ratio: 0.8, passes: true },
    shearCheck: { allowableStress: 175, actualStress: 150, ratio: 0.86, passes: true },
    deflectionCheck: { allowableDeflection: 1.0, actualDeflection: 0.5, ratio: 0.5, passes: true },
    isDesignAcceptable: true,
    controllingCriteria: 'Bending',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockPerformServerSideCalculations.mockResolvedValue(validStructuralResults as any);
    mockAnalyzeSawnLumberBeam.mockReturnValue(validWoodAnalysisResults as any);
  });

  describe('GET method', () => {
    it('should return API information', async () => {
      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Sawn Lumber Beam Analysis API');
      expect(data.supportedDesignMethods).toEqual(['ASD', 'LRFD']);
    });
  });

  describe('POST method - Success Cases', () => {
    it('should successfully analyze sawn lumber beam with valid inputs (Imperial)', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validBeamPropertiesState,
        designMethod: 'ASD'
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.structuralAnalysis).toBeDefined();
      expect(data.woodAnalysis).toBeDefined();
      expect(data.adjustmentFactors).toBeDefined();
      expect(data.adjustedDesignValues).toBeDefined();
      expect(data.designChecks).toBeDefined();
      expect(data.summary).toBeDefined();
    });

    it('should successfully analyze sawn lumber beam with valid inputs (Metric)', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.METRIC,
        beamPropertiesState: validBeamPropertiesState,
        designMethod: 'LRFD'
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(mockPerformServerSideCalculations).toHaveBeenCalledWith(
        expect.objectContaining({
          ...validBeamData,
          selectedLoadCombos: []
        }),
        UnitSystem.METRIC,
        validBeamPropertiesState
      );
    });

    it('should work with lumber properties instead of manual dimensions', async () => {
      const beamPropertiesWithLumberProps = {
        ...validBeamPropertiesState,
        manualWidth: undefined,
        manualDepth: undefined,
        lumberProperties: {
          width: 3.5,
          depth: 11.25,
        }
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: beamPropertiesWithLumberProps,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('POST method - Validation Errors', () => {
    it('should return 400 for missing beamData', async () => {
      const requestBody = {
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Missing required parameters');
    });

    it('should return 400 for missing unitSystem', async () => {
      const requestBody = {
        beamData: validBeamData,
        beamPropertiesState: validBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Missing required parameters');
    });

    it('should return 400 for missing beamPropertiesState', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('Missing beam properties state');
    });

    it('should return 400 for wrong lumber type', async () => {
      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        lumberType: 'glulam' as const
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('specifically for sawn lumber analysis');
    });
  });

  describe('POST method - Design Values Validation', () => {
    it('should return 400 for missing design values', async () => {
      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        designValues: undefined
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid design values');
      expect(data.details).toContain('Design values are required but not provided');
    });

    it('should return 400 for missing Fb', async () => {
      const invalidDesignValues = { ...validDesignValues };
      delete (invalidDesignValues as any).Fb;

      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        designValues: invalidDesignValues as DesignValues
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid design values');
      expect(data.details).toContain('Fb (bending stress) is required and must be a positive number');
    });

    it('should return 400 for negative Fb', async () => {
      const invalidDesignValues = { ...validDesignValues, Fb: -100 };

      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        designValues: invalidDesignValues as DesignValues
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.details).toContain('Fb (bending stress) is required and must be a positive number');
    });

    it('should return 400 for multiple missing design values', async () => {
      const invalidDesignValues = { ...validDesignValues };
      delete (invalidDesignValues as any).Fb;
      delete (invalidDesignValues as any).Ft;
      delete (invalidDesignValues as any).E;

      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        designValues: invalidDesignValues as DesignValues
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid design values');
      expect(data.details).toHaveLength(3);
      expect(data.details).toContain('Fb (bending stress) is required and must be a positive number');
      expect(data.details).toContain('Ft (tension stress parallel to grain) is required and must be a positive number');
      expect(data.details).toContain('E (modulus of elasticity) is required and must be a positive number');
    });

    it('should validate all required design values', async () => {
      const requiredValues = ['Fb', 'Ft', 'Fv', 'Fc_perp', 'Fc', 'E', 'Emin', 'G'];
      
      for (const valueKey of requiredValues) {
        const invalidDesignValues = { ...validDesignValues };
        delete (invalidDesignValues as any)[valueKey];

        const invalidBeamPropertiesState = {
          ...validBeamPropertiesState,
          designValues: invalidDesignValues as DesignValues
        };

        const requestBody = {
          beamData: validBeamData,
          unitSystem: UnitSystem.IMPERIAL,
          beamPropertiesState: invalidBeamPropertiesState,
        };

        const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
          method: 'POST',
          body: JSON.stringify(requestBody)
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toBe('Missing or invalid design values');
        expect(data.details.some((detail: string) => detail.includes(valueKey))).toBe(true);
      }
    });
  });

  describe('POST method - Geometry Validation', () => {
    it('should return 400 for missing width and depth', async () => {
      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        manualWidth: undefined,
        manualDepth: undefined,
        lumberProperties: undefined
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid member geometry');
      expect(data.details).toContain('Member width is required and must be a positive number (provide manualWidth or lumberProperties.width)');
      expect(data.details).toContain('Member depth is required and must be a positive number (provide manualDepth or lumberProperties.depth)');
    });

    it('should return 400 for negative width', async () => {
      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        manualWidth: '-3.5',
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid member geometry');
      expect(data.details).toContain('Member width is required and must be a positive number (provide manualWidth or lumberProperties.width)');
    });

    it('should return 400 for zero depth', async () => {
      const invalidBeamPropertiesState = {
        ...validBeamPropertiesState,
        manualDepth: '0',
      };

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: invalidBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Missing or invalid member geometry');
      expect(data.details).toContain('Member depth is required and must be a positive number (provide manualDepth or lumberProperties.depth)');
    });
  });

  describe('POST method - Error Handling', () => {
    it('should return 500 for invalid JSON', async () => {
      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: 'invalid json'
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Server Error');
    });

    it('should return 500 for structural analysis failure', async () => {
      mockPerformServerSideCalculations.mockResolvedValue(null);

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Structural analysis failed');
    });

    it('should return 500 for wood analysis error', async () => {
      mockAnalyzeSawnLumberBeam.mockImplementation(() => {
        throw new Error('Wood analysis calculation error');
      });

      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validBeamPropertiesState,
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('Server Error');
      expect(data.success).toBe(false);
    });
  });

  describe('POST method - Response Structure', () => {
    it('should return correct response structure', async () => {
      const requestBody = {
        beamData: validBeamData,
        unitSystem: UnitSystem.IMPERIAL,
        beamPropertiesState: validBeamPropertiesState,
        designMethod: 'ASD'
      };

      const request = new NextRequest('http://localhost/api/wood/sawn-lumber-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('structuralAnalysis');
      expect(data).toHaveProperty('woodAnalysis');
      expect(data).toHaveProperty('adjustmentFactors');
      expect(data).toHaveProperty('adjustedDesignValues');
      expect(data).toHaveProperty('designChecks');
      expect(data).toHaveProperty('summary');
      expect(data).toHaveProperty('success', true);

      // Check adjustment factors structure
      expect(data.adjustmentFactors).toHaveProperty('Fb'); // Bending factors
      expect(data.adjustmentFactors.Fb).toHaveProperty('CD');
      expect(data.adjustmentFactors.Fb).toHaveProperty('CM');
      expect(data.adjustmentFactors.Fb).toHaveProperty('CL');
      expect(data.adjustmentFactors.Fb).toHaveProperty('CF');

      // Check adjusted design values structure
      expect(data.adjustedDesignValues).toHaveProperty('Fb_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Ft_prime');
      expect(data.adjustedDesignValues).toHaveProperty('Fv_prime');
      expect(data.adjustedDesignValues).toHaveProperty('E_prime');

      // Check design checks structure
      expect(data.designChecks).toHaveProperty('bending');
      expect(data.designChecks).toHaveProperty('shear');
      expect(data.designChecks).toHaveProperty('deflection');
      expect(data.designChecks.bending).toHaveProperty('passes');
      expect(data.designChecks.bending).toHaveProperty('ratio');

      // Check summary structure
      expect(data.summary).toHaveProperty('isDesignAcceptable');
      expect(data.summary).toHaveProperty('controllingCriteria');
      expect(data.summary).toHaveProperty('designMethod', 'ASD');
    });
  });
}); 