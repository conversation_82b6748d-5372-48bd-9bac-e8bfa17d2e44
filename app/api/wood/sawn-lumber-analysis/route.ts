import { NextResponse } from "next/server";
import { BeamData, BeamPropertiesState } from "@/lib/types/beam/beam-data";
import { UnitSystem } from "@/lib/types/units/unit-system";
import { performServerSideCalculations } from "@/lib/beam-analysis/server-calculations";
import {
  analyzeSawnLumberBeam,
  SawnLumberBeamAnalysisInputs,
  SawnLumberBeamAnalysisResults,
  ReferenceDesignValues,
  MemberGeometry,
  ServiceConditions,
  MaterialProperties,
  InputAnalysisResults,
  DesignMethod,
} from "@/lib/utils/wood/beam";
import { DESIGN_METHODS } from "@/lib/utils/wood/constants";
import { LoadType } from "@/lib/types/load/load-type";
import {
  getControllingLoadDurationFactor,
  getLoadDurationAnalysis,
} from "@/lib/utils/wood/adjustment-factors/load-duration/load-duration";
import {
  ftToIn,
  inToFt,
  kipToLb,
  lbToKip,
  kipFtToLbIn,
  lbInToKipFt,
  mToIn,
  inToM,
  nToLb,
  lbToN,
  nmToLbIn,
  lbInToNm,
  paToPsi,
  psiToPa,
} from "@/lib/utils/metric-converter";

export const dynamic = "force-dynamic";

interface SawnLumberAnalysisRequest {
  beamData: BeamData;
  unitSystem: UnitSystem;
  beamPropertiesState: BeamPropertiesState | null;
  designMethod?: 'ASD' | 'LRFD';
}

interface SawnLumberAnalysisResponse {
  structuralAnalysis: any; // Results from performServerSideCalculations
  woodAnalysis: SawnLumberBeamAnalysisResults;
  adjustmentFactors: any; // DesignValueSpecificFactors from woodAnalysis
  adjustedDesignValues: any; // AdjustedDesignValues from woodAnalysis
  designChecks: {
    bending: {
      allowableStress: number;
      actualStress: number;
      ratio: number;
      passes: boolean;
    };
    shear: {
      allowableStress: number;
      actualStress: number;
      ratio: number;
      passes: boolean;
    };
    deflection: {
      allowableDeflection: number;
      actualDeflection: number;
      ratio: number;
      passes: boolean;
    };
  };
  summary: {
    isDesignAcceptable: boolean;
    controllingCriteria: string;
    designMethod: string;
    deflectionLimit: number;
  };
  success: boolean;
  message?: string;
}

/**
 * Validate that all required design values are present
 */
function validateSawnLumberDesignValues(designValues: any): string[] {
  const errors: string[] = [];
  
  if (!designValues) {
    errors.push("Design values are required but not provided");
    return errors;
  }

  // Check required stress values
  if (typeof designValues.Fb !== 'number' || designValues.Fb <= 0) {
    errors.push("Fb (bending stress) is required and must be a positive number");
  }
  if (typeof designValues.Ft !== 'number' || designValues.Ft <= 0) {
    errors.push("Ft (tension stress parallel to grain) is required and must be a positive number");
  }
  if (typeof designValues.Fv !== 'number' || designValues.Fv <= 0) {
    errors.push("Fv (shear stress parallel to grain) is required and must be a positive number");
  }
  if (typeof designValues.Fc_perp !== 'number' || designValues.Fc_perp <= 0) {
    errors.push("Fc_perp (compression stress perpendicular to grain) is required and must be a positive number");
  }
  if (typeof designValues.Fc !== 'number' || designValues.Fc <= 0) {
    errors.push("Fc (compression stress parallel to grain) is required and must be a positive number");
  }

  // Check required modulus values
  if (typeof designValues.E !== 'number' || designValues.E <= 0) {
    errors.push("E (modulus of elasticity) is required and must be a positive number");
  }
  if (typeof designValues.Emin !== 'number' || designValues.Emin <= 0) {
    errors.push("Emin (minimum modulus of elasticity) is required and must be a positive number");
  }

  // G is often calculated or can have a reasonable default based on E
  // but let's require it to be explicit
  if (typeof designValues.G !== 'number' || designValues.G <= 0) {
    errors.push("G (shear modulus) is required and must be a positive number");
  }

  return errors;
}

/**
 * Validate that all required member geometry is present
 */
function validateMemberGeometry(beamPropertiesState: BeamPropertiesState): string[] {
  const errors: string[] = [];

  // Check width
  const width = beamPropertiesState.manualWidth ? parseFloat(beamPropertiesState.manualWidth) : 
                beamPropertiesState.lumberProperties?.standard_width;
  if (!width || width <= 0) {
    errors.push("Member width is required and must be a positive number (provide manualWidth or lumberProperties.standard_width)");
  }

  // Check depth  
  const depth = beamPropertiesState.manualDepth ? parseFloat(beamPropertiesState.manualDepth) : 
                beamPropertiesState.lumberProperties?.standard_depth;
  if (!depth || depth <= 0) {
    errors.push("Member depth is required and must be a positive number (provide manualDepth or lumberProperties.standard_depth)");
  }

  return errors;
}

/**
 * Convert beam properties and analysis results to sawn lumber analysis inputs
 */
function convertToSawnLumberInputs(
  beamData: BeamData,
  beamPropertiesState: BeamPropertiesState,
  structuralResults: any,
  designMethod: DesignMethod,
  unitSystem: UnitSystem
): SawnLumberBeamAnalysisInputs {
  
  // Convert structural analysis results to consistent units (lb-in system)
  let maxMoment = structuralResults.summaryData.maxMomentValue?.value || 0;
  let maxShear = structuralResults.summaryData.maxShearValue?.value || 0;
  let maxDeflection = Math.abs(structuralResults.summaryData.maxTotalDeflectionDownward?.value || 0);
  let beamLength = beamData.properties.length;

  // Convert units if needed
  if (unitSystem === UnitSystem.METRIC) {
    maxMoment = nmToLbIn(maxMoment);
    maxShear = nToLb(maxShear);
    maxDeflection = mToIn(maxDeflection);
    beamLength = mToIn(beamLength);
  } else if (unitSystem === UnitSystem.IMPERIAL) {
    maxMoment = kipFtToLbIn(maxMoment);
    maxShear = kipToLb(maxShear);
    // maxDeflection already in inches
    beamLength = ftToIn(beamLength);
  }

  // Extract dimensions from beam properties (already validated)
  const width = beamPropertiesState.manualWidth ? parseFloat(beamPropertiesState.manualWidth) : 
                beamPropertiesState.lumberProperties!.standard_width;
  const depth = beamPropertiesState.manualDepth ? parseFloat(beamPropertiesState.manualDepth) : 
                beamPropertiesState.lumberProperties!.standard_depth;
  
  // Member geometry
  const member: MemberGeometry = {
    length: beamLength,
    width: width,
    depth: depth,
    unbracedLength: beamPropertiesState.isBraced ? 
      0 : // When braced (switch ON), set unbracedLength to 0 (fully braced, CL = 1.0)
      (beamPropertiesState.lu ? parseFloat(beamPropertiesState.lu) : beamLength), // When unbraced (switch OFF), use actual unbraced length
  };

  // Reference design values (already validated - no defaults)
  const designValues = beamPropertiesState.designValues!;
  let referenceDesignValues: ReferenceDesignValues = {
    Fb: designValues.Fb,
    Ft: designValues.Ft,
    Fv: designValues.Fv,
    Fc_perp: designValues.Fc_perp,
    Fc: designValues.Fc,
    E: designValues.E,
    Emin: designValues.Emin,
    G: designValues.G,
  };

  // Convert reference design values from metric to imperial if needed (wood analysis works in imperial)
  if (unitSystem === UnitSystem.METRIC) {
    referenceDesignValues = {
      Fb: paToPsi(referenceDesignValues.Fb),
      Ft: paToPsi(referenceDesignValues.Ft),
      Fv: paToPsi(referenceDesignValues.Fv),
      Fc_perp: paToPsi(referenceDesignValues.Fc_perp),
      Fc: paToPsi(referenceDesignValues.Fc),
      E: paToPsi(referenceDesignValues.E),
      Emin: paToPsi(referenceDesignValues.Emin),
      G: paToPsi(referenceDesignValues.G),
    };
  }

  // Material properties
  const material: MaterialProperties = {
    species: beamPropertiesState.selectedSpecies || "Unknown Species",
    grade: beamPropertiesState.selectedGrade || "Unknown Grade", 
    lumberCategory: beamPropertiesState.selectedSizeClassification || "Unknown Category",
    referenceDesignValues,
  };

  // Service conditions - these can have reasonable defaults for non-critical parameters
  const serviceConditions: ServiceConditions = {
    moistureContent: beamPropertiesState.isWetService ? 
      (beamPropertiesState.moistureContent || 25) : // Use actual moisture content or default to 25% when wet service is ON
      12, // Use 12% (below threshold) when wet service is OFF to prevent wet service factors
    temperature: beamPropertiesState.isTemperatureFactored ? 
      (beamPropertiesState.temperature || 100) : // Use actual temperature or default to 100°F when temperature effects are ON
      70, // Use 70°F (normal temperature) when temperature effects are OFF to prevent temperature factors
    loadDuration: getControllingLoadType(beamData),
    isIncised: beamPropertiesState.isIncised || false,
    isRepetitiveMember: beamPropertiesState.isRepetitiveMember || false,
    isFlatUse: false, // Default to edge use
  };

  // Analysis results
  const analysisResults: InputAnalysisResults = {
    maxMoment,
    maxShear,
    maxDeflection,
  };

  return {
    member,
    material,
    serviceConditions,
    analysisResults,
    designMethod,
    deflectionLimit: beamPropertiesState.manual_totalDeflectionLimit || 240,
    includeSelfWeight: beamPropertiesState.includeBeamWeight || false,
  };
}

/**
 * Convert wood analysis results from Imperial (solver units) back to display units
 */
function convertWoodAnalysisResults(
  adjustmentFactors: any,
  adjustedDesignValues: any,
  designChecks: any,
  unitSystem: UnitSystem
) {
  // Adjustment factors are dimensionless, so no conversion needed
  const convertedAdjustmentFactors = { ...adjustmentFactors };

  // Convert design values from psi to Pa if metric
  const convertedAdjustedDesignValues = { ...adjustedDesignValues };
  if (unitSystem === UnitSystem.METRIC) {
    convertedAdjustedDesignValues.Fb_prime = psiToPa(adjustedDesignValues.Fb_prime);
    convertedAdjustedDesignValues.Ft_prime = psiToPa(adjustedDesignValues.Ft_prime);
    convertedAdjustedDesignValues.Fv_prime = psiToPa(adjustedDesignValues.Fv_prime);
    convertedAdjustedDesignValues.Fc_perp_prime = psiToPa(adjustedDesignValues.Fc_perp_prime);
    convertedAdjustedDesignValues.Fc_prime = psiToPa(adjustedDesignValues.Fc_prime);
    convertedAdjustedDesignValues.E_prime = psiToPa(adjustedDesignValues.E_prime);
    convertedAdjustedDesignValues.Emin_prime = psiToPa(adjustedDesignValues.Emin_prime);
  }

  // Convert design check stresses from psi to Pa if metric
  const convertedDesignChecks = { ...designChecks };
  if (unitSystem === UnitSystem.METRIC) {
    convertedDesignChecks.bending = {
      ...designChecks.bending,
      allowableStress: psiToPa(designChecks.bending.allowableStress),
      actualStress: psiToPa(designChecks.bending.actualStress),
    };
    convertedDesignChecks.shear = {
      ...designChecks.shear,
      allowableStress: psiToPa(designChecks.shear.allowableStress),
      actualStress: psiToPa(designChecks.shear.actualStress),
    };
    // Deflection values are already converted in structural analysis
  }

  return {
    convertedAdjustmentFactors,
    convertedAdjustedDesignValues,
    convertedDesignChecks,
  };
}

/**
 * Extract active load types from beam data to determine controlling load duration factor
 */
function getActiveLoadTypes(beamData: BeamData): LoadType[] {
  const activeTypes = new Set<LoadType>();
  beamData.loadGroups?.forEach(group => {
    group.loads?.forEach(load => {
      activeTypes.add(load.loadType);
    });
  });
  // Define a standard order for prioritization
  const orderedTypes = [
    LoadType.DEAD, LoadType.LIVE, LoadType.ROOF_LIVE, LoadType.SNOW,
    LoadType.WIND, LoadType.EARTHQUAKE, LoadType.RAIN, LoadType.SOIL,
    LoadType.FLOOD, LoadType.TEMPERATURE
  ];
  return orderedTypes.filter(type => activeTypes.has(type));
}

/**
 * Determine the controlling load type for load duration factor calculation
 */
function getControllingLoadType(beamData: BeamData): LoadType {
  const activeLoadTypes = getActiveLoadTypes(beamData);
  
  if (activeLoadTypes.length === 0) {
    console.warn("No active load types found in beam data, defaulting to LIVE load duration");
    return LoadType.LIVE;
  }
  
  if (activeLoadTypes.length === 1) {
    console.log(`Single load type found: ${activeLoadTypes[0]}, using for load duration factor`);
    return activeLoadTypes[0];
  }
  
  // Multiple load types - use load duration analysis to find controlling
  try {
    const analysis = getLoadDurationAnalysis(activeLoadTypes);
    console.log(`Multiple load types found [${activeLoadTypes.join(', ')}], controlling type: ${analysis.controllingLoadType} (CD = ${analysis.controllingFactor})`);
    return analysis.controllingLoadType;
  } catch (error) {
    console.error("Error determining controlling load type:", error);
    console.log("Falling back to LIVE load duration");
    return LoadType.LIVE;
  }
}

export async function POST(request: Request) {
  console.log("[API /api/wood/sawn-lumber-analysis] POST request received.");
  
  try {
    const body = (await request.json()) as SawnLumberAnalysisRequest;
    const { beamData, unitSystem, beamPropertiesState, designMethod = 'ASD' } = body;

    // Validate inputs
    if (!beamData || !unitSystem) {
      return NextResponse.json(
        { error: "Missing required parameters: beamData and unitSystem." },
        { status: 400 }
      );
    }

    if (!beamPropertiesState) {
      return NextResponse.json(
        { error: "Missing beam properties state for wood analysis." },
        { status: 400 }
      );
    }

    // Ensure we're analyzing sawn lumber
    if (beamPropertiesState.lumberType !== "sawn") {
      return NextResponse.json(
        { error: "This endpoint is specifically for sawn lumber analysis. Use glulam endpoint for glulam analysis." },
        { status: 400 }
      );
    }

    // Validate required design values
    const designValueErrors = validateSawnLumberDesignValues(beamPropertiesState.designValues);
    if (designValueErrors.length > 0) {
      return NextResponse.json(
        { 
          error: "Missing or invalid design values", 
          details: designValueErrors,
          success: false 
        },
        { status: 400 }
      );
    }

    // Validate required member geometry
    const geometryErrors = validateMemberGeometry(beamPropertiesState);
    if (geometryErrors.length > 0) {
      return NextResponse.json(
        { 
          error: "Missing or invalid member geometry", 
          details: geometryErrors,
          success: false 
        },
        { status: 400 }
      );
    }

    console.log("[API /api/wood/sawn-lumber-analysis] Performing structural analysis...");
    
    // Ensure beamData includes selected load combinations from beam properties
    const beamDataWithCombos = {
      ...beamData,
      selectedLoadCombos: beamPropertiesState.selectedLoadCombos || beamData.selectedLoadCombos || []
    };
    
    // First, perform the standard structural analysis
    const structuralResults = await performServerSideCalculations(
      beamDataWithCombos,
      unitSystem,
      beamPropertiesState
    );

    if (!structuralResults) {
      return NextResponse.json(
        { error: "Structural analysis failed to produce results." },
        { status: 500 }
      );
    }

    console.log("[API /api/wood/sawn-lumber-analysis] Performing wood-specific analysis...");

    // Convert to sawn lumber analysis inputs
    const sawnLumberInputs = convertToSawnLumberInputs(
      beamDataWithCombos,
      beamPropertiesState,
      structuralResults,
      designMethod as DesignMethod,
      unitSystem
    );

    // Perform sawn lumber analysis
    const woodAnalysis = analyzeSawnLumberBeam(sawnLumberInputs);

    const response: SawnLumberAnalysisResponse = {
      structuralAnalysis: structuralResults,
      woodAnalysis,
      adjustmentFactors: woodAnalysis.designValueSpecificFactors,
      adjustedDesignValues: woodAnalysis.adjustedDesignValues,
      designChecks: {
        bending: {
          allowableStress: woodAnalysis.bendingCheck.allowableStress,
          actualStress: woodAnalysis.bendingCheck.actualStress,
          ratio: woodAnalysis.bendingCheck.ratio,
          passes: woodAnalysis.bendingCheck.passes,
        },
        shear: {
          allowableStress: woodAnalysis.shearCheck.allowableStress,
          actualStress: woodAnalysis.shearCheck.actualStress,
          ratio: woodAnalysis.shearCheck.ratio,
          passes: woodAnalysis.shearCheck.passes,
        },
        deflection: {
          allowableDeflection: woodAnalysis.deflectionCheck.allowableDeflection,
          actualDeflection: woodAnalysis.deflectionCheck.actualDeflection,
          ratio: woodAnalysis.deflectionCheck.ratio,
          passes: woodAnalysis.deflectionCheck.passes,
        },
      },
      summary: {
        isDesignAcceptable: woodAnalysis.isDesignAcceptable,
        controllingCriteria: woodAnalysis.controllingCriteria,
        designMethod: designMethod,
        deflectionLimit: beamPropertiesState.manual_totalDeflectionLimit || 240,
      },
      success: true,
      message: "Sawn lumber beam analysis completed successfully.",
    };

    // Convert wood analysis results from Imperial (solver units) back to display units
    const {
      convertedAdjustmentFactors,
      convertedAdjustedDesignValues,
      convertedDesignChecks,
    } = convertWoodAnalysisResults(
      response.adjustmentFactors,
      response.adjustedDesignValues,
      response.designChecks,
      unitSystem
    );

    // Apply converted values to response
    response.adjustmentFactors = convertedAdjustmentFactors;
    response.adjustedDesignValues = convertedAdjustedDesignValues;
    response.designChecks = convertedDesignChecks;

    console.log("[API /api/wood/sawn-lumber-analysis] Analysis completed successfully.");
    return NextResponse.json(response);

  } catch (error) {
    let errorMessage = "An unknown error occurred during sawn lumber analysis.";
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    if (error instanceof SyntaxError) {
      errorMessage = `Invalid JSON in request body: ${errorMessage}`;
      console.error("[API /api/wood/sawn-lumber-analysis] SyntaxError:", errorMessage);
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }
    
    console.error("[API /api/wood/sawn-lumber-analysis] Error:", errorMessage, error);
    return NextResponse.json({ 
      error: `Server Error: ${errorMessage}`,
      success: false 
    }, { status: 500 });
  }
}

export async function GET(request: Request) {
  return NextResponse.json({ 
    message: "Sawn Lumber Beam Analysis API",
    description: "Use POST method to analyze sawn lumber beams with comprehensive NDS calculations.",
    requiredFields: ["beamData", "unitSystem", "beamPropertiesState"],
    optionalFields: ["designMethod"],
    supportedDesignMethods: ["ASD", "LRFD"]
  });
} 