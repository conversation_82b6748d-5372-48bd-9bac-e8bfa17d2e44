import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

interface SizeFactor {
  commercial_grade: string;
  table: string;
  min_width: number;
  max_width: number;
  min_thickness: number;
  max_thickness: number;
  CF_for_Fb: number;
  CF_for_Ft: number;
  CF_for_Fc: number;
}

// --- In-memory cache for Size Factors ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}

const CACHE_DURATION_MS_SIZE_FACTORS = 60 * 60 * 1000; // 1 hour

let sizeFactorsCache: Cache<SizeFactor[]> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Size Factors ---

export async function GET(): Promise<NextResponse> {
  // --- Cache check for Size Factors ---
  const now = Date.now();
  if (sizeFactorsCache.data && sizeFactorsCache.expires && sizeFactorsCache.expires > now) {
    console.log("Serving size factors data from cache.");
    return NextResponse.json(sizeFactorsCache.data, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh size factors data.");
  // --- End cache check for Size Factors ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'SizeFactors.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: 'Size factors data file not found' },
        { 
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    return new Promise<NextResponse>((resolve, reject) => {
      const rows: SizeFactor[] = [];

      stream
        .pipe(csv())
        .on('data', (row) => {
          const sizeFactor: SizeFactor = {
            commercial_grade: row.commercial_grade?.trim(),
            table: row.table?.trim(),
            min_width: parseFloat(row.min_width) || 0,
            max_width: row.max_width?.toLowerCase() === 'infinity' ? Infinity : parseFloat(row.max_width) || 0,
            min_thickness: parseFloat(row.min_thickness) || 0,
            max_thickness: row.max_thickness?.toLowerCase() === 'infinity' ? Infinity : parseFloat(row.max_thickness) || 0,
            CF_for_Fb: parseFloat(row.CF_for_Fb) || 1.0,
            CF_for_Ft: parseFloat(row.CF_for_Ft) || 1.0,
            CF_for_Fc: parseFloat(row.CF_for_Fc) || 1.0,
          };
          rows.push(sizeFactor);
        })
        .on('end', () => {
          // --- Cache update for Size Factors ---
          sizeFactorsCache.data = rows;
          sizeFactorsCache.expires = Date.now() + CACHE_DURATION_MS_SIZE_FACTORS;
          console.log("Size factors data cached.");
          // --- End cache update for Size Factors ---
          resolve(NextResponse.json(rows, {
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=3600',
            },
          }));
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse size factors data', details: error.message },
            { 
              status: 500,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load size factors data', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}