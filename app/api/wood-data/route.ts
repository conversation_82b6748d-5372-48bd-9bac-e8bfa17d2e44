import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';
import type { DesignValues } from '@/components/materials';
import {
  DEFAULT_NDS_VERSION,
  CACHE_DURATION_MS,
  ERROR_MESSAGES,
  HTTP_STATUS,
} from '@/lib/constants/nds-constants';

interface WoodData {
  speciesCombinations: string[];
  designValues: {
    [speciesCombination: string]: DesignValues[];
  };
  commercialGrades: { [speciesCombination: string]: string[] };
  availableVersions: string[]; // Add available versions
}

// --- In-memory cache for Wood Data ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}

const CACHE_DURATION_MS_WOOD_DATA = CACHE_DURATION_MS.WOOD_DATA;

let woodDataCache: Cache<WoodData> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Wood Data ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const selectedVersion = searchParams.get('version') || DEFAULT_NDS_VERSION;
  
  // --- Cache check for Wood Data ---
  const now = Date.now();
  if (woodDataCache.data && woodDataCache.expires && woodDataCache.expires > now) {
    console.log("Serving wood data from cache.");
    const filteredData = filterWoodDataByVersion(woodDataCache.data, selectedVersion);
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh wood data.");
  // --- End cache check for Wood Data ---

  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'DesignValues.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: ERROR_MESSAGES.WOOD_DATA_NOT_FOUND },
        { 
          status: HTTP_STATUS.NOT_FOUND,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    const woodData: WoodData = {
      speciesCombinations: [],
      designValues: {},
      commercialGrades: {},
      availableVersions: [],
    };

    const parseNumber = (value: string): number => {
      if (!value || value.trim() === '') return 0;
      if (value.toLowerCase() === 'infinity') return Infinity;
      return parseFloat(value.replace(/,/g, '')) || 0;
    };

    return new Promise<NextResponse>((resolve, reject) => {
      const rows: any[] = [];

      stream
        .pipe(csv())
        .on('data', (row) => rows.push(row))
        .on('end', () => {
          try {
            const versionsMap = new Map<string, boolean>();
            
            rows.forEach((row) => {
              const speciesCombination = row.species_combination?.trim().toUpperCase();
              const commercialGrade = row.commercial_grade?.trim();
              const version = row.version?.trim();
              
              if (!speciesCombination) return;
              
              // Collect all available versions
              if (version) {
                versionsMap.set(version, true);
              }

              if (!woodData.speciesCombinations.includes(speciesCombination)) {
                woodData.speciesCombinations.push(speciesCombination);
                woodData.designValues[speciesCombination] = [];
                woodData.commercialGrades[speciesCombination] = [];
              }

              if (commercialGrade && !woodData.commercialGrades[speciesCombination].includes(commercialGrade)) {
                woodData.commercialGrades[speciesCombination].push(commercialGrade);
              }

              const designValue: DesignValues = {
                minThickness: parseNumber(row.min_thick) ? parseNumber(row.min_thick) : -Infinity,
                maxThickness: parseNumber(row.max_thick) ? parseNumber(row.max_thick) : Infinity,
                minWidth: parseNumber(row.min_width) ? parseNumber(row.min_width) : -Infinity,
                maxWidth: parseNumber(row.max_width) ? parseNumber(row.max_width) : Infinity,
                Ft: parseNumber(row.tension_parallel_to_grain_Ft),
                Fb: parseNumber(row.bending_Fb),
                Fv: parseNumber(row.shear_parallel_to_grain_Fv),
                Fc_perp: parseNumber(row.compression_perpendicular_to_grain_Fc_),
                Fc: parseNumber(row.compression_parallel_to_grain_Fc),
                E: parseNumber(row.modulus_of_elasticity_E),
                Emin: parseNumber(row.Emin),
                G: parseNumber(row.specific_gravity_G),
                commercial_grade: commercialGrade || '',
                speciesCombination,
                grading_rules_agency: row.grading_rules_agency?.trim() || '',
                design_values_table: row.table?.trim() || '',
                location: row.location?.trim() || '',
                version: row.version?.trim() || '',
                service_condition: row.service_condition?.trim() || '',
                bending_Fb_Cr_Repetitive_Member: parseNumber(row.bending_Fb_Cr_Repetitive_Member),
                repetitive_member_factor_Cr: parseNumber(row.repetitive_member_factor_Cr),
                wet_service_factor_Cm_for_Fb: parseNumber(row.wet_service_factor_Cm_for_Fb),
                wet_service_factor_Cm_for_Ft: parseNumber(row.wet_service_factor_Cm_for_Ft),
                wet_service_factor_Cm_for_Fv: parseNumber(row.wet_service_factor_Cm_for_Fv),
                wet_service_factor_Cm_for_Fc_perp: parseNumber(row.wet_service_factor_Cm_for_Fc_perp),
                wet_service_factor_Cm_for_Fc: parseNumber(row.wet_service_factor_Cm_for_Fc),
                wet_service_factor_Cm_for_E_and_Emin: parseNumber(row.wet_service_factor_Cm_for_E_and_Emin),
                size_factor_Cf_for_Fb: parseNumber(row.size_factor_Cf_for_Fb),
                size_classification: row.size_classification?.trim() || '',
              };

              woodData.designValues[speciesCombination].push(designValue);
            });

            // Convert versions map to sorted array
            const versionsArray: string[] = [];
            versionsMap.forEach((_, version) => versionsArray.push(version));
            woodData.availableVersions = versionsArray.sort();

            woodData.speciesCombinations.sort();
            Object.keys(woodData.commercialGrades).forEach(speciesCombination => {
              woodData.commercialGrades[speciesCombination].sort();
            });

            // --- Cache update for Wood Data ---
            woodDataCache.data = woodData;
            woodDataCache.expires = Date.now() + CACHE_DURATION_MS_WOOD_DATA;
            console.log("Wood data cached.");
            // --- End cache update for Wood Data ---

            // Filter data by selected version before returning
            const filteredData = filterWoodDataByVersion(woodData, selectedVersion);

            resolve(NextResponse.json(filteredData, {
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, max-age=3600',
              },
            }));
          } catch (error) {
            reject(NextResponse.json(
              { error: 'Failed to process wood data', details: error instanceof Error ? error.message : 'Unknown error' },
              { 
                status: 500,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            ));
          }
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse wood data', details: error.message },
            { 
              status: 500,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load wood data', details: error instanceof Error ? error.message : 'Unknown error' },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}

// Helper function to filter wood data by version
function filterWoodDataByVersion(woodData: WoodData, selectedVersion: string): WoodData {
  const filteredData: WoodData = {
    speciesCombinations: [],
    designValues: {},
    commercialGrades: {},
    availableVersions: woodData.availableVersions, // Keep all available versions
  };

  woodData.speciesCombinations.forEach(speciesCombination => {
    const filteredDesignValues = woodData.designValues[speciesCombination]?.filter(
      dv => dv.version === selectedVersion
    ) || [];
    
    if (filteredDesignValues.length > 0) {
      filteredData.speciesCombinations.push(speciesCombination);
      filteredData.designValues[speciesCombination] = filteredDesignValues;
      
      // Get unique commercial grades for this version
      const gradesMap = new Map<string, boolean>();
      filteredDesignValues.forEach(dv => {
        if (dv.commercial_grade) {
          gradesMap.set(dv.commercial_grade, true);
        }
      });
      const uniqueGrades: string[] = [];
      gradesMap.forEach((_, grade) => uniqueGrades.push(grade));
      uniqueGrades.sort();
      filteredData.commercialGrades[speciesCombination] = uniqueGrades;
    }
  });

  return filteredData;
}