import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { Readable } from 'stream';

export async function GET(): Promise<NextResponse> {
  try {
    const csvPath = path.join(process.cwd(), 'lib', 'tables', 'woods', 'DesignValues.csv');
    
    try {
      await fs.access(csvPath);
    } catch (error) {
      return NextResponse.json(
        { error: 'Design values data file not found' },
        { status: 404 }
      );
    }

    const fileContent = await fs.readFile(csvPath, 'utf-8');
    const stream = Readable.from(fileContent);

    const versions = new Set<string>();

    return new Promise<NextResponse>((resolve, reject) => {
      stream
        .pipe(csv())
        .on('data', (row) => {
          const version = row.version?.trim();
          if (version) {
            versions.add(version);
          }
        })
        .on('end', () => {
          const sortedVersions = Array.from(versions).sort();
          resolve(NextResponse.json(sortedVersions, {
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=3600',
            },
          }));
        })
        .on('error', (error) => {
          reject(NextResponse.json(
            { error: 'Failed to parse wood data versions', details: error.message },
            { status: 500 }
          ));
        });
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load wood data versions', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 