import { NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";
import csv from "csv-parser";
import { Readable } from "stream";
import {
  DEFAULT_NDS_VERSION,
  ERROR_MESSAGES,
  HTTP_STATUS,
  CACHE_DURATION_MS,
} from '@/lib/constants/nds-constants';

interface Table5DRow {
  combination_symbol: string;
  species_group: string;
  grade: string; // Or 'grade' if normalized
  E_ksi: number;
  E_min_ksi: number;
  F_c_perp_psi: number;
  Ft_psi_2_or_more_lamniations: number;
  Fc_psi_4_or_more_laminations: number;
  Fc_psi_2_or_3_laminations: number;
  F_by_psi_4_or_more_laminations: number;
  F_by_psi_3_laminations: number;
  F_by_psi_2_laminations: number;
  F_vy_psi_4_or_more_laminations: number;
  Fbx_psi_2_laminations_to_15_in_deep: number;
  F_vx_psi: number;
  G: number; // Specific Gravity
  table: string; // Added by user
  version: string; // Added by user
  // No table, version, or wet_service_factor columns observed in Table5D.csv header
  Notes?: string; // Adding as a potential optional field, though not in headers
}

// --- In-memory cache for Table5D ---
interface Cache<T> {
  data: T | null;
  expires: number | null; // Timestamp when the cache expires
}



let table5dCache: Cache<Table5DRow[]> = {
  data: null,
  expires: null,
};
// --- End in-memory cache for Table5D ---

export async function GET(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const version = searchParams.get('version') || DEFAULT_NDS_VERSION;
  // --- Cache check for Table5D ---
  const now = Date.now();
  if (table5dCache.data && table5dCache.expires && table5dCache.expires > now) {
    console.log("Serving Table5D data from cache.");
    // Filter by version before returning cached data
    const filteredData = table5dCache.data.filter(row => row.version === version);
    return NextResponse.json(filteredData, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }
  console.log("Fetching fresh Table5D data.");
  // --- End cache check for Table5D ---

  try {
    const csvPath = path.join(
      process.cwd(),
      "lib",
      "tables",
      "woods",
      "glulam",
      "Table5D.csv"
    );

    try {
      await fs.access(csvPath);
    } catch (error) {
      console.error("File access error for Table5D.csv:", error);
      return NextResponse.json(
        { error: "Table5D.csv data file not found" },
        {
          status: HTTP_STATUS.NOT_FOUND,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const fileContent = await fs.readFile(csvPath, "utf-8");
    const stream = Readable.from(fileContent);
    const results: Table5DRow[] = [];

    const numericKeys: Array<keyof Table5DRow> = [
      "E_ksi",
      "E_min_ksi",
      "F_c_perp_psi",
      "Ft_psi_2_or_more_lamniations",
      "Fc_psi_4_or_more_laminations",
      "Fc_psi_2_or_3_laminations",
      "F_by_psi_4_or_more_laminations",
      "F_by_psi_3_laminations",
      "F_by_psi_2_laminations",
      "F_vy_psi_4_or_more_laminations",
      "Fbx_psi_2_laminations_to_15_in_deep",
      "F_vx_psi",
      "G",
    ];
    const optionalStringKeys: Array<keyof Table5DRow> = ["Notes"];
    // 'table' and 'version' will be handled as default string keys by the parsing logic

    return new Promise<NextResponse>((resolve, reject) => {
      stream
        .pipe(
          csv({
            mapHeaders: ({ header }) => {
              // Generic normalization: trim, replace spaces with underscores.
              // Column names in Table5D.csv seem to be well-formed for this.
              let normalizedHeader = header.trim().replace(/\s+/g, "_");
              // For Table5D, ensure exact match by also converting to lower case for interface keys if they are.
              // However, current interface uses casing from CSV, so this might not be needed if consistency is maintained.
              // Example: normalizedHeader = normalizedHeader.toLowerCase();
              return normalizedHeader;
            },
          })
        )
        .on("data", (dataFromCsv) => {
          const processedRow = {} as Table5DRow;
          for (const keyFromCsv in dataFromCsv) {
            const key = keyFromCsv as keyof Table5DRow;
            const rawValue = dataFromCsv[keyFromCsv];
            const valueString = String(rawValue || "").trim();

            if (numericKeys.includes(key)) {
              if (
                valueString === "" ||
                valueString.toLowerCase() === "na" ||
                valueString.toLowerCase() === "n/a"
              ) {
                (processedRow as any)[key] = NaN;
              } else {
                (processedRow as any)[key] = parseFloat(
                  valueString.replace(",", "")
                );
              }
            } else if (optionalStringKeys.includes(key)) {
              if (
                valueString === "" ||
                valueString.toLowerCase() === "na" ||
                valueString.toLowerCase() === "n/a"
              ) {
                (processedRow as any)[key] = undefined;
              } else {
                (processedRow as any)[key] = valueString;
              }
            } else { // Required string keys (including 'table' and 'version')
              (processedRow as any)[key] = valueString;
            }
          }
          results.push(processedRow);
        })
        .on("end", () => {
          // --- Cache update for Table5D ---
          table5dCache.data = results;
          table5dCache.expires = Date.now() + CACHE_DURATION_MS.WOOD_DATA;
          console.log("Table5D data cached.");
          // --- End cache update for Table5D ---
          
          // Filter by version before returning
          const filteredResults = results.filter(row => row.version === version);
          resolve(
            NextResponse.json(filteredResults, {
              headers: { 
                "Content-Type": "application/json",
                'Cache-Control': 'public, max-age=3600',
              },
            })
          );
        })
        .on("error", (error) => {
          console.error("CSV parsing error for Table5D.csv:", error);
          reject(
            NextResponse.json(
              {
                error: "Failed to parse Table5D.csv data",
                details: error.message,
              },
              {
                status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
                headers: { "Content-Type": "application/json" },
              }
            )
          );
        });
    });
  } catch (error) {
    console.error("Overall GET request error for Table5D:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown server error";
    return NextResponse.json(
      { error: "Failed to load Table5D.csv data", details: errorMessage },
      {
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
