"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, FileText, Eye, Trash2, CheckCircle, XCircle, TrendingUp, TrendingDown } from "lucide-react";
import Link from "next/link";
import { BeamVisualization } from '@/components/beam-analysis/beam-visualization';
import type { BeamData, BeamPropertiesState, Support, LoadGroup } from '@/lib/types/beam/beam-data';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { useToast } from "@/components/ui/use-toast";
import type { SummaryData, StressRatioResult, DeflectionResult } from '@/lib/types/analysis/analysis-output';

// Define a type for the saved analysis item we expect from the API
interface SavedAnalysisItem {
  id: string;
  name: string;
  length: number;
  modulusOfElasticity: number;
  momentOfInertia: number;
  beamProperties: string; // JSON string of BeamPropertiesState
  loads: string;          // JSON string of LoadGroup[]
  supports: string;       // JSON string of Support[]
  results: string | null; // JSON string of SummaryData, now allowing null
  createdAt: string;
}

// Type for the data prepared for display
interface PreparedDisplayData {
  beamData: BeamData;
  beamPropertiesState: BeamPropertiesState;
  summaryData: SummaryData | null;
}

// Helper function to format numbers, can be expanded
const formatNum = (num: number | undefined | null, digits = 2) => {
  if (num === undefined || num === null || isNaN(num) || !isFinite(num)) return "N/A";
  return num.toFixed(digits);
};

const formatDeflectionRatio = (ratio: number): string => {
  if (!ratio || !isFinite(ratio) || Math.abs(ratio) < 1e-9) {
    return "N/A";
  }
  const denominator = 1 / Math.abs(ratio);
  if (!isFinite(denominator)) return "N/A";
  return `L/${denominator.toFixed(0)}`;
};

export default function Home() {
  const router = useRouter();
  const { toast } = useToast();
  const [savedAnalyses, setSavedAnalyses] = useState<SavedAnalysisItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSavedAnalyses = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/beam-analysis/results/list');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch saved analyses');
        }
        const data: SavedAnalysisItem[] = await response.json();
        setSavedAnalyses(data);
      } catch (err: any) {
        setError(err.message);
        console.error("Error fetching saved analyses:", err);
      }
      setIsLoading(false);
    };

    fetchSavedAnalyses();
  }, []);

  const handleOpenAnalysis = (id: string) => {
    router.push(`/beam-analysis?loadId=${id}`);
  };

  const handleDeleteAnalysis = async (id: string, name: string) => {
    try {
      const response = await fetch(`/api/beam-analysis/results/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete analysis');
      }

      setSavedAnalyses(prevAnalyses => prevAnalyses.filter(analysis => analysis.id !== id));
      toast({
        title: "Analysis Deleted",
        description: `"${name}" was successfully deleted.`,
      });
    } catch (err: any) {
      console.error("Error deleting analysis:", err);
      toast({
        title: "Delete Failed",
        description: err.message || "An unexpected error occurred while deleting.",
        variant: "destructive",
      });
    }
  };

  // Helper to parse and prepare data for visualization and summary
  const prepareDisplayData = (item: SavedAnalysisItem): PreparedDisplayData | null => {
    try {
      const beamPropertiesState: BeamPropertiesState = JSON.parse(item.beamProperties);
      const loads: LoadGroup[] = JSON.parse(item.loads);
      const supports: Support[] = JSON.parse(item.supports);
      const summaryData: SummaryData | null = item.results ? JSON.parse(item.results) : null;

      // Reconstruct beamData.properties
      let area = 0;
      if (beamPropertiesState.manual_Area) {
        area = beamPropertiesState.manual_Area;
      } else if (beamPropertiesState.lumberType === 'sawn' && beamPropertiesState.lumberProperties?.A) {
        area = beamPropertiesState.lumberProperties.A;
      } else if (beamPropertiesState.lumberType === 'glulam' && beamPropertiesState.selectedGluLamProperties?.sectionProperties?.area) {
        area = beamPropertiesState.selectedGluLamProperties.sectionProperties.area;
      }

      const beamData: BeamData = {
        properties: {
          length: item.length,
          elasticModulus: item.modulusOfElasticity,
          momentOfInertia: item.momentOfInertia,
          area: area, 
        },
        loadGroups: loads,
        supports: supports,
      };
      return { beamData, beamPropertiesState, summaryData };
    } catch (parseError) {
      console.error("Error parsing saved analysis data for item:", item.name, parseError);
      return null;
    }
  };

  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-12 text-center">
          <h1 className="text-5xl font-bold text-primary mb-4">Structural Engineering Hub</h1>
          <p className="text-muted-foreground text-xl">Analyze, Save, and Review Your Structural Calculations</p>
        </header>

        <div className="mb-12">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl flex items-center"><FileText className="mr-2 text-primary" /> New Analysis</CardTitle>
              <CardDescription>Start a new beam analysis project.</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/beam-analysis">
                <Button className="w-full text-lg py-6">
                  Open Beam Analysis Tool
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        <div>
          <h2 className="text-3xl font-semibold text-primary mb-6"><Eye className="mr-2 inline-block" />Calculations</h2>
          {isLoading && <p className="text-center text-muted-foreground">Loading calculations...</p>}
          {error && <p className="text-center text-destructive">Error: {error}</p>}
          {!isLoading && !error && savedAnalyses.length === 0 && (
            <p className="text-center text-muted-foreground">No calculations found. Perform and save an analysis to see it here.</p>
          )}
          {!isLoading && !error && savedAnalyses.length > 0 && (
            <div className="grid grid-cols-1 gap-8">
              {savedAnalyses.map((item) => {
                const displayData = prepareDisplayData(item);
                let materialInfo = "Details not available";
                if (displayData?.beamPropertiesState) {
                  const bps = displayData.beamPropertiesState;
                  if (bps.lumberType === 'sawn') {
                    materialInfo = `Sawn: ${bps.selectedSpecies || 'N/A'} - ${bps.selectedGrade || 'N/A'}`;
                    if (bps.selectedSpecies === 'MANUAL' && bps.manual_E) {
                        materialInfo = `Sawn (Manual): E=${formatNum(bps.manual_E, 0)} psi`;
                    }
                  } else if (bps.lumberType === 'glulam') {
                    materialInfo = `Glulam: ${bps.selectedGluLamProperties?.species || 'N/A'} - ${bps.selectedGluLamProperties?.grade || 'N/A'}`;
                  } else if (bps.manual_E) { 
                    materialInfo = `Manual Properties: E=${formatNum(bps.manual_E, 0)} psi`;
                  }
                }

                const effectiveStressRatioLimit = displayData?.beamPropertiesState?.manual_maxStressRatioLimit ?? 1.0;

                return (
                  <Card key={item.id} className="w-full flex flex-col md:flex-row overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300">
                    {/* Left Side: Visualization & Basic Info */}
                    <div className="md:w-1/3 p-4 border-r border-border flex flex-col items-center justify-center bg-muted/40">
                      <div className="w-full h-48 mb-4 rounded-md overflow-hidden flex items-center justify-center">
                        {displayData?.beamData ? (
                          <BeamVisualization 
                            beamData={displayData.beamData} 
                            beamPropertiesState={displayData.beamPropertiesState} 
                            unitSystem={UnitSystem.IMPERIAL} // Assuming Imperial for display consistency
                          />
                        ) : (
                          <p className="text-sm text-muted-foreground p-4 text-center">Could not load visualization data.</p>
                        )}
                      </div>
                      <div className="text-sm text-center w-full">
                        <h3 className="text-lg font-semibold truncate mb-1">{item.name}</h3>
                        <p className="text-xs text-muted-foreground mb-2">Saved: {new Date(item.createdAt).toLocaleDateString()}</p>
                        <p><strong>Length:</strong> {formatNum(item.length)} ft</p> 
                        <p className="truncate"><strong>Material:</strong> {materialInfo}</p>
                      </div>
                    </div>

                    {/* Right Side: Summary Data */}
                    <div className="md:w-2/3 p-4 flex-1 flex flex-col">
                      {displayData?.summaryData ? (
                        <>
                          <h4 className="text-md font-semibold mb-3 border-b pb-2">Calculation Summary</h4>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm flex-grow">
                            <div>
                              <p className="font-medium text-muted-foreground">Bending Stress Ratio:</p>
                              <p className={`font-bold ${displayData.summaryData.maxBendingStressRatio.ratio > effectiveStressRatioLimit ? 'text-red-500' : 'text-green-600'}`}>
                                {formatNum(displayData.summaryData.maxBendingStressRatio.ratio, 3)}
                                {displayData.summaryData.maxBendingStressRatio.ratio > effectiveStressRatioLimit ? <XCircle className="inline ml-1 h-4 w-4"/> : <CheckCircle className="inline ml-1 h-4 w-4"/>}
                              </p>
                              <p className="text-xs text-muted-foreground">Actual: {formatNum(displayData.summaryData.maxBendingStressRatio.actualStress)} psi</p>
                              <p className="text-xs text-muted-foreground">Allowable: {formatNum(displayData.summaryData.maxBendingStressRatio.allowableStress)} psi</p>
                              <p className="text-xs text-muted-foreground truncate">Load Combo: {displayData.summaryData.maxBendingStressRatio.loadComboName}</p>
                            </div>
                            <div>
                              <p className="font-medium text-muted-foreground">Shear Stress Ratio:</p>
                              <p className={`font-bold ${displayData.summaryData.maxShearStressRatio.ratio > effectiveStressRatioLimit ? 'text-red-500' : 'text-green-600'}`}>
                                {formatNum(displayData.summaryData.maxShearStressRatio.ratio, 3)}
                                {displayData.summaryData.maxShearStressRatio.ratio > effectiveStressRatioLimit ? <XCircle className="inline ml-1 h-4 w-4"/> : <CheckCircle className="inline ml-1 h-4 w-4"/>}
                              </p>
                              <p className="text-xs text-muted-foreground">Actual: {formatNum(displayData.summaryData.maxShearStressRatio.actualStress)} psi</p>
                              <p className="text-xs text-muted-foreground">Allowable: {formatNum(displayData.summaryData.maxShearStressRatio.allowableStress)} psi</p>
                              <p className="text-xs text-muted-foreground truncate">Load Combo: {displayData.summaryData.maxShearStressRatio.loadComboName}</p>
                            </div>
                            <div>
                              <p className="font-medium text-muted-foreground flex items-center">Max Downward <TrendingDown className="ml-1 h-4 w-4 text-blue-500"/></p>
                              <p className="font-bold">{formatNum(Math.abs(displayData.summaryData.maxTotalDeflectionDownward.value), 3)} in</p>
                              <p className="text-xs text-muted-foreground">Ratio: {formatDeflectionRatio(displayData.summaryData.maxTotalDeflectionDownward.ratio)} (Limit L/{displayData.summaryData.maxTotalDeflectionDownward.limit || 'N/A'})</p>
                              <p className="text-xs text-muted-foreground truncate">Load Combo: {displayData.summaryData.maxTotalDeflectionDownward.loadComboName}</p>
                            </div>
                            <div>
                              <p className="font-medium text-muted-foreground flex items-center">Max Upward <TrendingUp className="ml-1 h-4 w-4 text-purple-500"/></p>
                              <p className="font-bold">{formatNum(displayData.summaryData.maxTotalDeflectionUpward.value, 3)} in</p>
                              <p className="text-xs text-muted-foreground">Ratio: {formatDeflectionRatio(displayData.summaryData.maxTotalDeflectionUpward.ratio)} (Limit L/{displayData.summaryData.maxTotalDeflectionUpward.limit || 'N/A'})</p>
                              <p className="text-xs text-muted-foreground truncate">Load Combo: {displayData.summaryData.maxTotalDeflectionUpward.loadComboName}</p>
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="flex-1 flex items-center justify-center">
                          <p className="text-sm text-muted-foreground p-4 text-center">No summary data available for this saved analysis.</p>
                        </div>
                      )}
                      <div className="mt-auto pt-4 border-t flex space-x-2">
                        <Button 
                          onClick={() => handleOpenAnalysis(item.id)} 
                          className="flex-1"
                          variant="outline"
                        >
                          Load Analysis
                        </Button>
                        <Button 
                          onClick={() => handleDeleteAnalysis(item.id, item.name)} 
                          className="flex-1"
                          variant="destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </Button>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </main>
  );
}