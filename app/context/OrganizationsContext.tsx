'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';

// Define interfaces (can be moved to a shared types file later)
export interface Project {
  id: string;
  title: string;
  description?: string;
  status?: string;
  dueDate?: string;
  createdAt?: string;
  updatedAt?: string;
  organizationId?: string | null; // Added to match Prisma schema
}

export interface Organization {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  projects: Project[];
  createdAt?: string; // Added for consistency
  updatedAt?: string; // Added for consistency
}

interface OrganizationsContextType {
  organizations: Organization[];
  isLoading: boolean; // To indicate loading state from API
  isMutating: boolean; // To indicate loading state for create/update/delete operations
  error: string | null; // To store API errors
  addOrganization: (orgData: Omit<Organization, 'id' | 'projects' | 'createdAt' | 'updatedAt'>) => Promise<Organization | undefined>;
  getOrganizationById: (orgId: string) => Organization | undefined; // Will primarily use local cache after initial load
  addProjectToOrganization: (orgId: string, projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'organizationId'> & { title: string }) => Promise<Project | undefined>; // To be updated for API
  deleteProjectsFromOrganization: (orgId: string, projectIds: string[]) => Promise<void>; // To be updated for API
  fetchOrganizations: () => Promise<void>; // Expose refetch capability
  updateOrganization: (orgId: string, orgData: Partial<Omit<Organization, 'id' | 'projects' | 'createdAt' | 'updatedAt'>>) => Promise<Organization | undefined>;
}

const OrganizationsContext = createContext<OrganizationsContextType | undefined>(undefined);

export const OrganizationsProvider = ({ children }: { children: ReactNode }) => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrganizations = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/organizations');
      if (!response.ok) {
        throw new Error('Failed to fetch organizations');
      }
      const data = await response.json();
      setOrganizations(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error fetching organizations');
      console.error("Fetch organizations error:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  const addOrganization = async (orgData: Omit<Organization, 'id' | 'projects' | 'createdAt' | 'updatedAt'>): Promise<Organization | undefined> => {
    setError(null);
    setIsMutating(true);
    try {
      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orgData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create organization');
      }
      const newOrganization = await response.json();
      setOrganizations(prevOrgs => [...prevOrgs, newOrganization]);
      return newOrganization;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error creating organization';
      setError(errorMessage);
      console.error("Add organization error:", err);
      return undefined;
    } finally {
      setIsMutating(false);
    }
  };

  const getOrganizationById = useCallback((orgId: string): Organization | undefined => {
    return organizations.find(org => org.id === orgId);
  }, [organizations]);

  const addProjectToOrganization = async (
    orgId: string, 
    projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'organizationId'> & { title: string }
  ): Promise<Project | undefined> => {
    setError(null);
    setIsMutating(true);
    try {
      const response = await fetch(`/api/organizations/${orgId}/projects`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }
      const newProject = await response.json();
      setOrganizations(prevOrgs =>
        prevOrgs.map(org => {
          if (org.id === orgId) {
            // Ensure projects array exists
            const updatedProjects = org.projects ? [...org.projects, newProject] : [newProject];
            return { ...org, projects: updatedProjects };
          }
          return org;
        })
      );
      return newProject;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error creating project';
      setError(errorMessage);
      console.error("Add project error:", err);
      return undefined;
    } finally {
      setIsMutating(false);
    }
  };

  const deleteProjectsFromOrganization = async (orgId: string, projectIds: string[]): Promise<void> => {
    setError(null);
    setIsMutating(true);
    try {
      const response = await fetch(`/api/organizations/${orgId}/projects`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectIds }), // Send projectIds in the body
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete projects');
      }

      // Update local state upon successful deletion
      setOrganizations(prevOrgs =>
        prevOrgs.map(org => {
          if (org.id === orgId) {
            const projectsToDeleteSet = new Set(projectIds);
            const updatedProjects = org.projects.filter(proj => !projectsToDeleteSet.has(proj.id));
            return { ...org, projects: updatedProjects };
          }
          return org;
        })
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error deleting projects';
      setError(errorMessage);
      console.error("Delete projects error:", err);
      // Potentially re-throw or handle as needed, e.g., notify user
    } finally {
      setIsMutating(false);
    }
  };

  const updateOrganization = async (
    orgId: string, 
    orgData: Partial<Omit<Organization, 'id' | 'projects' | 'createdAt' | 'updatedAt'>>
  ): Promise<Organization | undefined> => {
    setError(null);
    setIsMutating(true);
    try {
      const response = await fetch(`/api/organizations/${orgId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orgData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update organization');
      }
      const updatedOrganization = await response.json();
      setOrganizations(prevOrgs =>
        prevOrgs.map(org => (org.id === orgId ? { ...org, ...updatedOrganization } : org))
      );
      return updatedOrganization;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error updating organization';
      setError(errorMessage);
      console.error("Update organization error:", err);
      return undefined;
    } finally {
      setIsMutating(false);
    }
  };

  return (
    <OrganizationsContext.Provider value={{
      organizations,
      isLoading,
      isMutating,
      error,
      addOrganization,
      getOrganizationById,
      addProjectToOrganization,
      deleteProjectsFromOrganization,
      fetchOrganizations,
      updateOrganization
    }}>
      {children}
    </OrganizationsContext.Provider>
  );
};

export const useOrganizations = () => {
  const context = useContext(OrganizationsContext);
  if (context === undefined) {
    throw new Error('useOrganizations must be used within an OrganizationsProvider');
  }
  return context;
}; 