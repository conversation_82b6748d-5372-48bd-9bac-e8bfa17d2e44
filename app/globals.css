@tailwind base;
@tailwind components;
@tailwind utilities;

/* Light Theme - Purple Tones */
@layer base {
  :root {
    --background: 255 65% 96%; /* Very light lavender */
    --foreground: 260 40% 25%; /* Dark grayish purple text */
    --card: 255 60% 93%; /* Light lavender card */
    --card-foreground: 260 40% 25%;
    --popover: 255 60% 92%; /* Slightly darker light lavender */
    --popover-foreground: 260 40% 25%;
    --primary: 275 65% 55%; /* Medium vibrant purple */
    --primary-foreground: 275 60% 97%; /* Very light text for primary */
    --secondary: 260 55% 90%; /* Lighter lavender */
    --secondary-foreground: 260 40% 30%;
    --muted: 260 50% 88%; /* Muted lavender */
    --muted-foreground: 260 30% 50%; /* Muted purple text */
    --accent: 275 60% 60%; /* Accent purple */
    --accent-foreground: 275 60% 97%;
    --destructive: 0 70% 55%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 98%;
    --border: 260 40% 80%; /* Softer purple border */
    --input: 260 40% 85%; /* Input field background */
    --ring: 275 65% 60%; /* Ring color - primary accent */
    --chart-1: 275 70% 65%;
    --chart-2: 260 60% 70%;
    --chart-3: 290 65% 60%;
    --chart-4: 250 55% 75%;
    --chart-5: 300 70% 55%;
    --radius: 0.5rem;
  }

  /* Dark Theme - Screenshot Inspired */
  .dark {
    --background: 250 35% 22%; /* Hex: #2c2449 */
    --foreground: 0 0% 100%;   /* Hex: #FFFFFF */
    --card: 248 29% 30%;       /* Table/Card Bg: #3b3364 */
    --card-foreground: 0 0% 100%; /* White text on cards/table cells */
    --popover: 250 30% 25%;    /* Similar to card, slightly darker */
    --popover-foreground: 0 0% 100%; /* White text on popovers */
    --primary: 21 79% 62%;     /* Hex: #e98155 (Orange Accent) */
    --primary-foreground: 250 35% 10%; /* Dark text for good contrast on orange */
    --secondary: 250 35% 18%;   /* Darker than main background for sidebar */
    --secondary-foreground: 0 0% 100%; /* White text on secondary backgrounds */
    --muted: 248 25% 43%;      /* Table Header Bg: #5c5289 */
    --muted-foreground: 0 0% 100%; /* White text for muted/table headers */
    --accent: 21 79% 62%;      /* Same as primary */
    --accent-foreground: 250 35% 10%; /* Same as primary-foreground */
    --destructive: 0 70% 50%;   /* A clearer red for destructive actions */
    --destructive-foreground: 0 0% 100%; /* White text on destructive */
    --border: 250 30% 35%;     /* Subtle border, lighter than card */
    --input: 250 40% 16%;      /* Hex: #1e183a for input background */
    --ring: 21 79% 65%;        /* Ring based on new primary/accent */
    --chart-1: 21 79% 62%;    /* Chart colors to match accent */
    --chart-2: 21 79% 55%;
    --chart-3: 21 79% 70%;
    --chart-4: 250 30% 50%;   /* Contrasting purple for other chart colors */
    --chart-5: 250 35% 60%;
  }
}

/* Removed the old :root and @media (prefers-color-scheme: dark) blocks */
/* as they are now handled by the @layer base theme definitions. */

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
