"use client";

import React from "react";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-40 transform transition-transform ease-in-out duration-300 ${ 
        isOpen ? "translate-x-0" : "-translate-x-full"
      }`}
    >
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose} 
      ></div>

      {/* Sidebar Content */}
      <div className="relative w-80 h-full bg-[#2c234d] shadow-xl p-5 z-50 flex flex-col text-white">
        <button
          onClick={onClose} 
          className="absolute top-0 right-0 mt-4 mr-4 text-gray-400 hover:text-gray-200 lg:hidden"
          aria-label="Close sidebar"
        >
          {/* Placeholder for a close icon */}
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        
        {/* User Profile Section */}
        <div className="mb-10 flex flex-col items-center text-center">
          {/* Placeholder for Avatar */}
          <div className="w-20 h-20 bg-gray-500 rounded-full mb-3 flex items-center justify-center">
            <span className="text-xl font-semibold">M</span> {/* Placeholder Initials */}
          </div>
          <h2 className="text-lg font-semibold">Hello, Marquez</h2>
          <p className="text-sm text-gray-400"><EMAIL></p>
          <button className="mt-2 text-xs text-red-400 hover:text-red-300">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Logout (placeholder)
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-grow">
          <ul>
            <li className="mb-2">
              <button className="w-full text-left px-3 py-2 rounded-md hover:bg-[#352a5a] focus:outline-none focus:bg-[#352a5a] flex items-center">
                {/* Placeholder Icon */}
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                Dashboard
              </button>
            </li>
            <li className="mb-2 relative group">
              <button className="w-full text-left px-3 py-2 rounded-md bg-[#fd7e14] text-white focus:outline-none flex items-center justify-between">
                <div className="flex items-center">
                  {/* Placeholder Icon */}
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path></svg>
                  My Wallet
                </div>
                {/* Chevron Icon */}
                <svg className="w-4 h-4 transform transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7"></path></svg>
              </button>
              {/* Submenu for My Wallet - initially hidden, shown on hover/focus or if active */}
              <ul className="pl-8 mt-1 space-y-1">
                <li><button className="w-full text-left px-3 py-1.5 text-sm rounded-md text-gray-300 hover:bg-[#352a5a] hover:text-white focus:outline-none focus:bg-[#352a5a]">Add New</button></li>
                <li><button className="w-full text-left px-3 py-1.5 text-sm rounded-md text-gray-300 hover:bg-[#352a5a] hover:text-white focus:outline-none focus:bg-[#352a5a]">Card List</button></li>
                <li><button className="w-full text-left px-3 py-1.5 text-sm rounded-md text-gray-300 hover:bg-[#352a5a] hover:text-white focus:outline-none focus:bg-[#352a5a]">History</button></li>
              </ul>
            </li>
            <li className="mb-2">
              <button className="w-full text-left px-3 py-2 rounded-md hover:bg-[#352a5a] focus:outline-none focus:bg-[#352a5a] flex items-center justify-between">
                <div className="flex items-center">
                  {/* Placeholder Icon */}
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>
                  Transaction
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"></path></svg>
              </button>
            </li>
             <li className="mb-2">
              <button className="w-full text-left px-3 py-2 rounded-md hover:bg-[#352a5a] focus:outline-none focus:bg-[#352a5a] flex items-center justify-between">
                <div className="flex items-center">
                  {/* Placeholder Icon */}
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                  Crypto
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"></path></svg>
              </button>
            </li>
            <li className="mb-2">
              <button className="w-full text-left px-3 py-2 rounded-md hover:bg-[#352a5a] focus:outline-none focus:bg-[#352a5a] flex items-center justify-between">
                <div className="flex items-center">
                  {/* Placeholder Icon */}
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h.01M12 7h.01M16 7h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>
                  Exchange
                </div>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"></path></svg>
              </button>
            </li>
            <li className="mb-2">
              <button className="w-full text-left px-3 py-2 rounded-md hover:bg-[#352a5a] focus:outline-none focus:bg-[#352a5a] flex items-center justify-between">
                <div className="flex items-center">
                  {/* Placeholder Icon */}
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                  Settings
                </div>
                 <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"></path></svg>
              </button>
            </li>
          </ul>
        </nav>

        {/* Footer Section */}
        <div className="mt-auto pt-5 border-t border-gray-700">
          <p className="text-xs text-gray-400">Zenix Crypto Admin Dashboard</p>
          <p className="text-xs text-gray-500">
            © 2020 All Rights Reserved
          </p>
          <p className="text-xs text-gray-500">
            Made with <span className="text-red-500">❤</span> by Peterdraw
          </p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 