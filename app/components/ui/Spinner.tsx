'use client';

import React from 'react';
import { ClipLoader } from 'react-spinners';

interface SpinnerProps {
  loading: boolean;
  size?: number;
  color?: string;
  className?: string; // For additional styling/positioning
}

const Spinner: React.FC<SpinnerProps> = ({ 
  loading, 
  size = 35, 
  color = '#0070f3', // Default Next.js blue, or choose your app's primary color
  className 
}) => {
  if (!loading) return null;

  return (
    <div className={`flex justify-center items-center ${className || ''}`}>
      <ClipLoader
        color={color}
        loading={loading}
        size={size}
        aria-label="Loading Spinner"
        data-testid="loader"
      />
    </div>
  );
};

export default Spinner; 