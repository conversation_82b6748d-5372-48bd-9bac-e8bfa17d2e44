import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { Organization } from '../../context/OrganizationsContext'; // Corrected path

const styles = StyleSheet.create({
  page: {
    paddingTop: 15,
    paddingBottom: 35,
    paddingHorizontal: 35,
    fontFamily: 'Helvetica',
  },
  companyHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    alignItems: 'flex-start',
  },
  logo: {
    width: 100, 
    height: 40, 
    position: 'absolute',
    top: 15, 
    right: 35 
  },
  companyInfoAndLogo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  companyInfo: {
    flexDirection: 'column',
    maxWidth: '60%',
  },
  companyName: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 1,
  },
  companyAddressText: {
    fontSize: 8,
    color: '#333333',
    marginBottom: 0.5,
  },
  jobInfoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  jobInfoText: {
    fontSize: 8,
    fontWeight: 'bold',
    marginBottom: 1,
  },
  projectHeaderContainer: {
    textAlign: 'center',
    marginBottom: 8,
    paddingVertical: 2,
  },
  projectTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    marginBottom: 2,
  },
  projectSubtitle: {
    fontSize: 9,
    color: '#333333',
  },
  folderHeaderContainer: {
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 5,
    paddingVertical: 3,
    borderTopWidth: 0.5,
    borderTopColor: '#cccccc',
  },
  folderNameText: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#333333',
  },
  beamAnalysisContainer: {
    marginBottom: 15,
    borderTopWidth: 1.5,
    borderTopColor: '#666666',
    paddingTop: 10,
  },
  beamTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  beamName: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#222222',
    flex: 1,
  },
  beamDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingBottom: 3,
    borderBottomWidth: 1,
    borderBottomColor: '#cccccc',
  },
  materialInfoText: {
    fontSize: 10,
    fontStyle: 'italic',
    color: '#555555',
    flex: 1,
  },
  beamLengthInfo: {
    fontSize: 10,
    color: '#444444',
    textAlign: 'right',
  },
  contentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  primaryLeftColumn: {
    width: '50%',
    paddingRight: 5, 
  },
  middleColumn: {
    width: '16%',
    paddingHorizontal: 5,
  },
  primaryRightColumn: {
    width: '34%',
    paddingLeft: 5,
  },
  dataBlock: {
    marginBottom: 8,
  },
  dataBlockTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 3,
    color: '#333333',
    textTransform: 'uppercase',
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 1,
    alignItems: 'baseline',
  },
  dataLabel: {
    fontSize: 9,
    color: '#444444',
    marginRight: 5,
  },
  dataValue: {
    fontSize: 9,
    color: '#111111',
    fontWeight: 'bold',
    textAlign: 'right',
    marginRight: 2,
  },
  dataUnit: {
    fontSize: 9,
    color: '#555555',
    textAlign: 'left',
  },
  valueUnitContainer: {
    flexDirection: 'row',
    flexGrow: 1,
    justifyContent: 'flex-end',
    alignItems: 'baseline',
  },
  resultsValueBlock: {
    width: 'auto',
    alignSelf: 'flex-start',
    marginTop: 5,
  },
  loadsTable: {
    marginBottom: 8,
    borderWidth: 0.5,
    borderColor: '#999999',
  },
  loadsTableHeader: {
    flexDirection: 'row',
    backgroundColor: '#EAEAEA',
    borderBottomWidth: 0.5,
    borderColor: '#999999',
    paddingVertical: 2,
  },
  loadsTableCellHeader: {
    fontSize: 8.5,
    fontWeight: 'bold',
    textAlign: 'right',
    paddingHorizontal: 3,
    borderRightWidth: 0.5,
    borderColor: '#BFBFBF',
  },
  loadsTableCell: {
    fontSize: 8.5,
    textAlign: 'right',
    paddingVertical: 1,
    paddingHorizontal: 3,
    borderRightWidth: 0.5,
    borderColor: '#BFBFBF',
  },
  loadsTitleCell: {
    fontSize: 9,
    fontWeight: 'bold',
    textAlign: 'left',
    paddingLeft: 4,
    paddingVertical: 2,
    flexGrow: 1,
  },
  loadDerivationText: {
    fontSize: 8,
    fontStyle: 'italic',
    color: '#555555',
    marginTop: 1,
    marginBottom: 5, 
  },
  stressDeflectionRow: {
    flexDirection: 'row',
    marginBottom: 1,
    alignItems: 'flex-end',
  },
  stressDeflectionLabel: {
    fontSize: 9,
    color: '#444444',
    width: '8%',
  },
  stressDeflectionLabelDeflection: {
    fontSize: 9,
    color: '#444444',
    width: '18%', // Wider for dTL, dLL labels
  },
  stressDeflectionValue: {
    fontSize: 9,
    color: '#111111',
    fontWeight: 'bold',
    textAlign: 'left',
    width: '23%',
  },
  stressDeflectionUnit: {
    fontSize: 9,
    color: '#555555',
    textAlign: 'left',
    width: '9%',
    paddingLeft: 2,
  },
  stressDeflectionAllowableLabel: {
    fontSize: 9,
    color: '#444444',
    textAlign: 'left',
    width: '12%',
    paddingLeft: 4,
  },
  stressDeflectionAllowableValue: {
    fontSize: 9,
    color: '#111111',
    textAlign: 'left',
    width: '13%',
  },
  stressDeflectionAllowableUnit: {
    fontSize: 9,
    color: '#555555',
    textAlign: 'left',
    width: '9%',
    paddingLeft: 2,
  },
  stressDeflectionPercentage: {
    fontSize: 9,
    color: '#111111',
    textAlign: 'right',
    width: '20%',
  },
  fullWidthSeparator: {
    height: 1,
    backgroundColor: '#DDDDDD',
    marginVertical: 5,
  },
  spacer: { height: 10 },
  subscriptText: {
    fontSize: 7,
  },
  // Footer Styles
  footer: {
    position: 'absolute',
    bottom: 30, // Position from the bottom of the page
    left: 35,
    right: 35,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#aaaaaa',
    paddingTop: 5,
  },
  footerTextLeft: {
    fontSize: 8,
    color: '#555555',
    textAlign: 'left',
  },
  footerTextRight: {
    fontSize: 8,
    color: '#555555',
    textAlign: 'right',
  },
});

// Define a union type for styles that can be passed to LabelWithSubscript's baseStyle
type LabelBaseStyle = typeof styles.dataLabel | typeof styles.stressDeflectionLabel | typeof styles.stressDeflectionAllowableLabel;

// Interface for individual beam analysis result data passed to the PDF component
export interface PDFBeamAnalysisResult {
  id: string;
  analysisName: string;
  beamMaterialName?: string;
  sizeClassification?: string;
  L_ft?: number;
  Lu_ft?: number;

  w1plf_TL_plf?: number;
  w1plf_FLL_plf?: number;
  w1plf_R_plf?: number;
  w1plf_DL_plf?: number;
  loadDerivation?: string;

  reactions?: Array<{ value_lbs?: number; desc_lbs?: string }>;

  At_X_ft?: number;
  At_X_Vx_value?: number;
  At_X_Vx_lbs?: string;
  V_reduced_at_support_type?: string; 

  Vmax_lbs?: number;
  Vmax_position_ft?: number;
  M_in_kips?: number;
  M_position_ft?: number;

  try_b_in?: number;
  try_d_in?: number;
  try_A_in2?: number;
  try_S_in3?: number;
  try_I_in4?: number;

  case_value?: string;
  
  // Adjusted C_ Factors
  totalLoad_LDF?: number; // CD
  CL_value?: number;    // C_L
  Cr_value?: number;    // C_r
  adjCM_factor?: number; // C_M (Wet Service)
  adjCF_factor?: number; // C_F (Size)
  adjCfu_factor?: number;// C_fu (Flat Use)
  adjCi_factor?: number; // C_i (Incising)
  adjCt_factor?: number; // C_t (Temperature)
  adjCV_factor?: number; // C_V (Volume Factor - Glulam)

  // Elastic Properties (E', E'min, G)
  adjE_ksi?: number;
  adjEmin_ksi?: number;
  adjG_psi?: number; // This is Specific Gravity, unit handled in display

  // Allowable Stresses (to be displayed next to actuals)
  totalLoad_FvPrime_psi?: number;
  totalLoad_FbPrime_psi?: number;

  fv_psi?: number;
  fv_percentage?: string;
  fb_psi?: number;
  fb_percentage?: string;
  d_TL_in?: number;
  d_TL_L_ratio?: string;
  d_TL_loadCombo?: string; // Added field for total deflection load combo
  d_LL_in?: number;
  d_LL_L_ratio?: string;
  
  memberSize?: string;
  loads?: DetailedLoadFromRaw[]; // Changed from loadsToDisplay and made non-optional if always provided
  supports?: Array<{ type: string; position: number }>; // Added for supports data
  maxShearValue_loadComboName?: string;
  maxMomentValue_loadComboName?: string;
  folderId?: string | null; // Added for grouping in project-level PDF
}

// Added interface for detailed raw load data
interface DetailedLoadFromRaw {
  label: string;
  startPosition: number;
  endPosition: number;
  loadType?: string;
  type?: string; 
  startMagnitude?: number;
  endMagnitude?: number;
}

// Add Folder interface if not already present globally, or use a relevant imported one
interface Folder {
  id: string;
  name: string;
  // Add other folder properties if needed for sorting or display
}

interface ProjectData {
  title: string;
  clientName?: string | null;
  projectAddress?: string | null;
  clientAddress?: string | null;
  clientPhone?: string | null;
  jobNo: string;
  by: string;
  date: string;
  sheet: string;
  beamAnalysisResults: PDFBeamAnalysisResult[];
  organization?: Organization; 
  // folderName?: string | null; // Remove old folderName prop
  allProjectFolders?: Folder[];       // New: All folders in the project
  isProjectLevelPdf?: boolean;        // New: Flag for project vs. folder PDF
  currentFolderNameForSingleFolderPdf?: string | null; // New: Name of current folder if single folder PDF
}

interface ProjectCalculationsPDFProps {
  projectData: ProjectData;
}

// New component for labels with subscripts
const LabelWithSubscript = ({ 
  base, 
  sub, 
  prime, 
  postPrimeSub, 
  suffix = ":", 
  baseStyle = styles.dataLabel,
  subStyle = styles.subscriptText
}: {
  base: string;
  sub?: string;
  prime?: boolean;
  postPrimeSub?: string;
  suffix?: string;
  baseStyle?: LabelBaseStyle;
  subStyle?: typeof styles.subscriptText;
}) => {
  return (
    <Text style={baseStyle as any}>
      {base}
      {prime && <Text style={subStyle}>{"'"}</Text>}
      {sub && <Text style={subStyle}>{sub}</Text>}
      {postPrimeSub && <Text style={subStyle}>{postPrimeSub}</Text>}
      {suffix}
    </Text>
  );
};

// Updated component for units with Unicode superscripts
const UnitWithSuperscript = ({ base, exponent, style = styles.dataUnit }: {
  base: string;
  exponent: string;
  style?: typeof styles.dataUnit; 
}) => {
  const unicodeSuperscriptMap: { [key: string]: string } = {
    '0': '⁰',
    '1': '¹',
    '2': '²',
    '3': '³',
    '4': '⁴',
    '5': '⁵',
    '6': '⁶',
    '7': '⁷',
    '8': '⁸',
    '9': '⁹',
    // Add more if needed, e.g., for variables or other symbols
  };

  const unicodeExponent = unicodeSuperscriptMap[exponent] || exponent; // Fallback to original if not in map

  return (
    <Text style={style}>
      {base}{unicodeExponent}
    </Text>
  );
};

const DataDisplayRow = ({ label, value, unit }: { label: string | React.ReactNode; value?: string | number; unit?: string | React.ReactNode }) => (
  value !== undefined && value !== null && value !== '' ? (
    <View style={styles.dataRow}>
      {typeof label === 'string' ? <Text style={styles.dataLabel}>{label}:</Text> : label}
      <View style={styles.valueUnitContainer}>
        <Text style={styles.dataValue}>{String(value)}</Text>
        {typeof unit === 'string' ? <Text style={styles.dataUnit}>{unit || ''}</Text> : unit}
      </View>
    </View>
  ) : null
);

const StressDeflectionDisplayRow = ({
  label,
  actualValue,
  actualUnit,
  allowableValueLabel,
  allowableValue,
  allowableUnit,
  percentage,
  isDeflection = false,
  limitRatio
}: {
  label: string | React.ReactNode;
  actualValue?: string | number;
  actualUnit?: string;
  allowableValueLabel?: string | React.ReactNode;
  allowableValue?: string | number;
  allowableUnit?: string;
  percentage?: string;
  isDeflection?: boolean;
  limitRatio?: string;
}) => {
  if (actualValue === undefined || actualValue === null) return null;

  return (
    <View style={styles.stressDeflectionRow}>
      {typeof label === 'string' ? 
        <Text style={isDeflection ? styles.stressDeflectionLabelDeflection : styles.stressDeflectionLabel}>{label}:</Text> 
        : label}
      
      {isDeflection ? (
        <Text style={[styles.stressDeflectionValue, { width: '72%', textAlign: 'left', fontSize: 9, color: '#111111', fontWeight: 'normal' }]}>
          {String(actualValue)} {actualUnit || ''} {limitRatio || ''}
        </Text>
      ) : (
        <>
          <Text style={styles.stressDeflectionValue}>{String(actualValue)}</Text>
          <Text style={styles.stressDeflectionUnit}>{actualUnit || ''}</Text>
          {typeof allowableValueLabel === 'string' && allowableValue !== undefined ? 
            <Text style={styles.stressDeflectionAllowableLabel}>{allowableValueLabel}=</Text> : 
            allowableValueLabel
          }
          <Text style={styles.stressDeflectionAllowableValue}>{allowableValue !== undefined ? String(allowableValue) : ''}</Text>
          <Text style={styles.stressDeflectionAllowableUnit}>{allowableValue !== undefined ? (allowableUnit || 'psi') : ''}</Text>
        </>
      )}
      <Text style={styles.stressDeflectionPercentage}>{percentage || ''}</Text>
    </View>
  );
};

const BeamAnalysisSection = ({ analysis }: { analysis: PDFBeamAnalysisResult }) => {
  const format = (val: number | string | undefined, dpIfDecimal: number = 0): string => {
    if (val === undefined || val === null) return 'N/A';
    
    const numVal = Number(val); // Explicitly convert to number

    if (isNaN(numVal)) { // Handle cases where conversion results in NaN
      return 'N/A';
    }

    // Proceed with formatting if numVal is a valid number
    if (Number.isInteger(numVal) || numVal % 1 === 0) {
      return numVal.toFixed(0);
    }
    return numVal.toFixed(dpIfDecimal);
  };

  const formatLoadMagnitude = (load: DetailedLoadFromRaw): string => {
    const startMag = load.startMagnitude;
    const endMag = load.endMagnitude;
    if (load.type === 'distributed' && startMag !== undefined && endMag !== undefined) {
      return `(${format(startMag, 2)}, ${format(endMag, 2)})`;
    }
    if (startMag !== undefined && endMag !== undefined && startMag !== endMag) {
      return `${format(startMag, 2)} - ${format(endMag, 2)}`;
    } else if (startMag !== undefined) {
      return format(startMag, 2);
    } else if (endMag !== undefined) {
      return format(endMag, 2);
    }
    return '-';
  };

  // Define load type mapping for symbols and detection
  const loadTypeMapping = {
    'dead': { symbol: 'D', detect: ['dead', 'DEAD'] },
    'live': { symbol: 'L', detect: ['live', 'LIVE', 'floor_live', 'FLOOR_LIVE'] },
    'roof_live': { symbol: 'Lr', detect: ['roof_live', 'ROOF_LIVE'] },
    'snow': { symbol: 'S', detect: ['snow', 'SNOW'] },
    'wind': { symbol: 'W', detect: ['wind', 'WIND'] },
    'earthquake': { symbol: 'E', detect: ['earthquake', 'EARTHQUAKE'] },
    'rain': { symbol: 'R', detect: ['rain', 'RAIN'] },
    'soil': { symbol: 'H', detect: ['soil', 'SOIL'] },
    'flood': { symbol: 'F', detect: ['flood', 'FLOOD'] },
    'temperature': { symbol: 'T', detect: ['temperature', 'TEMPERATURE'] }
  };

  const loads = analysis.loads || [];

  // Group loads by their position ranges
  const loadGroups: { [key: string]: DetailedLoadFromRaw[] } = {};
  loads.forEach(load => {
    const positionKey = `${load.startPosition}-${load.endPosition}`;
    if (!loadGroups[positionKey]) {
      loadGroups[positionKey] = [];
    }
    loadGroups[positionKey].push(load);
  });

  // Convert to array and sort by start position
  const sortedLoadGroups = Object.entries(loadGroups)
    .map(([key, groupLoads]) => ({
      startPosition: groupLoads[0].startPosition,
      endPosition: groupLoads[0].endPosition,
      loads: groupLoads
    }))
    .sort((a, b) => a.startPosition - b.startPosition);

  // Determine which load types are present in the data
  const presentLoadTypes: string[] = [];
  Object.keys(loadTypeMapping).forEach(loadTypeKey => {
    const mapping = loadTypeMapping[loadTypeKey as keyof typeof loadTypeMapping];
    const hasThisLoadType = loads.some(load => 
      load.loadType && mapping.detect.includes(load.loadType.toUpperCase()) && formatLoadMagnitude(load) !== '-'
    );
    if (hasThisLoadType) {
      presentLoadTypes.push(loadTypeKey);
    }
  });

  const firstThreeColsTotalFlex = 30;
  const descFlex = firstThreeColsTotalFlex / 2;
  const atToFlex = firstThreeColsTotalFlex / 4;
  const remainingColsTotalFlex = 70;
  const numVisibleLoadCols = presentLoadTypes.length;
  const loadColFlex = numVisibleLoadCols > 0 ? remainingColsTotalFlex / numVisibleLoadCols : 0;

  const beamNameText = analysis.analysisName || 'Beam Analysis';
  let materialInfo = '';
  if (analysis.beamMaterialName && analysis.sizeClassification) {
    materialInfo = `${analysis.beamMaterialName} - ${analysis.sizeClassification}`;
  } else if (analysis.beamMaterialName) {
    materialInfo = analysis.beamMaterialName;
  } else if (analysis.sizeClassification) {
    materialInfo = analysis.sizeClassification;
  }

  // Debug logging for PDF component
  console.log(`[PDF Component Debug] For analysis ${analysis.id}, received d_TL_loadCombo:`, analysis.d_TL_loadCombo);
  console.log(`[PDF Component Debug] For analysis ${analysis.id}, received d_TL_L_ratio:`, analysis.d_TL_L_ratio);
  console.log(`[PDF Component Debug] For analysis ${analysis.id}, received d_TL_in:`, analysis.d_TL_in);

  // Check if there are any meaningful adjusted factors to display
  const hasAdjustedFactors = (
    (analysis.totalLoad_LDF !== undefined && analysis.totalLoad_LDF !== 1.0) ||
    (analysis.CL_value !== undefined && analysis.CL_value !== 1.0) ||
    (analysis.Cr_value !== undefined && analysis.Cr_value !== 1.0) ||
    (analysis.adjCM_factor !== undefined && analysis.adjCM_factor !== 1.0) ||
    (analysis.adjCF_factor !== undefined && analysis.adjCF_factor !== 1.0) ||
    (analysis.adjCfu_factor !== undefined && analysis.adjCfu_factor !== 1.0) ||
    (analysis.adjCi_factor !== undefined && analysis.adjCi_factor !== 1.0) ||
    (analysis.adjCt_factor !== undefined && analysis.adjCt_factor !== 1.0) ||
    (analysis.adjCV_factor !== undefined && analysis.adjCV_factor !== 1.0)
  );

  return (
    <View style={styles.beamAnalysisContainer} wrap={false}>
      <View style={styles.beamTitleRow}>
        <Text style={styles.beamName}>{beamNameText}</Text>
      </View>
      <View style={styles.beamDetailsRow}>
        <Text style={styles.materialInfoText}>{materialInfo}</Text>
        <Text style={styles.beamLengthInfo}>
          L = {analysis.L_ft !== undefined ? analysis.L_ft.toFixed(2) : 'N/A'} ft, Lu = {analysis.Lu_ft !== undefined ? analysis.Lu_ft.toFixed(2) : 'N/A'} ft
        </Text>
      </View>

      <View style={styles.contentRow}>
        <View style={styles.primaryLeftColumn}>
          <View style={styles.loadsTable}>
            <View style={styles.loadsTableHeader}>
              <Text style={[styles.loadsTableCellHeader, { flex: descFlex, textAlign: 'left', paddingLeft: 4 }]}>{" "}</Text>
              <Text style={[styles.loadsTableCellHeader, { flex: atToFlex }]}>At</Text>
              <Text style={[styles.loadsTableCellHeader, { flex: atToFlex }]}>To</Text>
              {presentLoadTypes.map((loadTypeKey, index) => {
                const mapping = loadTypeMapping[loadTypeKey as keyof typeof loadTypeMapping];
                const isLastColumn = index === presentLoadTypes.length - 1;
                return (
                  <Text 
                    key={loadTypeKey}
                    style={[
                      styles.loadsTableCellHeader, 
                      { flex: loadColFlex }, 
                      isLastColumn ? { borderRightWidth: 0 } : {}
                    ]}
                  >
                    {mapping.symbol}
                  </Text>
                );
              })}
            </View>
            {sortedLoadGroups.map((loadGroup, index, arr) => {
              return (
                <View 
                  key={index} 
                  style={{ 
                    flexDirection: 'row', 
                    borderBottomWidth: index === arr.length - 1 ? 0 : 0.5, 
                    borderColor: '#BFBFBF' 
                  }}
                >
                  <Text style={[styles.loadsTableCell, { flex: descFlex, textAlign: 'left', paddingLeft: 4 }]}>{`w${index + 1}, plf`}</Text>
                  <Text style={[styles.loadsTableCell, { flex: atToFlex }]}>{format(loadGroup.startPosition, 1)}</Text>
                  <Text style={[styles.loadsTableCell, { flex: atToFlex }]}>{format(loadGroup.endPosition, 1)}</Text>
                  {presentLoadTypes.map((loadTypeKey, colIndex) => {
                    const mapping = loadTypeMapping[loadTypeKey as keyof typeof loadTypeMapping];
                    const loadForThisType = loadGroup.loads.find(load => 
                      load.loadType && mapping.detect.includes(load.loadType.toUpperCase())
                    );
                    const isLastColumn = colIndex === presentLoadTypes.length - 1;
                    
                    return (
                      <Text 
                        key={loadTypeKey}
                        style={[
                          styles.loadsTableCell, 
                          { flex: loadColFlex }, 
                          isLastColumn ? { borderRightWidth: 0 } : {}
                        ]}
                      >
                        {loadForThisType ? formatLoadMagnitude(loadForThisType) : '-'}
                      </Text>
                    );
                  })}
                </View>
              );
            })}
          </View>

          <View style={styles.resultsValueBlock}>
            {(analysis.reactions || []).map((reaction, index) => (
              <DataDisplayRow 
                key={`reaction-${index}`}
                label={<LabelWithSubscript base="R" sub={`${index + 1}`}/>} 
                value={reaction.value_lbs !== undefined ? reaction.value_lbs.toFixed(2) : 'N/A'} 
                unit={reaction.desc_lbs ? `lbs (${reaction.desc_lbs})` : "lbs"} 
              />
            ))}
            <DataDisplayRow 
              label="Vmax" 
              value={analysis.Vmax_lbs !== undefined ? analysis.Vmax_lbs.toFixed(2) : 'N/A'} 
              unit={analysis.Vmax_position_ft !== undefined ? `lbs @ x=${analysis.Vmax_position_ft.toFixed(2)}ft ${analysis.case_value && analysis.maxShearValue_loadComboName ? `(${analysis.maxShearValue_loadComboName})` : ''}` : "lbs"} 
            />
            <DataDisplayRow 
              label="M" 
              value={analysis.M_in_kips !== undefined ? analysis.M_in_kips.toFixed(1) : 'N/A'} 
              unit={analysis.M_position_ft !== undefined ? `in-kips @ x=${analysis.M_position_ft.toFixed(2)}ft ${analysis.case_value && analysis.maxMomentValue_loadComboName ? `(${analysis.maxMomentValue_loadComboName})` : ''}` : "in-kips"} 
            />
          </View>
        </View>

        <View style={styles.middleColumn}>
          <View style={styles.dataBlock}>
            <Text style={styles.dataBlockTitle}>PROPERTIES:</Text>
            <DataDisplayRow label="b" value={analysis.try_b_in !==undefined ? analysis.try_b_in.toFixed(2) : 'N/A'} unit="in" />
            <DataDisplayRow label="d" value={analysis.try_d_in !==undefined ? analysis.try_d_in.toFixed(2) : 'N/A'} unit="in" />
            <DataDisplayRow label="A" value={analysis.try_A_in2 !==undefined ? analysis.try_A_in2.toFixed(2) : 'N/A'} unit={<UnitWithSuperscript base="in" exponent="2"/>} />
            <DataDisplayRow label="S" value={analysis.try_S_in3 !==undefined ? analysis.try_S_in3.toFixed(1) : 'N/A'} unit={<UnitWithSuperscript base="in" exponent="3"/>} />
            <DataDisplayRow label="I" value={analysis.try_I_in4 !==undefined ? analysis.try_I_in4.toFixed(1) : 'N/A'} unit={<UnitWithSuperscript base="in" exponent="4"/>} />
          </View>

          <View style={styles.dataBlock}> 
            <DataDisplayRow label={<LabelWithSubscript base="E" prime />} value={format(analysis.adjE_ksi, 0)} unit="ksi" />
            <DataDisplayRow label={<LabelWithSubscript base="E" prime sub="min" />} value={format(analysis.adjEmin_ksi, 0)} unit="ksi" />
            <DataDisplayRow label="G" value={analysis.adjG_psi !== undefined ? analysis.adjG_psi.toFixed(2) : 'N/A'} unit="" /> 
          </View>
        </View>

        <View style={styles.primaryRightColumn}>
          {hasAdjustedFactors && (
            <View style={styles.dataBlock}>
              <Text style={styles.dataBlockTitle}>ADJUSTED FACTORS</Text>
              {analysis.totalLoad_LDF !== undefined && analysis.totalLoad_LDF !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="D (LDF)" />} value={analysis.totalLoad_LDF.toFixed(2)} unit="" />
              )}
              {analysis.CL_value !== undefined && analysis.CL_value !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="L" />} value={analysis.CL_value.toFixed(2)} unit="" />
              )}
              {analysis.Cr_value !== undefined && analysis.Cr_value !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="r" />} value={analysis.Cr_value.toFixed(2)} unit="" />
              )}
              {analysis.adjCM_factor !== undefined && analysis.adjCM_factor !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="M" />} value={analysis.adjCM_factor.toFixed(2)} unit="" />
              )}
              {analysis.adjCF_factor !== undefined && analysis.adjCF_factor !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="F" />} value={analysis.adjCF_factor.toFixed(2)} unit="" />
              )}
              {analysis.adjCfu_factor !== undefined && analysis.adjCfu_factor !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="fu" />} value={analysis.adjCfu_factor.toFixed(2)} unit="" />
              )}
              {analysis.adjCi_factor !== undefined && analysis.adjCi_factor !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="i" />} value={analysis.adjCi_factor.toFixed(2)} unit="" />
              )}
              {analysis.adjCt_factor !== undefined && analysis.adjCt_factor !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="t" />} value={analysis.adjCt_factor.toFixed(2)} unit="" />
              )}
              {analysis.adjCV_factor !== undefined && analysis.adjCV_factor !== 1.0 && (
                <DataDisplayRow label={<LabelWithSubscript base="C" sub="V" />} value={analysis.adjCV_factor.toFixed(2)} unit="" />
              )}
            </View>
          )}

          <View style={styles.dataBlock}>
            <Text style={styles.dataBlockTitle}>STRESSES & DEFLECTIONS {analysis.case_value ? `(${analysis.case_value})` : ''}</Text>
            <StressDeflectionDisplayRow
              label={<LabelWithSubscript base="f" sub="v" baseStyle={styles.stressDeflectionLabel} />}
              actualValue={format(analysis.fv_psi, 2)}
              actualUnit="psi"
              allowableValueLabel={<LabelWithSubscript base="F" prime sub="v" suffix="=" baseStyle={styles.stressDeflectionAllowableLabel} subStyle={styles.subscriptText} />}
              allowableValue={analysis.totalLoad_FvPrime_psi !== undefined ? analysis.totalLoad_FvPrime_psi : 'N/A'}
              percentage={analysis.fv_percentage || '-'}
            />
            <StressDeflectionDisplayRow
              label={<LabelWithSubscript base="f" sub="b" baseStyle={styles.stressDeflectionLabel} />}
              actualValue={format(analysis.fb_psi, 2)}
              actualUnit="psi"
              allowableValueLabel={<LabelWithSubscript base="F" prime sub="b" suffix="=" baseStyle={styles.stressDeflectionAllowableLabel} subStyle={styles.subscriptText} />}
              allowableValue={analysis.totalLoad_FbPrime_psi !== undefined ? analysis.totalLoad_FbPrime_psi : 'N/A'}
              percentage={analysis.fb_percentage || '-'}
            />
            <StressDeflectionDisplayRow
              label={<LabelWithSubscript base="d" sub="TL" baseStyle={styles.stressDeflectionLabelDeflection} />}
              actualValue={analysis.d_TL_in !== undefined ? format(analysis.d_TL_in, 2) : 'N/A'}
              actualUnit="in"
              isDeflection={true}
              limitRatio={
                analysis.d_TL_loadCombo 
                  ? `(Limit L/${analysis.d_TL_L_ratio || '240'}, ${analysis.d_TL_loadCombo})` 
                  : analysis.d_TL_L_ratio 
                    ? `L/${analysis.d_TL_L_ratio}` 
                    : 'N/A'
              }
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export const ProjectCalculationsPDF: React.FC<ProjectCalculationsPDFProps> = ({ projectData }) => {
  const { 
    title, 
    clientName, 
    projectAddress, 
    clientAddress,
    clientPhone,
    jobNo, 
    by, 
    date, 
    sheet, 
    beamAnalysisResults,
    organization,
    allProjectFolders,
    isProjectLevelPdf,
    currentFolderNameForSingleFolderPdf
  } = projectData;

  const fallbackCompanyDetails = {
    name: 'DALE CHRISTIAN / STRUCTURAL ENGINEER, INC.',
    addressLine1: 'CONSULTING & DESIGN',
    addressLine2: '1744 West Katella Avenue, Suite 107',
    addressLine3: 'Orange, CA 92867',
    phone: '************',
    logoUrl: 'https://corsproxy.io/?url=https://i.imgur.com/Jz5r42x.png' // Placeholder Logo
  };

  const org = organization;

  const companyDetails = {
    name: org?.name || fallbackCompanyDetails.name,
    addressLine1: org?.address ? (org.address.split('\n')[0] || org.address) : fallbackCompanyDetails.addressLine1,
    addressLine2: org?.address && org.address.split('\n').length > 1 ? org.address.split('\n')[1] : (org?.address ? '' : fallbackCompanyDetails.addressLine2),
    addressLine3: org?.address && org.address.split('\n').length > 2 ? org.address.split('\n')[2] : (org?.address ? '' : fallbackCompanyDetails.addressLine3),
    phone: org?.phone || fallbackCompanyDetails.phone,
    logoUrl: fallbackCompanyDetails.logoUrl
  };

  // Construct the author string based on available organization name or fallback
  const authorString = org?.name || fallbackCompanyDetails.name;

  const generationDate = new Date().toLocaleString(); // Get current date and time

  // Construct subtitle text
  const subtitleParts = [];
  if (clientName) subtitleParts.push(clientName);
  if (projectAddress) subtitleParts.push(projectAddress);
  const subtitleText = subtitleParts.join(' | ');

  // Sort folders alphabetically by name for consistent order in PDF
  const sortedFolders = [...(allProjectFolders || [])].sort((a, b) => a.name.localeCompare(b.name));

  // Group analyses by folderId for project-level PDF
  const analysesByFolder: { [key: string]: PDFBeamAnalysisResult[] } = {};
  const analysesWithoutFolder: PDFBeamAnalysisResult[] = [];

  if (isProjectLevelPdf) {
    beamAnalysisResults.forEach(analysis => {
      if (analysis.folderId) {
        if (!analysesByFolder[analysis.folderId]) {
          analysesByFolder[analysis.folderId] = [];
        }
        analysesByFolder[analysis.folderId].push(analysis);
      } else {
        analysesWithoutFolder.push(analysis);
      }
    });
    // Sort analyses within each folder group by their original order (assuming `beamAnalysisResults` is pre-sorted)
    for (const folderId in analysesByFolder) {
      analysesByFolder[folderId].sort((a, b) => 
        beamAnalysisResults.findIndex(item => item.id === a.id) - 
        beamAnalysisResults.findIndex(item => item.id === b.id)
      );
    }
    analysesWithoutFolder.sort((a, b) => 
      beamAnalysisResults.findIndex(item => item.id === a.id) - 
      beamAnalysisResults.findIndex(item => item.id === b.id)
    );
  }

  return (
    <Document author={authorString} title={`${title} - Calculations Report - ${generationDate}`}>
      <Page size="LETTER" style={styles.page}>
        <View style={styles.companyHeaderContainer} fixed>
            <View style={styles.companyInfoAndLogo}>
                {/* {companyDetails.logoUrl && <Image src={companyDetails.logoUrl} style={styles.logo} />} */}
                <View style={styles.companyInfo}>
                    <Text style={styles.companyName}>{companyDetails.name}</Text>
                    <Text style={styles.companyAddressText}>{companyDetails.addressLine1}</Text>
                    <Text style={styles.companyAddressText}>{companyDetails.addressLine2}</Text>
                    <Text style={styles.companyAddressText}>{companyDetails.addressLine3}</Text>
                    <Text style={styles.companyAddressText}>{companyDetails.phone}</Text>
                    {jobNo && <Text style={[styles.companyAddressText, { marginTop: 3, fontWeight: 'bold' }]}>Job No.: {jobNo}</Text>}
                </View>
            </View>
          <View style={[styles.jobInfoContainer, { alignItems: 'flex-end' }]}>
            {clientName && 
              <Text style={[styles.companyName, { fontSize: 9, marginBottom: 1, textAlign: 'right' }]}>{clientName}</Text>}
            {clientAddress && 
              clientAddress.split('\n').map((line, index) => (
                <Text key={index} style={[styles.companyAddressText, { fontSize: 8, marginBottom: 0.5, textAlign: 'right' }]}>{line}</Text>
            ))}
            {clientPhone && 
              <Text style={[styles.companyAddressText, { fontSize: 8, textAlign: 'right' }]}>{clientPhone}</Text>}
          </View>
        </View>

        <View style={styles.projectHeaderContainer} fixed>
          {title && <Text style={styles.projectTitle}>{title}</Text>}
          {subtitleText ? (
            <Text style={styles.projectSubtitle}>
              {subtitleText}
            </Text>
          ) : null}
        </View>

        {/* Content Section */}
        {isProjectLevelPdf ? (
          <>
            {/* Analyses not in any folder first (if any) */}
            {analysesWithoutFolder.length > 0 && (
              <>
                {/* Optionally add a header like "Uncategorized Calculations" or just list them */}
                {analysesWithoutFolder.map((analysis, index) => (
                  <BeamAnalysisSection key={analysis.id || `no-folder-${index}`} analysis={analysis} />
                ))}
              </>
            )}

            {/* Then iterate through sorted folders and their analyses */}
            {sortedFolders.map((folder, folderIndex) => {
              const analysesInFolder = analysesByFolder[folder.id] || [];
              if (analysesInFolder.length === 0) return null; // Skip folder if no analyses for it

              return (
                <View key={folder.id} break={folderIndex > 0 || analysesWithoutFolder.length > 0}>
                  <View style={styles.folderHeaderContainer}>
                    <Text style={styles.folderNameText}>{folder.name}</Text>
                  </View>
                  {analysesInFolder.map((analysis, index) => (
                    <BeamAnalysisSection key={analysis.id || `folder-${folder.id}-${index}`} analysis={analysis} />
                  ))}
                </View>
              );
            })}
          </>
        ) : (
          // Single Folder PDF View
          <>
            {currentFolderNameForSingleFolderPdf && (
              <View style={styles.folderHeaderContainer}>
                <Text style={styles.folderNameText}>{currentFolderNameForSingleFolderPdf}</Text>
              </View>
            )}
            {beamAnalysisResults.map((analysis, index) => (
              <BeamAnalysisSection key={analysis.id || `single-folder-${index}`} analysis={analysis} />
            ))}
          </>
        )}

        {/* Footer View */}
        <View style={styles.footer} fixed>
          <Text style={styles.footerTextLeft}>{generationDate}</Text>
          <Text style={styles.footerTextRight} render={({ pageNumber, totalPages }) => (
            `Page ${pageNumber} of ${totalPages}`
          )} />
        </View>
      </Page>
    </Document>
  );
}; 