"use client";

import { useState } from "react";
import { BeamAnalysis } from "@/components/beam-analysis/beam-analysis";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { UnitSystem } from "@/lib/types/units/unit-system";

export default function BeamAnalysisPage() {
  const [unitSystem, setUnitSystem] = useState<UnitSystem>(UnitSystem.IMPERIAL);

  return (
    <main className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-4xl font-bold text-primary">Beam Analysis</h1>
            <div className="flex items-center space-x-2">
              <Label htmlFor="unit-system">Metric</Label>
              <Switch
                id="unit-system"
                checked={unitSystem === UnitSystem.IMPERIAL}
                onCheckedChange={(checked) => setUnitSystem(checked ? UnitSystem.IMPERIAL : UnitSystem.METRIC)}
                defaultChecked
              />
              <Label htmlFor="unit-system">Imperial</Label>
            </div>
          </div>
          <p className="text-muted-foreground text-center">Advanced beam analysis using finite element method</p>
        </header>
        <BeamAnalysis unitSystem={unitSystem} />
      </div>
    </main>
  );
} 