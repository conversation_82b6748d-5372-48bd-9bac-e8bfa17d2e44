"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState, ReactNode } from "react";
import { OrganizationsProvider } from "./context/OrganizationsContext";

export function Providers({ children }: { children: ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <OrganizationsProvider>
        {children}
      </OrganizationsProvider>
    </QueryClientProvider>
  );
}