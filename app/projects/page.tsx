'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// Assuming your Prisma models are accessible or you define a similar interface here
interface Calculation {
  id: string;
  name: string;
  type: string;
  // add other calculation fields
}

interface BeamAnalysisResult {
  id: string;
  name?: string | null;
  // add other beam analysis fields
}

interface Project {
  id: string;
  title: string;
  clientName?: string | null;
  address?: string | null;
  createdAt: string; // Assuming ISO string date format
  updatedAt: string; // Assuming ISO string date format
  calculations: Calculation[];
  beamAnalysisResults: BeamAnalysisResult[];
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newProjectTitle, setNewProjectTitle] = useState('');
  const [newProjectClientName, setNewProjectClientName] = useState('');
  const [newProjectAddress, setNewProjectAddress] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProjects, setSelectedProjects] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<keyof Project | 'actions'>('updatedAt'); // Default sort
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // TODO: Implement actual edit and delete logic
  const handleEditProject = (projectId: string) => {
    // Placeholder for edit functionality
    console.log("Edit project:", projectId);
    alert("Edit functionality not yet implemented.");
  };

  const handleDeleteProject = async (projectId: string) => {
    // Placeholder for delete functionality
    console.log("Delete project:", projectId);
    alert("Delete functionality not yet implemented.");
    // Optimistically remove the project from the UI or refetch projects
    // For now, just an alert and console log.
    // Example:
    // try {
    //   const response = await fetch(`/api/projects/${projectId}`, { method: 'DELETE' });
    //   if (!response.ok) {
    //     throw new Error('Failed to delete project');
    //   }
    //   setProjects(projects.filter(p => p.id !== projectId));
    // } catch (err) {
    //   setError(err instanceof Error ? err.message : 'An unknown error occurred');
    // }
  };

  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prevSelected => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(projectId)) {
        newSelected.delete(projectId);
      } else {
        newSelected.add(projectId);
      }
      return newSelected;
    });
  };

  const handleSort = (column: keyof Project | 'actions') => {
    if (column === 'actions') return; // 'Actions' column is not sortable

    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const sortedFilteredProjects = [...projects]
    .filter(project => 
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
      (project.clientName && project.clientName.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      if (sortColumn === 'actions') return 0;
      const valA = a[sortColumn];
      const valB = b[sortColumn];

      let comparison = 0;
      if (valA === null || valA === undefined) comparison = -1;
      else if (valB === null || valB === undefined) comparison = 1;
      else if (typeof valA === 'string' && typeof valB === 'string') {
        comparison = valA.localeCompare(valB);
      } else if (typeof valA === 'number' && typeof valB === 'number') {
        comparison = valA - valB;
      } else if (valA instanceof Date && valB instanceof Date) {
        comparison = valA.getTime() - valB.getTime();
      } else {
        // Fallback for mixed types or other types (e.g. booleans) - convert to string
        comparison = String(valA).localeCompare(String(valB));
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });

  const handleSelectAllProjects = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const allFilteredProjectIds = new Set(sortedFilteredProjects.map(p => p.id));
      setSelectedProjects(allFilteredProjectIds);
    } else {
      setSelectedProjects(new Set());
    }
  };

  // Placeholder for bulk delete
  const handleBulkDelete = async () => {
    if (selectedProjects.size === 0) {
      alert("No projects selected for deletion.");
      return;
    }
    // Implement bulk deletion logic here
    // alert(`Bulk delete for ${selectedProjects.size} projects not yet implemented.`);
    // console.log("Projects to delete:", Array.from(selectedProjects));
    if (window.confirm(`Are you sure you want to delete ${selectedProjects.size} selected project(s)? This action cannot be undone.`)) {
      try {
        const projectIdsToDelete = Array.from(selectedProjects);
        const response = await fetch('/api/projects', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ projectIds: projectIdsToDelete }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete projects');
        }

        // Update UI: remove deleted projects and clear selection
        setProjects(prevProjects => prevProjects.filter(p => !selectedProjects.has(p.id)));
        setSelectedProjects(new Set());
        setError(null); // Clear any previous errors
        alert(`${projectIdsToDelete.length} project(s) deleted successfully.`);

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred during bulk deletion';
        setError(errorMessage);
        console.error("Bulk delete error:", err);
        alert(`Error deleting projects: ${errorMessage}`);
      }
    }
  };

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/projects');
        if (!response.ok) {
          throw new Error('Failed to fetch projects');
        }
        const data = await response.json();
        setProjects(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error(err);
      }
      setIsLoading(false);
    };

    fetchProjects();
  }, []);

  const handleCreateProject = async () => {
    if (!newProjectTitle.trim()) {
      alert('Project title is required.');
      return;
    }
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newProjectTitle.trim(),
          clientName: newProjectClientName.trim() || null,
          address: newProjectAddress.trim() || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const newProject = await response.json();
      setProjects([...projects, newProject]);
      setShowCreateModal(false);
      setNewProjectTitle('');
      setNewProjectClientName('');
      setNewProjectAddress('');
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred creating project';
      setError(errorMessage);
      console.error(err);
      // Removed alert, error is displayed in the UI
    }
  };

  if (isLoading) {
    return <div className="container mx-auto p-4">Loading projects...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Projects</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Create Project
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-400 rounded">
          <p>Error: {error}</p>
        </div>
      )}

      <div className="mb-4">
        <input 
          type="text"
          placeholder="Search by project or client name..."
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {sortedFilteredProjects.length === 0 && !isLoading && !error ? (
        <p>{searchTerm ? 'No projects match your search.' : 'No projects yet. Click "Create Project" to add one.'}</p>
      ) : (
        <>
          {selectedProjects.size > 0 && (
            <div className="mb-4">
              <button
                onClick={handleBulkDelete}
                className="bg-red-600 hover:bg-red-800 text-white font-bold py-2 px-4 rounded"
              >
                Delete Selected ({selectedProjects.size})
              </button>
              {/* Add other bulk action buttons here, e.g., for bulk edit */}
            </div>
          )}
          <div className="overflow-x-auto shadow-md sm:rounded-lg">
            <table className="min-w-full text-sm text-left text-gray-500 dark:text-gray-400">
              <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                  <th scope="col" className="p-4">
                    <div className="flex items-center">
                      <input
                        id="checkbox-all-search"
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        onChange={handleSelectAllProjects}
                        checked={sortedFilteredProjects.length > 0 && selectedProjects.size === sortedFilteredProjects.length}
                        disabled={sortedFilteredProjects.length === 0}
                      />
                      <label htmlFor="checkbox-all-search" className="sr-only">checkbox</label>
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onClick={() => handleSort('title')}>
                    Project Name {sortColumn === 'title' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th scope="col" className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onClick={() => handleSort('clientName')}>
                    Client Name {sortColumn === 'clientName' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th scope="col" className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onClick={() => handleSort('address')}>
                    Address {sortColumn === 'address' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th scope="col" className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onClick={() => handleSort('createdAt')}>
                    Created {sortColumn === 'createdAt' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th scope="col" className="px-6 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600" onClick={() => handleSort('updatedAt')}>
                    Updated {sortColumn === 'updatedAt' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </th>
                  <th scope="col" className="px-6 py-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedFilteredProjects.map((project) => (
                  <tr
                    key={project.id}
                    className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                  >
                    <td className="w-4 p-4">
                      <div className="flex items-center">
                        <input
                          id={`checkbox-table-search-${project.id}`}
                          type="checkbox"
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                          checked={selectedProjects.has(project.id)}
                          onChange={() => handleSelectProject(project.id)}
                        />
                        <label htmlFor={`checkbox-table-search-${project.id}`} className="sr-only">checkbox</label>
                      </div>
                    </td>
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      <Link href={`/projects/${project.id}`} className="hover:text-blue-700 transition-colors">
                        {project.title}
                      </Link>
                    </th>
                    <td className="px-6 py-4">{project.clientName || '-'}</td>
                    <td className="px-6 py-4">{project.address || '-'}</td>
                    <td className="px-6 py-4">{new Date(project.createdAt).toLocaleDateString()}</td>
                    <td className="px-6 py-4">{new Date(project.updatedAt).toLocaleDateString()}</td>
                    <td className="px-6 py-4 flex space-x-2">
                      <button
                        onClick={() => handleEditProject(project.id)}
                        className="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteProject(project.id)}
                        className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}

      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-75 overflow-y-auto h-full w-full flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-2xl w-full max-w-lg mx-4 sm:mx-0">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Create New Project</h2>
            <div className="mb-4">
              <label htmlFor="projectTitle" className="block text-sm font-medium text-gray-700 mb-1">Project Title*</label>
              <input
                type="text"
                id="projectTitle"
                value={newProjectTitle}
                onChange={(e) => setNewProjectTitle(e.target.value)}
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-300"
                required
              />
            </div>
            <div className="mb-4">
              <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-1">Client Name</label>
              <input
                type="text"
                id="clientName"
                value={newProjectClientName}
                onChange={(e) => setNewProjectClientName(e.target.value)}
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-300"
              />
            </div>
            <div className="mb-8">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Address</label>
              <input
                type="text"
                id="address"
                value={newProjectAddress}
                onChange={(e) => setNewProjectAddress(e.target.value)}
                className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-300"
              />
            </div>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md shadow-sm transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateProject}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 