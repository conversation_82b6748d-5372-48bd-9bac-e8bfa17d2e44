'use client';

import { useEffect, useState, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { BeamAnalysisResultCard } from '@/components/projects/BeamAnalysisResultCard';
import { UnitSystem } from '@/lib/types/units/unit-system';
import { PlusCircle, ArrowLeft, FileDown, Eye } from 'lucide-react';
import dynamic from 'next/dynamic';
import { ProjectCalculationsPDF, type PDFBeamAnalysisResult } from '../../components/projects/ProjectCalculationsPDF';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';

// Dynamically import PDFDownloadLink and PDFViewer
const PDFDownloadLink = dynamic(
  () => import('@react-pdf/renderer').then((mod) => mod.PDFDownloadLink),
  {
    ssr: false,
    loading: () => <p>Loading download link...</p>,
  }
);

const PDFViewer = dynamic(
  () => import('@react-pdf/renderer').then((mod) => mod.PDFViewer),
  {
    ssr: false,
    loading: () => <p>Loading PDF viewer...</p>,
  }
);

// Interfaces
interface Calculation {
  id: string;
  name: string;
  type: string;
  order: number;
}

// This interface is used for displaying in the UI card, may differ from PDF data structure
interface BeamAnalysisResultForCard {
  id: string;
  calculationId: string;
  name?: string | null;
  length: number;
  modulusOfElasticity: number;
  momentOfInertia: number;
  beamProperties: string; // JSON string
  loads: string;          // JSON string
  supports: string;       // JSON string
  results: string | null; // JSON string
  createdAt: string;
  updatedAt: string;
  originalUnitSystem?: UnitSystem | string;
  order?: number | null; // Added order field
  area?: number;
}

interface Project {
  id: string;
  title: string;
  clientName?: string | null;
  address?: string | null;
  createdAt: string;
  updatedAt: string;
  calculations: Calculation[];
  beamAnalysisResults: BeamAnalysisResultForCard[]; // Use card-specific interface here
  jobNo?: string;
  projectBy?: string;
  projectDate?: string;
  sheetNo?: string;
  projectNumber?: string;
}

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const pageSearchParams = useSearchParams();
  const projectId = params?.projectId as string;

  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [orderedAnalyses, setOrderedAnalyses] = useState<BeamAnalysisResultForCard[]>([]);
  const [orderedCalculations, setOrderedCalculations] = useState<Calculation[]>([]);

  const fetchProjectDetails = useCallback(async () => {
    if (!projectId) return;
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/projects/${projectId}`, { cache: 'no-store' });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch project details (status: ${response.status})`);
      }
      const data: Project = await response.json();
      
      // Transform the raw API data to match BeamAnalysisResultForCard interface
      if (data.beamAnalysisResults) {
        const transformedAnalyses: BeamAnalysisResultForCard[] = data.beamAnalysisResults.map((br: any) => ({
          ...br,
          calculationId: br.calculation?.id || br.id, // Use calculation.id or fallback to br.id
          updatedAt: br.updatedAt || new Date().toISOString(), // Ensure updatedAt is present
        }));
        data.beamAnalysisResults = transformedAnalyses;
      }
      
      setProject(data);
      if (data && data.beamAnalysisResults) {
        setOrderedAnalyses(data.beamAnalysisResults.sort((a, b) => (a.order ?? 0) - (b.order ?? 0)));
      }
      if (data && data.calculations) {
        setOrderedCalculations(data.calculations.sort((a, b) => a.order - b.order));
      }
    } catch (err) {
      console.error("Error fetching project details:", err);
      setError(err instanceof Error ? err.message : String(err));
      setProject(null);
    } finally {
      setIsLoading(false);
    }
  }, [projectId, pageSearchParams]);

  useEffect(() => {
    fetchProjectDetails();
  }, [fetchProjectDetails]);

  useEffect(() => {
    if (project && project.beamAnalysisResults) {
      const sortedAnalyses = [...project.beamAnalysisResults].sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
      if (JSON.stringify(orderedAnalyses) !== JSON.stringify(sortedAnalyses)) {
        setOrderedAnalyses(sortedAnalyses);
      }
    }
    if (project && project.calculations) {
      const sortedCalculations = [...project.calculations].sort((a, b) => a.order - b.order);
      if (JSON.stringify(orderedCalculations) !== JSON.stringify(sortedCalculations)) {
        setOrderedCalculations(sortedCalculations);
      }
    }
  }, [project?.beamAnalysisResults, project?.calculations, orderedAnalyses, orderedCalculations]);

  const handleAnalysisDeleted = (deletedAnalysisId: string) => {
    setProject(prevProject => {
      if (!prevProject) return null;
      return {
        ...prevProject,
        beamAnalysisResults: prevProject.beamAnalysisResults.filter(
          analysis => analysis.id !== deletedAnalysisId
        ),
      };
    });
    setOrderedAnalyses(prevAnalyses => prevAnalyses.filter(analysis => analysis.id !== deletedAnalysisId));
  };

  const onDragEnd = async (result: DropResult) => {
    const { source, destination, type: dndType } = result;
    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    if (dndType === 'beamAnalysis') {
      const items = Array.from(orderedAnalyses);
      const [reorderedItem] = items.splice(source.index, 1);
      items.splice(destination.index, 0, reorderedItem);

      setOrderedAnalyses(items);
      if (project) {
        setProject(prevProject => {
          if (!prevProject) return null;
          const updatedAnalyses = items.map((analysis, index) => ({ ...analysis, order: index }));
          return { ...prevProject, beamAnalysisResults: updatedAnalyses };
        });
      }
      const analysesToUpdate = items.map((item, index) => ({
        id: item.id,
        order: index,
      }));
      try {
        const response = await fetch(`/api/projects/${projectId}/beam-analyses/reorder`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ beamAnalyses: analysesToUpdate }),
        });
        if (!response.ok) {
          const errorData = await response.json();
          console.error('Failed to reorder beam analyses:', errorData.error);
          fetchProjectDetails();
        }
      } catch (error) {
        console.error('Error reordering beam analyses:', error);
        fetchProjectDetails();
      }
    } else if (dndType === 'calculation') {
      const items = Array.from(orderedCalculations);
      const [reorderedItem] = items.splice(source.index, 1);
      items.splice(destination.index, 0, reorderedItem);

      setOrderedCalculations(items);
      if (project) {
        setProject(prevProject => {
          if (!prevProject) return null;
          const updatedCalculations = items.map((calc, index) => ({ ...calc, order: index }));
          return { ...prevProject, calculations: updatedCalculations };
        });
      }

      const calculationsToUpdate = items.map((item, index) => ({
        id: item.id,
        order: index,
      }));

      try {
        const response = await fetch(`/api/projects/${projectId}/calculations/reorder`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ calculations: calculationsToUpdate }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Failed to reorder calculations:', errorData.error);
          fetchProjectDetails();
        }
      } catch (error) {
        console.error('Error reordering calculations:', error);
        fetchProjectDetails();
      }
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-6"></div>
          <div className="space-y-6">
            {[1, 2].map(i => (
              <div key={i} className="h-40 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-red-500 text-lg">Error loading project: {error}</p>
        <Button onClick={fetchProjectDetails} className="mt-4">Try Again</Button>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-xl">Project not found.</p>
        <Link href="/projects">
          <Button variant="outline" className="mt-4">Back to Projects</Button>
        </Link>
      </div>
    );
  }

  // Function to transform BeamAnalysisResultForCard to PDFBeamAnalysisResult
  const transformAnalysisForPDF = (analysis: BeamAnalysisResultForCard): PDFBeamAnalysisResult => {
    let properties: any = {};
    let parsedRawLoadsData: any[] = [];
    let analysisResults: any = {};

    console.log(`[Debug PDF Data] For analysis ID: ${analysis.id}, RAW analysis.loads:`, analysis.loads);
    try {
      if (analysis.loads) {
        parsedRawLoadsData = JSON.parse(analysis.loads);
      } else {
          console.warn(`[Debug PDF Data] For analysis ID: ${analysis.id}, analysis.loads is undefined or empty.`);
      }
    } catch (e) {
      console.error(`Error parsing loads JSON for analysis ${analysis.id}:`, e, analysis.loads);
    }
    
    const detailedLoadsForPDF: PDFBeamAnalysisResult['loads'] = [];
    if (Array.isArray(parsedRawLoadsData)) {
      parsedRawLoadsData.forEach(loadGroup => {
        if (loadGroup && Array.isArray(loadGroup.loads)) {
          loadGroup.loads.forEach((individualLoad: any) => {
            detailedLoadsForPDF.push({
              label: individualLoad.label || loadGroup.label || 'Unnamed Load',
              startPosition: individualLoad.startPosition,
              endPosition: individualLoad.endPosition,
              loadType: individualLoad.loadType,
              type: individualLoad.type,
              startMagnitude: individualLoad.startMagnitude,
              endMagnitude: individualLoad.endMagnitude,
            });
          });
        }
      });
    }
    console.log(`[Debug PDF Data] For analysis ID: ${analysis.id}, Populated detailedLoadsForPDF:`, JSON.stringify(detailedLoadsForPDF, null, 2));


    let totalDeadLoad_plf = 0;
    let totalFloorLiveLoad_plf = 0;
    let totalRoofLiveLoad_plf = 0;

    const mainLoadContainerForSummary = (Array.isArray(parsedRawLoadsData) && parsedRawLoadsData.length > 0) ? parsedRawLoadsData[0] : {};

    if (mainLoadContainerForSummary && Array.isArray(mainLoadContainerForSummary.loads)) {
      mainLoadContainerForSummary.loads.forEach((loadComponent: any) => {
        const magnitudeForSummary = parseFloat(String(loadComponent.startMagnitude)); 
        if (!isNaN(magnitudeForSummary)) {
          switch (loadComponent.loadType?.toLowerCase()) {
            case 'dead': totalDeadLoad_plf += magnitudeForSummary; break;
            case 'live': case 'floor-live': totalFloorLiveLoad_plf += magnitudeForSummary; break;
            case 'roof-live': case 'roof': case 'snow': totalRoofLiveLoad_plf += magnitudeForSummary; break;
            default:
              if (mainLoadContainerForSummary.isSelfWeight && loadComponent.label?.toLowerCase().includes('self')) {
                  totalDeadLoad_plf += magnitudeForSummary;
              } else {
              }
              break;
          }
        }
      });
    }
    const totalLoad_plf = totalDeadLoad_plf + totalFloorLiveLoad_plf + totalRoofLiveLoad_plf;

    console.log(`[Debug PDF Data] For analysis ID: ${analysis.id}, RAW analysis.results:`, analysis.results);
    try {
      if (analysis.results) analysisResults = JSON.parse(analysis.results);
      else console.warn(`[Debug PDF Data] For analysis ID: ${analysis.id}, analysis.results is undefined or empty.`);
    } catch (e) {
      console.error(`Error parsing results JSON for analysis ${analysis.id}:`, e, analysis.results);
    }
    console.log(`[Debug PDF Data] For analysis ID: ${analysis.id}, PROCESSED analysisResults object:`, JSON.stringify(analysisResults, null, 2));

    console.log(`[PDF Transform Debug] For analysis ID: ${analysis.id}`);
    console.log(`[PDF Transform Debug] Full properties object:`, JSON.stringify(properties, null, 2));
    try {
      if (analysis.beamProperties) {
        properties = JSON.parse(analysis.beamProperties);
      } else {
        properties = {};
      }
    } catch (e) {
      console.error(`Error parsing beamProperties JSON for analysis ${analysis.id}:`, e, analysis.beamProperties);
      properties = {};
    }

    console.log(`[PDF Transform Debug] Parsed properties:`, JSON.stringify(properties, null, 2));
    console.log(`[PDF Transform Debug] isManualMode:`, properties.isManualMode);
    console.log(`[PDF Transform Debug] manualWidth:`, properties.manualWidth);
    console.log(`[PDF Transform Debug] manualDepth:`, properties.manualDepth);
    console.log(`[PDF Transform Debug] manual_Area:`, properties.manual_Area);
    console.log(`[PDF Transform Debug] lumberProperties:`, JSON.stringify(properties.lumberProperties, null, 2));

    const designValuesFromProps = properties.designValues || {};
    
    const sectionProps = properties.section || properties.dimensions || properties;
    const lumberProps = properties.lumberProperties || {};

    const supportForces = analysisResults.supportReactions?.forces || {};
    const maxShearValue = analysisResults.maxShearValue || {};
    const maxMomentValue = analysisResults.maxMomentValue || {};
    const maxBendingStress = analysisResults.maxBendingStressRatio || {};
    const maxShearStress = analysisResults.maxShearStressRatio || {};
    const totalDeflection = analysisResults.maxTotalDeflectionDownward || analysisResults.summaryData?.maxTotalDeflectionDownward || {};
    const liveLoadDeflection = analysisResults.maxLiveLoadDeflectionDownward || analysisResults.summaryData?.maxLiveLoadDeflectionDownward || {};

    console.error(`🔍 TRANSFORMATION DEBUG FOR ${analysis.id} - totalDeflection:`, JSON.stringify(totalDeflection, null, 2));
    console.error(`🔍 TRANSFORMATION DEBUG FOR ${analysis.id} - totalDeflection.loadComboName:`, totalDeflection.loadComboName);
    console.error(`🔍 TRANSFORMATION DEBUG FOR ${analysis.id} - totalDeflection.limit:`, totalDeflection.limit);
    console.log(`[PDF Transform Debug] For analysis ID: ${analysis.id}, maxShearValue.loadComboName:`, maxShearValue.loadComboName);
    console.log(`[PDF Transform Debug] For analysis ID: ${analysis.id}, maxMomentValue.loadComboName:`, maxMomentValue.loadComboName);
    console.log(`[PDF Transform Debug] For analysis ID: ${analysis.id}, analysisResults.summaryData:`, JSON.stringify(analysisResults.summaryData, null, 2));

    // Additional specific debugging for the load combo assignment
    const finalLoadCombo = totalDeflection.loadComboName || maxBendingStress.loadComboName || maxShearStress.loadComboName || maxMomentValue.loadComboName || maxShearValue.loadComboName;
    console.error(`🔍 FINAL LOAD COMBO FOR ${analysis.id}:`, finalLoadCombo);
    console.error(`🔍 FINAL L RATIO FOR ${analysis.id}:`, totalDeflection.limit);

    const formatNumber = (num: any, decimalPlaces: number = 2): number | undefined => {
      if (num === null || num === undefined) return undefined;
      const parsedNum = parseFloat(String(num));
      if (isNaN(parsedNum)) return undefined;
      if (decimalPlaces === 0 && Number.isInteger(parsedNum)) return parsedNum;
      return parseFloat(parsedNum.toFixed(decimalPlaces));
    };
    const baseLft = typeof analysis.length === 'number' ? analysis.length : parseFloat(String(analysis.length)) || 0;

    // Extract properties with glulam handling
    let try_b_raw, try_d_raw, try_A_raw, try_S_raw, try_I_raw;
    
    // Check for glulam properties first
    if (properties.lumberType === 'glulam' && properties.selectedGluLamProperties) {
      const glulamProps = properties.selectedGluLamProperties;
      try_b_raw = glulamProps.width;
      try_d_raw = glulamProps.depth;
      try_A_raw = glulamProps.sectionProperties?.area;
      try_S_raw = glulamProps.sectionProperties?.Sx;
      try_I_raw = glulamProps.sectionProperties?.Ix;
    } else {
      // Standard lumber or manual mode fallback chain
      try_b_raw = lumberProps.standard_width !== undefined ? lumberProps.standard_width : (properties.manualWidth !== undefined ? parseFloat(properties.manualWidth) : (sectionProps.b_in || sectionProps.b || sectionProps.width_in || sectionProps.width));
      try_d_raw = lumberProps.standard_depth !== undefined ? lumberProps.standard_depth : (properties.manualDepth !== undefined ? parseFloat(properties.manualDepth) : (sectionProps.d_in || sectionProps.d || sectionProps.depth_in || sectionProps.depth));
      try_A_raw = lumberProps.area_of_section_a_in2 !== undefined ? lumberProps.area_of_section_a_in2 : (properties.manual_Area !== undefined ? properties.manual_Area : (analysis.area !== undefined ? analysis.area : (sectionProps.A_in2 || sectionProps.A || sectionProps.area_in2 || sectionProps.area)));
      try_S_raw = lumberProps.Sxx !== undefined ? lumberProps.Sxx : (properties.manual_Sxx !== undefined ? properties.manual_Sxx : (sectionProps.S_in3 || sectionProps.S || sectionProps.Sx_in3 || sectionProps.Sx));
      try_I_raw = lumberProps.Ixx !== undefined ? lumberProps.Ixx : (properties.manual_Ixx !== undefined ? properties.manual_Ixx : (analysis.momentOfInertia !== undefined ? analysis.momentOfInertia : (sectionProps.I_in4 || sectionProps.I || sectionProps.Ix_in4 || sectionProps.Ix)));
    }

    console.log(`[PDF Transform Debug] Raw values before manual mode logic:`, {
      try_b_raw, try_d_raw, try_A_raw, try_S_raw, try_I_raw
    });

    // Enhanced manual mode detection - check multiple possible indicators
    const isManualMode = properties.isManualMode || 
                        (!lumberProps.standard_width && properties.manualWidth) ||
                        (!lumberProps.area_of_section_a_in2 && properties.manual_Area) ||
                        (Object.keys(lumberProps).length === 0 && (properties.manualWidth || properties.manualDepth));
    
    console.log(`[PDF Transform Debug] Manual mode detected:`, isManualMode);

    // For manual mode or glulam mode, if we have width and depth but missing calculated properties, calculate them
    if ((isManualMode || properties.lumberType === 'glulam') && try_b_raw !== undefined && try_d_raw !== undefined) {
      const width = parseFloat(String(try_b_raw));
      const depth = parseFloat(String(try_d_raw));
      
      console.log(`[PDF Transform Debug] Beam dimensions:`, { width, depth });
      
      if (!isNaN(width) && width > 0 && !isNaN(depth) && depth > 0) {
        // Calculate section properties if they're missing
        if (try_A_raw === undefined) {
          try_A_raw = width * depth;
          console.log(`[PDF Transform Debug] Calculated Area:`, try_A_raw);
        }
        if (try_S_raw === undefined) {
          try_S_raw = (width * Math.pow(depth, 2)) / 6;
          console.log(`[PDF Transform Debug] Calculated Section Modulus:`, try_S_raw);
        }
        if (try_I_raw === undefined) {
          try_I_raw = (width * Math.pow(depth, 3)) / 12;
          console.log(`[PDF Transform Debug] Calculated Moment of Inertia:`, try_I_raw);
        }
      }
    }

    const try_b = formatNumber(try_b_raw, 2);
    const try_d = formatNumber(try_d_raw, 2);
    const try_A = formatNumber(try_A_raw, 2);
    const try_S = formatNumber(try_S_raw, 1);
    const try_I = formatNumber(try_I_raw, 1);

    if ([try_b_raw, try_d_raw, try_A_raw, try_S_raw, try_I_raw].some(val => val === undefined) && Object.keys(lumberProps).length === 0 && !properties.manualWidth) {
        console.warn(`[Debug PDF Data Try Fallback] For analysis ID: ${analysis.id}, one or more raw 'Try' values are undefined...`);
    }
    
    const lu_ft_formatted = formatNumber(properties.lu || sectionProps.Lu_ft || sectionProps.lu_ft || lumberProps.lu_ft, 2);

    const LDF_val = designValuesFromProps.CD || designValuesFromProps.C_D || properties.loadDurationFactor;

    const C_t_val = designValuesFromProps.calculatedTemperatureFactors?.Fb?.factor || designValuesFromProps.C_t;
    const C_i_val = designValuesFromProps.calculatedIncisingFactors?.Fb?.factor || designValuesFromProps.C_i_Fb;
    const C_M_val = designValuesFromProps.C_M_Fb || properties.wetServiceFactor?.fb;

    let beamMaterialName = '';
    // Check for glulam material name first
    if (properties.lumberType === 'glulam' && properties.selectedGluLamProperties) {
      const glulamProps = properties.selectedGluLamProperties;
      if (glulamProps.selectedTable5ADetail?.stress_class) {
        beamMaterialName = `${glulamProps.selectedTable5ADetail.stress_class} (${glulamProps.speciesGroup})`;
      } else if (glulamProps.speciesGroup && glulamProps.species) {
        beamMaterialName = `${glulamProps.speciesGroup} - ${glulamProps.species}`;
      } else if (glulamProps.speciesGroup) {
        beamMaterialName = glulamProps.speciesGroup;
      }
    } else if (properties.selectedSpeciesCombination && properties.selectedGrade) {
      beamMaterialName = `${String(properties.selectedSpeciesCombination).trim()} ${String(properties.selectedGrade).trim()}`;
    } else if (properties.selectedSpeciesCombination) {
      beamMaterialName = String(properties.selectedSpeciesCombination).trim();
    } else if (properties.selectedSpecies && properties.selectedGrade) {
      beamMaterialName = `${String(properties.selectedSpecies).trim()} ${String(properties.selectedGrade).trim()}`;
    } else if (designValuesFromProps.speciesCombination && designValuesFromProps.commercial_grade) {
      beamMaterialName = `${String(designValuesFromProps.speciesCombination).trim()} ${String(designValuesFromProps.commercial_grade).trim()}`;
    } else if (designValuesFromProps.speciesCombination) {
      beamMaterialName = String(designValuesFromProps.speciesCombination).trim();
    } else if (properties.material?.name) {
      beamMaterialName = String(properties.material.name).trim();
    } else if (lumberProps.species && lumberProps.grade) {
      beamMaterialName = `${lumberProps.species} ${lumberProps.grade}`.trim();
    } else if (lumberProps.species) {
      beamMaterialName = String(lumberProps.species).trim();
    }
    console.log(`[PDF Transform Debug] Derived beamMaterialName (Species/Grade): "${beamMaterialName}"`);

    let sizeClassification = '';
    // Check for glulam size classification first
    if (properties.lumberType === 'glulam' && properties.selectedGluLamProperties) {
      const glulamProps = properties.selectedGluLamProperties;
      sizeClassification = `${glulamProps.width}" x ${glulamProps.depth}"`;
    } else if (properties.selectedSizeClassification) {
      sizeClassification = String(properties.selectedSizeClassification).trim();
    } else if (designValuesFromProps.size_classification) {
      sizeClassification = String(designValuesFromProps.size_classification).trim();
    } else if (lumberProps.size_classification) {
      sizeClassification = String(lumberProps.size_classification).trim();
    } else if (properties.material?.size_classification) {
      sizeClassification = String(properties.material.size_classification).trim();
    } else if (lumberProps.lumber_size_classification) {
      sizeClassification = String(lumberProps.lumber_size_classification).trim();
    } else if (properties.size_classification) {
      sizeClassification = String(properties.size_classification).trim();
    }
    console.log(`[PDF Transform Debug] Derived sizeClassification: "${sizeClassification}"`);

    const E_psi = designValuesFromProps.adjusted_E;
    const Emin_psi = designValuesFromProps.adjusted_Emin;

    return {
      id: analysis.id,
      analysisName: analysis.name || 'Unnamed Analysis',
      beamMaterialName: beamMaterialName,
      sizeClassification: sizeClassification,
      L_ft: baseLft,
      Lu_ft: lu_ft_formatted !== undefined ? lu_ft_formatted : baseLft,

      w1plf_TL_plf: formatNumber(totalLoad_plf, 0),
      w1plf_FLL_plf: formatNumber(totalFloorLiveLoad_plf, 0),
      w1plf_R_plf: formatNumber(totalRoofLiveLoad_plf, 0),
      w1plf_DL_plf: formatNumber(totalDeadLoad_plf, 0),
      loadDerivation: mainLoadContainerForSummary.label || sectionProps.loadDerivation || lumberProps.loadDerivation,

      reactions: Object.entries(supportForces).map(([key, reactionData]) => {
        const data = reactionData as { maxUp?: number; maxDown?: number; maxUpCombo?: string; maxDownCombo?: string };
        const value_kips = data?.maxUp ?? data?.maxDown ?? 0;
        return {
          value_lbs: formatNumber(value_kips * 1000, 2),
          desc_lbs: String(data?.maxUpCombo || data?.maxDownCombo || ""),
        };
      }),

      At_X_ft: undefined, At_X_Vx_value: undefined, At_X_Vx_lbs: undefined,

      V_reduced_at_support_type: analysisResults.vReducedAtSupportType,
      Vmax_lbs: formatNumber(maxShearValue.value, 2),
      Vmax_position_ft: formatNumber(maxShearValue.position, 2),
      M_in_kips: formatNumber(maxMomentValue.value !== undefined ? maxMomentValue.value * 12 : undefined, 1),
      M_position_ft: formatNumber(maxMomentValue.position, 2),

      try_b_in: try_b, try_d_in: try_d, try_A_in2: try_A, try_S_in3: try_S, try_I_in4: try_I,

      case_value: maxBendingStress.loadComboName || maxShearStress.loadComboName,
      
      totalLoad_LDF: formatNumber(LDF_val, 2),
      CL_value: formatNumber(designValuesFromProps.C_L ?? properties.beamStabilityFactorCL, 2),
      Cr_value: formatNumber(designValuesFromProps.C_r ?? properties.repetitiveMemberFactor, 2),
      adjCM_factor: formatNumber(C_M_val, 2),
      adjCF_factor: formatNumber(designValuesFromProps.C_F, 2),
      adjCfu_factor: formatNumber(designValuesFromProps.C_fu ?? properties.flatUseFactor, 2),
      adjCi_factor: formatNumber(C_i_val, 2),
      adjCt_factor: formatNumber(C_t_val, 2),
      adjCV_factor: formatNumber(designValuesFromProps.C_V ?? properties.volumeFactorCv, 2),

      adjE_ksi: E_psi !== undefined ? formatNumber(E_psi / 1000, 0) : undefined,
      adjEmin_ksi: Emin_psi !== undefined ? formatNumber(Emin_psi / 1000, 0) : undefined,
      adjG_psi: formatNumber(designValuesFromProps.G, 2),

      totalLoad_FvPrime_psi: formatNumber(designValuesFromProps.adjusted_Fv || (
        // Fallbacks for manual mode allowable shear stress
        designValuesFromProps.Fv || 
        properties.manual_Fv_allow ||  // Fixed: correct field name for manual allowable shear stress
        properties.manual_Fv || 
        properties.allowable_Fv || 
        properties.Fv || 
        lumberProps.Fv || 
        maxShearStress.allowableStress ||  // Added: get F'v from analysis results
        undefined
      ), 0),
      totalLoad_FbPrime_psi: formatNumber((designValuesFromProps.adjusted_Fb_pos ?? designValuesFromProps.adjusted_Fb) || (
        // Fallbacks for manual mode allowable bending stress
        designValuesFromProps.Fb || 
        properties.manual_Fb_allow ||  // Fixed: correct field name for manual allowable bending stress
        properties.manual_Fb || 
        properties.allowable_Fb || 
        properties.Fb || 
        lumberProps.Fb || 
        maxBendingStress.allowableStress ||  // Added: get F'b from analysis results
        undefined
      ), 0),
      
      fv_psi: formatNumber(maxShearStress.actualStress, 0),
      fv_percentage: maxShearStress.ratio ? `${(parseFloat(String(maxShearStress.ratio)) * 100).toFixed(1)}%` : undefined,
      fb_psi: formatNumber(maxBendingStress.actualStress, 0),
      fb_percentage: maxBendingStress.ratio ? `${(parseFloat(String(maxBendingStress.ratio)) * 100).toFixed(1)}%` : undefined,
      d_TL_in: formatNumber(totalDeflection.value ? Math.abs(totalDeflection.value) : undefined, 2),
      d_TL_L_ratio: totalDeflection.limit,
      d_TL_loadCombo: finalLoadCombo,
      d_LL_in: formatNumber(liveLoadDeflection.value ? Math.abs(liveLoadDeflection.value) : undefined, 2),
      d_LL_L_ratio: liveLoadDeflection.limit,
      
      memberSize: properties.memberSize || sectionProps.memberSize || sectionProps.member_size || lumberProps.memberSize || lumberProps.nominal_size_bxd,
      loads: detailedLoadsForPDF,
    };
  };

  // Prepare data for PDF using the orderedAnalyses
  const pdfAnalyses: PDFBeamAnalysisResult[] = project ? orderedAnalyses.map(transformAnalysisForPDF) : [];

  const projectDataForPDF = project && pdfAnalyses.length > 0 ? {
    title: project.title,
    clientName: project.clientName || 'N/A',
    address: project.address || 'N/A',
    beamAnalysisResults: pdfAnalyses,
    jobNo: project.projectNumber || ' ',
    by: project.projectBy || ' ',
    date: project.projectDate || new Date().toLocaleDateString(),
    sheet: project.sheetNo || ' ',
  } : null;

  const handleCreateBeamAnalysis = () => router.push(`/beam-analysis?projectId=${project.id}`);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <Link href="/projects" className="text-sm text-blue-600 hover:underline flex items-center mb-1">
            <ArrowLeft size={16} className="mr-1" /> Back to All Projects
          </Link>
          <h1 className="text-3xl font-bold text-gray-800">{project.title}</h1>
          {project.clientName && <p className="text-md text-gray-600">Client: {project.clientName}</p>}
          {project.address && <p className="text-sm text-gray-500">Address: {project.address}</p>}
        </div>
        {typeof window !== 'undefined' && pdfAnalyses.length > 0 && (
          <Button variant="outline" onClick={() => setShowPdfPreview(true)}>
            <Eye size={20} className="mr-2" /> Preview PDF
          </Button>
        )}
      </div>

      {showPdfPreview && pdfAnalyses.length > 0 && (
        <Dialog open={showPdfPreview} onOpenChange={setShowPdfPreview}>
          <DialogContent className="max-w-4xl w-[90vw] h-[95vh] flex flex-col p-0 sm:p-2">
            <DialogHeader className="px-4 pt-4 sm:px-6 sm:pt-5">
              <DialogTitle>PDF Preview: {project.title}</DialogTitle>
            </DialogHeader>
            <div className="flex-grow overflow-hidden px-1 sm:px-2 py-2">
              {projectDataForPDF ? (
                <PDFViewer width="100%" height="100%">
                  <ProjectCalculationsPDF projectData={projectDataForPDF} />
                </PDFViewer>
              ) : (
                <p>Loading PDF preview...</p> 
              )}
            </div>
            <DialogFooter className="px-4 pb-4 sm:px-6 sm:pb-5 pt-2">
              {projectDataForPDF && (
                <PDFDownloadLink
                  document={<ProjectCalculationsPDF projectData={projectDataForPDF} />}
                  fileName={`${projectDataForPDF.title.replace(/\\s+/g, '_')}_Beam_Calculations.pdf`}
                >
                  {({ loading }) => (
                    <Button variant="default" disabled={loading} className="w-full sm:w-auto">
                      {loading ? (
                        <><FileDown size={20} className="mr-2 animate-pulse" /> Generating...</>
                      ) : (
                        <><FileDown size={20} className="mr-2" /> Download PDF</>
                      )}
                    </Button>
                  )}
                </PDFDownloadLink>
              )}
              <DialogClose asChild>
                <Button variant="outline" className="w-full sm:w-auto mt-2 sm:mt-0">Close Preview</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <DragDropContext onDragEnd={onDragEnd}>
        <section className="mt-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-semibold text-gray-700">Beam Analyses</h2>
            {project && (
              <Button onClick={handleCreateBeamAnalysis} className="bg-green-600 hover:bg-green-700 text-white">
                <PlusCircle className="mr-2 h-4 w-4" /> Create New Beam Analysis
              </Button>
            )}
          </div>
          {project.beamAnalysisResults && project.beamAnalysisResults.length > 0 ? (
            <Droppable droppableId="beamAnalysesDroppable" type="beamAnalysis">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-6"
                >
                  {orderedAnalyses.map((analysis, index) => (
                    <Draggable key={analysis.id} draggableId={analysis.id} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          <BeamAnalysisResultCard
                            analysisResult={analysis}
                            onAnalysisDeleted={handleAnalysisDeleted}
                            unitSystem={analysis.originalUnitSystem === UnitSystem.METRIC || analysis.originalUnitSystem === 'Metric' ? UnitSystem.METRIC : UnitSystem.IMPERIAL}
                          />
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          ) : (
            <p className="text-gray-600 py-4 text-center">No beam analyses added to this project yet.</p>
          )}
        </section>

        {project.calculations && project.calculations.length > 0 && (
          <section className="mt-10">
            <h2 className="text-2xl font-semibold text-gray-700 mb-4">Other Calculations</h2>
            <Droppable droppableId="calculationsDroppable" type="calculation">
              {(provided) => (
                <Card {...provided.droppableProps} ref={provided.innerRef} className="shadow-sm">
                  <CardContent className="pt-6">
                    <ul className="list-disc pl-5 space-y-1 text-gray-600">
                      {orderedCalculations.map((calc, index) => (
                        <Draggable key={calc.id} draggableId={calc.id} index={index}>
                          {(provided) => (
                            <li
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              {calc.name} ({calc.type})
                            </li>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </Droppable>
          </section>
        )}
      </DragDropContext>
    </div>
  );
} 