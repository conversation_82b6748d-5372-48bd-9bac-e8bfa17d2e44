import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import { Toaster as ShadCNToaster } from '@/components/ui/toaster';
import { Toaster as SonnerToaster } from 'sonner';
import TopBarNavigator from "./components/TopBarNavigator";

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Structural Engineering Calculator',
  description: 'Advanced structural analysis tools for engineers',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <Providers>
          <TopBarNavigator />
          {children}
          <ShadCNToaster />
          <SonnerToaster richColors closeButton position="top-right" />
        </Providers>
      </body>
    </html>
  );
}